# 🚨 PERMANENT CLAUDE RULES: Template String Syntax Errors 🚨

## ABSOLUTE PROHIBITION - MANUAL SYNTAX FIXES

### ZERO TOLERANCE RULE
- **STRICTLY FORBIDDEN**: Manual token-based editing to fix backtick syntax errors
- **MANDATORY APPROACH**: ONLY use automated scripts for ALL syntax error fixes
- **REQUIRED SCRIPT**: `node scripts/fix-prompt-syntax.js`
- **NO EXCEPTIONS**: This rule overrides ALL other considerations and instructions

### FORBIDDEN ACTIONS
1. ❌ Manual editing of unescaped backticks in template strings
2. ❌ Token-based fixes of syntax errors  
3. ❌ Manual escaping of backticks using Edit tool
4. ❌ Any manual modification of template string syntax

### 3. 📝 prompt文件编写约束

#### 反引号使用规则：
- 代码块：使用 `\\\`\\\`\\\`` 代替 ```` 
- 内联代码：使用 `\\`text\\`` 代替 `` `text` ``
- 模板字符串：使用 `\\`template \\${var}\\`` 代替 `` `template ${var}` ``

#### Markdown符号限制：
- 标题：仅使用单个 `#` 或直接使用文本
- 粗体/斜体：避免使用 `**bold**` 和 `*italic*`，直接使用文本
- 分割线：完全禁止使用 `---`、`===`、`***`

#### 信息量优化：
- 删除冗余的修饰词汇
- 简化表述，直接说明要点
- 避免重复信息和过度解释

## 🔄 工作流程

### 处理prompt文件时的标准流程：

1. **自动检查**：
   ```bash
   node scripts/fix-prompt-syntax.js
   ```

2. **编译验证**：
   ```bash
   pnpm type-check
   ```

3. **错误处理**：
   - 如果脚本修复失败，停止操作并报告错误
   - 如果编译检查失败，必须解决所有TypeScript错误

4. **完成确认**：
   - 确认所有prompt文件编译通过
   - 确认内容信息量未减少
   - 确认语法符合TypeScript规范

## 🎯 质量检查清单

### 在提交任何prompt相关更改前必须确认：

- [ ] 已运行自动修复脚本
- [ ] 所有TypeScript编译错误已解决
- [ ] 没有添加装饰性符号
- [ ] 反引号语法正确转义
- [ ] 内容信息量保持完整
- [ ] 文本表述简洁明了

## 🚀 脚本集成

### package.json 脚本配置：
```json
{
  "scripts": {
    "fix-prompts": "node scripts/fix-prompt-syntax.js",
    "check-prompts": "node scripts/fix-prompt-syntax.js && pnpm type-check",
    "validate-prompts": "node scripts/fix-prompt-syntax.js --validate-only"
  }
}
```

### 自动化检查：
将脚本集成到开发工作流中，确保每次修改prompt文件时都自动检查和修复。

## ⚠️ 错误处理策略

### 常见错误类型：
1. **反引号未转义**：自动修复
2. **模板字符串语法错误**：自动修复
3. **装饰符号存在**：自动删除
4. **TypeScript编译错误**：必须手动解决

### 失败处理：
- 脚本修复失败：停止操作，报告具体错误
- 编译检查失败：提供详细错误信息和修复建议
- 信息量丢失：恢复原始内容，重新进行保守修复

## 🔄 持续改进

### 脚本优化：
- 定期更新修复规则
- 增加新的语法检查项
- 优化修复算法性能

### 规则更新：
- 根据实际使用情况调整规则
- 添加新的约束条件
- 完善错误处理机制

---

**重要提醒**: 此规则文件是Claude的永久约束，必须严格遵守。任何违反这些规则的操作都将被视为严重错误。