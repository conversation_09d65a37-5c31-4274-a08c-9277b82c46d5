/**
 * ESLint custom rule to prevent unescaped markdown and emoji syntax in TypeScript files
 * This rule prevents the syntax errors that occur when markdown content is not properly
 * escaped in template literals or strings.
 */

module.exports = {
  meta: {
    type: 'problem',
    docs: {
      description: 'Disallow unescaped markdown and emoji syntax outside of template literals and strings',
      category: 'Possible Errors',
      recommended: true,
    },
    fixable: null,
    schema: [],
    messages: {
      unescapedMarkdown: 'Unescaped markdown syntax "{{syntax}}" found. Wrap in template literal or string.',
      unescapedEmoji: 'Unescaped emoji "{{emoji}}" found. Use proper string escaping or template literal.',
      unescapedAsterisk: 'Unescaped markdown bold/italic syntax "**" found. Wrap in template literal or string.',
    },
  },

  create(context) {
    const sourceCode = context.getSourceCode();
    
    // Patterns to detect problematic syntax
    const markdownPatterns = [
      /^\*\*[^*]+\*\*/, // Bold markdown
      /^#{1,6}\s/, // Headers
      /^-\s/, // List items
      /^\d+\.\s/, // Numbered lists
      /^```/, // Code blocks
    ];
    
    // Emoji pattern (basic Unicode emoji detection)
    const emojiPattern = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
    
    function checkLine(lineText, lineNumber) {
      const trimmedLine = lineText.trim();
      
      // Skip empty lines and comments
      if (!trimmedLine || trimmedLine.startsWith('//') || trimmedLine.startsWith('/*') || trimmedLine.startsWith('*')) {
        return;
      }
      
      // Skip lines that are properly within template literals or strings
      if (trimmedLine.includes('`') || trimmedLine.includes('"') || trimmedLine.includes("'")) {
        return;
      }
      
      // Check for markdown patterns
      for (const pattern of markdownPatterns) {
        if (pattern.test(trimmedLine)) {
          context.report({
            loc: {
              line: lineNumber,
              column: 0,
            },
            messageId: 'unescapedMarkdown',
            data: {
              syntax: trimmedLine.substring(0, 20) + '...',
            },
          });
          return;
        }
      }
      
      // Check for double asterisk (bold markdown)
      if (trimmedLine.startsWith('**') && trimmedLine.includes('**')) {
        context.report({
          loc: {
            line: lineNumber,
            column: 0,
          },
          messageId: 'unescapedAsterisk',
        });
        return;
      }
      
      // Check for emojis
      if (emojiPattern.test(trimmedLine)) {
        const emojiMatch = trimmedLine.match(emojiPattern);
        if (emojiMatch) {
          context.report({
            loc: {
              line: lineNumber,
              column: trimmedLine.indexOf(emojiMatch[0]),
            },
            messageId: 'unescapedEmoji',
            data: {
              emoji: emojiMatch[0],
            },
          });
        }
      }
    }
    
    return {
      Program(node) {
        const lines = sourceCode.getText().split('\n');
        
        lines.forEach((line, index) => {
          checkLine(line, index + 1);
        });
      },
    };
  },
};