/* eslint-disable max-lines-per-function */
import { Api, Post, useRes, useReq } from '@edenx/runtime/bff';
// Removed ArkService import
import OpenAI from 'openai';
import { PassThrough } from 'stream';
import {
  getMcpDependencies,
  createMcpContext,
  getFormattedMcpToolsForOpenAI,
} from './mcp';
// --- 新增：导入第三方 MCP 相关函数 ---
import {
  getFormattedThirdPartyToolsForOpenAI,
  callThirdPartyTool,
} from './thirdMcp';
// ------------------------------------
import type {
  ChatCompletionMessageParam,
  ChatCompletionToolMessageParam,
  ChatCompletionAssistantMessageParam,
  ChatCompletionSystemMessageParam,
  ChatCompletionUserMessageParam,
  ChatCompletionTool,
} from 'openai/resources/chat/completions';

const openai = new OpenAI({
  apiKey: 'b94eae06-0224-454b-afb1-350321d279b9', // 请替换为你的 API Key 或使用环境变量
  baseURL: 'https://ark-cn-beijing.bytedance.net/api/v3',
});

const conversationHistory = new Map<string, ChatCompletionMessageParam[]>();

// Removed the unused 'chat' Api endpoint

// 修改为 Post 请求，并从请求体中获取用户消息
// eslint-disable-next-line max-lines-per-function
export const streamChat = Api(Post('/streamChat'), async () => {
  const req = useReq();
  const res = useRes();
  const { message, conversationId } = req.body;

  if (!message || !conversationId) {
    res.status = 400;
    return 'Message and conversationId are required';
  }

  res.set({
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    Connection: 'keep-alive',
  });
  const streamPassThrough = new PassThrough();

  const sendEvent = (type: string, data: any) => {
    streamPassThrough.write(`data: ${JSON.stringify({ type, ...data })}\n\n`);
  };

  const processChat = async () => {
    let messages: ChatCompletionMessageParam[] = [];
    try {
      // --- 修改：获取并合并两种工具 ---
      const [mcpToolsInternal, mcpToolsThirdParty] = await Promise.all([
        getFormattedMcpToolsForOpenAI(),
        getFormattedThirdPartyToolsForOpenAI(),
      ]);
      const allTools: ChatCompletionTool[] = [
        ...mcpToolsInternal,
        ...mcpToolsThirdParty,
      ];
      console.log('allTools:', JSON.stringify(allTools, null, 2));
      // 创建一个 Set 来快速查找第三方工具的名称
      const thirdPartyToolNames = new Set(
        mcpToolsThirdParty.map(t => t.function.name),
      );
      // -----------------------------

      if (conversationHistory.has(conversationId)) {
        messages = conversationHistory.get(conversationId)!;
        // console.log(`Loaded history for conversation ${conversationId}:`, messages.length, 'messages'); // Removed
        sendEvent('log', {
          message: `Loaded ${messages.length} previous messages.`,
        });
      } else {
        const systemMessage: ChatCompletionSystemMessageParam = {
          role: 'system',
          content:
            '你是人工智能助手，可以调用工具来回答问题。请根据需要使用提供的工具。',
        };
        messages.push(systemMessage);
        // console.log(`Initialized history for new conversation ${conversationId}`); // Removed
      }

      const userMessage: ChatCompletionUserMessageParam = {
        role: 'user',
        content: message,
      };
      messages.push(userMessage);

      // console.log(`Messages for conversation ${conversationId} before 1st API call:`, messages); // Removed
      // console.log(
      //   'Tools provided to OpenAI:',
      //   JSON.stringify(allTools, null, 2), // 使用合并后的工具列表
      // ); // Removed

      const initialStream = await openai.chat.completions.create({
        messages,
        model: 'ep-20250508175508-bm5bc',
        stream: true,
        tools: allTools.length > 0 ? allTools : undefined, // 使用合并后的工具列表
        tool_choice: allTools.length > 0 ? 'auto' : undefined, // 使用合并后的工具列表
      });

      let accumulatedContent = '';
      let accumulatedReasoning = '';
      let toolCalls: OpenAI.Chat.Completions.ChatCompletionMessageToolCall[] =
        [];
      let finishReason: string | null = null;
      // let firstChunk = true; // Removed
      let assistantResponse: ChatCompletionAssistantMessageParam | null = null;

      for await (const part of initialStream) {
        // console.log('part.choices[0] (initial):', part.choices[0]); // Removed
        finishReason = part.choices[0]?.finish_reason ?? finishReason;
        const delta = part.choices[0]?.delta as any;

        if (delta?.content) {
          accumulatedContent += delta.content;
          sendEvent('content', { content: delta.content });
          // firstChunk = false; // Removed
        }

        if (delta?.reasoning_content) {
          accumulatedReasoning += delta.reasoning_content;
          sendEvent('reasoning', { reasoning: delta.reasoning_content });
          // firstChunk = false; // Removed
        }

        if (delta?.tool_calls) {
          // firstChunk = false; // Removed
          if (!assistantResponse) {
            assistantResponse = {
              role: 'assistant',
              content: null,
              tool_calls: [],
            };
            sendEvent('thinking_tool', {
              message: 'AI is planning to use tools...',
            });
          }
          delta.tool_calls.forEach((toolCallDelta: any) => {
            if (toolCallDelta.index === undefined) {
              return;
            }
            if (!assistantResponse!.tool_calls) {
              assistantResponse!.tool_calls = [];
            }
            while (
              assistantResponse!.tool_calls.length <= toolCallDelta.index
            ) {
              assistantResponse!.tool_calls.push({
                id: '',
                type: 'function',
                function: { name: '', arguments: '' },
              });
            }
            const currentToolCall =
              assistantResponse!.tool_calls[toolCallDelta.index];
            if (toolCallDelta.id) {
              currentToolCall.id = toolCallDelta.id;
            }
            if (toolCallDelta.function?.name) {
              currentToolCall.function.name += toolCallDelta.function.name;
            }
            if (toolCallDelta.function?.arguments) {
              currentToolCall.function.arguments +=
                toolCallDelta.function.arguments;
            }
          });
          toolCalls = assistantResponse.tool_calls || [];
        }
      }

      if (toolCalls.length > 0) {
        if (!assistantResponse) {
          assistantResponse = {
            role: 'assistant',
            content: null,
            tool_calls: toolCalls,
          };
        }
      } else if (accumulatedContent) {
        assistantResponse = { role: 'assistant', content: accumulatedContent };
      }

      if (assistantResponse) {
        messages.push(assistantResponse);
      }

      // console.log('First stream finished. Finish reason:', finishReason); // Removed
      // console.log('Accumulated content:', accumulatedContent); // Removed
      // console.log('Accumulated reasoning:', accumulatedReasoning); // Removed
      // console.log(
      //   'Accumulated tool calls:',
      //   JSON.stringify(toolCalls, null, 2),
      // ); // Removed

      if (finishReason === 'tool_calls' && toolCalls.length > 0) {
        const mcpSdk = await getMcpDependencies();
        const mcpCtx = createMcpContext(mcpSdk);
        const toolResults: ChatCompletionToolMessageParam[] = [];

        for (const toolCall of toolCalls) {
          if (!toolCall.id) {
            console.error('Tool call is missing ID:', toolCall); // Keep error log
            continue;
          }
          const toolName = toolCall.function.name;
          const toolArgsString = toolCall.function.arguments;
          let args: any = {};
          let toolResultContent = '';

          sendEvent('tool_call_request', {
            tool_name: toolName,
            tool_args: toolArgsString,
          });

          try {
            console.log(
              `[Debug] Received toolArgsString for ${toolName}:`,
              toolArgsString,
            );
            args = JSON.parse(toolArgsString);
            console.log(
              `[Debug] Parsed args for ${toolName}:`,
              JSON.stringify(args, null, 2),
            );

            // --- 修改：根据工具名称判断调用哪个函数 ---
            let result: any;
            if (thirdPartyToolNames.has(toolName)) {
              // 调用第三方工具
              console.log(
                `Calling THIRD-PARTY tool: ${toolName} with final args:`,
                args,
              ); // 使用最终的 args
              result = await callThirdPartyTool(toolName, args); // 传递最终的 args
              // 第三方工具的 callThirdPartyTool 已经处理了结果的 JSON 序列化（如果需要）
              // 或者直接返回了需要的内容
              toolResultContent =
                typeof result === 'string' ? result : JSON.stringify(result);
              console.log(
                `THIRD-PARTY tool ${toolName} result:`,
                toolResultContent,
              ); // Log specific result
            } else {
              // 调用内部 MCP 工具
              console.log(
                `Calling INTERNAL MCP tool: ${toolName} with parsed args:`,
                args,
              ); // 使用解析后的 args
              result = await mcpSdk.client.callTool(toolName, args, {
                // 传递解析后的 args
                ctx: mcpCtx,
              });
              // 内部工具需要序列化结果
              toolResultContent = JSON.stringify(result);
              console.log(
                `INTERNAL MCP tool ${toolName} result:`,
                toolResultContent,
              ); // Log specific result
            }
            // -----------------------------------------

            sendEvent('tool_call_result', {
              tool_name: toolName,
              tool_result: toolResultContent,
              success: true,
            });
          } catch (error: any) {
            // --- 修改：统一错误处理 ---
            const errorMessage = `Failed to call tool ${toolName}: ${error.message}`;
            console.error(`Error calling tool ${toolName}:`, error); // Keep error log
            toolResultContent = JSON.stringify({ error: errorMessage });
            // -------------------------
            sendEvent('tool_call_result', {
              tool_name: toolName,
              tool_result: toolResultContent,
              success: false,
            });
          }

          toolResults.push({
            tool_call_id: toolCall.id,
            role: 'tool',
            content: toolResultContent,
          });
        }

        messages.push(...toolResults);
        // console.log('Messages before second API call:', messages); // Removed
        sendEvent('log', { message: 'Sending tool results back to AI...' });

        // --- 修改：确认第二个模型的名称是否需要调整 ---
        // 如果第三方工具可能返回大量数据或需要不同处理方式，可能需要不同的模型
        const secondStream = await openai.chat.completions.create({
          messages,
          model: 'ep-20250418170933-fl7q9', // 确认此模型是否适合处理两种工具的结果
          stream: true,
        });
        // -----------------------------------------

        let secondAccumulatedContent = '';
        let secondAccumulatedReasoning = '';
        for await (const part of secondStream) {
          // console.log('part.choices[0] (second):', part.choices[0]); // Removed
          const delta = part.choices[0]?.delta as any;

          if (delta?.content) {
            secondAccumulatedContent += delta.content;
            sendEvent('content', { content: delta.content });
          }
          if (delta?.reasoning_content) {
            secondAccumulatedReasoning += delta.reasoning_content;
            sendEvent('reasoning', { reasoning: delta.reasoning_content });
          }
        }
        // console.log('Second stream finished.'); // Removed
        // console.log('Second accumulated content:', secondAccumulatedContent); // Removed
        // console.log('Second accumulated reasoning:', secondAccumulatedReasoning); // Removed

        if (secondAccumulatedContent) {
          const finalAssistantMessage: ChatCompletionAssistantMessageParam = {
            role: 'assistant',
            content: secondAccumulatedContent,
          };
          messages.push(finalAssistantMessage);
        }
      } else if (
        accumulatedContent === '' &&
        accumulatedReasoning === '' &&
        toolCalls.length === 0
      ) {
        // Check if truly empty
        console.warn(
          'AI response was empty (no content, reasoning, or tool calls).',
        ); // Keep warning
        sendEvent('log', { message: '[AI response was empty]' });
      }

      conversationHistory.set(conversationId, messages);
      // console.log(`Saved history for conversation ${conversationId}:`, messages.length, 'messages'); // Removed

      sendEvent('done', { message: 'Processing complete.' });
      streamPassThrough.end();
    } catch (error) {
      console.error('Error during chat processing:', error); // Keep error log
      try {
        sendEvent('error', {
          error: `Failed to process chat: ${error instanceof Error ? error.message : String(error)}`,
        });
      } catch (writeError) {
        console.error('Failed to write error to stream:', writeError); // Keep error log
      } finally {
        streamPassThrough.end();
      }
    }
  };

  processChat();

  return streamPassThrough;
});
