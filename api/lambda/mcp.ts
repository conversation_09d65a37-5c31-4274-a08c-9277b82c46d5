// 移除顶部的静态导入
// import {
//   BytedMcpClient,
//   BytedMcpClientContext,
//   MCP_GATEWAY_REGION,
//   generateBytedServiceLoggerWithLogId,
//   generateDefaultLogId,
// } from '@byted/mcp-client-sdk';

import { Api, Get, Post, useReq } from '@edenx/runtime/bff';
import { ChatCompletionTool } from 'openai/resources.mjs';

// 缓存 SDK 模块和 Client 实例
let mcpModulePromise: Promise<any> | null = null;
let clientInstance: any = null;

// --- 修改：添加 export ---
export async function getMcpDependencies() {
  if (!mcpModulePromise) {
    mcpModulePromise = import('@byted/mcp-client-sdk');
  }
  const mcpSdk = await mcpModulePromise;

  if (!clientInstance) {
    const { BytedMcpClient, MCP_GATEWAY_REGION } = mcpSdk;
    clientInstance = new BytedMcpClient({
      sampleMcpServer: {
        transport: 'http',
        psm: 'douyin.search.mcp', // 请确认 PSM
        mcpGatewayRegion: MCP_GATEWAY_REGION.CN,
      },
    });
  }
  // 返回 SDK 的所有导出以及 client 实例
  return { ...mcpSdk, client: clientInstance };
}

// 辅助函数创建 Context
// --- 修改：添加 export ---
export function createMcpContext(sdk: any) {
  const { generateDefaultLogId, generateBytedServiceLoggerWithLogId } = sdk;
  const logId = generateDefaultLogId();
  // 类型 BytedMcpClientContext 需要从动态导入的模块中获取
  const ctx: any /* sdk.BytedMcpClientContext */ = {
    // 使用 any 或 sdk.BytedMcpClientContext
    logger: generateBytedServiceLoggerWithLogId({ logId }),
    logId,
  };
  return ctx;
}

// --- 新增：获取格式化后的 MCP 工具 Schema ---
export async function getFormattedMcpToolsForOpenAI(): Promise<
  // 由于不清楚具体问题，假设需要导入 ChatCompletionTool 类型
  ChatCompletionTool[]
> {
  try {
    const mcpSdk = await getMcpDependencies();
    const mcpCtx = createMcpContext(mcpSdk);
    await mcpSdk.client.connectAll({ ctx: mcpCtx });

    // 假设 mcpSdk.client.getTools 返回的格式是 { name: string, description: string, parameters: JsonSchema }[]
    const rawTools = await mcpSdk.client.loadAllTools({ ctx: mcpCtx });
    console.log(
      'rawTools MCP Tools for OpenAI:',
      JSON.stringify(rawTools, null, 2),
    );
    // 格式化为 OpenAI 需要的 ChatCompletionTool[] 格式
    const formattedTools: ChatCompletionTool[] = rawTools.map((tool: any) => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description || '', // 确保有描述
        parameters: tool.parameters || { type: 'object', properties: {} }, // 确保有参数定义
      },
    }));
    console.log(
      'Formatted MCP Tools for OpenAI:',
      JSON.stringify(formattedTools, null, 2),
    );
    return formattedTools;
  } catch (error) {
    console.error('Error fetching or formatting MCP tools:', error);
    return []; // 出错时返回空数组
  }
}
// ------------------------------------------

export const getMcpTools = Api(Get('/mcp/tools'), async () => {
  // 这个 API 端点可以保留，用于调试或前端展示，但 AI 调用不直接用它
  try {
    const mcpSdk = await getMcpDependencies();
    const mcpCtx = createMcpContext(mcpSdk);
    await mcpSdk.client.connectAll({ ctx: mcpCtx });
    const tools = await mcpSdk.client.loadAllTools({ ctx: mcpCtx });
    return tools;
  } catch (error: any) {
    console.error('Error in /mcp/tools endpoint:', error);
    // @ts-expect-error
    res.status = 500;
    return { error: `Failed to get MCP tools: ${error.message}` };
  }
});

export const callMcpTool = Api(Post('/mcp/call'), async () => {
  const sdk = await getMcpDependencies();
  const ctx = createMcpContext(sdk);

  const request = useReq();
  const { body: payload } = request;
  if (!payload || typeof payload !== 'object') {
    throw new Error('Invalid request body');
  }
  // 明确类型，避免隐式 any
  const { toolName, args } = payload as { toolName: string; args: any };

  if (!toolName) {
    throw new Error('toolName is required in the request body');
  }

  // 确保连接（如果需要）
  const result = await sdk.client.callTool(toolName, args, { ctx }); // 传递 ctx
  return result;
});
