import { Api, Post, Get, Put, Delete, useReq } from '@edenx/runtime/bff'; // 引入 Put 和 useParams

// --- 修改 SavePromptPayload 接口 ---
interface SavePromptPayload {
  title: string;
  desc: string;
  content: string;
}
// ---------------------------------

// --- 修改 savePrompt 函数 ---
export const savePrompt = Api(Post('/prompt/save'), async () => {
  const request = useReq();
  const { body: payload } = request;

  if (!payload || typeof payload !== 'object') {
    return new Response(JSON.stringify({ error: 'Invalid request body' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  // 从 payload 中解构 title, desc, content
  const { title, desc, content } = payload as SavePromptPayload;

  // 基础验证
  if (!title || typeof title !== 'string' || title.trim() === '') {
    return new Response(JSON.stringify({ error: 'Title cannot be empty' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }
  if (!content || typeof content !== 'string' || content.trim() === '') {
    return new Response(JSON.stringify({ error: 'Content cannot be empty' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }
  // desc 可以是可选的，这里不做强制非空检查

  console.log('Received prompt to save:', { title, desc, content });

  try {
    const storageApiUrl =
      'https://paas-gw.byted.org/api/v1/search_cloud_platform/common-service/api/open_auth/search_data/insert';

    // 更新 requestBody.data 结构
    const requestBody = {
      data: {
        title, // 保存 title
        desc, // 保存 desc
        content, // 保存 content
        // timestamp: new Date().toISOString(),
      },
      entityKey: 'search_so_ai_lab',
      extra: {},
      requestRealUser: 'liming.luke',
    };

    const response = await fetch(storageApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 使用与之前相同的认证方式
        Authorization: 'Bearer 87c1e887863bad035ce536315bb353e2',
      },
      body: JSON.stringify(requestBody),
    });

    const responseText = await response.text();

    if (!response.ok) {
      let errorData: any = responseText;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        console.warn(
          'Storage API error response is not valid JSON:',
          responseText,
        );
      }
      console.error('Storage API error response:', errorData);
      throw new Error(
        `Storage API request failed with status ${response.status}: ${typeof errorData === 'string' ? errorData : JSON.stringify(errorData)}`,
      );
    }

    let resultData: any;
    try {
      resultData = JSON.parse(responseText);
    } catch (e) {
      console.error(
        'Storage API success response is not valid JSON:',
        responseText,
      );
      throw new Error('Failed to parse successful API response as JSON.');
    }
    console.log('Storage API success response:', resultData);

    const successResponse = {
      success: true,
      message: 'Prompt saved successfully via API.',
    };
    return successResponse;
  } catch (error: any) {
    console.error('Error saving prompt:', error);
    return new Response(
      JSON.stringify({ error: `Failed to save prompt: ${error.message}` }),
      { status: 500, headers: { 'Content-Type': 'application/json' } },
    );
  }
});
// -------------------------

// --- 新增 getPrompts 函数 ---
export const getPrompts = Api(Get('/prompts'), async () => {
  try {
    const findAllApiUrl =
      'https://bits.bytedance.net/api/v1/search_cloud_platform/common-service/api/open_auth/search_data/find_all'; // 使用 bits 域名

    const requestBody = {
      limit: 100,
      page: 0,
      requestRealUser: 'liming.luke',
      entityKey: 'search_so_ai_lab',
      extra: {},
    };

    // 注意：此 API 很可能也需要认证，使用与 savePrompt 相同的认证头
    const response = await fetch(findAllApiUrl, {
      method: 'POST', // 确认是 POST 请求
      headers: {
        'Content-Type': 'application/json',
        // 使用与 savePrompt 相同的认证方式
        Authorization: 'Bearer 87c1e887863bad035ce536315bb353e2',
      },
      body: JSON.stringify(requestBody),
    });

    const responseText = await response.text();

    if (!response.ok) {
      let errorData: any = responseText;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        console.warn(
          'Find All API error response is not valid JSON:',
          responseText,
        );
      }
      console.error('Find All API error response:', errorData);
      throw new Error(
        `Find All API request failed with status ${response.status}: ${typeof errorData === 'string' ? errorData : JSON.stringify(errorData)}`,
      );
    }

    let resultData: any;
    try {
      resultData = JSON.parse(responseText);
    } catch (e) {
      console.error(
        'Find All API success response is not valid JSON:',
        responseText,
      );
      throw new Error(
        'Failed to parse successful Find All API response as JSON.',
      );
    }

    // 检查 API 返回的 status_code
    if (resultData.status_code !== 0) {
      console.error('Find All API returned non-zero status code:', resultData);
      throw new Error(
        `Find All API failed: ${resultData.message || 'Unknown error'}`,
      );
    }

    console.log('Find All API success response:', resultData);

    // 返回 data 数组给前端
    return resultData.data || []; // 确保即使 data 不存在也返回空数组
  } catch (error: any) {
    console.error('Error fetching prompts:', error);
    // 返回错误给前端
    return new Response(
      JSON.stringify({ error: `Failed to fetch prompts: ${error.message}` }),
      { status: 500, headers: { 'Content-Type': 'application/json' } },
    );
  }
});
// -------------------------

// --- 新增：更新 Prompt 的 API ---
// --- 修改：更新 Prompt 的 API 接口定义 ---
interface UpdatePromptPayload {
  content?: string; // content 仍然是主要的，但也可以是可选的，如果只想更新 title/desc
  title?: string; // 添加可选的 title
  desc?: string; // 添加可选的 desc
}
// ------------------------------------

export const updatePrompt = Api(Put('/prompt/update/:id'), async () => {
  const request = useReq();
  const { params } = request;
  console.log('request.params', request.params);
  const { id } = params;
  const { body: payload } = request;

  if (!id) {
    return new Response(JSON.stringify({ error: 'Prompt ID is required' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  if (
    !payload ||
    typeof payload !== 'object' ||
    Object.keys(payload).length === 0
  ) {
    // 检查 payload 是否为空对象
    return new Response(
      JSON.stringify({ error: 'Invalid or empty request body' }),
      {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }

  // --- 修改：解构出 title, desc, content ---
  const { content, title, desc } = payload as UpdatePromptPayload;
  // ---------------------------------------

  // 基础验证：至少需要提供一个要更新的字段
  if (content === undefined && title === undefined && desc === undefined) {
    return new Response(
      JSON.stringify({
        error:
          'At least one field (title, desc, or content) must be provided for update',
      }),
      {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }
  // 如果提供了 content，验证它不为空字符串
  if (
    content !== undefined &&
    (content === null || (typeof content === 'string' && content.trim() === ''))
  ) {
    return new Response(
      JSON.stringify({ error: 'Content cannot be empty if provided' }),
      {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }
  // 如果提供了 title，验证它不为空字符串
  if (
    title !== undefined &&
    (title === null || (typeof title === 'string' && title.trim() === ''))
  ) {
    return new Response(
      JSON.stringify({ error: 'Title cannot be empty if provided' }),
      {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }
  // desc 可以为空字符串

  // --- 修改：更新日志 ---
  console.log(`Received prompt update for ID ${id}:`, { title, desc, content });
  // --------------------

  try {
    const updateApiUrl =
      'https://paas-gw.byted.org/api/v1/search_cloud_platform/common-service/api/open_auth/search_data/update_one';

    // --- 修改：构造要更新的数据对象 ---
    const dataToUpdate: { [key: string]: any } = {};
    if (content !== undefined) {
      dataToUpdate.content = content;
    }
    if (title !== undefined) {
      dataToUpdate.title = title;
    }
    if (desc !== undefined) {
      // 如果 desc 是 null 或空字符串，也应该更新
      dataToUpdate.desc = desc;
    }
    // 总是更新 updateTime
    // dataToUpdate.updateTime = new Date().toISOString(); // 如果需要记录更新时间
    // ---------------------------------

    // --- 修改：构造请求体 ---
    const requestBody = {
      data: dataToUpdate, // 使用构造好的更新对象
      entityKey: 'search_so_ai_lab',
      extra: {},
      filter: {
        id,
      },
      requestRealUser: 'liming.luke',
    };
    // -----------------------

    const response = await fetch(updateApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer 87c1e887863bad035ce536315bb353e2',
      },
      body: JSON.stringify(requestBody),
    });

    const responseText = await response.text();

    if (!response.ok) {
      let errorData: any = responseText;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        /* ignore */
      }
      console.error('Update API error response:', errorData);
      throw new Error(
        `Update API request failed with status ${response.status}: ${typeof errorData === 'string' ? errorData : JSON.stringify(errorData)}`,
      );
    }

    let resultData: any;
    try {
      resultData = JSON.parse(responseText);
    } catch (e) {
      /* ignore */
    }
    console.log('Update API success response:', resultData);

    // 检查后端 API 返回的状态码（如果适用）
    // if (resultData.status_code !== 0) {
    //   throw new Error(`Update API failed: ${resultData.message || 'Unknown error'}`);
    // }

    return { success: true, message: 'Prompt updated successfully.' };
    // --- ---
  } catch (error: any) {
    console.error('Error updating prompt:', error);
    return new Response(
      JSON.stringify({ error: `Failed to update prompt: ${error.message}` }),
      { status: 500, headers: { 'Content-Type': 'application/json' } },
    );
  }
});
// -----------------------------

// --- 新增：删除 Prompt 的 API ---
export const deletePrompt = Api(Delete('/prompt/delete/:id'), async () => {
  const request = useReq();
  const { params } = request;
  const { id } = params;

  if (!id) {
    return new Response(JSON.stringify({ error: 'Prompt ID is required' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  console.log(`Received prompt delete request for ID ${id}`);

  try {
    // --- 调用 ByteDance Cloud API (删除操作) ---
    const deleteApiUrl =
      'https://paas-gw.byted.org/api/v1/search_cloud_platform/common-service/api/open_auth/search_data/destroy_one'; // 确认这是正确的删除 URL

    // 构造请求体，需要指定 entityKey 和 filter
    const requestBody = {
      entityKey: 'search_so_ai_lab', // 确保与你的 entityKey 匹配
      extra: {},
      filter: {
        id, // 使用 URL 中的 ID 来指定要删除的文档
      },
      requestRealUser: 'liming.luke', // 或者从请求上下文获取真实用户
    };

    // 使用与 save/find/update 相同的认证方式
    const response = await fetch(deleteApiUrl, {
      method: 'POST', // 根据 API 文档，删除操作通常也是 POST
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer 87c1e887863bad035ce536315bb353e2', // 你的认证 Token
      },
      body: JSON.stringify(requestBody),
    });

    const responseText = await response.text();

    if (!response.ok) {
      let errorData: any = responseText;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        /* ignore */
      }
      console.error('Delete API error response:', errorData);
      throw new Error(
        `Delete API request failed with status ${response.status}: ${typeof errorData === 'string' ? errorData : JSON.stringify(errorData)}`,
      );
    }

    let resultData: any;
    try {
      resultData = JSON.parse(responseText);
    } catch (e) {
      console.warn(
        'Delete API success response is not valid JSON:',
        responseText,
      );
      // 即使不是 JSON，如果状态码是 2xx，也可能表示成功，取决于后端 API 设计
      // 这里我们假设非 JSON 响应也可能是成功的，但记录一个警告
      resultData = {
        message: 'Delete operation completed, but response was not JSON.',
      };
    }

    console.log('Delete API success response:', resultData);

    // 检查后端 API 返回的状态码（如果适用）
    // 注意：删除操作的成功状态码可能与查询/更新不同，需要确认
    // if (resultData.status_code !== 0 && resultData.code !== 0) { // 假设成功码是 0
    //   throw new Error(`Delete API failed: ${resultData.message || 'Unknown error'}`);
    // }

    // 返回成功响应给前端
    return { success: true, message: 'Prompt deleted successfully.' };
  } catch (error: any) {
    console.error('Error deleting prompt:', error);
    return new Response(
      JSON.stringify({ error: `Failed to delete prompt: ${error.message}` }),
      { status: 500, headers: { 'Content-Type': 'application/json' } },
    );
  }
});
// -----------------------------
