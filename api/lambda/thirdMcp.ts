import { Api, Get, Post, useReq } from '@edenx/runtime/bff';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import {
  // ListToolsRequest, // Removed
  ListToolsResultSchema,
  // CallToolRequest, // Removed
  CallToolResultSchema,
  CallToolResult, // Keep this type potentially for schema reference, but not for client.callTool return annotation
  Tool, // --- 修改：尝试导入 Tool 而不是 ToolDefinition ---
} from '@modelcontextprotocol/sdk/types.js';
import { ChatCompletionTool } from 'openai/resources.mjs';
// --- 新增：导入配置文件 ---
import mcpConfigs from '../config/mcp.config';
// -------------------------

// --- 修改：管理多个客户端实例和工具映射 ---
let thirdPartyClients = new Map<string, Client>();
let toolToClientMap = new Map<string, string>(); // toolName -> clientName
let clientsInitialized = false;
// -----------------------------------------

// --- 新增：初始化所有配置的客户端 ---
async function initializeThirdPartyMcpClients(): Promise<void> {
  if (clientsInitialized) {
    return;
  }
  console.log('Initializing third-party MCP clients from config...');
  thirdPartyClients = new Map<string, Client>();
  toolToClientMap = new Map<string, string>();

  for (const config of mcpConfigs) {
    if (!config.isOpen) {
      console.log(`Skipping disabled MCP server config: ${config.name}`);
      continue;
    }
    if (config.type !== 'sse') {
      console.warn(
        `Unsupported MCP server type: ${config.type} for ${config.name}. Skipping.`,
      );
      continue;
    }

    console.log(`Initializing client for: ${config.name} (${config.url})`);
    const client = new Client({
      name: `mcp-client-${config.name}`, // 使用配置名称区分
      version: '1.0.0',
    });

    try {
      const transport = new SSEClientTransport(new URL(config.url));
      console.log(`Connecting to ${config.name}...`);
      await client.connect(transport);
      console.log(`Connected to ${config.name}.`);
      thirdPartyClients.set(config.name, client);
    } catch (error) {
      console.error(`Failed to connect to ${config.name}:`, error);
      // 连接失败不阻塞其他客户端初始化
    }
  }
  clientsInitialized = true;
  console.log(`Initialized ${thirdPartyClients.size} third-party MCP clients.`);
}
// ------------------------------------

// --- 修改：列出所有客户端的工具并建立映射 ---
// --- 修改：函数签名使用 Tool ---
async function listAllToolsAndMap(): Promise<Tool[]> {
  await initializeThirdPartyMcpClients(); // 确保客户端已初始化

  // --- 修改：数组类型使用 Tool ---
  const allTools: Tool[] = [];
  toolToClientMap.clear(); // 清空旧映射

  console.log('Listing tools from all connected third-party clients...');
  for (const [clientName, client] of thirdPartyClients.entries()) {
    try {
      // --- 修改：使用 client.listTools()，它应该返回 ListToolsResult，包含 Tool[] ---
      const toolsResult = await client.listTools(); // listTools 返回 { tools: Tool[] }

      console.log(`Tools from ${clientName}:`);
      if (toolsResult.tools.length === 0) {
        console.log(`  No tools available from ${clientName}`);
      } else {
        toolsResult.tools.forEach(tool => {
          // tool 的类型现在是 Tool
          // --- 修改：处理潜在的工具名冲突 (简单策略：后者覆盖前者，可按需调整) ---
          if (toolToClientMap.has(tool.name)) {
            console.warn(
              `Tool name conflict: "${tool.name}" from ${clientName} overrides the one from ${toolToClientMap.get(tool.name)}. Consider renaming tools for uniqueness.`,
            );
          }
          allTools.push(tool);
          toolToClientMap.set(tool.name, clientName); // 建立工具名到客户端名的映射
          // -----------------------------------------------------------------
        });
      }
    } catch (error) {
      console.error(`Failed to list tools from ${clientName}: ${error}`);
    }
  }
  console.log(`Total unique third-party tools found: ${toolToClientMap.size}`);
  return allTools;
}
// -----------------------------------------

// --- 修改：获取格式化工具列表 ---
export async function getFormattedThirdPartyToolsForOpenAI(): Promise<
  ChatCompletionTool[]
> {
  try {
    const rawTools = await listAllToolsAndMap(); // 调用新的聚合函数

    const formattedTools: ChatCompletionTool[] = rawTools.map(tool => ({
      // tool 的类型是 Tool
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.inputSchema || { type: 'object', properties: {} },
      },
    }));
    console.log(
      'Formatted ALL Third-Party Tools for OpenAI:',
      JSON.stringify(formattedTools, null, 2),
    );
    return formattedTools;
  } catch (error) {
    console.error('Error fetching or formatting third-party tools:', error);
    return [];
  }
}
// ------------------------------------

// --- 修改：调用第三方工具，根据映射路由 ---
export async function callThirdPartyTool(
  toolName: string,
  args: any,
): Promise<any> {
  await initializeThirdPartyMcpClients(); // 确保客户端已初始化

  const clientName = toolToClientMap.get(toolName);
  if (!clientName) {
    throw new Error(
      `Tool "${toolName}" not found or its client is not available.`,
    );
  }

  const client = thirdPartyClients.get(clientName);
  if (!client) {
    // 这理论上不应该发生，因为映射存在
    throw new Error(`Client "${clientName}" for tool "${toolName}" not found.`);
  }

  console.log(`Routing call for tool "${toolName}" to client "${clientName}"`);

  try {
    // --- 修改：移除 result 的显式类型注解，让 TS 推断 ---
    const result = await client.callTool({
      name: toolName,
      arguments: args,
    });
    // -------------------------------------------------
    console.log(`Client ${clientName} raw result for ${toolName}:`, result);

    if (result && typeof result.content !== 'undefined') {
      return typeof result.content === 'string'
        ? result.content
        : JSON.stringify(result.content);
    } else {
      console.warn(
        `Tool ${toolName} (Client ${clientName}) did not return an 'output' field.`,
      );
      // 返回一个包含警告的 JSON 字符串，或者根据需要返回其他内容
      return JSON.stringify({
        warning: 'Tool executed but returned no output field.',
        rawResult: result, // 可以选择性地包含原始结果以供调试
      });
    }
  } catch (error: any) {
    console.error(
      `Error calling tool ${toolName} on client ${clientName}:`,
      error,
    );
    throw new Error(
      `Failed to call tool ${toolName} via ${clientName}: ${error.message || error}`,
    );
  }
}
// -----------------------------------------

// --- API 端点保持不变，内部逻辑已更新 ---
export const listThirdPartyMcpTools = Api(Get('/thirdmcp/tools'), async () => {
  const list = await getFormattedThirdPartyToolsForOpenAI();
  return list;
});
// ---------------------------------------
