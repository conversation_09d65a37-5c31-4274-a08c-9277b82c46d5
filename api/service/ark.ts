import { Injectable } from '@gulux/gulux';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: 'c27e721e-3c39-493c-81f8-a37f69efc7a4',
  baseURL: 'https://ark-cn-beijing.bytedance.net/api/v3',
});

@Injectable()
export default class ArkService {
  // eslint-disable-next-line @typescript-eslint/explicit-member-accessibility
  public async chat() {
    const completion = await openai.chat.completions.create({
      messages: [
        { role: 'system', content: '你是人工智能助手' },
        { role: 'user', content: '常见的十字花科植物有哪些？' },
      ],
      model: 'ep-20250321111723-d9f7l',
    });
    const data = completion.choices[0]?.message?.content;

    return data;
  }
}
