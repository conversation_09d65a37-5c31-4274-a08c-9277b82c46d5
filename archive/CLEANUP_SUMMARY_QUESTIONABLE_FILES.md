# Repository Cleanup - Questionable Files Summary

## Files Requiring User Confirmation

### 🤔 **Questionable Documentation Files**

#### **1. Test Files (Potential Archive Candidates)**
**Location**: `src/routes/batch_processor/test/`
- `test-*.html` files (71 files total)
- Many appear to be temporary testing/debugging files
- **Recommendation**: Archive most, keep only essential integration tests
- **Space Saving**: ~15-20MB

#### **2. Architecture Documentation (Review Needed)**
**Files to Review**:
- `src/routes/lynx_preview/ARCHITECTURE_DESIGN.md` - Check if current
- `src/routes/batch_processor/docs/Enhanced_Lynx_Prompt_*.md` - May be outdated
- Various component documentation in `/docs/` directories

**Questions for User**:
- Are these architectural docs still relevant for current development?
- Should outdated architecture docs be archived or updated?

#### **3. Deleted/Archive Directories (Confirmation Needed)**

**Already Archived (Good)**:
- `src/routes/batch_processor/deleted_lynx2web/` - ✅ Properly archived
- `src/routes/batch_processor/deleted_runtime_convert/` - ✅ Properly archived  
- `src/routes/code_generate/deleted/` - ✅ Properly archived

**Questionable Directories**:
- `src/routes/batch_processor/runtime_convert_parse5/` - Is this active or should it be archived?
- Various test directories with many HTML files

#### **4. Configuration and Setup Files**
**Files to Review**:
- Multiple `.eslintrc.js` files - Some may be redundant
- Various `tsconfig.json` variations
- Debug scripts and utilities

### 🔍 **Large File Categories Needing Decision**

#### **A. Log Files (MAJOR SPACE SAVINGS)**
**Found**: ~1.1GB of rotating log files
- Hundreds of timestamped log files
- Safe to clean but need confirmation on retention policy
- **Potential Savings**: 1.1GB+

#### **B. IDE History Files** 
**Found**: ~72MB in `.history` and `.lh` directories
- Already moved 223 files to archive
- More may exist in subdirectories
- **Potential Savings**: Additional 50-100MB

#### **C. Node Modules Documentation**
**Found**: ~190 additional MD files in node_modules
- These are safe to ignore (regenerated on install)
- No action needed

### 🎯 **Proposed Actions Requiring Confirmation**

#### **High Impact, Low Risk**
1. **Clean Log Files**: Remove/archive rotating logs (1.1GB savings)
2. **Complete IDE History Cleanup**: Remove remaining .history/.lh files
3. **Archive Test HTML Files**: Move most test-*.html files to archive

#### **Medium Impact, Review Needed**
1. **Archive Outdated Docs**: Move old architecture docs to archive
2. **Consolidate Config Files**: Remove redundant ESLint/TS configs
3. **Review Runtime Convert Parse5**: Determine if active or archive

#### **Low Impact, Optional**
1. **Clean Up Staging Area**: Many files are staged but deleted/moved
2. **Organize Remaining Test Files**: Keep only essential tests
3. **Update Documentation Index**: Create master index of remaining docs

### 📊 **Total Potential Space Savings**
- **Already Achieved**: ~300MB (268 MD files archived)
- **Additional Potential**: 
  - Log files: ~1.1GB
  - IDE history: ~100MB  
  - Test files: ~20MB
  - **Total Possible**: ~1.4GB+ reduction

### ❓ **Questions for User Decision**

1. **Log Retention Policy**: How long should rotating logs be kept? Archive all older than X days?

2. **Test File Strategy**: 
   - Keep all test-*.html files or archive non-essential ones?
   - Which test files are still actively used?

3. **Architecture Documentation**: 
   - Should outdated architecture docs be archived or updated?
   - Which architectural documents are still current?

4. **Runtime Convert Parse5**: 
   - Is this directory still in active development?
   - Should it be moved to deleted_runtime_convert?

5. **Configuration Cleanup**:
   - Remove redundant .eslintrc.js files in subdirectories?
   - Consolidate TypeScript configurations?

### 🚀 **Ready for Immediate Action (Safe)**
These can be executed immediately without risk:
- Archive remaining .history files
- Clean up staged deleted files  
- Archive obvious temporary documentation
- Remove empty or near-empty MD files

### ⚠️ **Requires User Review**
These need user input before proceeding:
- Log file cleanup strategy
- Test file retention policy
- Architecture documentation decisions
- Configuration file consolidation