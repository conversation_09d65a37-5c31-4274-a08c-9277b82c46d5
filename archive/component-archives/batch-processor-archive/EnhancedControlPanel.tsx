import React, { useState, useEffect } from 'react';
import {
  Button,
  InputNumber,
  Card,
  Space,
  Tooltip,
  Switch,
  Select,
  Typography,
} from '@douyinfe/semi-ui';
import {
  IconPause,
  IconPlay,
  IconRefresh,
  IconSetting,
  IconClose,
  IconTick,
  IconHelpCircle,
} from '@douyinfe/semi-icons';
import { JobPriority } from '../utils/ConcurrencyManager';
import { BatchProgress } from '../types';

const { Text, Title } = Typography;
const { Option } = Select;

interface EnhancedControlPanelProps {
  isProcessing: boolean;
  isPaused: boolean;
  progress: BatchProgress;
  onStart: (options?: { priority?: JobPriority; group?: string }) => void;
  onStop: () => void;
  onPause: () => void;
  onResume: () => void;
  onRetryFailed: () => void;
  onUpdateSettings: (settings: {
    maxConcurrent?: number;
    rateLimit?: number;
    maxRetries?: number;
    delayBetweenRequests?: number;
  }) => void;
  initialSettings?: {
    maxConcurrent: number;
    rateLimit: number;
    maxRetries: number;
    delayBetweenRequests: number;
  };
  queries: string[];
  className?: string;
  showAdvanced?: boolean;
}

/**
 * 增强版控制面板组件
 * 提供更精细的并发控制、队列优先级和速率限制设置
 */
export const EnhancedControlPanel: React.FC<EnhancedControlPanelProps> = ({
  isProcessing,
  isPaused,
  progress,
  onStart,
  onStop,
  onPause,
  onResume,
  onRetryFailed,
  onUpdateSettings,
  initialSettings = {
    maxConcurrent: 5,
    rateLimit: 10,
    maxRetries: 3,
    delayBetweenRequests: 2000,
  },
  queries = [],
  className = '',
  showAdvanced = false,
}) => {
  // 设置状态
  const [settings, setSettings] = useState({
    ...initialSettings,
    maxConcurrent: 5, // 固定并发数为5
  });
  const [showSettings, setShowSettings] = useState(showAdvanced);
  const [priority, setPriority] = useState<JobPriority>(JobPriority.NORMAL);
  const [group, setGroup] = useState<string>('默认组');

  // 当设置变化时更新
  useEffect(() => {
    onUpdateSettings(settings);
  }, [settings, onUpdateSettings]);

  // 处理设置变更
  const handleSettingChange = (key: string, value: number) => {
    if (key === 'maxConcurrent') {
      return;
    } // 忽略并发数变更

    setSettings(prev => {
      const updated = { ...prev, [key]: value };
      return updated;
    });
  };

  // 处理开始按钮点击
  const handleStart = () => {
    onStart({ priority, group });
  };

  // 格式化时间
  const formatTime = (ms?: number): string => {
    if (!ms) {
      return '计算中...';
    }

    const seconds = Math.floor(ms / 1000);
    if (seconds < 60) {
      return `${seconds}秒`;
    }

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds}秒`;
  };

  // 是否启用开始按钮
  const startEnabled = !isProcessing && queries.length > 0;

  // 组件样式
  const styles = {
    controlPanel: {
      width: '100%',
    },
    controlPanelHeader: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '16px',
    },
    controlPanelMain: {
      width: '100%',
    },
    progressInfo: {
      backgroundColor: 'var(--semi-color-fill-0)',
      padding: '16px',
      borderRadius: '6px',
      width: '100%',
    },
    actionButtons: {
      margin: '16px 0',
      display: 'flex',
      flexWrap: 'wrap' as const,
      gap: '8px',
    },
    advancedSettings: {
      width: '100%',
      backgroundColor: 'var(--semi-color-fill-0)',
      marginTop: '12px',
    },
    settingItem: {
      marginBottom: '16px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      flexWrap: 'wrap' as const,
      gap: '8px',
    },
    buttonText: {
      whiteSpace: 'nowrap',
      overflow: 'visible',
    },
    buttonGroup: {
      display: 'flex',
      flexWrap: 'wrap' as const,
      gap: '8px',
    },
  };

  return (
    <Card
      className={`enhanced-control-panel ${className}`}
      style={styles.controlPanel}
    >
      <div style={styles.controlPanelHeader}>
        <Title heading={5}>控制面板</Title>
        <Space>
          <Tooltip content={showSettings ? '隐藏高级设置' : '显示高级设置'}>
            <Button
              icon={<IconSetting />}
              type="tertiary"
              onClick={() => setShowSettings(!showSettings)}
            />
          </Tooltip>
          {isProcessing && (
            <Tooltip content="停止处理">
              <Button icon={<IconClose />} type="danger" onClick={onStop} />
            </Tooltip>
          )}
        </Space>
      </div>

      {/* 基本控制 */}
      <div style={styles.controlPanelMain}>
        <Space vertical align="start" spacing="loose" style={{ width: '100%' }}>
          {/* 进度信息 */}
          {isProcessing && (
            <div style={styles.progressInfo}>
              <Space vertical align="start" style={{ width: '100%' }}>
                <div className="progress-stats">
                  <Text strong>已完成: </Text>
                  <Text>
                    {progress.completed}/{progress.total}
                  </Text>
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    (失败: {progress.failed})
                  </Text>
                </div>

                <div className="progress-time">
                  <Text strong>预计剩余时间: </Text>
                  <Text>{formatTime(progress.estimatedTimeRemaining)}</Text>
                </div>

                {progress.current && (
                  <div className="current-query">
                    <Text strong>当前处理: </Text>
                    <Text>{progress.current}</Text>
                  </div>
                )}
              </Space>
            </div>
          )}

          {/* 操作按钮 */}
          <div style={styles.actionButtons}>
            <Space wrap spacing={8}>
              {!isProcessing ? (
                <Button
                  type="primary"
                  icon={<IconPlay />}
                  disabled={!startEnabled}
                  onClick={handleStart}
                  size="large"
                  style={styles.buttonText}
                >
                  开始处理 ({queries.length})
                </Button>
              ) : isPaused ? (
                <Button
                  type="primary"
                  icon={<IconPlay />}
                  onClick={onResume}
                  style={styles.buttonText}
                >
                  恢复处理
                </Button>
              ) : (
                <Button
                  type="warning"
                  icon={<IconPause />}
                  onClick={onPause}
                  style={styles.buttonText}
                >
                  暂停处理
                </Button>
              )}

              <Button
                type="secondary"
                icon={<IconRefresh />}
                onClick={onRetryFailed}
                disabled={progress.failed === 0}
                style={styles.buttonText}
              >
                重试失败 ({progress.failed})
              </Button>
            </Space>
          </div>

          {/* 高级设置 */}
          {showSettings && (
            <Card style={styles.advancedSettings}>
              <Space vertical align="start" style={{ width: '100%' }}>
                <Title heading={6}>高级设置</Title>

                {/* 优先级设置 */}
                <div style={styles.settingItem}>
                  <Text>优先级:</Text>
                  <Select
                    value={priority}
                    onChange={value => setPriority(value as JobPriority)}
                    style={{ width: 120 }}
                  >
                    <Option value={JobPriority.LOW}>低</Option>
                    <Option value={JobPriority.NORMAL}>中</Option>
                    <Option value={JobPriority.HIGH}>高</Option>
                    <Option value={JobPriority.CRITICAL}>紧急</Option>
                  </Select>
                </div>

                {/* 分组设置 */}
                <div style={styles.settingItem}>
                  <Text>分组:</Text>
                  <Select
                    value={group}
                    onChange={value => {
                      if (typeof value === 'string') {
                        setGroup(value);
                      }
                    }}
                    style={{ width: 120 }}
                  >
                    <Option value="默认组">默认组</Option>
                    <Option value="重要">重要</Option>
                    <Option value="次要">次要</Option>
                    <Option value="测试">测试</Option>
                  </Select>
                </div>

                {/* 并发数设置（固定为5） */}
                <div style={styles.settingItem}>
                  <div>
                    <Space>
                      <Text>并发数:</Text>
                      <Tooltip content="同时处理的最大查询数量">
                        <IconHelpCircle size="small" />
                      </Tooltip>
                    </Space>
                  </div>
                  <div>
                    <Text strong>5</Text>
                    <Text type="tertiary" style={{ marginLeft: 8 }}>
                      (固定值)
                    </Text>
                  </div>
                </div>

                {/* 速率限制 */}
                <div style={styles.settingItem}>
                  <Space>
                    <Text>速率限制:</Text>
                    <InputNumber
                      min={1}
                      max={30}
                      value={settings.rateLimit}
                      onChange={value =>
                        handleSettingChange('rateLimit', value as number)
                      }
                    />
                    <Text type="tertiary">每分钟</Text>
                    <Tooltip content="每分钟最大请求数量">
                      <IconHelpCircle size="small" />
                    </Tooltip>
                  </Space>
                </div>

                {/* 重试设置 */}
                <div style={styles.settingItem}>
                  <Space>
                    <Text>最大重试:</Text>
                    <InputNumber
                      min={0}
                      max={5}
                      value={settings.maxRetries}
                      onChange={value =>
                        handleSettingChange('maxRetries', value as number)
                      }
                    />
                    <Tooltip content="失败时自动重试的最大次数">
                      <IconHelpCircle size="small" />
                    </Tooltip>
                  </Space>
                </div>

                {/* 请求间隔 */}
                <div style={styles.settingItem}>
                  <Space>
                    <Text>请求间隔:</Text>
                    <InputNumber
                      min={0}
                      max={5000}
                      step={100}
                      value={settings.delayBetweenRequests}
                      onChange={value =>
                        handleSettingChange(
                          'delayBetweenRequests',
                          value as number,
                        )
                      }
                    />
                    <Text type="tertiary">毫秒</Text>
                    <Tooltip content="请求之间的延迟时间">
                      <IconHelpCircle size="small" />
                    </Tooltip>
                  </Space>
                </div>
              </Space>
            </Card>
          )}
        </Space>
      </div>
    </Card>
  );
};
