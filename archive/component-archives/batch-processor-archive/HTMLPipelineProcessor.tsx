/**
 * HTML处理管道组件
 *
 * 专门处理HTML内容的生成、预览和上传
 * 与LYNX处理管道分离，使用不同的上传策略
 */

import React, { useState, useCallback } from 'react';
import Icon from './Icon';

interface HTMLContent {
  id: string;
  content: string;
  fileName: string;
  size: number;
  isComplete: boolean;
  hasStyles: boolean;
  hasScripts: boolean;
}

interface HTMLProcessingResult {
  success: boolean;
  cdnUrl?: string;
  downloadUrl?: string;
  previewUrl?: string;
  error?: string;
  metadata?: {
    originalSize: number;
    compressedSize: number;
    processingTime: number;
    contentType: 'html';
  };
}

interface HTMLPipelineProcessorProps {
  content: string;
  onProcessingComplete?: (result: HTMLProcessingResult) => void;
  onProcessingError?: (error: string) => void;
  className?: string;
}

const HTMLPipelineProcessor: React.FC<HTMLPipelineProcessorProps> = ({
  content,
  onProcessingComplete,
  onProcessingError,
  className = '',
}) => {
  const [processing, setProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [stage, setStage] = useState<
    'idle' | 'analyzing' | 'processing' | 'uploading' | 'complete' | 'error'
  >('idle');
  const [htmlContent, setHtmlContent] = useState<HTMLContent | null>(null);
  const [result, setResult] = useState<HTMLProcessingResult | null>(null);

  // 分析HTML内容
  const analyzeHTML = useCallback((content: string): HTMLContent => {
    const id = `html_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 检查是否是完整的HTML文档
    const isComplete =
      content.includes('<!DOCTYPE') || content.includes('<html');

    // 检查是否包含样式
    const hasStyles =
      content.includes('<style') || content.includes('stylesheet');

    // 检查是否包含脚本
    const hasScripts = content.includes('<script');

    // 确定文件名
    const fileName = isComplete ? 'index.html' : 'content.html';

    return {
      id,
      content,
      fileName,
      size: content.length,
      isComplete,
      hasStyles,
      hasScripts,
    };
  }, []);

  // 处理HTML内容
  const processHTML = useCallback(async () => {
    if (!content.trim()) {
      onProcessingError?.('HTML内容为空');
      return;
    }

    setProcessing(true);
    setProgress(0);
    setStage('analyzing');

    try {
      // 1. 分析HTML内容
      setProgress(20);
      const analyzed = analyzeHTML(content);
      setHtmlContent(analyzed);

      // 2. 内容处理
      setStage('processing');
      setProgress(40);

      // 验证HTML基本结构
      if (!analyzed.content.includes('<')) {
        throw new Error('内容不包含有效的HTML标签');
      }

      // 3. 准备上传
      setStage('uploading');
      setProgress(60);

      // 模拟上传过程
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 4. 生成结果
      setProgress(80);

      const mockResult: HTMLProcessingResult = {
        success: true,
        cdnUrl: `https://cdn.example.com/html/${analyzed.id}/${analyzed.fileName}`,
        downloadUrl: `https://cdn.example.com/download/html/${analyzed.id}.zip`,
        previewUrl: `https://preview.example.com/html/${analyzed.id}`,
        metadata: {
          originalSize: analyzed.size,
          compressedSize: Math.floor(analyzed.size * 0.7), // 模拟压缩
          processingTime: Date.now(),
          contentType: 'html',
        },
      };

      setResult(mockResult);
      setStage('complete');
      setProgress(100);

      onProcessingComplete?.(mockResult);
    } catch (error) {
      setStage('error');
      const errorMessage = error instanceof Error ? error.message : '处理失败';
      onProcessingError?.(errorMessage);
    } finally {
      setProcessing(false);
    }
  }, [content, analyzeHTML, onProcessingComplete, onProcessingError]);

  // 重置状态
  const reset = useCallback(() => {
    setProcessing(false);
    setProgress(0);
    setStage('idle');
    setHtmlContent(null);
    setResult(null);
  }, []);

  // 获取阶段显示文本
  const getStageText = () => {
    switch (stage) {
      case 'analyzing':
        return '分析HTML结构...';
      case 'processing':
        return '处理HTML内容...';
      case 'uploading':
        return '上传到CDN...';
      case 'complete':
        return '处理完成';
      case 'error':
        return '处理失败';
      default:
        return '准备就绪';
    }
  };

  // 获取状态图标
  const getStatusIcon = () => {
    switch (stage) {
      case 'complete':
        return <Icon type="check" color="success" size="sm" />;
      case 'error':
        return <Icon type="close" color="error" size="sm" />;
      case 'idle':
        return <Icon type="play" color="primary" size="sm" />;
      default:
        return (
          <Icon
            type="refresh"
            color="primary"
            size="sm"
            className="animate-spin"
          />
        );
    }
  };

  return (
    <div className={`bg-white rounded-lg border shadow-sm p-4 ${className}`}>
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium flex items-center">
          <Icon type="web" color="primary" size="sm" className="mr-2" />
          HTML 处理管道
        </h3>
        <div className="flex gap-2">
          <button
            onClick={processHTML}
            disabled={processing || !content.trim()}
            className="btn-authority btn-primary-glass text-sm px-3 py-1 disabled:opacity-50"
          >
            {processing ? '处理中...' : '开始处理'}
          </button>
          {(stage === 'complete' || stage === 'error') && (
            <button
              onClick={reset}
              className="btn-authority btn-secondary-glass text-sm px-3 py-1"
            >
              重置
            </button>
          )}
        </div>
      </div>

      {/* 状态显示 */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            {getStatusIcon()}
            <span className="ml-2 text-sm font-medium">{getStageText()}</span>
          </div>
          {processing && (
            <span className="text-sm text-gray-500">{progress}%</span>
          )}
        </div>

        {processing && (
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        )}
      </div>

      {/* HTML内容分析 */}
      {htmlContent && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium mb-2">HTML 内容分析</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="text-gray-600">文件名:</span>
              <span className="ml-2 font-medium">{htmlContent.fileName}</span>
            </div>
            <div>
              <span className="text-gray-600">大小:</span>
              <span className="ml-2 font-medium">
                {(htmlContent.size / 1024).toFixed(1)} KB
              </span>
            </div>
            <div>
              <span className="text-gray-600">完整文档:</span>
              <span
                className={`ml-2 ${htmlContent.isComplete ? 'text-green-600' : 'text-yellow-600'}`}
              >
                {htmlContent.isComplete ? '是' : '否'}
              </span>
            </div>
            <div>
              <span className="text-gray-600">包含样式:</span>
              <span
                className={`ml-2 ${htmlContent.hasStyles ? 'text-green-600' : 'text-gray-500'}`}
              >
                {htmlContent.hasStyles ? '是' : '否'}
              </span>
            </div>
          </div>

          {/* 特征标签 */}
          <div className="mt-2 flex flex-wrap gap-1">
            {htmlContent.isComplete && (
              <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                完整HTML
              </span>
            )}
            {htmlContent.hasStyles && (
              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                包含样式
              </span>
            )}
            {htmlContent.hasScripts && (
              <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">
                包含脚本
              </span>
            )}
          </div>
        </div>
      )}

      {/* 处理结果 */}
      {result && stage === 'complete' && (
        <div className="p-3 bg-green-50 rounded-lg border border-green-200">
          <h4 className="text-sm font-medium text-green-800 mb-2">处理完成</h4>
          <div className="space-y-2 text-sm">
            <div>
              <span className="text-gray-600">CDN URL:</span>
              <a
                href={result.cdnUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="ml-2 text-blue-600 hover:text-blue-800 underline break-all"
              >
                {result.cdnUrl}
              </a>
            </div>
            <div>
              <span className="text-gray-600">下载链接:</span>
              <a
                href={result.downloadUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="ml-2 text-blue-600 hover:text-blue-800 underline break-all"
              >
                {result.downloadUrl}
              </a>
            </div>
            {result.previewUrl && (
              <div>
                <span className="text-gray-600">预览链接:</span>
                <a
                  href={result.previewUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="ml-2 text-blue-600 hover:text-blue-800 underline break-all"
                >
                  {result.previewUrl}
                </a>
              </div>
            )}
          </div>

          {result.metadata && (
            <div className="mt-2 pt-2 border-t border-green-200">
              <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                <div>
                  原始大小: {(result.metadata.originalSize / 1024).toFixed(1)}{' '}
                  KB
                </div>
                <div>
                  压缩后: {(result.metadata.compressedSize / 1024).toFixed(1)}{' '}
                  KB
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 错误显示 */}
      {stage === 'error' && (
        <div className="p-3 bg-red-50 rounded-lg border border-red-200">
          <h4 className="text-sm font-medium text-red-800 mb-1">处理失败</h4>
          <p className="text-sm text-red-700">请检查HTML内容格式是否正确</p>
        </div>
      )}

      {/* 说明信息 */}
      {stage === 'idle' && (
        <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="text-sm font-medium text-blue-800 mb-1">
            HTML 处理说明
          </h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• 支持完整HTML文档和HTML片段</li>
            <li>• 自动检测并保留样式和脚本</li>
            <li>• 直接上传到CDN，提供预览和下载链接</li>
            <li>• 不经过Playground，独立处理流程</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default HTMLPipelineProcessor;
