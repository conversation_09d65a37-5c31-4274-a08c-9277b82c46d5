import React, { useMemo } from 'react';

// -----------------------------------------------------------------------------
// ProgressStatusIndicator 组件
// -----------------------------------------------------------------------------
// 该组件显示批处理过程中的关键状态节点，简洁地展示处理进度上方的状态信息
// 包括：开始接收流式数据、进度计算、处理状态等关键节点
// -----------------------------------------------------------------------------

interface StatusNode {
  id: string;
  timestamp: number;
  message: string;
  query?: string;
  type: 'streaming' | 'progress' | 'processing' | 'complete' | 'error';
}

interface ProgressStatusIndicatorProps {
  /** 当前处理状态 */
  currentStatus: 'idle' | 'processing' | 'completed' | 'error';
  /** 进度统计信息 */
  progress: {
    total: number;
    succeeded: number;
    failed: number;
    processing: number;
    pending: number;
  };
  /** 最近的状态节点信息 */
  recentStatusNodes?: StatusNode[];
  /** 当前正在处理的查询 */
  currentProcessingQueries?: string[];
  /** 显示模式：完整显示或紧凑显示 */
  mode?: 'full' | 'compact';
}

export const ProgressStatusIndicator: React.FC<
  ProgressStatusIndicatorProps
> = ({
  currentStatus,
  progress,
  recentStatusNodes = [],
  currentProcessingQueries = [],
  mode = 'compact',
}) => {
  // 格式化状态消息
  const formatStatusMessage = (node: StatusNode): string => {
    if (node.type === 'streaming' && node.query) {
      const truncatedQuery =
        node.query.length > 20
          ? `${node.query.substring(0, 20)}...`
          : node.query;
      return `开始处理: "${truncatedQuery}"`;
    }

    if (node.type === 'progress') {
      return `进度更新: ${progress.succeeded + progress.failed}/${progress.total}`;
    }

    return node.message;
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'processing':
        return (
          <svg
            className="animate-spin h-4 w-4 text-blue-500"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        );
      case 'completed':
        return (
          <svg
            className="h-4 w-4 text-green-500"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clipRule="evenodd"
            />
          </svg>
        );
      case 'error':
        return (
          <svg
            className="h-4 w-4 text-red-500"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clipRule="evenodd"
            />
          </svg>
        );
      default:
        return (
          <svg
            className="h-4 w-4 text-gray-400"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
              clipRule="evenodd"
            />
          </svg>
        );
    }
  };

  // 生成状态摘要
  const statusSummary = useMemo(() => {
    if (currentStatus === 'idle') {
      return '等待开始处理';
    }

    if (currentStatus === 'completed') {
      const successRate =
        progress.total > 0
          ? Math.round((progress.succeeded / progress.total) * 100)
          : 0;
      return `处理完成 (成功率: ${successRate}%)`;
    }

    if (currentStatus === 'error') {
      return '处理出现错误';
    }

    // processing 状态
    if (progress.processing > 0) {
      return `正在处理 ${progress.processing} 个查询`;
    }

    return '准备处理中';
  }, [currentStatus, progress]);

  // 紧凑模式显示
  if (mode === 'compact') {
    return (
      <div className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-md mb-3">
        <div className="flex items-center space-x-2">
          {getStatusIcon(currentStatus)}
          <span className="text-sm text-gray-700 font-medium">
            {statusSummary}
          </span>
        </div>

        {currentProcessingQueries.length > 0 && (
          <div className="text-xs text-gray-500 max-w-xs truncate">
            {currentProcessingQueries.slice(0, 2).map((query, index) => (
              <span key={index} className="mr-2">
                "{query.length > 15 ? `${query.substring(0, 15)}...` : query}"
              </span>
            ))}
            {currentProcessingQueries.length > 2 && (
              <span>等{currentProcessingQueries.length - 2}个</span>
            )}
          </div>
        )}
      </div>
    );
  }

  // 完整模式显示
  return (
    <div className="bg-white border rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-semibold text-gray-800">处理状态</h4>
        <div className="flex items-center space-x-2">
          {getStatusIcon(currentStatus)}
          <span className="text-sm text-gray-600">{statusSummary}</span>
        </div>
      </div>

      {/* 当前处理的查询列表 */}
      {currentProcessingQueries.length > 0 && (
        <div className="mb-3">
          <div className="text-xs text-gray-500 mb-1">正在处理：</div>
          <div className="space-y-1">
            {currentProcessingQueries.slice(0, 3).map((query, index) => (
              <div key={index} className="flex items-center space-x-2 text-xs">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                <span className="text-gray-700 truncate">
                  {query.length > 50 ? `${query.substring(0, 50)}...` : query}
                </span>
              </div>
            ))}
            {currentProcessingQueries.length > 3 && (
              <div className="text-xs text-gray-400">
                还有 {currentProcessingQueries.length - 3} 个查询正在处理...
              </div>
            )}
          </div>
        </div>
      )}

      {/* 最近状态节点 */}
      {recentStatusNodes.length > 0 && (
        <div>
          <div className="text-xs text-gray-500 mb-2">最近活动：</div>
          <div className="space-y-1 max-h-20 overflow-hidden">
            {recentStatusNodes.slice(-3).map(node => (
              <div key={node.id} className="flex items-start space-x-2 text-xs">
                <div className="w-1 h-1 bg-gray-400 rounded-full mt-1.5 flex-shrink-0" />
                <div className="text-gray-600 truncate">
                  <span className="text-gray-400">
                    {new Date(node.timestamp).toLocaleTimeString('zh-CN', {
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                    })}
                  </span>{' '}
                  {formatStatusMessage(node)}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProgressStatusIndicator;
