import React, { useState, useCallback } from 'react';
import Icon from './Icon';
import { PromptHistoryItem } from '../services/PromptHistoryService';
import { formatRelativeTime, truncateText } from '../utils/promptUtils';

interface PromptHistoryListProps {
  /** 历史记录列表 */
  items: PromptHistoryItem[];
  /** 是否正在加载 */
  isLoading: boolean;
  /** 错误信息 */
  error: Error | null;
  /** 使用提示词回调 */
  onUsePrompt: (item: PromptHistoryItem) => void;
  /** 重命名提示词回调 */
  onRenamePrompt: (id: string, newTitle: string) => Promise<boolean>;
  /** 删除提示词回调 */
  onDeletePrompt: (id: string) => Promise<boolean>;
  /** 清空历史记录回调 */
  onClearHistory: () => Promise<void>;
  /** 日志记录器 */
  logger: any;
}

/**
 * 提示词历史记录列表组件
 */
const PromptHistoryList: React.FC<PromptHistoryListProps> = ({
  items,
  isLoading,
  error,
  onUsePrompt,
  onRenamePrompt,
  onDeletePrompt,
  onClearHistory,
  logger,
}) => {
  const [renamingId, setRenamingId] = useState<string | null>(null);
  const [newTitle, setNewTitle] = useState('');
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [isClearing, setIsClearing] = useState(false);

  // 开始重命名
  const handleStartRename = useCallback((item: PromptHistoryItem) => {
    setRenamingId(item.id);
    setNewTitle(item.title);
  }, []);

  // 确认重命名
  const handleConfirmRename = useCallback(async () => {
    if (!renamingId || !newTitle.trim()) {
      return;
    }

    try {
      const success = await onRenamePrompt(renamingId, newTitle.trim());
      if (success) {
        logger.success('重命名成功');
      } else {
        logger.error('重命名失败');
      }
    } catch (error) {
      logger.error(
        `重命名失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    } finally {
      setRenamingId(null);
      setNewTitle('');
    }
  }, [renamingId, newTitle, onRenamePrompt, logger]);

  // 取消重命名
  const handleCancelRename = useCallback(() => {
    setRenamingId(null);
    setNewTitle('');
  }, []);

  // 删除提示词
  const handleDelete = useCallback(
    async (id: string) => {
      setDeletingId(id);
      try {
        const success = await onDeletePrompt(id);
        if (success) {
          logger.success('删除成功');
        } else {
          logger.error('删除失败');
        }
      } catch (error) {
        logger.error(
          `删除失败: ${error instanceof Error ? error.message : String(error)}`,
        );
      } finally {
        setDeletingId(null);
      }
    },
    [onDeletePrompt, logger],
  );

  // 清空历史记录
  const handleClearHistory = useCallback(async () => {
    if (!window.confirm('确定要清空所有历史记录吗？此操作不可撤销。')) {
      return;
    }

    setIsClearing(true);
    try {
      await onClearHistory();
      logger.success('历史记录已清空');
    } catch (error) {
      logger.error(
        `清空失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    } finally {
      setIsClearing(false);
    }
  }, [onClearHistory, logger]);

  // 处理键盘事件
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        handleConfirmRename();
      } else if (e.key === 'Escape') {
        e.preventDefault();
        handleCancelRename();
      }
    },
    [handleConfirmRename, handleCancelRename],
  );

  // 渲染加载状态
  if (isLoading) {
    return (
      <div className="history-loading-state">
        <Icon type="processing" size="xl" color="processing" animate />
        <p>正在加载历史记录...</p>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div
        className="history-empty-state"
        style={{
          borderColor: 'var(--color-error)',
          background:
            'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(252, 228, 236, 0.6) 100%)',
        }}
      >
        <Icon type="warning" color="error" size="xl" />
        <p style={{ color: 'var(--color-error)' }}>加载历史记录失败</p>
        <p
          style={{
            fontSize: '12px',
            marginTop: '8px',
            opacity: 0.7,
            color: 'var(--color-error)',
          }}
        >
          {error.message}
        </p>
      </div>
    );
  }

  // 渲染空状态
  if (items.length === 0) {
    return (
      <div className="history-empty-state">
        <Icon type="history" color="neutral" size="xl" />
        <p>暂无历史记录</p>
        <p style={{ fontSize: '12px', marginTop: '8px', opacity: 0.7 }}>
          保存提示词后，历史记录将显示在这里
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* 标题和操作 */}
      <div
        className="flex items-center justify-between mb-6 flex-shrink-0 p-4 rounded-lg"
        style={{
          background:
            'linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-white) 50%, var(--color-blue-50) 100%)',
          border: '1px solid var(--color-primary-100)',
        }}
      >
        <h3
          className="flex items-center gap-3 font-semibold text-lg"
          style={{ color: 'var(--color-primary-600)' }}
        >
          <Icon type="history" color="primary" size="md" />
          历史记录 ({items.length}/10)
        </h3>
        {items.length > 0 && (
          <button
            onClick={handleClearHistory}
            disabled={isClearing}
            className="history-action-btn"
            style={{
              background: isClearing
                ? 'linear-gradient(135deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%)'
                : 'linear-gradient(135deg, var(--color-error-light) 0%, rgba(216, 27, 96, 0.1) 100%)',
              color: isClearing
                ? 'var(--color-gray-500)'
                : 'var(--color-error)',
              border: `1px solid ${isClearing ? 'var(--color-gray-300)' : 'var(--color-error)'}`,
              cursor: isClearing ? 'not-allowed' : 'pointer',
            }}
            title="清空所有历史记录"
          >
            <Icon
              type="trash"
              size="sm"
              color={isClearing ? 'neutral' : 'error'}
            />
            {isClearing ? '清空中...' : '清空'}
          </button>
        )}
      </div>

      {/* 历史记录列表 - 紧凑布局优化 */}
      <div className="flex-1 space-y-2 overflow-y-auto optimized-scrollbar p-1 compact-list-container">
        {items.map(item => (
          <div key={item.id} className="history-record-item compact-list-item">
            {/* 标题行 - 紧凑间距 */}
            <div className="flex items-center justify-between mb-2">
              {renamingId === item.id ? (
                <div className="flex-1 flex items-center gap-2">
                  <input
                    type="text"
                    value={newTitle}
                    onChange={e => setNewTitle(e.target.value)}
                    onKeyDown={handleKeyDown}
                    className="flex-1 py-1.5 px-2.5 text-sm rounded-md border border-gray-200 focus:outline-none focus:ring-2"
                    style={{
                      background: 'var(--color-white)',
                      borderColor: 'var(--color-primary-200)',
                      focusRingColor: 'var(--color-primary-300)',
                    }}
                    placeholder="输入新标题"
                    autoFocus
                  />
                  <button
                    onClick={handleConfirmRename}
                    className="history-action-btn"
                    disabled={!newTitle.trim()}
                  >
                    <Icon type="check" size="sm" color="success" />
                    确认
                  </button>
                  <button
                    onClick={handleCancelRename}
                    className="history-action-btn"
                  >
                    <Icon type="close" size="sm" color="error" />
                    取消
                  </button>
                </div>
              ) : (
                <>
                  <h4
                    className="flex-1 truncate font-medium text-base cursor-pointer compact-title"
                    onClick={() => onUsePrompt(item)}
                    title={item.title}
                    style={{ color: 'var(--color-primary-700)' }}
                  >
                    {item.title}
                  </h4>
                  <div className="flex items-center gap-2 ml-3">
                    <button
                      onClick={e => {
                        e.stopPropagation();
                        handleStartRename(item);
                      }}
                      className="p-1.5 rounded-md transition-all duration-200"
                      style={{
                        background: 'rgba(233, 30, 99, 0.05)',
                        border: '1px solid rgba(233, 30, 99, 0.1)',
                      }}
                      onMouseEnter={e => {
                        e.currentTarget.style.background =
                          'rgba(233, 30, 99, 0.1)';
                        e.currentTarget.style.transform = 'scale(1.05)';
                      }}
                      onMouseLeave={e => {
                        e.currentTarget.style.background =
                          'rgba(233, 30, 99, 0.05)';
                        e.currentTarget.style.transform = 'scale(1)';
                      }}
                      title="重命名"
                    >
                      <Icon type="edit" color="primary" size="sm" />
                    </button>
                    <button
                      onClick={e => {
                        e.stopPropagation();
                        handleDelete(item.id);
                      }}
                      disabled={deletingId === item.id}
                      className="p-1.5 rounded-md transition-all duration-200"
                      style={{
                        background: 'rgba(216, 27, 96, 0.05)',
                        border: '1px solid rgba(216, 27, 96, 0.1)',
                      }}
                      onMouseEnter={e => {
                        if (!e.currentTarget.disabled) {
                          e.currentTarget.style.background =
                            'rgba(216, 27, 96, 0.1)';
                          e.currentTarget.style.transform = 'scale(1.05)';
                        }
                      }}
                      onMouseLeave={e => {
                        e.currentTarget.style.background =
                          'rgba(216, 27, 96, 0.05)';
                        e.currentTarget.style.transform = 'scale(1)';
                      }}
                      title="删除"
                    >
                      {deletingId === item.id ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-pink-600 border-t-transparent" />
                      ) : (
                        <Icon type="delete" color="error" size="sm" />
                      )}
                    </button>
                  </div>
                </>
              )}
            </div>

            {/* 内容预览 */}
            <div
              className="history-record-content cursor-pointer compact-content"
              onClick={() => onUsePrompt(item)}
            >
              {truncateText(item.content, 120)}
            </div>

            {/* 元信息 - 紧凑间距 */}
            <div
              className="flex items-center justify-between text-xs mb-2 cursor-pointer compact-meta"
              style={{ color: 'var(--color-blue-500)' }}
              onClick={() => onUsePrompt(item)}
            >
              <div className="flex items-center gap-2">
                <Icon type="clock" size="sm" color="secondary" />
                <span>{formatRelativeTime(item.lastUsedAt)}</span>
              </div>
              <div className="flex items-center gap-4">
                <span className="flex items-center gap-1">
                  <Icon type="refresh" size="sm" color="primary" />
                  {item.usageCount} 次
                </span>
                <span className="flex items-center gap-1">
                  <Icon type="info" size="sm" color="neutral" />
                  {item.content.length} 字符
                </span>
              </div>
            </div>

            {/* 使用按钮 */}
            <div className="history-record-actions compact-action-group">
              <button
                onClick={e => {
                  e.stopPropagation();
                  onUsePrompt(item);
                }}
                className="history-action-btn w-full justify-center py-2 compact-action-btn"
                style={{
                  background:
                    'linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-blue-500) 100%)',
                  color: 'white',
                  fontWeight: '500',
                }}
              >
                <Icon type="use" color="neutral" size="sm" />
                使用此提示词
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PromptHistoryList;
