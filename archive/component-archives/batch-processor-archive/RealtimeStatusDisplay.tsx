/**
 * 实时状态展示组件
 *
 * 在300px高度限制内展示批处理的实时状态信息
 * 包括进度、内容分析、操作状态等详细信息
 */

import React, { useState, useEffect, useMemo } from 'react';
import Icon from './Icon';

// 状态数据接口
interface BatchProcessingStatus {
  progress: {
    total: number;
    completed: number;
    processing: number;
    pending: number;
    succeeded: number;
    failed: number;
    percentage: number;
    successRate: number;
  };
  performance: {
    startTime: number;
    currentTime: number;
    elapsedTime: number;
    estimatedRemaining: number;
    throughput: number;
    averageTaskTime: number;
  };
  currentTask: {
    id: string;
    query: string;
    stage: 'api' | 'parsing' | 'analysis' | 'upload' | 'complete';
    stageProgress: number;
  } | null;
}

interface ContentAnalysisStatus {
  detection: {
    type: 'lynx' | 'html' | 'mixed' | 'unknown' | 'analyzing';
    confidence: number;
    processingTime: number;
  };
  features: {
    lynxFeatures: Array<{ name: string; count: number; confidence: number }>;
    htmlFeatures: Array<{ name: string; count: number; confidence: number }>;
  };
  recommendation: {
    pipeline: 'lynx-pipeline' | 'html-pipeline' | 'mixed-pipeline';
    reason: string;
  };
  statistics: {
    totalAnalyzed: number;
    lynxDetected: number;
    htmlDetected: number;
    mixedDetected: number;
    analysisAccuracy: number;
  };
}

interface OperationStageStatus {
  apiCall: {
    status:
      | 'idle'
      | 'connecting'
      | 'sending'
      | 'receiving'
      | 'success'
      | 'error';
    progress: number;
    startTime?: number;
    duration?: number;
    requestSize?: number;
    responseSize?: number;
    errorMessage?: string;
    retryCount?: number;
  };
  parsing: {
    status:
      | 'idle'
      | 'extracting'
      | 'structuring'
      | 'validating'
      | 'success'
      | 'error';
    progress: number;
    extractedBlocks: number;
    totalFiles: number;
    currentFile?: string;
    parsingStrategy?: string;
    errorMessage?: string;
  };
  analysis: {
    status: 'idle' | 'analyzing' | 'classifying' | 'success' | 'error';
    progress: number;
    detectedType?: 'lynx' | 'html' | 'mixed';
    confidence?: number;
    featuresFound: number;
    processingTime?: number;
    errorMessage?: string;
  };
  upload: {
    status:
      | 'idle'
      | 'preparing'
      | 'compressing'
      | 'uploading'
      | 'finalizing'
      | 'success'
      | 'error';
    progress: number;
    originalSize?: number;
    compressedSize?: number;
    uploadedBytes?: number;
    uploadSpeed?: number;
    cdnUrl?: string;
    downloadUrl?: string;
    errorMessage?: string;
  };
  completion: {
    status: 'pending' | 'generating' | 'success' | 'error';
    finalUrl?: string;
    totalProcessingTime: number;
    resultType: 'lynx' | 'html';
    successMessage?: string;
    errorMessage?: string;
  };
}

interface RealtimeStatusDisplayProps {
  batchStatus?: BatchProcessingStatus;
  contentAnalysis?: ContentAnalysisStatus;
  operationStatus?: OperationStageStatus;
  isActive?: boolean;
  onStatusUpdate?: (status: any) => void;
}

// 状态图标组件
const StatusIcon: React.FC<{ status: string; className?: string }> = ({
  status,
  className = '',
}) => {
  switch (status) {
    case 'success':
      return (
        <Icon type="check" color="success" size="sm" className={className} />
      );
    case 'error':
      return (
        <Icon type="close" color="error" size="sm" className={className} />
      );
    case 'idle':
      return (
        <Icon type="circle" color="gray" size="sm" className={className} />
      );
    default:
      return (
        <Icon
          type="refresh"
          color="primary"
          size="sm"
          className={`${className} animate-spin`}
        />
      );
  }
};

// 进度条组件
const ProgressBar: React.FC<{
  progress: number;
  className?: string;
  color?: 'primary' | 'success' | 'warning' | 'error';
}> = ({ progress, className = '', color = 'primary' }) => {
  const colorClasses = {
    primary: 'bg-gradient-to-r from-blue-500 to-blue-600',
    success: 'bg-gradient-to-r from-green-500 to-green-600',
    warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600',
    error: 'bg-gradient-to-r from-red-500 to-red-600',
  };

  return (
    <div className={`w-full bg-gray-200 rounded-full h-2 ${className}`}>
      <div
        className={`h-2 rounded-full transition-all duration-300 ease-out ${colorClasses[color]}`}
        style={{ width: `${Math.max(0, Math.min(100, progress))}%` }}
      />
    </div>
  );
};

// 时间格式化
const formatTime = (seconds: number): string => {
  if (seconds < 60) {
    return `${Math.round(seconds)}秒`;
  }
  if (seconds < 3600) {
    return `${Math.floor(seconds / 60)}分${Math.round(seconds % 60)}秒`;
  }
  return `${Math.floor(seconds / 3600)}小时${Math.floor((seconds % 3600) / 60)}分钟`;
};

// 字节格式化
const formatBytes = (bytes: number): string => {
  if (bytes === 0) {
    return '0 B';
  }
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

const RealtimeStatusDisplay: React.FC<RealtimeStatusDisplayProps> = ({
  batchStatus,
  contentAnalysis,
  operationStatus,
  isActive = false,
  onStatusUpdate,
}) => {
  const [currentTime, setCurrentTime] = useState(Date.now());

  // 定期更新当前时间
  useEffect(() => {
    if (isActive) {
      const interval = setInterval(() => {
        setCurrentTime(Date.now());
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isActive]);

  // 计算实时性能指标
  const performanceMetrics = useMemo(() => {
    if (!batchStatus?.performance) {
      return null;
    }

    const elapsed = (currentTime - batchStatus.performance.startTime) / 1000;
    const remaining = batchStatus.performance.estimatedRemaining;

    return {
      elapsed,
      remaining,
      throughput: batchStatus.performance.throughput,
      averageTime: batchStatus.performance.averageTaskTime,
    };
  }, [batchStatus, currentTime]);

  // 获取内容类型图标
  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'html':
        return '🌐';
      case 'lynx':
        return '📱';
      case 'mixed':
        return '🔄';
      case 'analyzing':
        return '🔍';
      default:
        return '❓';
    }
  };

  // 获取置信度颜色
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) {
      return 'text-green-600 bg-green-100';
    }
    if (confidence >= 0.6) {
      return 'text-yellow-600 bg-yellow-100';
    }
    return 'text-red-600 bg-red-100';
  };

  if (!isActive && !batchStatus) {
    return (
      <div className="h-[300px] flex items-center justify-center bg-gray-50 rounded-lg border">
        <div className="text-center text-gray-500">
          <Icon type="clock" color="gray" size="lg" className="mb-2" />
          <div className="text-sm">等待批处理开始...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-[300px] bg-white rounded-lg border shadow-sm overflow-hidden flex flex-col">
      {/* 状态标题栏 (40px) */}
      <div className="h-10 px-3 border-b border-gray-200 flex items-center justify-between bg-gray-50">
        <h2 className="text-sm font-medium flex items-center">
          <StatusIcon
            status={
              batchStatus?.progress.processing > 0
                ? 'processing'
                : batchStatus?.progress.failed > 0
                  ? 'error'
                  : batchStatus?.progress.completed ===
                      batchStatus?.progress.total
                    ? 'success'
                    : 'idle'
            }
            className="mr-2"
          />
          <span className="truncate">
            {batchStatus?.progress.processing > 0
              ? `正在处理 ${batchStatus.progress.processing} 个查询 (${batchStatus.progress.percentage.toFixed(0)}% 完成)`
              : batchStatus?.progress.failed > 0
                ? `处理出现错误 (${batchStatus.progress.failed} 个任务失败)`
                : batchStatus?.progress.completed ===
                      batchStatus?.progress.total &&
                    batchStatus?.progress.total > 0
                  ? `处理完成 (成功率: ${Math.round((batchStatus.progress.succeeded / batchStatus.progress.total) * 100)}%)`
                  : '等待开始处理'}
          </span>
        </h2>
        {batchStatus?.currentTask && (
          <div className="text-xs text-gray-500 max-w-32 truncate">
            {batchStatus.currentTask.query}
          </div>
        )}
      </div>

      {/* 进度概览区 (80px) */}
      {batchStatus && (
        <div className="h-20 px-3 py-2 border-b border-gray-100">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-xs font-medium text-gray-700">
                总体进度
              </span>
              <span className="text-xs text-gray-600">
                {batchStatus.progress.completed}/{batchStatus.progress.total} (
                {batchStatus.progress.percentage.toFixed(0)}%)
              </span>
            </div>
            <ProgressBar progress={batchStatus.progress.percentage} />
            <div className="grid grid-cols-4 gap-1 text-center">
              <div className="bg-green-50 rounded p-1">
                <div className="text-sm font-bold text-green-600">
                  {batchStatus.progress.succeeded}
                </div>
                <div className="text-xs text-gray-600">成功</div>
              </div>
              <div className="bg-red-50 rounded p-1">
                <div className="text-sm font-bold text-red-600">
                  {batchStatus.progress.failed}
                </div>
                <div className="text-xs text-gray-600">失败</div>
              </div>
              <div className="bg-blue-50 rounded p-1">
                <div className="text-sm font-bold text-blue-600">
                  {batchStatus.progress.processing}
                </div>
                <div className="text-xs text-gray-600">处理中</div>
              </div>
              <div className="bg-gray-50 rounded p-1">
                <div className="text-sm font-bold text-gray-600">
                  {batchStatus.progress.pending}
                </div>
                <div className="text-xs text-gray-600">等待</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 内容识别区 (60px) */}
      {contentAnalysis && (
        <div className="h-15 px-3 py-2 border-b border-gray-100">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs font-medium text-gray-700">内容识别</span>
            {contentAnalysis.detection.type !== 'analyzing' && (
              <div
                className={`px-2 py-0.5 rounded-full text-xs font-medium ${getConfidenceColor(contentAnalysis.detection.confidence)}`}
              >
                {getContentTypeIcon(contentAnalysis.detection.type)}{' '}
                {contentAnalysis.detection.type.toUpperCase()}{' '}
                {(contentAnalysis.detection.confidence * 100).toFixed(0)}%
              </div>
            )}
          </div>
          {contentAnalysis.detection.type !== 'analyzing' && (
            <div className="flex flex-wrap gap-1">
              {contentAnalysis.features.lynxFeatures
                .slice(0, 2)
                .map((feature, index) => (
                  <span
                    key={`lynx-${index}`}
                    className="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-blue-100 text-blue-800"
                  >
                    📱 {feature.name}
                  </span>
                ))}
              {contentAnalysis.features.htmlFeatures
                .slice(0, 2)
                .map((feature, index) => (
                  <span
                    key={`html-${index}`}
                    className="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-green-100 text-green-800"
                  >
                    🌐 {feature.name}
                  </span>
                ))}
            </div>
          )}
        </div>
      )}

      {/* 操作详情区 (100px) */}
      {operationStatus && (
        <div className="flex-1 px-3 py-2 border-b border-gray-100 overflow-y-auto">
          <div className="space-y-2">
            {/* API 调用 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <StatusIcon status={operationStatus.apiCall.status} />
                <span className="text-xs text-gray-700">API 调用</span>
              </div>
              <div className="text-xs text-gray-500">
                {operationStatus.apiCall.status === 'success' &&
                  operationStatus.apiCall.duration && (
                    <span>
                      {(operationStatus.apiCall.duration / 1000).toFixed(1)}s
                    </span>
                  )}
                {operationStatus.apiCall.status !== 'idle' &&
                  operationStatus.apiCall.status !== 'success' &&
                  operationStatus.apiCall.status !== 'error' && (
                    <span>{operationStatus.apiCall.progress}%</span>
                  )}
              </div>
            </div>

            {/* 内容解析 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <StatusIcon status={operationStatus.parsing.status} />
                <span className="text-xs text-gray-700">内容解析</span>
              </div>
              <div className="text-xs text-gray-500">
                {operationStatus.parsing.totalFiles > 0 && (
                  <span>{operationStatus.parsing.totalFiles} 文件</span>
                )}
                {operationStatus.parsing.status !== 'idle' &&
                  operationStatus.parsing.status !== 'success' &&
                  operationStatus.parsing.status !== 'error' && (
                    <span>{operationStatus.parsing.progress}%</span>
                  )}
              </div>
            </div>

            {/* 内容分析 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <StatusIcon status={operationStatus.analysis.status} />
                <span className="text-xs text-gray-700">内容分析</span>
              </div>
              <div className="text-xs text-gray-500">
                {operationStatus.analysis.detectedType && (
                  <span>
                    {operationStatus.analysis.detectedType.toUpperCase()}
                  </span>
                )}
                {operationStatus.analysis.status !== 'idle' &&
                  operationStatus.analysis.status !== 'success' &&
                  operationStatus.analysis.status !== 'error' && (
                    <span>{operationStatus.analysis.progress}%</span>
                  )}
              </div>
            </div>

            {/* CDN 上传 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <StatusIcon status={operationStatus.upload.status} />
                <span className="text-xs text-gray-700">CDN 上传</span>
              </div>
              <div className="text-xs text-gray-500">
                {operationStatus.upload.status === 'success' &&
                  operationStatus.upload.compressedSize && (
                    <span>
                      {formatBytes(operationStatus.upload.compressedSize)}
                    </span>
                  )}
                {operationStatus.upload.status !== 'idle' &&
                  operationStatus.upload.status !== 'success' &&
                  operationStatus.upload.status !== 'error' && (
                    <span>{operationStatus.upload.progress}%</span>
                  )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 底部信息区 (20px) */}
      <div className="h-5 px-3 py-1 bg-gray-50 flex items-center justify-between text-xs text-gray-500">
        <span>
          {performanceMetrics && (
            <>⚡ {performanceMetrics.throughput.toFixed(1)} 任务/分钟</>
          )}
        </span>
        <span>
          {performanceMetrics && performanceMetrics.remaining > 0 && (
            <>⏱️ 预计 {formatTime(performanceMetrics.remaining)}</>
          )}
        </span>
      </div>
    </div>
  );
};

export default RealtimeStatusDisplay;
