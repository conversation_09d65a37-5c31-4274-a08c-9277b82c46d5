# Lynx2Web功能调试指南

## 📊 Console日志说明

Lynx2Web功能已配备详细的console.log调试信息，帮助你了解每个步骤的进度和失败原因。

## 🔍 日志结构

### 日志前缀说明
- `🎯` - 用户交互入口（WebPreviewButton）
- `🔧` - 服务层操作（WebPreviewService）
- `🔍` - 内容验证（LynxValidator）
- `📸` - 截图服务（ScreenshotService）
- `🔧 [InlineWorker]` - Web Worker内部操作

### 时间统计
- `⏱️ 总预览生成时间` - 整个预览生成过程
- `⏱️ 转换服务调用时间` - WebPreviewService调用时间
- `⏱️ WebPreviewService总时间` - 服务层总时间
- `⏱️ Worker转换时间` - Worker内部转换时间
- `⏱️ 总截图时间` - 截图服务总时间

## 📋 完整日志流程

### 1. 用户点击预览按钮
```
🎯 [WebPreviewButton] 开始处理预览请求
├── 📝 结果ID: batch-result-123
├── 📄 提取内容长度: 2547
├── 📄 提取内容预览: <FILES><FILE path="index.ttml">...
├── 🔒 禁用状态: false
└── 🚀 开始预览生成流程
```

### 2. 服务层初始化
```
🔧 [WebPreviewService] 开始Lynx转Web预览
├── 📋 服务配置: { workerPath: "/workers/lynx-converter.js", ... }
├── 📝 输入参数: { contentLength: 2547, resultId: "...", options: {...} }
└── ⏱️ WebPreviewService总时间: 开始计时
```

### 3. 内容验证阶段
```
🔍 [WebPreviewService] 开始内容验证
├── 🔍 [LynxValidator.hasLynxContent] 开始Lynx内容检测
├── 🔍 [LynxValidator.detectLynxFeatures] 开始详细特征检测
├── 🔍 检测structure特征
├── 🔍 检测tags特征
├── 📊 最终检测结果: { confidence: "75.5%", hasLynxContent: true, ... }
└── ✓ 验证结果: { isValid: true }
```

### 4. Worker转换阶段
```
🔄 [WebPreviewService] 开始执行转换
├── 🔧 [WebPreviewService.createWorker] 开始创建Worker
├── 📁 尝试从文件路径加载Worker
├── ⚠️ 文件路径Worker加载失败: { error: "...", ... }
├── 🔄 降级到内联Worker
├── 🔧 [WebPreviewService.createInlineWorker] 开始创建内联Worker
├── ✅ 内联Worker创建成功
├── 📤 发送Worker消息: { type: "CONVERT", contentLength: 2547, ... }
├── 🔧 [InlineWorker] 收到消息: { type: "CONVERT", ... }
├── 🔄 [InlineWorker] 开始转换
├── 📝 [InlineWorker.convertLynxToHTML] 解析Lynx内容
├── 📋 解析结果: { ttmlLength: 1234, ttssLength: 456, jsLength: 89 }
├── ✅ [InlineWorker.convertLynxToHTML] 转换成功
└── 📨 收到Worker响应: { success: true, htmlLength: 3456, ... }
```

### 5. 截图生成阶段
```
📸 [ScreenshotService.captureHTML] 开始截图流程
├── 📦 检查依赖: { html2canvasLoaded: true, uploadServiceAvailable: true }
├── 🔧 创建临时iframe
├── ⏳ 等待iframe加载
├── 📷 开始截图
├── ✅ 截图生成成功: { width: 375, height: 667 }
├── 📦 转换为Blob: { size: 45678, type: "image/jpeg", sizeKB: "44.61KB" }
├── ☁️ 上传到CDN
├── ✅ CDN上传成功: { url: "https://cdn.example.com/..." }
└── 🎉 截图流程完成
```

### 6. 完成和展示
```
✅ [WebPreviewButton] 预览生成成功
├── 🎉 最终预览数据: { htmlPreview: "<!DOCTYPE html>...", screenshotInfo: {...} }
├── 🔔 回调函数已调用
└── 🏁 预览生成流程结束
```

## ❌ 常见错误和排查

### 1. 内容验证失败
```
⚠️ [WebPreviewService] 内容验证失败
├── 错误: "VALIDATION_ERROR"
├── 原因: "内容中未检测到Lynx代码"
└── 检查: 确保内容包含<view>、<text>等Lynx标签
```

### 2. Worker加载失败
```
⚠️ [WebPreviewService.createWorker] 文件路径Worker加载失败
├── 错误: "Uncaught SyntaxError: Unexpected token '<'"
├── 原因: EdenX环境下Worker文件路径访问失败
└── 解决: 自动降级到内联Worker
```

### 3. 转换失败
```
❌ [InlineWorker.convertLynxToHTML] 转换失败
├── 错误: "CONVERSION_ERROR"
├── 原因: "转换失败: ..."
└── 检查: Lynx语法是否正确
```

### 4. 截图失败
```
💥 [ScreenshotService.captureHTML] 截图失败
├── 错误: "html2canvas not available"
├── 原因: html2canvas依赖加载失败
└── 检查: 网络连接和依赖可用性
```

### 5. CDN上传失败
```
💥 [ScreenshotService.captureHTML] 截图失败
├── 错误: "Upload failed: ..."
├── 原因: CDN服务不可用
└── 检查: UploadService配置和网络连接
```

## 🛠 调试技巧

### 1. 启用详细日志
所有日志已默认启用，打开浏览器开发者工具的Console面板即可查看。

### 2. 过滤日志
在Console中使用过滤器：
- `[WebPreviewButton]` - 只看按钮组件的日志
- `[WebPreviewService]` - 只看服务层的日志
- `[LynxValidator]` - 只看内容验证的日志
- `[ScreenshotService]` - 只看截图服务的日志
- `[InlineWorker]` - 只看Worker内部的日志

### 3. 性能分析
关注这些时间指标：
- 如果"总预览生成时间"过长，检查网络和依赖加载
- 如果"Worker转换时间"过长，检查Lynx内容复杂度
- 如果"总截图时间"过长，检查html2canvas性能

### 4. 错误定位
1. 首先查看最外层的错误信息
2. 向下追踪具体的失败步骤
3. 检查相关的参数和状态信息
4. 对比成功案例的日志差异

## 📊 性能基准

### 正常情况下的预期时间
- 内容验证: < 50ms
- Worker创建: < 100ms（内联模式）
- 内容转换: < 200ms
- 截图生成: 1-3秒
- CDN上传: 0.5-2秒
- **总时间**: 2-6秒

### 如果超出预期时间
1. 检查网络连接
2. 检查依赖加载状态
3. 检查Lynx内容大小和复杂度
4. 检查CDN服务状态

## 🔧 故障排除流程

1. **打开开发者工具Console**
2. **清空控制台**
3. **点击Web预览按钮**
4. **观察日志输出**
5. **定位失败步骤**
6. **检查错误详情**
7. **根据错误类型采取对应措施**

---

有了这些详细的日志信息，你可以清楚地了解Lynx2Web功能的每个执行步骤，快速定位问题所在，并进行针对性的修复。