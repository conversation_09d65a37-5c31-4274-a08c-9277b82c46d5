# Lynx2Web 功能文档

## 概述

Lynx2Web是batch_processor中的一个核心功能模块，提供将AI生成的Lynx代码实时转换为Web预览的能力。该功能解决了Lynx代码无法在Web环境中直接预览的问题，为用户提供直观的可视化体验。

## 功能特性

### 🎯 核心功能
- **智能内容检测** - 自动识别并过滤包含Lynx语法的内容
- **实时转换** - 将TTML/TTSS/JS代码转换为标准HTML/CSS/JS
- **安全预览** - 使用iframe沙箱确保预览安全性
- **移动端适配** - 固定375×667px移动端预览尺寸
- **截图存储** - 自动生成缩略图并上传CDN
- **一键放大** - 点击缩略图查看全屏预览

### 🚀 技术特性
- **Web Worker架构** - 非阻塞后台转换处理
- **单次转换策略** - 失败直接显示错误，避免性能损耗
- **TypeScript支持** - 完整的类型定义和类型安全
- **响应式设计** - 适配不同屏幕尺寸和设备
- **无障碍支持** - 键盘导航和高对比度模式

## 架构设计

### 目录结构
```
lynx2web/
├── components/           # React组件
│   ├── WebPreviewButton.tsx    # 主预览按钮
│   ├── ThumbnailPreview.tsx    # 缩略图组件
│   ├── WebPreviewModal.tsx     # 全屏预览弹窗
│   └── index.ts                # 组件导出
├── services/            # 服务层
│   ├── WebPreviewService.ts    # 主服务类
│   ├── ScreenshotService.ts    # 截图服务
│   └── index.ts                # 服务导出
├── utils/               # 工具函数
│   ├── lynxValidator.ts        # Lynx内容验证
│   └── index.ts                # 工具导出
├── workers/             # Web Worker
│   └── lynx-converter.js       # 转换处理器
├── types/               # 类型定义
│   └── index.ts                # TypeScript类型
├── styles/              # 样式文件
│   └── lynx2web.css           # 组件样式
└── docs/                # 文档
    ├── README.md               # 功能文档
    ├── IMPLEMENTATION_COMPLETE.md  # 实现报告
    └── BUGFIX_REPORT.md        # 修复报告
```

### 数据流架构
```
用户操作 → WebPreviewButton → WebPreviewService → Web Worker
    ↓                                                    ↓
预览结果 ← ThumbnailPreview ← ScreenshotService ← HTML转换
    ↓
WebPreviewModal (全屏预览)
```

### 核心组件说明

#### WebPreviewButton
- **功能**: 主要的交互入口，显示转换状态和触发转换
- **状态**: idle → detecting → converting → capturing → success/error
- **位置**: 集成在ResultsPanel的成功结果项中

#### ThumbnailPreview
- **功能**: 显示120×160px的预览缩略图
- **特性**: 悬停效果、点击放大、加载状态显示
- **存储**: 截图自动上传CDN并缓存URL

#### WebPreviewModal
- **功能**: 全屏预览弹窗，支持iframe显示完整页面
- **交互**: ESC键关闭、背景点击关闭、全屏模式支持
- **安全**: iframe沙箱模式限制脚本执行范围

## 技术实现

### 转换流程

1. **内容验证** (LynxValidator)
   ```typescript
   const validation = validator.detectLynx(content);
   if (!validation.isValid) return error;
   ```

2. **Web Worker转换** (lynx-converter.js)
   ```javascript
   // TTML → HTML转换
   // TTSS → CSS转换  
   // JavaScript语法适配
   ```

3. **截图生成** (ScreenshotService)
   ```typescript
   const canvas = await html2canvas(previewElement);
   const screenshot = canvas.toDataURL('image/png');
   ```

4. **CDN上传** (集成UploadService)
   ```typescript
   const uploadResult = await uploadService.uploadToCDN(screenshot);
   ```

### 核心API

#### WebPreviewService
```typescript
class WebPreviewService {
  async convertToWebPreview(
    content: string, 
    resultId: string, 
    options?: ConversionOptions
  ): Promise<PreviewResult>
  
  validateContent(content: string): LynxDetectionResult
  dispose(): void
}
```

#### 类型定义
```typescript
interface PreviewResult {
  success: boolean;
  html?: string;
  screenshot?: ScreenshotData;
  error?: string;
  message?: string;
}

interface ScreenshotData {
  url: string;
  width: number;
  height: number;
  size: number;
}
```

## 集成方式

### 在ResultsPanel中的集成
```typescript
{status === 'success' && result.metadata?.extractedContent && (
  <WebPreviewButton
    result={{
      id: result.id,
      extractedContent: result.metadata.extractedContent,
      status: result.status,
      playgroundUrl: result.playgroundUrl
    }}
  />
)}
```

### 数据存储
```typescript
// 在EnhancedBatchProcessorService中
const result: ProcessResult = {
  // ... 其他字段
  metadata: {
    extractedContent: extractResult.extractedContent,
    fileCount: Object.keys(fileStructure).length,
    totalSize: totalSize,
    compressionRatio: compressionRatio,
  },
};
```

## 使用指南

### 用户操作流程

1. **触发转换**: 在批处理成功结果中点击"Web预览"按钮
2. **状态监控**: 按钮显示转换进度（检测→转换→截图→完成）
3. **查看缩略图**: 转换成功后显示120×160px预览图
4. **全屏查看**: 点击缩略图打开全屏预览弹窗
5. **交互操作**: 支持全屏模式、ESC关闭等

### 开发者集成

1. **引入组件**
   ```typescript
   import { WebPreviewButton } from '../lynx2web/components/WebPreviewButton';
   ```

2. **传递数据**
   ```typescript
   <WebPreviewButton
     result={{
       id: string,
       extractedContent: string,
       status: string,
       playgroundUrl?: string
     }}
   />
   ```

3. **样式导入**
   ```typescript
   import './lynx2web/styles/lynx2web.css';
   ```

## 配置选项

### 默认配置
```typescript
const DEFAULT_CONFIG = {
  workerPath: '/workers/lynx-converter.js',
  defaultTimeout: 15000,
  thumbnailSize: { width: 120, height: 160 },
  screenshotOptions: {
    format: 'png',
    quality: 0.8,
    maxSize: 1024 * 1024 // 1MB
  }
};
```

### 环境要求
- **依赖**: html2canvas (已包含在项目中)
- **浏览器**: 支持Web Worker和html2canvas的现代浏览器
- **权限**: CDN上传权限（复用现有UploadService）

## 错误处理

### 错误类型
- **VALIDATION_ERROR**: 内容不包含有效Lynx语法
- **CONVERSION_ERROR**: Web Worker转换失败
- **SCREENSHOT_ERROR**: 截图生成失败
- **UPLOAD_ERROR**: CDN上传失败

### 错误显示
```typescript
// 按钮状态显示
<button className="web-preview-error">
  <Icon type="error" />
  <span>{error.message}</span>
</button>
```

## 性能优化

### 优化策略
- **智能检测**: 仅对包含Lynx内容的结果显示按钮
- **Web Worker**: 避免主线程阻塞
- **缓存机制**: 转换结果和截图URL本地缓存
- **懒加载**: 按需加载Web Worker和html2canvas
- **压缩优化**: 截图压缩减少存储空间

### 监控指标
- 转换成功率
- 平均转换时间
- 截图生成时间
- CDN上传速度
- 用户交互率

## 安全考虑

### 安全措施
- **iframe沙箱**: 限制脚本执行权限
- **内容验证**: 严格的Lynx语法检测
- **URL白名单**: CDN域名限制
- **XSS防护**: 内容转义和沙箱隔离

### 沙箱配置
```html
<iframe 
  sandbox="allow-scripts allow-same-origin allow-forms"
  srcDoc={previewHtml}
/>
```

## 维护指南

### 常见问题排查

1. **转换失败**
   - 检查Web Worker文件是否正确部署
   - 验证Lynx内容格式是否符合PE规则
   - 查看浏览器控制台错误信息

2. **截图异常**
   - 确认html2canvas依赖可用
   - 检查预览元素是否正确渲染
   - 验证CDN上传服务状态

3. **性能问题**
   - 监控Web Worker执行时间
   - 检查内存使用情况
   - 优化转换算法效率

### 更新维护

1. **PE规则更新**: 同步更新lynx-converter.js转换逻辑
2. **依赖升级**: 定期更新html2canvas等依赖版本
3. **性能监控**: 持续监控转换成功率和性能指标
4. **用户反馈**: 根据用户使用情况优化交互体验

## 扩展开发

### 功能扩展点
- **多尺寸预览**: 支持平板、桌面端预览
- **主题切换**: 支持明暗主题预览
- **代码编辑**: 提供在线代码编辑功能
- **分享功能**: 支持预览结果分享
- **历史记录**: 保存转换历史记录

### API扩展
```typescript
interface WebPreviewServiceExtended {
  // 多尺寸预览
  convertToMultiSizePreview(content: string, sizes: PreviewSize[]): Promise<PreviewResult[]>
  
  // 主题预览
  convertWithTheme(content: string, theme: 'light' | 'dark'): Promise<PreviewResult>
  
  // 批量转换
  batchConvert(contents: string[]): Promise<PreviewResult[]>
}
```

---

## 更新日志

### v1.0.2 (2025-06-20)
- ✅ 修复Props接口不匹配问题
- ✅ 完善数据流存储逻辑
- ✅ 优化错误处理机制

### v1.0.1 (2025-06-20)
- ✅ 修复extractedContent数据源问题
- ✅ 完善服务层metadata存储

### v1.0.0 (2025-06-20)
- ✅ 初始版本发布
- ✅ 完整功能实现
- ✅ 文档和测试完善

---

**维护团队**: Claude Code Development Team  
**最后更新**: 2025-06-20  
**文档版本**: v1.0.2