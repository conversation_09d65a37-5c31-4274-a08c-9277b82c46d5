/**
 * 自动Web预览组件
 * 在解析成功有playground URL后自动生成Web iframe等比缩略图
 */

import React, { useEffect, useState, useRef } from 'react';
import { LynxValidator } from '../utils/lynxValidator';

export interface AutoWebPreviewProps {
  /** 结果数据 */
  result: {
    id: string;
    extractedContent: string;
    status: string;
    playgroundUrl?: string;
  };
  /** 缩略图尺寸 */
  thumbnailSize?: {
    width: number;
    height: number;
  };
  /** 点击回调 */
  onPreviewClick?: (result: any) => void;
}

export const AutoWebPreview: React.FC<AutoWebPreviewProps> = ({
  result,
  thumbnailSize = { width: 120, height: 160 },
  onPreviewClick,
}) => {
  const [previewHtml, setPreviewHtml] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 增强HTML内容以支持触控板滚动
  const enhanceHtmlForTouchScroll = (html: string): string => {
    // 如果已经是完整的HTML文档，增强现有内容
    if (html.includes('<!DOCTYPE html>') || html.includes('<html')) {
      // 在head标签中添加触控板滚动支持的样式和脚本
      return html.replace(
        '</head>',
        `
        <style>
          /* 触控板滚动增强 */
          html, body {
            overflow-y: auto !important;
            overflow-x: hidden;
            height: auto;
            min-height: 100vh;
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: contain;
            touch-action: pan-y pinch-zoom;
          }
          
          * {
            box-sizing: border-box;
          }
          
          /* 自定义滚动条 */
          ::-webkit-scrollbar {
            width: 8px;
          }
          
          ::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.05);
            border-radius: 4px;
          }
          
          ::-webkit-scrollbar-thumb {
            background: rgba(0,0,0,0.2);
            border-radius: 4px;
            transition: background 0.2s ease;
          }
          
          ::-webkit-scrollbar-thumb:hover {
            background: rgba(0,0,0,0.4);
          }
        </style>
        <script>
          // 触控板滚动支持脚本
          document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 [AutoWebPreview] 触控板滚动支持已加载');
            
            // 确保body可以滚动
            document.body.style.overflow = 'auto';
            document.documentElement.style.overflow = 'auto';
            
            // 处理触摸事件
            document.addEventListener('touchstart', function(e) {
              // 允许触摸事件传播
            }, { passive: true });
            
            document.addEventListener('touchmove', function(e) {
              // 允许触摸滚动
            }, { passive: true });
            
            // 处理滚轮事件
            document.addEventListener('wheel', function(e) {
              // 允许滚轮滚动
            }, { passive: true });
          });
        </script>
        </head>`,
      );
    }

    // 如果不是完整HTML，包装成完整文档
    return `
      <!DOCTYPE html>
      <html lang="zh-CN">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
          <title>Web预览</title>
          <style>
            html, body {
              margin: 0;
              padding: 12px;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
              line-height: 1.6;
              color: #333;
              background-color: #fff;
              
              /* 关键：确保可以滚动 */
              overflow-y: auto !important;
              overflow-x: hidden;
              height: auto;
              min-height: 100vh;
              
              /* 触控板和触摸滚动优化 */
              -webkit-overflow-scrolling: touch;
              overscroll-behavior: contain;
              touch-action: pan-y pinch-zoom;
            }
            
            * {
              box-sizing: border-box;
            }
            
            /* 自定义滚动条 */
            ::-webkit-scrollbar {
              width: 8px;
            }
            
            ::-webkit-scrollbar-track {
              background: rgba(0,0,0,0.05);
              border-radius: 4px;
            }
            
            ::-webkit-scrollbar-thumb {
              background: rgba(0,0,0,0.2);
              border-radius: 4px;
              transition: background 0.2s ease;
            }
            
            ::-webkit-scrollbar-thumb:hover {
              background: rgba(0,0,0,0.4);
            }
            
            img {
              max-width: 100%;
              height: auto;
              display: block;
            }
            
            pre, code {
              white-space: pre-wrap;
              word-wrap: break-word;
              overflow-wrap: break-word;
            }
          </style>
        </head>
        <body>
          <div class="preview-container">
            ${html}
          </div>
          
          <script>
            document.addEventListener('DOMContentLoaded', function() {
              console.log('📱 [AutoWebPreview] 触控板滚动支持已加载');
              
              // 确保可以滚动
              document.body.style.overflow = 'auto';
              document.documentElement.style.overflow = 'auto';
              
              // 确保最小高度以支持滚动
              const container = document.querySelector('.preview-container');
              if (container) {
                // 强制设置足够的高度来产生滚动
                const minHeight = Math.max(800, window.innerHeight * 1.5);
                container.style.minHeight = minHeight + 'px';
                console.log('📐 [AutoWebPreview] 设置容器最小高度:', minHeight);
              }
              
              // 处理触摸事件
              document.addEventListener('touchstart', function(e) {
                // 允许触摸事件传播
              }, { passive: true });
              
              document.addEventListener('touchmove', function(e) {
                // 允许触摸滚动
              }, { passive: true });
              
              // 处理滚轮事件
              document.addEventListener('wheel', function(e) {
                // 允许滚轮滚动
              }, { passive: true });
            });
          </script>
        </body>
      </html>
    `;
  };

  // 自动生成预览
  useEffect(() => {
    if (result.extractedContent && result.playgroundUrl) {
      generatePreview();
    }
  }, [result.extractedContent, result.playgroundUrl]);

  const generatePreview = async () => {
    console.log('🔄 [AutoWebPreview] 开始自动生成预览');
    console.log('📋 结果数据:', {
      id: result.id,
      hasContent: !!result.extractedContent,
      contentLength: result.extractedContent?.length || 0,
      hasPlaygroundUrl: !!result.playgroundUrl,
    });

    setIsLoading(true);
    setError(null);

    try {
      // 验证Lynx内容
      const hasLynxContent = LynxValidator.hasLynxContent(
        result.extractedContent,
      );
      if (!hasLynxContent) {
        console.warn('⚠️ [AutoWebPreview] 未检测到Lynx内容');
        setError('内容中未检测到Lynx代码');
        return;
      }

      // 使用WebPreviewService进行转换
      console.log('📝 [AutoWebPreview] 使用WebPreviewService转换');
      const { WebPreviewService } = await import(
        '../services/WebPreviewService'
      );
      const service = new WebPreviewService();

      const previewResult = await service.convertToWebPreview(
        result.extractedContent,
        result.id,
        { timeout: 10000 },
      );

      if (previewResult.success && previewResult.html) {
        console.log('✅ [AutoWebPreview] WebPreviewService转换成功');
        // 增强HTML内容以支持触控板滚动
        const enhancedHtml = enhanceHtmlForTouchScroll(previewResult.html);
        setPreviewHtml(enhancedHtml);
      } else {
        throw new Error(previewResult.message || 'WebPreviewService转换失败');
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '生成预览失败';
      console.error('❌ [AutoWebPreview] 预览生成失败:', errorMsg);
      setError(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理点击事件
  const handleClick = () => {
    if (result.playgroundUrl) {
      if (onPreviewClick) {
        onPreviewClick(result);
      } else {
        window.open(result.playgroundUrl, '_blank');
      }
    }
  };

  // 加载状态
  if (isLoading) {
    return (
      <div
        className="auto-web-preview-loading bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-center"
        style={{ width: thumbnailSize.width, height: thumbnailSize.height }}
      >
        <div className="text-center">
          <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2" />
          <p className="text-xs text-blue-600">生成中...</p>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div
        className="auto-web-preview-error bg-red-50 border border-red-200 rounded-lg flex items-center justify-center p-2"
        style={{ width: thumbnailSize.width, height: thumbnailSize.height }}
      >
        <div className="text-center">
          <svg
            className="w-6 h-6 text-red-500 mx-auto mb-1"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clipRule="evenodd"
            />
          </svg>
          <p className="text-xs text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  // 成功状态 - 显示iframe预览
  if (previewHtml) {
    return (
      <div
        ref={containerRef}
        className="auto-web-preview-success relative border border-gray-200 rounded-lg overflow-hidden cursor-pointer hover:shadow-md transition-shadow duration-200 group"
        style={{ width: thumbnailSize.width, height: thumbnailSize.height }}
        onClick={handleClick}
      >
        <iframe
          ref={iframeRef}
          srcDoc={previewHtml}
          className="w-full h-full border-none"
          sandbox="allow-scripts allow-same-origin allow-forms allow-pointer-lock allow-popups allow-modals"
          title={`Web预览 - ${result.id}`}
          style={{
            pointerEvents: 'auto',
            touchAction: 'pan-y pinch-zoom',
          }}
          onMouseEnter={() => {
            // 确保iframe获得焦点以支持滚动
            if (iframeRef.current) {
              setTimeout(() => {
                try {
                  iframeRef.current?.focus();
                } catch (e) {
                  console.warn('iframe focus failed:', e);
                }
              }, 0);
            }
          }}
        />

        {/* 悬停覆盖层 */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
          <div className="bg-white rounded-full p-2 shadow-lg">
            <svg
              className="w-4 h-4 text-gray-700"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
              />
            </svg>
          </div>
        </div>
      </div>
    );
  }

  // 默认状态 - 无预览
  return (
    <div
      className="auto-web-preview-empty bg-gray-50 border border-gray-200 rounded-lg flex items-center justify-center"
      style={{ width: thumbnailSize.width, height: thumbnailSize.height }}
    >
      <div className="text-center">
        <svg
          className="w-8 h-8 text-gray-400 mx-auto mb-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
          />
        </svg>
        <p className="text-xs text-gray-500">暂无预览</p>
      </div>
    </div>
  );
};

export default AutoWebPreview;
