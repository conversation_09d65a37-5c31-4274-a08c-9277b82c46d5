/**
 * 缩略图预览组件
 * 显示Web预览的缩略图，支持点击放大
 */

import React, { useState, useRef, useEffect } from 'react';
import type { ThumbnailPreviewProps } from '../types';
import { WebPreviewModal } from './WebPreviewModal';

export const ThumbnailPreview: React.FC<ThumbnailPreviewProps> = ({
  preview,
  resultId,
  width = 120,
  height = 160,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // 处理图片加载
  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(false);
  };

  // 处理点击放大
  const handleClick = () => {
    setShowModal(true);
  };

  // 关闭弹窗
  const handleCloseModal = () => {
    setShowModal(false);
  };

  // 渲染预览内容
  const renderPreviewContent = () => {
    // 优先使用截图
    if (preview.screenshot && !imageError) {
      return (
        <div className="relative w-full h-full">
          <img
            src={preview.screenshot.url}
            alt={`Web预览 - ${resultId}`}
            className={`w-full h-full object-cover transition-opacity duration-300 ${
              imageLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            onLoad={handleImageLoad}
            onError={handleImageError}
            style={{
              objectFit: 'cover',
              objectPosition: 'top center',
            }}
          />

          {/* 加载状态 */}
          {!imageLoaded && !imageError && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
              <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
            </div>
          )}
        </div>
      );
    }

    // 降级为iframe预览
    if (preview.html) {
      return (
        <iframe
          ref={iframeRef}
          srcDoc={preview.html}
          className="w-full h-full border-none"
          title={`iframe预览 - ${resultId}`}
          sandbox="allow-scripts allow-same-origin"
          style={{
            transform: 'scale(0.8)',
            transformOrigin: 'top left',
            width: '125%',
            height: '125%',
          }}
        />
      );
    }

    // 错误状态
    return (
      <div className="w-full h-full flex flex-col items-center justify-center bg-gray-100 text-gray-500">
        <svg className="w-8 h-8 mb-2" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
            clipRule="evenodd"
          />
        </svg>
        <span className="text-xs">预览失败</span>
      </div>
    );
  };

  return (
    <>
      {/* 缩略图容器 */}
      <div
        className="thumbnail-preview group relative cursor-pointer bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-105"
        style={{ width, height }}
        onClick={handleClick}
      >
        {/* 预览内容 */}
        <div className="w-full h-full">{renderPreviewContent()}</div>

        {/* 悬停遮罩 */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white bg-opacity-90 rounded-full p-2">
            <svg
              className="w-4 h-4 text-gray-700"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"
              />
            </svg>
          </div>
        </div>

        {/* 底部标签 */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2">
          <div className="text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            点击放大
          </div>
        </div>

        {/* 类型标识 */}
        <div className="absolute top-2 right-2">
          <div className="bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full font-medium opacity-80">
            Web
          </div>
        </div>
      </div>

      {/* 放大预览弹窗 */}
      {showModal && (
        <WebPreviewModal
          preview={preview}
          resultId={resultId}
          isOpen={showModal}
          onClose={handleCloseModal}
        />
      )}
    </>
  );
};
