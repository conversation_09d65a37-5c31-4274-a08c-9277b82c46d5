/**
 * Web预览按钮组件
 * 提供Lynx到Web预览的触发和状态显示
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import type {
  WebPreviewButtonProps,
  PreviewResult,
  PreviewStatus,
} from '../types';
import { WebPreviewService } from '../services/WebPreviewService';
import { ThumbnailPreview } from './ThumbnailPreview';

export const WebPreviewButton: React.FC<WebPreviewButtonProps> = ({
  result,
  onPreviewGenerated,
  disabled = false,
}) => {
  const [status, setStatus] = useState<PreviewStatus>('idle');
  const [preview, setPreview] = useState<PreviewResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const serviceRef = useRef<WebPreviewService | null>(null);
  const isGeneratingRef = useRef(false);

  // 初始化服务
  useEffect(() => {
    serviceRef.current = new WebPreviewService();

    return () => {
      serviceRef.current?.dispose();
    };
  }, []);

  /**
   * 生成Web预览
   */
  const generatePreview = useCallback(async () => {
    console.group('🎯 [WebPreviewButton] 开始处理预览请求');
    console.log('📝 结果ID:', result.id);
    console.log('📄 提取内容长度:', result.extractedContent?.length || 0);
    console.log(
      '📄 提取内容预览:',
      `${result.extractedContent?.substring(0, 200)}...`,
    );
    console.log('🔒 禁用状态:', disabled);
    console.log('🏃 当前正在生成:', isGeneratingRef.current);
    console.log('🔧 服务可用:', !!serviceRef.current);

    if (!serviceRef.current || isGeneratingRef.current || disabled) {
      console.warn('⚠️ [WebPreviewButton] 预览被阻止:', {
        hasService: !!serviceRef.current,
        isGenerating: isGeneratingRef.current,
        disabled,
      });
      console.groupEnd();
      return;
    }

    console.log('🚀 [WebPreviewButton] 开始预览生成流程');
    console.time('⏱️ 总预览生成时间');

    isGeneratingRef.current = true;
    setStatus('detecting');
    setError(null);

    try {
      // 检测阶段
      console.log('🕵️ [WebPreviewButton] 进入检测阶段');
      setStatus('detecting');
      await new Promise(resolve => setTimeout(resolve, 300)); // 短暂延迟以显示状态

      // 转换阶段
      console.log('🔄 [WebPreviewButton] 进入转换阶段');
      console.log('📋 转换参数:', {
        contentLength: result.extractedContent.length,
        resultId: result.id,
        timeout: 15000,
      });

      setStatus('converting');
      console.time('⏱️ 转换服务调用时间');

      const previewResult = await serviceRef.current.convertToWebPreview(
        result.extractedContent,
        result.id,
        { timeout: 15000 }, // 15秒超时
      );

      console.timeEnd('⏱️ 转换服务调用时间');
      console.log('📋 [WebPreviewButton] 转换结果详情:', {
        success: previewResult.success,
        hasHtml: !!previewResult.html,
        htmlLength: previewResult.html?.length || 0,
        hasScreenshot: !!previewResult.screenshot,
        screenshotUrl: previewResult.screenshot?.url,
        error: previewResult.error,
        message: previewResult.message,
      });

      if (previewResult.success) {
        // 截图阶段
        console.log('📸 [WebPreviewButton] 进入截图阶段');
        setStatus('capturing');
        await new Promise(resolve => setTimeout(resolve, 200));

        // 成功
        console.log('✅ [WebPreviewButton] 预览生成成功');
        console.log('🎉 最终预览数据:', {
          htmlPreview: `${previewResult.html?.substring(0, 300)}...`,
          screenshotInfo: previewResult.screenshot
            ? {
                url: previewResult.screenshot.url,
                size: `${previewResult.screenshot.width}x${previewResult.screenshot.height}`,
                fileSize: previewResult.screenshot.size,
              }
            : null,
        });

        setStatus('success');
        setPreview(previewResult);
        onPreviewGenerated?.(result.id, previewResult);

        console.log('🔔 [WebPreviewButton] 回调函数已调用');
      } else {
        // 失败
        const errorMsg = previewResult.message || '转换失败';
        console.error('❌ [WebPreviewButton] 转换失败:', {
          error: previewResult.error,
          message: previewResult.message,
          finalErrorMsg: errorMsg,
        });

        setStatus('error');
        setError(errorMsg);
      }
    } catch (err) {
      const errorMsg =
        err instanceof Error ? err.message : '生成预览时发生未知错误';
      console.error('💥 [WebPreviewButton] 转换异常:', {
        error: err,
        errorType: typeof err,
        errorMessage: errorMsg,
        errorName: err instanceof Error ? err.name : undefined,
        stack: err instanceof Error ? err.stack : undefined,
      });

      setStatus('error');
      setError(errorMsg);
    } finally {
      console.timeEnd('⏱️ 总预览生成时间');
      console.log('🏁 [WebPreviewButton] 预览生成流程结束');
      console.log('📊 最终状态:', {
        finalStatus: status,
        hasError: !!error,
        hasPreview: !!preview,
        isGenerating: false,
      });

      isGeneratingRef.current = false;
      console.groupEnd();
    }
  }, [result, onPreviewGenerated, disabled]);

  /**
   * 重试预览生成
   */
  const retryPreview = useCallback(() => {
    setStatus('idle');
    setError(null);
    setPreview(null);
  }, []);

  // 渲染状态指示器
  const renderStatusIndicator = () => {
    switch (status) {
      case 'detecting':
        return (
          <div className="flex items-center space-x-2 text-blue-600">
            <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
            <span className="text-xs">检测内容...</span>
          </div>
        );

      case 'converting':
        return (
          <div className="flex items-center space-x-2 text-purple-600">
            <div className="w-3 h-3 border-2 border-purple-600 border-t-transparent rounded-full animate-spin" />
            <span className="text-xs">转换中...</span>
          </div>
        );

      case 'capturing':
        return (
          <div className="flex items-center space-x-2 text-green-600">
            <div className="w-3 h-3 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
            <span className="text-xs">生成截图...</span>
          </div>
        );

      default:
        return null;
    }
  };

  // 错误状态
  if (status === 'error' && error) {
    return (
      <div className="web-preview-error bg-red-50 border border-red-200 rounded-lg p-3">
        <div className="flex items-start space-x-2">
          <svg
            className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clipRule="evenodd"
            />
          </svg>
          <div className="flex-1 min-w-0">
            <p className="text-xs text-red-700 font-medium">预览失败</p>
            <p className="text-xs text-red-600 mt-1">{error}</p>
          </div>
        </div>
        <button
          onClick={retryPreview}
          className="mt-2 px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 text-xs rounded transition-colors duration-200"
        >
          重试
        </button>
      </div>
    );
  }

  // 成功状态 - 显示缩略图预览
  if (status === 'success' && preview) {
    return (
      <div className="web-preview-success">
        <ThumbnailPreview
          preview={preview}
          resultId={result.id}
          width={120}
          height={160}
        />
      </div>
    );
  }

  // 生成中状态
  if (status !== 'idle') {
    return (
      <div className="web-preview-loading bg-blue-50 border border-blue-200 rounded-lg p-3">
        {renderStatusIndicator()}
        <div className="mt-2">
          <div className="w-full bg-blue-100 rounded-full h-1.5">
            <div
              className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
              style={{
                width:
                  status === 'detecting'
                    ? '25%'
                    : status === 'converting'
                      ? '65%'
                      : status === 'capturing'
                        ? '90%'
                        : '100%',
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  // 初始状态 - 显示生成按钮
  return (
    <button
      onClick={generatePreview}
      disabled={disabled}
      className={`
        web-preview-btn group relative
        inline-flex items-center space-x-2 px-4 py-2 
        border border-transparent rounded-lg
        font-medium text-sm transition-all duration-200
        ${
          disabled
            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
            : 'bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white shadow-md hover:shadow-lg transform hover:scale-105'
        }
      `}
    >
      <svg
        className="w-4 h-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
        />
      </svg>
      <span>Web预览</span>

      {/* 悬停提示 */}
      {!disabled && (
        <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
          生成Web预览
        </div>
      )}
    </button>
  );
};
