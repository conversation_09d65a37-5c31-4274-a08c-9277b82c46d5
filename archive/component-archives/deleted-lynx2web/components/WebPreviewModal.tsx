/**
 * Web预览弹窗组件
 * 显示完整尺寸的Web预览
 */

import React, { useEffect, useRef, useState } from 'react';
import type { WebPreviewModalProps } from '../types';

export const WebPreviewModal: React.FC<WebPreviewModalProps> = ({
  preview,
  resultId,
  isOpen,
  onClose,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 处理ESC键关闭
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // 防止背景滚动
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).mozFullScreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);
    };

    if (isOpen) {
      document.addEventListener('fullscreenchange', handleFullscreenChange);
      document.addEventListener(
        'webkitfullscreenchange',
        handleFullscreenChange,
      );
      document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    }

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener(
        'webkitfullscreenchange',
        handleFullscreenChange,
      );
      document.removeEventListener(
        'mozfullscreenchange',
        handleFullscreenChange,
      );
    };
  }, [isOpen]);

  // 处理iframe加载
  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  // 处理背景点击关闭
  const handleBackdropClick = (event: React.MouseEvent) => {
    if (event.target === modalRef.current) {
      onClose();
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div
      ref={modalRef}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 backdrop-blur-sm"
      onClick={handleBackdropClick}
    >
      <div
        className="relative bg-white rounded-lg shadow-2xl w-full h-full max-w-4xl max-h-[90vh] flex flex-col"
        onClick={e => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50 rounded-t-lg">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-green-500 rounded-full" />
            <h3 className="text-lg font-semibold text-gray-900">
              Web预览 - {resultId}
            </h3>
          </div>

          <div className="flex items-center space-x-2">
            {/* 全屏按钮 */}
            <button
              onClick={async () => {
                if (!iframeRef.current) {
                  return;
                }

                try {
                  if (!isFullscreen) {
                    // 进入全屏
                    if (iframeRef.current.requestFullscreen) {
                      await iframeRef.current.requestFullscreen();
                    } else if (
                      (iframeRef.current as any).webkitRequestFullscreen
                    ) {
                      await (
                        iframeRef.current as any
                      ).webkitRequestFullscreen();
                    } else if (
                      (iframeRef.current as any).mozRequestFullScreen
                    ) {
                      await (iframeRef.current as any).mozRequestFullScreen();
                    }
                  } else {
                    // 退出全屏
                    if (document.exitFullscreen) {
                      await document.exitFullscreen();
                    } else if ((document as any).webkitExitFullscreen) {
                      await (document as any).webkitExitFullscreen();
                    } else if ((document as any).mozCancelFullScreen) {
                      await (document as any).mozCancelFullScreen();
                    }
                  }
                } catch (error) {
                  console.warn('全屏操作失败:', error);
                }
              }}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded-lg transition-colors duration-200"
              title={isFullscreen ? '退出全屏' : '全屏显示'}
            >
              {isFullscreen ? (
                // 退出全屏图标
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 4H4v4m12-4h4v4M4 16v4h4m12 0h-4v-4m-8-8l8 8m-8 0l8-8"
                  />
                </svg>
              ) : (
                // 进入全屏图标
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
                  />
                </svg>
              )}
            </button>

            {/* 关闭按钮 */}
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded-lg transition-colors duration-200"
              title="关闭预览"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 relative overflow-hidden">
          {/* 加载状态 */}
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
              <div className="flex flex-col items-center space-y-3">
                <div className="w-8 h-8 border-3 border-blue-500 border-t-transparent rounded-full animate-spin" />
                <span className="text-gray-600 text-sm">加载预览中...</span>
              </div>
            </div>
          )}

          {/* 预览iframe */}
          {preview.html && (
            <iframe
              ref={iframeRef}
              srcDoc={preview.html}
              className="w-full h-full border-none"
              title={`完整预览 - ${resultId}`}
              sandbox="allow-scripts allow-same-origin allow-forms allow-pointer-lock allow-popups allow-modals"
              onLoad={handleIframeLoad}
              style={{
                minHeight: '500px',
                pointerEvents: 'auto',
                touchAction: 'pan-y pinch-zoom',
              }}
              onMouseEnter={() => {
                // 确保iframe获得焦点以支持滚动
                if (iframeRef.current) {
                  setTimeout(() => {
                    try {
                      iframeRef.current?.focus();
                    } catch (e) {
                      console.warn('WebPreviewModal iframe focus failed:', e);
                    }
                  }, 0);
                }
              }}
            />
          )}

          {/* 错误状态 */}
          {!preview.html && (
            <div className="flex items-center justify-center h-full bg-gray-100">
              <div className="text-center">
                <svg
                  className="w-16 h-16 text-gray-400 mx-auto mb-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                    clipRule="evenodd"
                  />
                </svg>
                <p className="text-gray-600">无法加载预览内容</p>
              </div>
            </div>
          )}
        </div>

        {/* 底部信息栏 */}
        <div className="p-3 bg-gray-50 border-t border-gray-200 rounded-b-lg">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center space-x-4">
              <span>预览模式: Web HTML</span>
              {preview.screenshot && (
                <span>
                  截图: {preview.screenshot.width}×{preview.screenshot.height}
                </span>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <span>按ESC键关闭</span>
              <span>•</span>
              <span>点击背景关闭</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
