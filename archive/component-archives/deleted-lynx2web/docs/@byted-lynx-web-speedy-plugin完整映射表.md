# @byted-lynx/web-speedy-plugin 完整映射表文档

## 概述

本文档提供了从 `@byted-lynx/web-speedy-plugin` 提取的完整映射表，包含 TTML 元素到 HTML 的映射、事件指令映射、CSS 转换配置等。

## 1. TTML 元素映射表

### 1.1 布局容器元素

| TTML 元素 | HTML 标签 | CSS 类名 | 特殊属性 | 说明 |
|-----------|-----------|----------|----------|------|
| `view` | `div` | `lynx-view` | - | 基础视图容器 |
| `scroll-view` | `div` | `lynx-scroll-view` | `overflow: auto` | 可滚动视图容器 |

### 1.2 文本元素

| TTML 元素 | HTML 标签 | CSS 类名 | 特殊属性 | 说明 |
|-----------|-----------|----------|----------|------|
| `text` | `span` | `lynx-text` | - | 普通文本 |
| `rich-text` | `div` | `lynx-rich-text` | - | 富文本容器 |

### 1.3 媒体元素

| TTML 元素 | HTML 标签 | CSS 类名 | 自闭合 | 属性映射 | 说明 |
|-----------|-----------|----------|--------|----------|------|
| `image` | `img` | `lynx-image` | ✓ | `src→src`, `mode→objectFit`, `lazy-load→loading` | 图片元素 |
| `video` | `video` | `lynx-video` | - | `controls: true` | 视频播放器 |
| `audio` | `audio` | `lynx-audio` | - | `controls: true` | 音频播放器 |
| `cover-image` | `img` | `lynx-cover-image` | ✓ | - | 封面图片 |
| `cover-view` | `div` | `lynx-cover-view` | - | - | 封面视图 |

### 1.4 表单元素

| TTML 元素 | HTML 标签 | CSS 类名 | 自闭合 | 属性映射 | 说明 |
|-----------|-----------|----------|--------|----------|------|
| `input` | `input` | `lynx-input` | ✓ | `type→type`, `placeholder→placeholder`, `value→value`, `maxlength→maxLength`, `disabled→disabled`, `password→type` | 输入框 |
| `textarea` | `textarea` | `lynx-textarea` | - | - | 多行文本输入 |
| `button` | `button` | `lynx-button` | - | - | 按钮 |
| `switch` | `input` | `lynx-switch` | ✓ | `type: checkbox` | 开关 |
| `slider` | `input` | `lynx-slider` | ✓ | `type: range` | 滑块 |
| `picker` | `select` | `lynx-picker` | - | - | 选择器 |
| `checkbox` | `input` | `lynx-checkbox` | ✓ | `type: checkbox` | 复选框 |
| `checkbox-group` | `div` | `lynx-checkbox-group` | - | - | 复选框组 |
| `radio` | `input` | `lynx-radio` | ✓ | `type: radio` | 单选框 |
| `radio-group` | `div` | `lynx-radio-group` | - | - | 单选框组 |

### 1.5 导航元素

| TTML 元素 | HTML 标签 | CSS 类名 | 属性映射 | 说明 |
|-----------|-----------|----------|----------|------|
| `navigator` | `a` | `lynx-navigator` | `url→href`, `open-type→target`, `hover-class→data-hover-class` | 导航器 |
| `link` | `a` | `lynx-link` | - | 链接 |

### 1.6 高级组件

| TTML 元素 | HTML 标签 | CSS 类名 | 属性映射 | 说明 |
|-----------|-----------|----------|----------|------|
| `swiper` | `div` | `lynx-swiper` | `indicator-dots→data-indicator-dots`, `autoplay→data-autoplay`, `interval→data-interval`, `duration→data-duration`, `circular→data-circular` | 轮播容器 |
| `swiper-item` | `div` | `lynx-swiper-item` | - | 轮播项 |
| `progress` | `progress` | `lynx-progress` | `percent→value`, `show-info→data-show-info`, `border-radius→data-border-radius` | 进度条 |
| `web-view` | `iframe` | `lynx-web-view` | `src→src` | 内嵌网页 |

### 1.7 列表组件

| TTML 元素 | HTML 标签 | CSS 类名 | 说明 |
|-----------|-----------|----------|------|
| `list` | `div` | `lynx-list` | 列表容器 |
| `list-item` | `div` | `lynx-list-item` | 列表项 |
| `cell` | `div` | `lynx-cell` | 单元格 |
| `header` | `header` | `lynx-header` | 头部 |
| `footer` | `footer` | `lynx-footer` | 尾部 |

### 1.8 画布和媒体

| TTML 元素 | HTML 标签 | CSS 类名 | 属性映射 | 说明 |
|-----------|-----------|----------|----------|------|
| `canvas` | `canvas` | `lynx-canvas` | `canvas-id→id`, `disable-scroll→data-disable-scroll` | 画布 |

## 2. 事件指令映射表

### 2.1 冒泡事件 (bind*)

| TTML 事件 | React 事件 | 阻止冒泡 | 说明 |
|-----------|------------|----------|------|
| `bindtap` | `onClick` | ❌ | 点击事件 |
| `bindtouchstart` | `onTouchStart` | ❌ | 触摸开始 |
| `bindtouchmove` | `onTouchMove` | ❌ | 触摸移动 |
| `bindtouchend` | `onTouchEnd` | ❌ | 触摸结束 |
| `bindtouchcancel` | `onTouchCancel` | ❌ | 触摸取消 |
| `bindlongpress` | `onContextMenu` | ❌ | 长按事件 |
| `bindinput` | `onInput` | ❌ | 输入事件 |
| `bindfocus` | `onFocus` | ❌ | 获得焦点 |
| `bindblur` | `onBlur` | ❌ | 失去焦点 |
| `bindchange` | `onChange` | ❌ | 值改变 |
| `bindscroll` | `onScroll` | ❌ | 滚动事件 |
| `bindscrolltoupper` | `onScroll` | ❌ | 滚动到顶部 |
| `bindscrolltolower` | `onScroll` | ❌ | 滚动到底部 |
| `bindload` | `onLoad` | ❌ | 加载完成 |
| `binderror` | `onError` | ❌ | 错误事件 |

### 2.2 捕获事件 (catch:*)

| TTML 事件 | React 事件 | 阻止冒泡 | 说明 |
|-----------|------------|----------|------|
| `catch:tap` | `onClick` | ✓ | 点击事件（捕获） |
| `catch:input` | `onInput` | ✓ | 输入事件（捕获） |
| `catch:change` | `onChange` | ✓ | 值改变（捕获） |
| `catch:scroll` | `onScroll` | ✓ | 滚动事件（捕获） |
| `catch:touchstart` | `onTouchStart` | ✓ | 触摸开始（捕获） |
| `catch:touchmove` | `onTouchMove` | ✓ | 触摸移动（捕获） |
| `catch:touchend` | `onTouchEnd` | ✓ | 触摸结束（捕获） |
| `catch:longpress` | `onContextMenu` | ✓ | 长按事件（捕获） |

## 3. TTML 指令映射表

### 3.1 条件渲染指令

| TTML 指令 | 类型 | 说明 | 转换结果 |
|-----------|------|------|----------|
| `lx:if` | conditional | 条件渲染 | React 条件表达式 |
| `lx:elif` | conditional | 否则如果 | React 条件表达式链 |
| `lx:else` | conditional-fallback | 否则 | React 条件表达式默认分支 |
| `tt:if` | conditional | TikTok 条件渲染 | 同 `lx:if` |

### 3.2 列表渲染指令

| TTML 指令 | 类型 | 说明 | 转换结果 |
|-----------|------|------|----------|
| `lx:for` | iteration | 列表渲染 | React `.map()` 方法 |
| `lx:key` | optimization | React key | React key 属性 |
| `tt:for` | iteration | TikTok 列表渲染 | 同 `lx:for` |
| `tt:key` | optimization | TikTok key | 同 `lx:key` |

## 4. 通用属性映射表

| TTML 属性 | HTML/React 属性 | 说明 |
|-----------|------------------|------|
| `class` | `className` | CSS 类名 |
| `style` | `style` | 内联样式 |
| `id` | `id` | 元素 ID |
| `hidden` | `hidden` | 隐藏属性 |
| `data-*` | `data-*` | 保留数据属性 |
| `aria-*` | `aria-*` | 保留无障碍属性 |

## 5. TTSS/CSS 转换配置

### 5.1 RPX 单位转换

| 转换模式 | 计算公式 | 示例 |
|----------|----------|------|
| VW | `(rpxValue / 750 * 100)vw` | `100rpx → 13.333333vw` |
| REM | `(rpxValue / 37.5)rem` | `100rpx → 2.666667rem` |
| PX | `rpxValue * (750 / designWidth)px` | `100rpx → 100px` |
| CALC | `calc(rpxValue * 100vw / 750)` | `100rpx → calc(100 * 100vw / 750)` |

### 5.2 设计稿配置

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `designWidth` | 750 | 设计稿宽度 |
| `defaultMode` | VW | 默认转换模式 |

## 6. 自闭合标签列表

以下 TTML 元素会转换为自闭合的 HTML 标签：

- `image`
- `input`
- `switch`
- `slider`
- `progress`
- `web-view`
- `cover-image`
- `checkbox`
- `radio`
- `canvas`

## 7. 接口定义

### 7.1 ElementMapping 接口

```typescript
interface ElementMapping {
  tag: string;                           // 目标 HTML 标签
  props?: Record<string, any>;           // 默认属性
  selfClosing?: boolean;                 // 是否自闭合标签
  attributeMapping?: Record<string, string>; // 属性映射
}
```

### 7.2 DirectiveMapping 接口

```typescript
interface DirectiveMapping {
  type: 'conditional' | 'iteration' | 'optimization' | 'event';
  transform: (value: string, node: any, context: any) => any;
}
```

### 7.3 EventMapping 接口

```typescript
interface EventMapping {
  reactEvent: string;      // React 事件名称
  stopPropagation: boolean; // 是否阻止事件冒泡
}
```

## 8. 版本信息

- **插件版本**: `@byted-lynx/web-speedy-plugin: ^5.1.1`
- **文档生成时间**: 2025-06-25
- **适用范围**: ByteDance Lynx 框架到 Web 的转换

## 9. 相关文档

- [Lynx与Web语法映射对比升级方案](./docs/Lynx与Web语法映射对比升级方案.md)
- [pePromptLoader升级指南-Claude4_Lynx语法优化方案](./docs/pePromptLoader升级指南-Claude4_Lynx语法优化方案.md)

---

**注意**: 本映射表基于 `@byted-lynx/web-speedy-plugin` 的实际实现提取，确保了 TTML 到 HTML/React 的准确转换。在使用时请参考具体的实现文件以获取最新的映射规则。