# Lynx 解析器原理文档

## 概述

本文档详细描述了 Lynx2Web 转换器的解析原理，包括模板语法解析、循环处理、生命周期执行等核心功能的实现机制。

## 1. 整体架构

### 1.1 解析流程

```
Lynx 源码 → 文件提取 → 数据解析 → 模板处理 → 标签转换 → HTML 生成
    ↓           ↓         ↓         ↓         ↓         ↓
  FILES     TTML/TTSS   Card数据   模板绑定   Web标签   完整HTML
  标签      /JS分离      解析      循环处理    转换      +JS环境
```

### 1.2 核心组件

1. **文件解析器**: 提取 `<FILE>` 标签中的 TTML、TTSS、JS 内容
2. **数据解析器**: 从 Card({}) 中提取 data、生命周期方法、自定义方法
3. **模板处理器**: 处理 `{{}}` 语法和 `tt:for` 循环
4. **标签转换器**: 将 Lynx 标签转换为 HTML 标签
5. **HTML 生成器**: 生成包含生命周期执行的完整 HTML

## 2. 文件解析原理

### 2.1 FILES 标签解析

```javascript
// 解析 <FILES><FILE path="...">content</FILE></FILES> 结构
parseContent(content) {
  const files = {};
  
  // 正则匹配 FILE 标签
  const filePattern = /<FILE\s+path="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/g;
  let match;
  
  while ((match = filePattern.exec(content)) !== null) {
    files[match[1]] = match[2].trim();
  }
  
  return files;
}
```

**解析规则:**
- 支持路径格式: `index.ttml`, `index.ttss`, `index.js`, `index.json`
- 自动去除内容前后空白
- 容错处理：如果没有 FILE 标签，尝试直接解析 template/style/script 标签

## 3. JavaScript 数据解析

### 3.1 Card 结构解析

Lynx 使用 `Card({})` 作为组件定义格式：

```javascript
Card({
  data: {
    // 页面数据
  },
  onLoad() {
    // 生命周期方法
  },
  customMethod() {
    // 自定义方法
  }
});
```

### 3.2 数据对象提取

```javascript
extractDataObject(cardContent) {
  // 匹配 data: { ... } 结构
  const dataMatch = cardContent.match(/data\s*:\s*\{([\s\S]*?)\}(?=\s*[,}])/);
  if (!dataMatch) return {};
  
  const dataStr = '{' + dataMatch[1] + '}';
  return this.parseDataObject(dataStr);
}
```

**解析步骤:**
1. 使用正则匹配 `data:` 字段
2. 提取花括号内的内容
3. 转换为可解析的 JSON 格式
4. 安全解析对象

### 3.3 生命周期方法提取

```javascript
extractLifeCycleMethods(cardContent) {
  const lifeCycleMethods = {};
  const lifeCycleNames = [
    'onLoad', 'onReady', 'onShow', 'onHide', 'onDestroy',
    'onDataChanged', 'onError', 'onUnload'
  ];
  
  lifeCycleNames.forEach(methodName => {
    // 支持两种语法: methodName() {} 和 methodName: function() {}
    const patterns = [
      new RegExp(`${methodName}\\s*\\(\\)\\s*\\{([\\s\\S]*?)\\}(?=\\s*[,}])`, 'i'),
      new RegExp(`${methodName}\\s*:\\s*function\\s*\\(\\)\\s*\\{([\\s\\S]*?)\\}(?=\\s*[,}])`, 'i')
    ];
    
    for (const pattern of patterns) {
      const match = cardContent.match(pattern);
      if (match) {
        lifeCycleMethods[methodName] = match[1].trim();
        break;
      }
    }
  });
  
  return lifeCycleMethods;
}
```

## 4. 模板语法解析

### 4.1 模板绑定处理

Lynx 使用 `{{}}` 语法进行数据绑定：

```javascript
processTemplateBindings(html, data) {
  // 先修复常见的语法错误
  let fixedHtml = this.fixMalformedTemplates(html);
  
  // 处理所有 {{expression}} 
  return fixedHtml.replace(/\{\{\s*([^}]+)\s*\}\}/g, (match, expression) => {
    try {
      const result = this.evaluateExpression(expression.trim(), data);
      return result !== undefined ? String(result) : match;
    } catch (error) {
      return match; // 保持原样
    }
  });
}
```

### 4.2 表达式求值器

支持多种表达式类型：

```javascript
evaluateExpression(expr, data) {
  expr = expr.trim();
  
  // 1. 比较运算符: status === 'active'
  if (/[<>=!]=?/.test(expr) && !expr.includes('?')) {
    return this.evaluateComparisonOperation(expr, data);
  }
  
  // 2. 数组访问: items[0], items[index]
  if (expr.includes('[') && expr.includes(']')) {
    return this.evaluateArrayAccess(expr, data);
  }
  
  // 3. 属性链: user.profile.name
  if (expr.includes('.')) {
    return this.evaluatePropertyChain(expr, data);
  }
  
  // 4. 函数调用: items.length, user.getName()
  if (expr.includes('(') && expr.includes(')')) {
    return this.evaluateFunctionCall(expr, data);
  }
  
  // 5. 数学运算: price * 0.8
  if (/[+\-*/]/.test(expr)) {
    return this.evaluateSimpleOperation(expr, data);
  }
  
  // 6. 三元运算: condition ? value1 : value2
  if (expr.includes('?') && expr.includes(':')) {
    return this.evaluateTernaryOperator(expr, data);
  }
  
  // 7. 逻辑运算: isActive && isVisible
  if (/&&|\|\|/.test(expr)) {
    return this.evaluateLogicalOperation(expr, data);
  }
  
  // 8. 直接属性访问
  if (data.hasOwnProperty(expr)) {
    return data[expr];
  }
  
  // 9. 字面量处理
  return this.parseLiteral(expr);
}
```

### 4.3 语法错误自动修复

```javascript
fixMalformedTemplates(html) {
  let fixed = html;
  
  // 修复缺少前花括号: {item.name}} → {{item.name}}
  fixed = fixed.replace(/([^{])\{([^{}]+)\}\}/g, '$1{{$2}}');
  
  // 修复缺少后花括号: {{item.name} → {{item.name}}
  fixed = fixed.replace(/\{\{([^{}]+)\}([^}])/g, '{{$1}}$2');
  
  // 修复三个花括号: {{{item.name}}} → {{item.name}}
  fixed = fixed.replace(/\{\{\{([^{}]+)\}\}\}/g, '{{$1}}');
  
  // 修复单花括号: {item.name} → {{item.name}}
  fixed = fixed.replace(/\{([a-zA-Z_$][a-zA-Z0-9_$.[\]'"]*?)\}/g, (match, expr) => {
    if (/^[a-zA-Z_$][a-zA-Z0-9_$.[\]'"]*$/.test(expr.trim())) {
      return `{{${expr}}}`;
    }
    return match;
  });
  
  return fixed;
}
```

## 5. 循环处理原理

### 5.1 多种循环语法支持

```javascript
processLoopRendering(html, data) {
  const patterns = [
    // Lynx 标准语法: tt:for="item in items"
    /<([^>]+)\s+tt:for="([^"]*)"([^>]*)>([\s\S]*?)<\/\1>/g,
    
    // 微信小程序兼容: wx:for="{{items}}" wx:for-item="item"
    /<([^>]+)\s+wx:for="([^"]*)"(?:\s+wx:for-item="([^"]*)")?([^>]*)>([\s\S]*?)<\/\1>/g,
    
    // 支付宝小程序兼容: a:for="{{items}}" a:for-item="item"
    /<([^>]+)\s+a:for="([^"]*)"(?:\s+a:for-item="([^"]*)")?([^>]*)>([\s\S]*?)<\/\1>/g
  ];
  
  // 依次处理每种语法
  patterns.forEach((pattern, index) => {
    html = html.replace(pattern, (match, tagName, forExpr, itemName, attrs, innerHtml) => {
      return this.processLoopContent(tagName, forExpr, itemName, attrs, innerHtml, data, index);
    });
  });
  
  return html;
}
```

### 5.2 循环表达式解析

```javascript
parseLoopExpression(forExpr, itemName, patternIndex) {
  let items = [];
  let actualItemName = 'item';
  let indexName = 'index';
  
  if (patternIndex === 0) {
    // tt:for="item in items" 格式
    if (forExpr.includes(' in ')) {
      const [item, source] = forExpr.split(' in ').map(s => s.trim());
      actualItemName = item;
      
      // 支持解构语法: (item, index) in items
      if (item.includes(',')) {
        const parts = item.replace(/[()]/g, '').split(',').map(s => s.trim());
        actualItemName = parts[0];
        if (parts[1]) indexName = parts[1];
      }
      
      items = this.evaluateExpression(source.replace(/[{}]/g, '').trim(), data);
    }
  } else {
    // 微信/支付宝格式
    if (itemName) actualItemName = itemName;
    items = this.evaluateExpression(forExpr.replace(/[{}]/g, '').trim(), data);
  }
  
  return { items, actualItemName, indexName };
}
```

### 5.3 循环上下文生成

```javascript
createLoopContext(data, item, index, itemName, items) {
  return {
    ...data,
    [itemName]: item,
    index: index,
    i: index,
    // 循环辅助变量
    first: index === 0,
    last: index === items.length - 1,
    even: index % 2 === 0,
    odd: index % 2 === 1,
    count: items.length
  };
}
```

## 6. 标签转换原理

### 6.1 Lynx 到 HTML 标签映射

```javascript
convertTags(html) {
  const tagMap = {
    'view': 'div',           // 容器组件
    'text': 'span',          // 文本组件
    'image': 'img',          // 图片组件
    'scroll-view': 'div',    // 滚动容器
    'list': 'div',           // 列表容器
    'list-item': 'div'       // 列表项
  };
  
  Object.entries(tagMap).forEach(([lynxTag, htmlTag]) => {
    // 转换开始标签
    const openRegex = new RegExp(`<${lynxTag}(\\s[^>]*?)?>`, 'g');
    html = html.replace(openRegex, `<${htmlTag}$1>`);
    
    // 转换结束标签
    const closeRegex = new RegExp(`</${lynxTag}>`, 'g');
    html = html.replace(closeRegex, `</${htmlTag}>`);
  });
  
  return html;
}
```

### 6.2 事件绑定转换

```javascript
processEvents(html) {
  // Lynx 事件 → Web 事件
  html = html.replace(/bindtap="([^"]*)"/g, 'onclick="handleTap(\'$1\')"');
  html = html.replace(/bindlongpress="([^"]*)"/g, 'oncontextmenu="handleLongPress(\'$1\'); return false;"');
  html = html.replace(/catchtap="([^"]*)"/g, 'onclick="handleTap(\'$1\'); event.stopPropagation();"');
  
  return html;
}
```

## 7. 生命周期执行机制

### 7.1 小程序环境模拟

```javascript
generateLynxEnvironment(data) {
  return `
    const lynxEnv = {
      data: pageData,
      
      setData: function(newData, callback) {
        Object.assign(this.data, newData);
        this.updateDOM();
        if (callback) callback();
      },
      
      updateDOM: function() {
        // DOM 更新逻辑
      },
      
      getSystemInfo: function() {
        return {
          platform: 'web',
          windowWidth: window.innerWidth,
          windowHeight: window.innerHeight,
          pixelRatio: window.devicePixelRatio || 1
        };
      }
    };
  `;
}
```

### 7.2 生命周期方法生成

```javascript
generateLifeCycleCode(lifeCycleMethods) {
  let code = '';
  
  Object.entries(lifeCycleMethods).forEach(([methodName, methodBody]) => {
    code += `
      window.${methodName} = function() {
        try {
          ${methodBody}
        } catch (error) {
          console.error('${methodName} 执行错误:', error);
        }
      };
    `;
  });
  
  return code;
}
```

### 7.3 生命周期执行时机

```javascript
// onLoad: 页面加载时立即执行
if (typeof window.onLoad === 'function') {
  window.onLoad.call(lynxEnv);
}

// onReady: DOM 内容加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
  if (typeof window.onReady === 'function') {
    window.onReady.call(lynxEnv);
  }
  
  // onShow: 页面显示时执行
  if (typeof window.onShow === 'function') {
    window.onShow.call(lynxEnv);
  }
});

// onHide: 页面隐藏时执行
document.addEventListener('visibilitychange', function() {
  if (document.hidden && typeof window.onHide === 'function') {
    window.onHide.call(lynxEnv);
  }
});
```

## 8. CSS 转换原理

### 8.1 单位转换

```javascript
convertCSS(ttss) {
  let css = ttss;
  
  // rpx 转 px (1rpx = 0.5px，基于375px宽度)
  css = css.replace(/(\d+(?:\.\d+)?)rpx/g, (match, value) => {
    const pxValue = parseFloat(value) * 0.5;
    return `${pxValue}px`;
  });
  
  return css;
}
```

### 8.2 Lynx 特有样式处理

```javascript
// 移除 Lynx 特有的样式属性
css = css.replace(/enable-scroll\s*:\s*[^;]+;?/g, '');
css = css.replace(/scroll-[xy]\s*:\s*[^;]+;?/g, '');
```

## 9. 错误处理机制

### 9.1 解析错误处理

```javascript
try {
  const result = this.evaluateExpression(expression, data);
  return result !== undefined ? String(result) : match;
} catch (error) {
  console.warn('表达式解析失败:', expression, error);
  return match; // 保持原样，不中断解析
}
```

### 9.2 降级处理策略

1. **模板语法错误**: 自动修复常见错误，无法修复时保持原样
2. **数据访问错误**: 返回 undefined，在模板中显示原始表达式
3. **循环数据错误**: 跳过当前循环，继续处理其他内容
4. **生命周期错误**: 捕获并记录错误，不影响页面渲染

## 10. 性能优化

### 10.1 表达式缓存

```javascript
// 复杂表达式结果缓存（可选实现）
const expressionCache = new Map();

evaluateExpression(expr, data) {
  const cacheKey = `${expr}_${JSON.stringify(data)}`;
  if (expressionCache.has(cacheKey)) {
    return expressionCache.get(cacheKey);
  }
  
  const result = this.doEvaluateExpression(expr, data);
  expressionCache.set(cacheKey, result);
  return result;
}
```

### 10.2 批量处理

- 一次性处理所有模板绑定
- 批量转换标签避免多次 DOM 操作
- 统一处理循环避免嵌套解析

## 11. 调试支持

### 11.1 详细日志

```javascript
console.log('🔍 处理表达式:', originalExpr);
console.log('✅ 表达式结果:', originalExpr, '->', result);
console.log('🔄 循环数据:', items, '项目名称:', itemName);
```

### 11.2 错误定位

- 每个解析步骤都有详细的日志输出
- 错误信息包含具体的表达式和上下文
- 支持在浏览器控制台查看解析过程

这个解析器专门针对 Lynx 框架设计，完整支持 Lynx 的语法特性，为 Web 预览提供了可靠的转换能力。