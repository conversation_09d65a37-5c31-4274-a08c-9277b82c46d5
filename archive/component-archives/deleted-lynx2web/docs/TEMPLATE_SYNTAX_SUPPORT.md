# 模板语法支持文档

本文档描述了增强后的 `{{}}` 模板解析器支持的各种表达式类型。

## 基础语法

### 1. 简单属性访问
```lynx
// 数据: { name: "张三", age: 25 }
<text>{{name}}</text>          → 张三
<text>{{age}}</text>           → 25
```

### 2. 对象属性链访问
```lynx
// 数据: { user: { profile: { name: "张三", avatar: "url" } } }
<text>{{user.profile.name}}</text>     → 张三
<text>{{user.profile.avatar}}</text>   → url
```

### 3. 数组访问
```lynx
// 数据: { items: ["苹果", "香蕉", "橙子"] }
<text>{{items[0]}}</text>      → 苹果
<text>{{items[2]}}</text>      → 橙子
```

### 4. 数组属性访问
```lynx
// 数据: { items: [{ name: "商品1" }, { name: "商品2" }] }
<text>{{items[0].name}}</text> → 商品1
```

## 高级语法

### 5. 简单数学运算
```lynx
// 数据: { price: 100, count: 3 }
<text>价格: {{price * count}}</text>     → 价格: 300
<text>折扣: {{price * 0.8}}</text>       → 折扣: 80
<text>总数: {{count + 1}}</text>         → 总数: 4
```

### 6. 三元条件运算符
```lynx
// 数据: { status: "active", count: 5 }
<text>{{status === 'active' ? '活跃' : '非活跃'}}</text>    → 活跃
<text>{{count > 0 ? '有库存' : '无库存'}}</text>            → 有库存
```

### 7. 函数调用支持
```lynx
// 数据: { items: [1,2,3], user: { getName: () => "张三" } }
<text>数量: {{items.length}}</text>      → 数量: 3
<text>用户: {{user.getName()}}</text>    → 用户: 张三
```

## 循环语法 (tt:for)

### 8. 基础循环
```lynx
// 数据: { items: ["苹果", "香蕉", "橙子"] }
<view tt:for="item in items">
  <text>{{item}}</text>
</view>
```

### 9. 对象数组循环
```lynx
// 数据: { products: [{ name: "商品1", price: 100 }, { name: "商品2", price: 200 }] }
<view tt:for="product in products">
  <text>{{product.name}}: ¥{{product.price}}</text>
</view>
```

### 10. 循环索引访问
```lynx
<view tt:for="item in items">
  <text>第{{index}}项: {{item}}</text>
</view>
```

### 11. 循环中的复杂表达式
```lynx
// 数据: { products: [{ price: 100 }, { price: 200 }] }
<view tt:for="product in products">
  <text>原价: ¥{{product.price}}</text>
  <text>折扣价: ¥{{product.price * 0.8}}</text>
  <text>序号: {{index + 1}}</text>
</view>
```

## 字面量支持

### 12. 字符串字面量
```lynx
<text>{{"Hello World"}}</text>    → Hello World
<text>{{'单引号字符串'}}</text>    → 单引号字符串
```

### 13. 数字字面量
```lynx
<text>{{42}}</text>              → 42
<text>{{3.14}}</text>            → 3.14
```

### 14. 布尔值字面量
```lynx
<text>{{true}}</text>            → true
<text>{{false}}</text>           → false
```

## 特殊关键字

### 15. data 关键字
```lynx
// 访问整个数据对象
<text>{{data}}</text>            → [object Object] 或 JSON字符串
```

## 错误处理

### 16. 无效表达式处理
- 如果表达式无法解析，保持原始 `{{expression}}` 格式
- 在浏览器控制台输出详细的调试信息
- 支持降级渲染，不会导致整个组件崩溃

## 调试支持

### 17. 控制台日志
所有模板解析过程都会在控制台输出详细日志：
- `🔗 [LynxConverter] 处理数据绑定`
- `🔍 处理表达式: expression`
- `✅ 表达式结果: expression -> result`
- `⚠️ 表达式解析失败: expression`

## 性能优化

### 18. 表达式缓存
- 复杂表达式求值结果会被适当缓存
- 循环中的重复计算得到优化
- 错误表达式不会重复解析

## 限制和注意事项

1. **安全限制**: 不支持任意 JavaScript 代码执行
2. **函数调用**: 仅支持无参数的方法调用
3. **复杂运算**: 仅支持基础的数学运算符 (+, -, *, /)
4. **嵌套限制**: 深度嵌套的对象访问可能影响性能
5. **类型转换**: 所有结果最终转换为字符串显示

## 示例数据结构

```javascript
// 完整的测试数据示例
const testData = {
  user: {
    name: "张三",
    age: 25,
    profile: {
      avatar: "https://example.com/avatar.jpg",
      bio: "前端开发工程师"
    }
  },
  products: [
    { id: 1, name: "苹果", price: 5.5, category: "水果" },
    { id: 2, name: "香蕉", price: 3.2, category: "水果" },
    { id: 3, name: "牛奶", price: 12.8, category: "饮品" }
  ],
  stats: {
    total: 100,
    active: 85,
    pending: 15
  },
  settings: {
    theme: "dark",
    notifications: true
  }
};
```

这个增强的模板解析器大大提升了 Lynx 到 Web 转换的表达能力和实用性。