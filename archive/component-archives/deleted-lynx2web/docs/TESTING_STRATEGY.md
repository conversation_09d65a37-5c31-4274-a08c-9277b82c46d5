# WebPreviewService 自动化测试策略

## 📋 问题分析与优化

### 🔍 当前系统痛点

#### 1. **Worker测试复杂性**
- ❌ **内联Worker难测试**: 当前使用`blob:`URL创建Worker，测试环境模拟困难
- ❌ **假转换逻辑**: Worker当前只返回硬编码HTML，没有真实的Lynx→HTML转换
- ❌ **消息时序控制**: 异步Worker通信+15秒超时+错误处理的复杂时序

#### 2. **依赖环境问题**
- ❌ **html2canvas依赖**: 动态import在测试环境经常失败
- ❌ **iframe DOM操作**: 需要真实浏览器环境，jsdom模拟不完整
- ❌ **跨域安全策略**: blob URL + iframe的安全限制

#### 3. **测试数据不足**
- ❌ **缺少真实Lynx样例**: 没有足够的测试用例覆盖各种场景
- ❌ **边界条件未覆盖**: 大文件、格式错误、空内容等情况
- ❌ **性能基准缺失**: 没有转换速度和内存使用的基准

#### 4. **自动化流程缺陷**
- ❌ **反馈循环慢**: 当前需要手动测试，无法快速迭代
- ❌ **回归检测缺失**: 修改后无法自动验证是否破坏现有功能
- ❌ **可视化结果对比**: 无法自动对比转换前后的效果

## 🎯 精准测试策略

### 🔥 核心测试原则

#### **80/20 覆盖原则**
- 🎯 **80%的问题**来自20%的关键路径
- 🎯 **关键路径**: Worker通信 → 异步链路 → 错误处理
- 🎯 **快速反馈**: 单元测试<1秒，集成测试<5秒，E2E<30秒

#### **分层隔离原则** 
- 🔧 **Unit**: 纯逻辑，Mock一切依赖
- 🔗 **Integration**: 真实服务协作，Mock环境
- 🌐 **E2E**: 真实环境，完整用户流程

## 📊 三层测试架构

### 🧪 Layer 1: 单元测试 (Unit Tests)
**目标**: 快速验证核心逻辑，覆盖率>90%

#### **1.1 Worker逻辑提取测试**
```typescript
// 测试重点：将Worker代码提取为可测试的纯函数
describe('LynxConverter', () => {
  test('convertTTMLToHTML - 基本标签转换', () => {
    const input = '<view><text>Hello</text></view>';
    const output = convertTTMLToHTML(input);
    expect(output).toBe('<div><span>Hello</span></div>');
  });
  
  test('convertTTSS - rpx单位转换', () => {
    const input = '.container { width: 750rpx; }';
    const output = convertTTSS(input);
    expect(output).toBe('.container { width: 375px; }');
  });
  
  test('parseFileStructure - 多文件解析', () => {
    const input = '<FILE path="index.ttml">...</FILE>';
    const files = parseFileStructure(input);
    expect(files['index.ttml']).toBeDefined();
  });
});
```

#### **1.2 服务核心逻辑测试**
```typescript
describe('WebPreviewService', () => {
  test('validateContent - Lynx内容检测', () => {
    const service = new WebPreviewService();
    const result = service.validateContent('<view>test</view>');
    expect(result.isValid).toBe(true);
  });
  
  test('配置合并和默认值', () => {
    const service = new WebPreviewService({ defaultTimeout: 5000 });
    expect(service.getConfig().defaultTimeout).toBe(5000);
  });
});
```

#### **1.3 错误处理测试**
```typescript
describe('错误处理机制', () => {
  test('Worker通信超时', async () => {
    const service = new WebPreviewService({ defaultTimeout: 100 });
    const result = await service.convertToWebPreview('invalid', 'test');
    expect(result.error).toBe('TIMEOUT_ERROR');
  });
  
  test('无效内容处理', async () => {
    const result = await service.convertToWebPreview('', 'test');
    expect(result.error).toBe('VALIDATION_ERROR');
  });
});
```

### 🔗 Layer 2: 集成测试 (Integration Tests)
**目标**: 验证服务协作，测试真实Worker通信

#### **2.1 Worker通信测试**
```typescript
describe('Worker Integration', () => {
  test('Worker创建和通信', async () => {
    const service = new WebPreviewService();
    const isHealthy = await service.checkHealth();
    expect(isHealthy).toBe(true);
  });
  
  test('转换流程完整性', async () => {
    const testLynx = LYNX_TEST_CASES.simple;
    const result = await service.convertToWebPreview(testLynx, 'test');
    
    expect(result.success).toBe(true);
    expect(result.html).toContain('<!DOCTYPE html>');
    expect(result.html).toContain('</html>');
  });
  
  test('并发转换稳定性', async () => {
    const promises = Array(5).fill().map((_, i) => 
      service.convertToWebPreview(LYNX_TEST_CASES.simple, `test-${i}`)
    );
    const results = await Promise.all(promises);
    
    results.forEach(result => {
      expect(result.success).toBe(true);
    });
  });
});
```

#### **2.2 截图服务集成测试**
```typescript
describe('ScreenshotService Integration', () => {
  test('html2canvas可用时正常截图', async () => {
    // Mock html2canvas可用
    mockHtml2Canvas(true);
    
    const html = '<html><body>Test</body></html>';
    const screenshot = await screenshotService.captureHTML(html);
    
    expect(screenshot).toBeTruthy();
    expect(screenshot.url).toMatch(/^data:image/);
  });
  
  test('html2canvas不可用时降级处理', async () => {
    // Mock html2canvas不可用
    mockHtml2Canvas(false);
    
    const html = '<html><body>Test</body></html>';
    const screenshot = await screenshotService.captureHTML(html);
    
    expect(screenshot).toBeNull();
  });
});
```

#### **2.3 性能基准测试**
```typescript
describe('Performance Benchmarks', () => {
  test('转换速度基准', async () => {
    const startTime = performance.now();
    await service.convertToWebPreview(LYNX_TEST_CASES.large, 'perf-test');
    const duration = performance.now() - startTime;
    
    expect(duration).toBeLessThan(5000); // 5秒内完成
  });
  
  test('内存使用监控', async () => {
    const initialMemory = getMemoryUsage();
    
    // 执行多次转换
    for(let i = 0; i < 10; i++) {
      await service.convertToWebPreview(LYNX_TEST_CASES.medium, `mem-test-${i}`);
    }
    
    const finalMemory = getMemoryUsage();
    const memoryGrowth = finalMemory - initialMemory;
    
    expect(memoryGrowth).toBeLessThan(50 * 1024 * 1024); // 内存增长<50MB
  });
});
```

### 🌐 Layer 3: 端到端测试 (E2E Tests)
**目标**: 真实浏览器环境，完整用户流程验证

#### **3.1 用户交互流程测试**
```typescript
describe('用户完整流程', () => {
  test('点击预览按钮到显示结果', async () => {
    // 1. 用户点击Web预览按钮
    await userEvent.click(screen.getByRole('button', { name: 'Web预览' }));
    
    // 2. 验证loading状态
    expect(screen.getByText('检测内容...')).toBeInTheDocument();
    
    // 3. 等待转换完成
    await waitFor(() => {
      expect(screen.getByText('转换中...')).toBeInTheDocument();
    });
    
    // 4. 验证最终结果
    await waitFor(() => {
      expect(screen.getByTestId('preview-iframe')).toBeInTheDocument();
    }, { timeout: 20000 });
  });
  
  test('错误状态显示和重试', async () => {
    // Mock转换失败
    mockConversionFailure();
    
    await userEvent.click(screen.getByRole('button', { name: 'Web预览' }));
    
    // 验证错误显示
    await waitFor(() => {
      expect(screen.getByText(/预览失败/)).toBeInTheDocument();
    });
    
    // 测试重试功能
    await userEvent.click(screen.getByRole('button', { name: '重试' }));
    expect(mockConvertToWebPreview).toHaveBeenCalledTimes(2);
  });
});
```

#### **3.2 可视化回归测试**
```typescript
describe('Visual Regression', () => {
  test('转换结果视觉对比', async () => {
    const testCases = [
      LYNX_TEST_CASES.simple,
      LYNX_TEST_CASES.complex,
      LYNX_TEST_CASES.layout
    ];
    
    for(const lynxCode of testCases) {
      const result = await service.convertToWebPreview(lynxCode, 'visual-test');
      
      // 渲染到iframe并截图
      const screenshot = await captureIframeScreenshot(result.html);
      
      // 与基准图片对比
      const diff = await compareWithBaseline(screenshot, lynxCode);
      expect(diff.mismatchPercentage).toBeLessThan(5); // 差异<5%
    }
  });
});
```

## 📝 测试数据管理

### 🗃️ 测试用例数据库

#### **基础测试用例集**
```typescript
export const LYNX_TEST_CASES = {
  // 基本功能测试
  simple: `
    <template>
      <view class="container">
        <text>Hello Lynx</text>
      </view>
    </template>
    <style>
      .container { padding: 20rpx; }
    </style>
  `,
  
  // 复杂布局测试
  complex: `
    <template>
      <view class="page">
        <view class="header">
          <text class="title">{{title}}</text>
        </view>
        <scroll-view class="content">
          <view tt:for="{{items}}" tt:key="id" class="item">
            <text>{{item.name}}</text>
          </view>
        </scroll-view>
      </view>
    </template>
    <style>
      .page { height: 100vh; }
      .header { height: 88rpx; background: #fff; }
      .content { flex: 1; }
    </style>
    <script>
      Card({
        data: {
          title: "Test Page",
          items: [
            { id: 1, name: "Item 1" },
            { id: 2, name: "Item 2" }
          ]
        }
      });
    </script>
  `,
  
  // 边界条件测试
  empty: '',
  malformed: '<view><text>Unclosed tag',
  large: generateLargeLynxCode(1000), // 生成1000个元素的大型页面
  unicode: '<text>🚀 Unicode Test 中文测试</text>',
  
  // 真实业务场景
  ecommerce: loadRealBusinessCase('ecommerce-page.lynx'),
  form: loadRealBusinessCase('complex-form.lynx'),
  chart: loadRealBusinessCase('data-visualization.lynx')
};
```

#### **期望输出基准**
```typescript
export const EXPECTED_OUTPUTS = {
  simple: {
    htmlStructure: ['<!DOCTYPE html>', '<html>', '<body>', '</body>', '</html>'],
    cssRules: ['.container { padding: 10px; }'],
    jsFeatures: [],
    elementCount: 2,
    estimatedSize: 500 // bytes
  },
  
  complex: {
    htmlStructure: ['<!DOCTYPE html>', '<div class="page">', '<div class="header">'],
    cssRules: ['.page { height: 100vh; }', '.header { height: 44px; }'],
    jsFeatures: ['data binding', 'loop rendering'],
    elementCount: 15,
    estimatedSize: 2000
  }
};
```

## 🔄 自动化流程

### ⚡ 快速反馈循环

#### **Git Hook 触发**
```bash
# .git/hooks/pre-commit
#!/bin/bash
echo "🧪 Running fast tests..."

# 30秒内完成的核心测试
pnpm test:unit --timeout=30000
pnpm test:integration:fast

if [ $? -ne 0 ]; then
  echo "❌ Tests failed. Commit aborted."
  exit 1
fi

echo "✅ Fast tests passed. Commit approved."
```

#### **开发时监听**
```bash
# package.json scripts
{
  "test:watch": "vitest --watch --reporter=verbose",
  "test:unit": "vitest run src/**/*.unit.test.ts",
  "test:integration": "vitest run src/**/*.integration.test.ts", 
  "test:e2e": "playwright test",
  "test:visual": "percy exec -- npm run test:e2e",
  "test:perf": "vitest run src/**/*.perf.test.ts"
}
```

### 🔍 CI/CD 完整验证

#### **分阶段测试流水线**
```yaml
# .github/workflows/test.yml
name: WebPreview Tests
on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Unit Tests
        run: pnpm test:unit
        timeout-minutes: 2
  
  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - name: Integration Tests
        run: pnpm test:integration
        timeout-minutes: 5
  
  e2e-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    steps:
      - name: E2E Tests
        run: pnpm test:e2e
        timeout-minutes: 10
  
  performance-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    steps:
      - name: Performance Benchmarks
        run: pnpm test:perf
      - name: Upload Results
        uses: actions/upload-artifact@v3
        with:
          name: performance-report
          path: performance-report.json
```

### 📊 监控和报告

#### **实时监控面板**
```typescript
// 测试结果监控
interface TestMetrics {
  unitTests: {
    passed: number;
    failed: number;
    coverage: number;
    duration: number;
  };
  integrationTests: {
    passed: number;
    failed: number;
    averageResponseTime: number;
  };
  e2eTests: {
    passed: number;
    failed: number;
    visualRegressions: number;
  };
  performance: {
    conversionSpeed: number; // ms
    memoryUsage: number;     // MB  
    errorRate: number;       // %
  };
}
```

#### **自动报告生成**
```typescript
// 每日测试报告
generateDailyReport({
  testCoverage: '94.2%',
  performanceTrend: '+2.1%', // 性能提升
  newFailures: 0,
  fixedIssues: 3,
  visualRegressions: 0,
  recommendations: [
    '建议增加大文件转换的测试用例',
    '可以优化Worker内存使用'
  ]
});
```

## 🛠️ 技术栈配置

### 📦 依赖包选择
```json
{
  "devDependencies": {
    "vitest": "^1.0.0",              // 快速测试运行器  
    "@testing-library/react": "^14.0.0",  // React组件测试
    "@testing-library/user-event": "^14.0.0", // 用户交互模拟
    "happy-dom": "^12.0.0",          // 轻量DOM环境
    "playwright": "^1.40.0",         // E2E测试
    "@percy/playwright": "^1.0.0",   // 视觉回归测试
    "msw": "^2.0.0",                 // API Mock
    "benchmark": "^2.1.4",          // 性能基准测试
    "memfs": "^4.0.0",              // 文件系统Mock
    "fake-indexeddb": "^5.0.0"      // IndexedDB Mock
  }
}
```

### ⚙️ Vitest配置优化
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    environment: 'happy-dom', // 比jsdom快3倍
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      provider: 'v8',
      threshold: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    benchmark: {
      include: ['**/*.{bench,benchmark}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}']
    },
    testTimeout: 30000, // 30秒超时
    pool: 'threads',    // 并行执行
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 4
      }
    }
  }
});
```

## 🎯 实施路线图

### 📅 Phase 1: 基础框架 (Week 1)
- [x] ✅ 配置Vitest + Testing Library
- [x] ✅ 创建测试数据集和Mock工具
- [x] ✅ 实现核心单元测试
- [x] ✅ 设置Git Hook快速反馈

### 📅 Phase 2: 核心测试 (Week 2)  
- [ ] 🔧 Worker逻辑提取和测试
- [ ] 🔧 集成测试覆盖异步流程
- [ ] 🔧 错误处理和边界条件测试
- [ ] 🔧 性能基准测试建立

### 📅 Phase 3: 完整验证 (Week 3)
- [ ] 🌐 E2E测试覆盖用户流程
- [ ] 📸 视觉回归测试
- [ ] 📊 CI/CD集成和监控面板
- [ ] 📈 自动化报告和预警

### 📅 Phase 4: 优化迭代 (Week 4)
- [ ] 🚀 测试性能优化
- [ ] 📝 测试用例补充和维护
- [ ] 🔍 监控数据分析和改进
- [ ] 📚 团队培训和文档完善

## 🏆 成功指标

### 📊 量化指标
- **测试覆盖率**: >90% (当前: 0%)
- **测试执行时间**: 单元<1秒, 集成<5秒, E2E<30秒
- **反馈循环**: Commit到结果<2分钟  
- **Bug检出率**: >95%的回归问题在CI中发现
- **性能监控**: 转换速度基准±10%内波动

### 🎯 质量指标  
- **零回归**: 主要功能零破坏性变更
- **快速迭代**: 每次修改都有自动化验证
- **可视化对比**: 转换效果自动对比验证
- **团队效率**: 减少50%手动测试时间

---

## 📋 总结

这个测试策略解决了当前WebPreviewService的核心痛点：

1. **🔧 Worker测试问题** → 逻辑提取 + MessageChannel模拟
2. **⚡ 反馈循环慢** → Git Hook + 分层测试 + 快速执行
3. **🔍 覆盖不全** → 数据驱动 + 边界条件 + 真实场景
4. **📊 缺少监控** → 性能基准 + 视觉回归 + 自动报告

通过80/20原则重点覆盖关键路径，确保核心功能稳定的同时，支持快速迭代开发。