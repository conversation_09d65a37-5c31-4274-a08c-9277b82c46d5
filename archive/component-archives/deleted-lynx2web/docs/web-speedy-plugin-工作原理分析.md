# web-speedy-plugin 工作原理分析

## 概述

`@byted-lynx/web-speedy-plugin` 是一个用于将 TTML（Lynx 模板语言）和相关资源转换为 Web 运行时代码的编译插件。该插件是 Lynx 生态系统中的核心组件，负责将 Lynx DSL 转换为可在 Web 环境中运行的代码。

## 核心功能

### 1. 多种 DSL 模式支持
插件支持三种运行时 DSL 模式：
- `ttml`: Lynx 原生模板语言
- `reactlynx2`: React Lynx 2.0 模式  
- `reactlynx3`: React Lynx 3.0 模式

### 2. 文件类型转换处理
插件能够处理多种文件类型的转换：
- `.ttml` → JSX 模块
- `.js/.ts` → 经过 TTML 脚本转换
- `.jsx/.tsx` → React Lynx 转换
- `.css/.less/.scss/.sass/.ttss` → Web CSS 转换
- `.lepus` → Lepus 文件转换

## 工作流程

### 1. 配置阶段 (Configuration)
- 合并用户配置和默认配置
- 设置文件扩展名解析规则
- 配置模块解析策略
- 处理入口文件路径转换（`.js/.ts` → `.ttml`）

### 2. 解析阶段 (Resolve)
- 处理模块路径解析
- 管理查询参数传播（lepus、tagV、ttml、common）
- 处理 commonChunk 解析
- 支持虚拟文件系统（如 `.lynxWebCssEntry`）

### 3. 加载阶段 (Load)
- 为不同文件类型注册相应的加载器
- 处理 commonChunk 的动态加载逻辑

### 4. 转换阶段 (Transform)
- TTML 模板转 JSX
- CSS 样式作用域化
- Lepus 脚本转换
- React Lynx 组件转换

### 5. 资源处理阶段 (ProcessAssets)
- CSS-in-JS 处理
- Web Runtime 模块生成
- Source Map 修复
- SPA 模式子编译

## 转换规则详解

### TTML 转换规则

#### 1. 模板文件转换 (`.ttml` → JSX)
```javascript
// 转换过程：
// 1. 加载对应的 .json 配置文件
// 2. 解析 TTML 模板语法
// 3. 生成 JSX 模块代码
// 4. 添加调试标签（开发模式）
// 5. 处理组件导入和作用域
```

**转换选项：**
- `tagV`: 5位哈希标识符，用于组件作用域
- `addLynxWebDebugTag`: 开发模式下添加调试标签
- `enableInferComponent`: 启用组件推断
- `lepusStrict`: 严格模式 Lepus 处理
- `useLepusNG`: 使用新版 Lepus 解析器

#### 2. 样式文件转换
```javascript
// CSS 作用域化规则：
// 1. 根据 tagV 添加选择器前缀
// 2. 处理全局样式模块（globalModulePaths）
// 3. RPX 单位转换
// 4. 选择器优先级提升（webBumpAllSelectorSpecificity）
```

**样式处理参数：**
- `tagV`: 组件作用域标识
- `common`: 公共样式标记
- `globalModulePaths`: 全局样式路径匹配
- `enableRemoveCSSScope`: 禁用样式作用域

#### 3. Lepus 脚本转换
```javascript
// Lepus 处理规则：
// 1. 支持 .js/.ts/.lepus 扩展名
// 2. lepusNullPropAsUndef: null 属性转为 undefined
// 3. useLepusNG: 使用新版解析器
// 4. lepusStrict: 严格模式处理
```

### React Lynx 转换规则

#### JSX/TSX 组件转换
- 添加运行时调试信息
- 组件标签收集和注册
- DSL 模式适配（reactlynx2/reactlynx3）

### 模块解析规则

#### 查询参数传播 (Query Infection)
```javascript
// 感染性查询参数：
{
  lepus: true,              // 传播到所有文件
  tagV: supportedCssExtname, // 仅传播到CSS文件
  common: true              // 传播到所有文件
}
```

#### 文件扩展名优先级
```javascript
extensions: [
  '.ttml',   // 最高优先级
  '.jsx', '.tsx',
  '.js', '.ts',
  '.lepus',
  '.css', '.less', '.sass', '.scss', '.ttss',
  '.json'
]
```

### CommonChunk 机制

#### 1. 按需加载规则
```javascript
// URL 格式：./module?commonChunk&name=chunkName
// 解析为：lynx.requireModule(chunkUrl)
// 异步加载：./module?commonChunk&async
```

#### 2. 子编译配置
- 独立的编译上下文
- 共享主编译器配置
- 输出到 `lynx_common_chunks` 目录

### SPA 模式处理

#### 1. SPA 编译流程
- 禁用代码压缩（`minify = false`）
- 清空构建目标（`target = []`）
- 创建虚拟入口：`webruntime2-virtual-spa-entry`
- 生成 HTML 和 JS 文件

#### 2. 资源打包
- React 运行时外部化
- IIFE 格式输出
- 全局变量：`entryComponent`

## 配置参数详解

### 核心配置项
```javascript
{
  runtimeDslMode: 'ttml' | 'reactlynx2' | 'reactlynx3',
  spa: boolean, // SPA 模式开关
  forceBundleCommonChunk: boolean, // 强制打包 commonChunk
  rpx: {
    rpxMode: RpxMode,
    designWidth: number // 默认 750
  },
  pageConfig: {
    useLepusNG: boolean,
    lepusStrict: boolean,
    lepusNullPropAsUndef: boolean,
    enableCSSInheritance: boolean,
    enableComponentNullProp: boolean,
    defaultDisplayLinear: boolean,
    defaultOverflowVisible: boolean,
    enableTextOverflow: boolean,
    webBumpAllSelectorSpecificity: boolean
  }
}
```

### TTML 选项
```javascript
ttmlOptions: {
  enableInferComponent: boolean,
  plugins: Array, // TTML 解析器插件
  usingComponents: Record<string, string> // 组件映射
}
```

## 插件钩子执行顺序

1. **configuration**: 配置合并和验证
2. **bundlerConfiguration**: 打包器配置
3. **startCompilation**: 编译开始前处理
4. **resolve**: 模块路径解析
5. **afterResolved**: 解析后处理
6. **load**: 文件加载
7. **transform**: 代码转换
8. **processAssets**: 资源处理
9. **beforeEmit**: 输出前处理

## 性能优化机制

### 1. 文件缓存
- 中间文件输出到临时目录
- Source Map 缓存和重用

### 2. 按需编译
- CommonChunk 独立编译
- SPA 子编译隔离

### 3. 并行处理
- 多文件并行转换
- 异步资源处理

## 调试和开发支持

### 开发模式特性
- 调试标签注入
- 详细日志输出
- Source Map 支持
- 热更新兼容

### 错误处理
- 详细错误信息
- 编译失败回滚
- 依赖文件监听

## 总结

`web-speedy-plugin` 是一个功能完整的 DSL 转换插件，通过精心设计的转换管道，将 Lynx 生态系统中的各种资源类型转换为 Web 兼容的代码。其核心优势在于：

1. **多格式支持**: 支持 TTML、React Lynx、CSS 等多种文件格式
2. **智能解析**: 通过查询参数传播实现上下文感知的模块解析
3. **性能优化**: CommonChunk 机制和并行处理提升构建效率
4. **开发友好**: 丰富的调试功能和错误处理机制

该插件是 Lynx 到 Web 转换的核心引擎，为跨平台开发提供了强有力的技术支撑。