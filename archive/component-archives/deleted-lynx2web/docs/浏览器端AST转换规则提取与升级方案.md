# Search-so-ai Lynx2Web浏览器端AST转换规则提取与升级方案

## 1. 项目背景与挑战

### 1.1 核心问题分析
当前search-so-ai的lynx2web功能完全在浏览器端运行，而@byted-lynx/web-speedy-plugin依赖Node.js构建环境，无法直接在浏览器中使用。需要**提取核心转换规则**，设计纯浏览器端的AST解析和转换方案。

### 1.2 架构约束
- **纯浏览器环境**：无Node.js依赖，无文件系统访问
- **实时转换**：用户输入后即时生成预览
- **内存限制**：需要高效的内存管理和缓存策略
- **性能要求**：转换时间 <2秒，支持Web Worker

### 1.3 技术目标
- 提取@byted-lynx/web-speedy-plugin的100%转换规则
- 设计轻量级浏览器端AST解析引擎
- 实现企业级转换质量的纯JavaScript方案
- 保持与现有UI组件的完全兼容

## 2. AST映射规则深度分析

### 2.1 核心转换架构提取

基于对lina-mono中@byted-lynx/web-speedy-plugin的深入分析，核心转换流程为：

```
TTML源码 → 词法分析 → 语法分析 → AST构建 → 插件转换 → JSX生成 → CSS处理 → 完整Web应用
```

#### 2.1.1 词法分析器规则提取
```javascript
// 提取的Token类型定义
const TokenType = {
  StartTag: 'startTag',           // <view>
  EndTag: 'endTag',              // </view>
  SelfClosingTag: 'selfClosing', // <image />
  AttributeName: 'attrName',     // class, lx:if
  AttributeValue: 'attrValue',   // "container", "{{data}}"
  Text: 'text',                  // 文本内容
  Mustache: 'mustache',          // {{expression}}
  Comment: 'comment',            // <!-- 注释 -->
  Whitespace: 'whitespace'       // 空白字符
};

// 核心词法分析正则表达式
const LEXER_PATTERNS = {
  // 标签识别
  TAG_PATTERN: /<\/?([a-zA-Z-]+)([^>]*?)(\/>|>)/g,
  
  // 属性解析
  ATTR_PATTERN: /([a-zA-Z:-]+)(?:=["']([^"']*?)["'])?/g,
  
  // 插值表达式
  MUSTACHE_PATTERN: /\{\{([^}]+)\}\}/g,
  
  // 注释
  COMMENT_PATTERN: /<!--([^-]*)-->/g,
  
  // 文本内容
  TEXT_PATTERN: /[^<{]+/g
};
```

#### 2.1.2 AST节点类型定义
```javascript
// 完整的AST节点类型
const ASTNodeType = {
  Root: 'root',
  Element: 'element',
  Text: 'text',
  Comment: 'comment',
  Mustache: 'mustache',
  Attribute: 'attribute',
  Directive: 'directive',
  Component: 'component'
};

// AST节点结构标准
const createASTNode = (type, props = {}) => ({
  type,
  ...props,
  children: props.children || [],
  attributes: props.attributes || {},
  position: props.position || { line: 0, column: 0 }
});
```

### 2.2 元素映射规则完整提取

#### 2.2.1 基础元素映射表
```javascript
// 从web-speedy-plugin提取的完整映射规则
const ELEMENT_MAPPING = {
  // 布局容器
  'view': { tag: 'div', props: { className: 'lynx-view' } },
  'scroll-view': { 
    tag: 'div', 
    props: { className: 'lynx-scroll-view', style: { overflow: 'auto' } }
  },
  
  // 文本相关
  'text': { tag: 'span', props: { className: 'lynx-text' } },
  'rich-text': { tag: 'div', props: { className: 'lynx-rich-text' } },
  
  // 媒体元素
  'image': { 
    tag: 'img', 
    props: { className: 'lynx-image' },
    selfClosing: true,
    attributeMapping: {
      'src': 'src',
      'mode': 'objectFit'
    }
  },
  'video': { tag: 'video', props: { className: 'lynx-video', controls: true } },
  'audio': { tag: 'audio', props: { className: 'lynx-audio', controls: true } },
  
  // 表单元素
  'input': { 
    tag: 'input', 
    props: { className: 'lynx-input' },
    selfClosing: true,
    attributeMapping: {
      'type': 'type',
      'placeholder': 'placeholder',
      'value': 'value'
    }
  },
  'textarea': { tag: 'textarea', props: { className: 'lynx-textarea' } },
  'button': { tag: 'button', props: { className: 'lynx-button' } },
  'switch': { 
    tag: 'input', 
    props: { type: 'checkbox', className: 'lynx-switch' },
    selfClosing: true 
  },
  'slider': { 
    tag: 'input', 
    props: { type: 'range', className: 'lynx-slider' },
    selfClosing: true 
  },
  'picker': { tag: 'select', props: { className: 'lynx-picker' } },
  
  // 导航相关
  'navigator': { tag: 'a', props: { className: 'lynx-navigator' } },
  'link': { tag: 'a', props: { className: 'lynx-link' } },
  
  // 高级组件
  'swiper': { tag: 'div', props: { className: 'lynx-swiper' } },
  'swiper-item': { tag: 'div', props: { className: 'lynx-swiper-item' } },
  'progress': { tag: 'progress', props: { className: 'lynx-progress' } },
  'web-view': { 
    tag: 'iframe', 
    props: { className: 'lynx-web-view' },
    selfClosing: true 
  },
  
  // 列表相关
  'list': { tag: 'div', props: { className: 'lynx-list' } },
  'list-item': { tag: 'div', props: { className: 'lynx-list-item' } },
  'cell': { tag: 'div', props: { className: 'lynx-cell' } },
  'header': { tag: 'header', props: { className: 'lynx-header' } },
  'footer': { tag: 'footer', props: { className: 'lynx-footer' } }
};

// 自闭合标签集合
const SELF_CLOSING_TAGS = new Set([
  'image', 'input', 'switch', 'slider', 'progress', 'web-view'
]);
```

#### 2.2.2 属性转换规则
```javascript
// 通用属性映射
const COMMON_ATTRIBUTE_MAPPING = {
  'class': 'className',
  'style': 'style',
  'id': 'id',
  'hidden': 'hidden',
  'data-*': 'data-*', // 保持data属性不变
};

// 特殊属性处理
const SPECIAL_ATTRIBUTE_HANDLERS = {
  // 样式属性特殊处理
  'style': (value, context) => {
    if (typeof value === 'string') {
      return parseInlineStyle(value, context.componentId);
    }
    return value;
  },
  
  // 类名属性处理
  'class': (value, context) => {
    if (value.includes('{{')) {
      return generateDynamicClassName(value);
    }
    return value;
  },
  
  // ID属性作用域化
  'id': (value, context) => {
    return `${context.componentId}-${value}`;
  }
};
```

### 2.3 指令系统完整规则

#### 2.3.1 结构指令映射
```javascript
// 完整的指令转换规则
const DIRECTIVE_MAPPING = {
  // 条件渲染指令
  'lx:if': {
    type: 'conditional',
    transform: (value, node, context) => ({
      type: 'ConditionalExpression',
      test: parseExpression(value, context),
      consequent: transformNode(node, context),
      alternate: null
    })
  },
  
  'lx:elif': {
    type: 'conditional',
    transform: (value, node, context) => ({
      type: 'ConditionalExpression', 
      test: parseExpression(value, context),
      consequent: transformNode(node, context),
      alternate: null // 由chain处理器处理
    })
  },
  
  'lx:else': {
    type: 'conditional',
    transform: (value, node, context) => transformNode(node, context)
  },
  
  // 列表渲染指令
  'lx:for': {
    type: 'iteration',
    transform: (value, node, context) => {
      const { itemName, indexName, listExpression } = parseForExpression(value);
      
      return {
        type: 'JSXExpressionContainer',
        expression: {
          type: 'CallExpression',
          callee: {
            type: 'MemberExpression',
            object: parseExpression(listExpression, context),
            property: { type: 'Identifier', name: 'map' }
          },
          arguments: [
            {
              type: 'ArrowFunctionExpression',
              params: [
                { type: 'Identifier', name: itemName },
                { type: 'Identifier', name: indexName }
              ],
              body: transformNode(node, {
                ...context,
                scope: { ...context.scope, [itemName]: true, [indexName]: true }
              })
            }
          ]
        }
      };
    }
  },
  
  'lx:key': {
    type: 'optimization',
    transform: (value, node, context) => {
      // React key属性
      return {
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name: 'key' },
        value: {
          type: 'JSXExpressionContainer',
          expression: parseExpression(value, context)
        }
      };
    }
  }
};
```

#### 2.3.2 事件指令映射
```javascript
// 完整的事件映射规则
const EVENT_DIRECTIVE_MAPPING = {
  // 冒泡事件 (bind*)
  'bindtap': { reactEvent: 'onClick', stopPropagation: false },
  'bindinput': { reactEvent: 'onInput', stopPropagation: false },
  'bindchange': { reactEvent: 'onChange', stopPropagation: false },
  'bindblur': { reactEvent: 'onBlur', stopPropagation: false },
  'bindfocus': { reactEvent: 'onFocus', stopPropagation: false },
  'bindscroll': { reactEvent: 'onScroll', stopPropagation: false },
  'bindtouchstart': { reactEvent: 'onTouchStart', stopPropagation: false },
  'bindtouchmove': { reactEvent: 'onTouchMove', stopPropagation: false },
  'bindtouchend': { reactEvent: 'onTouchEnd', stopPropagation: false },
  
  // 捕获事件 (catch:*)
  'catch:tap': { reactEvent: 'onClick', stopPropagation: true },
  'catch:input': { reactEvent: 'onInput', stopPropagation: true },
  'catch:change': { reactEvent: 'onChange', stopPropagation: true },
  'catch:scroll': { reactEvent: 'onScroll', stopPropagation: true },
  'catch:touchstart': { reactEvent: 'onTouchStart', stopPropagation: true },
  'catch:touchmove': { reactEvent: 'onTouchMove', stopPropagation: true },
  'catch:touchend': { reactEvent: 'onTouchEnd', stopPropagation: true }
};

// 事件处理器生成
function transformEventHandler(eventName, handlerExpression, eventConfig) {
  const { reactEvent, stopPropagation } = eventConfig;
  
  return {
    type: 'JSXAttribute',
    name: { type: 'JSXIdentifier', name: reactEvent },
    value: {
      type: 'JSXExpressionContainer',
      expression: {
        type: 'ArrowFunctionExpression',
        params: [{ type: 'Identifier', name: 'e' }],
        body: {
          type: 'BlockStatement',
          body: [
            // 阻止事件传播
            ...(stopPropagation ? [{
              type: 'ExpressionStatement',
              expression: {
                type: 'CallExpression',
                callee: {
                  type: 'MemberExpression',
                  object: { type: 'Identifier', name: 'e' },
                  property: { type: 'Identifier', name: 'stopPropagation' }
                },
                arguments: []
              }
            }] : []),
            
            // 执行用户处理器
            {
              type: 'ExpressionStatement',
              expression: {
                type: 'CallExpression',
                callee: parseExpression(handlerExpression),
                arguments: [{ type: 'Identifier', name: 'e' }]
              }
            }
          ]
        }
      }
    }
  };
}
```

### 2.4 样式转换规则 (TTSS → CSS)

#### 2.4.1 RPX单位转换规则
```javascript
// 从web-speedy-plugin提取的RPX转换策略
const RpxMode = {
  VW: 'vw',     // viewport width
  REM: 'rem',   // root em
  PX: 'px',     // fixed pixel
  CALC: 'calc'  // CSS calc()
};

const RPX_CONVERSION_CONFIG = {
  designWidth: 750,        // 设计稿宽度
  defaultMode: RpxMode.VW, // 默认转换模式
  
  // 不同转换模式的计算公式
  converters: {
    [RpxMode.VW]: (rpxValue, designWidth) => 
      `${(rpxValue / designWidth * 100).toFixed(6)}vw`,
      
    [RpxMode.REM]: (rpxValue, designWidth) => 
      `${(rpxValue / 37.5).toFixed(6)}rem`,
      
    [RpxMode.PX]: (rpxValue, designWidth) => 
      `${rpxValue * (window.innerWidth / designWidth)}px`,
      
    [RpxMode.CALC]: (rpxValue, designWidth) => 
      `calc(${rpxValue} * 100vw / ${designWidth})`
  }
};

// RPX转换核心函数
function convertRpxUnits(css, config = RPX_CONVERSION_CONFIG) {
  const { converters, defaultMode, designWidth } = config;
  const converter = converters[defaultMode];
  
  return css.replace(/(\d+(?:\.\d+)?)rpx/g, (match, value) => {
    const rpxValue = parseFloat(value);
    return converter(rpxValue, designWidth);
  });
}
```

#### 2.4.2 CSS选择器作用域化
```javascript
// CSS作用域隔离规则
function addSelectorScope(css, componentId) {
  const scopeAttribute = `[data-v-${componentId}]`;
  
  return css.replace(/([^{}]+)\s*\{/g, (match, selectors) => {
    const scopedSelectors = selectors
      .split(',')
      .map(selector => {
        const trimmed = selector.trim();
        
        // 保持全局选择器不变
        if (isGlobalSelector(trimmed)) {
          return trimmed;
        }
        
        // 深度选择器处理
        if (trimmed.includes('>>>') || trimmed.includes('/deep/')) {
          return handleDeepSelector(trimmed, scopeAttribute);
        }
        
        // 普通选择器添加作用域
        return `${scopeAttribute} ${trimmed}`;
      })
      .join(', ');
    
    return `${scopedSelectors} {`;
  });
}

// 全局选择器检测
function isGlobalSelector(selector) {
  const globalPatterns = [
    /^@/,                    // @media, @keyframes等
    /^:root/,               // CSS变量
    /^html\b/,              // html标签
    /^body\b/,              // body标签
    /^\*/,                  // 通配符
    /^\.global-/            // 自定义全局类
  ];
  
  return globalPatterns.some(pattern => pattern.test(selector));
}

// 深度选择器处理
function handleDeepSelector(selector, scopeAttribute) {
  return selector
    .replace(/\s*>>>\s*/, ` ${scopeAttribute} `)
    .replace(/\s*\/deep\/\s*/, ` ${scopeAttribute} `);
}
```

#### 2.4.3 CSS-in-JS生成
```javascript
// 将CSS转换为JavaScript对象
function generateCssInJs(css, componentId) {
  const rules = parseCSS(css);
  const jsObject = {};
  
  rules.forEach(rule => {
    if (rule.type === 'rule') {
      const className = generateClassName(rule.selector, componentId);
      jsObject[className] = {};
      
      rule.declarations.forEach(decl => {
        const property = camelCaseProperty(decl.property);
        const value = normalizePropertyValue(decl.value);
        jsObject[className][property] = value;
      });
    }
  });
  
  return {
    cssObject: jsObject,
    cssString: generateCssString(jsObject),
    classNames: Object.keys(jsObject)
  };
}

// CSS属性名驼峰化
function camelCaseProperty(property) {
  return property.replace(/-([a-z])/g, (match, letter) => 
    letter.toUpperCase()
  );
}

// 属性值标准化
function normalizePropertyValue(value) {
  // 处理数值
  if (/^\d+$/.test(value)) {
    return parseInt(value);
  }
  
  // 处理像素值
  if (/^\d+px$/.test(value)) {
    return parseInt(value);
  }
  
  // 保持字符串值
  return value;
}
```

## 3. 浏览器端AST引擎设计

### 3.1 轻量级词法分析器

```javascript
// 浏览器端优化的词法分析器
class BrowserLexer {
  constructor() {
    this.tokens = [];
    this.position = 0;
    this.line = 1;
    this.column = 1;
  }

  tokenize(input) {
    this.tokens = [];
    this.position = 0;
    this.line = 1;
    this.column = 1;
    
    const patterns = [
      { type: 'COMMENT', regex: /<!--[\s\S]*?-->/ },
      { type: 'MUSTACHE', regex: /\{\{[^}]*\}\}/ },
      { type: 'START_TAG', regex: /<([a-zA-Z-]+)([^>]*?)>/ },
      { type: 'END_TAG', regex: /<\/([a-zA-Z-]+)>/ },
      { type: 'SELF_CLOSING', regex: /<([a-zA-Z-]+)([^>]*?)\/>/ },
      { type: 'TEXT', regex: /[^<{]+/ }
    ];
    
    while (this.position < input.length) {
      let matched = false;
      
      for (const pattern of patterns) {
        const regex = new RegExp(pattern.regex.source, 'y');
        regex.lastIndex = this.position;
        
        const match = regex.exec(input);
        if (match) {
          this.addToken(pattern.type, match);
          this.position = regex.lastIndex;
          matched = true;
          break;
        }
      }
      
      if (!matched) {
        this.position++;
        this.column++;
      }
    }
    
    return this.tokens;
  }

  addToken(type, match) {
    const token = {
      type,
      value: match[0],
      position: { line: this.line, column: this.column },
      groups: match.slice(1)
    };
    
    this.tokens.push(token);
    this.updatePosition(match[0]);
  }

  updatePosition(text) {
    for (const char of text) {
      if (char === '\n') {
        this.line++;
        this.column = 1;
      } else {
        this.column++;
      }
    }
  }
}
```

### 3.2 AST构建器

```javascript
// 浏览器端AST构建器
class BrowserASTBuilder {
  constructor() {
    this.stack = [];
    this.root = null;
    this.current = null;
  }

  build(tokens) {
    this.stack = [];
    this.root = this.createNode('root');
    this.current = this.root;
    this.stack.push(this.root);
    
    for (const token of tokens) {
      this.processToken(token);
    }
    
    return this.root;
  }

  processToken(token) {
    switch (token.type) {
      case 'START_TAG':
        this.handleStartTag(token);
        break;
      case 'END_TAG':
        this.handleEndTag(token);
        break;
      case 'SELF_CLOSING':
        this.handleSelfClosingTag(token);
        break;
      case 'TEXT':
        this.handleText(token);
        break;
      case 'MUSTACHE':
        this.handleMustache(token);
        break;
      case 'COMMENT':
        this.handleComment(token);
        break;
    }
  }

  handleStartTag(token) {
    const [, tagName, attributesStr] = token.groups;
    const attributes = this.parseAttributes(attributesStr);
    
    const element = this.createNode('element', {
      tagName,
      attributes,
      position: token.position
    });
    
    this.current.children.push(element);
    
    if (!SELF_CLOSING_TAGS.has(tagName)) {
      this.stack.push(element);
      this.current = element;
    }
  }

  handleEndTag(token) {
    if (this.stack.length > 1) {
      this.stack.pop();
      this.current = this.stack[this.stack.length - 1];
    }
  }

  handleSelfClosingTag(token) {
    const [, tagName, attributesStr] = token.groups;
    const attributes = this.parseAttributes(attributesStr);
    
    const element = this.createNode('element', {
      tagName,
      attributes,
      selfClosing: true,
      position: token.position
    });
    
    this.current.children.push(element);
  }

  handleText(token) {
    const textNode = this.createNode('text', {
      content: token.value.trim(),
      position: token.position
    });
    
    if (textNode.content) {
      this.current.children.push(textNode);
    }
  }

  handleMustache(token) {
    const expression = token.value.slice(2, -2).trim();
    const mustacheNode = this.createNode('mustache', {
      expression,
      position: token.position
    });
    
    this.current.children.push(mustacheNode);
  }

  handleComment(token) {
    const commentNode = this.createNode('comment', {
      content: token.value.slice(4, -3).trim(),
      position: token.position
    });
    
    this.current.children.push(commentNode);
  }

  parseAttributes(attributesStr) {
    const attributes = {};
    const attrRegex = /([a-zA-Z:-]+)(?:=["']([^"']*)["'])?/g;
    let match;
    
    while ((match = attrRegex.exec(attributesStr)) !== null) {
      const [, name, value = ''] = match;
      attributes[name] = value;
    }
    
    return attributes;
  }

  createNode(type, props = {}) {
    return {
      type,
      children: [],
      ...props
    };
  }
}
```

### 3.3 AST转换器

```javascript
// 完整的AST转换器
class BrowserASTTransformer {
  constructor(config = {}) {
    this.config = {
      componentId: config.componentId || this.generateComponentId(),
      rpxMode: config.rpxMode || RpxMode.VW,
      designWidth: config.designWidth || 750,
      ...config
    };
  }

  transform(ast) {
    return this.transformNode(ast, {
      componentId: this.config.componentId,
      scope: {},
      isRoot: true
    });
  }

  transformNode(node, context) {
    switch (node.type) {
      case 'root':
        return this.transformRoot(node, context);
      case 'element':
        return this.transformElement(node, context);
      case 'text':
        return this.transformText(node, context);
      case 'mustache':
        return this.transformMustache(node, context);
      case 'comment':
        return this.transformComment(node, context);
      default:
        return null;
    }
  }

  transformRoot(node, context) {
    const children = node.children
      .map(child => this.transformNode(child, { ...context, isRoot: false }))
      .filter(Boolean);

    return {
      type: 'JSXFragment',
      children
    };
  }

  transformElement(node, context) {
    const { tagName, attributes } = node;
    
    // 检查是否为指令元素
    if (this.hasDirectives(attributes)) {
      return this.transformDirectiveElement(node, context);
    }
    
    // 普通元素转换
    const mapping = ELEMENT_MAPPING[tagName];
    if (!mapping) {
      console.warn(`Unknown element: ${tagName}`);
      return null;
    }
    
    const jsxElement = {
      type: 'JSXElement',
      openingElement: {
        type: 'JSXOpeningElement',
        name: { type: 'JSXIdentifier', name: mapping.tag },
        attributes: this.transformAttributes(attributes, mapping, context),
        selfClosing: mapping.selfClosing || SELF_CLOSING_TAGS.has(tagName)
      },
      children: mapping.selfClosing ? [] : node.children
        .map(child => this.transformNode(child, context))
        .filter(Boolean)
    };
    
    if (!mapping.selfClosing) {
      jsxElement.closingElement = {
        type: 'JSXClosingElement',
        name: { type: 'JSXIdentifier', name: mapping.tag }
      };
    }
    
    return jsxElement;
  }

  transformDirectiveElement(node, context) {
    const { attributes } = node;
    
    // 处理条件渲染
    if (attributes['lx:if'] || attributes['lx:elif'] || attributes['lx:else']) {
      return this.transformConditionalElement(node, context);
    }
    
    // 处理列表渲染
    if (attributes['lx:for']) {
      return this.transformForElement(node, context);
    }
    
    // 移除指令属性后正常转换
    const cleanAttributes = { ...attributes };
    delete cleanAttributes['lx:key'];
    
    return this.transformElement({
      ...node,
      attributes: cleanAttributes
    }, context);
  }

  transformConditionalElement(node, context) {
    const { attributes } = node;
    let condition = null;
    
    if (attributes['lx:if']) {
      condition = this.parseExpression(attributes['lx:if'], context);
    } else if (attributes['lx:elif']) {
      condition = this.parseExpression(attributes['lx:elif'], context);
    }
    
    const element = this.transformElement({
      ...node,
      attributes: this.removeDirectiveAttributes(attributes)
    }, context);
    
    if (condition) {
      return {
        type: 'JSXExpressionContainer',
        expression: {
          type: 'LogicalExpression',
          operator: '&&',
          left: condition,
          right: element
        }
      };
    }
    
    return element;
  }

  transformForElement(node, context) {
    const forValue = node.attributes['lx:for'];
    const { itemName, indexName, listExpression } = this.parseForExpression(forValue);
    
    const element = this.transformElement({
      ...node,
      attributes: this.removeDirectiveAttributes(node.attributes)
    }, {
      ...context,
      scope: { ...context.scope, [itemName]: true, [indexName]: true }
    });
    
    return {
      type: 'JSXExpressionContainer',
      expression: {
        type: 'CallExpression',
        callee: {
          type: 'MemberExpression',
          object: this.parseExpression(listExpression, context),
          property: { type: 'Identifier', name: 'map' }
        },
        arguments: [
          {
            type: 'ArrowFunctionExpression',
            params: [
              { type: 'Identifier', name: itemName },
              { type: 'Identifier', name: indexName }
            ],
            body: element
          }
        ]
      }
    };
  }

  transformAttributes(attributes, mapping, context) {
    const jsxAttributes = [];
    
    // 添加默认属性
    if (mapping.props) {
      Object.entries(mapping.props).forEach(([key, value]) => {
        jsxAttributes.push({
          type: 'JSXAttribute',
          name: { type: 'JSXIdentifier', name: key },
          value: typeof value === 'string' 
            ? { type: 'Literal', value }
            : { type: 'JSXExpressionContainer', expression: value }
        });
      });
    }
    
    // 转换用户属性
    Object.entries(attributes).forEach(([name, value]) => {
      // 跳过指令属性
      if (name.startsWith('lx:') || name.startsWith('bind') || name.startsWith('catch:')) {
        if (name.startsWith('bind') || name.startsWith('catch:')) {
          const eventAttr = this.transformEventAttribute(name, value, context);
          if (eventAttr) jsxAttributes.push(eventAttr);
        }
        return;
      }
      
      // 普通属性转换
      const transformedAttr = this.transformAttribute(name, value, mapping, context);
      if (transformedAttr) jsxAttributes.push(transformedAttr);
    });
    
    // 添加组件作用域属性
    jsxAttributes.push({
      type: 'JSXAttribute',
      name: { type: 'JSXIdentifier', name: 'data-v-' + context.componentId },
      value: { type: 'Literal', value: '' }
    });
    
    return jsxAttributes;
  }

  transformAttribute(name, value, mapping, context) {
    // 属性名映射
    const mappedName = COMMON_ATTRIBUTE_MAPPING[name] || 
                      (mapping.attributeMapping && mapping.attributeMapping[name]) || 
                      name;
    
    // 特殊属性处理
    if (SPECIAL_ATTRIBUTE_HANDLERS[name]) {
      const processedValue = SPECIAL_ATTRIBUTE_HANDLERS[name](value, context);
      return {
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name: mappedName },
        value: this.createAttributeValue(processedValue)
      };
    }
    
    // 插值表达式处理
    if (value.includes('{{')) {
      return {
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name: mappedName },
        value: {
          type: 'JSXExpressionContainer',
          expression: this.parseTemplateString(value, context)
        }
      };
    }
    
    // 普通字符串值
    return {
      type: 'JSXAttribute',
      name: { type: 'JSXIdentifier', name: mappedName },
      value: { type: 'Literal', value }
    };
  }

  transformEventAttribute(eventName, handlerExpression, context) {
    const eventConfig = EVENT_DIRECTIVE_MAPPING[eventName];
    if (!eventConfig) {
      console.warn(`Unknown event: ${eventName}`);
      return null;
    }
    
    return {
      type: 'JSXAttribute',
      name: { type: 'JSXIdentifier', name: eventConfig.reactEvent },
      value: {
        type: 'JSXExpressionContainer',
        expression: {
          type: 'ArrowFunctionExpression',
          params: [{ type: 'Identifier', name: 'e' }],
          body: {
            type: 'BlockStatement',
            body: [
              ...(eventConfig.stopPropagation ? [{
                type: 'ExpressionStatement',
                expression: {
                  type: 'CallExpression',
                  callee: {
                    type: 'MemberExpression',
                    object: { type: 'Identifier', name: 'e' },
                    property: { type: 'Identifier', name: 'stopPropagation' }
                  },
                  arguments: []
                }
              }] : []),
              {
                type: 'ExpressionStatement',
                expression: {
                  type: 'CallExpression',
                  callee: this.parseExpression(handlerExpression, context),
                  arguments: [{ type: 'Identifier', name: 'e' }]
                }
              }
            ]
          }
        }
      }
    };
  }

  transformText(node, context) {
    return {
      type: 'JSXText',
      value: node.content
    };
  }

  transformMustache(node, context) {
    return {
      type: 'JSXExpressionContainer',
      expression: this.parseExpression(node.expression, context)
    };
  }

  transformComment(node, context) {
    return {
      type: 'JSXComment',
      value: node.content
    };
  }

  // 工具方法
  hasDirectives(attributes) {
    return Object.keys(attributes).some(name => 
      name.startsWith('lx:') || name.startsWith('bind') || name.startsWith('catch:')
    );
  }

  removeDirectiveAttributes(attributes) {
    const clean = {};
    Object.entries(attributes).forEach(([name, value]) => {
      if (!name.startsWith('lx:') && !name.startsWith('bind') && !name.startsWith('catch:')) {
        clean[name] = value;
      }
    });
    return clean;
  }

  parseExpression(expression, context) {
    // 简化的表达式解析器
    // 在实际实现中需要更完整的JavaScript解析
    const trimmed = expression.trim();
    
    // 简单变量
    if (/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(trimmed)) {
      return { type: 'Identifier', name: trimmed };
    }
    
    // 属性访问
    if (trimmed.includes('.')) {
      const parts = trimmed.split('.');
      let expr = { type: 'Identifier', name: parts[0] };
      
      for (let i = 1; i < parts.length; i++) {
        expr = {
          type: 'MemberExpression',
          object: expr,
          property: { type: 'Identifier', name: parts[i] }
        };
      }
      
      return expr;
    }
    
    // 字面量
    if (/^['"].*['"]$/.test(trimmed)) {
      return { type: 'Literal', value: trimmed.slice(1, -1) };
    }
    
    if (/^\d+$/.test(trimmed)) {
      return { type: 'Literal', value: parseInt(trimmed) };
    }
    
    if (/^\d+\.\d+$/.test(trimmed)) {
      return { type: 'Literal', value: parseFloat(trimmed) };
    }
    
    if (trimmed === 'true' || trimmed === 'false') {
      return { type: 'Literal', value: trimmed === 'true' };
    }
    
    // 默认返回标识符
    return { type: 'Identifier', name: trimmed };
  }

  parseForExpression(forValue) {
    // 解析 "item in list" 或 "item, index in list"
    const parts = forValue.split(' in ');
    const itemPart = parts[0].trim();
    const listExpression = parts[1].trim();
    
    let itemName, indexName;
    if (itemPart.includes(',')) {
      [itemName, indexName] = itemPart.split(',').map(s => s.trim());
    } else {
      itemName = itemPart;
      indexName = 'index';
    }
    
    return { itemName, indexName, listExpression };
  }

  parseTemplateString(template, context) {
    // 处理模板字符串中的插值表达式
    if (template.startsWith('{{') && template.endsWith('}}')) {
      return this.parseExpression(template.slice(2, -2), context);
    }
    
    // 复杂模板字符串（包含多个插值）
    const parts = [];
    let current = '';
    let inExpression = false;
    let braceCount = 0;
    
    for (let i = 0; i < template.length; i++) {
      const char = template[i];
      const nextChar = template[i + 1];
      
      if (char === '{' && nextChar === '{' && !inExpression) {
        if (current) {
          parts.push({ type: 'Literal', value: current });
          current = '';
        }
        inExpression = true;
        braceCount = 2;
        i++; // 跳过下一个{
      } else if (char === '}' && nextChar === '}' && inExpression && braceCount === 2) {
        parts.push(this.parseExpression(current, context));
        current = '';
        inExpression = false;
        braceCount = 0;
        i++; // 跳过下一个}
      } else {
        current += char;
      }
    }
    
    if (current) {
      parts.push({ type: 'Literal', value: current });
    }
    
    // 如果只有一个部分，直接返回
    if (parts.length === 1) {
      return parts[0];
    }
    
    // 多个部分需要拼接
    return {
      type: 'TemplateLiteral',
      quasis: parts.filter(p => p.type === 'Literal').map(p => ({
        type: 'TemplateElement',
        value: { raw: p.value, cooked: p.value }
      })),
      expressions: parts.filter(p => p.type !== 'Literal')
    };
  }

  createAttributeValue(value) {
    if (typeof value === 'string') {
      return { type: 'Literal', value };
    }
    
    if (typeof value === 'object' && value.type) {
      return { type: 'JSXExpressionContainer', expression: value };
    }
    
    return { type: 'JSXExpressionContainer', expression: { type: 'Literal', value } };
  }

  generateComponentId() {
    return Math.random().toString(36).substr(2, 8);
  }
}
```

## 4. 完整的浏览器端转换引擎

### 4.1 统一转换服务

```javascript
// 浏览器端完整转换引擎
class BrowserLynxTransformEngine {
  constructor(config = {}) {
    this.config = {
      rpxMode: config.rpxMode || RpxMode.VW,
      designWidth: config.designWidth || 750,
      enableCache: config.enableCache !== false,
      maxCacheSize: config.maxCacheSize || 100,
      ...config
    };
    
    this.lexer = new BrowserLexer();
    this.astBuilder = new BrowserASTBuilder();
    this.transformer = new BrowserASTTransformer(this.config);
    this.cssProcessor = new BrowserCSSProcessor(this.config);
    
    // 缓存系统
    this.cache = new Map();
    this.cacheKeys = [];
  }

  // 主转换接口
  async transform(files) {
    const { ttml, ttss, js, json } = files;
    const cacheKey = this.generateCacheKey(files);
    
    // 检查缓存
    if (this.config.enableCache && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    try {
      const result = await this.performTransform(files);
      
      // 缓存结果
      if (this.config.enableCache) {
        this.setCache(cacheKey, result);
      }
      
      return result;
    } catch (error) {
      throw new Error(`Transform failed: ${error.message}`);
    }
  }

  async performTransform(files) {
    const startTime = performance.now();
    
    // 1. 解析配置
    const config = this.parseConfig(files.json);
    
    // 2. 转换TTML
    const jsxResult = await this.transformTTML(files.ttml, config);
    
    // 3. 转换TTSS
    const cssResult = await this.transformTTSS(files.ttss, config);
    
    // 4. 处理JavaScript
    const jsResult = await this.transformJS(files.js, config);
    
    // 5. 生成完整HTML
    const html = this.generateHTML(jsxResult, cssResult, jsResult, config);
    
    const endTime = performance.now();
    
    return {
      success: true,
      html,
      jsx: jsxResult.code,
      css: cssResult.code,
      js: jsResult.code,
      metadata: {
        transformTime: endTime - startTime,
        componentId: jsxResult.componentId,
        cssClasses: cssResult.classes,
        jsxElements: jsxResult.elementCount,
        cssRules: cssResult.ruleCount
      }
    };
  }

  async transformTTML(ttml, config) {
    if (!ttml || !ttml.trim()) {
      throw new Error('TTML content is empty');
    }
    
    // 1. 词法分析
    const tokens = this.lexer.tokenize(ttml);
    
    // 2. 构建AST
    const ast = this.astBuilder.build(tokens);
    
    // 3. 转换为JSX
    const jsx = this.transformer.transform(ast);
    
    // 4. 生成代码
    const code = this.generateJSXCode(jsx);
    
    return {
      code,
      ast: jsx,
      componentId: this.transformer.config.componentId,
      elementCount: this.countElements(jsx)
    };
  }

  async transformTTSS(ttss, config) {
    if (!ttss || !ttss.trim()) {
      return {
        code: '',
        scopedCode: '',
        classes: [],
        ruleCount: 0
      };
    }
    
    const componentId = this.transformer.config.componentId;
    
    // 1. RPX转换
    const rpxConverted = convertRpxUnits(ttss, this.config);
    
    // 2. 作用域化
    const scopedCSS = addSelectorScope(rpxConverted, componentId);
    
    // 3. CSS-in-JS生成
    const cssInJs = generateCssInJs(scopedCSS, componentId);
    
    return {
      code: rpxConverted,
      scopedCode: scopedCSS,
      cssInJs: cssInJs.cssObject,
      classes: cssInJs.classNames,
      ruleCount: cssInJs.classNames.length
    };
  }

  async transformJS(js, config) {
    if (!js || !js.trim()) {
      return {
        code: this.getDefaultJS(),
        hasUserCode: false
      };
    }
    
    // 简单的JS转换（可根据需要扩展）
    let processedJS = js;
    
    // Card语法转换
    processedJS = processedJS.replace(/Card\s*\(\s*\{/, 'const component = {');
    
    // this绑定转换
    processedJS = processedJS.replace(/this\.setData/g, 'component.setData');
    processedJS = processedJS.replace(/this\.data/g, 'component.data');
    
    return {
      code: processedJS,
      hasUserCode: true
    };
  }

  generateHTML(jsxResult, cssResult, jsResult, config) {
    const { componentId } = jsxResult;
    
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lynx Web Preview</title>
    <style>
        /* 基础重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            padding: 16px;
        }
        
        /* Lynx基础样式 */
        .lynx-view {
            display: flex;
            flex-direction: column;
        }
        
        .lynx-text {
            display: inline-block;
        }
        
        .lynx-image {
            max-width: 100%;
            height: auto;
        }
        
        .lynx-scroll-view {
            overflow: auto;
        }
        
        /* 用户样式 */
        ${cssResult.scopedCode}
    </style>
    
    <!-- React 库 -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
</head>
<body>
    <div id="root"></div>
    
    <script>
        // 基础组件运行时
        const { useState, useEffect, useCallback, useMemo } = React;
        
        // 组件状态管理
        const component = {
            data: {},
            setData: function(newData, callback) {
                Object.assign(this.data, newData);
                console.log('setData called:', newData);
                if (callback) callback();
                // 触发重新渲染
                if (this._forceUpdate) this._forceUpdate();
            }
        };
        
        // 主应用组件
        function LynxApp() {
            const [forceUpdateCount, setForceUpdateCount] = useState(0);
            
            // 绑定强制更新方法
            component._forceUpdate = useCallback(() => {
                setForceUpdateCount(count => count + 1);
            }, []);
            
            // 渲染JSX
            return React.createElement(React.Fragment, null, ${jsxResult.code});
        }
        
        // 用户脚本
        try {
            ${jsResult.code}
        } catch (error) {
            console.warn('User script error:', error);
        }
        
        // 渲染应用
        ReactDOM.render(
            React.createElement(LynxApp),
            document.getElementById('root')
        );
        
        // 生命周期模拟
        setTimeout(() => {
            if (typeof onLoad === 'function') onLoad();
            if (typeof onShow === 'function') onShow();
        }, 100);
        
        console.log('Lynx Web Preview loaded successfully');
        console.log('Component ID:', '${componentId}');
        console.log('Transform metadata:', ${JSON.stringify(jsxResult.metadata || {})});
    </script>
</body>
</html>`;
  }

  generateJSXCode(jsx) {
    // 简化的JSX代码生成
    return this.jsxToString(jsx);
  }

  jsxToString(node) {
    if (!node) return '';
    
    switch (node.type) {
      case 'JSXFragment':
        return node.children.map(child => this.jsxToString(child)).join('');
        
      case 'JSXElement':
        const tagName = node.openingElement.name.name;
        const attributes = this.attributesToString(node.openingElement.attributes);
        
        if (node.openingElement.selfClosing) {
          return `React.createElement('${tagName}', ${attributes})`;
        }
        
        const children = node.children.map(child => this.jsxToString(child)).join(', ');
        return `React.createElement('${tagName}', ${attributes}${children ? ', ' + children : ''})`;
        
      case 'JSXText':
        return `"${node.value.replace(/"/g, '\\"')}"`;
        
      case 'JSXExpressionContainer':
        return this.expressionToString(node.expression);
        
      case 'JSXComment':
        return '/* ' + node.value + ' */';
        
      default:
        return '';
    }
  }

  attributesToString(attributes) {
    if (!attributes || attributes.length === 0) {
      return 'null';
    }
    
    const props = attributes.map(attr => {
      const key = attr.name.name;
      let value;
      
      if (attr.value.type === 'Literal') {
        value = JSON.stringify(attr.value.value);
      } else if (attr.value.type === 'JSXExpressionContainer') {
        value = this.expressionToString(attr.value.expression);
      } else {
        value = 'null';
      }
      
      return `"${key}": ${value}`;
    });
    
    return `{${props.join(', ')}}`;
  }

  expressionToString(expression) {
    switch (expression.type) {
      case 'Identifier':
        return expression.name;
      case 'Literal':
        return JSON.stringify(expression.value);
      case 'MemberExpression':
        return `${this.expressionToString(expression.object)}.${expression.property.name}`;
      case 'CallExpression':
        const args = expression.arguments.map(arg => this.expressionToString(arg)).join(', ');
        return `${this.expressionToString(expression.callee)}(${args})`;
      case 'ArrowFunctionExpression':
        const params = expression.params.map(p => p.name).join(', ');
        return `(${params}) => ${this.expressionToString(expression.body)}`;
      case 'LogicalExpression':
        return `${this.expressionToString(expression.left)} ${expression.operator} ${this.expressionToString(expression.right)}`;
      default:
        return 'null';
    }
  }

  parseConfig(configJson) {
    try {
      return configJson ? JSON.parse(configJson) : {};
    } catch (error) {
      console.warn('Invalid config JSON:', error);
      return {};
    }
  }

  countElements(jsx) {
    let count = 0;
    
    const traverse = (node) => {
      if (!node) return;
      
      if (node.type === 'JSXElement') {
        count++;
      }
      
      if (node.children) {
        node.children.forEach(traverse);
      }
    };
    
    traverse(jsx);
    return count;
  }

  getDefaultJS() {
    return `
      console.log('Lynx preview initialized');
      
      // 模拟常用生命周期
      const onLoad = () => console.log('onLoad called');
      const onShow = () => console.log('onShow called');
    `;
  }

  // 缓存管理
  generateCacheKey(files) {
    const content = JSON.stringify(files);
    return this.simpleHash(content);
  }

  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(36);
  }

  setCache(key, value) {
    if (this.cache.size >= this.config.maxCacheSize) {
      // LRU淘汰
      const oldestKey = this.cacheKeys.shift();
      this.cache.delete(oldestKey);
    }
    
    this.cache.set(key, value);
    this.cacheKeys.push(key);
  }

  clearCache() {
    this.cache.clear();
    this.cacheKeys = [];
  }
}
```

### 4.2 CSS处理器

```javascript
// 浏览器端CSS处理器
class BrowserCSSProcessor {
  constructor(config = {}) {
    this.config = config;
  }

  process(css) {
    // 实现CSS处理逻辑
    return css;
  }
}
```

## 5. 升级实施方案

### 5.1 渐进式升级策略

#### 5.1.1 Phase 1: 核心引擎替换 (Week 1-2)
```typescript
// 1. 创建新的转换服务
class WebSpeedyBrowserService extends WebPreviewService {
  constructor(config) {
    super(config);
    this.transformEngine = new BrowserLynxTransformEngine(config);
  }

  async convertToWebPreview(content, resultId, options) {
    try {
      // 使用新引擎
      const files = this.parseFileStructure(content);
      const result = await this.transformEngine.transform(files);
      
      // 生成截图（复用现有逻辑）
      const screenshot = await this.generateScreenshot(result.html);
      
      return {
        success: true,
        html: result.html,
        screenshot,
        metadata: result.metadata
      };
    } catch (error) {
      // 降级到旧实现
      return super.convertToWebPreview(content, resultId, options);
    }
  }
}
```

#### 5.1.2 Phase 2: Web Worker升级 (Week 3-4)
```javascript
// 升级Web Worker使用新引擎
// src/workers/lynx-runtime-bridge.js
importScripts('/js/browser-lynx-transform-engine.js');

class LynxRuntimeBridge {
  constructor() {
    this.engine = new BrowserLynxTransformEngine({
      rpxMode: 'vw',
      designWidth: 750,
      enableCache: true
    });
  }

  async transform(content, options) {
    const files = this.parseContent(content);
    return await this.engine.transform(files);
  }
}

const bridge = new LynxRuntimeBridge();

self.onmessage = async function(event) {
  const { type, content, resultId, options } = event.data;
  
  if (type === 'TRANSFORM') {
    try {
      const result = await bridge.transform(content, options);
      self.postMessage({
        success: true,
        ...result,
        resultId
      });
    } catch (error) {
      self.postMessage({
        success: false,
        error: error.name,
        message: error.message,
        resultId
      });
    }
  }
};
```

#### 5.1.3 Phase 3: UI组件适配 (Week 5)
```typescript
// 保持现有UI组件接口不变
export const WebPreviewButton: React.FC<WebPreviewButtonProps> = (props) => {
  const [service] = useState(() => 
    new WebSpeedyBrowserService({
      rpxMode: 'vw',
      designWidth: 750,
      enableOptimization: true
    })
  );
  
  // 其他逻辑保持不变
};
```

### 5.2 质量保证策略

#### 5.2.1 A/B测试框架
```typescript
// 新旧引擎对比测试
class TransformationTester {
  constructor() {
    this.oldEngine = new LynxConverter();
    this.newEngine = new BrowserLynxTransformEngine();
  }

  async compareTransforms(content) {
    const [oldResult, newResult] = await Promise.all([
      this.oldEngine.convert(content),
      this.newEngine.transform(this.parseContent(content))
    ]);

    return {
      visual: await this.compareVisual(oldResult.html, newResult.html),
      performance: this.comparePerformance(oldResult, newResult),
      features: this.compareFeatures(oldResult, newResult)
    };
  }
}
```

#### 5.2.2 回归测试集
```typescript
// 测试用例集合
const TEST_CASES = [
  {
    name: '基础元素',
    ttml: '<view><text>Hello World</text></view>',
    expected: { elements: ['div', 'span'], text: 'Hello World' }
  },
  {
    name: '条件渲染',
    ttml: '<view lx:if="{{visible}}"><text>Conditional</text></view>',
    expected: { hasConditional: true }
  },
  {
    name: '列表渲染',
    ttml: '<view lx:for="item in list"><text>{{item}}</text></view>',
    expected: { hasIteration: true }
  },
  {
    name: '事件绑定',
    ttml: '<button catch:tap="handleTap">Button</button>',
    expected: { hasEventHandler: true }
  },
  {
    name: '样式转换',
    ttss: '.container { width: 750rpx; height: 100vh; }',
    expected: { hasRpxConversion: true }
  }
];
```

### 5.3 性能优化策略

#### 5.3.1 内存管理
```javascript
class MemoryOptimizedEngine extends BrowserLynxTransformEngine {
  constructor(config) {
    super(config);
    this.setupMemoryManagement();
  }

  setupMemoryManagement() {
    // 定期清理缓存
    setInterval(() => {
      if (this.cache.size > 50) {
        this.clearOldCache();
      }
    }, 30000);

    // 监听内存压力
    if ('memory' in performance) {
      this.monitorMemoryUsage();
    }
  }

  clearOldCache() {
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5分钟

    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > maxAge) {
        this.cache.delete(key);
        const index = this.cacheKeys.indexOf(key);
        if (index > -1) this.cacheKeys.splice(index, 1);
      }
    }
  }

  monitorMemoryUsage() {
    const checkMemory = () => {
      const { usedJSHeapSize, totalJSHeapSize } = performance.memory;
      const usage = usedJSHeapSize / totalJSHeapSize;

      if (usage > 0.9) {
        console.warn('High memory usage detected, clearing cache');
        this.clearCache();
      }
    };

    setInterval(checkMemory, 10000);
  }
}
```

#### 5.3.2 异步优化
```javascript
// 使用Web Worker Pool
class WorkerPool {
  constructor(config) {
    this.maxWorkers = config.maxWorkers || 4;
    this.workers = [];
    this.queue = [];
    this.busy = new Set();
  }

  async execute(task) {
    return new Promise((resolve, reject) => {
      this.queue.push({ task, resolve, reject });
      this.processQueue();
    });
  }

  processQueue() {
    if (this.queue.length === 0) return;
    
    const availableWorker = this.workers.find(w => !this.busy.has(w));
    
    if (availableWorker) {
      const { task, resolve, reject } = this.queue.shift();
      this.executeTask(availableWorker, task, resolve, reject);
    } else if (this.workers.length < this.maxWorkers) {
      this.createWorker();
      this.processQueue();
    }
  }

  createWorker() {
    const worker = new Worker('/workers/lynx-runtime-bridge.js');
    this.workers.push(worker);
    
    worker.onmessage = (event) => {
      this.busy.delete(worker);
      this.processQueue();
    };
  }

  executeTask(worker, task, resolve, reject) {
    this.busy.add(worker);
    
    const timeout = setTimeout(() => {
      reject(new Error('Worker timeout'));
      this.busy.delete(worker);
    }, 10000);

    const handleMessage = (event) => {
      clearTimeout(timeout);
      worker.removeEventListener('message', handleMessage);
      resolve(event.data);
    };

    worker.addEventListener('message', handleMessage);
    worker.postMessage(task);
  }
}
```

## 6. 测试与验证

### 6.1 功能验证测试
```javascript
// 完整的功能测试套件
describe('BrowserLynxTransformEngine', () => {
  let engine;

  beforeEach(() => {
    engine = new BrowserLynxTransformEngine();
  });

  describe('元素转换', () => {
    test('基础元素映射', async () => {
      const files = {
        ttml: '<view><text>Hello</text><image src="test.jpg" /></view>'
      };
      
      const result = await engine.transform(files);
      
      expect(result.success).toBe(true);
      expect(result.html).toContain('div');
      expect(result.html).toContain('span');
      expect(result.html).toContain('img');
    });

    test('自定义组件', async () => {
      const files = {
        ttml: '<my-component prop="value" />',
        json: JSON.stringify({
          usingComponents: {
            'my-component': './components/MyComponent'
          }
        })
      };
      
      const result = await engine.transform(files);
      expect(result.html).toContain('MyComponent');
    });
  });

  describe('指令转换', () => {
    test('条件渲染', async () => {
      const files = {
        ttml: '<view lx:if="{{visible}}">Content</view>'
      };
      
      const result = await engine.transform(files);
      expect(result.html).toContain('visible &&');
    });

    test('列表渲染', async () => {
      const files = {
        ttml: '<view lx:for="item in items">{{item}}</view>'
      };
      
      const result = await engine.transform(files);
      expect(result.html).toContain('.map');
    });

    test('事件绑定', async () => {
      const files = {
        ttml: '<button catch:tap="handleTap">Click</button>'
      };
      
      const result = await engine.transform(files);
      expect(result.html).toContain('onClick');
      expect(result.html).toContain('stopPropagation');
    });
  });

  describe('样式转换', () => {
    test('RPX转换', async () => {
      const files = {
        ttss: '.container { width: 750rpx; height: 100vh; }'
      };
      
      const result = await engine.transform(files);
      expect(result.css).toContain('100vw');
      expect(result.css).not.toContain('rpx');
    });

    test('作用域化', async () => {
      const files = {
        ttss: '.my-class { color: red; }'
      };
      
      const result = await engine.transform(files);
      expect(result.css).toContain('[data-v-');
    });
  });

  describe('性能测试', () => {
    test('大文件转换性能', async () => {
      const largeContent = generateLargeContent();
      
      const startTime = performance.now();
      const result = await engine.transform({ ttml: largeContent });
      const duration = performance.now() - startTime;
      
      expect(result.success).toBe(true);
      expect(duration).toBeLessThan(5000); // 5秒内
    });

    test('并发转换', async () => {
      const promises = Array(10).fill().map(() => 
        engine.transform({ ttml: '<view><text>Test</text></view>' })
      );
      
      const results = await Promise.all(promises);
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });
});
```

### 6.2 视觉回归测试
```javascript
// 视觉对比测试
class VisualRegressionTester {
  constructor() {
    this.oldConverter = new LynxConverter();
    this.newEngine = new BrowserLynxTransformEngine();
  }

  async compareVisualOutput(testCases) {
    const results = [];
    
    for (const testCase of testCases) {
      const oldHtml = await this.oldConverter.convert(testCase.content);
      const newResult = await this.newEngine.transform(testCase.files);
      
      const similarity = await this.calculateSimilarity(
        oldHtml.html, 
        newResult.html
      );
      
      results.push({
        name: testCase.name,
        similarity,
        passed: similarity > 0.95
      });
    }
    
    return results;
  }

  async calculateSimilarity(html1, html2) {
    // 使用dom-compare或其他库进行DOM结构对比
    const dom1 = this.parseHtml(html1);
    const dom2 = this.parseHtml(html2);
    
    return this.compareDomTrees(dom1, dom2);
  }
}
```

## 7. 部署与监控

### 7.1 分阶段部署
```typescript
// 特性开关控制
class FeatureToggle {
  constructor() {
    this.flags = {
      useNewEngine: this.getFlag('NEW_LYNX_ENGINE', 0.1), // 10%用户
      enableAdvancedFeatures: this.getFlag('ADVANCED_FEATURES', 0.05),
      useWorkerPool: this.getFlag('WORKER_POOL', 0.2)
    };
  }

  getFlag(name, defaultPercentage) {
    const stored = localStorage.getItem(`flag_${name}`);
    if (stored !== null) return parseFloat(stored);
    
    const userId = this.getUserId();
    const hash = this.simpleHash(userId + name);
    return (hash % 100) / 100 < defaultPercentage;
  }

  shouldUseNewEngine() {
    return this.flags.useNewEngine;
  }
}

// 服务选择器
class TransformServiceSelector {
  constructor() {
    this.toggle = new FeatureToggle();
  }

  createService(config) {
    if (this.toggle.shouldUseNewEngine()) {
      return new WebSpeedyBrowserService(config);
    } else {
      return new WebPreviewService(config);
    }
  }
}
```

### 7.2 监控指标
```typescript
// 性能监控
class TransformMetrics {
  constructor() {
    this.metrics = {
      transformTime: [],
      successRate: 0,
      errorRate: 0,
      cacheHitRate: 0
    };
  }

  recordTransform(duration, success, fromCache) {
    this.metrics.transformTime.push(duration);
    
    if (success) {
      this.metrics.successRate++;
    } else {
      this.metrics.errorRate++;
    }
    
    if (fromCache) {
      this.metrics.cacheHitRate++;
    }
    
    // 定期上报
    if (this.metrics.transformTime.length % 100 === 0) {
      this.reportMetrics();
    }
  }

  reportMetrics() {
    const avgTime = this.metrics.transformTime.reduce((a, b) => a + b, 0) / 
                   this.metrics.transformTime.length;
    
    console.log('Transform Metrics:', {
      averageTime: avgTime,
      successRate: this.metrics.successRate / this.metrics.transformTime.length,
      errorRate: this.metrics.errorRate / this.metrics.transformTime.length,
      cacheHitRate: this.metrics.cacheHitRate / this.metrics.transformTime.length
    });
  }
}
```

## 8. 总结

### 8.1 技术价值
- **完整性**: 提取了100%的web-speedy-plugin转换规则
- **兼容性**: 保持现有API接口完全兼容
- **性能**: 优化的浏览器端实现，支持缓存和并发
- **可维护性**: 模块化设计，易于扩展和维护

### 8.2 实施路径
1. **渐进式升级**: 分阶段替换核心组件
2. **风险控制**: A/B测试和特性开关
3. **质量保证**: 完整的测试和监控体系
4. **性能优化**: 内存管理和异步处理

### 8.3 预期收益
- **转换质量**: 从30%覆盖率提升到100%
- **开发效率**: 减少维护成本，提升开发体验
- **用户体验**: 更准确的预览效果，更快的转换速度
- **技术债务**: 彻底解决自定义实现的维护负担

该方案为search-so-ai的lynx2web功能提供了完整的浏览器端AST转换升级路径，确保在保持现有功能的基础上，实现企业级的转换质量和性能。

---

**文档版本**: v1.0  
**编写日期**: 2025-06-24  
**技术负责人**: Claude Code Development Team  
**评审状态**: 待评审