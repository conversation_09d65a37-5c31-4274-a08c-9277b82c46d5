/**
 * 截图服务
 * 负责生成Web预览截图并上传到CDN
 */

import type { ScreenshotData } from '../types';

// 动态导入html2canvas
let html2canvas: any = null;

export interface ScreenshotOptions {
  width?: number;
  height?: number;
  format?: string;
  quality?: number;
  filename?: string;
  scale?: number;
}

export class ScreenshotService {
  private uploadService: any = null;
  private isHtml2CanvasLoaded = false;

  constructor() {
    this.loadDependencies();
  }

  /**
   * 加载依赖
   */
  private async loadDependencies() {
    console.log('📦 [ScreenshotService.loadDependencies] 开始加载依赖');

    try {
      // 动态加载html2canvas
      if (!html2canvas) {
        console.log('📦 [ScreenshotService.loadDependencies] 加载html2canvas');
        console.time('⏱️ html2canvas加载时间');

        try {
          const module = await import('html2canvas');
          html2canvas = module.default || module;
          this.isHtml2CanvasLoaded = true;

          console.timeEnd('⏱️ html2canvas加载时间');
          console.log(
            '✅ [ScreenshotService.loadDependencies] html2canvas加载成功',
          );
        } catch (importError) {
          console.warn(
            '⚠️ [ScreenshotService.loadDependencies] html2canvas加载失败:',
            {
              error: importError,
              message:
                importError instanceof Error
                  ? importError.message
                  : 'Unknown import error',
            },
          );
          this.isHtml2CanvasLoaded = false;
        }
      } else {
        console.log('✓ [ScreenshotService.loadDependencies] html2canvas已存在');
        this.isHtml2CanvasLoaded = true;
      }

      // 动态加载UploadService
      if (!this.uploadService) {
        console.log(
          '📦 [ScreenshotService.loadDependencies] 加载UploadService',
        );
        console.time('⏱️ UploadService加载时间');

        try {
          console.log(
            '🔍 [ScreenshotService] 尝试导入路径: ../../services/UploadService',
          );
          const uploadModule = await import('../../services/UploadService');
          console.log('📦 [ScreenshotService] 导入模块:', uploadModule);

          const { UploadService } = uploadModule;
          if (!UploadService) {
            throw new Error('UploadService class not found in module');
          }

          console.log('🏗️ [ScreenshotService] 创建UploadService实例');
          this.uploadService = new UploadService();
          console.log(
            '✅ [ScreenshotService] UploadService实例创建成功:',
            this.uploadService,
          );

          console.timeEnd('⏱️ UploadService加载时间');
          console.log(
            '✅ [ScreenshotService.loadDependencies] UploadService加载成功',
          );
        } catch (uploadError) {
          console.error(
            '💥 [ScreenshotService.loadDependencies] UploadService加载失败:',
            {
              error: uploadError,
              message:
                uploadError instanceof Error
                  ? uploadError.message
                  : 'Unknown error',
              stack:
                uploadError instanceof Error ? uploadError.stack : undefined,
              importPath: '../../services/UploadService',
            },
          );
          // 将 uploadService 设置为 null 以便后续处理知道它不可用
          this.uploadService = null;
        }
      } else {
        console.log(
          '✓ [ScreenshotService.loadDependencies] UploadService已存在',
        );
      }

      console.log('🎉 [ScreenshotService.loadDependencies] 所有依赖加载完成');
    } catch (error) {
      console.error('💥 [ScreenshotService.loadDependencies] 依赖加载失败:', {
        error,
        errorType: typeof error,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * 截图HTML内容并上传到CDN
   */
  async captureHTML(
    html: string,
    options: ScreenshotOptions = {},
  ): Promise<ScreenshotData | null> {
    console.group('📸 [ScreenshotService.captureHTML] 开始截图流程');
    console.log('📋 截图参数:', {
      htmlLength: html?.length || 0,
      options: {
        width: options.width || 375,
        height: options.height || 667,
        format: options.format || 'jpeg',
        quality: options.quality || 0.8,
        filename: options.filename,
      },
    });
    console.time('⏱️ 总截图时间');

    try {
      // 确保依赖已加载
      console.log('📦 [ScreenshotService.captureHTML] 检查依赖');
      await this.loadDependencies();

      console.log('🔍 [ScreenshotService.captureHTML] 依赖状态:', {
        html2canvasLoaded: this.isHtml2CanvasLoaded,
        uploadServiceAvailable: !!this.uploadService,
      });

      if (!this.isHtml2CanvasLoaded) {
        console.warn(
          '⚠️ [ScreenshotService.captureHTML] html2canvas不可用，跳过截图生成',
        );
        console.groupEnd();
        return null;
      }

      // 创建临时iframe
      console.log('🔧 [ScreenshotService.captureHTML] 创建临时iframe');
      console.time('⏱️ iframe创建时间');
      const iframe = await this.createTemporaryIframe(html, options);
      console.timeEnd('⏱️ iframe创建时间');
      console.log('✅ [ScreenshotService.captureHTML] iframe创建成功');

      try {
        // 等待内容加载
        console.log('⏳ [ScreenshotService.captureHTML] 等待iframe加载');
        console.time('⏱️ iframe加载时间');
        await this.waitForIframeLoad(iframe);
        console.timeEnd('⏱️ iframe加载时间');
        console.log('✅ [ScreenshotService.captureHTML] iframe加载完成');

        // 生成截图
        console.log('📷 [ScreenshotService.captureHTML] 开始截图');
        console.time('⏱️ html2canvas执行时间');
        const canvas = await this.captureIframeContent(iframe, options);
        console.timeEnd('⏱️ html2canvas执行时间');
        console.log('✅ [ScreenshotService.captureHTML] 截图生成成功');
        console.log('📋 Canvas信息:', {
          width: canvas.width,
          height: canvas.height,
          hasContext: !!canvas.getContext('2d'),
        });

        // 转换为Blob
        console.log('📦 [ScreenshotService.captureHTML] 转换为Blob');
        console.time('⏱️ Canvas转Blob时间');
        const blob = await this.canvasToBlob(canvas, options);
        console.timeEnd('⏱️ Canvas转Blob时间');
        console.log('✅ [ScreenshotService.captureHTML] Blob转换成功');
        console.log('📋 Blob信息:', {
          size: blob.size,
          type: blob.type,
          sizeKB: `${(blob.size / 1024).toFixed(2)}KB`,
        });

        // 上传到CDN
        console.log('☁️ [ScreenshotService.captureHTML] 上传到CDN');
        console.time('⏱️ CDN上传时间');
        const uploadResult = await this.uploadToCDN(blob, options.filename);
        console.timeEnd('⏱️ CDN上传时间');

        if (uploadResult) {
          console.log('✅ [ScreenshotService.captureHTML] CDN上传成功');
          console.log('🔗 上传结果:', uploadResult);
        } else {
          console.warn('⚠️ [ScreenshotService.captureHTML] CDN上传失败或跳过');
        }

        const finalResult = {
          url: uploadResult?.url || null,
          width: canvas.width,
          height: canvas.height,
          format: options.format || 'jpeg',
          size: blob.size,
        };

        console.log('🎉 [ScreenshotService.captureHTML] 截图流程完成');
        console.log('📊 最终结果:', finalResult);
        console.timeEnd('⏱️ 总截图时间');
        console.groupEnd();

        return finalResult;
      } finally {
        // 清理临时iframe
        console.log('🧹 [ScreenshotService.captureHTML] 清理iframe');
        this.cleanupIframe(iframe);
      }
    } catch (error) {
      console.error('💥 [ScreenshotService.captureHTML] 截图失败:', {
        error,
        errorType: typeof error,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      });

      console.timeEnd('⏱️ 总截图时间');
      console.groupEnd();
      return null;
    }
  }

  /**
   * 创建临时iframe
   */
  private async createTemporaryIframe(
    html: string,
    options: ScreenshotOptions,
  ): Promise<HTMLIFrameElement> {
    const iframe = document.createElement('iframe');

    // 设置iframe样式（隐藏但保持渲染）
    iframe.style.cssText = `
      position: absolute;
      top: -9999px;
      left: -9999px;
      width: ${options.width || 375}px;
      height: ${options.height || 667}px;
      border: none;
      background: white;
      opacity: 0;
      pointer-events: none;
    `;

    // 设置安全属性
    iframe.sandbox.add('allow-scripts', 'allow-same-origin');

    // 注入HTML内容
    iframe.srcdoc = html;

    // 添加到DOM
    document.body.appendChild(iframe);

    return iframe;
  }

  /**
   * 等待iframe加载完成
   */
  private waitForIframeLoad(iframe: HTMLIFrameElement): Promise<void> {
    return new Promise(resolve => {
      const timeout = setTimeout(() => {
        resolve(); // 超时也继续执行
      }, 3000); // 3秒超时

      const handleLoad = () => {
        clearTimeout(timeout);
        iframe.removeEventListener('load', handleLoad);

        // 额外等待一点时间确保渲染完成
        setTimeout(resolve, 500);
      };

      if (iframe.contentDocument?.readyState === 'complete') {
        handleLoad();
      } else {
        iframe.addEventListener('load', handleLoad);
      }
    });
  }

  /**
   * 截图iframe内容
   */
  private async captureIframeContent(
    iframe: HTMLIFrameElement,
    options: ScreenshotOptions,
  ): Promise<HTMLCanvasElement> {
    const iframeDocument = iframe.contentDocument;
    if (!iframeDocument) {
      throw new Error('Cannot access iframe document');
    }

    const targetElement = iframeDocument.body || iframeDocument.documentElement;

    // html2canvas配置
    const html2canvasOptions = {
      width: options.width || 375,
      height: options.height || 667,
      scale: options.scale || window.devicePixelRatio || 1,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      logging: false,
      removeContainer: true,
      foreignObjectRendering: true,
      imageTimeout: 1000,
      onclone: (clonedDoc: Document) => {
        // 确保克隆文档的样式正确
        const clonedBody = clonedDoc.body;
        if (clonedBody) {
          clonedBody.style.width = `${options.width || 375}px`;
          clonedBody.style.height = `${options.height || 667}px`;
          clonedBody.style.overflow = 'hidden';
        }
      },
    };

    return html2canvas(targetElement, html2canvasOptions);
  }

  /**
   * Canvas转Blob
   */
  private canvasToBlob(
    canvas: HTMLCanvasElement,
    options: ScreenshotOptions,
  ): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const format = options.format === 'png' ? 'image/png' : 'image/jpeg';
      const quality = options.quality || 0.8;

      canvas.toBlob(
        blob => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to convert canvas to blob'));
          }
        },
        format,
        quality,
      );
    });
  }

  /**
   * 上传到CDN
   */
  private async uploadToCDN(
    blob: Blob,
    filename?: string,
  ): Promise<{ url: string } | null> {
    if (!this.uploadService) {
      console.warn(
        '⚠️ [ScreenshotService.uploadToCDN] UploadService不可用，跳过截图上传',
      );
      return null;
    }

    try {
      // 使用现有的UploadService上传
      const result = await this.uploadService.uploadFile(
        blob,
        filename || `screenshot-${Date.now()}.jpg`,
      );

      return {
        url: result.url || result.cdnUrl || result.path,
      };
    } catch (error) {
      console.error(
        '💥 [ScreenshotService.uploadToCDN] 上传失败:',
        error instanceof Error ? error.message : 'Unknown error',
      );
      return null;
    }
  }

  /**
   * 清理临时iframe
   */
  private cleanupIframe(iframe: HTMLIFrameElement): void {
    try {
      if (iframe.parentNode) {
        iframe.parentNode.removeChild(iframe);
      }
    } catch (error) {
      console.warn('[ScreenshotService] Failed to cleanup iframe:', error);
    }
  }

  /**
   * 直接截图已存在的元素
   */
  async captureElement(
    element: HTMLElement,
    options: ScreenshotOptions = {},
  ): Promise<ScreenshotData | null> {
    try {
      await this.loadDependencies();

      if (!this.isHtml2CanvasLoaded) {
        throw new Error('html2canvas not available');
      }

      const canvas = await html2canvas(element, {
        width: options.width,
        height: options.height,
        scale: options.scale || 1,
        useCORS: true,
        allowTaint: true,
        logging: false,
      });

      const blob = await this.canvasToBlob(canvas, options);
      const uploadResult = await this.uploadToCDN(blob, options.filename);

      return {
        url: uploadResult?.url || null,
        width: canvas.width,
        height: canvas.height,
        format: options.format || 'jpeg',
        size: blob.size,
      };
    } catch (error) {
      console.error('[ScreenshotService] Element capture failed:', error);
      return null;
    }
  }

  /**
   * 检查服务可用性
   */
  async isAvailable(): Promise<boolean> {
    try {
      await this.loadDependencies();
      return this.isHtml2CanvasLoaded && !!this.uploadService;
    } catch {
      return false;
    }
  }

  /**
   * 检查依赖加载状态
   */
  getDependencyStatus(): {
    html2canvasLoaded: boolean;
    uploadServiceLoaded: boolean;
    allDependenciesLoaded: boolean;
  } {
    return {
      html2canvasLoaded: this.isHtml2CanvasLoaded,
      uploadServiceLoaded: !!this.uploadService,
      allDependenciesLoaded: this.isHtml2CanvasLoaded && !!this.uploadService,
    };
  }

  /**
   * 清理资源
   */
  dispose(): void {
    // 清理相关资源
    this.uploadService = null;
  }
}
