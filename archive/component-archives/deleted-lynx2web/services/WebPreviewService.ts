/**
 * Web预览服务
 * 管理Lynx到Web的转换流程
 */

import type {
  PreviewResult,
  ConversionOptions,
  WorkerMessage,
  WorkerResponse,
  WebPreviewConfig,
} from '../types';
import { LynxValidator } from '../utils/lynxValidator';
import { ScreenshotService } from './ScreenshotService';

export class WebPreviewService {
  private worker: Worker | null = null;
  private screenshotService: ScreenshotService;
  private config: WebPreviewConfig;
  private workerBlobUrl: string | null = null;

  constructor(config?: Partial<WebPreviewConfig>) {
    console.log('🏗️ [WebPreviewService] 构造函数调用');

    this.config = {
      workerPath: '', // Force empty path to ensure inline worker is used
      defaultTimeout: 10000,
      thumbnailSize: {
        width: 375,
        height: 667,
      },
      screenshotOptions: {
        format: 'jpeg',
        quality: 0.8,
        maxSize: 2 * 1024 * 1024, // 2MB
      },
      ...config,
    };

    console.log('⚙️ [WebPreviewService] 配置初始化完成:', {
      workerPath: this.config.workerPath,
      defaultTimeout: this.config.defaultTimeout,
      configOverrides: config,
    });

    this.screenshotService = new ScreenshotService();
  }

  /**
   * 转换Lynx内容为Web预览
   */
  async convertToWebPreview(
    content: string,
    resultId: string,
    options?: ConversionOptions,
  ): Promise<PreviewResult> {
    console.group('🔧 [WebPreviewService] 开始Lynx转Web预览');
    console.log('📋 服务配置:', this.config);
    console.log('📝 输入参数:', {
      contentLength: content?.length || 0,
      resultId,
      options,
      hasContent: !!content,
    });
    console.time('⏱️ WebPreviewService总时间');

    try {
      // 1. 验证Lynx内容
      console.log('🔍 [WebPreviewService] 开始内容验证');
      console.time('⏱️ 内容验证时间');

      const validation = this.validateContent(content);

      console.timeEnd('⏱️ 内容验证时间');
      console.log('✓ [WebPreviewService] 验证结果:', validation);

      if (!validation.isValid) {
        console.warn('⚠️ [WebPreviewService] 内容验证失败');
        console.groupEnd();
        return {
          success: false,
          error: 'VALIDATION_ERROR',
          message: validation.reason || '内容验证失败',
        };
      }

      // 2. 执行转换
      console.log('🔄 [WebPreviewService] 开始执行转换');
      console.time('⏱️ 转换执行时间');

      const conversionResult = await this.executeConversion(
        content,
        resultId,
        options,
      );

      console.timeEnd('⏱️ 转换执行时间');
      console.log('📋 [WebPreviewService] 转换结果:', {
        success: conversionResult.success,
        hasHtml: !!conversionResult.html,
        htmlLength: conversionResult.html?.length || 0,
        error: conversionResult.error,
      });

      if (!conversionResult.success) {
        console.error('❌ [WebPreviewService] 转换失败');
        console.groupEnd();
        return conversionResult;
      }

      // 3. 生成截图
      console.log('📸 [WebPreviewService] 开始生成截图');
      console.time('⏱️ 截图生成时间');

      const screenshot = await this.generateScreenshot(
        conversionResult.html!,
        resultId,
      );

      console.timeEnd('⏱️ 截图生成时间');
      console.log(
        '📸 [WebPreviewService] 截图结果:',
        screenshot
          ? {
              url: screenshot.url,
              size: `${screenshot.width}x${screenshot.height}`,
              fileSize: screenshot.size,
            }
          : '无截图',
      );

      const finalResult = {
        success: true,
        html: conversionResult.html,
        screenshot,
      };

      console.log('✅ [WebPreviewService] 转换完成');
      console.timeEnd('⏱️ WebPreviewService总时间');
      console.groupEnd();

      return finalResult;
    } catch (error) {
      console.error('💥 [WebPreviewService] 服务异常:', {
        error,
        errorType: typeof error,
        errorMessage: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined,
      });

      console.timeEnd('⏱️ WebPreviewService总时间');
      console.groupEnd();

      return {
        success: false,
        error: 'SERVICE_ERROR',
        message: error instanceof Error ? error.message : '服务异常',
      };
    }
  }

  /**
   * 验证内容
   */
  private validateContent(content: string): {
    isValid: boolean;
    reason?: string;
  } {
    console.log('🔍 [WebPreviewService.validateContent] 开始验证');
    console.log('📄 内容基本信息:', {
      hasContent: !!content,
      type: typeof content,
      length: content?.length || 0,
      preview: `${content?.substring(0, 100)}...`,
    });

    // 基础验证
    if (!content || typeof content !== 'string') {
      console.warn('⚠️ [WebPreviewService.validateContent] 基础验证失败');
      return { isValid: false, reason: '内容为空或格式无效' };
    }

    // Lynx内容检测
    console.log('🔍 [WebPreviewService.validateContent] 检测Lynx内容');
    const hasLynxContent = LynxValidator.hasLynxContent(content);
    console.log('📋 Lynx内容检测结果:', hasLynxContent);

    if (!hasLynxContent) {
      console.warn('⚠️ [WebPreviewService.validateContent] Lynx内容检测失败');
      return { isValid: false, reason: '内容中未检测到Lynx代码' };
    }

    // 详细验证
    console.log('🔍 [WebPreviewService.validateContent] 执行详细验证');
    const detailedValidation = LynxValidator.validateForConversion(content);
    console.log('📋 详细验证结果:', detailedValidation);

    return detailedValidation;
  }

  /**
   * 执行转换
   */
  private async executeConversion(
    content: string,
    resultId: string,
    options?: ConversionOptions,
  ): Promise<PreviewResult> {
    console.log('🔄 [WebPreviewService.executeConversion] 开始执行转换');
    console.log('📋 转换参数:', {
      contentLength: content.length,
      resultId,
      options,
      hasWorker: !!this.worker,
    });

    // 确保Worker存在且有效
    if (!this.worker) {
      console.log('🔧 [WebPreviewService.executeConversion] 创建新Worker');
      try {
        this.worker = this.createWorker();
        console.log('✅ [WebPreviewService.executeConversion] Worker创建成功');
      } catch (error) {
        console.error(
          '💥 [WebPreviewService.executeConversion] Worker创建失败:',
          error,
        );
        return {
          success: false,
          error: 'WORKER_CREATION_ERROR',
          message: `Worker创建失败: ${error instanceof Error ? error.message : 'Unknown error'}`,
        };
      }
    } else {
      console.log('✓ [WebPreviewService.executeConversion] 使用现有Worker');
    }

    const timeout = options?.timeout || this.config.defaultTimeout;
    console.log(
      '⏰ [WebPreviewService.executeConversion] 设置超时:',
      `${timeout}ms`,
    );

    return new Promise(resolve => {
      console.log('🚀 [WebPreviewService.executeConversion] 设置Worker通信');

      const timeoutId = setTimeout(() => {
        console.error('⏰ [WebPreviewService.executeConversion] 转换超时');
        resolve({
          success: false,
          error: 'TIMEOUT_ERROR',
          message: `转换超时 (${timeout / 1000}秒)`,
        });
      }, timeout);

      const handleMessage = (event: MessageEvent<WorkerResponse>) => {
        console.log('📨 [WebPreviewService.executeConversion] 收到Worker响应');
        console.log('📋 Worker响应数据:', {
          success: event.data.success,
          hasHtml: !!event.data.html,
          htmlLength: event.data.html?.length || 0,
          error: event.data.error,
          message: event.data.message,
          processingTime: event.data.processingTime,
          responseType: typeof event.data,
          allProperties: Object.keys(event.data),
        });

        // 忽略健康检查响应，只处理转换响应
        if (event.data.message === 'Worker健康' && !event.data.html) {
          console.log(
            '🩺 [WebPreviewService.executeConversion] 忽略健康检查响应',
          );
          return;
        }

        clearTimeout(timeoutId);
        this.worker!.removeEventListener('message', handleMessage);

        const response = event.data;

        if (response.success && response.html) {
          console.log(
            '✅ [WebPreviewService.executeConversion] Worker转换成功',
          );
          console.log(
            '📄 生成的HTML预览:',
            `${response.html.substring(0, 200)}...`,
          );

          resolve({
            success: true,
            html: response.html,
          });
        } else {
          console.error(
            '❌ [WebPreviewService.executeConversion] Worker转换失败:',
            {
              error: response.error,
              message: response.message,
              hasHtml: !!response.html,
              isHealthCheck: response.message === 'Worker健康',
            },
          );

          resolve({
            success: false,
            error: response.error || 'CONVERSION_ERROR',
            message: response.message || '转换失败',
          });
        }
      };

      const handleError = (error: ErrorEvent) => {
        console.error(
          '💥 [WebPreviewService.executeConversion] Worker错误事件:',
          {
            message: error.message,
            filename: error.filename,
            lineno: error.lineno,
            colno: error.colno,
            error: error.error,
          },
        );

        clearTimeout(timeoutId);
        this.worker!.removeEventListener('error', handleError);

        resolve({
          success: false,
          error: 'WORKER_ERROR',
          message: `Worker错误: ${error.message}`,
        });
      };

      console.log('📡 [WebPreviewService.executeConversion] 设置事件监听器');
      this.worker!.addEventListener('message', handleMessage);
      this.worker!.addEventListener('error', handleError);

      // 发送转换请求
      console.log('📤 [WebPreviewService.executeConversion] 预处理内容');
      const preprocessedContent = LynxValidator.preprocessContent(content);
      console.log('📋 预处理结果:', {
        originalLength: content.length,
        preprocessedLength: preprocessedContent.length,
        preview: `${preprocessedContent.substring(0, 200)}...`,
      });

      const message: WorkerMessage = {
        type: 'CONVERT',
        content: preprocessedContent,
        resultId,
        options,
      };

      console.log('📤 [WebPreviewService.executeConversion] 发送Worker消息:', {
        type: message.type,
        contentLength: message.content?.length || 0,
        resultId: message.resultId,
        options: message.options,
      });

      this.worker!.postMessage(message);
      console.log('✓ [WebPreviewService.executeConversion] Worker消息已发送');
    });
  }

  /**
   * 生成截图
   */
  private async generateScreenshot(html: string, resultId: string) {
    try {
      return await this.screenshotService.captureHTML(html, {
        width: this.config.thumbnailSize.width,
        height: this.config.thumbnailSize.height,
        format: this.config.screenshotOptions.format,
        quality: this.config.screenshotOptions.quality,
        filename: `lynx-preview-${resultId}.${this.config.screenshotOptions.format}`,
      });
    } catch (error) {
      console.warn('[WebPreviewService] Screenshot generation failed:', error);
      return null;
    }
  }

  /**
   * 创建Worker - 强制使用内联模式
   */
  private createWorker(): Worker {
    console.log(
      '🔧 [WebPreviewService.createWorker] 开始创建Worker（强制内联模式）',
    );
    console.log(
      '💡 [WebPreviewService.createWorker] 跳过所有文件路径检查，直接使用内联Worker',
    );

    // 清理任何现有的worker和blob URL
    if (this.worker) {
      console.log('🧹 [WebPreviewService.createWorker] 清理现有Worker');
      this.worker.terminate();
      this.worker = null;
    }

    if (this.workerBlobUrl) {
      console.log('🧹 [WebPreviewService.createWorker] 清理现有Blob URL');
      URL.revokeObjectURL(this.workerBlobUrl);
      this.workerBlobUrl = null;
    }

    try {
      return this.createInlineWorker();
    } catch (error) {
      console.error('💥 [WebPreviewService.createWorker] Worker创建失败:', {
        error,
        errorType: typeof error,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
      });

      throw new Error(
        `Failed to create worker: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * 创建内联Worker（降级方案）
   */
  private createInlineWorker(): Worker {
    console.log('🔧 [WebPreviewService.createInlineWorker] 开始创建内联Worker');
    console.time('⏱️ 内联Worker创建时间');

    // 最小化的测试Worker代码
    console.log('📝 [WebPreviewService.createInlineWorker] 准备最小Worker代码');

    const workerCode = `
      console.log('🔧 [LynxConverter] 启动成功');
      
      // Lynx转Web转换器类
      class LynxConverter {
        constructor() {
          this.data = {};
          this.methods = {};
        }
        
        // 解析Lynx内容
        parseContent(content) {
          console.log('📝 [LynxConverter] 解析内容');
          
          const files = {};
          
          // 提取FILE标签内容
          const filePattern = /<FILE\\s+path="([^"]+)"[^>]*>([\\s\\S]*?)<\\/FILE>/g;
          let match;
          
          while ((match = filePattern.exec(content)) !== null) {
            files[match[1]] = match[2].trim();
          }
          
          // 如果没有FILE标签，直接解析内容
          if (Object.keys(files).length === 0) {
            // 尝试提取template, style, script
            const templateMatch = content.match(/<template[^>]*>([\\s\\S]*?)<\\/template>/i);
            const styleMatch = content.match(/<style[^>]*>([\\s\\S]*?)<\\/style>/i);
            const scriptMatch = content.match(/<script[^>]*>([\\s\\S]*?)<\\/script>/i);
            
            if (templateMatch) files['index.ttml'] = templateMatch[1];
            if (styleMatch) files['index.ttss'] = styleMatch[1];
            if (scriptMatch) files['index.js'] = scriptMatch[1];
          }
          
          return files;
        }
        
        // 解析Lynx JavaScript获取数据和方法
        parseData(jsContent) {
          if (!jsContent) return {};
          
          try {
            console.log('🔍 [LynxConverter] 开始解析Lynx JS内容');
            
            // 提取Card中的内容 - 这是Lynx特有的格式
            const cardMatch = jsContent.match(/Card\\s*\\(\\s*\\{([\\s\\S]*?)\\}\\s*\\)/);
            if (!cardMatch) {
              console.warn('⚠️ [LynxConverter] 未找到Lynx Card定义');
              return {};
            }
            
            const cardContent = cardMatch[1];
            console.log('📋 [LynxConverter] Card内容长度:', cardContent.length);
            
            // 1. 提取data对象
            const data = this.extractDataObject(cardContent);
            
            // 2. 提取生命周期方法
            const lifeCycleMethods = this.extractLifeCycleMethods(cardContent);
            
            // 3. 提取自定义方法
            const customMethods = this.extractCustomMethods(cardContent);
            
            // 4. 合并所有解析结果
            const result = {
              ...data,
              _methods: customMethods,
              _lifeCycle: lifeCycleMethods,
              _hasLifeCycle: Object.keys(lifeCycleMethods).length > 0
            };
            
            console.log('📊 [LynxConverter] 解析结果:', {
              dataKeys: Object.keys(data),
              methodCount: Object.keys(customMethods).length,
              lifeCycleCount: Object.keys(lifeCycleMethods).length
            });
            
            return result;
          } catch (error) {
            console.warn('⚠️ [LynxConverter] Lynx JS解析失败:', error);
            return {};
          }
        }
        
        // 提取data对象
        extractDataObject(cardContent) {
          const dataMatch = cardContent.match(/data\\s*:\\s*\\{([\\s\\S]*?)\\}(?=\\s*[,}])/);
          if (!dataMatch) {
            console.log('ℹ️ [LynxConverter] 未找到data对象');
            return {};
          }
          
          const dataStr = '{' + dataMatch[1] + '}';
          return this.parseDataObject(dataStr);
        }
        
        // 提取生命周期方法
        extractLifeCycleMethods(cardContent) {
          console.log('🔄 [LynxConverter] 提取生命周期方法');
          
          const lifeCycleMethods = {};
          const lifeCycleNames = [
            'onLoad', 'onReady', 'onShow', 'onHide', 'onDestroy', 
            'onDataChanged', 'onError', 'onUnload', 'onPullDownRefresh',
            'onReachBottom', 'onShareAppMessage', 'onPageScroll'
          ];
          
          lifeCycleNames.forEach(methodName => {
            // 匹配方法定义 methodName() { ... } 或 methodName: function() { ... }
            const patterns = [
              new RegExp(\`\${methodName}\\\\s*\\\\(\\\\)\\\\s*\\\\{([\\\\s\\\\S]*?)\\\\}(?=\\\\s*[,}])\`, 'i'),
              new RegExp(\`\${methodName}\\\\s*:\\\\s*function\\\\s*\\\\(\\\\)\\\\s*\\\\{([\\\\s\\\\S]*?)\\\\}(?=\\\\s*[,}])\`, 'i')
            ];
            
            for (const pattern of patterns) {
              const match = cardContent.match(pattern);
              if (match) {
                lifeCycleMethods[methodName] = match[1].trim();
                console.log(\`✅ [LynxConverter] 找到生命周期方法: \${methodName}\`);
                break;
              }
            }
          });
          
          return lifeCycleMethods;
        }
        
        // 提取自定义方法
        extractCustomMethods(cardContent) {
          console.log('🔧 [LynxConverter] 提取自定义方法');
          
          const methods = {};
          
          // 匹配函数定义模式: methodName: function() { ... } 或 methodName() { ... }
          const methodPattern = /([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*[:=]?\\s*function\\s*\\([^)]*\\)\\s*\\{([\\s\\S]*?)\\}(?=\\s*[,}])/g;
          const arrowPattern = /([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*:\\s*\\([^)]*\\)\\s*=>\\s*\\{([\\s\\S]*?)\\}(?=\\s*[,}])/g;
          const shortPattern = /([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*\\([^)]*\\)\\s*\\{([\\s\\S]*?)\\}(?=\\s*[,}])/g;
          
          const patterns = [methodPattern, arrowPattern, shortPattern];
          
          patterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(cardContent)) !== null) {
              const methodName = match[1];
              let methodBody = match[2].trim();
              
              // 不跳过任何方法，包括被生命周期方法调用的自定义方法
              if (methodName !== 'data') {
                console.log(\`🔍 [LynxConverter] 检查方法: \${methodName}\`);
                
                // 简化验证，主要检查方法体是否存在
                if (methodBody && methodBody.length > 0) {
                  methods[methodName] = methodBody;
                  console.log(\`✅ [LynxConverter] 提取方法: \${methodName}\`);
                } else {
                  console.warn(\`⚠️ [LynxConverter] 方法体为空: \${methodName}\`);
                }
              }
            }
          });
          
          return methods;
        }
        
        // 验证方法体是否有效
        isValidMethodBody(methodBody) {
          if (!methodBody || typeof methodBody !== 'string') {
            return false;
          }
          
          const trimmed = methodBody.trim();
          
          // 检查基本语法规则
          // 1. 不能为空
          if (trimmed.length === 0) {
            return false;
          }
          
          // 2. 检查括号匹配
          const openBraces = (trimmed.match(/\\{/g) || []).length;
          const closeBraces = (trimmed.match(/\\}/g) || []).length;
          const openParens = (trimmed.match(/\\(/g) || []).length;
          const closeParens = (trimmed.match(/\\)/g) || []).length;
          
          if (openBraces !== closeBraces || openParens !== closeParens) {
            return false;
          }
          
          // 3. 不能包含某些明显错误的模式
          const invalidPatterns = [
            /this\\.[a-zA-Z_$][a-zA-Z0-9_$]*\\s*\\(/,  // this.methodName( 可能导致 'this.animateCards is not a function'
            /^\\s*function\\s*\\(/,  // 开头是function定义
          ];
          
          for (const pattern of invalidPatterns) {
            if (pattern.test(trimmed)) {
              console.warn('⚠️ 检测到潜在问题模式:', pattern, '在方法体:', trimmed.substring(0, 50));
              return false;
            }
          }
          
          return true;
        }
        
        
        // 安全解析数据对象
        parseDataObject(dataStr) {
          try {
            // 简单的数据解析，支持常见格式
            const cleanStr = dataStr
              .replace(/([{,]\\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*:/g, '$1"$2":') // 给键加引号
              .replace(/'/g, '"'); // 单引号转双引号
            
            return JSON.parse(cleanStr);
          } catch (e) {
            // 降级到简单解析
            return this.simpleDataParse(dataStr);
          }
        }
        
        // 简单数据解析（降级方案）
        simpleDataParse(dataStr) {
          const data = {};
          
          // 解析简单的键值对
          const keyValuePattern = /([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*:\\s*([^,}]+)/g;
          let match;
          
          while ((match = keyValuePattern.exec(dataStr)) !== null) {
            const key = match[1].trim();
            let value = match[2].trim();
            
            // 处理不同类型的值
            if (value.startsWith('"') && value.endsWith('"')) {
              data[key] = value.slice(1, -1); // 字符串
            } else if (value.startsWith("'") && value.endsWith("'")) {
              data[key] = value.slice(1, -1); // 字符串
            } else if (!isNaN(value)) {
              data[key] = Number(value); // 数字
            } else if (value === 'true' || value === 'false') {
              data[key] = value === 'true'; // 布尔值
            } else if (value.startsWith('[') && value.endsWith(']')) {
              try {
                data[key] = JSON.parse(value); // 数组
              } catch (e) {
                data[key] = value;
              }
            } else {
              data[key] = value; // 其他当字符串处理
            }
          }
          
          return data;
        }
        
        // 处理模板语法 {{}} 
        processTemplateBindings(html, data) {
          console.log('🔗 [LynxConverter] 处理数据绑定');
          console.log('📊 数据对象:', data);
          
          // 先修复常见的格式错误
          let fixedHtml = this.fixMalformedTemplates(html);
          
          return fixedHtml.replace(/\\{\\{\\s*([^}]+)\\s*\\}\\}/g, (match, expression) => {
            const originalExpr = expression.trim();
            console.log('🔍 处理表达式:', originalExpr);
            
            try {
              // 解析表达式的结果
              const result = this.evaluateExpression(originalExpr, data);
              console.log('✅ 表达式结果:', originalExpr, '->', result);
              
              // 如果结果是错误信息，直接返回
              if (typeof result === 'string' && result.startsWith('[解析错误:')) {
                return \`<span style="color: red; font-family: monospace; background: #fff5f5; padding: 2px 4px; border-radius: 3px;">\${result}</span>\`;
              }
              
              return result !== undefined && result !== null ? String(result) : match;
            } catch (error) {
              console.warn('⚠️ 表达式解析失败:', originalExpr, error);
              return \`<span style="color: red; font-family: monospace; background: #fff5f5; padding: 2px 4px; border-radius: 3px;">[解析异常: \${originalExpr}]</span>\`;
            }
          });
        }
        
        // 检查模板语法（不自动修复）
        fixMalformedTemplates(html) {
          console.log('🔍 [LynxConverter] 检查模板语法（不自动修复）');
          
          // 不进行任何修复，保持原始语法
          // 如果有语法错误，在转换时会显示错误信息
          
          return html;
        }
        
        // 增强的表达式求值器
        evaluateExpression(expr, data) {
          console.log('🔢 求值表达式:', expr, '数据:', data);
          
          // 去除多余空格
          expr = expr.trim();
          
          // 0. 处理复杂比较运算符 data.status === 'active', count > 0, price >= 100
          if (/[<>=!]=?/.test(expr) && !expr.includes('?')) {
            return this.evaluateComparisonOperation(expr, data);
          }
          
          // 1. 处理数组访问 data.items[0], items[index], items[item.id]
          if (expr.includes('[') && expr.includes(']')) {
            return this.evaluateArrayAccess(expr, data);
          }
          
          // 2. 处理对象属性链访问 data.user.name, user.profile.avatar
          if (expr.includes('.')) {
            // 特殊处理：如果包含运算符，优先处理运算
            if (/[+\\-*/]/.test(expr) && !expr.includes('(')) {
              return this.evaluateSimpleOperation(expr, data);
            }
            return this.evaluatePropertyChain(expr, data);
          }
          
          // 3. 处理函数调用 data.getName(), items.length, data.items.filter()
          if (expr.includes('(') && expr.includes(')')) {
            return this.evaluateFunctionCall(expr, data);
          }
          
          // 4. 处理简单操作符 data.count + 1, data.price * 0.8
          if (/[+\\-*/]/.test(expr)) {
            return this.evaluateSimpleOperation(expr, data);
          }
          
          // 5. 处理条件表达式 data.status === 'active' ? '活跃' : '非活跃'
          if (expr.includes('?') && expr.includes(':')) {
            return this.evaluateTernaryOperator(expr, data);
          }
          
          // 6. 处理逻辑运算符 data.isActive && data.isVisible
          if (/&&|\\|\\|/.test(expr)) {
            return this.evaluateLogicalOperation(expr, data);
          }
          
          // 7. 直接属性访问
          if (data && data.hasOwnProperty(expr)) {
            return data[expr];
          }
          
          // 8. 特殊关键字处理
          if (expr === 'data') {
            return data;
          }
          
          // 9. 字面量处理
          if (expr.startsWith('"') && expr.endsWith('"')) {
            return expr.slice(1, -1); // 字符串字面量
          }
          
          if (expr.startsWith("'") && expr.endsWith("'")) {
            return expr.slice(1, -1); // 字符串字面量
          }
          
          if (!isNaN(expr) && !isNaN(parseFloat(expr))) {
            return parseFloat(expr); // 数字字面量
          }
          
          if (expr === 'true') return true;
          if (expr === 'false') return false;
          if (expr === 'null') return null;
          if (expr === 'undefined') return undefined;
          
          // 10. 处理模板字符串内的变量引用（不带引号的字符串）
          if (/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(expr)) {
            // 尝试在data中查找
            if (data && typeof data === 'object' && data[expr] !== undefined) {
              return data[expr];
            }
          }
          
          // 11. 生成错误信息而不是返回undefined
          const errorMessage = \`无法解析表达式: \${expr}\`;
          console.warn('🤷', errorMessage);
          console.warn('📊 可用数据:', Object.keys(data || {}));
          
          // 返回错误信息显示在页面上
          return \`[解析错误: \${expr}]\`;
        }
        
        // 处理比较运算符 ===, !==, >, <, >=, <=
        evaluateComparisonOperation(expr, data) {
          console.log('⚖️ [比较运算] 表达式:', expr);
          
          // 匹配比较运算符
          const comparisonMatch = expr.match(/^(.+?)\\s*(===|!==|>=|<=|>|<|==|!=)\\s*(.+?)$/);
          if (!comparisonMatch) return undefined;
          
          const [, leftExpr, operator, rightExpr] = comparisonMatch;
          const leftValue = this.evaluateExpression(leftExpr.trim(), data);
          const rightValue = this.evaluateExpression(rightExpr.trim(), data);
          
          console.log('⚖️ [比较运算] 左值:', leftValue, '运算符:', operator, '右值:', rightValue);
          
          switch (operator) {
            case '===': return leftValue === rightValue;
            case '!==': return leftValue !== rightValue;
            case '==': return leftValue == rightValue;
            case '!=': return leftValue != rightValue;
            case '>': return Number(leftValue) > Number(rightValue);
            case '<': return Number(leftValue) < Number(rightValue);
            case '>=': return Number(leftValue) >= Number(rightValue);
            case '<=': return Number(leftValue) <= Number(rightValue);
            default: return undefined;
          }
        }
        
        // 处理逻辑运算符 &&, ||
        evaluateLogicalOperation(expr, data) {
          console.log('🔗 [逻辑运算] 表达式:', expr);
          
          // 处理 && 运算符
          if (expr.includes('&&')) {
            const parts = expr.split('&&').map(p => p.trim());
            const results = parts.map(part => this.evaluateExpression(part, data));
            return results.every(result => Boolean(result));
          }
          
          // 处理 || 运算符
          if (expr.includes('||')) {
            const parts = expr.split('||').map(p => p.trim());
            const results = parts.map(part => this.evaluateExpression(part, data));
            return results.some(result => Boolean(result));
          }
          
          return undefined;
        }
        
        // 处理属性链访问 data.user.name, user.profile.avatar
        evaluatePropertyChain(expr, data) {
          const keys = expr.split('.');
          let value = data;
          
          for (const key of keys) {
            if (key === 'data') {
              value = data;
              continue;
            }
            
            if (value && typeof value === 'object' && value.hasOwnProperty(key)) {
              value = value[key];
            } else {
              return undefined;
            }
          }
          
          return value;
        }
        
        // 处理数组访问 data.items[0], items[1]
        evaluateArrayAccess(expr, data) {
          const arrayMatch = expr.match(/^(.+?)\\[(.+?)\\]$/);
          if (!arrayMatch) return undefined;
          
          const [, arrayPath, indexExpr] = arrayMatch;
          const arrayValue = this.evaluateExpression(arrayPath.trim(), data);
          
          if (!Array.isArray(arrayValue)) {
            return undefined;
          }
          
          // 处理索引表达式
          let index;
          if (indexExpr.trim() === 'index' || indexExpr.trim() === 'i') {
            // 在循环上下文中，这些需要特殊处理
            index = 0; // 默认取第一个
          } else if (!isNaN(indexExpr.trim())) {
            index = parseInt(indexExpr.trim());
          } else {
            // 索引也可能是一个表达式
            index = this.evaluateExpression(indexExpr.trim(), data);
          }
          
          if (typeof index === 'number' && index >= 0 && index < arrayValue.length) {
            return arrayValue[index];
          }
          
          return undefined;
        }
        
        // 处理函数调用 data.items.length, user.getName()
        evaluateFunctionCall(expr, data) {
          const funcMatch = expr.match(/^(.+?)\\.([a-zA-Z_$][a-zA-Z0-9_$]*)\\(\\)$/);
          if (!funcMatch) return undefined;
          
          const [, objectPath, methodName] = funcMatch;
          const object = this.evaluateExpression(objectPath, data);
          
          if (object && typeof object[methodName] === 'function') {
            try {
              return object[methodName]();
            } catch (e) {
              console.warn('函数调用失败:', expr, e);
              return undefined;
            }
          }
          
          // 处理常见的属性访问（如 length）
          if (object && object.hasOwnProperty(methodName)) {
            return object[methodName];
          }
          
          return undefined;
        }
        
        // 处理简单运算 data.count + 1, data.price * 0.8
        evaluateSimpleOperation(expr, data) {
          // 简单的数学运算解析
          const operatorMatch = expr.match(/^(.+?)\\s*([+\\-*/])\\s*(.+?)$/);
          if (!operatorMatch) return undefined;
          
          const [, leftExpr, operator, rightExpr] = operatorMatch;
          const leftValue = this.evaluateExpression(leftExpr.trim(), data);
          const rightValue = this.evaluateExpression(rightExpr.trim(), data);
          
          if (typeof leftValue === 'number' && typeof rightValue === 'number') {
            switch (operator) {
              case '+': return leftValue + rightValue;
              case '-': return leftValue - rightValue;
              case '*': return leftValue * rightValue;
              case '/': return rightValue !== 0 ? leftValue / rightValue : undefined;
            }
          }
          
          return undefined;
        }
        
        // 处理三元运算符 condition ? value1 : value2
        evaluateTernaryOperator(expr, data) {
          const ternaryMatch = expr.match(/^(.+?)\\s*\\?\\s*(.+?)\\s*:\\s*(.+?)$/);
          if (!ternaryMatch) return undefined;
          
          const [, conditionExpr, trueExpr, falseExpr] = ternaryMatch;
          const condition = this.evaluateExpression(conditionExpr.trim(), data);
          
          if (condition) {
            return this.evaluateExpression(trueExpr.trim(), data);
          } else {
            return this.evaluateExpression(falseExpr.trim(), data);
          }
        }
        
        // 处理循环渲染 tt:for (增强版)
        processLoopRendering(html, data) {
          console.log('🔄 [LynxConverter] 处理循环渲染 (增强版)');
          
          // 支持多种循环语法格式
          const patterns = [
            // 1. 标准语法: tt:for="item in items"
            /<([^>]+)\\s+tt:for="([^"]*)"([^>]*)>([\\s\\S]*?)<\\/\\1>/g,
            // 2. 微信小程序语法: wx:for="{{items}}" wx:for-item="item"
            /<([^>]+)\\s+wx:for="([^"]*)"(?:\\s+wx:for-item="([^"]*)")?([^>]*)>([\\s\\S]*?)<\\/\\1>/g,
            // 3. 支付宝小程序语法: a:for="{{items}}" a:for-item="item"  
            /<([^>]+)\\s+a:for="([^"]*)"(?:\\s+a:for-item="([^"]*)")?([^>]*)>([\\s\\S]*?)<\\/\\1>/g
          ];
          
          let processedHtml = html;
          
          patterns.forEach((pattern, patternIndex) => {
            processedHtml = processedHtml.replace(pattern, (match, tagName, forExpr, itemName, attrs, innerHtml) => {
              console.log(\`🔄 处理循环 (模式\${patternIndex + 1}):\`, forExpr, '项目名:', itemName);
              
              let items = [];
              let actualItemName = 'item';
              let indexName = 'index';
              
              // 解析不同的 for 表达式格式
              if (patternIndex === 0) {
                // tt:for="item in items" 格式
                if (forExpr.includes(' in ')) {
                  const [item, source] = forExpr.split(' in ').map(s => s.trim());
                  actualItemName = item;
                  
                  // 支持解构语法: (item, index) in items
                  if (item.includes(',')) {
                    const parts = item.replace(/[()]/g, '').split(',').map(s => s.trim());
                    actualItemName = parts[0];
                    if (parts[1]) indexName = parts[1];
                  }
                  
                  const sourceExpr = source.replace(/[{}]/g, '').trim();
                  items = this.evaluateExpression(sourceExpr, data);
                } else {
                  // tt:for="{{items}}" 格式
                  const sourceExpr = forExpr.replace(/[{}]/g, '').trim();
                  items = this.evaluateExpression(sourceExpr, data);
                }
              } else {
                // 微信/支付宝小程序格式
                if (itemName) actualItemName = itemName;
                const sourceExpr = forExpr.replace(/[{}]/g, '').trim();
                items = this.evaluateExpression(sourceExpr, data);
                // 处理额外的属性
                const indexMatch = attrs.match(/(?:wx|a):for-index="([^"]*)"/);
                if (indexMatch) indexName = indexMatch[1];
              }
              
              // 支持对象遍历
              if (!Array.isArray(items) && typeof items === 'object') {
                console.log('🔄 检测到对象，转换为键值对数组');
                items = Object.entries(items).map(([key, value]) => ({ key, value }));
                actualItemName = actualItemName || 'item';
              }
              
              if (!Array.isArray(items)) {
                console.warn('⚠️ [LynxConverter] 循环数据无效:', items, '表达式:', forExpr);
                console.warn('📊 当前数据对象:', data);
                console.warn('📊 可用数据字段:', Object.keys(data || {}));
                
                // 检查是否是字符串形式的错误信息
                if (typeof items === 'string' && items.startsWith('[解析错误:')) {
                  return \`<div style="color: red; padding: 10px; border: 1px solid red; margin: 5px; font-family: monospace;">[循环转换错误] \${items}</div>\`;
                }
                
                // 返回详细错误信息
                const errorMsg = \`循环数据解析失败: \${forExpr} (期望数组，得到: \${typeof items} = \${JSON.stringify(items)})\`;
                return \`<div style="color: red; padding: 10px; border: 1px solid red; margin: 5px; font-family: monospace; background: #fff5f5;">[循环转换错误] \${errorMsg}<br>可用数据: \${Object.keys(data || {}).join(', ')}</div>\`;
              }
              
              console.log('📋 循环数据:', items, '项目名称:', actualItemName, '索引名称:', indexName);
              
              // 生成循环内容
              return items.map((item, index) => {
                let itemHtml = innerHtml;
                
                // 创建增强的循环上下文数据
                const loopContext = {
                  ...data,
                  [actualItemName]: item,
                  [indexName]: index,
                  index: index, // 向后兼容
                  i: index,     // 简写形式
                  // 添加一些有用的循环变量
                  first: index === 0,
                  last: index === items.length - 1,
                  even: index % 2 === 0,
                  odd: index % 2 === 1,
                  count: items.length
                };
              
              console.log('🔄 循环项目 #' + index + ':', item, '上下文:', loopContext);
              
              // 使用增强的模板处理器处理循环内部的模板
              itemHtml = this.processTemplateBindingsInLoop(itemHtml, loopContext, actualItemName, item, index);
              
              // 移除循环相关属性
              itemHtml = itemHtml.replace(/\\s+(?:tt|wx|a):(?:for|for-item|for-index|key)="[^"]*"/g, '');
              
              return \`<\${tagName}\${attrs} data-index="\${index}" data-item-type="\${typeof item}">\${itemHtml}</\${tagName}>\`;
            }).join('');
          });
          });
          
          return processedHtml;
        }
        
        // 处理循环内部的模板绑定
        processTemplateBindingsInLoop(html, loopContext, itemName, currentItem, currentIndex) {
          console.log('🔗 [循环模板] 处理循环内部绑定');
          
          // 先修复循环内部的模板语法错误
          let fixedHtml = this.fixMalformedTemplates(html);
          
          return fixedHtml.replace(/\\{\\{\\s*([^}]+)\\s*\\}\\}/g, (match, expression) => {
            const originalExpr = expression.trim();
            console.log('🔍 [循环模板] 处理表达式:', originalExpr);
            
            try {
              // 先尝试在循环上下文中解析
              let result = this.evaluateExpressionInLoop(originalExpr, loopContext, itemName, currentItem, currentIndex);
              
              if (result !== undefined && result !== null) {
                console.log('✅ [循环模板] 循环上下文解析成功:', originalExpr, '->', result);
                return String(result);
              }
              
              // 如果循环上下文中找不到，尝试全局数据
              result = this.evaluateExpression(originalExpr, loopContext);
              
              if (result !== undefined && result !== null) {
                console.log('✅ [循环模板] 全局上下文解析成功:', originalExpr, '->', result);
                return String(result);
              }
              
              console.warn('⚠️ [循环模板] 表达式无法解析:', originalExpr);
              return match; // 保持原样
              
            } catch (error) {
              console.warn('⚠️ [循环模板] 表达式解析失败:', originalExpr, error);
              return match; // 保持原样
            }
          });
        }
        
        // 在循环上下文中求值表达式
        evaluateExpressionInLoop(expr, loopContext, itemName, currentItem, currentIndex) {
          console.log('🔢 [循环求值] 表达式:', expr, '当前项:', currentItem, '索引:', currentIndex);
          
          expr = expr.trim();
          
          // 1. 处理特殊的循环变量
          if (expr === 'index' || expr === 'i') {
            return currentIndex;
          }
          
          if (expr === itemName) {
            return currentItem;
          }
          
          // 2. 处理项目属性访问 item.name, item.value
          if (expr.startsWith(itemName + '.')) {
            const propertyPath = expr.substring(itemName.length + 1);
            return this.evaluatePropertyChain(propertyPath, currentItem);
          }
          
          // 3. 处理项目数组访问 item[0], item['key']
          if (expr.startsWith(itemName + '[') && expr.endsWith(']')) {
            const indexPart = expr.substring(itemName.length + 1, expr.length - 1);
            
            if (Array.isArray(currentItem)) {
              const index = parseInt(indexPart);
              if (!isNaN(index) && index >= 0 && index < currentItem.length) {
                return currentItem[index];
              }
            } else if (typeof currentItem === 'object') {
              const key = indexPart.replace(/['"]/g, ''); // 移除引号
              return currentItem[key];
            }
          }
          
          // 4. 处理复合表达式 item.count + 1, item.price * 0.8
          if (/[+\\-*/]/.test(expr) && expr.includes(itemName)) {
            // 替换 itemName 为实际值，然后求值
            let modifiedExpr = expr;
            
            // 替换简单的 item 引用
            if (typeof currentItem === 'number') {
              modifiedExpr = modifiedExpr.replace(new RegExp(\`\\\\b\${itemName}\\\\b\`, 'g'), String(currentItem));
            }
            
            // 替换 item.property 引用
            const propertyPattern = new RegExp(\`\${itemName}\\\\.(\\\\w+)\`, 'g');
            modifiedExpr = modifiedExpr.replace(propertyPattern, (match, prop) => {
              const value = currentItem && typeof currentItem === 'object' ? currentItem[prop] : undefined;
              return value !== undefined ? String(value) : match;
            });
            
            return this.evaluateSimpleOperation(modifiedExpr, loopContext);
          }
          
          // 5. 使用标准求值器在循环上下文中求值
          return this.evaluateExpression(expr, loopContext);
        }
        
        // 转换标签
        convertTags(html) {
          const tagMap = {
            'view': 'div',
            'text': 'span',
            'image': 'img',
            'scroll-view': 'div',
            'list': 'div',
            'list-item': 'div'
          };
          
          Object.entries(tagMap).forEach(([lynxTag, htmlTag]) => {
            const openRegex = new RegExp(\`<\${lynxTag}(\\\\s[^>]*?)?>\`, 'g');
            html = html.replace(openRegex, \`<\${htmlTag}$1>\`);
            
            const closeRegex = new RegExp(\`</\${lynxTag}>\`, 'g');
            html = html.replace(closeRegex, \`</\${htmlTag}>\`);
          });
          
          return html;
        }
        
        // 处理事件绑定
        processEvents(html) {
          html = html.replace(/bindtap="([^"]*)"/g, 'onclick="handleTap(\\'$1\\')"');
          html = html.replace(/bindlongpress="([^"]*)"/g, 'oncontextmenu="handleLongPress(\\'$1\\'); return false;"');
          html = html.replace(/catchtap="([^"]*)"/g, 'onclick="handleTap(\\'$1\\'); event.stopPropagation();"');
          
          return html;
        }
        
        // 清理Lynx特有属性
        cleanLynxAttributes(html) {
          html = html.replace(/\\s+tt:for="[^"]*"/g, '');
          html = html.replace(/\\s+tt:if="[^"]*"/g, '');
          html = html.replace(/\\s+tt:key="[^"]*"/g, '');
          html = html.replace(/\\s+tt:elif="[^"]*"/g, '');
          html = html.replace(/\\s+tt:else/g, '');
          
          return html;
        }
        
        // 转换CSS
        convertCSS(ttss) {
          if (!ttss) return '';
          
          let css = ttss;
          
          // rpx转换 (1rpx = 0.5px 基于375px宽度)
          css = css.replace(/(\\d+(?:\\.\\d+)?)rpx/g, (match, value) => {
            const pxValue = parseFloat(value) * 0.5;
            return \`\${pxValue}px\`;
          });
          
          // 移除Lynx特有样式
          css = css.replace(/enable-scroll\\s*:\\s*[^;]+;?/g, '');
          css = css.replace(/scroll-[xy]\\s*:\\s*[^;]+;?/g, '');
          
          return css;
        }
        
        // 主转换方法
        convert(content) {
          try {
            console.log('🔄 [LynxConverter] 开始转换');
            console.time('⏱️ 转换时间');
            
            // 1. 解析内容
            const files = this.parseContent(content);
            
            // 2. 解析数据
            const data = this.parseData(files['index.js'] || '');
            
            // 3. 获取模板和样式
            let html = files['index.ttml'] || '';
            const css = this.convertCSS(files['index.ttss'] || '');
            
            if (!html) {
              throw new Error('未找到TTML模板内容');
            }
            
            // 4. 处理循环渲染（必须在数据绑定之前）
            html = this.processLoopRendering(html, data);
            
            // 5. 处理数据绑定
            html = this.processTemplateBindings(html, data);
            
            // 6. 转换标签
            html = this.convertTags(html);
            
            // 7. 处理事件
            html = this.processEvents(html);
            
            // 8. 清理属性
            html = this.cleanLynxAttributes(html);
            
            // 9. 生成完整HTML
            const fullHTML = this.generateHTML(html, css, data);
            
            console.timeEnd('⏱️ 转换时间');
            console.log('✅ [LynxConverter] 转换成功');
            
            return {
              success: true,
              html: fullHTML
            };
            
          } catch (error) {
            console.error('❌ [LynxConverter] 转换失败:', error);
            return {
              success: false,
              error: 'CONVERSION_ERROR',
              message: \`转换失败: \${error.message}\`
            };
          }
        }
        
        // 生成完整HTML (增强版)
        generateHTML(bodyHtml, css, data) {
          // 提取生命周期方法和自定义方法
          const lifeCycleMethods = data._lifeCycle || {};
          const customMethods = data._methods || {};
          const hasLifeCycle = data._hasLifeCycle || false;
          
          // 清理数据对象，移除内部属性
          const cleanData = { ...data };
          delete cleanData._lifeCycle;
          delete cleanData._methods;
          delete cleanData._hasLifeCycle;
          
          // 生成生命周期执行代码
          const lifeCycleCode = this.generateLifeCycleCode(lifeCycleMethods);
          
          // 生成自定义方法代码
          const customMethodsCode = this.generateCustomMethodsCode(customMethods);
          
          return \`<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Lynx Web Preview</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6; color: #333; background: #f5f5f5; padding: 16px;
    }
    div { display: flex; flex-direction: column; }
    span { display: inline-block; }
    img { max-width: 100%; height: auto; }
    .lynx-container { min-height: 100vh; }
    \${css}
  </style>
</head>
<body>
  <div class="lynx-container">
    \${bodyHtml}
  </div>
  
  <script>
    // 页面数据
    const pageData = \${JSON.stringify(cleanData, null, 2)};
    
    // 模拟小程序环境
    const lynxEnv = {
      data: pageData,
      setData: function(newData, callback) {
        console.log('🔄 [setData] 更新数据:', newData);
        Object.assign(this.data, newData);
        
        // 重新渲染相关元素（简化版）
        this.updateDOM();
        
        if (callback && typeof callback === 'function') {
          callback();
        }
      },
      
      updateDOM: function() {
        console.log('🔄 [updateDOM] 更新DOM（模拟）');
        // 这里可以添加实际的DOM更新逻辑
      },
      
      // 模拟系统信息
      getSystemInfo: function() {
        return {
          platform: 'web',
          system: navigator.userAgent,
          windowWidth: window.innerWidth,
          windowHeight: window.innerHeight,
          pixelRatio: window.devicePixelRatio || 1
        };
      }
    };
    
    // 自定义方法（绑定到lynxEnv上）
    \${customMethodsCode}
    
    // 事件处理函数
    function handleTap(handler) {
      console.log('👆 [点击事件]:', handler);
      if (typeof lynxEnv[handler] === 'function') {
        try {
          lynxEnv[handler].call(lynxEnv);
        } catch (error) {
          console.error('事件处理错误:', error);
        }
      } else if (typeof window[handler] === 'function') {
        try {
          window[handler].call(lynxEnv);
        } catch (error) {
          console.error('事件处理错误:', error);
        }
      } else {
        console.warn('未找到事件处理函数:', handler);
      }
    }
    
    function handleLongPress(handler) {
      console.log('👆 [长按事件]:', handler);
      if (typeof lynxEnv[handler] === 'function') {
        try {
          lynxEnv[handler].call(lynxEnv);
        } catch (error) {
          console.error('事件处理错误:', error);
        }
      } else if (typeof window[handler] === 'function') {
        try {
          window[handler].call(lynxEnv);
        } catch (error) {
          console.error('事件处理错误:', error);
        }
      } else {
        console.warn('未找到事件处理函数:', handler);
      }
    }
    
    // 生命周期执行（绑定到lynxEnv上）
    \${lifeCycleCode}
    
    // 页面加载完成
    console.log('📱 [Lynx Web Preview] 页面加载完成');
    console.log('📊 [页面数据]:', pageData);
    \${hasLifeCycle ? 'console.log("⚡ [生命周期] 已启用生命周期方法");' : ''}
    console.log('🔧 [环境信息]:', lynxEnv.getSystemInfo());
    
    // 执行onLoad生命周期
    if (typeof lynxEnv.onLoad === 'function') {
      try {
        console.log('🚀 [生命周期] 执行 onLoad');
        lynxEnv.onLoad.call(lynxEnv);
      } catch (error) {
        console.error('onLoad 执行错误:', error);
      }
    }
    
    // 执行onReady生命周期
    document.addEventListener('DOMContentLoaded', function() {
      if (typeof lynxEnv.onReady === 'function') {
        try {
          console.log('🚀 [生命周期] 执行 onReady');
          lynxEnv.onReady.call(lynxEnv);
        } catch (error) {
          console.error('onReady 执行错误:', error);
        }
      }
      
      // 执行onShow生命周期
      if (typeof lynxEnv.onShow === 'function') {
        try {
          console.log('🚀 [生命周期] 执行 onShow');
          lynxEnv.onShow.call(lynxEnv);
        } catch (error) {
          console.error('onShow 执行错误:', error);
        }
      }
    });
    
    // 页面隐藏时执行onHide
    document.addEventListener('visibilitychange', function() {
      if (document.hidden && typeof lynxEnv.onHide === 'function') {
        try {
          console.log('🚀 [生命周期] 执行 onHide');
          lynxEnv.onHide.call(lynxEnv);
        } catch (error) {
          console.error('onHide 执行错误:', error);
        }
      } else if (!document.hidden && typeof lynxEnv.onShow === 'function') {
        try {
          console.log('🚀 [生命周期] 执行 onShow (页面重新显示)');
          lynxEnv.onShow.call(lynxEnv);
        } catch (error) {
          console.error('onShow 执行错误:', error);
        }
      }
    });
    
  </script>
</body>
</html>\`;
        }
        
        // 生成生命周期执行代码
        generateLifeCycleCode(lifeCycleMethods) {
          if (!lifeCycleMethods || Object.keys(lifeCycleMethods).length === 0) {
            return '// 无生命周期方法';
          }
          
          let code = '// 生命周期方法定义\\n';
          
          Object.entries(lifeCycleMethods).forEach(([methodName, methodBody]) => {
            // 确保方法体是有效的JavaScript代码
            let cleanMethodBody = methodBody || '';
            
            code += \`
    lynxEnv.\${methodName} = function() {
      console.log('🔄 [生命周期] 执行 \${methodName}');
      try {
        \${cleanMethodBody}
      } catch (error) {
        console.error('\${methodName} 执行错误:', error);
      }
    };
    
    // 同时绑定到window以便直接调用
    window.\${methodName} = lynxEnv.\${methodName}.bind(lynxEnv);
    \`;
          });
          
          return code;
        }
        
        // 生成自定义方法代码
        generateCustomMethodsCode(customMethods) {
          if (!customMethods || Object.keys(customMethods).length === 0) {
            return '// 无自定义方法';
          }
          
          let code = '// 自定义方法定义\\n';
          
          Object.entries(customMethods).forEach(([methodName, methodBody]) => {
            // 确保方法体是有效的JavaScript代码
            let cleanMethodBody = methodBody || '';
            
            code += \`
    lynxEnv.\${methodName} = function() {
      console.log('🔧 [自定义方法] 执行 \${methodName}');
      try {
        \${cleanMethodBody}
      } catch (error) {
        console.error('\${methodName} 执行错误:', error);
      }
    };
    
    // 同时绑定到window以便直接调用
    window.\${methodName} = lynxEnv.\${methodName}.bind(lynxEnv);
    \`;
          });
          
          return code;
        }
      }
      
      // Worker实例
      const converter = new LynxConverter();
      
      self.onmessage = function(event) {
        console.log('📨 [LynxConverter] 收到消息:', event.data.type);
        
        if (event.data.type === 'HEALTH_CHECK') {
          self.postMessage({ success: true, message: 'Worker健康' });
          return;
        }
        
        if (event.data.type === 'CONVERT') {
          const result = converter.convert(event.data.content);
          self.postMessage(result);
          return;
        }
        
        self.postMessage({ success: false, error: 'UNKNOWN_TYPE' });
      };
      
      self.onerror = function(error) {
        console.error('❌ [LynxConverter] Worker错误:', error);
        self.postMessage({ success: false, error: 'WORKER_ERROR', message: error.message });
      };
    `;

    console.log('📦 [WebPreviewService.createInlineWorker] 创建Worker Blob');
    console.log('📋 Worker代码长度:', workerCode.length);

    const blob = new Blob([workerCode], { type: 'application/javascript' });
    this.workerBlobUrl = URL.createObjectURL(blob);

    console.log(
      '🔗 [WebPreviewService.createInlineWorker] Blob URL已创建:',
      this.workerBlobUrl,
    );

    console.log('🔨 [WebPreviewService.createInlineWorker] 创建Worker实例');
    console.log('🔗 使用Blob URL:', this.workerBlobUrl);
    console.log('🔗 Blob URL长度:', this.workerBlobUrl?.length);

    const worker = new Worker(this.workerBlobUrl);

    console.timeEnd('⏱️ 内联Worker创建时间');
    console.log('✅ [WebPreviewService.createInlineWorker] 内联Worker创建成功');
    console.log('🔧 Worker实例:', worker);
    console.log('🔧 Worker URL:', worker.url || 'URL属性不可用');

    // 添加错误监听器进行调试
    worker.addEventListener('error', error => {
      console.error('🚨 [WebPreviewService] Worker错误(调试):', {
        message: error.message,
        filename: error.filename,
        lineno: error.lineno,
        colno: error.colno,
        error: error.error,
        workerBlobUrl: this.workerBlobUrl,
      });
    });

    // 不在创建时执行健康检查，避免消息混淆
    console.log(
      '✓ [WebPreviewService.createInlineWorker] Worker创建完成(内联模式)',
    );
    console.log('⚠️ 跳过健康检查，避免与转换消息冲突');
    return worker;
  }

  /**
   * 检查服务健康状态
   */
  async checkHealth(): Promise<boolean> {
    try {
      if (!this.worker) {
        this.worker = this.createWorker();
      }

      return new Promise(resolve => {
        const timeout = setTimeout(() => resolve(false), 3000);

        const handleMessage = (event: MessageEvent) => {
          clearTimeout(timeout);
          this.worker!.removeEventListener('message', handleMessage);
          resolve(event.data.success === true);
        };

        this.worker!.addEventListener('message', handleMessage);
        this.worker!.postMessage({ type: 'HEALTH_CHECK' });
      });
    } catch {
      return false;
    }
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }

    if (this.workerBlobUrl) {
      URL.revokeObjectURL(this.workerBlobUrl);
      this.workerBlobUrl = null;
    }

    this.screenshotService.dispose();
  }

  /**
   * 获取服务配置
   */
  getConfig(): WebPreviewConfig {
    return { ...this.config };
  }

  /**
   * 更新服务配置
   */
  updateConfig(newConfig: Partial<WebPreviewConfig>): void {
    this.config = {
      ...this.config,
      ...newConfig,
    };
  }
}
