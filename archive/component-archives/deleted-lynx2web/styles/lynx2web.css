/**
 * Lynx2Web模块样式
 * 提供Web预览相关组件的样式定义
 */

/* Web预览按钮 */
.web-preview-btn {
  position: relative;
  overflow: hidden;
}

.web-preview-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.web-preview-btn:hover::before {
  left: 100%;
}

/* 预览状态容器 */
.web-preview-loading {
  min-width: 200px;
}

.web-preview-error {
  min-width: 250px;
  max-width: 300px;
}

.web-preview-success {
  display: inline-block;
}

/* 缩略图预览 */
.thumbnail-preview {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.thumbnail-preview:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.thumbnail-preview .thumbnail-overlay {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3));
  backdrop-filter: blur(2px);
}

/* 预览弹窗动画 */
.web-preview-modal {
  animation: modalFadeIn 0.3s ease-out;
}

.web-preview-modal > div {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 加载动画 */
.preview-loading-spinner {
  border-width: 2px;
  border-style: solid;
  border-color: currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 进度条动画 */
.preview-progress-bar {
  position: relative;
  overflow: hidden;
  background: rgba(59, 130, 246, 0.1);
}

.preview-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.3),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    width: 0%;
    left: 0%;
  }
  50% {
    width: 100%;
    left: 0%;
  }
  100% {
    width: 0%;
    left: 100%;
  }
}

/* 状态图标 */
.status-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
}

.status-icon.success {
  background: #0ea5e9;
  color: white;
}

.status-icon.error {
  background: #ef4444;
  color: white;
}

.status-icon.loading {
  background: #3b82f6;
  color: white;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .web-preview-modal > div {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
  }
  
  .thumbnail-preview {
    width: 100px !important;
    height: 133px !important;
  }
  
  .web-preview-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .thumbnail-preview {
    border: 2px solid currentColor;
  }
  
  .web-preview-btn {
    border: 2px solid currentColor;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .thumbnail-preview,
  .web-preview-btn,
  .preview-loading-spinner {
    transition: none;
    animation: none;
  }
  
  .web-preview-modal,
  .web-preview-modal > div {
    animation: none;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .web-preview-loading {
    background: #1f2937;
    border-color: #374151;
    color: #e5e7eb;
  }
  
  .web-preview-error {
    background: #7f1d1d;
    border-color: #dc2626;
    color: #fecaca;
  }
  
  .thumbnail-preview {
    background: #1f2937;
    border-color: #374151;
  }
}

/* 全屏样式 */
iframe:fullscreen {
  width: 100vw !important;
  height: 100vh !important;
  min-height: unset !important;
  background-color: white !important;
  z-index: 9999 !important;
  border: none !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

iframe:fullscreen::backdrop {
  background-color: black;
}

/* Webkit全屏支持 */
iframe:-webkit-full-screen {
  width: 100vw !important;
  height: 100vh !important;
  min-height: unset !important;
  background-color: white !important;
  z-index: 9999 !important;
  border: none !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

iframe:-webkit-full-screen::backdrop {
  background-color: black;
}

/* Firefox全屏支持 */
iframe:-moz-full-screen {
  width: 100vw !important;
  height: 100vh !important;
  min-height: unset !important;
  background-color: white !important;
  z-index: 9999 !important;
  border: none !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

iframe:-moz-full-screen::backdrop {
  background-color: black;
}

/* 打印样式 */
@media print {
  .web-preview-modal,
  .web-preview-btn {
    display: none !important;
  }
  
  .thumbnail-preview {
    break-inside: avoid;
  }
}