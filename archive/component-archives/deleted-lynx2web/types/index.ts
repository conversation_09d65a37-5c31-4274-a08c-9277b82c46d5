/**
 * Lynx2Web模块类型定义
 */

// 预览结果类型
export interface PreviewResult {
  success: boolean;
  html?: string;
  error?: string;
  message?: string;
  screenshot?: ScreenshotData;
}

// 截图数据类型
export interface ScreenshotData {
  url: string;
  width: number;
  height: number;
  format?: string;
  size?: number;
}

// 转换配置类型
export interface ConversionOptions {
  timeout?: number;
  targetWidth?: number;
  targetHeight?: number;
  enableScreenshot?: boolean;
}

// Lynx内容检测结果
export interface LynxDetectionResult {
  hasLynxContent: boolean;
  confidence: number;
  detectedFeatures: string[];
}

// Worker消息类型
export interface WorkerMessage {
  type: 'CONVERT' | 'HEALTH_CHECK';
  content?: string;
  resultId?: string;
  options?: ConversionOptions;
}

// Worker响应类型
export interface WorkerResponse {
  success: boolean;
  html?: string;
  error?: string;
  message?: string;
  processingTime?: number;
}

// 预览状态类型
export type PreviewStatus =
  | 'idle'
  | 'detecting'
  | 'converting'
  | 'capturing'
  | 'success'
  | 'error';

// 预览组件Props类型
export interface WebPreviewButtonProps {
  result: {
    id: string;
    extractedContent: string;
    status: string;
    playgroundUrl?: string;
  };
  onPreviewGenerated?: (resultId: string, preview: PreviewResult) => void;
  disabled?: boolean;
}

export interface ThumbnailPreviewProps {
  preview: PreviewResult;
  resultId: string;
  width?: number;
  height?: number;
}

export interface WebPreviewModalProps {
  preview: PreviewResult;
  resultId: string;
  isOpen: boolean;
  onClose: () => void;
}

// 服务配置类型
export interface WebPreviewConfig {
  workerPath: string;
  defaultTimeout: number;
  thumbnailSize: {
    width: number;
    height: number;
  };
  screenshotOptions: {
    format: string;
    quality: number;
    maxSize: number;
  };
}
