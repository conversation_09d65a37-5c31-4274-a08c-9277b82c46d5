/**
 * Lynx内容验证器
 * 检测内容是否包含有效的Lynx代码
 */

import type { LynxDetectionResult } from '../types';

export class LynxValidator {
  // Lynx特征指示器
  private static readonly LYNX_INDICATORS = {
    // 文件结构指示器
    structure: ['<FILES>', '<FILE'],

    // TTML标签指示器
    tags: ['<view', '<text', '<image', '<scroll-view', '<list', '<list-item'],

    // TTML属性指示器
    attributes: [
      'tt:for',
      'tt:if',
      'tt:key',
      'bindtap',
      'bindlongpress',
      'catchtap',
    ],

    // TTSS样式指示器
    styles: ['rpx', 'enable-scroll', 'scroll-x', 'scroll-y'],

    // JavaScript指示器
    scripts: ['Card(', 'setData', 'onLoad', 'onShow', 'onHide'],
  };

  /**
   * 检测内容是否包含Lynx代码
   */
  static hasLynxContent(content: string): boolean {
    console.log('🔍 [LynxValidator.hasLynxContent] 开始Lynx内容检测');
    console.log('📄 输入内容信息:', {
      hasContent: !!content,
      type: typeof content,
      length: content?.length || 0,
    });

    if (!content || typeof content !== 'string') {
      console.warn('⚠️ [LynxValidator.hasLynxContent] 内容无效');
      return false;
    }

    console.log('🔍 [LynxValidator.hasLynxContent] 执行特征检测');
    const result = this.detectLynxFeatures(content);
    console.log('📋 [LynxValidator.hasLynxContent] 检测结果:', {
      hasLynxContent: result.hasLynxContent,
      confidence: result.confidence,
      featuresCount: result.detectedFeatures.length,
    });

    return result.hasLynxContent;
  }

  /**
   * 详细检测Lynx特征
   */
  static detectLynxFeatures(content: string): LynxDetectionResult {
    console.log('🔍 [LynxValidator.detectLynxFeatures] 开始详细特征检测');

    if (!content || typeof content !== 'string') {
      console.warn('⚠️ [LynxValidator.detectLynxFeatures] 内容无效');
      return {
        hasLynxContent: false,
        confidence: 0,
        detectedFeatures: [],
      };
    }

    console.log('📝 [LynxValidator.detectLynxFeatures] 标准化内容');
    const normalizedContent = content.toLowerCase();
    const detectedFeatures: string[] = [];
    let totalScore = 0;
    let maxScore = 0;

    console.log('🔍 [LynxValidator.detectLynxFeatures] 开始检测各类特征');

    // 检测各类特征
    Object.entries(this.LYNX_INDICATORS).forEach(([category, indicators]) => {
      console.log(`🔍 [LynxValidator.detectLynxFeatures] 检测${category}特征`);

      const categoryScore = this.calculateCategoryScore(
        normalizedContent,
        indicators,
      );
      const categoryWeight = this.getCategoryWeight(category);

      console.log(`📋 ${category}检测结果:`, {
        score: categoryScore,
        weight: categoryWeight,
        indicators: indicators.length,
      });

      maxScore += categoryWeight;

      if (categoryScore > 0) {
        const weightedScore = categoryScore * categoryWeight;
        totalScore += weightedScore;

        const foundIndicators = indicators.filter(indicator =>
          normalizedContent.includes(indicator.toLowerCase()),
        );

        console.log(`✓ ${category}找到特征:`, foundIndicators);
        detectedFeatures.push(...foundIndicators);
      }
    });

    const confidence = maxScore > 0 ? totalScore / maxScore : 0;
    const hasLynxContent = confidence > 0.3; // 30%的置信度阈值

    console.log('📊 [LynxValidator.detectLynxFeatures] 最终检测结果:', {
      totalScore,
      maxScore,
      confidence: `${(confidence * 100).toFixed(1)}%`,
      hasLynxContent,
      threshold: '30%',
      detectedFeaturesCount: detectedFeatures.length,
      uniqueFeatures: [...new Set(detectedFeatures)].length,
    });

    return {
      hasLynxContent,
      confidence,
      detectedFeatures: [...new Set(detectedFeatures)], // 去重
    };
  }

  /**
   * 计算分类得分
   */
  private static calculateCategoryScore(
    content: string,
    indicators: string[],
  ): number {
    let score = 0;

    indicators.forEach(indicator => {
      if (content.includes(indicator.toLowerCase())) {
        score += 1;
      }
    });

    // 返回该分类的得分比例
    return indicators.length > 0 ? score / indicators.length : 0;
  }

  /**
   * 获取分类权重
   */
  private static getCategoryWeight(category: string): number {
    const weights: Record<string, number> = {
      structure: 0.4, // 文件结构最重要
      tags: 0.3, // TTML标签次重要
      attributes: 0.2, // TTML属性
      styles: 0.1, // TTSS样式
      scripts: 0.1, // JavaScript特征
    };

    return weights[category] || 0.1;
  }

  /**
   * 验证内容是否适合转换
   */
  static validateForConversion(content: string): {
    isValid: boolean;
    reason?: string;
    suggestions?: string[];
  } {
    if (!content || typeof content !== 'string') {
      return {
        isValid: false,
        reason: '内容为空或格式无效',
      };
    }

    const detection = this.detectLynxFeatures(content);

    if (!detection.hasLynxContent) {
      return {
        isValid: false,
        reason: '未检测到Lynx代码',
        suggestions: [
          '确保内容包含<view>、<text>等Lynx标签',
          '检查是否包含<FILES>或<FILE>标签结构',
          '确认内容来源是否为Lynx代码生成结果',
        ],
      };
    }

    if (detection.confidence < 0.5) {
      return {
        isValid: false,
        reason: `Lynx代码置信度较低 (${(detection.confidence * 100).toFixed(1)}%)`,
        suggestions: [
          '内容可能不完整，请检查是否为完整的Lynx代码',
          '确认内容包含足够的Lynx特征标识',
        ],
      };
    }

    return {
      isValid: true,
    };
  }

  /**
   * 预处理内容
   * 清理和标准化内容格式
   */
  static preprocessContent(content: string): string {
    console.log('🔧 [LynxValidator.preprocessContent] 开始预处理内容');
    console.log('📄 原始内容长度:', content?.length || 0);

    if (!content) {
      console.warn('⚠️ [LynxValidator.preprocessContent] 内容为空');
      return '';
    }

    let processed = content;

    console.log('🧹 [LynxValidator.preprocessContent] 清理转义字符');
    const beforeCleanup = processed.length;

    // 清理转义字符
    processed = processed
      .replace(/\\n/g, '\n')
      .replace(/\\"/g, '"')
      .replace(/\\</g, '<')
      .replace(/\\>/g, '>');

    console.log('📋 转义字符清理结果:', {
      before: beforeCleanup,
      after: processed.length,
      changes: beforeCleanup !== processed.length,
    });

    // 修复常见的格式问题
    console.log('🔧 [LynxValidator.preprocessContent] 修复常见格式问题');
    const beforeFix = processed.length;
    processed = this.fixCommonIssues(processed);

    console.log('📋 格式修复结果:', {
      before: beforeFix,
      after: processed.length,
      changes: beforeFix !== processed.length,
    });

    console.log('✅ [LynxValidator.preprocessContent] 预处理完成');
    console.log('📊 预处理总结:', {
      originalLength: content.length,
      finalLength: processed.length,
      reduction: content.length - processed.length,
    });

    return processed;
  }

  /**
   * 修复常见的格式问题
   */
  private static fixCommonIssues(content: string): string {
    let fixed = content;

    // 修复未闭合的<FILES>标签
    if (fixed.includes('<FILES>') && !fixed.includes('</FILES>')) {
      fixed += '\n</FILES>';
    }

    // 修复未闭合的<FILE>标签
    const fileOpenCount = (fixed.match(/<FILE/g) || []).length;
    const fileCloseCount = (fixed.match(/<\/FILE>/g) || []).length;

    if (fileOpenCount > fileCloseCount) {
      const missing = fileOpenCount - fileCloseCount;
      for (let i = 0; i < missing; i++) {
        fixed += '\n</FILE>';
      }
    }

    return fixed;
  }
}
