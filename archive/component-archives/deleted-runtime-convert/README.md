# Runtime Convert

> 🚀 纯浏览器端TTML/TTSS转换引擎

一个完全独立、可发布为npm包的TTML/TTSS转换引擎，专为浏览器环境设计，提供100%兼容的Lynx语法支持。

## ✨ 特性

- 🌐 **100% 浏览器端运行** - 无Node.js依赖，纯JavaScript实现
- 🎯 **完整Lynx语法支持** - 支持所有TTML/TTSS语法和指令
- ⚡ **高性能AST解析** - 优化的词法分析和语法树构建
- 🔄 **智能缓存系统** - LRU缓存策略，提升转换性能
- 📝 **TypeScript支持** - 完整的类型定义和智能提示
- 🎨 **CSS作用域化** - 自动添加作用域，避免样式冲突
- 🔧 **高内聚低耦合** - 模块化设计，易于集成和扩展
- 📦 **零依赖设计** - 不依赖任何第三方库

## 🚀 快速开始

### 基础使用

```javascript
import { RuntimeConverter } from './runtime-convert';

// 创建转换器实例
const converter = new RuntimeConverter({
  rpxMode: 'vw',
  designWidth: 750,
  enableCache: true
});

// 转换TTML/TTSS文件
const result = await converter.convert({
  ttml: `
    <view class="container">
      <text class="title">{{title}}</text>
      <button bindtap="handleClick">点击我</button>
    </view>
  `,
  ttss: `
    .container { 
      padding: 32rpx; 
      background: #f5f5f5; 
    }
    .title { 
      font-size: 36rpx; 
      color: #333; 
    }
  `,
  js: `
    Page({
      data: {
        title: 'Hello Runtime Convert'
      },
      
      handleClick() {
        this.setData({
          title: '点击成功！'
        });
      }
    });
  `
});

if (result.success) {
  // 在iframe中显示转换结果
  document.getElementById('preview').srcdoc = result.html;
  console.log('转换成功:', result.metadata);
} else {
  console.error('转换失败:', result.message);
}
```

### 便捷函数

```javascript
import { convertFiles, validateTTML } from './runtime-convert';

// 快速转换（无缓存）
const result = await convertFiles({
  ttml: '<view><text>Hello</text></view>'
});

// 验证TTML语法
const validation = validateTTML('<view><text>Content</text></view>');
if (!validation.valid) {
  console.error('语法错误:', validation.errors);
}
```

## 📖 API 文档

### RuntimeConverter

主要的转换器类。

#### 构造函数

```typescript
new RuntimeConverter(config?: TransformConfig)
```

**配置选项 (TransformConfig):**

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `rpxMode` | `'vw' \| 'rem' \| 'px' \| 'calc'` | `'vw'` | RPX单位转换模式 |
| `designWidth` | `number` | `750` | 设计稿宽度 |
| `componentId` | `string` | 自动生成 | 组件唯一ID |
| `enableScope` | `boolean` | `true` | 是否启用CSS作用域 |
| `enableCache` | `boolean` | `true` | 是否启用缓存 |
| `maxCacheSize` | `number` | `100` | 最大缓存数量 |
| `enableOptimization` | `boolean` | `true` | 是否启用优化 |
| `usingComponents` | `Record<string, string>` | `{}` | 自定义组件映射 |

#### 主要方法

**convert(files: InputFiles): Promise<TransformResult>**

转换TTML/TTSS文件为Web预览。

```typescript
interface InputFiles {
  ttml?: string;    // TTML模板代码
  ttss?: string;    // TTSS样式代码
  js?: string;      // JavaScript代码
  json?: string;    // 配置JSON
}

interface TransformResult {
  success: boolean;
  html?: string;        // 生成的完整HTML
  jsx?: string;         // JSX代码
  css?: string;         // 处理后的CSS
  js?: string;          // 处理后的JS
  error?: string;       // 错误类型
  message?: string;     // 错误消息
  metadata?: {
    transformTime: number;      // 转换耗时(ms)
    componentId: string;        // 组件ID
    elementCount: number;       // 元素数量
    cssRuleCount: number;       // CSS规则数量
    hasUserScript: boolean;     // 是否包含用户脚本
    cacheHit?: boolean;         // 是否命中缓存
  };
}
```

**其他方法:**

- `getConfig()`: 获取当前配置
- `updateConfig(config)`: 更新配置
- `clearCache()`: 清空缓存
- `dispose()`: 销毁实例

### 工具函数

**createConverter(config?: TransformConfig): RuntimeConverter**

创建转换器实例的工厂函数。

**convertFiles(files: InputFiles, config?: TransformConfig): Promise<TransformResult>**

快速转换函数，不使用缓存。

**validateTTML(ttml: string): { valid: boolean; errors: string[] }**

验证TTML语法。

**validateTTSS(ttss: string): { valid: boolean; errors: string[] }**

验证TTSS语法。

## 🎯 支持的语法

### TTML标签

#### 基础组件
- `<view>` → `<div>` - 视图容器
- `<text>` → `<span>` - 文本组件
- `<image>` → `<img>` - 图片组件
- `<button>` → `<button>` - 按钮组件

#### 表单组件
- `<input>` → `<input>` - 输入框
- `<textarea>` → `<textarea>` - 多行输入
- `<switch>` → `<input type="checkbox">` - 开关
- `<slider>` → `<input type="range">` - 滑块
- `<picker>` → `<select>` - 选择器

#### 媒体组件
- `<video>` → `<video>` - 视频播放器
- `<audio>` → `<audio>` - 音频播放器
- `<canvas>` → `<canvas>` - 画布

#### 布局组件
- `<scroll-view>` → `<div>` - 滚动视图
- `<swiper>` → `<div>` - 轮播容器
- `<swiper-item>` → `<div>` - 轮播项

### 指令系统

#### 条件渲染
```html
<view lx:if="{{visible}}">显示内容</view>
<view lx:elif="{{type === 'A'}}">类型A</view>
<view lx:else>其他类型</view>
```

#### 列表渲染
```html
<!-- 基础列表 -->
<view lx:for="item in items">
  <text>{{item.name}}</text>
</view>

<!-- 带索引的列表 -->
<view lx:for="item, index in items" lx:key="{{item.id}}">
  <text>{{index}}: {{item.name}}</text>
</view>
```

### 事件系统

#### 冒泡事件 (bind*)
```html
<button bindtap="handleClick">点击</button>
<input bindinput="handleInput" bindfocus="handleFocus" />
<view bindtouchstart="onTouchStart" bindtouchend="onTouchEnd" />
```

#### 捕获事件 (catch:*)
```html
<button catch:tap="handleClick">点击（阻止冒泡）</button>
<view catch:touchmove="preventScroll">阻止滚动</view>
```

### 样式系统 (TTSS)

#### RPX单位
```css
.container {
  width: 750rpx;        /* 转换为: 100vw */
  height: 200rpx;       /* 转换为: 26.666667vw */
  padding: 32rpx;       /* 转换为: 4.266667vw */
}
```

#### 作用域化
```css
/* 原始CSS */
.title { color: red; }

/* 转换后 */
[data-v-abc123] .title { color: red; }
```

### 模板语法

#### 插值表达式
```html
<text>用户名: {{user.name}}</text>
<text>总计: {{price * quantity}}</text>
<image src="{{avatarUrl}}" alt="头像" />
```

#### 属性绑定
```html
<view class="container {{active ? 'active' : ''}}">
<input value="{{inputValue}}" placeholder="{{placeholder}}" />
<button disabled="{{loading}}">{{loading ? '加载中' : '提交'}}</button>
```

## 🔧 高级配置

### RPX转换模式

```javascript
const converter = new RuntimeConverter({
  rpxMode: 'vw',        // viewport width (默认)
  designWidth: 750      // 设计稿宽度
});

// 其他模式:
// 'rem' - 转换为rem单位 (1rpx = 1/37.5 rem)
// 'px' - 转换为固定像素 (基于当前视口)
// 'calc' - 使用CSS calc()函数
```

### 自定义组件

```javascript
const converter = new RuntimeConverter({
  usingComponents: {
    'user-card': './components/UserCard',
    'product-list': './components/ProductList'
  }
});

// TTML中使用:
// <user-card name="{{user.name}}" avatar="{{user.avatar}}" />
```

### 缓存配置

```javascript
const converter = new RuntimeConverter({
  enableCache: true,
  maxCacheSize: 200,    // 最大缓存200个结果
});

// 获取缓存统计
const stats = converter.getCacheStats();
console.log('缓存命中率:', stats.hitRate);

// 清空缓存
converter.clearCache();
```

## 🧪 测试

### 运行测试

```bash
# 在浏览器中打开测试页面
open tests/test-runner.html

# 或访问带参数的URL自动运行
open tests/test-runner.html?autoRun=true
```

### 测试类别

- **基础功能测试** - 标签转换、属性处理
- **指令系统测试** - 条件渲染、列表渲染
- **事件系统测试** - 各种事件绑定
- **样式系统测试** - RPX转换、作用域化
- **模板语法测试** - 插值表达式、属性绑定
- **组件系统测试** - 自定义组件
- **错误处理测试** - 各种异常情况
- **性能测试** - 大量数据转换
- **边界情况测试** - 特殊字符、极端情况

### 编写自定义测试

```javascript
import { TEST_CASES, getTestCasesByCategory } from './tests/test-cases';

// 获取特定类别的测试用例
const basicTests = getTestCasesByCategory('basic');

// 运行单个测试用例
const testCase = TEST_CASES[0];
const result = await converter.convert(testCase.input);

if (testCase.shouldThrow) {
  // 期望抛出错误的测试
} else {
  // 期望成功的测试
  assert(result.success === testCase.expected.success);
}
```

## 📊 性能指标

### 转换性能

| 场景 | 平均时间 | 最大时间 |
|------|----------|----------|
| 小型页面 (<100 elements) | <50ms | <100ms |
| 中型页面 (100-500 elements) | <200ms | <500ms |
| 大型页面 (500-1000 elements) | <800ms | <1500ms |

### 内存使用

- **基础转换器实例**: ~1MB
- **缓存 (100项)**: ~5MB
- **单次转换**: ~100KB

## 🛠️ 开发指南

### 项目结构

```
runtime_convert/
├── core/                   # 核心API
│   └── index.ts           # 主要接口
├── parsers/               # 解析器
│   ├── lexer.ts          # 词法分析器
│   └── ast-builder.ts    # AST构建器
├── transformers/          # 转换器
│   └── ast-transformer.ts # AST转换器
├── processors/            # 处理器
│   ├── css-processor.ts  # CSS处理器
│   └── code-generator.ts # 代码生成器
├── utils/                 # 工具函数
│   └── constants.ts      # 常量配置
├── types/                 # 类型定义
│   └── index.ts          # TypeScript类型
├── tests/                 # 测试文件
│   ├── test-runner.html  # 测试运行器
│   └── test-cases.ts     # 测试用例
├── examples/              # 示例代码
│   └── basic-usage.html  # 基础使用示例
└── index.ts              # 包入口文件
```

### 扩展指南

#### 添加新标签支持

1. 在 `utils/constants.ts` 中添加元素映射:

```typescript
export const ELEMENT_MAPPING = {
  // 现有映射...
  'new-element': {
    tag: 'div',
    props: { className: 'lynx-new-element' },
    attributeMapping: {
      'custom-attr': 'data-custom'
    }
  }
};
```

2. 如果是自闭合标签，添加到 `SELF_CLOSING_TAGS`:

```typescript
export const SELF_CLOSING_TAGS = new Set([
  // 现有标签...
  'new-element'
]);
```

#### 添加新指令支持

在 `transformers/ast-transformer.ts` 中扩展指令处理逻辑:

```typescript
private transformDirectiveElement(node: ElementNode, context: TransformContext): JSXNode {
  const { attributes } = node;

  // 添加新指令处理
  if (attributes['lx:custom']) {
    return this.transformCustomDirective(node, context);
  }

  // 现有指令处理...
}
```

#### 添加新CSS特性

在 `processors/css-processor.ts` 中扩展CSS处理:

```typescript
private preprocessCSS(css: string): string {
  let processed = css;

  // 添加新的CSS预处理逻辑
  processed = this.handleCustomCSSFeature(processed);

  return processed;
}
```

## 🔍 故障排除

### 常见问题

**Q: 转换失败，提示"TTML content is required"**
A: 确保传入的files对象包含非空的ttml属性。

**Q: RPX单位没有转换**
A: 检查rpxMode配置是否正确，确保TTSS内容包含有效的rpx单位。

**Q: 自定义组件没有被识别**
A: 确保在配置中正确设置了usingComponents映射。

**Q: 事件处理器不工作**
A: 检查事件名称是否正确，确保使用了支持的事件类型。

### 调试技巧

1. **启用调试模式**:

```javascript
import { createDebugConverter } from './runtime-convert';

const converter = createDebugConverter({
  debug: true,
  enableCache: false
});
```

2. **检查转换结果**:

```javascript
const result = await converter.convert(files);
console.log('转换元数据:', result.metadata);
console.log('生成的JSX:', result.jsx);
console.log('处理后的CSS:', result.css);
```

3. **验证语法**:

```javascript
import { validateTTML, validateTTSS } from './runtime-convert';

const tttmlValidation = validateTTML(files.ttml);
if (!tttmlValidation.valid) {
  console.error('TTML语法错误:', tttmlValidation.errors);
}
```

## 📝 更新日志

### v1.0.0 (2024-06-24)

🎉 **首次发布**

- ✅ 完整的TTML/TTSS语法支持
- ✅ 纯浏览器端运行
- ✅ 高性能AST解析引擎
- ✅ 智能缓存系统
- ✅ TypeScript完整支持
- ✅ 全面的测试覆盖
- ✅ 丰富的示例和文档

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交Issue和Pull Request！

### 开发环境

1. Clone项目
2. 在浏览器中打开 `examples/basic-usage.html`
3. 开始开发和测试

### 提交规范

- feat: 新功能
- fix: Bug修复
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建工具或辅助工具的变动

## 📞 支持

- 📧 Email: [<EMAIL>](mailto:<EMAIL>)
- 🐛 Issues: [GitHub Issues](https://github.com/search-so-ai/runtime-convert/issues)
- 📖 文档: [在线文档](https://runtime-convert.dev/docs)

---

**Runtime Convert** - 让TTML/TTSS在浏览器中自由运行 🚀