/**
 * @package runtime-convert
 * @description Batch Processor 兼容性适配器
 * 纯 runtime_convert 数据流适配器
 */

import type {
  PreviewResult,
  ConversionOptions,
  EnhancedWebPreviewService,
} from '../integration/web-preview-service';
import type { ProcessResult } from '../../types';

/**
 * Batch Processor 适配器
 * 将 batch processor 中的数据格式适配到 runtime_convert
 */
export class BatchProcessorAdapter {
  private previewService: EnhancedWebPreviewService;

  constructor(previewService: EnhancedWebPreviewService) {
    this.previewService = previewService;
  }

  /**
   * 适配 InteractiveIframe 中的转换流程
   * @param result ProcessResult from batch processor
   * @param enableScreenshot 是否启用截图
   * @param targetWidth 目标宽度
   * @param targetHeight 目标高度
   */
  async convertForInteractiveIframe(
    result: ProcessResult,
    enableScreenshot = false,
    targetWidth = 280,
    targetHeight = 500,
  ): Promise<PreviewResult> {
    console.log('🔄 [BatchProcessorAdapter] 开始适配 InteractiveIframe 转换');

    try {
      // 1. 从 ProcessResult.metadata.extractedContent 获取内容
      const extractedContent = result.metadata?.extractedContent;

      if (!extractedContent) {
        console.warn('⚠️ [BatchProcessorAdapter] 没有找到 extractedContent');
        return {
          success: false,
          error: 'NO_CONTENT',
          message: '没有找到可转换的内容',
        };
      }

      console.log(
        '📝 [BatchProcessorAdapter] 提取的内容长度:',
        extractedContent.length,
      );

      // 2. 配置转换选项 - 默认启用Worker模式
      const conversionOptions: ConversionOptions = {
        timeout: 8000,
        enableScreenshot,
        enableWorker: true, // 启用Worker模式以避免阻塞UI
        screenshotOptions: {
          width: targetWidth,
          height: targetHeight,
          quality: 0.8,
        },
      };

      console.log('⚙️ [BatchProcessorAdapter] 转换选项:', conversionOptions);

      // 3. 执行转换
      const previewResult = await this.previewService.convertToWebPreview(
        extractedContent,
        result.id,
        conversionOptions,
      );

      console.log('✅ [BatchProcessorAdapter] 转换完成:', {
        success: previewResult.success,
        hasHtml: !!previewResult.html,
        hasScreenshot: !!previewResult.screenshot,
        metadata: previewResult.metadata,
      });

      return previewResult;
    } catch (error) {
      console.error('❌ [BatchProcessorAdapter] 转换失败:', error);

      return {
        success: false,
        error: 'CONVERSION_ERROR',
        message: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 适配 EnhancedBatchProcessorService 中的流解析数据
   * @param parsedContent 从 streamParser 解析的内容
   * @param resultId 结果ID
   */
  async convertParsedContent(
    parsedContent: string,
    resultId: string,
  ): Promise<PreviewResult> {
    console.log('🔄 [BatchProcessorAdapter] 开始适配解析内容转换');

    try {
      // 检测内容类型
      const isHTML = this.detectHTMLContent(parsedContent);

      console.log('🔍 [BatchProcessorAdapter] 内容类型检测:', {
        isHTML,
        contentLength: parsedContent.length,
        contentPreview: parsedContent.substring(0, 200),
      });

      // 如果是HTML内容，直接返回
      if (isHTML) {
        return {
          success: true,
          html: this.wrapHTMLContent(parsedContent),
          message: '直接使用HTML内容',
        };
      }

      // 否则作为TTML内容处理 - 启用Worker模式
      const conversionOptions: ConversionOptions = {
        timeout: 15000,
        enableScreenshot: false, // 批处理时通常不需要截图
        enableWorker: true, // 启用Worker模式避免阻塞主线程
      };

      return await this.previewService.convertToWebPreview(
        parsedContent,
        resultId,
        conversionOptions,
      );
    } catch (error) {
      console.error('❌ [BatchProcessorAdapter] 转换失败:', error);

      // 直接展示语法错误给用户
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: 'SYNTAX_ERROR',
        message: `TTML语法错误:\n${errorMessage}`,
      };
    }
  }

  /**
   * 检测是否为HTML内容
   */
  private detectHTMLContent(content: string): boolean {
    const htmlIndicators = [
      '<!DOCTYPE html>',
      '<html',
      '<head>',
      '<body>',
      '<div',
      '<span',
      '<p>',
      '<h1>',
      '<h2>',
      '<h3>',
    ];

    return htmlIndicators.some(indicator =>
      content.toLowerCase().includes(indicator.toLowerCase()),
    );
  }

  /**
   * 包装HTML内容
   */
  private wrapHTMLContent(htmlContent: string): string {
    // 如果已经是完整的HTML文档
    if (
      htmlContent.includes('<!DOCTYPE html>') ||
      htmlContent.includes('<html')
    ) {
      return htmlContent;
    }

    // 否则包装成完整文档
    return `
      <!DOCTYPE html>
      <html lang="zh-CN">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Batch Processing Preview</title>
          <style>
            body {
              margin: 0;
              padding: 16px;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
              line-height: 1.6;
            }
            .preview-container {
              max-width: 100%;
              overflow-x: auto;
            }
          </style>
        </head>
        <body>
          <div class="preview-container">
            ${htmlContent}
          </div>
        </body>
      </html>
    `;
  }

  /**
   * 获取适配器统计信息
   */
  getAdapterStats(): {
    previewServiceStats: ReturnType<EnhancedWebPreviewService['getStats']>;
    adaptersCreated: number;
  } {
    return {
      previewServiceStats: this.previewService.getStats(),
      adaptersCreated: 1, // 简单计数
    };
  }

  /**
   * 清理资源
   */
  dispose(): void {
    // 适配器本身不拥有 previewService，不需要销毁
    console.log('🧹 [BatchProcessorAdapter] 适配器已清理');
  }
}

/**
 * 创建适配器工厂函数
 */
export function createBatchProcessorAdapter(
  previewService: EnhancedWebPreviewService,
): BatchProcessorAdapter {
  return new BatchProcessorAdapter(previewService);
}

/**
 * 全局适配器实例管理（可选）
 */
let globalAdapter: BatchProcessorAdapter | null = null;

export function getGlobalBatchProcessorAdapter(
  previewService?: EnhancedWebPreviewService,
): BatchProcessorAdapter {
  if (!globalAdapter && previewService) {
    globalAdapter = new BatchProcessorAdapter(previewService);
  }

  if (!globalAdapter) {
    throw new Error(
      'BatchProcessorAdapter not initialized. Please provide a previewService.',
    );
  }

  return globalAdapter;
}

export function resetGlobalBatchProcessorAdapter(): void {
  if (globalAdapter) {
    globalAdapter.dispose();
    globalAdapter = null;
  }
}
