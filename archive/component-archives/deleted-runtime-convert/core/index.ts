/**
 * @package runtime-convert
 * @description 主要API接口 - 纯浏览器端TTML/TTSS转换引擎
 * @version 1.0.0
 * <AUTHOR> Code Development Team
 */

import type {
  InputFiles,
  TransformResult,
  TransformConfig,
  TransformMetadata,
  CacheEntry,
  ErrorType,
} from '../types';

import { <PERSON>rowser<PERSON>exer } from '../parsers/lexer';
import { BrowserASTBuilder } from '../parsers/ast-builder';
import { BrowserASTTransformer } from '../transformers/ast-transformer';
import { BrowserCSSProcessor } from '../processors/css-processor';
import { BrowserCodeGenerator } from '../processors/code-generator';
import { DEFAULT_CONFIG } from '../utils/constants';

/**
 * 主转换引擎类
 */
export class RuntimeConverter {
  private config: Required<TransformConfig>;
  private lexer: BrowserLexer;
  private astBuilder: BrowserASTBuilder;
  private transformer: BrowserASTTransformer;
  private cssProcessor: BrowserCSSProcessor;
  private codeGenerator: BrowserCodeGenerator;

  // 缓存系统
  private cache = new Map<string, CacheEntry>();
  private cacheKeys: string[] = [];

  constructor(config: TransformConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };

    // 如果没有提供componentId，生成一个
    if (!this.config.componentId) {
      this.config.componentId = this.generateComponentId();
    }

    // 初始化各个处理器
    this.lexer = new BrowserLexer();
    this.astBuilder = new BrowserASTBuilder();
    this.transformer = new BrowserASTTransformer(this.config);
    this.cssProcessor = new BrowserCSSProcessor(this.config);
    this.codeGenerator = new BrowserCodeGenerator(this.config);
  }

  /**
   * 转换TTML/TTSS文件为Web预览
   * 这是主要的对外API接口
   */
  async convert(files: InputFiles): Promise<TransformResult> {
    const startTime = performance.now();

    try {
      // 1. 输入验证
      this.validateInput(files);

      // 2. 检查缓存
      const cacheKey = this.generateCacheKey(files);
      if (this.config.enableCache && this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey)!;
        return {
          ...cached.value,
          metadata: {
            ...cached.value.metadata!,
            cacheHit: true,
          },
        };
      }

      // 3. 解析配置
      const lynxConfig = this.parseConfig(files.json);
      this.updateComponentConfig(lynxConfig);

      // 4. 转换TTML
      const jsxResult = await this.transformTTML(files.ttml || '');

      // 5. 转换TTSS
      const cssResult = await this.transformTTSS(files.ttss || '');

      // 6. 处理JavaScript
      const jsResult = await this.transformJS(files.js || '');

      // 7. 生成完整HTML
      const html = this.codeGenerator.generateHTML(
        jsxResult,
        cssResult,
        jsResult,
      );

      const endTime = performance.now();

      const result: TransformResult = {
        success: true,
        html,
        jsx: jsxResult.code,
        css: cssResult.scopedCode,
        js: jsResult.code,
        metadata: {
          transformTime: endTime - startTime,
          componentId: this.config.componentId,
          elementCount: this.countElements(jsxResult.ast),
          cssRuleCount: cssResult.ruleCount,
          hasUserScript: jsResult.hasUserCode,
          cacheHit: false,
        },
      };

      // 8. 缓存结果
      if (this.config.enableCache) {
        this.setCache(cacheKey, result);
      }

      return result;
    } catch (error) {
      const endTime = performance.now();

      return {
        success: false,
        error: this.getErrorType(error),
        message: error instanceof Error ? error.message : String(error),
        metadata: {
          transformTime: endTime - startTime,
          componentId: this.config.componentId,
          elementCount: 0,
          cssRuleCount: 0,
          hasUserScript: false,
          cacheHit: false,
        },
      };
    }
  }

  /**
   * 转换TTML代码
   */
  private async transformTTML(ttml: string): Promise<{
    code: string;
    ast: any;
    componentId: string;
  }> {
    if (!ttml?.trim()) {
      const emptyJSX =
        'React.createElement("div", { className: "lynx-empty" }, "暂无内容")';
      return {
        code: emptyJSX,
        ast: { type: 'JSXElement', children: [] },
        componentId: this.config.componentId,
      };
    }

    // 1. 词法分析
    const tokens = this.lexer.tokenize(ttml);

    // 2. 构建AST
    const ast = this.astBuilder.build(tokens);

    // 3. 转换为JSX
    const jsx = this.transformer.transform(ast);

    // 4. 生成代码
    const code = this.codeGenerator.generateJSXCode(jsx);

    return {
      code,
      ast: jsx,
      componentId: this.config.componentId,
    };
  }

  /**
   * 转换TTSS代码
   */
  private async transformTTSS(ttss: string): Promise<{
    code: string;
    scopedCode: string;
    classes: string[];
    ruleCount: number;
  }> {
    const result = this.cssProcessor.process(ttss, this.config.componentId);

    return {
      code: result.code,
      scopedCode: result.scopedCode,
      classes: result.classes,
      ruleCount: result.ruleCount,
    };
  }

  /**
   * 转换JavaScript代码
   */
  private async transformJS(js: string): Promise<{
    code: string;
    hasUserCode: boolean;
  }> {
    if (!js?.trim()) {
      return {
        code: '',
        hasUserCode: false,
      };
    }

    // 简单的JS转换处理
    let processedJS = js;

    // 处理小程序语法
    processedJS = processedJS.replace(
      /Page\s*\(\s*\{/g,
      'const pageConfig = {',
    );
    processedJS = processedJS.replace(
      /Component\s*\(\s*\{/g,
      'const componentConfig = {',
    );
    processedJS = processedJS.replace(/App\s*\(\s*\{/g, 'const appConfig = {');

    // 处理this引用（在运行时会被替换）
    // 这里保持原样，在代码生成器中处理

    return {
      code: processedJS,
      hasUserCode: true,
    };
  }

  /**
   * 解析配置文件
   */
  private parseConfig(configJson?: string): any {
    if (!configJson?.trim()) {
      return {};
    }

    try {
      return JSON.parse(configJson);
    } catch (error) {
      console.warn('Invalid config JSON:', error);
      return {};
    }
  }

  /**
   * 更新组件配置
   */
  private updateComponentConfig(lynxConfig: any): void {
    if (lynxConfig.usingComponents) {
      this.config.usingComponents = {
        ...this.config.usingComponents,
        ...lynxConfig.usingComponents,
      };
      this.transformer.updateConfig({
        usingComponents: this.config.usingComponents,
      });
    }
  }

  /**
   * 输入验证
   */
  private validateInput(files: InputFiles): void {
    if (!files || typeof files !== 'object') {
      throw new Error('Invalid input: files must be an object');
    }

    // 至少需要有TTML内容
    if (!files.ttml?.trim()) {
      throw new Error('Invalid input: TTML content is required');
    }

    // 验证各个文件内容
    if (files.ttml && typeof files.ttml !== 'string') {
      throw new Error('Invalid input: TTML must be a string');
    }

    if (files.ttss && typeof files.ttss !== 'string') {
      throw new Error('Invalid input: TTSS must be a string');
    }

    if (files.js && typeof files.js !== 'string') {
      throw new Error('Invalid input: JS must be a string');
    }

    if (files.json && typeof files.json !== 'string') {
      throw new Error('Invalid input: JSON must be a string');
    }
  }

  /**
   * 计算元素数量
   */
  private countElements(ast: any): number {
    if (!ast) {
      return 0;
    }

    let count = 0;

    const traverse = (node: any) => {
      if (!node) {
        return;
      }

      if (node.type === 'JSXElement') {
        count++;
      }

      if (node.children) {
        node.children.forEach(traverse);
      }
    };

    traverse(ast);
    return count;
  }

  /**
   * 获取错误类型
   */
  private getErrorType(error: any): ErrorType {
    if (error?.type) {
      return error.type;
    }

    if (error?.name === 'LexicalError' || error?.name === 'ASTBuildError') {
      return 'SYNTAX_ERROR' as ErrorType;
    }

    if (error?.name === 'TransformError') {
      return 'TRANSFORM_ERROR' as ErrorType;
    }

    return 'RUNTIME_ERROR' as ErrorType;
  }

  // ===============================
  // 缓存管理
  // ===============================

  /**
   * 生成缓存键
   */
  private generateCacheKey(files: InputFiles): string {
    const content = JSON.stringify({
      ttml: files.ttml || '',
      ttss: files.ttss || '',
      js: files.js || '',
      json: files.json || '',
      config: this.config,
    });

    return this.simpleHash(content);
  }

  /**
   * 设置缓存
   */
  private setCache(key: string, value: TransformResult): void {
    // LRU淘汰策略
    if (this.cache.size >= this.config.maxCacheSize) {
      const oldestKey = this.cacheKeys.shift();
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    const cacheEntry: CacheEntry = {
      key,
      value,
      timestamp: Date.now(),
      size: this.calculateResultSize(value),
    };

    this.cache.set(key, cacheEntry);
    this.cacheKeys.push(key);
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.cache.clear();
    this.cacheKeys = [];
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    totalSize: number;
  } {
    const totalSize = Array.from(this.cache.values()).reduce(
      (sum, entry) => sum + entry.size,
      0,
    );

    return {
      size: this.cache.size,
      maxSize: this.config.maxCacheSize,
      hitRate: 0, // 需要实际追踪命中率
      totalSize,
    };
  }

  // ===============================
  // 配置管理
  // ===============================

  /**
   * 获取当前配置
   */
  getConfig(): Required<TransformConfig> {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<TransformConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // 更新子处理器配置
    this.transformer.updateConfig(this.config);
    this.cssProcessor.updateConfig(this.config);
  }

  /**
   * 重置配置为默认值
   */
  resetConfig(): void {
    this.config = { ...DEFAULT_CONFIG };
    this.updateConfig({});
  }

  // ===============================
  // 工具方法
  // ===============================

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(36);
  }

  /**
   * 计算结果大小
   */
  private calculateResultSize(result: TransformResult): number {
    const str = JSON.stringify(result);
    return new Blob([str]).size;
  }

  /**
   * 生成组件ID
   */
  private generateComponentId(): string {
    return Math.random().toString(36).substr(2, 8);
  }

  /**
   * 销毁实例
   */
  dispose(): void {
    this.clearCache();
    // 清理其他资源
  }
}

// ===============================
// 便捷工厂函数
// ===============================

/**
 * 创建转换器实例
 */
export function createConverter(config?: TransformConfig): RuntimeConverter {
  return new RuntimeConverter(config);
}

/**
 * 快速转换函数（无缓存）
 */
export async function convertFiles(
  files: InputFiles,
  config?: TransformConfig,
): Promise<TransformResult> {
  const converter = new RuntimeConverter({ ...config, enableCache: false });
  try {
    return await converter.convert(files);
  } finally {
    converter.dispose();
  }
}

/**
 * 验证TTML语法
 */
export function validateTTML(ttml: string): {
  valid: boolean;
  errors: string[];
} {
  try {
    const lexer = new BrowserLexer();
    const astBuilder = new BrowserASTBuilder();

    const tokens = lexer.tokenize(ttml);
    const ast = astBuilder.build(tokens);

    return { valid: true, errors: [] };
  } catch (error) {
    return {
      valid: false,
      errors: [error instanceof Error ? error.message : String(error)],
    };
  }
}

/**
 * 验证TTSS语法
 */
export function validateTTSS(ttss: string): {
  valid: boolean;
  errors: string[];
} {
  try {
    const processor = new BrowserCSSProcessor();
    processor.process(ttss);
    return { valid: true, errors: [] };
  } catch (error) {
    return {
      valid: false,
      errors: [error instanceof Error ? error.message : String(error)],
    };
  }
}

// ===============================
// 类型导出
// ===============================

export type {
  InputFiles,
  TransformResult,
  TransformConfig,
  TransformMetadata,
  TransformError,
  ErrorType,
} from '../types';

export {
  ASTNodeType,
  TokenType,
  isElementNode,
  isTextNode,
  isMustacheNode,
  isCommentNode,
} from '../types';

// ===============================
// 默认导出
// ===============================

export default RuntimeConverter;
