<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Runtime Convert - 基础使用示例</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 28px;
        }
        
        .header p {
            margin: 0;
            color: #7f8c8d;
            font-size: 16px;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .demo-panel {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .panel-header {
            padding: 15px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            font-size: 16px;
        }
        
        .panel-content {
            padding: 20px;
        }
        
        .code-editor {
            width: 100%;
            min-height: 300px;
            padding: 15px;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            resize: vertical;
            background: #f8f9fa;
        }
        
        .controls {
            margin: 15px 0;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
            font-weight: 500;
        }
        
        .btn-primary {
            background: #007aff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .preview-frame {
            width: 100%;
            height: 500px;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            background: white;
        }
        
        .result-info {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            font-size: 13px;
            color: #666;
        }
        
        .error-message {
            margin-top: 15px;
            padding: 10px;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            border-radius: 6px;
            font-size: 13px;
        }
        
        .success-message {
            margin-top: 15px;
            padding: 10px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            border-radius: 6px;
            font-size: 13px;
        }
        
        .examples-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .examples-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e1e8ed;
        }
        
        .examples-header h2 {
            margin: 0;
            color: #2c3e50;
            font-size: 20px;
        }
        
        .example-tabs {
            display: flex;
            gap: 5px;
            margin-bottom: 20px;
        }
        
        .tab-btn {
            padding: 8px 16px;
            border: 1px solid #e1e8ed;
            background: #f8f9fa;
            cursor: pointer;
            border-radius: 6px 6px 0 0;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .tab-btn.active {
            background: #007aff;
            color: white;
            border-color: #007aff;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007aff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .metadata {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .metadata-item {
            text-align: center;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .metadata-value {
            font-weight: bold;
            font-size: 16px;
            color: #2c3e50;
        }
        
        .metadata-label {
            font-size: 11px;
            color: #666;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Runtime Convert 基础使用示例</h1>
            <p>纯浏览器端TTML/TTSS转换引擎演示</p>
        </div>
        
        <div class="demo-grid">
            <!-- 输入面板 -->
            <div class="demo-panel">
                <div class="panel-header">📝 输入代码</div>
                <div class="panel-content">
                    <div class="example-tabs">
                        <button class="tab-btn active" data-tab="ttml">TTML</button>
                        <button class="tab-btn" data-tab="ttss">TTSS</button>
                        <button class="tab-btn" data-tab="js">JavaScript</button>
                        <button class="tab-btn" data-tab="json">配置</button>
                    </div>
                    
                    <div class="tab-content active" data-tab="ttml">
                        <textarea id="tttmlInput" class="code-editor" placeholder="输入TTML代码...">
<view class="page">
  <view class="header">
    <text class="title">{{title}}</text>
    <button bindtap="handleClick" class="btn">点击我</button>
  </view>
  
  <view class="content">
    <view lx:for="item in items" class="item">
      <text>{{item.name}}</text>
      <text class="desc">{{item.description}}</text>
    </view>
  </view>
  
  <view lx:if="{{showFooter}}" class="footer">
    <text>页脚内容</text>
  </view>
</view></textarea>
                    </div>
                    
                    <div class="tab-content" data-tab="ttss">
                        <textarea id="ttssInput" class="code-editor" placeholder="输入TTSS样式...">
.page {
  width: 750rpx;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.title {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.btn {
  background: white;
  color: #667eea;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.content {
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
}

.item {
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.item:last-child {
  border-bottom: none;
}

.desc {
  color: #666;
  font-size: 24rpx;
  margin-top: 8rpx;
}

.footer {
  text-align: center;
  margin-top: 20rpx;
  color: #999;
}</textarea>
                    </div>
                    
                    <div class="tab-content" data-tab="js">
                        <textarea id="jsInput" class="code-editor" placeholder="输入JavaScript代码...">
Card({
  data: {
    title: 'Runtime Convert 示例',
    showFooter: true,
    items: [
      {
        name: '基础转换',
        description: '支持所有基础Lynx标签转换'
      },
      {
        name: '样式处理',
        description: 'RPX单位转换和CSS作用域化'
      },
      {
        name: '事件绑定',
        description: '完整的事件系统支持'
      },
      {
        name: '指令系统',
        description: '条件渲染和列表渲染'
      }
    ]
  },
  
  onLoad(options) {
    console.log('页面加载完成', options);
  },
  
  handleClick() {
    this.setData({
      title: '点击成功！',
      showFooter: !this.data.showFooter
    });
    
    console.log('按钮被点击了');
  }
});</textarea>
                    </div>
                    
                    <div class="tab-content" data-tab="json">
                        <textarea id="jsonInput" class="code-editor" placeholder="输入配置JSON...">
{
  "usingComponents": {
    "custom-header": "./components/Header",
    "user-list": "./components/UserList"
  },
  "navigationBarTitleText": "Runtime Convert 示例",
  "backgroundColor": "#f5f5f5"
}</textarea>
                    </div>
                    
                    <div class="controls">
                        <button id="convertBtn" class="btn btn-primary">🔄 转换</button>
                        <button id="clearBtn" class="btn btn-secondary">🗑️ 清空</button>
                        <button id="exampleBtn" class="btn btn-success">📄 加载示例</button>
                    </div>
                    
                    <div id="resultInfo" class="result-info" style="display: none;"></div>
                    <div id="errorMessage" class="error-message" style="display: none;"></div>
                    <div id="successMessage" class="success-message" style="display: none;"></div>
                </div>
            </div>
            
            <!-- 预览面板 -->
            <div class="demo-panel">
                <div class="panel-header">📱 实时预览</div>
                <div class="panel-content">
                    <div id="loadingIndicator" class="loading">
                        <div>请点击"转换"按钮开始转换</div>
                    </div>
                    <iframe id="previewFrame" class="preview-frame" style="display: none;"></iframe>
                </div>
            </div>
        </div>
        
        <!-- 示例集合 -->
        <div class="examples-section">
            <div class="examples-header">
                <h2>🎯 更多示例</h2>
            </div>
            
            <div class="example-tabs">
                <button class="tab-btn active" data-example="basic">基础组件</button>
                <button class="tab-btn" data-example="form">表单组件</button>
                <button class="tab-btn" data-example="list">列表渲染</button>
                <button class="tab-btn" data-example="advanced">高级特性</button>
            </div>
            
            <div class="tab-content active" data-example="basic">
                <button class="btn btn-secondary" onclick="loadExample('basic-components')">加载基础组件示例</button>
                <p>包含view、text、image、button等基础组件的使用示例</p>
            </div>
            
            <div class="tab-content" data-example="form">
                <button class="btn btn-secondary" onclick="loadExample('form-demo')">加载表单示例</button>
                <p>包含input、textarea、switch、slider等表单组件的使用示例</p>
            </div>
            
            <div class="tab-content" data-example="list">
                <button class="btn btn-secondary" onclick="loadExample('list-demo')">加载列表示例</button>
                <p>展示lx:for指令和复杂列表渲染的使用方法</p>
            </div>
            
            <div class="tab-content" data-example="advanced">
                <button class="btn btn-secondary" onclick="loadExample('advanced-features')">加载高级特性</button>
                <p>包含自定义组件、复杂事件处理、动画效果等高级特性</p>
            </div>
        </div>
    </div>

    <script type="module">
        // 模拟RuntimeConverter（在实际使用中会导入真实模块）
        class MockRuntimeConverter {
            async convert(files) {
                // 模拟转换延迟
                await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
                
                // 基础验证
                if (!files.ttml?.trim()) {
                    throw new Error('TTML content is required');
                }
                
                // 模拟语法错误
                if (files.ttml.includes('<invalid>')) {
                    throw new Error('Invalid TTML syntax: unknown tag <invalid>');
                }
                
                // 生成模拟结果
                const html = this.generateMockHTML(files);
                
                return {
                    success: true,
                    html,
                    jsx: 'React.createElement("div", { className: "lynx-view" }, ...children)',
                    css: files.ttss ? this.processMockCSS(files.ttss) : '',
                    js: files.js || '',
                    metadata: {
                        transformTime: Math.random() * 200 + 100,
                        componentId: 'demo-' + Math.random().toString(36).substr(2, 6),
                        elementCount: (files.ttml.match(/<\w+/g) || []).length,
                        cssRuleCount: (files.ttss?.match(/\{/g) || []).length,
                        hasUserScript: !!files.js?.trim(),
                        cacheHit: false
                    }
                };
            }
            
            generateMockHTML(files) {
                const componentId = 'demo-' + Math.random().toString(36).substr(2, 6);
                
                return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Runtime Convert Preview</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        
        /* Lynx基础样式 */
        .lynx-view { display: flex; flex-direction: column; }
        .lynx-text { display: inline-block; }
        .lynx-button { 
            padding: 8px 16px; 
            background: #007aff; 
            color: white; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
        }
        .lynx-button:hover { background: #0056b3; }
        
        /* 转换后的用户样式 */
        ${files.ttss ? this.processMockCSS(files.ttss, componentId) : ''}
    </style>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
</head>
<body>
    <div id="root"></div>
    <script>
        const { useState, useEffect } = React;
        
        function App() {
            const [data, setData] = useState({
                title: 'Runtime Convert 示例',
                showFooter: true,
                items: [
                    { name: '基础转换', description: '支持所有基础Lynx标签转换' },
                    { name: '样式处理', description: 'RPX单位转换和CSS作用域化' },
                    { name: '事件绑定', description: '完整的事件系统支持' },
                    { name: '指令系统', description: '条件渲染和列表渲染' }
                ]
            });
            
            const handleClick = () => {
                setData(prev => ({
                    ...prev,
                    title: '点击成功！',
                    showFooter: !prev.showFooter
                }));
                console.log('按钮被点击了');
            };
            
            useEffect(() => {
                console.log('页面加载完成');
            }, []);
            
            return React.createElement('div', { 
                className: 'page',
                'data-v-${componentId}': ''
            }, [
                React.createElement('div', { 
                    key: 'header',
                    className: 'header'
                }, [
                    React.createElement('span', { 
                        key: 'title',
                        className: 'title'
                    }, data.title),
                    React.createElement('button', {
                        key: 'btn',
                        className: 'btn lynx-button',
                        onClick: handleClick
                    }, '点击我')
                ]),
                
                React.createElement('div', {
                    key: 'content',
                    className: 'content'
                }, data.items.map((item, index) => 
                    React.createElement('div', {
                        key: index,
                        className: 'item'
                    }, [
                        React.createElement('span', { key: 'name' }, item.name),
                        React.createElement('span', { 
                            key: 'desc',
                            className: 'desc' 
                        }, item.description)
                    ])
                )),
                
                data.showFooter ? React.createElement('div', {
                    key: 'footer',
                    className: 'footer'
                }, React.createElement('span', null, '页脚内容')) : null
            ].filter(Boolean));
        }
        
        ReactDOM.render(React.createElement(App), document.getElementById('root'));
    </script>
</body>
</html>`;
            }
            
            processMockCSS(css, componentId = '') {
                // 简单的RPX转换
                let processed = css.replace(/(\d+(?:\.\d+)?)rpx/g, (match, value) => {
                    const vw = (parseFloat(value) / 750 * 100).toFixed(6);
                    return `${vw}vw`;
                });
                
                // 简单的作用域化
                if (componentId) {
                    processed = processed.replace(/([^{}]+)\s*\{/g, (match, selectors) => {
                        const scopedSelectors = selectors
                            .split(',')
                            .map(selector => {
                                const trimmed = selector.trim();
                                if (trimmed.startsWith('@') || trimmed.startsWith(':root')) {
                                    return trimmed;
                                }
                                return `[data-v-${componentId}] ${trimmed}`;
                            })
                            .join(', ');
                        return `${scopedSelectors} {`;
                    });
                }
                
                return processed;
            }
        }
        
        // 初始化转换器
        const converter = new MockRuntimeConverter();
        
        // UI交互逻辑
        let currentExample = 'basic';
        
        // Tab切换
        document.querySelectorAll('[data-tab]').forEach(btn => {
            btn.addEventListener('click', () => {
                const tab = btn.dataset.tab;
                
                // 更新tab按钮状态
                document.querySelectorAll('[data-tab]').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // 显示对应内容
                document.querySelectorAll('.tab-content[data-tab]').forEach(content => {
                    content.classList.toggle('active', content.dataset.tab === tab);
                });
            });
        });
        
        // 示例tab切换
        document.querySelectorAll('[data-example]').forEach(btn => {
            btn.addEventListener('click', () => {
                const example = btn.dataset.example;
                currentExample = example;
                
                // 更新tab按钮状态
                document.querySelectorAll('[data-example]').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // 显示对应内容
                document.querySelectorAll('.tab-content[data-example]').forEach(content => {
                    content.classList.toggle('active', content.dataset.example === example);
                });
            });
        });
        
        // 转换按钮
        document.getElementById('convertBtn').addEventListener('click', async () => {
            const btn = document.getElementById('convertBtn');
            const originalText = btn.textContent;
            
            try {
                // 更新按钮状态
                btn.textContent = '🔄 转换中...';
                btn.disabled = true;
                
                // 显示加载状态
                showLoading();
                hideMessages();
                
                // 获取输入内容
                const files = {
                    ttml: document.getElementById('tttmlInput').value.trim(),
                    ttss: document.getElementById('ttssInput').value.trim(),
                    js: document.getElementById('jsInput').value.trim(),
                    json: document.getElementById('jsonInput').value.trim()
                };
                
                // 执行转换
                const result = await converter.convert(files);
                
                // 显示结果
                if (result.success) {
                    showPreview(result.html);
                    showResultInfo(result.metadata);
                    showSuccessMessage('转换成功！');
                } else {
                    showError(result.message || '转换失败');
                }
                
            } catch (error) {
                showError(error.message);
            } finally {
                // 恢复按钮状态
                btn.textContent = originalText;
                btn.disabled = false;
            }
        });
        
        // 清空按钮
        document.getElementById('clearBtn').addEventListener('click', () => {
            document.getElementById('tttmlInput').value = '';
            document.getElementById('ttssInput').value = '';
            document.getElementById('jsInput').value = '';
            document.getElementById('jsonInput').value = '';
            
            hidePreview();
            hideMessages();
        });
        
        // 示例按钮
        document.getElementById('exampleBtn').addEventListener('click', () => {
            loadDefaultExample();
        });
        
        // 工具函数
        function showLoading() {
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('loadingIndicator').innerHTML = `
                <div class="spinner"></div>
                正在转换...
            `;
            document.getElementById('previewFrame').style.display = 'none';
        }
        
        function showPreview(html) {
            const frame = document.getElementById('previewFrame');
            const loading = document.getElementById('loadingIndicator');
            
            loading.style.display = 'none';
            frame.style.display = 'block';
            
            // 写入HTML到iframe
            const doc = frame.contentDocument || frame.contentWindow.document;
            doc.open();
            doc.write(html);
            doc.close();
        }
        
        function hidePreview() {
            document.getElementById('previewFrame').style.display = 'none';
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('loadingIndicator').innerHTML = '<div>请点击"转换"按钮开始转换</div>';
        }
        
        function showResultInfo(metadata) {
            const info = document.getElementById('resultInfo');
            info.style.display = 'block';
            info.innerHTML = `
                <div class="metadata">
                    <div class="metadata-item">
                        <div class="metadata-value">${metadata.transformTime.toFixed(1)}ms</div>
                        <div class="metadata-label">转换时间</div>
                    </div>
                    <div class="metadata-item">
                        <div class="metadata-value">${metadata.elementCount}</div>
                        <div class="metadata-label">元素数量</div>
                    </div>
                    <div class="metadata-item">
                        <div class="metadata-value">${metadata.cssRuleCount}</div>
                        <div class="metadata-label">CSS规则</div>
                    </div>
                    <div class="metadata-item">
                        <div class="metadata-value">${metadata.hasUserScript ? '是' : '否'}</div>
                        <div class="metadata-label">包含脚本</div>
                    </div>
                </div>
            `;
        }
        
        function showError(message) {
            const error = document.getElementById('errorMessage');
            error.style.display = 'block';
            error.textContent = `❌ ${message}`;
        }
        
        function showSuccessMessage(message) {
            const success = document.getElementById('successMessage');
            success.style.display = 'block';
            success.textContent = `✅ ${message}`;
            
            // 3秒后自动隐藏
            setTimeout(() => {
                success.style.display = 'none';
            }, 3000);
        }
        
        function hideMessages() {
            document.getElementById('resultInfo').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }
        
        function loadDefaultExample() {
            // 已经在HTML中预填充了默认示例
            showSuccessMessage('默认示例已加载');
        }
        
        function loadExample(type) {
            const examples = {
                'basic-components': {
                    ttml: `<view class="demo-page">
  <text class="title">基础组件演示</text>
  
  <view class="section">
    <text class="section-title">文本组件</text>
    <text>普通文本</text>
    <text class="highlight">高亮文本</text>
  </view>
  
  <view class="section">
    <text class="section-title">图片组件</text>
    <image src="https://via.placeholder.com/300x200" class="demo-image" />
  </view>
  
  <view class="section">
    <text class="section-title">按钮组件</text>
    <button bindtap="handlePrimary" class="btn-primary">主要按钮</button>
    <button bindtap="handleSecondary" class="btn-secondary">次要按钮</button>
  </view>
</view>`,
                    ttss: `.demo-page { padding: 32rpx; }
.title { font-size: 48rpx; font-weight: bold; margin-bottom: 40rpx; }
.section { margin-bottom: 32rpx; }
.section-title { font-size: 32rpx; color: #333; margin-bottom: 16rpx; }
.highlight { color: #007aff; font-weight: bold; }
.demo-image { width: 300rpx; height: 200rpx; border-radius: 8rpx; }
.btn-primary { background: #007aff; color: white; margin-right: 16rpx; }
.btn-secondary { background: #6c757d; color: white; }`,
                    js: `Card({
  data: {},
  
  handlePrimary() {
    console.log('主要按钮点击');
  },
  
  handleSecondary() {
    console.log('次要按钮点击');
  }
});`
                },
                
                'form-demo': {
                    ttml: `<view class="form-demo">
  <text class="form-title">表单组件演示</text>
  
  <view class="form-group">
    <text class="label">用户名：</text>
    <input bindinput="onUsernameChange" placeholder="请输入用户名" class="input" />
  </view>
  
  <view class="form-group">
    <text class="label">密码：</text>
    <input type="password" bindinput="onPasswordChange" placeholder="请输入密码" class="input" />
  </view>
  
  <view class="form-group">
    <text class="label">开启通知：</text>
    <switch bindchange="onSwitchChange" checked="{{notificationEnabled}}" />
  </view>
  
  <view class="form-group">
    <text class="label">音量：{{volume}}%</text>
    <slider bindchange="onVolumeChange" value="{{volume}}" max="100" />
  </view>
  
<button bindtap="handleSubmit" class="submit-btn">提交</button>
</view>`,
                    ttss: `.form-demo { padding: 32rpx; }
.form-title { font-size: 40rpx; font-weight: bold; margin-bottom: 32rpx; }
.form-group { margin-bottom: 24rpx; }
.label { display: block; margin-bottom: 8rpx; font-size: 28rpx; }
.input { width: 100%; padding: 16rpx; border: 1rpx solid #ddd; border-radius: 8rpx; }
.submit-btn { width: 100%; background: #28a745; color: white; padding: 16rpx; margin-top: 24rpx; }`,
                    js: `Card({
  data: {
    username: '',
    password: '',
    notificationEnabled: true,
    volume: 50
  },
  
  onUsernameChange(e) {
    this.setData({ username: e.detail.value });
  },
  
  onPasswordChange(e) {
    this.setData({ password: e.detail.value });
  },
  
  onSwitchChange(e) {
    this.setData({ notificationEnabled: e.detail.value });
  },
  
  onVolumeChange(e) {
    this.setData({ volume: e.detail.value });
  },
  
  handleSubmit() {
    console.log('表单提交', this.data);
  }
});`
                },
                
                'list-demo': {
                    ttml: `<view class="list-demo">
  <text class="list-title">列表渲染演示</text>
  
  <view class="filter-bar">
    <button bindtap="filterAll" class="filter-btn {{filter === 'all' ? 'active' : ''}}">全部</button>
    <button bindtap="filterActive" class="filter-btn {{filter === 'active' ? 'active' : ''}}">进行中</button>
    <button bindtap="filterCompleted" class="filter-btn {{filter === 'completed' ? 'active' : ''}}">已完成</button>
  </view>
  
  <view class="todo-list">
    <view lx:for="item, index in filteredItems" lx:key="{{item.id}}" class="todo-item {{item.completed ? 'completed' : ''}}">
      <view class="todo-content">
        <text class="todo-text">{{item.text}}</text>
        <text class="todo-date">{{item.date}}</text>
      </view>
      <view class="todo-actions">
        <button bindtap="toggleItem" data-index="{{index}}" class="toggle-btn">
          {{item.completed ? '撤销' : '完成'}}
        </button>
        <button bindtap="deleteItem" data-index="{{index}}" class="delete-btn">删除</button>
      </view>
    </view>
  </view>
  
  <view lx:if="{{filteredItems.length === 0}}" class="empty-state">
    <text>暂无数据</text>
  </view>
</view>`,
                    ttss: `.list-demo { padding: 32rpx; }
.list-title { font-size: 40rpx; font-weight: bold; margin-bottom: 24rpx; }
.filter-bar { display: flex; gap: 16rpx; margin-bottom: 24rpx; }
.filter-btn { padding: 12rpx 24rpx; border: 1rpx solid #ddd; background: white; }
.filter-btn.active { background: #007aff; color: white; border-color: #007aff; }
.todo-item { background: white; border-radius: 8rpx; padding: 16rpx; margin-bottom: 16rpx; display: flex; align-items: center; }
.todo-item.completed { opacity: 0.6; }
.todo-content { flex: 1; }
.todo-text { font-size: 28rpx; }
.todo-date { font-size: 24rpx; color: #666; margin-top: 4rpx; }
.todo-actions { display: flex; gap: 8rpx; }
.toggle-btn { background: #28a745; color: white; padding: 8rpx 16rpx; }
.delete-btn { background: #dc3545; color: white; padding: 8rpx 16rpx; }
.empty-state { text-align: center; padding: 40rpx; color: #666; }`,
                    js: `Card({
  data: {
    filter: 'all',
    items: [
      { id: 1, text: '学习TTML语法', completed: true, date: '2024-01-01' },
      { id: 2, text: '掌握TTSS样式', completed: false, date: '2024-01-02' },
      { id: 3, text: '练习事件处理', completed: false, date: '2024-01-03' },
      { id: 4, text: '理解指令系统', completed: true, date: '2024-01-04' }
    ]
  },
  
  get filteredItems() {
    const { items, filter } = this.data;
    if (filter === 'active') return items.filter(item => !item.completed);
    if (filter === 'completed') return items.filter(item => item.completed);
    return items;
  },
  
  filterAll() {
    this.setData({ filter: 'all' });
  },
  
  filterActive() {
    this.setData({ filter: 'active' });
  },
  
  filterCompleted() {
    this.setData({ filter: 'completed' });
  },
  
  toggleItem(e) {
    const index = e.currentTarget.dataset.index;
    const items = [...this.data.items];
    items[index].completed = !items[index].completed;
    this.setData({ items });
  },
  
  deleteItem(e) {
    const index = e.currentTarget.dataset.index;
    const items = this.data.items.filter((_, i) => i !== index);
    this.setData({ items });
  }
});`
                }
            };
            
            if (examples[type]) {
                const example = examples[type];
                document.getElementById('tttmlInput').value = example.ttml;
                document.getElementById('ttssInput').value = example.ttss || '';
                document.getElementById('jsInput').value = example.js || '';
                document.getElementById('jsonInput').value = '{}';
                
                showSuccessMessage(`${type} 示例已加载`);
            }
        }
        
        // 全局暴露函数供按钮调用
        window.loadExample = loadExample;
        
        console.log('🚀 Runtime Convert 基础使用示例已加载');
        console.log('📖 请编辑代码并点击转换按钮查看效果');
    </script>
</body>
</html>