/**
 * @package runtime-convert
 * @description 纯浏览器端TTML/TTSS转换引擎
 * @version 1.0.0
 * <AUTHOR> Code Development Team
 * @license MIT
 *
 * 这是一个完全独立的、可发布为npm包的TTML/TTSS转换引擎
 * 特点：
 * - 100%浏览器端运行，无Node.js依赖
 * - 完整的Lynx语法支持
 * - 高性能AST解析和转换
 * - 智能缓存和内存管理
 * - TypeScript完整类型支持
 * - 高内聚低耦合设计
 */

// ===============================
// 主要API导出
// ===============================

export {
  RuntimeConverter,
  createConverter,
  convertFiles,
  validateTTML,
  validateTTSS,
} from './core';

// Import for default export and utility classes
import { RuntimeConverter } from './core';
import type { TransformConfig, InputFiles } from './types';

export type {
  InputFiles,
  TransformResult,
  TransformConfig,
  TransformMetadata,
  TransformError,
  ErrorType,
  ASTNode,
  ElementNode,
  TextNode,
  MustacheNode,
  CommentNode,
  Token,
  Position,
} from './types';

export {
  ASTNodeType,
  TokenType,
  isElementNode,
  isTextNode,
  isMustacheNode,
  isCommentNode,
} from './types';

// ===============================
// 工具函数导出
// ===============================

export { LexerUtils } from './parsers/lexer';
export { ASTUtils } from './parsers/ast-builder';
export { CSSUtils } from './processors/css-processor';
export { CodeGenUtils } from './processors/code-generator';

// ===============================
// 常量配置导出
// ===============================

export {
  ELEMENT_MAPPING,
  EVENT_DIRECTIVE_MAPPING,
  COMMON_ATTRIBUTE_MAPPING,
  SELF_CLOSING_TAGS,
  CSS_UNIT_CONVERSION,
  DEFAULT_CONFIG,
  BASE_CSS_TEMPLATE,
} from './utils/constants';

// ===============================
// 版本信息
// ===============================

export const VERSION = '1.0.0';
export const BUILD_DATE = new Date().toISOString();

// ===============================
// 浏览器兼容性检查
// ===============================

export function checkBrowserCompatibility(): {
  compatible: boolean;
  missing: string[];
  warnings: string[];
} {
  const missing: string[] = [];
  const warnings: string[] = [];

  // 检查必需的API
  if (typeof Map === 'undefined') {
    missing.push('Map');
  }
  if (typeof Set === 'undefined') {
    missing.push('Set');
  }
  if (typeof Promise === 'undefined') {
    missing.push('Promise');
  }
  if (typeof Blob === 'undefined') {
    missing.push('Blob');
  }
  if (typeof performance === 'undefined') {
    missing.push('performance');
  }

  // 检查可选但推荐的API
  if (typeof requestAnimationFrame === 'undefined') {
    warnings.push(
      'requestAnimationFrame not available, some optimizations disabled',
    );
  }
  if (!window.CSS?.supports) {
    warnings.push('CSS.supports not available, some CSS features may not work');
  }

  return {
    compatible: missing.length === 0,
    missing,
    warnings,
  };
}

// ===============================
// 调试和开发工具
// ===============================

export function createDebugConverter(
  config?: TransformConfig & { debug?: boolean },
) {
  const { debug = true, ...restConfig } = config || {};

  class DebugConverter extends RuntimeConverter {
    constructor(cfg: TransformConfig) {
      super(cfg);
      if (debug) {
        console.log('[RuntimeConverter] Debug mode enabled');
        console.log('[RuntimeConverter] Config:', this.getConfig());
      }
    }

    async convert(files: InputFiles) {
      if (debug) {
        console.group('[RuntimeConverter] Starting conversion');
        console.log('Input files:', {
          ttml: files.ttml?.length || 0,
          ttss: files.ttss?.length || 0,
          js: files.js?.length || 0,
          json: files.json?.length || 0,
        });
        console.time('Total conversion time');
      }

      try {
        const result = await super.convert(files);

        if (debug) {
          console.log('Conversion result:', {
            success: result.success,
            htmlSize: result.html?.length || 0,
            metadata: result.metadata,
          });
          console.timeEnd('Total conversion time');
          console.groupEnd();
        }

        return result;
      } catch (error) {
        if (debug) {
          console.error('Conversion failed:', error);
          console.timeEnd('Total conversion time');
          console.groupEnd();
        }
        throw error;
      }
    }
  }

  return new DebugConverter(restConfig);
}

// ===============================
// 性能监控
// ===============================

export class PerformanceMonitor {
  private metrics = new Map<string, number[]>();

  recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(value);
  }

  getAverageMetric(name: string): number {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) {
      return 0;
    }
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  getMetricSummary(name: string): {
    average: number;
    min: number;
    max: number;
    count: number;
  } {
    const values = this.metrics.get(name) || [];
    if (values.length === 0) {
      return { average: 0, min: 0, max: 0, count: 0 };
    }

    return {
      average: values.reduce((sum, val) => sum + val, 0) / values.length,
      min: Math.min(...values),
      max: Math.max(...values),
      count: values.length,
    };
  }

  getAllMetrics(): Record<string, ReturnType<typeof this.getMetricSummary>> {
    const result: Record<string, ReturnType<typeof this.getMetricSummary>> = {};
    for (const name of this.metrics.keys()) {
      result[name] = this.getMetricSummary(name);
    }
    return result;
  }

  clear(): void {
    this.metrics.clear();
  }
}

// ===============================
// 错误收集器
// ===============================

export class ErrorCollector {
  private errors: Array<{
    timestamp: number;
    error: Error;
    context?: any;
  }> = [];

  recordError(error: Error, context?: any): void {
    this.errors.push({
      timestamp: Date.now(),
      error,
      context,
    });

    // 限制错误数量
    if (this.errors.length > 100) {
      this.errors.shift();
    }
  }

  getErrors(): typeof this.errors {
    return [...this.errors];
  }

  getErrorStats(): {
    total: number;
    byType: Record<string, number>;
    recent: number; // 最近5分钟的错误数量
  } {
    const now = Date.now();
    const fiveMinutesAgo = now - 5 * 60 * 1000;

    const byType: Record<string, number> = {};
    let recent = 0;

    this.errors.forEach(({ timestamp, error }) => {
      const type = error.constructor.name;
      byType[type] = (byType[type] || 0) + 1;

      if (timestamp > fiveMinutesAgo) {
        recent++;
      }
    });

    return {
      total: this.errors.length,
      byType,
      recent,
    };
  }

  clear(): void {
    this.errors = [];
  }
}

// ===============================
// 全局实例（可选使用）
// ===============================

let globalConverter: RuntimeConverter | null = null;
let globalMonitor: PerformanceMonitor | null = null;
let globalErrorCollector: ErrorCollector | null = null;

export function getGlobalConverter(config?: TransformConfig): RuntimeConverter {
  if (!globalConverter) {
    globalConverter = new RuntimeConverter(config);
  }
  return globalConverter;
}

export function getGlobalMonitor(): PerformanceMonitor {
  if (!globalMonitor) {
    globalMonitor = new PerformanceMonitor();
  }
  return globalMonitor;
}

export function getGlobalErrorCollector(): ErrorCollector {
  if (!globalErrorCollector) {
    globalErrorCollector = new ErrorCollector();
  }
  return globalErrorCollector;
}

export function resetGlobalInstances(): void {
  globalConverter?.dispose();
  globalConverter = null;
  globalMonitor?.clear();
  globalMonitor = null;
  globalErrorCollector?.clear();
  globalErrorCollector = null;
}

// ===============================
// 包信息
// ===============================

export const PACKAGE_INFO = {
  name: 'runtime-convert',
  version: VERSION,
  description: '纯浏览器端TTML/TTSS转换引擎',
  author: 'Claude Code Development Team',
  license: 'MIT',
  repository: 'https://github.com/search-so-ai/runtime-convert',
  keywords: ['ttml', 'ttss', 'lynx', 'browser', 'converter', 'ast', 'jsx'],
  engines: {
    browsers: ['Chrome >= 60', 'Firefox >= 55', 'Safari >= 12', 'Edge >= 79'],
  },
  features: [
    '100% 浏览器端运行',
    '完整 Lynx 语法支持',
    '高性能 AST 解析',
    '智能缓存系统',
    'TypeScript 支持',
    '零依赖设计',
  ],
} as const;

// ===============================
// 默认导出
// ===============================

export default RuntimeConverter;
