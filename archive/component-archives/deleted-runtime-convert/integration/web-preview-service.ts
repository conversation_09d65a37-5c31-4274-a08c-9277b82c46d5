/**
 * @package runtime-convert
 * @description batch_processor集成服务 - 纯 runtime_convert 实现
 */

import type {
  InputFiles,
  TransformResult,
  TransformConfig,
  TransformMetadata,
} from '../types';
import { RuntimeConverter } from '../core';
import {
  getWorkerManager,
  type WorkerManager,
} from '../workers/worker-manager';

/**
 * 预览结果接口（兼容原有接口）
 */
export interface PreviewResult {
  success: boolean;
  html?: string;
  screenshot?: ScreenshotData;
  error?: string;
  message?: string;
  metadata?: TransformMetadata;
}

export interface ScreenshotData {
  url: string;
  width: number;
  height: number;
  size: number;
}

/**
 * 转换选项接口
 */
export interface ConversionOptions {
  timeout?: number;
  enableScreenshot?: boolean;
  screenshotOptions?: {
    width?: number;
    height?: number;
    quality?: number;
  };
  /** 是否启用Worker模式 */
  enableWorker?: boolean;
  /** 是否强制使用主线程模式 */
  forceMainThread?: boolean;
}

/**
 * Web预览服务 - 集成runtime-convert引擎，支持Worker模式
 */
export class EnhancedWebPreviewService {
  private converter: RuntimeConverter;
  private screenshotService: ScreenshotService | null = null;
  private workerManager: WorkerManager;
  private enableWorker: boolean;

  constructor(config?: TransformConfig & { enableWorker?: boolean }) {
    // 使用默认配置，CSS转换采用固定规则
    this.converter = new RuntimeConverter(config);

    // 初始化Worker管理器
    this.enableWorker = config?.enableWorker ?? true;
    this.workerManager = getWorkerManager({
      enableWorker: this.enableWorker,
      taskTimeout: 30000,
      maxRetries: 2,
    });

    // 动态加载截图服务（如果可用）
    this.initializeScreenshotService();

    console.log(
      `[EnhancedWebPreviewService] 初始化完成，Worker模式: ${this.enableWorker ? '启用' : '禁用'}`,
    );
  }

  /**
   * 转换为Web预览 - 主要API接口
   */
  async convertToWebPreview(
    content: string,
    resultId: string,
    options: ConversionOptions = {},
  ): Promise<PreviewResult> {
    const startTime = performance.now();

    try {
      // 1. 解析内容结构
      const files = this.parseFileStructure(content);

      // 2. 验证内容
      const validation = this.validateContent(files);
      if (!validation.isValid) {
        return {
          success: false,
          error: 'VALIDATION_ERROR',
          message: validation.reason || '内容验证失败',
        };
      }

      // 3. 执行转换 - 优先使用Worker模式
      let transformResult;
      const shouldUseWorker =
        this.enableWorker &&
        !options.forceMainThread &&
        options.enableWorker !== false;

      try {
        if (shouldUseWorker) {
          console.log('[EnhancedWebPreviewService] 🔄 使用Worker模式执行转换');
          transformResult = await this.convertWithWorker(files);
        } else {
          console.log('[EnhancedWebPreviewService] 🔄 使用主线程模式执行转换');
          transformResult = await this.convertWithMainThread(files);
        }
      } catch (conversionError) {
        console.error(
          `Runtime Convert 转换失败 (${shouldUseWorker ? 'Worker' : '主线程'}模式):`,
          conversionError,
        );

        // 如果Worker模式失败，尝试回退到主线程模式
        if (shouldUseWorker && !options.forceMainThread) {
          console.log(
            '[EnhancedWebPreviewService] 🔄 Worker模式失败，回退到主线程模式',
          );
          try {
            transformResult = await this.convertWithMainThread(files);
          } catch (fallbackError) {
            console.error('主线程回退模式也失败:', fallbackError);
            const errorMessage =
              fallbackError instanceof Error
                ? fallbackError.message
                : String(fallbackError);
            return {
              success: false,
              error: 'SYNTAX_ERROR',
              message: `TTML语法错误 (Worker+主线程均失败):\n${errorMessage}`,
            };
          }
        } else {
          // 直接展示语法错误给用户，不掩饰
          const errorMessage =
            conversionError instanceof Error
              ? conversionError.message
              : String(conversionError);
          return {
            success: false,
            error: 'SYNTAX_ERROR',
            message: `TTML语法错误:\n${errorMessage}`,
          };
        }
      }

      if (!transformResult.success) {
        return {
          success: false,
          error: transformResult.error || 'TRANSFORM_ERROR',
          message: transformResult.message || '转换失败',
        };
      }

      // 4. 生成截图（如果启用）
      let screenshot: ScreenshotData | undefined;
      if (
        options.enableScreenshot &&
        this.screenshotService &&
        transformResult.html
      ) {
        try {
          screenshot = await this.generateScreenshot(
            transformResult.html,
            options.screenshotOptions,
          );
        } catch (screenshotError) {
          console.warn('截图生成失败:', screenshotError);
          // 截图失败不影响整体转换结果
        }
      }

      const endTime = performance.now();

      return {
        success: true,
        html: transformResult.html,
        screenshot,
        metadata: {
          ...transformResult.metadata,
          transformTime: endTime - startTime,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: 'RUNTIME_ERROR',
        message: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 使用Worker模式执行转换
   */
  private async convertWithWorker(files: InputFiles): Promise<TransformResult> {
    const startTime = performance.now();

    try {
      // 使用Worker管理器执行转换
      const result = await this.workerManager.convert(files);

      const processingTime = performance.now() - startTime;
      console.log(
        `[EnhancedWebPreviewService] Worker转换完成，耗时: ${processingTime.toFixed(2)}ms`,
      );

      return {
        ...result,
        metadata: {
          ...result.metadata,
          threadType: 'Web Worker',
          totalTime: processingTime,
        },
      };
    } catch (error) {
      const processingTime = performance.now() - startTime;
      console.error(
        `[EnhancedWebPreviewService] Worker转换失败，耗时: ${processingTime.toFixed(2)}ms`,
        error,
      );
      throw error;
    }
  }

  /**
   * 使用主线程模式执行转换
   */
  private async convertWithMainThread(
    files: InputFiles,
  ): Promise<TransformResult> {
    const startTime = performance.now();

    try {
      // 让出控制权，避免立即阻塞主线程
      await new Promise(resolve => setTimeout(resolve, 0));

      const result = await this.converter.convert(files);

      const processingTime = performance.now() - startTime;
      console.log(
        `[EnhancedWebPreviewService] 主线程转换完成，耗时: ${processingTime.toFixed(2)}ms`,
      );

      // 检查处理时间，如果过长则发出警告
      if (processingTime > 1000) {
        console.warn(
          `[EnhancedWebPreviewService] ⚠️ 主线程转换时间较长: ${processingTime.toFixed(2)}ms，建议使用Worker模式`,
        );
      }

      return {
        ...result,
        metadata: {
          ...result.metadata,
          threadType: '主线程',
          totalTime: processingTime,
        },
      };
    } catch (error) {
      const processingTime = performance.now() - startTime;
      console.error(
        `[EnhancedWebPreviewService] 主线程转换失败，耗时: ${processingTime.toFixed(2)}ms`,
        error,
      );
      throw error;
    }
  }

  /**
   * 解析文件结构
   */
  private parseFileStructure(content: string): InputFiles {
    // 清理转义字符
    let processed = content
      .replace(/\\n/g, '\n')
      .replace(/\\"/g, '"')
      .replace(/\\</g, '<')
      .replace(/\\>/g, '>');

    // 移除外层<FILES>包装（如果存在）
    if (processed.includes('<FILES>')) {
      const filesMatch = processed.match(/<FILES>([\s\S]*?)<\/FILES>/);
      if (filesMatch) {
        processed = filesMatch[1].trim();
      }
    }

    const files: InputFiles = {};

    // 匹配<FILE>标签
    const filePattern = /<FILE\s+path="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/g;
    let match;

    while ((match = filePattern.exec(processed)) !== null) {
      const [, path, fileContent] = match;
      const trimmedContent = fileContent.trim();

      if (path.endsWith('.ttml')) {
        files.ttml = trimmedContent;
      } else if (path.endsWith('.ttss')) {
        files.ttss = trimmedContent;
      } else if (path.endsWith('.js')) {
        files.js = trimmedContent;
      } else if (path.endsWith('.json')) {
        files.json = trimmedContent;
      }
    }

    // 如果没有找到FILE标签，尝试直接解析内容
    if (!files.ttml && Object.keys(files).length === 0) {
      if (this.looksLikeTTML(processed)) {
        files.ttml = processed;
      }
    }

    return files;
  }

  /**
   * 验证内容
   */
  private validateContent(files: InputFiles): {
    isValid: boolean;
    reason?: string;
  } {
    if (!files.ttml?.trim()) {
      return { isValid: false, reason: '未找到TTML内容' };
    }

    // 基础TTML语法检查 - 更宽松的验证
    if (!this.looksLikeTTML(files.ttml) && !this.looksLikeHTML(files.ttml)) {
      return { isValid: false, reason: '内容不包含有效的TTML或HTML语法' };
    }

    return { isValid: true };
  }

  /**
   * 检查是否像TTML内容
   */
  private looksLikeTTML(content: string): boolean {
    const tttmlIndicators = [
      '<view',
      '<text',
      '<image',
      '<button',
      '<input',
      '<scroll-view',
      'lx:',
      'bind',
      'catch:',
    ];

    return tttmlIndicators.some(indicator => content.includes(indicator));
  }

  /**
   * 检查是否像HTML内容
   */
  private looksLikeHTML(content: string): boolean {
    const htmlIndicators = [
      '<div',
      '<span',
      '<p>',
      '<img',
      '<a',
      '<ul',
      '<li',
      '<h1',
      '<h2',
      '<h3',
      'class=',
      'id=',
      'style=',
    ];

    return htmlIndicators.some(indicator => content.includes(indicator));
  }

  /**
   * 生成截图
   */
  private async generateScreenshot(
    html: string,
    options: ConversionOptions['screenshotOptions'] = {},
  ): Promise<ScreenshotData> {
    const { width = 375, height = 667, quality = 0.8 } = options;

    // 如果没有截图服务，使用模拟服务
    if (!this.screenshotService) {
      console.log('使用模拟截图服务');
      const mockService = new MockScreenshotService();
      return await mockService.capture(html, {
        width,
        height,
        quality,
      });
    }

    return await this.screenshotService.capture(html, {
      width,
      height,
      quality,
    });
  }

  /**
   * 初始化截图服务
   */
  private async initializeScreenshotService(): Promise<void> {
    try {
      // 尝试动态导入截图服务（如果存在）
      // 暂时禁用截图服务，因为 ScreenshotService 不在 runtime_convert 中
      console.log('截图服务暂时禁用，等待后续实现');
      this.screenshotService = null;
    } catch (error) {
      console.warn('截图服务不可用:', error);
      this.screenshotService = null;
    }
  }

  /**
   * 获取转换器统计信息
   */
  getStats(): {
    cacheStats: ReturnType<RuntimeConverter['getCacheStats']>;
    config: ReturnType<RuntimeConverter['getConfig']>;
    workerStatus: ReturnType<WorkerManager['getStatus']>;
  } {
    return {
      cacheStats: this.converter.getCacheStats(),
      config: this.converter.getConfig(),
      workerStatus: this.workerManager.getStatus(),
    };
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.converter.clearCache();
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<TransformConfig>): void {
    this.converter.updateConfig(config);
  }

  /**
   * 销毁服务
   */
  async dispose(): Promise<void> {
    console.log('[EnhancedWebPreviewService] 正在销毁服务...');

    // 销毁Worker管理器
    await this.workerManager.dispose();

    // 销毁主线程转换器
    this.converter.dispose();

    // 清理截图服务
    this.screenshotService = null;

    console.log('[EnhancedWebPreviewService] 服务已销毁');
  }
}

/**
 * 简化的截图服务接口
 * 暂时定义，等待后续实现
 */
interface ScreenshotService {
  capture: (
    html: string,
    options: {
      width: number;
      height: number;
      quality: number;
    },
  ) => Promise<ScreenshotData>;
}

/**
 * 模拟截图服务实现（用于过渡期）
 */
class MockScreenshotService implements ScreenshotService {
  async capture(
    html: string,
    options: {
      width: number;
      height: number;
      quality: number;
    },
  ): Promise<ScreenshotData> {
    // 返回模拟截图数据
    return {
      url: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', // 1x1透明图片
      width: options.width,
      height: options.height,
      size: 100, // 模拟大小
    };
  }
}

/**
 * 兼容性适配器 - 提供与原有WebPreviewService相同的接口
 */
export class WebPreviewServiceAdapter {
  private enhancedService: EnhancedWebPreviewService;

  constructor(config?: any) {
    // 使用默认配置，系统自动处理
    this.enhancedService = new EnhancedWebPreviewService(config);
  }

  /**
   * 转换接口 - 保持与原有接口兼容
   */
  async convertToWebPreview(
    content: string,
    resultId: string,
    options?: any,
  ): Promise<PreviewResult> {
    const conversionOptions: ConversionOptions = {
      timeout: options?.timeout || 15000,
      enableScreenshot: true,
    };

    return await this.enhancedService.convertToWebPreview(
      content,
      resultId,
      conversionOptions,
    );
  }

  /**
   * 销毁接口
   */
  async dispose(): Promise<void> {
    await this.enhancedService.dispose();
  }
}

// 默认导出兼容适配器
export default WebPreviewServiceAdapter;
