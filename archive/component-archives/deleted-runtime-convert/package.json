{"name": "@search-so-ai/runtime-convert", "version": "1.0.0", "description": "纯浏览器端TTML/TTSS转换引擎 - Browser-only TTML/TTSS conversion engine", "main": "index.js", "module": "index.js", "types": "index.d.ts", "files": ["**/*.js", "**/*.d.ts", "**/*.html", "**/*.md", "!node_modules", "!tests/**/*.js", "!examples/**/*.js"], "scripts": {"build": "tsc", "test": "echo 'Open tests/test-runner.html in browser'", "test:auto": "echo 'Open tests/test-runner.html?autoRun=true in browser'", "demo": "echo 'Open examples/basic-usage.html in browser'", "dev": "tsc --watch", "clean": "rm -rf **/*.js **/*.d.ts", "prepublishOnly": "npm run build"}, "keywords": ["ttml", "ttss", "lynx", "browser", "converter", "ast", "jsx", "runtime", "transform", "web", "mobile", "rpx", "miniprogram"], "author": "Claude Code Development Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/search-so-ai/runtime-convert.git"}, "homepage": "https://github.com/search-so-ai/runtime-convert#readme", "bugs": {"url": "https://github.com/search-so-ai/runtime-convert/issues"}, "engines": {"node": ">=14.0.0"}, "browserslist": ["Chrome >= 60", "Firefox >= 55", "Safari >= 12", "Edge >= 79"], "devDependencies": {"typescript": "^4.9.0 || ^5.0.0"}, "peerDependencies": {}, "dependencies": {}, "exports": {".": {"import": "./index.js", "require": "./index.js", "types": "./index.d.ts"}, "./types": {"import": "./types/index.js", "require": "./types/index.js", "types": "./types/index.d.ts"}, "./core": {"import": "./core/index.js", "require": "./core/index.js", "types": "./core/index.d.ts"}, "./integration": {"import": "./integration/web-preview-service.js", "require": "./integration/web-preview-service.js", "types": "./integration/web-preview-service.d.ts"}}, "publishConfig": {"access": "public"}, "funding": {"type": "github", "url": "https://github.com/sponsors/search-so-ai"}, "config": {"runtime-convert": {"version": "1.0.0", "build": "2024-06-24", "features": ["browser-only", "zero-dependencies", "typescript-support", "ast-parsing", "caching", "optimization"]}}}