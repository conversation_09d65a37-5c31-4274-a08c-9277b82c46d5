/**
 * @package runtime-convert
 * @description AST构建器 - 将tokens转换为抽象语法树
 */

import type {
  Token,
  ASTNode,
  ElementNode,
  TextNode,
  MustacheNode,
  CommentNode,
  Position,
  TransformError,
  ErrorType,
} from '../types';
import { ASTNodeType } from '../types';
import { AttributeParser, ExpressionParser, LexerUtils } from './lexer';
import { SELF_CLOSING_TAGS } from '../utils/constants';

export class BrowserASTBuilder {
  private stack: ElementNode[] = [];
  private root: ASTNode | null = null;
  private current: ElementNode | null = null;

  /**
   * 从tokens构建AST
   */
  build(tokens: Token[]): ASTNode {
    this.reset();
    this.root = this.createNode(ASTNodeType.Root) as ASTNode;
    this.current = this.root as ElementNode;
    this.stack.push(this.current);

    const errors: TransformError[] = [];

    for (const token of tokens) {
      try {
        this.processToken(token);
      } catch (error) {
        // 收集语法错误而不是隐藏
        const transformError: TransformError = {
          name: 'TransformError',
          type: 'SYNTAX_ERROR' as ErrorType,
          message: error instanceof Error ? error.message : String(error),
          position: token.position,
          code: 'SYNTAX_ERROR',
          severity: 'error',
          source: token.value.substring(0, 100),
        };
        errors.push(transformError);

        console.error('❌ [AST Builder] 语法错误:', {
          type: token.type,
          value: token.value.substring(0, 50),
          position: token.position,
          error: transformError.message,
        });
      }
    }

    // 智能检查未闭合标签，提供详细上下文信息
    if (this.stack.length > 1) {
      const unclosedElements = this.stack.slice(1);
      const detailedErrors = this.analyzeUnclosedTags(unclosedElements);

      for (const error of detailedErrors) {
        errors.push(error);
      }

      console.error('❌ [AST Builder] 未闭合标签错误:', detailedErrors);

      // 抛出详细的错误信息
      const errorSummary = detailedErrors.map(e => e.message).join('\n');
      throw new Error(`TTML语法错误:\n${errorSummary}`);
    }

    // 如果有语法错误，抛出包含所有错误信息的异常
    if (errors.length > 0) {
      const errorMessages = errors
        .map(e => `${e.code}: ${e.message}`)
        .join('\n');
      throw new Error(`TTML语法错误:\n${errorMessages}`);
    }

    return this.root!;
  }

  /**
   * 重置构建器状态
   */
  private reset(): void {
    this.stack = [];
    this.root = null;
    this.current = null;
  }

  /**
   * 智能分析未闭合标签，提供详细上下文信息
   */
  private analyzeUnclosedTags(
    unclosedElements: ElementNode[],
  ): TransformError[] {
    const errors: TransformError[] = [];

    for (let i = 0; i < unclosedElements.length; i++) {
      const element = unclosedElements[i];
      const context = this.getTagContext(element, unclosedElements);

      const error: TransformError = {
        name: 'TransformError',
        type: 'SYNTAX_ERROR' as ErrorType,
        message: `未闭合的标签 <${element.tagName}> ${context}`,
        position: element.position || { line: 0, column: 0, offset: 0 },
        code: 'UNCLOSED_TAG',
        severity: 'error',
        source: this.generateTagSource(element),
      };

      errors.push(error);
    }

    return errors;
  }

  /**
   * 获取标签的上下文信息
   */
  private getTagContext(
    element: ElementNode,
    allUnclosed: ElementNode[],
  ): string {
    const contexts: string[] = [];

    // 添加位置信息
    if (element.position) {
      contexts.push(`在第 ${element.position.line} 行`);
    }

    // 添加属性信息以帮助识别
    if (element.attributes && Object.keys(element.attributes).length > 0) {
      const attrs = Object.entries(element.attributes)
        .slice(0, 2) // 只显示前2个属性避免过长
        .map(([key, value]) => `${key}="${value}"`)
        .join(' ');
      contexts.push(`属性: ${attrs}`);
    }

    // 添加嵌套层级信息
    const depth = allUnclosed.indexOf(element) + 1;
    if (depth > 1) {
      contexts.push(`嵌套层级: ${depth}`);
    }

    return contexts.length > 0 ? `(${contexts.join(', ')})` : '';
  }

  /**
   * 生成标签源码用于错误显示
   */
  private generateTagSource(element: ElementNode): string {
    // 处理缺失标签名的情况
    const tagName = element.tagName || 'UNKNOWN';
    const attrs = element.attributes
      ? Object.entries(element.attributes)
          .map(([key, value]) => `${key}="${value}"`)
          .join(' ')
      : '';

    if (!element.tagName && attrs) {
      // 如果标签名缺失但有属性，显示属性片段
      return `<${attrs}>`;
    }

    return attrs ? `<${tagName} ${attrs}>` : `<${tagName}>`;
  }

  /**
   * 智能查找匹配的开始标签
   */
  private findMatchingOpenTag(
    tagName: string,
    position: Position,
  ): {
    found: boolean;
    matchIndex: number;
    unclosedTags: ElementNode[];
    suggestion: string;
  } {
    // 从栈顶向下查找匹配的标签
    for (let i = this.stack.length - 1; i >= 1; i--) {
      if (this.stack[i].tagName === tagName) {
        const unclosedTags = this.stack.slice(i + 1);
        return {
          found: true,
          matchIndex: i,
          unclosedTags,
          suggestion: '',
        };
      }
    }

    // 没有找到匹配标签，提供智能建议
    const availableTags = this.stack.slice(1).map(el => el.tagName);
    const suggestion = this.generateTagSuggestion(tagName, availableTags);

    return {
      found: false,
      matchIndex: -1,
      unclosedTags: [],
      suggestion,
    };
  }

  /**
   * 生成标签建议
   */
  private generateTagSuggestion(
    wrongTag: string,
    availableTags: string[],
  ): string {
    if (availableTags.length === 0) {
      return '当前没有未闭合的标签';
    }

    // 使用简单的编辑距离算法找到最相似的标签
    const similarities = availableTags.map(tag => ({
      tag,
      distance: this.calculateEditDistance(wrongTag, tag),
    }));

    similarities.sort((a, b) => a.distance - b.distance);
    const bestMatch = similarities[0];

    if (bestMatch.distance <= 2) {
      return `您是否想要闭合 <${bestMatch.tag}>？`;
    } else {
      return `当前未闭合的标签: ${availableTags.join(', ')}`;
    }
  }

  /**
   * 计算编辑距离（用于标签相似度匹配）
   */
  private calculateEditDistance(a: string, b: string): number {
    const matrix = Array(a.length + 1)
      .fill(null)
      .map(() => Array(b.length + 1).fill(null));

    for (let i = 0; i <= a.length; i++) {
      matrix[i][0] = i;
    }
    for (let j = 0; j <= b.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= a.length; i++) {
      for (let j = 1; j <= b.length; j++) {
        if (a[i - 1] === b[j - 1]) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // substitution
            matrix[i][j - 1] + 1, // insertion
            matrix[i - 1][j] + 1, // deletion
          );
        }
      }
    }

    return matrix[a.length][b.length];
  }

  /**
   * 处理单个token
   */
  private processToken(token: Token): void {
    switch (token.type) {
      case 'startTag':
        this.handleStartTag(token);
        break;
      case 'endTag':
        this.handleEndTag(token);
        break;
      case 'selfClosing':
        this.handleSelfClosingTag(token);
        break;
      case 'text':
        this.handleText(token);
        break;
      case 'mustache':
        this.handleMustache(token);
        break;
      case 'comment':
        this.handleComment(token);
        break;
      default:
        console.warn(`Unknown token type: ${token.type}`);
    }
  }

  /**
   * 处理开始标签
   */
  private handleStartTag(token: Token): void {
    // 更强健的标签名提取，防止捕获组问题
    let tagName: string | undefined;
    let attributesStr: string | undefined;

    if (token.groups && token.groups.length >= 2) {
      [, tagName, attributesStr] = token.groups;
    } else {
      // 备用解析：直接从token值中提取
      const match = token.value.match(/^<([a-zA-Z][a-zA-Z0-9-]*)\s*([^>]*?)>$/);
      if (match) {
        tagName = match[1];
        attributesStr = match[2];
      }
    }

    if (!tagName) {
      throw new ASTBuildError(
        `Invalid start tag: missing tag name in "${token.value}"`,
        token.position,
        token.value,
      );
    }

    const attributes = AttributeParser.parse(attributesStr || '');

    // 验证属性
    this.validateAttributes(attributes, token.position);

    const element = this.createElementNode({
      tagName,
      attributes,
      position: token.position,
      selfClosing: false,
    });

    this.current!.children.push(element);

    // 如果不是自闭合标签，入栈
    if (
      !SELF_CLOSING_TAGS.has(tagName) &&
      !LexerUtils.isSelfClosingTag(tagName)
    ) {
      this.stack.push(element);
      this.current = element;
    }
  }

  /**
   * 处理结束标签
   */
  private handleEndTag(token: Token): void {
    const [, tagName] = token.groups || [];

    if (!tagName) {
      throw new ASTBuildError(
        'Invalid end tag format',
        token.position,
        token.value,
      );
    }

    if (this.stack.length <= 1) {
      throw new ASTBuildError(
        `Unexpected closing tag </${tagName}> - no matching opening tag`,
        token.position,
        token.value,
      );
    }

    // 智能匹配：在栈中查找最近的匹配标签
    const matchResult = this.findMatchingOpenTag(tagName, token.position);

    if (!matchResult.found) {
      throw new ASTBuildError(
        `No matching opening tag for </${tagName}>. ${matchResult.suggestion}`,
        token.position,
        token.value,
      );
    }

    // 如果中间有未闭合的标签，给出智能提示
    if (matchResult.unclosedTags.length > 0) {
      const unclosedList = matchResult.unclosedTags
        .map(t => `<${t.tagName}>`)
        .join(', ');
      console.warn(
        `⚠️ [AST Builder] 自动闭合中间未闭合的标签: ${unclosedList}`,
      );
    }

    // 闭合到匹配的标签
    while (this.stack.length > matchResult.matchIndex) {
      const closingElement = this.stack.pop();
      if (closingElement && this.stack.length > 0) {
        this.current = this.stack[this.stack.length - 1];
      }
    }
  }

  /**
   * 处理自闭合标签
   */
  private handleSelfClosingTag(token: Token): void {
    const [, tagName, attributesStr] = token.groups || [];

    if (!tagName) {
      throw new ASTBuildError(
        'Invalid self-closing tag',
        token.position,
        token.value,
      );
    }

    const attributes = AttributeParser.parse(attributesStr || '');

    // 验证属性
    this.validateAttributes(attributes, token.position);

    const element = this.createElementNode({
      tagName,
      attributes,
      position: token.position,
      selfClosing: true,
    });

    this.current!.children.push(element);
  }

  /**
   * 处理文本节点
   */
  private handleText(token: Token): void {
    const content = token.value.trim();

    if (!content) {
      return; // 忽略空白文本
    }

    const textNode = this.createNode(ASTNodeType.Text, {
      content,
      position: token.position,
    }) as TextNode;

    this.current!.children.push(textNode);
  }

  /**
   * 处理插值表达式
   */
  private handleMustache(token: Token): void {
    const expression = ExpressionParser.parseMustache(token.value);

    if (!ExpressionParser.isValidExpression(expression)) {
      throw new ASTBuildError(
        `Invalid expression: ${expression}`,
        token.position,
        token.value,
      );
    }

    const mustacheNode = this.createNode(ASTNodeType.Mustache, {
      expression,
      position: token.position,
    }) as MustacheNode;

    this.current!.children.push(mustacheNode);
  }

  /**
   * 处理注释
   */
  private handleComment(token: Token): void {
    const content = token.value.slice(4, -3).trim(); // 移除 <!-- 和 -->

    const commentNode = this.createNode(ASTNodeType.Comment, {
      content,
      position: token.position,
    }) as CommentNode;

    this.current!.children.push(commentNode);
  }

  /**
   * 创建AST节点
   */
  private createNode(type: ASTNodeType, props: Partial<ASTNode> = {}): ASTNode {
    return {
      type,
      children: [],
      ...props,
    };
  }

  /**
   * 创建元素节点
   */
  private createElementNode(props: {
    tagName: string;
    attributes: Record<string, string>;
    position?: Position;
    selfClosing?: boolean;
  }): ElementNode {
    const { tagName, attributes, position, selfClosing } = props;

    return {
      type: ASTNodeType.Element,
      tagName,
      attributes,
      children: [],
      position,
      selfClosing,
      isComponent: this.isCustomComponent(tagName),
    };
  }

  /**
   * 验证属性
   */
  private validateAttributes(
    attributes: Record<string, string>,
    position?: Position,
  ): void {
    for (const [name, value] of Object.entries(attributes)) {
      // 验证属性名
      if (!AttributeParser.isValidAttributeName(name)) {
        throw new ASTBuildError(
          `Invalid attribute name: ${name}`,
          position,
          name,
        );
      }

      // 验证指令表达式
      if (LexerUtils.isDirective(name)) {
        this.validateDirective(name, value, position);
      }

      // 验证事件处理器
      if (LexerUtils.isEventAttribute(name)) {
        this.validateEventHandler(name, value, position);
      }
    }
  }

  /**
   * 验证指令
   */
  private validateDirective(
    name: string,
    value: string,
    position?: Position,
  ): void {
    if (name === 'lx:for') {
      // 验证 lx:for 语法
      if (
        !/^\s*\w+\s+in\s+.+$/.test(value) &&
        !/^\s*\w+\s*,\s*\w+\s+in\s+.+$/.test(value)
      ) {
        throw new ASTBuildError(
          `Invalid lx:for syntax: ${value}. Expected "item in list" or "item, index in list"`,
          position,
          value,
        );
      }
    } else if (
      name.startsWith('lx:') &&
      name !== 'lx:key' &&
      name !== 'lx:else'
    ) {
      // 其他指令需要有效表达式
      if (!ExpressionParser.isValidExpression(value)) {
        throw new ASTBuildError(
          `Invalid directive expression: ${value}`,
          position,
          value,
        );
      }
    }
  }

  /**
   * 验证事件处理器
   */
  private validateEventHandler(
    name: string,
    value: string,
    position?: Position,
  ): void {
    if (!value.trim()) {
      throw new ASTBuildError(
        `Empty event handler for ${name}`,
        position,
        value,
      );
    }

    // 简单验证事件处理器表达式
    if (!ExpressionParser.isValidExpression(value)) {
      throw new ASTBuildError(
        `Invalid event handler expression: ${value}`,
        position,
        value,
      );
    }
  }

  /**
   * 检查是否为自定义组件
   */
  private isCustomComponent(tagName: string): boolean {
    return LexerUtils.isLynxComponent(tagName);
  }
}

/**
 * AST 工具函数
 */
export const ASTUtils = {
  /**
   * 遍历AST节点
   */
  traverse(node: ASTNode, visitor: (node: ASTNode) => void): void {
    visitor(node);
    if (node.children) {
      node.children.forEach(child => this.traverse(child, visitor));
    }
  },

  /**
   * 查找特定类型的节点
   */
  findNodes(root: ASTNode, type: ASTNodeType): ASTNode[] {
    const result: ASTNode[] = [];
    this.traverse(root, node => {
      if (node.type === type) {
        result.push(node);
      }
    });
    return result;
  },

  /**
   * 获取节点深度
   */
  getDepth(node: ASTNode): number {
    if (!node.children || node.children.length === 0) {
      return 1;
    }
    return 1 + Math.max(...node.children.map(child => this.getDepth(child)));
  },
};

/**
 * Additional AST utility methods (static class version removed to avoid duplication)
 */
// // export class ASTUtils {
//   /**
//    * 遍历AST
//    */
//   // static walk(node: ASTNode, visitor: (node: ASTNode, parent?: ASTNode) => void, parent?: ASTNode): void {
//     visitor(node, parent);
//
//     if (node.children) {
//       for (const child of node.children) {
//         this.walk(child, visitor, node);
//       }
//     }
//   }
//
//   /**
//    * 查找节点
//    */
//   static find(node: ASTNode, predicate: (node: ASTNode) => boolean): ASTNode | null {
//     if (predicate(node)) {
//       return node;
//     }
//
//     if (node.children) {
//       for (const child of node.children) {
//         const found = this.find(child, predicate);
//         if (found) {
//           return found;
//         }
//       }
//     }
//
//     return null;
//   }
//
//   /**
//    * 查找所有匹配节点
//    */
//   static findAll(node: ASTNode, predicate: (node: ASTNode) => boolean): ASTNode[] {
//     const results: ASTNode[] = [];
//
//     this.walk(node, (n) => {
//       if (predicate(n)) {
//         results.push(n);
//       }
//     });
//
//     return results;
//   }
//
//   /**
//    * 计算节点数量
//    */
//   static countNodes(node: ASTNode): number {
//     let count = 1;
//
//     if (node.children) {
//       for (const child of node.children) {
//         count += this.countNodes(child);
//       }
//     }
//
//     return count;
//   }
//
//   /**
//    * 计算元素节点数量
//    */
//   static countElements(node: ASTNode): number {
//     let count = 0;
//
//     this.walk(node, (n) => {
//       if (n.type === ASTNodeType.Element) {
//         count++;
//       }
//     });
//
//     return count;
//   }
//
//   /**
//    * 提取所有文本内容
//    */
//   static extractText(node: ASTNode): string {
//     const texts: string[] = [];
//
//     this.walk(node, (n) => {
//       if (n.type === ASTNodeType.Text) {
//         texts.push((n as TextNode).content);
//       }
//     });
//
//     return texts.join(' ');
//   }
//
//   /**
//    * 提取所有使用的变量
//    */
//   static extractVariables(node: ASTNode): string[] {
//     const variables = new Set<string>();
//
//     this.walk(node, (n) => {
//       if (n.type === ASTNodeType.Mustache) {
//         const mustacheNode = n as MustacheNode;
//         const vars = ExpressionParser.extractVariables(mustacheNode.expression);
//         vars.forEach(v => variables.add(v));
//       } else if (n.type === ASTNodeType.Element) {
//         const elementNode = n as ElementNode;
//         for (const [name, value] of Object.entries(elementNode.attributes)) {
//           if (LexerUtils.isDirective(name) || value.includes('{{')) {
//             const cleanValue = value.replace(/\{\{([^}]+)\}\}/g, '$1');
//             const vars = ExpressionParser.extractVariables(cleanValue);
//             vars.forEach(v => variables.add(v));
//           }
//         }
//       }
//     });
//
//     return Array.from(variables);
//   }
//
//   /**
//    * 验证AST结构
//    */
//   static validate(node: ASTNode): { valid: boolean; errors: string[] } {
//     const errors: string[] = [];
//
//     this.walk(node, (n, parent) => {
//       // 验证节点类型
//       if (!Object.values(ASTNodeType).includes(n.type)) {
//         errors.push(`Invalid node type: ${n.type}`);
//       }
//
//       // 验证元素节点
//       if (n.type === ASTNodeType.Element) {
//         const elementNode = n as ElementNode;
//
//         // 验证标签名
//         if (!elementNode.tagName || typeof elementNode.tagName !== 'string') {
//           errors.push('Element node missing tagName');
//         }
//
//         // 验证属性
//         if (!elementNode.attributes || typeof elementNode.attributes !== 'object') {
//           errors.push('Element node missing attributes');
//         }
//       }
//
//       // 验证文本节点
//       if (n.type === ASTNodeType.Text) {
//         const textNode = n as TextNode;
//         if (typeof textNode.content !== 'string') {
//           errors.push('Text node missing content');
//         }
//       }
//
//       // 验证插值节点
//       if (n.type === ASTNodeType.Mustache) {
//         const mustacheNode = n as MustacheNode;
//         if (typeof mustacheNode.expression !== 'string') {
//           errors.push('Mustache node missing expression');
//         }
//       }
//     });
//
//     return {
//       valid: errors.length === 0,
//       errors
//     };
//   }
//
//   /**
//    * 克隆AST节点
//    */
//   // static clone(node: ASTNode): ASTNode {
//     // return JSON.parse(JSON.stringify(node));
//   // }
// // }

/**
 * AST构建错误类
 */
export class ASTBuildError extends Error implements TransformError {
  public type: ErrorType = 'SYNTAX_ERROR' as ErrorType;

  constructor(
    message: string,
    public position?: Position,
    public code?: string,
  ) {
    super(
      position
        ? `AST Build Error at line ${position.line}, column ${position.column}: ${message}`
        : `AST Build Error: ${message}`,
    );
    this.name = 'ASTBuildError';
  }
}
