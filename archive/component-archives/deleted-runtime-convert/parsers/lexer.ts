/**
 * @package runtime-convert
 * @description 纯浏览器端TTML词法分析器
 */

import type { Token, TokenType, Position } from '../types';
import { LEXER_PATTERNS } from '../utils/constants';

export class BrowserLexer {
  private tokens: Token[] = [];
  private position = 0;
  private line = 1;
  private column = 1;
  private input = '';

  /**
   * 对TTML源码进行词法分析
   */
  tokenize(input: string): Token[] {
    this.reset();
    this.input = input;

    while (this.position < this.input.length) {
      if (!this.scanToken()) {
        // 如果没有匹配到任何token，跳过当前字符
        this.advance();
      }
    }

    return this.tokens;
  }

  /**
   * 重置分析器状态
   */
  private reset(): void {
    this.tokens = [];
    this.position = 0;
    this.line = 1;
    this.column = 1;
    this.input = '';
  }

  /**
   * 扫描单个token
   */
  private scanToken(): boolean {
    const patterns = [
      { type: 'comment' as TokenType, regex: LEXER_PATTERNS.COMMENT },
      { type: 'mustache' as TokenType, regex: LEXER_PATTERNS.MUSTACHE },
      {
        type: 'selfClosing' as TokenType,
        regex: LEXER_PATTERNS.SELF_CLOSING_TAG,
      },
      { type: 'startTag' as TokenType, regex: LEXER_PATTERNS.START_TAG },
      { type: 'endTag' as TokenType, regex: LEXER_PATTERNS.END_TAG },
      { type: 'text' as TokenType, regex: LEXER_PATTERNS.TEXT },
    ];

    for (const pattern of patterns) {
      const regex = new RegExp(pattern.regex.source, 'y');
      regex.lastIndex = this.position;

      const match = regex.exec(this.input);
      if (match) {
        // 智能验证：检查标签的完整性和上下文
        if (pattern.type === 'startTag' || pattern.type === 'endTag') {
          const validation = this.validateTagToken(match[0], pattern.type);
          if (!validation.isValid) {
            // 如果不是有效的标签，作为文本处理
            this.addToken('text' as TokenType, match);
            this.position = regex.lastIndex;
            return true;
          }
        }

        this.addToken(pattern.type, match);
        this.position = regex.lastIndex;
        return true;
      }
    }

    return false;
  }

  /**
   * 智能验证标签token的有效性
   */
  private validateTagToken(
    value: string,
    type: TokenType,
  ): { isValid: boolean; reason?: string } {
    // 基础格式检查
    if (!value.startsWith('<')) {
      return { isValid: false, reason: '不以<开始' };
    }

    if (!value.endsWith('>')) {
      return { isValid: false, reason: '不以>结束' };
    }

    // 检查标签名的有效性（放在孤立属性检查之前，更准确）
    const tagNameMatch =
      type === 'endTag'
        ? value.match(/^<\/([a-zA-Z][a-zA-Z0-9-]*)\s*>$/)
        : value.match(/^<([a-zA-Z][a-zA-Z0-9-]*)/);

    if (!tagNameMatch) {
      // 只有在确实没有标签名时才检查孤立属性
      if (this.isOrphanedAttribute(value)) {
        return { isValid: false, reason: '孤立的属性片段' };
      }
      return { isValid: false, reason: '无效的标签名格式' };
    }

    const tagName = tagNameMatch[1];

    // 检查标签名是否合法
    if (!this.isValidTagName(tagName)) {
      return { isValid: false, reason: `无效的标签名: ${tagName}` };
    }

    // 对于有效标签名的情况，不再进行孤立属性检查
    return { isValid: true };
  }

  /**
   * 检查是否为孤立的属性片段
   */
  private isOrphanedAttribute(value: string): boolean {
    // 检查上下文：向前查看是否有未闭合的标签开始
    const beforeContext = this.input.substring(
      Math.max(0, this.position - 100),
      this.position,
    );
    const afterContext = this.input.substring(
      this.position,
      Math.min(this.input.length, this.position + 100),
    );

    // 如果包含=但格式不像完整标签，可能是属性片段
    if (value.includes('=')) {
      // 更宽松的标签结构检查，支持复杂属性值（包含大括号、嵌套引号等）
      const hasProperTagStructure = /^<[a-zA-Z][a-zA-Z0-9-]*(\s+[^>]*)?>/m.test(
        value,
      );

      // 额外检查：确保以 < 开头，以 > 结尾，包含有效标签名
      const startsWithTag = /^<[a-zA-Z][a-zA-Z0-9-]*/.test(value);
      const endsWithCloseBracket = value.trim().endsWith('>');

      // 如果不符合基本标签结构，才认为是孤立属性
      if (!hasProperTagStructure || !startsWithTag || !endsWithCloseBracket) {
        // 进一步验证：检查是否为纯属性片段（不以<标签名开头）
        const isPureAttribute = /^[a-zA-Z-]+\s*=/.test(value.trim());
        return isPureAttribute;
      }
    }

    // 检查是否在引号内（可能是属性值的一部分）
    const quotesBefore = (beforeContext.match(/"/g) || []).length;
    if (quotesBefore % 2 === 1) {
      return true; // 在未闭合的引号内
    }

    return false;
  }

  /**
   * 验证标签名是否合法
   */
  private isValidTagName(tagName: string): boolean {
    // HTML/XML标签名规则：以字母开始，可包含字母、数字、连字符
    return /^[a-zA-Z][a-zA-Z0-9-]*$/.test(tagName);
  }

  /**
   * 添加token到结果数组
   */
  private addToken(type: TokenType, match: RegExpExecArray): void {
    const value = match[0];
    const token: Token = {
      type,
      value,
      position: { line: this.line, column: this.column },
      groups: match.slice(1),
    };

    // 特殊处理：过滤纯空白文本
    if (type === 'text' && /^\s*$/.test(value)) {
      this.updatePosition(value);
      return;
    }

    // 对标签类型Token进行额外验证
    if ((type === 'startTag' || type === 'endTag') && token.groups) {
      // 验证第一个捕获组（标签名）是否存在
      const tagName = token.groups[0];
      if (!tagName || !this.isValidTagName(tagName)) {
        console.warn(
          `[Lexer] 检测到可能的标签名缺失: "${value}", 捕获组:`,
          token.groups,
        );
        // 尝试重新解析标签名
        const reMatch = value.match(/^<\/?([a-zA-Z][a-zA-Z0-9-]*)/);
        if (reMatch?.[1]) {
          token.groups[0] = reMatch[1];
          console.log(`[Lexer] 修复标签名: ${reMatch[1]}`);
        }
      }
    }

    this.tokens.push(token);
    this.updatePosition(value);
  }

  /**
   * 前进一个字符
   */
  private advance(): void {
    if (this.position < this.input.length) {
      const char = this.input[this.position];
      this.position++;

      if (char === '\n') {
        this.line++;
        this.column = 1;
      } else {
        this.column++;
      }
    }
  }

  /**
   * 更新位置信息
   */
  private updatePosition(text: string): void {
    for (const char of text) {
      if (char === '\n') {
        this.line++;
        this.column = 1;
      } else {
        this.column++;
      }
    }
  }
}

/**
 * 属性解析器
 */
export const AttributeParser = {
  /**
   * 解析属性字符串
   */
  parse(attributeString: string): Record<string, string> {
    const attributes: Record<string, string> = {};

    if (!attributeString?.trim()) {
      return attributes;
    }

    const regex = new RegExp(LEXER_PATTERNS.ATTRIBUTE.source, 'g');
    let match;

    while ((match = regex.exec(attributeString)) !== null) {
      const [, name, doubleQuoted, singleQuoted, unquoted] = match;
      const value = doubleQuoted ?? singleQuoted ?? unquoted ?? '';

      if (name) {
        attributes[name] = value;
      }
    }

    return attributes;
  },

  /**
   * 验证属性名
   */
  isValidAttributeName(name: string): boolean {
    return /^[a-zA-Z:@-][a-zA-Z0-9:@.-]*$/.test(name);
  },

  /**
   * 标准化属性值
   */
  normalizeAttributeValue(value: string): string {
    // 移除首尾引号
    if (
      (value.startsWith('"') && value.endsWith('"')) ||
      (value.startsWith("'") && value.endsWith("'"))
    ) {
      return value.slice(1, -1);
    }
    return value;
  },
};

/**
 * 表达式解析器（简化版）
 */
export class ExpressionParser {
  /**
   * 解析插值表达式
   */
  static parseMustache(expression: string): string {
    // 移除 {{ 和 }}
    if (expression.startsWith('{{') && expression.endsWith('}}')) {
      return expression.slice(2, -2).trim();
    }
    return expression.trim();
  }

  /**
   * 检查是否为有效的表达式
   */
  static isValidExpression(expression: string): boolean {
    // 简单的表达式验证
    const trimmed = expression.trim();

    // 空表达式
    if (!trimmed) {
      return false;
    }

    // 简单变量名
    if (/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(trimmed)) {
      return true;
    }

    // 属性访问
    if (/^[a-zA-Z_$][a-zA-Z0-9_$.]*$/.test(trimmed)) {
      return true;
    }

    // 数组访问
    if (/^[a-zA-Z_$][a-zA-Z0-9_$]*(\[[^\]]+\])*$/.test(trimmed)) {
      return true;
    }

    // 函数调用
    if (/^[a-zA-Z_$][a-zA-Z0-9_$.]*\([^)]*\)$/.test(trimmed)) {
      return true;
    }

    // 字面量
    if (
      /^(['"].*['"]|\d+(?:\.\d+)?|true|false|null|undefined)$/.test(trimmed)
    ) {
      return true;
    }

    return false;
  }

  /**
   * 提取表达式中的变量
   */
  static extractVariables(expression: string): string[] {
    const variables: string[] = [];
    const regex = /[a-zA-Z_$][a-zA-Z0-9_$]*/g;
    let match;

    while ((match = regex.exec(expression)) !== null) {
      const variable = match[0];

      // 排除关键字和字面量
      if (!this.isKeywordOrLiteral(variable)) {
        variables.push(variable);
      }
    }

    return [...new Set(variables)]; // 去重
  }

  /**
   * 检查是否为关键字或字面量
   */
  private static isKeywordOrLiteral(word: string): boolean {
    const keywords = [
      'true',
      'false',
      'null',
      'undefined',
      'var',
      'let',
      'const',
      'function',
      'if',
      'else',
      'for',
      'while',
      'do',
      'try',
      'catch',
      'finally',
      'throw',
      'return',
      'break',
      'continue',
      'typeof',
      'instanceof',
      'new',
      'this',
    ];

    return keywords.includes(word);
  }
}

/**
 * 词法分析错误类
 */
export class LexicalError extends Error {
  constructor(
    message: string,
    public position: Position,
    public code?: string,
  ) {
    super(
      `Lexical Error at line ${position.line}, column ${position.column}: ${message}`,
    );
    this.name = 'LexicalError';
  }
}

/**
 * 词法分析工具函数
 */
export const LexerUtils = {
  /**
   * 检查标签是否为自闭合
   */
  isSelfClosingTag(tagName: string): boolean {
    const selfClosingTags = [
      'area',
      'base',
      'br',
      'col',
      'embed',
      'hr',
      'img',
      'input',
      'link',
      'meta',
      'param',
      'source',
      'track',
      'wbr',
      // Lynx自闭合标签
      'image',
      'switch',
      'slider',
      'progress',
      'web-view',
      'cover-image',
      'checkbox',
      'radio',
      'page-meta',
      'canvas',
    ];

    return selfClosingTags.includes(tagName.toLowerCase());
  },

  /**
   * 检查标签是否为Lynx组件
   */
  isLynxComponent(tagName: string): boolean {
    return tagName.includes('-') || /^[A-Z]/.test(tagName);
  },

  /**
   * 检查属性是否为指令
   */
  isDirective(attributeName: string): boolean {
    return (
      attributeName.startsWith('lx:') ||
      attributeName.startsWith('bind') ||
      attributeName.startsWith('catch:')
    );
  },

  /**
   * 检查属性是否为事件
   */
  isEventAttribute(attributeName: string): boolean {
    return (
      attributeName.startsWith('bind') || attributeName.startsWith('catch:')
    );
  },

  /**
   * 生成唯一ID
   */
  generateId(): string {
    return Math.random().toString(36).substr(2, 8);
  },

  /**
   * 计算字符串哈希
   */
  simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(36);
  },
};
