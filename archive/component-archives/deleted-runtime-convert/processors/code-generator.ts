/**
 * @package runtime-convert
 * @description 代码生成器 - 将JSX AST转换为可执行的JavaScript代码
 */

import type { TransformConfig } from '../types';
import { BASE_CSS_TEMPLATE } from '../utils/constants';

/**
 * JSX节点接口（从ast-transformer导入的类型）
 */
interface JSXNode {
  type: string;
  [key: string]: any;
}

/**
 * 代码生成结果
 */
interface CodeGenerationResult {
  jsxCode: string;
  html: string;
  componentCode: string;
  runtimeCode: string;
}

export class BrowserCodeGenerator {
  private config: Required<TransformConfig>;

  constructor(config: TransformConfig = {}) {
    this.config = {
      componentId: config.componentId || this.generateComponentId(),
      enableScope: config.enableScope !== false,
      enableCache: config.enableCache !== false,
      maxCacheSize: config.maxCacheSize || 100,
      enableOptimization: config.enableOptimization !== false,
      enableStrictMode: config.enableStrictMode || false,
      usingComponents: config.usingComponents || {},
    };
  }

  /**
   * 生成完整的HTML页面
   */
  generateHTML(
    jsxResult: { code: string; ast: JSXNode; componentId: string },
    cssResult: { code: string; scopedCode: string },
    jsResult: { code: string; hasUserCode: boolean },
  ): string {
    const { componentId } = jsxResult;

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Lynx Runtime Preview</title>
    <style>
        ${BASE_CSS_TEMPLATE}
        
        /* 用户自定义样式 */
        ${cssResult.scopedCode}
        
        /* 运行时优化样式 */
        .lynx-runtime-container {
            width: 100%;
            min-height: 100vh;
            background: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .lynx-error-boundary {
            padding: 20px;
            background: #ffebee;
            border: 1px solid #ffcdd2;
            border-radius: 8px;
            margin: 20px;
            color: #c62828;
        }
        
        .lynx-loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #666;
        }
    </style>
    
    <!-- React 运行时 -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
</head>
<body>
    <div id="lynx-root" class="lynx-runtime-container">
        <div class="lynx-loading">正在加载...</div>
    </div>
    
    <script>
        ${this.generateRuntimeCode(jsxResult, jsResult, componentId)}
    </script>
    
    <!-- 错误处理 -->
    <script>
        window.addEventListener('error', function(event) {
            console.error('Runtime Error:', event.error);
            const root = document.getElementById('lynx-root');
            if (root) {
                root.innerHTML = \`
                    <div class="lynx-error-boundary">
                        <h3>运行时错误</h3>
                        <p>\${event.message}</p>
                        <details>
                            <summary>错误详情</summary>
                            <pre>\${event.error?.stack || 'No stack trace available'}</pre>
                        </details>
                    </div>
                \`;
            }
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled Promise Rejection:', event.reason);
        });
    </script>
</body>
</html>`;
  }

  /**
   * 生成运行时代码
   */
  private generateRuntimeCode(
    jsxResult: { code: string; ast: JSXNode; componentId: string },
    jsResult: { code: string; hasUserCode: boolean },
    componentId: string,
  ): string {
    return `
// ===== Lynx Runtime Environment =====
(function() {
    'use strict';
    
    const { useState, useEffect, useCallback, useMemo, useRef } = React;
    
    // ===== 运行时状态管理 =====
    const LynxRuntime = {
        // 全局状态
        globalState: new Map(),
        
        // 组件实例映射
        componentInstances: new Map(),
        
        // 事件总线
        eventBus: new EventTarget(),
        
        // 生命周期钩子
        lifecycleHooks: {
            onLoad: [],
            onShow: [],
            onHide: [],
            onUnload: []
        },
        
        // 注册生命周期钩子
        registerLifecycleHook(type, handler) {
            if (this.lifecycleHooks[type]) {
                this.lifecycleHooks[type].push(handler);
            }
        },
        
        // 触发生命周期钩子
        triggerLifecycle(type, data) {
            const hooks = this.lifecycleHooks[type] || [];
            hooks.forEach(hook => {
                try {
                    hook(data);
                } catch (error) {
                    console.error(\`Lifecycle hook error (\${type}):\`, error);
                }
            });
        },
        
        // 设置全局状态
        setGlobalState(key, value) {
            this.globalState.set(key, value);
            this.eventBus.dispatchEvent(new CustomEvent('stateChange', {
                detail: { key, value }
            }));
        },
        
        // 获取全局状态
        getGlobalState(key, defaultValue) {
            return this.globalState.has(key) ? this.globalState.get(key) : defaultValue;
        }
    };
    
    // ===== 组件基础类 =====
    function LynxComponent(initialData = {}) {
        const [data, setData] = useState(initialData);
        const [loading, setLoading] = useState(false);
        const instanceRef = useRef(null);
        
        // 创建组件实例
        const instance = useMemo(() => {
            if (!instanceRef.current) {
                instanceRef.current = {
                    data,
                    setData: (newData, callback) => {
                        if (typeof newData === 'function') {
                            setData(prevData => {
                                const result = newData(prevData);
                                if (callback) setTimeout(callback, 0);
                                return result;
                            });
                        } else {
                            setData(prevData => {
                                const result = { ...prevData, ...newData };
                                if (callback) setTimeout(callback, 0);
                                return result;
                            });
                        }
                    },
                    
                    // 模拟小程序API
                    showToast: (options) => {
                        const { title = '', icon = 'none', duration = 2000 } = options || {};
                        console.log(\`Toast: \${title}\`);
                        // 可以在这里实现真实的toast显示
                    },
                    
                    showModal: (options) => {
                        const { title = '', content = '', showCancel = true } = options || {};
                        return new Promise(resolve => {
                            const result = confirm(\`\${title}\\n\${content}\`);
                            resolve({ confirm: result, cancel: !result });
                        });
                    },
                    
                    navigateTo: (options) => {
                        const { url } = options || {};
                        console.log(\`Navigate to: \${url}\`);
                        // 可以在这里实现路由导航
                    },
                    
                    redirectTo: (options) => {
                        const { url } = options || {};
                        console.log(\`Redirect to: \${url}\`);
                        // 可以在这里实现路由重定向
                    },
                    
                    // 网络请求
                    request: (options) => {
                        const { url, method = 'GET', data, header = {} } = options || {};
                        return fetch(url, {
                            method,
                            headers: header,
                            body: method !== 'GET' ? JSON.stringify(data) : undefined
                        }).then(response => response.json());
                    },
                    
                    // 本地存储
                    setStorageSync: (key, data) => {
                        try {
                            localStorage.setItem(key, JSON.stringify(data));
                        } catch (error) {
                            console.error('setStorageSync error:', error);
                        }
                    },
                    
                    getStorageSync: (key) => {
                        try {
                            const item = localStorage.getItem(key);
                            return item ? JSON.parse(item) : null;
                        } catch (error) {
                            console.error('getStorageSync error:', error);
                            return null;
                        }
                    },
                    
                    removeStorageSync: (key) => {
                        try {
                            localStorage.removeItem(key);
                        } catch (error) {
                            console.error('removeStorageSync error:', error);
                        }
                    }
                };
                
                // 注册到运行时
                LynxRuntime.componentInstances.set('${componentId}', instanceRef.current);
            }
            return instanceRef.current;
        }, []);
        
        // 更新实例数据
        useEffect(() => {
            instance.data = data;
        }, [data, instance]);
        
        return instance;
    }
    
    // ===== 用户组件定义 =====
    function UserComponent() {
        const component = LynxComponent(${this.generateInitialData(jsResult)});
        
        // 用户脚本执行
        useEffect(() => {
            try {
                ${jsResult.hasUserCode ? this.wrapUserScript(jsResult.code) : '// No user script'}
                
                // 触发生命周期
                LynxRuntime.triggerLifecycle('onLoad', component.data);
                LynxRuntime.triggerLifecycle('onShow', component.data);
            } catch (error) {
                console.error('User script execution error:', error);
            }
        }, []);
        
        // 组件卸载时清理
        useEffect(() => {
            return () => {
                LynxRuntime.triggerLifecycle('onHide', component.data);
                LynxRuntime.triggerLifecycle('onUnload', component.data);
                LynxRuntime.componentInstances.delete('${componentId}');
            };
        }, []);
        
        // 渲染JSX
        try {
            return (
                ${jsxResult.code}
            );
        } catch (error) {
            console.error('Render error:', error);
            return React.createElement('div', {
                className: 'lynx-error-boundary'
            }, [
                React.createElement('h3', { key: 'title' }, '渲染错误'),
                React.createElement('p', { key: 'message' }, error.message),
                React.createElement('details', { key: 'details' }, [
                    React.createElement('summary', { key: 'summary' }, '错误详情'),
                    React.createElement('pre', { key: 'stack' }, error.stack || 'No stack trace')
                ])
            ]);
        }
    }
    
    // ===== 应用启动 =====
    function startLynxApp() {
        const rootElement = document.getElementById('lynx-root');
        if (!rootElement) {
            console.error('Root element not found');
            return;
        }
        
        try {
            // 创建React根节点并渲染
            const root = ReactDOM.createRoot ? 
                ReactDOM.createRoot(rootElement) : 
                { render: (element) => ReactDOM.render(element, rootElement) };
            
            root.render(React.createElement(UserComponent));
            
            console.log('Lynx app started successfully');
            console.log('Component ID:', '${componentId}');
            console.log('Runtime version:', '1.0.0');
        } catch (error) {
            console.error('Failed to start Lynx app:', error);
            rootElement.innerHTML = \`
                <div class="lynx-error-boundary">
                    <h3>应用启动失败</h3>
                    <p>\${error.message}</p>
                </div>
            \`;
        }
    }
    
    // ===== 全局工具函数 =====
    window.LynxRuntime = LynxRuntime;
    window.getCurrentPages = () => [{ route: 'index', options: {} }];
    window.getApp = () => ({ globalData: {} });
    
    // ===== 启动应用 =====
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', startLynxApp);
    } else {
        startLynxApp();
    }
})();`;
  }

  /**
   * 生成初始数据
   */
  private generateInitialData(jsResult: {
    code: string;
    hasUserCode: boolean;
  }): string {
    if (!jsResult.hasUserCode) {
      return '{}';
    }

    // 尝试从用户脚本中提取data对象
    const dataMatch = jsResult.code.match(/data\s*:\s*(\{[\s\S]*?\})/);
    if (dataMatch) {
      try {
        // 简单的data提取，可能需要更复杂的解析
        return dataMatch[1];
      } catch (error) {
        console.warn('Failed to extract initial data:', error);
      }
    }

    return '{}';
  }

  /**
   * 包装用户脚本
   */
  private wrapUserScript(userCode: string): string {
    // 处理用户脚本，使其在组件上下文中执行
    let processedCode = userCode;

    // 替换常见的小程序语法
    processedCode = processedCode.replace(
      /Page\s*\(\s*\{/g,
      '(function(component) { const pageConfig = {',
    );
    processedCode = processedCode.replace(
      /Component\s*\(\s*\{/g,
      '(function(component) { const componentConfig = {',
    );
    processedCode = processedCode.replace(
      /App\s*\(\s*\{/g,
      '(function(component) { const appConfig = {',
    );

    // 替换this引用
    processedCode = processedCode.replace(/this\.data/g, 'component.data');
    processedCode = processedCode.replace(
      /this\.setData/g,
      'component.setData',
    );
    processedCode = processedCode.replace(
      /this\.([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
      'component.$1',
    );

    // 注册生命周期方法
    const lifecycleMethods = [
      'onLoad',
      'onShow',
      'onHide',
      'onUnload',
      'onReady',
    ];
    lifecycleMethods.forEach(method => {
      const regex = new RegExp(
        `${method}\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{([\\s\\S]*?)\\}`,
        'g',
      );
      processedCode = processedCode.replace(
        regex,
        (match, body) => `${method}: function(options) {
          const self = component;
          LynxRuntime.registerLifecycleHook('${method}', function() {
            ${body}
          });
        }`,
      );
    });

    // 确保在组件上下文中执行
    return `
      (function(component) {
        // 用户脚本执行环境
        const self = component;
        const that = component;
        
        // 执行用户代码
        ${processedCode}
        
        // 如果定义了方法，绑定到组件实例
        if (typeof pageConfig !== 'undefined') {
          Object.keys(pageConfig).forEach(key => {
            if (typeof pageConfig[key] === 'function') {
              component[key] = pageConfig[key].bind(component);
            }
          });
        }
        
        if (typeof componentConfig !== 'undefined') {
          Object.keys(componentConfig).forEach(key => {
            if (typeof componentConfig[key] === 'function') {
              component[key] = componentConfig[key].bind(component);
            }
          });
        }
      })(component);
    `;
  }

  /**
   * 生成JSX代码字符串
   */
  generateJSXCode(jsx: JSXNode): string {
    return this.jsxToString(jsx);
  }

  /**
   * JSX节点转字符串
   */
  private jsxToString(node: JSXNode): string {
    if (!node) {
      return 'null';
    }

    switch (node.type) {
      case 'JSXFragment':
        return `React.Fragment, null, ${node.children.map((child: JSXNode) => this.jsxToString(child)).join(', ')}`;

      case 'JSXElement':
        const tagName = node.openingElement.name.name;
        const attributes = this.attributesToString(
          node.openingElement.attributes,
        );

        if (node.openingElement.selfClosing) {
          return `React.createElement('${tagName}', ${attributes})`;
        }

        const children = node.children
          .map((child: JSXNode) => this.jsxToString(child))
          .filter(Boolean)
          .join(', ');

        return `React.createElement('${tagName}', ${attributes}${children ? `, ${children}` : ''})`;

      case 'JSXText':
        return JSON.stringify(node.value);

      case 'JSXExpressionContainer':
        return this.expressionToString(node.expression);

      case 'JSXComment':
        return `/* ${node.value} */`;

      default:
        console.warn(`Unknown JSX node type: ${node.type}`);
        return 'null';
    }
  }

  /**
   * 属性转字符串
   */
  private attributesToString(attributes: any[]): string {
    if (!attributes || attributes.length === 0) {
      return 'null';
    }

    const props = attributes.map(attr => {
      const key = attr.name.name;
      let value;

      if (!attr.value) {
        value = 'true';
      } else if (attr.value.type === 'Literal') {
        value = JSON.stringify(attr.value.value);
      } else if (attr.value.type === 'JSXExpressionContainer') {
        value = this.expressionToString(attr.value.expression);
      } else {
        value = 'null';
      }

      return `"${key}": ${value}`;
    });

    return `{${props.join(', ')}}`;
  }

  /**
   * 表达式转字符串
   */
  private expressionToString(expression: any): string {
    if (!expression) {
      return 'null';
    }

    switch (expression.type) {
      case 'Identifier':
        return `component.data.${expression.name} !== undefined ? component.data.${expression.name} : ${expression.name}`;

      case 'Literal':
        return JSON.stringify(expression.value);

      case 'MemberExpression':
        const objectStr = this.expressionToString(expression.object);
        const propertyStr = expression.computed
          ? `[${this.expressionToString(expression.property)}]`
          : `.${expression.property.name}`;
        return `${objectStr}${propertyStr}`;

      case 'CallExpression':
        const callee = this.expressionToString(expression.callee);
        const args = expression.arguments
          .map((arg: any) => this.expressionToString(arg))
          .join(', ');
        return `${callee}(${args})`;

      case 'ArrowFunctionExpression':
        const params = expression.params.map((p: any) => p.name).join(', ');
        const body = this.expressionToString(expression.body);
        return `(${params}) => ${body}`;

      case 'LogicalExpression':
        const left = this.expressionToString(expression.left);
        const right = this.expressionToString(expression.right);
        return `(${left} ${expression.operator} ${right})`;

      case 'ConditionalExpression':
        const test = this.expressionToString(expression.test);
        const consequent = this.expressionToString(expression.consequent);
        const alternate = this.expressionToString(expression.alternate);
        return `(${test} ? ${consequent} : ${alternate})`;

      case 'ObjectExpression':
        const properties = expression.properties
          .map((prop: any) => {
            const key = prop.key.name || JSON.stringify(prop.key.value);
            const value = this.expressionToString(prop.value);
            return `${key}: ${value}`;
          })
          .join(', ');
        return `{${properties}}`;

      case 'ArrayExpression':
        const elements = expression.elements
          .map((elem: any) => this.expressionToString(elem))
          .join(', ');
        return `[${elements}]`;

      case 'TemplateLiteral':
        // 简化模板字符串处理
        const parts = [];
        for (let i = 0; i < expression.quasis.length; i++) {
          parts.push(JSON.stringify(expression.quasis[i].value.cooked));
          if (i < expression.expressions.length) {
            parts.push(this.expressionToString(expression.expressions[i]));
          }
        }
        return parts.join(' + ');

      case 'BlockStatement':
        const statements = expression.body
          .map((stmt: any) => this.expressionToString(stmt))
          .join('; ');
        return `{ ${statements} }`;

      case 'ExpressionStatement':
        return this.expressionToString(expression.expression);

      default:
        console.warn(`Unknown expression type: ${expression.type}`);
        return 'null';
    }
  }

  /**
   * 生成组件ID
   */
  private generateComponentId(): string {
    return Math.random().toString(36).substr(2, 8);
  }

  /**
   * 获取配置
   */
  getConfig(): Required<TransformConfig> {
    return { ...this.config };
  }
}

/**
 * 代码生成工具函数
 */
export const CodeGenUtils = {
  /**
   * 美化JavaScript代码（简单格式化）
   */
  formatCode(code: string): string {
    let formatted = code;
    let indentLevel = 0;
    const indent = '  ';

    // 简单的代码格式化
    formatted = formatted.replace(/\{/g, ` {\n${indent.repeat(++indentLevel)}`);
    formatted = formatted.replace(/\}/g, `\n${indent.repeat(--indentLevel)}}`);
    formatted = formatted.replace(/;/g, `;\n${indent.repeat(indentLevel)}`);

    return formatted;
  },

  /**
   * 压缩JavaScript代码
   */
  minifyCode(code: string): string {
    return code
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
      .replace(/\/\/.*$/gm, '') // 移除单行注释
      .replace(/\s+/g, ' ') // 压缩空白
      .replace(/\s*([{}();,])\s*/g, '$1') // 移除符号周围空白
      .trim();
  },

  /**
   * 计算代码复杂度（简单指标）
   */
  calculateComplexity(code: string): number {
    const complexityPatterns = [
      /if\s*\(/g,
      /for\s*\(/g,
      /while\s*\(/g,
      /switch\s*\(/g,
      /catch\s*\(/g,
      /function\s*\(/g,
      /=>\s*/g,
    ];

    return complexityPatterns.reduce((total, pattern) => {
      const matches = code.match(pattern);
      return total + (matches ? matches.length : 0);
    }, 1);
  },
};
