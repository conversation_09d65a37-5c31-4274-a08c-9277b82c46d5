/**
 * @package runtime-convert
 * @description CSS处理器 - 处理TTSS到CSS的转换
 */

import type { TransformConfig, CSSProcessingResult } from '../types';
import { CSS_UNIT_CONVERSION } from '../utils/constants';

/**
 * CSS规则类型
 */
interface CSSRule {
  type: 'rule' | 'media' | 'keyframes' | 'import';
  selector?: string;
  declarations?: CSSDeclaration[];
  rules?: CSSRule[];
  media?: string;
  keyframes?: string;
  href?: string;
}

/**
 * CSS声明类型
 */
interface CSSDeclaration {
  property: string;
  value: string;
  important?: boolean;
}

/**
 * CSS解析结果
 */
interface CSSParseResult {
  rules: CSSRule[];
  errors: string[];
}

export class BrowserCSSProcessor {
  private config: Required<TransformConfig>;

  constructor(config: TransformConfig = {}) {
    this.config = {
      componentId: config.componentId || this.generateComponentId(),
      enableScope: config.enableScope !== false,
      enableCache: config.enableCache !== false,
      maxCacheSize: config.maxCacheSize || 100,
      enableOptimization: config.enableOptimization !== false,
      enableStrictMode: config.enableStrictMode || false,
      usingComponents: config.usingComponents || {},
    };
  }

  /**
   * 处理TTSS/CSS代码
   */
  process(css: string, componentId?: string): CSSProcessingResult {
    if (!css?.trim()) {
      return {
        code: '',
        scopedCode: '',
        classes: [],
        ruleCount: 0,
      };
    }

    const actualComponentId = componentId || this.config.componentId;

    try {
      // 1. 预处理CSS
      const preprocessed = this.preprocessCSS(css);

      // 2. RPX单位转换 (固定转换规则)
      const rpxConverted = this.convertRpxUnits(preprocessed);

      // 3. 作用域化
      const scopedCSS = this.config.enableScope
        ? this.addSelectorScope(rpxConverted, actualComponentId)
        : rpxConverted;

      // 4. 解析CSS获取类名和规则数
      const parseResult = this.parseCSS(scopedCSS);
      const classes = this.extractClassNames(parseResult.rules);

      // 5. 优化（如果启用）
      const finalCSS = this.config.enableOptimization
        ? this.optimizeCSS(scopedCSS)
        : scopedCSS;

      return {
        code: rpxConverted,
        scopedCode: finalCSS,
        classes,
        ruleCount: parseResult.rules.length,
      };
    } catch (error) {
      console.error('CSS processing failed:', error);
      return {
        code: css,
        scopedCode: css,
        classes: [],
        ruleCount: 0,
      };
    }
  }

  /**
   * 预处理CSS
   */
  private preprocessCSS(css: string): string {
    let processed = css;

    // 1. 移除注释
    processed = processed.replace(/\/\*[\s\S]*?\*\//g, '');

    // 2. 标准化换行符
    processed = processed.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

    // 3. 移除多余空白
    processed = processed.replace(/\s+/g, ' ').trim();

    // 4. 标准化分号
    processed = processed.replace(/;(\s*})/g, '$1');

    return processed;
  }

  /**
   * CSS单位转换 - 遵循web-speedy-plugin标准规则
   */
  convertRpxUnits(css: string): string {
    // 只转换rpx单位，使用web-speedy-plugin默认的vw转换
    // 其他单位(px, em, rem, %, vh, vw等)保持不变，支持AI接口任意单位
    return css.replace(/(\d+(?:\.\d+)?)rpx/g, (match, value) => {
      const rpxValue = parseFloat(value);
      return CSS_UNIT_CONVERSION.convertRpxDefault(rpxValue);
    });
  }

  /**
   * 添加选择器作用域
   */
  addSelectorScope(css: string, componentId: string): string {
    const scopeAttribute = `[data-v-${componentId}]`;

    return css.replace(/([^{}]+)\s*\{/g, (match, selectors) => {
      const scopedSelectors = selectors
        .split(',')
        .map(selector => this.scopeSelector(selector.trim(), scopeAttribute))
        .join(', ');

      return `${scopedSelectors} {`;
    });
  }

  /**
   * 作用域化单个选择器
   */
  private scopeSelector(selector: string, scopeAttribute: string): string {
    // 保持全局选择器不变
    if (this.isGlobalSelector(selector)) {
      return selector;
    }

    // 处理深度选择器
    if (selector.includes('>>>') || selector.includes('/deep/')) {
      return this.handleDeepSelector(selector, scopeAttribute);
    }

    // 处理伪类选择器
    const pseudoMatch = selector.match(/^(.+?)(::?[a-z-]+.*?)$/);
    if (pseudoMatch) {
      const [, baseSelector, pseudo] = pseudoMatch;
      return `${scopeAttribute} ${baseSelector}${pseudo}`;
    }

    // 普通选择器添加作用域
    return `${scopeAttribute} ${selector}`;
  }

  /**
   * 检查是否为全局选择器
   */
  private isGlobalSelector(selector: string): boolean {
    const globalPatterns = [
      /^@/, // @media, @keyframes等
      /^:root/, // CSS变量
      /^html\b/, // html标签
      /^body\b/, // body标签
      /^\*/, // 通配符
      /^\.global-/, // 自定义全局类
      /^page\b/, // 小程序page
      /^#/, // ID选择器（通常为全局）
    ];

    return globalPatterns.some(pattern => pattern.test(selector));
  }

  /**
   * 处理深度选择器
   */
  private handleDeepSelector(selector: string, scopeAttribute: string): string {
    return selector
      .replace(/\s*>>>\s*/, ` ${scopeAttribute} `)
      .replace(/\s*\/deep\/\s*/, ` ${scopeAttribute} `);
  }

  /**
   * 简单CSS解析器
   */
  private parseCSS(css: string): CSSParseResult {
    const rules: CSSRule[] = [];
    const errors: string[] = [];

    try {
      let current = 0;
      const { length } = css;

      while (current < length) {
        current = this.skipWhitespace(css, current);

        if (current >= length) {
          break;
        }

        // 解析@规则
        if (css[current] === '@') {
          const atRuleResult = this.parseAtRule(css, current);
          rules.push(atRuleResult.rule);
          current = atRuleResult.end;
          continue;
        }

        // 解析普通规则
        const ruleResult = this.parseRule(css, current);
        if (ruleResult.rule) {
          rules.push(ruleResult.rule);
        }
        current = ruleResult.end;
      }
    } catch (error) {
      errors.push(
        `Parse error: ${error instanceof Error ? error.message : String(error)}`,
      );
    }

    return { rules, errors };
  }

  /**
   * 解析CSS规则
   */
  private parseRule(
    css: string,
    start: number,
  ): { rule: CSSRule | null; end: number } {
    let current = start;

    // 查找选择器结束位置（{）
    const selectorStart = current;
    while (current < css.length && css[current] !== '{') {
      current++;
    }

    if (current >= css.length) {
      return { rule: null, end: css.length };
    }

    const selector = css.slice(selectorStart, current).trim();
    current++; // 跳过 {

    // 解析声明
    const declarations: CSSDeclaration[] = [];
    const declarationStart = current;
    let braceCount = 1;

    while (current < css.length && braceCount > 0) {
      if (css[current] === '{') {
        braceCount++;
      } else if (css[current] === '}') {
        braceCount--;
      }
      current++;
    }

    const declarationBlock = css.slice(declarationStart, current - 1);
    const parsedDeclarations = this.parseDeclarations(declarationBlock);
    declarations.push(...parsedDeclarations);

    return {
      rule: {
        type: 'rule',
        selector,
        declarations,
      },
      end: current,
    };
  }

  /**
   * 解析@规则
   */
  private parseAtRule(
    css: string,
    start: number,
  ): { rule: CSSRule; end: number } {
    let current = start + 1; // 跳过@

    // 读取@规则名称
    const nameStart = current;
    while (current < css.length && /[a-zA-Z-]/.test(css[current])) {
      current++;
    }
    const ruleName = css.slice(nameStart, current);

    // 跳过空白
    current = this.skipWhitespace(css, current);

    // 读取@规则值
    const valueStart = current;
    while (
      current < css.length &&
      css[current] !== '{' &&
      css[current] !== ';'
    ) {
      current++;
    }
    const ruleValue = css.slice(valueStart, current).trim();

    if (css[current] === ';') {
      // 简单@规则（如@import）
      current++;
      return {
        rule: {
          type: ruleName === 'import' ? 'import' : 'rule',
          href: ruleValue,
        },
        end: current,
      };
    }

    if (css[current] === '{') {
      current++; // 跳过{

      // 读取@规则内容
      let braceCount = 1;
      const contentStart = current;

      while (current < css.length && braceCount > 0) {
        if (css[current] === '{') {
          braceCount++;
        } else if (css[current] === '}') {
          braceCount--;
        }
        current++;
      }

      const content = css.slice(contentStart, current - 1);

      return {
        rule: {
          type:
            ruleName === 'media'
              ? 'media'
              : ruleName === 'keyframes'
                ? 'keyframes'
                : 'rule',
          media: ruleName === 'media' ? ruleValue : undefined,
          keyframes: ruleName === 'keyframes' ? ruleValue : undefined,
          rules:
            ruleName === 'media' ? this.parseCSS(content).rules : undefined,
        },
        end: current,
      };
    }

    return {
      rule: { type: 'rule' },
      end: current,
    };
  }

  /**
   * 解析CSS声明
   */
  private parseDeclarations(block: string): CSSDeclaration[] {
    const declarations: CSSDeclaration[] = [];

    block.split(';').forEach(declaration => {
      const trimmed = declaration.trim();
      if (!trimmed) {
        return;
      }

      const colonIndex = trimmed.indexOf(':');
      if (colonIndex === -1) {
        return;
      }

      const property = trimmed.slice(0, colonIndex).trim();
      let value = trimmed.slice(colonIndex + 1).trim();

      let important = false;
      if (value.endsWith('!important')) {
        important = true;
        value = value.replace(/\s*!important$/, '').trim();
      }

      if (property && value) {
        declarations.push({ property, value, important });
      }
    });

    return declarations;
  }

  /**
   * 跳过空白字符
   */
  private skipWhitespace(css: string, start: number): number {
    let current = start;
    while (current < css.length && /\s/.test(css[current])) {
      current++;
    }
    return current;
  }

  /**
   * 提取类名
   */
  private extractClassNames(rules: CSSRule[]): string[] {
    const classNames = new Set<string>();

    rules.forEach(rule => {
      if (rule.type === 'rule' && rule.selector) {
        // 提取类选择器
        const classMatches = rule.selector.match(/\.([a-zA-Z0-9_-]+)/g);
        if (classMatches) {
          classMatches.forEach(match => {
            classNames.add(match.slice(1)); // 移除点号
          });
        }
      }

      if (rule.rules) {
        const nestedClasses = this.extractClassNames(rule.rules);
        nestedClasses.forEach(className => classNames.add(className));
      }
    });

    return Array.from(classNames);
  }

  /**
   * 优化CSS
   */
  private optimizeCSS(css: string): string {
    let optimized = css;

    // 1. 移除重复的空白
    optimized = optimized.replace(/\s+/g, ' ');

    // 2. 移除不必要的分号
    optimized = optimized.replace(/;(\s*})/g, '$1');

    // 3. 移除空规则
    optimized = optimized.replace(/[^{}]+\{\s*\}/g, '');

    // 4. 压缩颜色值
    optimized = this.compressColors(optimized);

    // 5. 压缩长度值
    optimized = this.compressLengths(optimized);

    return optimized.trim();
  }

  /**
   * 压缩颜色值
   */
  private compressColors(css: string): string {
    // 压缩16进制颜色
    return css.replace(
      /#([0-9a-fA-F])\1([0-9a-fA-F])\2([0-9a-fA-F])\3/g,
      '#$1$2$3',
    );
  }

  /**
   * 压缩长度值
   */
  private compressLengths(css: string): string {
    // 移除零值的单位
    return css.replace(
      /\b0(px|em|rem|%|vh|vw|vmin|vmax|ex|ch|cm|mm|in|pt|pc)\b/g,
      '0',
    );
  }

  /**
   * 生成CSS-in-JS对象
   */
  generateCssInJs(css: string): Record<string, any> {
    const parseResult = this.parseCSS(css);
    const cssObject: Record<string, any> = {};

    parseResult.rules.forEach(rule => {
      if (rule.type === 'rule' && rule.selector && rule.declarations) {
        const className = this.selectorToClassName(rule.selector);
        const styles: Record<string, any> = {};

        rule.declarations.forEach(decl => {
          const property = this.camelCaseProperty(decl.property);
          styles[property] = this.normalizePropertyValue(decl.value);
        });

        if (Object.keys(styles).length > 0) {
          cssObject[className] = styles;
        }
      }
    });

    return cssObject;
  }

  /**
   * 选择器转类名
   */
  private selectorToClassName(selector: string): string {
    // 简单处理：移除特殊字符，转为驼峰
    return selector
      .replace(/[^a-zA-Z0-9-_]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
      .toLowerCase();
  }

  /**
   * CSS属性名驼峰化
   */
  private camelCaseProperty(property: string): string {
    return property.replace(/-([a-z])/g, (match, letter) =>
      letter.toUpperCase(),
    );
  }

  /**
   * 标准化属性值
   */
  private normalizePropertyValue(value: string): any {
    // 数值
    if (/^\d+$/.test(value)) {
      return parseInt(value);
    }

    // 像素值
    if (/^\d+px$/.test(value)) {
      return parseInt(value);
    }

    // 布尔值
    if (value === 'true') {
      return true;
    }
    if (value === 'false') {
      return false;
    }

    // 字符串值
    return value;
  }

  /**
   * 生成组件ID
   */
  private generateComponentId(): string {
    return Math.random().toString(36).substr(2, 8);
  }

  /**
   * 获取配置
   */
  getConfig(): Required<TransformConfig> {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<TransformConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

/**
 * CSS处理工具函数
 */
export const CSSUtils = {
  /**
   * 验证CSS语法（简单验证）
   */
  isValidCSS(css: string): boolean {
    try {
      // 简单检查：括号匹配
      let braceCount = 0;
      for (const char of css) {
        if (char === '{') {
          braceCount++;
        }
        if (char === '}') {
          braceCount--;
        }
        if (braceCount < 0) {
          return false;
        }
      }
      return braceCount === 0;
    } catch {
      return false;
    }
  },

  /**
   * 计算CSS文件大小
   */
  calculateSize(css: string): number {
    return new Blob([css]).size;
  },

  /**
   * 计算压缩率
   */
  calculateCompressionRatio(original: string, compressed: string): number {
    const originalSize = this.calculateSize(original);
    const compressedSize = this.calculateSize(compressed);
    return originalSize > 0 ? (1 - compressedSize / originalSize) * 100 : 0;
  },

  /**
   * 生成CSS哈希
   */
  generateHash(css: string): string {
    let hash = 0;
    for (let i = 0; i < css.length; i++) {
      const char = css.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(36);
  },
};
