/**
 * @package runtime-convert
 * @description 测试用例集合
 */

import type { InputFiles, TransformResult } from '../types';

export interface TestCase {
  id: string;
  name: string;
  description: string;
  category: string;
  input: InputFiles;
  expected?: Partial<TransformResult>;
  shouldThrow?: boolean;
  timeout?: number;
  tags?: string[];
}

/**
 * 完整的测试用例集合
 */
export const TEST_CASES: TestCase[] = [
  // ===============================
  // 基础功能测试
  // ===============================
  {
    id: 'basic-001',
    name: '基础view标签转换',
    description: '测试view标签转换为div',
    category: 'basic',
    input: {
      ttml: '<view>Hello World</view>',
    },
    expected: {
      success: true,
    },
    tags: ['basic', 'element'],
  },

  {
    id: 'basic-002',
    name: '基础text标签转换',
    description: '测试text标签转换为span',
    category: 'basic',
    input: {
      ttml: '<text>Hello Text</text>',
    },
    expected: {
      success: true,
    },
    tags: ['basic', 'element'],
  },

  {
    id: 'basic-003',
    name: '嵌套标签转换',
    description: '测试嵌套的view和text标签',
    category: 'basic',
    input: {
      ttml: '<view><text>Nested Content</text></view>',
    },
    expected: {
      success: true,
    },
    tags: ['basic', 'nested'],
  },

  {
    id: 'basic-004',
    name: '自闭合标签转换',
    description: '测试image等自闭合标签',
    category: 'basic',
    input: {
      ttml: '<view><image src="test.jpg" /></view>',
    },
    expected: {
      success: true,
    },
    tags: ['basic', 'self-closing'],
  },

  {
    id: 'basic-005',
    name: '属性转换',
    description: '测试基础属性转换',
    category: 'basic',
    input: {
      ttml: '<view class="container" id="main">Content</view>',
    },
    expected: {
      success: true,
    },
    tags: ['basic', 'attributes'],
  },

  // ===============================
  // 指令系统测试
  // ===============================
  {
    id: 'directive-001',
    name: '条件渲染 lx:if',
    description: '测试lx:if指令',
    category: 'directive',
    input: {
      ttml: '<view lx:if="{{visible}}">Conditional Content</view>',
    },
    expected: {
      success: true,
    },
    tags: ['directive', 'conditional'],
  },

  {
    id: 'directive-002',
    name: '列表渲染 lx:for',
    description: '测试lx:for指令',
    category: 'directive',
    input: {
      ttml: '<view lx:for="item in items"><text>{{item}}</text></view>',
    },
    expected: {
      success: true,
    },
    tags: ['directive', 'loop'],
  },

  {
    id: 'directive-003',
    name: '列表渲染带索引',
    description: '测试lx:for指令带索引',
    category: 'directive',
    input: {
      ttml: '<view lx:for="item, index in items"><text>{{index}}: {{item}}</text></view>',
    },
    expected: {
      success: true,
    },
    tags: ['directive', 'loop', 'index'],
  },

  {
    id: 'directive-004',
    name: '组合指令',
    description: '测试lx:if和lx:for组合使用',
    category: 'directive',
    input: {
      ttml: '<view lx:for="item in items" lx:if="{{item.visible}}"><text>{{item.name}}</text></view>',
    },
    expected: {
      success: true,
    },
    tags: ['directive', 'combination'],
  },

  // ===============================
  // 事件系统测试
  // ===============================
  {
    id: 'event-001',
    name: '基础点击事件',
    description: '测试bindtap事件',
    category: 'event',
    input: {
      ttml: '<button bindtap="handleClick">Click Me</button>',
    },
    expected: {
      success: true,
    },
    tags: ['event', 'bind'],
  },

  {
    id: 'event-002',
    name: '事件冒泡阻止',
    description: '测试catch:tap事件',
    category: 'event',
    input: {
      ttml: '<button catch:tap="handleClick">Click Me</button>',
    },
    expected: {
      success: true,
    },
    tags: ['event', 'catch'],
  },

  {
    id: 'event-003',
    name: '表单事件',
    description: '测试input事件',
    category: 'event',
    input: {
      ttml: '<input bindinput="handleInput" bindfocus="handleFocus" />',
    },
    expected: {
      success: true,
    },
    tags: ['event', 'form'],
  },

  {
    id: 'event-004',
    name: '触摸事件',
    description: '测试触摸相关事件',
    category: 'event',
    input: {
      ttml: '<view bindtouchstart="onTouchStart" bindtouchend="onTouchEnd">Touch Area</view>',
    },
    expected: {
      success: true,
    },
    tags: ['event', 'touch'],
  },

  // ===============================
  // 样式系统测试
  // ===============================
  {
    id: 'style-001',
    name: 'RPX单位转换',
    description: '测试rpx到vw的转换',
    category: 'style',
    input: {
      ttml: '<view class="container">Content</view>',
      ttss: '.container { width: 750rpx; height: 200rpx; }',
    },
    expected: {
      success: true,
    },
    tags: ['style', 'rpx'],
  },

  {
    id: 'style-002',
    name: '内联样式转换',
    description: '测试style属性转换',
    category: 'style',
    input: {
      ttml: '<view style="width: 100rpx; color: red;">Styled Content</view>',
    },
    expected: {
      success: true,
    },
    tags: ['style', 'inline'],
  },

  {
    id: 'style-003',
    name: 'CSS作用域化',
    description: '测试CSS作用域隔离',
    category: 'style',
    input: {
      ttml: '<view class="test">Content</view>',
      ttss: '.test { color: blue; } .global { margin: 0; }',
    },
    expected: {
      success: true,
    },
    tags: ['style', 'scope'],
  },

  {
    id: 'style-004',
    name: '复杂CSS规则',
    description: '测试媒体查询、伪类等复杂CSS',
    category: 'style',
    input: {
      ttml: '<view class="responsive">Content</view>',
      ttss: `
        .responsive { 
          width: 100%; 
        }
        .responsive:hover { 
          color: blue; 
        }
        @media (max-width: 768px) {
          .responsive { 
            width: 50%; 
          }
        }
      `,
    },
    expected: {
      success: true,
    },
    tags: ['style', 'complex', 'media-query'],
  },

  // ===============================
  // 模板语法测试
  // ===============================
  {
    id: 'template-001',
    name: '简单插值',
    description: '测试{{}}插值表达式',
    category: 'template',
    input: {
      ttml: '<text>Hello {{name}}</text>',
    },
    expected: {
      success: true,
    },
    tags: ['template', 'interpolation'],
  },

  {
    id: 'template-002',
    name: '复杂插值',
    description: '测试复杂表达式插值',
    category: 'template',
    input: {
      ttml: '<text>Count: {{items.length}}</text>',
    },
    expected: {
      success: true,
    },
    tags: ['template', 'expression'],
  },

  {
    id: 'template-003',
    name: '属性插值',
    description: '测试属性中的插值',
    category: 'template',
    input: {
      ttml: '<image src="{{imageUrl}}" alt="{{imageAlt}}" />',
    },
    expected: {
      success: true,
    },
    tags: ['template', 'attribute'],
  },

  {
    id: 'template-004',
    name: '混合插值',
    description: '测试文本和插值混合',
    category: 'template',
    input: {
      ttml: '<text>用户{{user.name}}，今天是{{date}}，天气{{weather}}</text>',
    },
    expected: {
      success: true,
    },
    tags: ['template', 'mixed'],
  },

  // ===============================
  // 组件系统测试
  // ===============================
  {
    id: 'component-001',
    name: '自定义组件',
    description: '测试自定义组件转换',
    category: 'component',
    input: {
      ttml: '<my-component prop="value">Content</my-component>',
      json: JSON.stringify({
        usingComponents: {
          'my-component': './components/MyComponent',
        },
      }),
    },
    expected: {
      success: true,
    },
    tags: ['component', 'custom'],
  },

  {
    id: 'component-002',
    name: '组件属性传递',
    description: '测试组件属性传递',
    category: 'component',
    input: {
      ttml: '<user-card name="{{user.name}}" avatar="{{user.avatar}}" />',
      json: JSON.stringify({
        usingComponents: {
          'user-card': './components/UserCard',
        },
      }),
    },
    expected: {
      success: true,
    },
    tags: ['component', 'props'],
  },

  {
    id: 'component-003',
    name: '嵌套组件',
    description: '测试组件嵌套使用',
    category: 'component',
    input: {
      ttml: `
        <page-container>
          <header-component title="{{title}}" />
          <content-area>
            <user-list items="{{users}}" />
          </content-area>
        </page-container>
      `,
      json: JSON.stringify({
        usingComponents: {
          'page-container': './components/PageContainer',
          'header-component': './components/Header',
          'content-area': './components/ContentArea',
          'user-list': './components/UserList',
        },
      }),
    },
    expected: {
      success: true,
    },
    tags: ['component', 'nested'],
  },

  // ===============================
  // JavaScript处理测试
  // ===============================
  {
    id: 'js-001',
    name: '基础JavaScript',
    description: '测试基础JavaScript处理',
    category: 'javascript',
    input: {
      ttml: '<view>{{message}}</view>',
      js: `
        const data = {
          message: 'Hello from JS'
        };
        
        function handleClick() {
          console.log('Clicked');
        }
      `,
    },
    expected: {
      success: true,
    },
    tags: ['javascript', 'basic'],
  },

  {
    id: 'js-002',
    name: 'Page语法转换',
    description: '测试Page()语法转换',
    category: 'javascript',
    input: {
      ttml: '<view>{{count}}</view>',
      js: `
        Card({
          data: {
            count: 0
          },
          
          onLoad() {
            console.log('Page loaded');
          },
          
          increment() {
            this.setData({
              count: this.data.count + 1
            });
          }
        });
      `,
    },
    expected: {
      success: true,
    },
    tags: ['javascript', 'page'],
  },

  {
    id: 'js-003',
    name: 'Component语法转换',
    description: '测试Component()语法转换',
    category: 'javascript',
    input: {
      ttml: '<view>{{value}}</view>',
      js: `
        Component({
          data: {
            value: 'component'
          },
          
          methods: {
            handleTap() {
              this.triggerEvent('tap', { value: this.data.value });
            }
          }
        });
      `,
    },
    expected: {
      success: true,
    },
    tags: ['javascript', 'component'],
  },

  // ===============================
  // 错误处理测试
  // ===============================
  {
    id: 'error-001',
    name: '空TTML内容',
    description: '测试空TTML内容的错误处理',
    category: 'error',
    input: {
      ttml: '',
    },
    shouldThrow: true,
    tags: ['error', 'validation'],
  },

  {
    id: 'error-002',
    name: '无效TTML语法',
    description: '测试无效TTML语法的错误处理',
    category: 'error',
    input: {
      ttml: '<view><text>Unclosed tag</view>',
    },
    shouldThrow: true,
    tags: ['error', 'syntax'],
  },

  {
    id: 'error-003',
    name: '无效JSON配置',
    description: '测试无效JSON配置的处理',
    category: 'error',
    input: {
      ttml: '<view>Content</view>',
      json: '{ invalid json',
    },
    expected: {
      success: true, // 应该忽略无效JSON继续处理
    },
    tags: ['error', 'json'],
  },

  {
    id: 'error-004',
    name: '无效指令语法',
    description: '测试无效指令语法',
    category: 'error',
    input: {
      ttml: '<view lx:for="invalid syntax">Content</view>',
    },
    shouldThrow: true,
    tags: ['error', 'directive'],
  },

  // ===============================
  // 性能测试
  // ===============================
  {
    id: 'perf-001',
    name: '大量元素转换',
    description: '测试大量元素的转换性能',
    category: 'performance',
    input: {
      ttml: `<view>${Array(500).fill('<view><text>Item</text></view>').join('')}</view>`,
    },
    expected: {
      success: true,
    },
    timeout: 5000,
    tags: ['performance', 'large'],
  },

  {
    id: 'perf-002',
    name: '复杂嵌套结构',
    description: '测试深度嵌套结构的性能',
    category: 'performance',
    input: {
      ttml:
        Array(20).fill('<view>').join('') +
        'Deep Content' +
        Array(20).fill('</view>').join(''),
    },
    expected: {
      success: true,
    },
    timeout: 3000,
    tags: ['performance', 'nested'],
  },

  {
    id: 'perf-003',
    name: '大量CSS规则',
    description: '测试大量CSS规则的处理性能',
    category: 'performance',
    input: {
      ttml: '<view class="test">Content</view>',
      ttss: Array(200)
        .fill(0)
        .map((_, i) => `.class${i} { width: ${i}rpx; }`)
        .join('\n'),
    },
    expected: {
      success: true,
    },
    timeout: 3000,
    tags: ['performance', 'css'],
  },

  // ===============================
  // 边界情况测试
  // ===============================
  {
    id: 'edge-001',
    name: '特殊字符处理',
    description: '测试特殊字符的处理',
    category: 'edge',
    input: {
      ttml: '<text>&lt;&gt;&amp;"\'</text>',
    },
    expected: {
      success: true,
    },
    tags: ['edge', 'characters'],
  },

  {
    id: 'edge-002',
    name: '空白字符处理',
    description: '测试各种空白字符的处理',
    category: 'edge',
    input: {
      ttml: '<view>   \n\t  <text>  Content  </text>  \n\t  </view>',
    },
    expected: {
      success: true,
    },
    tags: ['edge', 'whitespace'],
  },

  {
    id: 'edge-003',
    name: 'Unicode字符处理',
    description: '测试Unicode字符的处理',
    category: 'edge',
    input: {
      ttml: '<text>🌟 Unicode 测试 🎉 emoji</text>',
    },
    expected: {
      success: true,
    },
    tags: ['edge', 'unicode'],
  },

  {
    id: 'edge-004',
    name: '极长属性值',
    description: '测试极长属性值的处理',
    category: 'edge',
    input: {
      ttml: `<view class="${'a'.repeat(1000)}">Content</view>`,
    },
    expected: {
      success: true,
    },
    tags: ['edge', 'long-attribute'],
  },
];

/**
 * 根据标签过滤测试用例
 */
export function getTestCasesByTag(tag: string): TestCase[] {
  return TEST_CASES.filter(testCase => testCase.tags?.includes(tag));
}

/**
 * 根据类别过滤测试用例
 */
export function getTestCasesByCategory(category: string): TestCase[] {
  return TEST_CASES.filter(testCase => testCase.category === category);
}

/**
 * 获取所有类别
 */
export function getAllCategories(): string[] {
  return [...new Set(TEST_CASES.map(tc => tc.category))];
}

/**
 * 获取所有标签
 */
export function getAllTags(): string[] {
  return [...new Set(TEST_CASES.flatMap(tc => tc.tags || []))];
}

/**
 * 根据ID获取测试用例
 */
export function getTestCaseById(id: string): TestCase | undefined {
  return TEST_CASES.find(tc => tc.id === id);
}

/**
 * 测试运行器配置
 */
export interface TestRunnerConfig {
  timeout: number;
  verbose: boolean;
  stopOnFirstFailure: boolean;
  parallel: boolean;
  maxConcurrency: number;
}

export const DEFAULT_TEST_CONFIG: TestRunnerConfig = {
  timeout: 10000,
  verbose: true,
  stopOnFirstFailure: false,
  parallel: false,
  maxConcurrency: 3,
};
