/**
 * @package runtime-convert
 * @description AST转换器 - 将Lynx AST转换为React JSX
 */

import type {
  ASTNode,
  ElementNode,
  TextNode,
  MustacheNode,
  CommentNode,
  TransformConfig,
  ElementMapping,
  EventMapping,
  TransformError,
  TransformErrorImpl,
  ErrorType,
} from '../types';
import {
  ASTNodeType,
  isElementNode,
  isTextNode,
  isMustacheNode,
  isCommentNode,
} from '../types';
import {
  ELEMENT_MAPPING,
  EVENT_DIRECTIVE_MAPPING,
  COMMON_ATTRIBUTE_MAPPING,
  SELF_CLOSING_TAGS,
} from '../utils/constants';
import { ExpressionParser, LexerUtils } from '../parsers/lexer';

// JSX节点类型定义
interface JSXNode {
  type: string;
  [key: string]: any;
}

interface JSXElement extends JSXNode {
  type: 'JSXElement';
  openingElement: JSXOpeningElement;
  closingElement?: JSXClosingElement;
  children: JSXNode[];
}

interface JSXOpeningElement extends JSXNode {
  type: 'JSXOpeningElement';
  name: JSXIdentifier;
  attributes: JSXAttribute[];
  selfClosing: boolean;
}

interface JSXClosingElement extends JSXNode {
  type: 'JSXClosingElement';
  name: JSXIdentifier;
}

interface JSXAttribute extends JSXNode {
  type: 'JSXAttribute';
  name: JSXIdentifier;
  value: JSXAttributeValue | null;
}

interface JSXIdentifier extends JSXNode {
  type: 'JSXIdentifier';
  name: string;
}

interface JSXAttributeValue extends JSXNode {
  type: 'Literal' | 'JSXExpressionContainer';
  value?: any;
  expression?: JSXNode;
}

interface JSXText extends JSXNode {
  type: 'JSXText';
  value: string;
}

interface JSXExpressionContainer extends JSXNode {
  type: 'JSXExpressionContainer';
  expression: JSXNode;
}

interface JSXFragment extends JSXNode {
  type: 'JSXFragment';
  children: JSXNode[];
}

/**
 * 转换上下文
 */
interface TransformContext {
  componentId: string;
  scope: Record<string, boolean>;
  isRoot: boolean;
  config: Required<TransformConfig>;
  usingComponents: Record<string, string>;
}

export class BrowserASTTransformer {
  private config: Required<TransformConfig>;

  constructor(config: TransformConfig = {}) {
    this.config = {
      componentId: config.componentId || this.generateComponentId(),
      enableScope: config.enableScope !== false,
      enableCache: config.enableCache !== false,
      maxCacheSize: config.maxCacheSize || 100,
      enableOptimization: config.enableOptimization !== false,
      enableStrictMode: config.enableStrictMode || false,
      usingComponents: config.usingComponents || {},
    };
  }

  /**
   * 转换AST为JSX
   */
  transform(ast: ASTNode): JSXNode {
    const context: TransformContext = {
      componentId: this.config.componentId,
      scope: {},
      isRoot: true,
      config: this.config,
      usingComponents: this.config.usingComponents,
    };

    return this.transformNode(ast, context);
  }

  /**
   * 转换单个节点
   */
  private transformNode(node: ASTNode, context: TransformContext): JSXNode {
    try {
      switch (node.type) {
        case ASTNodeType.Root:
          return this.transformRoot(node, context);
        case ASTNodeType.Element:
          return this.transformElement(node as ElementNode, context);
        case ASTNodeType.Text:
          return this.transformText(node as TextNode, context);
        case ASTNodeType.Mustache:
          return this.transformMustache(node as MustacheNode, context);
        case ASTNodeType.Comment:
          return this.transformComment(node as CommentNode, context);
        default:
          throw new TransformErrorImpl(
            `Unknown node type: ${node.type}`,
            'TRANSFORM_ERROR' as ErrorType,
            node.position,
          );
      }
    } catch (error) {
      if (error instanceof TransformErrorImpl) {
        throw error;
      }
      throw new TransformErrorImpl(
        `Failed to transform node: ${error instanceof Error ? error.message : String(error)}`,
        'TRANSFORM_ERROR' as ErrorType,
        node.position,
      );
    }
  }

  /**
   * 转换根节点
   */
  private transformRoot(node: ASTNode, context: TransformContext): JSXFragment {
    const children = node.children
      .map(child => this.transformNode(child, { ...context, isRoot: false }))
      .filter(Boolean);

    return {
      type: 'JSXFragment',
      children,
    };
  }

  /**
   * 转换元素节点
   */
  private transformElement(
    node: ElementNode,
    context: TransformContext,
  ): JSXNode {
    const { tagName, attributes } = node;

    // 检查是否为指令元素
    if (this.hasDirectives(attributes)) {
      return this.transformDirectiveElement(node, context);
    }

    // 检查是否为自定义组件
    if (this.isCustomComponent(tagName, context)) {
      return this.transformCustomComponent(node, context);
    }

    // 普通元素转换
    const mapping = ELEMENT_MAPPING[tagName];
    if (!mapping) {
      console.warn(`Unknown element: ${tagName}, treating as div`);
      return this.transformAsDiv(node, context);
    }

    return this.transformStandardElement(node, mapping, context);
  }

  /**
   * 转换标准元素
   */
  private transformStandardElement(
    node: ElementNode,
    mapping: ElementMapping,
    context: TransformContext,
  ): JSXElement {
    const { tagName, attributes, children } = node;
    const isSelfClosing = mapping.selfClosing || SELF_CLOSING_TAGS.has(tagName);

    const jsxElement: JSXElement = {
      type: 'JSXElement',
      openingElement: {
        type: 'JSXOpeningElement',
        name: { type: 'JSXIdentifier', name: mapping.tag },
        attributes: this.transformAttributes(attributes, mapping, context),
        selfClosing: isSelfClosing,
      },
      children: isSelfClosing
        ? []
        : children
            .map(child => this.transformNode(child, context))
            .filter(Boolean),
    };

    if (!isSelfClosing) {
      jsxElement.closingElement = {
        type: 'JSXClosingElement',
        name: { type: 'JSXIdentifier', name: mapping.tag },
      };
    }

    return jsxElement;
  }

  /**
   * 转换为div元素（未知元素的fallback）
   */
  private transformAsDiv(
    node: ElementNode,
    context: TransformContext,
  ): JSXElement {
    const mapping: ElementMapping = {
      tag: 'div',
      props: { className: `lynx-unknown-${node.tagName}` },
    };

    return this.transformStandardElement(node, mapping, context);
  }

  /**
   * 转换自定义组件
   */
  private transformCustomComponent(
    node: ElementNode,
    context: TransformContext,
  ): JSXElement {
    const { tagName, attributes, children } = node;
    const componentName = this.pascalCase(tagName);

    const jsxElement: JSXElement = {
      type: 'JSXElement',
      openingElement: {
        type: 'JSXOpeningElement',
        name: { type: 'JSXIdentifier', name: componentName },
        attributes: this.transformComponentAttributes(attributes, context),
        selfClosing: children.length === 0,
      },
      children: children
        .map(child => this.transformNode(child, context))
        .filter(Boolean),
    };

    if (children.length > 0) {
      jsxElement.closingElement = {
        type: 'JSXClosingElement',
        name: { type: 'JSXIdentifier', name: componentName },
      };
    }

    return jsxElement;
  }

  /**
   * 转换指令元素
   */
  private transformDirectiveElement(
    node: ElementNode,
    context: TransformContext,
  ): JSXNode {
    const { attributes } = node;

    // 处理条件渲染
    if (attributes['lx:if'] || attributes['lx:elif'] || attributes['lx:else']) {
      return this.transformConditionalElement(node, context);
    }

    // 处理列表渲染
    if (attributes['lx:for']) {
      return this.transformForElement(node, context);
    }

    // 移除指令属性后正常转换
    const cleanAttributes = this.removeDirectiveAttributes(attributes);
    const cleanNode = { ...node, attributes: cleanAttributes };

    return this.transformElement(cleanNode, context);
  }

  /**
   * 转换条件渲染元素
   */
  private transformConditionalElement(
    node: ElementNode,
    context: TransformContext,
  ): JSXExpressionContainer {
    const { attributes } = node;
    let condition: JSXNode | null = null;

    if (attributes['lx:if']) {
      condition = this.parseExpression(attributes['lx:if'], context);
    } else if (attributes['lx:elif']) {
      condition = this.parseExpression(attributes['lx:elif'], context);
    }

    const element = this.transformElement(
      {
        ...node,
        attributes: this.removeDirectiveAttributes(attributes),
      },
      context,
    );

    if (condition) {
      return {
        type: 'JSXExpressionContainer',
        expression: {
          type: 'LogicalExpression',
          operator: '&&',
          left: condition,
          right: element,
        },
      };
    }

    return {
      type: 'JSXExpressionContainer',
      expression: element,
    };
  }

  /**
   * 转换列表渲染元素
   */
  private transformForElement(
    node: ElementNode,
    context: TransformContext,
  ): JSXExpressionContainer {
    const forValue = node.attributes['lx:for'];
    const { itemName, indexName, listExpression } =
      this.parseForExpression(forValue);

    const element = this.transformElement(
      {
        ...node,
        attributes: this.removeDirectiveAttributes(node.attributes),
      },
      {
        ...context,
        scope: { ...context.scope, [itemName]: true, [indexName]: true },
      },
    );

    // 添加key属性
    if (element.type === 'JSXElement') {
      const keyAttr: JSXAttribute = {
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name: 'key' },
        value: {
          type: 'JSXExpressionContainer',
          expression: { type: 'Identifier', name: indexName },
        },
      };
      element.openingElement.attributes.unshift(keyAttr);
    }

    return {
      type: 'JSXExpressionContainer',
      expression: {
        type: 'CallExpression',
        callee: {
          type: 'MemberExpression',
          object: this.parseExpression(listExpression, context),
          property: { type: 'Identifier', name: 'map' },
        },
        arguments: [
          {
            type: 'ArrowFunctionExpression',
            params: [
              { type: 'Identifier', name: itemName },
              { type: 'Identifier', name: indexName },
            ],
            body: element,
          },
        ],
      },
    };
  }

  /**
   * 转换属性
   */
  private transformAttributes(
    attributes: Record<string, string>,
    mapping: ElementMapping,
    context: TransformContext,
  ): JSXAttribute[] {
    const jsxAttributes: JSXAttribute[] = [];

    // 添加默认属性
    if (mapping.props) {
      Object.entries(mapping.props).forEach(([key, value]) => {
        jsxAttributes.push({
          type: 'JSXAttribute',
          name: { type: 'JSXIdentifier', name: key },
          value: this.createAttributeValue(value),
        });
      });
    }

    // 转换用户属性
    Object.entries(attributes).forEach(([name, value]) => {
      // 跳过指令属性
      if (name.startsWith('lx:')) {
        return;
      }

      // 处理事件属性
      if (name.startsWith('bind') || name.startsWith('catch:')) {
        const eventAttr = this.transformEventAttribute(name, value, context);
        if (eventAttr) {
          jsxAttributes.push(eventAttr);
        }
        return;
      }

      // 普通属性转换
      const transformedAttr = this.transformAttribute(
        name,
        value,
        mapping,
        context,
      );
      if (transformedAttr) {
        jsxAttributes.push(transformedAttr);
      }
    });

    // 添加组件作用域属性
    if (context.config.enableScope) {
      jsxAttributes.push({
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name: `data-v-${context.componentId}` },
        value: { type: 'Literal', value: '' },
      });
    }

    return jsxAttributes;
  }

  /**
   * 转换单个属性
   */
  private transformAttribute(
    name: string,
    value: string,
    mapping: ElementMapping,
    context: TransformContext,
  ): JSXAttribute | null {
    // 属性名映射
    const mappedName = this.getMappedAttributeName(name, mapping);

    // 特殊属性处理
    if (name === 'style') {
      return this.transformStyleAttribute(mappedName, value, context);
    }

    if (name === 'class') {
      return this.transformClassAttribute(mappedName, value, context);
    }

    // 插值表达式处理
    if (value.includes('{{')) {
      return {
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name: mappedName },
        value: {
          type: 'JSXExpressionContainer',
          expression: this.parseTemplateString(value, context),
        },
      };
    }

    // 布尔属性处理
    if (this.isBooleanAttribute(name)) {
      return {
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name: mappedName },
        value:
          value === 'true' || value === name
            ? { type: 'Literal', value: true }
            : null,
      };
    }

    // 普通字符串值
    return {
      type: 'JSXAttribute',
      name: { type: 'JSXIdentifier', name: mappedName },
      value: { type: 'Literal', value },
    };
  }

  /**
   * 转换样式属性
   */
  private transformStyleAttribute(
    name: string,
    value: string,
    context: TransformContext,
  ): JSXAttribute {
    if (value.includes('{{')) {
      // 动态样式
      return {
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name },
        value: {
          type: 'JSXExpressionContainer',
          expression: this.parseTemplateString(value, context),
        },
      };
    } else {
      // 静态样式，转换为对象
      const styleObject = this.parseInlineStyle(value);
      return {
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name },
        value: {
          type: 'JSXExpressionContainer',
          expression: {
            type: 'ObjectExpression',
            properties: Object.entries(styleObject).map(([prop, val]) => ({
              type: 'Property',
              key: { type: 'Identifier', name: this.camelCase(prop) },
              value: { type: 'Literal', value: val },
            })),
          },
        },
      };
    }
  }

  /**
   * 转换类名属性
   */
  private transformClassAttribute(
    name: string,
    value: string,
    context: TransformContext,
  ): JSXAttribute {
    if (value.includes('{{')) {
      return {
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name },
        value: {
          type: 'JSXExpressionContainer',
          expression: this.parseTemplateString(value, context),
        },
      };
    } else {
      return {
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name },
        value: { type: 'Literal', value },
      };
    }
  }

  /**
   * 转换事件属性
   */
  private transformEventAttribute(
    eventName: string,
    handlerExpression: string,
    context: TransformContext,
  ): JSXAttribute | null {
    const eventConfig = EVENT_DIRECTIVE_MAPPING[eventName];
    if (!eventConfig) {
      console.warn(`Unknown event: ${eventName}`);
      return null;
    }

    const { reactEvent, stopPropagation } = eventConfig;

    return {
      type: 'JSXAttribute',
      name: { type: 'JSXIdentifier', name: reactEvent },
      value: {
        type: 'JSXExpressionContainer',
        expression: {
          type: 'ArrowFunctionExpression',
          params: [{ type: 'Identifier', name: 'e' }],
          body: {
            type: 'BlockStatement',
            body: [
              ...(stopPropagation
                ? [
                    {
                      type: 'ExpressionStatement',
                      expression: {
                        type: 'CallExpression',
                        callee: {
                          type: 'MemberExpression',
                          object: { type: 'Identifier', name: 'e' },
                          property: {
                            type: 'Identifier',
                            name: 'stopPropagation',
                          },
                        },
                        arguments: [],
                      },
                    },
                  ]
                : []),
              {
                type: 'ExpressionStatement',
                expression: {
                  type: 'CallExpression',
                  callee: this.parseExpression(handlerExpression, context),
                  arguments: [{ type: 'Identifier', name: 'e' }],
                },
              },
            ],
          },
        },
      },
    };
  }

  /**
   * 转换组件属性
   */
  private transformComponentAttributes(
    attributes: Record<string, string>,
    context: TransformContext,
  ): JSXAttribute[] {
    const jsxAttributes: JSXAttribute[] = [];

    Object.entries(attributes).forEach(([name, value]) => {
      // 跳过指令属性
      if (
        name.startsWith('lx:') ||
        name.startsWith('bind') ||
        name.startsWith('catch:')
      ) {
        return;
      }

      const attr = this.transformAttribute(name, value, { tag: '' }, context);
      if (attr) {
        jsxAttributes.push(attr);
      }
    });

    return jsxAttributes;
  }

  /**
   * 转换文本节点
   */
  private transformText(node: TextNode, context: TransformContext): JSXText {
    return {
      type: 'JSXText',
      value: node.content,
    };
  }

  /**
   * 转换插值表达式
   */
  private transformMustache(
    node: MustacheNode,
    context: TransformContext,
  ): JSXExpressionContainer {
    return {
      type: 'JSXExpressionContainer',
      expression: this.parseExpression(node.expression, context),
    };
  }

  /**
   * 转换注释
   */
  private transformComment(
    node: CommentNode,
    context: TransformContext,
  ): JSXNode {
    return {
      type: 'JSXComment',
      value: node.content,
    };
  }

  // ===============================
  // 工具方法
  // ===============================

  /**
   * 检查是否有指令
   */
  private hasDirectives(attributes: Record<string, string>): boolean {
    return Object.keys(attributes).some(
      name =>
        name.startsWith('lx:') ||
        name.startsWith('bind') ||
        name.startsWith('catch:'),
    );
  }

  /**
   * 检查是否为自定义组件
   */
  private isCustomComponent(
    tagName: string,
    context: TransformContext,
  ): boolean {
    return (
      context.usingComponents[tagName] !== undefined ||
      LexerUtils.isLynxComponent(tagName)
    );
  }

  /**
   * 移除指令属性
   */
  private removeDirectiveAttributes(
    attributes: Record<string, string>,
  ): Record<string, string> {
    const clean: Record<string, string> = {};
    Object.entries(attributes).forEach(([name, value]) => {
      if (
        !name.startsWith('lx:') &&
        !name.startsWith('bind') &&
        !name.startsWith('catch:')
      ) {
        clean[name] = value;
      }
    });
    return clean;
  }

  /**
   * 获取映射后的属性名
   */
  private getMappedAttributeName(
    name: string,
    mapping: ElementMapping,
  ): string {
    return (
      COMMON_ATTRIBUTE_MAPPING[name] || mapping.attributeMapping?.[name] || name
    );
  }

  /**
   * 检查是否为布尔属性
   */
  private isBooleanAttribute(name: string): boolean {
    const booleanAttrs = [
      'checked',
      'selected',
      'disabled',
      'readonly',
      'multiple',
      'hidden',
      'required',
      'autoplay',
      'controls',
      'loop',
      'muted',
    ];
    return booleanAttrs.includes(name);
  }

  /**
   * 解析内联样式
   */
  private parseInlineStyle(styleStr: string): Record<string, string> {
    const styles: Record<string, string> = {};

    styleStr.split(';').forEach(declaration => {
      const [property, value] = declaration.split(':').map(s => s.trim());
      if (property && value) {
        styles[property] = value;
      }
    });

    return styles;
  }

  /**
   * 解析表达式
   */
  private parseExpression(
    expression: string,
    context: TransformContext,
  ): JSXNode {
    const trimmed = expression.trim();

    // 简单变量
    if (/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(trimmed)) {
      return { type: 'Identifier', name: trimmed };
    }

    // 属性访问
    if (trimmed.includes('.')) {
      const parts = trimmed.split('.');
      let expr: JSXNode = { type: 'Identifier', name: parts[0] };

      for (let i = 1; i < parts.length; i++) {
        expr = {
          type: 'MemberExpression',
          object: expr,
          property: { type: 'Identifier', name: parts[i] },
        };
      }

      return expr;
    }

    // 字面量
    if (/^['"].*['"]$/.test(trimmed)) {
      return { type: 'Literal', value: trimmed.slice(1, -1) };
    }

    if (/^\d+$/.test(trimmed)) {
      return { type: 'Literal', value: parseInt(trimmed) };
    }

    if (/^\d+\.\d+$/.test(trimmed)) {
      return { type: 'Literal', value: parseFloat(trimmed) };
    }

    if (trimmed === 'true' || trimmed === 'false') {
      return { type: 'Literal', value: trimmed === 'true' };
    }

    // 默认返回标识符
    return { type: 'Identifier', name: trimmed };
  }

  /**
   * 解析模板字符串
   */
  private parseTemplateString(
    template: string,
    context: TransformContext,
  ): JSXNode {
    if (template.startsWith('{{') && template.endsWith('}}')) {
      return this.parseExpression(template.slice(2, -2), context);
    }

    // 复杂模板字符串
    const parts: JSXNode[] = [];
    const regex = /\{\{([^}]+)\}\}/g;
    let lastIndex = 0;
    let match;

    while ((match = regex.exec(template)) !== null) {
      // 添加文本部分
      if (match.index > lastIndex) {
        const text = template.slice(lastIndex, match.index);
        if (text) {
          parts.push({ type: 'Literal', value: text });
        }
      }

      // 添加表达式部分
      parts.push(this.parseExpression(match[1], context));
      lastIndex = regex.lastIndex;
    }

    // 添加最后的文本部分
    if (lastIndex < template.length) {
      const text = template.slice(lastIndex);
      if (text) {
        parts.push({ type: 'Literal', value: text });
      }
    }

    if (parts.length === 1) {
      return parts[0];
    }

    // 生成模板字符串
    return {
      type: 'TemplateLiteral',
      quasis: parts
        .filter(p => p.type === 'Literal')
        .map(p => ({
          type: 'TemplateElement',
          value: { raw: p.value, cooked: p.value },
        })),
      expressions: parts.filter(p => p.type !== 'Literal'),
    };
  }

  /**
   * 解析for表达式
   */
  private parseForExpression(forValue: string): {
    itemName: string;
    indexName: string;
    listExpression: string;
  } {
    const parts = forValue.split(' in ');
    const itemPart = parts[0].trim();
    const listExpression = parts[1].trim();

    let itemName, indexName;
    if (itemPart.includes(',')) {
      [itemName, indexName] = itemPart.split(',').map(s => s.trim());
    } else {
      itemName = itemPart;
      indexName = 'index';
    }

    return { itemName, indexName, listExpression };
  }

  /**
   * 创建属性值
   */
  private createAttributeValue(value: any): JSXAttributeValue {
    if (typeof value === 'string') {
      return { type: 'Literal', value };
    }

    if (typeof value === 'object') {
      return {
        type: 'JSXExpressionContainer',
        expression: { type: 'Literal', value },
      };
    }

    return { type: 'Literal', value };
  }

  /**
   * 驼峰化字符串
   */
  private camelCase(str: string): string {
    return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
  }

  /**
   * 帕斯卡化字符串
   */
  private pascalCase(str: string): string {
    return str.replace(/(^|-)([a-z])/g, (match, dash, letter) =>
      letter.toUpperCase(),
    );
  }

  /**
   * 生成组件ID
   */
  private generateComponentId(): string {
    return Math.random().toString(36).substr(2, 8);
  }

  /**
   * 获取配置
   */
  getConfig(): Required<TransformConfig> {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<TransformConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

/**
 * 转换错误类 - moved to types/index.ts to avoid conflicts
 */
// export class TransformError extends Error {
//   constructor(
//     message: string,
//     public type: ErrorType,
//     public position?: any,
//     public code?: string
//   ) {
//     super(message);
//     this.name = 'TransformError';
//   }
// }
