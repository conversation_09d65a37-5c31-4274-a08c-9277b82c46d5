{"compilerOptions": {"target": "ES2018", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2018", "DOM", "DOM.Iterable"], "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": ".", "rootDir": ".", "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "resolveJsonModule": true, "allowJs": false, "checkJs": false, "incremental": true, "tsBuildInfoFile": ".tsbuildinfo"}, "include": ["**/*.ts"], "exclude": ["node_modules", "**/*.js", "**/*.d.ts", "**/*.html", "tests/**/*", "examples/**/*"], "ts-node": {"esm": true}}