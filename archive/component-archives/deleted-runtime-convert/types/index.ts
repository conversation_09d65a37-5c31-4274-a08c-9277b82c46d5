/**
 * @package runtime-convert
 * @description 纯浏览器端TTML/TTSS转换引擎类型定义
 * @version 1.0.0
 * <AUTHOR> Code Development Team
 */

// ===============================
// Token 和词法分析相关类型
// ===============================

export enum TokenType {
  StartTag = 'startTag',
  EndTag = 'endTag',
  SelfClosingTag = 'selfClosing',
  AttributeName = 'attrName',
  AttributeValue = 'attrValue',
  Text = 'text',
  Mustache = 'mustache',
  Comment = 'comment',
  Whitespace = 'whitespace',
}

export interface Token {
  type: TokenType;
  value: string;
  position: Position;
  groups?: string[];
}

export interface Position {
  line: number;
  column: number;
  offset?: number;
}

// ===============================
// AST 节点类型
// ===============================

export enum ASTNodeType {
  Root = 'root',
  Element = 'element',
  Text = 'text',
  Comment = 'comment',
  Mustache = 'mustache',
  Attribute = 'attribute',
  Directive = 'directive',
  Component = 'component',
}

export interface ASTNode {
  type: ASTNodeType;
  children: ASTNode[];
  position?: Position;
  content?: string;
  expression?: string;
}

export interface ElementNode extends ASTNode {
  type: ASTNodeType.Element;
  tagName: string;
  attributes: Record<string, string>;
  selfClosing?: boolean;
  isComponent?: boolean;
}

export interface TextNode extends ASTNode {
  type: ASTNodeType.Text;
  content: string;
  children: never[];
}

export interface MustacheNode extends ASTNode {
  type: ASTNodeType.Mustache;
  expression: string;
  children: never[];
}

export interface CommentNode extends ASTNode {
  type: ASTNodeType.Comment;
  content: string;
  children: never[];
}

// ===============================
// 转换配置类型
// ===============================

export interface TransformConfig {
  // 组件配置
  componentId?: string;
  enableScope?: boolean;

  // 性能配置
  enableCache?: boolean;
  maxCacheSize?: number;

  // 功能开关
  enableOptimization?: boolean;
  enableStrictMode?: boolean;

  // 自定义组件
  usingComponents?: Record<string, string>;
}

// ===============================
// 元素映射配置
// ===============================

export interface ElementMapping {
  tag: string;
  props?: Record<string, any>;
  selfClosing?: boolean;
  attributeMapping?: Record<string, string>;
}

export interface EventMapping {
  reactEvent: string;
  stopPropagation: boolean;
}

// ===============================
// 转换结果类型
// ===============================

export interface TransformResult {
  success: boolean;
  html?: string;
  jsx?: string;
  css?: string;
  js?: string;
  error?: string;
  message?: string;
  metadata?: TransformMetadata;
}

export interface TransformMetadata {
  transformTime: number;
  componentId: string;
  elementCount: number;
  cssRuleCount: number;
  hasUserScript: boolean;
  cacheHit?: boolean;
  threadType?: string;
  totalTime?: number;
}

// ===============================
// 输入文件类型
// ===============================

export interface InputFiles {
  ttml?: string;
  ttss?: string;
  js?: string;
  json?: string;
  [key: string]: string | undefined;
}

// ===============================
// CSS处理类型
// ===============================

export interface CSSProcessingResult {
  code: string;
  scopedCode: string;
  cssInJs?: Record<string, any>;
  classes: string[];
  ruleCount: number;
}

// ===============================
// 错误类型
// ===============================

export enum ErrorType {
  SYNTAX_ERROR = 'SYNTAX_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  TRANSFORM_ERROR = 'TRANSFORM_ERROR',
  CSS_ERROR = 'CSS_ERROR',
  RUNTIME_ERROR = 'RUNTIME_ERROR',
}

export interface TransformError extends Error {
  name: string;
  type: ErrorType;
  position?: Position;
  code?: string;
  severity?: string;
  source?: string;
}

export class TransformErrorImpl extends Error implements TransformError {
  constructor(
    message: string,
    public type: ErrorType,
    public position?: Position,
    public code?: string,
    public severity?: string,
    public source?: string,
  ) {
    super(message);
    this.name = 'TransformError';
  }
}

// ===============================
// 缓存类型
// ===============================

export interface CacheEntry {
  key: string;
  value: TransformResult;
  timestamp: number;
  size: number;
}

// ===============================
// 工具类型
// ===============================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// ===============================
// Worker-related types
// ===============================

export interface FileStructure {
  [key: string]: string | FileStructure;
}

export interface ConversionResult {
  success: boolean;
  html?: string;
  css?: string;
  js?: string;
  error?: string;
  metadata?: TransformMetadata;
}

// ===============================
// 导出类型检查函数
// ===============================

export function isElementNode(node: ASTNode): node is ElementNode {
  return node.type === ASTNodeType.Element;
}

export function isTextNode(node: ASTNode): node is TextNode {
  return node.type === ASTNodeType.Text;
}

export function isMustacheNode(node: ASTNode): node is MustacheNode {
  return node.type === ASTNodeType.Mustache;
}

export function isCommentNode(node: ASTNode): node is CommentNode {
  return node.type === ASTNodeType.Comment;
}
