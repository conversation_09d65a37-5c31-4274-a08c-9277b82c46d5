/**
 * @package runtime-convert
 * @description 转换引擎常量配置
 */

import type { ElementMapping, EventMapping, TransformConfig } from '../types';

// ===============================
// 元素映射表 - 从web-speedy-plugin提取
// ===============================

export const ELEMENT_MAPPING: Record<string, ElementMapping> = {
  // 布局容器
  view: {
    tag: 'div',
    props: { className: 'lynx-view' },
  },
  'scroll-view': {
    tag: 'div',
    props: {
      className: 'lynx-scroll-view',
      style: { overflow: 'auto' },
    },
  },

  // 文本相关
  text: {
    tag: 'span',
    props: { className: 'lynx-text' },
  },
  'rich-text': {
    tag: 'div',
    props: { className: 'lynx-rich-text' },
  },

  // 媒体元素
  image: {
    tag: 'img',
    props: { className: 'lynx-image' },
    selfClosing: true,
    attributeMapping: {
      src: 'src',
      mode: 'objectFit',
      'lazy-load': 'loading',
    },
  },
  video: {
    tag: 'video',
    props: {
      className: 'lynx-video',
      controls: true,
    },
  },
  audio: {
    tag: 'audio',
    props: {
      className: 'lynx-audio',
      controls: true,
    },
  },

  // 表单元素
  input: {
    tag: 'input',
    props: { className: 'lynx-input' },
    selfClosing: true,
    attributeMapping: {
      type: 'type',
      placeholder: 'placeholder',
      value: 'value',
      maxlength: 'maxLength',
      disabled: 'disabled',
    },
  },
  textarea: {
    tag: 'textarea',
    props: { className: 'lynx-textarea' },
    attributeMapping: {
      placeholder: 'placeholder',
      maxlength: 'maxLength',
      disabled: 'disabled',
    },
  },
  button: {
    tag: 'button',
    props: { className: 'lynx-button' },
    attributeMapping: {
      disabled: 'disabled',
      'form-type': 'type',
    },
  },
  switch: {
    tag: 'input',
    props: {
      type: 'checkbox',
      className: 'lynx-switch',
    },
    selfClosing: true,
    attributeMapping: {
      checked: 'checked',
      disabled: 'disabled',
    },
  },
  slider: {
    tag: 'input',
    props: {
      type: 'range',
      className: 'lynx-slider',
    },
    selfClosing: true,
    attributeMapping: {
      min: 'min',
      max: 'max',
      step: 'step',
      value: 'value',
      disabled: 'disabled',
    },
  },
  picker: {
    tag: 'select',
    props: { className: 'lynx-picker' },
    attributeMapping: {
      disabled: 'disabled',
    },
  },
  'picker-view': {
    tag: 'div',
    props: { className: 'lynx-picker-view' },
  },
  'picker-view-column': {
    tag: 'div',
    props: { className: 'lynx-picker-view-column' },
  },

  // 导航相关
  navigator: {
    tag: 'a',
    props: { className: 'lynx-navigator' },
    attributeMapping: {
      url: 'href',
      target: 'target',
    },
  },
  'functional-page-navigator': {
    tag: 'a',
    props: { className: 'lynx-functional-navigator' },
  },

  // 媒体组件
  camera: {
    tag: 'div',
    props: { className: 'lynx-camera' },
  },
  'live-player': {
    tag: 'div',
    props: { className: 'lynx-live-player' },
  },
  'live-pusher': {
    tag: 'div',
    props: { className: 'lynx-live-pusher' },
  },

  // 地图组件
  map: {
    tag: 'div',
    props: { className: 'lynx-map' },
  },

  // 画布组件
  canvas: {
    tag: 'canvas',
    props: { className: 'lynx-canvas' },
    attributeMapping: {
      'canvas-id': 'id',
      width: 'width',
      height: 'height',
    },
  },

  // 开放能力组件
  'web-view': {
    tag: 'iframe',
    props: { className: 'lynx-web-view' },
    selfClosing: true,
    attributeMapping: {
      src: 'src',
    },
  },
  ad: {
    tag: 'div',
    props: { className: 'lynx-ad' },
  },

  // 页面属性组件
  'page-meta': {
    tag: 'div',
    props: { className: 'lynx-page-meta' },
    selfClosing: true,
  },
  'navigation-bar': {
    tag: 'div',
    props: { className: 'lynx-navigation-bar' },
  },

  // 高级组件
  swiper: {
    tag: 'div',
    props: { className: 'lynx-swiper' },
    attributeMapping: {
      'indicator-dots': 'data-indicator-dots',
      autoplay: 'data-autoplay',
      interval: 'data-interval',
      duration: 'data-duration',
      circular: 'data-circular',
      vertical: 'data-vertical',
    },
  },
  'swiper-item': {
    tag: 'div',
    props: { className: 'lynx-swiper-item' },
  },
  'movable-area': {
    tag: 'div',
    props: { className: 'lynx-movable-area' },
  },
  'movable-view': {
    tag: 'div',
    props: { className: 'lynx-movable-view' },
  },
  'cover-view': {
    tag: 'div',
    props: { className: 'lynx-cover-view' },
  },
  'cover-image': {
    tag: 'img',
    props: { className: 'lynx-cover-image' },
    selfClosing: true,
  },
  progress: {
    tag: 'progress',
    props: { className: 'lynx-progress' },
    attributeMapping: {
      percent: 'value',
      'show-info': 'data-show-info',
      'border-radius': 'data-border-radius',
      'font-size': 'data-font-size',
      'stroke-width': 'data-stroke-width',
      color: 'data-color',
      activeColor: 'data-active-color',
      backgroundColor: 'data-background-color',
      active: 'data-active',
      'active-mode': 'data-active-mode',
    },
  },
  icon: {
    tag: 'i',
    props: { className: 'lynx-icon' },
    attributeMapping: {
      type: 'data-type',
      size: 'data-size',
      color: 'data-color',
    },
  },

  // 表单组件
  form: {
    tag: 'form',
    props: { className: 'lynx-form' },
  },
  label: {
    tag: 'label',
    props: { className: 'lynx-label' },
    attributeMapping: {
      for: 'htmlFor',
    },
  },
  checkbox: {
    tag: 'input',
    props: {
      type: 'checkbox',
      className: 'lynx-checkbox',
    },
    selfClosing: true,
    attributeMapping: {
      value: 'value',
      checked: 'checked',
      disabled: 'disabled',
      color: 'data-color',
    },
  },
  'checkbox-group': {
    tag: 'div',
    props: { className: 'lynx-checkbox-group' },
  },
  radio: {
    tag: 'input',
    props: {
      type: 'radio',
      className: 'lynx-radio',
    },
    selfClosing: true,
    attributeMapping: {
      value: 'value',
      checked: 'checked',
      disabled: 'disabled',
      color: 'data-color',
    },
  },
  'radio-group': {
    tag: 'div',
    props: { className: 'lynx-radio-group' },
  },

  // 列表相关
  list: {
    tag: 'div',
    props: { className: 'lynx-list' },
  },
  'list-item': {
    tag: 'div',
    props: { className: 'lynx-list-item' },
  },
  cell: {
    tag: 'div',
    props: { className: 'lynx-cell' },
  },
  header: {
    tag: 'header',
    props: { className: 'lynx-header' },
  },
  footer: {
    tag: 'footer',
    props: { className: 'lynx-footer' },
  },
};

// ===============================
// 自闭合标签集合
// ===============================

export const SELF_CLOSING_TAGS = new Set([
  'image',
  'input',
  'switch',
  'slider',
  'progress',
  'web-view',
  'cover-image',
  'checkbox',
  'radio',
  'page-meta',
  'canvas',
]);

// ===============================
// 通用属性映射
// ===============================

export const COMMON_ATTRIBUTE_MAPPING: Record<string, string> = {
  class: 'className',
  style: 'style',
  id: 'id',
  hidden: 'hidden',
  'data-*': 'data-*', // 保持data属性不变
  'aria-*': 'aria-*', // 保持aria属性不变
};

// ===============================
// 事件映射表
// ===============================

export const EVENT_DIRECTIVE_MAPPING: Record<string, EventMapping> = {
  // 冒泡事件 (bind*)
  bindtap: { reactEvent: 'onClick', stopPropagation: false },
  bindtouchstart: { reactEvent: 'onTouchStart', stopPropagation: false },
  bindtouchmove: { reactEvent: 'onTouchMove', stopPropagation: false },
  bindtouchcancel: { reactEvent: 'onTouchCancel', stopPropagation: false },
  bindtouchend: { reactEvent: 'onTouchEnd', stopPropagation: false },
  bindlongpress: { reactEvent: 'onContextMenu', stopPropagation: false },
  bindlongtap: { reactEvent: 'onContextMenu', stopPropagation: false },
  bindtransitionend: {
    reactEvent: 'onTransitionEnd',
    stopPropagation: false,
  },
  bindanimationstart: {
    reactEvent: 'onAnimationStart',
    stopPropagation: false,
  },
  bindanimationiteration: {
    reactEvent: 'onAnimationIteration',
    stopPropagation: false,
  },
  bindanimationend: { reactEvent: 'onAnimationEnd', stopPropagation: false },
  bindtouchforcechange: {
    reactEvent: 'onTouchStart',
    stopPropagation: false,
  },

  // 表单事件
  bindinput: { reactEvent: 'onInput', stopPropagation: false },
  bindfocus: { reactEvent: 'onFocus', stopPropagation: false },
  bindblur: { reactEvent: 'onBlur', stopPropagation: false },
  bindconfirm: { reactEvent: 'onKeyDown', stopPropagation: false },
  bindkeyboardheightchange: {
    reactEvent: 'onFocus',
    stopPropagation: false,
  },
  bindchange: { reactEvent: 'onChange', stopPropagation: false },
  bindcolumnchange: { reactEvent: 'onChange', stopPropagation: false },

  // 滚动事件
  bindscroll: { reactEvent: 'onScroll', stopPropagation: false },
  bindscrolltoupper: { reactEvent: 'onScroll', stopPropagation: false },
  bindscrolltolower: { reactEvent: 'onScroll', stopPropagation: false },
  bindrefresherpulling: { reactEvent: 'onScroll', stopPropagation: false },
  bindrefresherrefresh: { reactEvent: 'onScroll', stopPropagation: false },
  bindrefresherrestore: { reactEvent: 'onScroll', stopPropagation: false },
  bindrefresherabort: { reactEvent: 'onScroll', stopPropagation: false },

  // 捕获事件 (catch:*)
  'catch:tap': { reactEvent: 'onClick', stopPropagation: true },
  'catch:touchstart': { reactEvent: 'onTouchStart', stopPropagation: true },
  'catch:touchmove': { reactEvent: 'onTouchMove', stopPropagation: true },
  'catch:touchcancel': { reactEvent: 'onTouchCancel', stopPropagation: true },
  'catch:touchend': { reactEvent: 'onTouchEnd', stopPropagation: true },
  'catch:longpress': { reactEvent: 'onContextMenu', stopPropagation: true },
  'catch:longtap': { reactEvent: 'onContextMenu', stopPropagation: true },
  'catch:transitionend': {
    reactEvent: 'onTransitionEnd',
    stopPropagation: true,
  },
  'catch:animationstart': {
    reactEvent: 'onAnimationStart',
    stopPropagation: true,
  },
  'catch:animationiteration': {
    reactEvent: 'onAnimationIteration',
    stopPropagation: true,
  },
  'catch:animationend': { reactEvent: 'onAnimationEnd', stopPropagation: true },
  'catch:touchforcechange': {
    reactEvent: 'onTouchStart',
    stopPropagation: true,
  },

  // 表单捕获事件
  'catch:input': { reactEvent: 'onInput', stopPropagation: true },
  'catch:focus': { reactEvent: 'onFocus', stopPropagation: true },
  'catch:blur': { reactEvent: 'onBlur', stopPropagation: true },
  'catch:confirm': { reactEvent: 'onKeyDown', stopPropagation: true },
  'catch:keyboardheightchange': {
    reactEvent: 'onFocus',
    stopPropagation: true,
  },
  'catch:change': { reactEvent: 'onChange', stopPropagation: true },
  'catch:columnchange': { reactEvent: 'onChange', stopPropagation: true },

  // 滚动捕获事件
  'catch:scroll': { reactEvent: 'onScroll', stopPropagation: true },
  'catch:scrolltoupper': { reactEvent: 'onScroll', stopPropagation: true },
  'catch:scrolltolower': { reactEvent: 'onScroll', stopPropagation: true },
  'catch:refresherpulling': { reactEvent: 'onScroll', stopPropagation: true },
  'catch:refresherrefresh': { reactEvent: 'onScroll', stopPropagation: true },
  'catch:refresherrestore': { reactEvent: 'onScroll', stopPropagation: true },
  'catch:refresherabort': { reactEvent: 'onScroll', stopPropagation: true },
};

// ===============================
// CSS单位转换规则 - 参考web-speedy-plugin标准
// ===============================

export const CSS_UNIT_CONVERSION = {
  // 默认设计宽度 (与web-speedy-plugin保持一致)
  DEFAULT_DESIGN_WIDTH: 750,

  // rpx转换函数 - 支持多种转换模式
  convertRpx: {
    // VW模式: 1rpx = 1/750 * 100vw (默认模式)
    toVw: (rpxValue: number, designWidth = 750): string =>
      `${((rpxValue / designWidth) * 100).toFixed(6)}vw`,

    // REM模式: 1rpx = 1/37.5 rem (基于750px设计稿)
    toRem: (rpxValue: number): string => `${(rpxValue / 37.5).toFixed(6)}rem`,

    // PX模式: 1rpx = 1px * (当前视口宽度/设计宽度)
    toPx: (rpxValue: number, designWidth = 750): string =>
      `${Math.round(rpxValue * (window.innerWidth / designWidth))}px`,

    // CALC模式: 使用CSS calc()函数动态计算
    toCalc: (rpxValue: number, designWidth = 750): string =>
      `calc(${rpxValue} * 100vw / ${designWidth})`,
  },

  // 默认转换 (使用VW模式，与web-speedy-plugin默认行为一致)
  convertRpxDefault: (rpxValue: number): string =>
    CSS_UNIT_CONVERSION.convertRpx.toVw(rpxValue),

  // 保持所有其他单位不变，支持AI接口任意单位
  preserveUnit: (value: string): string => value,
} as const;

// ===============================
// 词法分析正则表达式
// ===============================

export const LEXER_PATTERNS = {
  // 注释
  COMMENT: /<!--[\s\S]*?-->/g,

  // 插值表达式
  MUSTACHE: /\{\{[^}]*\}\}/g,

  // 自闭合标签
  SELF_CLOSING_TAG: /<([a-zA-Z][a-zA-Z0-9-]*)\s*([^>]*?)\s*\/>/g,

  // 开始标签
  START_TAG: /<([a-zA-Z][a-zA-Z0-9-]*)\s*([^>]*?)>/g,

  // 结束标签
  END_TAG: /<\/([a-zA-Z][a-zA-Z0-9-]*)>/g,

  // 属性
  ATTRIBUTE: /([a-zA-Z:@-]+)(?:\s*=\s*(?:"([^"]*)"|'([^']*)'|([^\s>]+)))?/g,

  // 文本内容
  TEXT: /[^<{]+/g,

  // 空白字符
  WHITESPACE: /\s+/g,
} as const;

// ===============================
// 默认配置
// ===============================

export const DEFAULT_CONFIG: Required<TransformConfig> = {
  componentId: '',
  enableScope: true,
  enableCache: true,
  maxCacheSize: 100,
  enableOptimization: true,
  enableStrictMode: false,
  usingComponents: {},
} as const;

// ===============================
// CSS基础样式模板
// ===============================

export const BASE_CSS_TEMPLATE = `
/* Lynx基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
  background: #f8f9fa;
}

/* Lynx组件基础样式 */
.lynx-view {
  display: flex;
  flex-direction: column;
}

.lynx-text {
  display: inline-block;
}

.lynx-image {
  max-width: 100%;
  height: auto;
  display: block;
}

.lynx-scroll-view {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.lynx-button {
  border: none;
  background: #007aff;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.lynx-button:hover {
  background: #0056b3;
}

.lynx-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.lynx-input,
.lynx-textarea {
  border: 1px solid #ddd;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.lynx-input:focus,
.lynx-textarea:focus {
  outline: none;
  border-color: #007aff;
}

.lynx-switch {
  appearance: none;
  width: 52px;
  height: 32px;
  background: #ddd;
  border-radius: 16px;
  position: relative;
  cursor: pointer;
}

.lynx-switch:checked {
  background: #007aff;
}

.lynx-switch::before {
  content: '';
  position: absolute;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: white;
  top: 2px;
  left: 2px;
  transition: transform 0.2s;
}

.lynx-switch:checked::before {
  transform: translateX(20px);
}

.lynx-swiper {
  overflow: hidden;
  position: relative;
}

.lynx-swiper-item {
  width: 100%;
  flex-shrink: 0;
}

.lynx-progress {
  width: 100%;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.lynx-list {
  list-style: none;
}

.lynx-list-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.lynx-navigator,
.lynx-link {
  color: #007aff;
  text-decoration: none;
}

.lynx-navigator:hover,
.lynx-link:hover {
  text-decoration: underline;
}
` as const;
