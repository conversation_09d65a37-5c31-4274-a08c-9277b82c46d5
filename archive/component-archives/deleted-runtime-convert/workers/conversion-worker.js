/**
 * Runtime Convert Web Worker (编译后版本)
 * 将Runtime Convert的重型计算迁移到Web Worker中执行，避免阻塞主线程
 */

// 注意：这是为了支持当前构建系统而创建的JavaScript版本
// 在production环境中，应该从TypeScript编译生成

/**
 * 简化的RuntimeConverter实现
 * 注意：这里需要根据实际的RuntimeConverter API进行调整
 */
class SimpleRuntimeConverter {
  constructor(config = {}) {
    this.config = config;
    console.log('[ConversionWorker] SimpleRuntimeConverter 初始化');
  }

  /**
   * 转换文件
   */
  async convert(files) {
    console.log('[ConversionWorker] 开始转换', files);

    // 简化的转换逻辑
    try {
      // 模拟转换处理时间
      await new Promise(resolve => setTimeout(resolve, 100));

      const ttml = files.ttml || files['src/index.ttml'] || '';

      if (!ttml.trim()) {
        throw new Error('没有找到TTML内容');
      }

      // 简化的TTML转HTML逻辑
      const html = this.convertTTMLToHTML(ttml);

      return {
        success: true,
        html,
        metadata: {
          processingTime: Date.now(),
          threadType: 'Web Worker',
          converterType: 'SimpleRuntimeConverter',
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        metadata: {
          processingTime: Date.now(),
          threadType: 'Web Worker',
          converterType: 'SimpleRuntimeConverter',
        },
      };
    }
  }

  /**
   * 简化的TTML到HTML转换
   */
  convertTTMLToHTML(ttml) {
    // 基础的标签映射
    const html = ttml
      .replace(/<view([^>]*)>/g, '<div$1>')
      .replace(/<\/view>/g, '</div>')
      .replace(/<text([^>]*)>/g, '<span$1>')
      .replace(/<\/text>/g, '</span>')
      .replace(/<image([^>]*)>/g, '<img$1>')
      .replace(/<button([^>]*)>/g, '<button$1>')
      .replace(/<input([^>]*)>/g, '<input$1>')
      .replace(/<scroll-view([^>]*)>/g, '<div$1 style="overflow:auto;">')
      .replace(/<\/scroll-view>/g, '</div>');

    // 包装为完整HTML文档
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LYNX Preview</title>
  <style>
    body {
      margin: 0;
      padding: 16px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      line-height: 1.6;
    }
    .container {
      max-width: 100%;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <div class="container">
    ${html}
  </div>
</body>
</html>`;
  }

  dispose() {
    console.log('[ConversionWorker] SimpleRuntimeConverter 销毁');
  }
}

/**
 * Worker状态管理
 */
class WorkerState {
  constructor() {
    this.converter = null;
    this.isInitialized = false;
    this.taskCount = 0;
  }

  /**
   * 初始化转换器
   */
  initializeConverter(config = {}) {
    if (!this.converter) {
      this.converter = new SimpleRuntimeConverter(config);
      this.isInitialized = true;
      console.log('[ConversionWorker] Runtime Convert已初始化');
    }
  }

  /**
   * 执行转换任务
   */
  async executeConversion(files, config = {}) {
    if (!this.isInitialized) {
      this.initializeConverter(config);
    }

    if (!this.converter) {
      throw new Error('Runtime Convert初始化失败');
    }

    this.taskCount++;
    const taskId = this.taskCount;

    console.log(`[ConversionWorker] 开始执行转换任务 #${taskId}`);
    const startTime = performance.now();

    try {
      const result = await this.converter.convert(files);
      const processingTime = performance.now() - startTime;

      console.log(
        `[ConversionWorker] 转换任务 #${taskId} 完成，耗时: ${processingTime.toFixed(2)}ms`,
      );

      return {
        ...result,
        metadata: {
          ...result.metadata,
          processingTime,
          threadType: 'Web Worker',
          taskId: taskId.toString(),
        },
      };
    } catch (error) {
      const processingTime = performance.now() - startTime;
      console.error(
        `[ConversionWorker] 转换任务 #${taskId} 失败，耗时: ${processingTime.toFixed(2)}ms`,
        error,
      );
      throw error;
    }
  }

  /**
   * 销毁转换器
   */
  dispose() {
    if (this.converter) {
      try {
        this.converter.dispose();
      } catch (error) {
        console.warn('[ConversionWorker] 转换器销毁时出现警告:', error);
      }
      this.converter = null;
      this.isInitialized = false;
      console.log('[ConversionWorker] Runtime Convert已销毁');
    }
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      taskCount: this.taskCount,
      hasConverter: !!this.converter,
    };
  }
}

// 创建Worker状态实例
const workerState = new WorkerState();

/**
 * 处理主线程消息
 */
self.onmessage = async function (event) {
  const { id, type, payload } = event.data;
  const startTime = performance.now();

  try {
    let result;
    const success = true;

    switch (type) {
      case 'convert':
        if (!payload || !payload.files) {
          throw new Error('缺少必需的files参数');
        }

        console.log(`[ConversionWorker] 收到转换请求 ${id}`);
        result = await workerState.executeConversion(
          payload.files,
          payload.config,
        );
        break;

      case 'dispose':
        console.log(`[ConversionWorker] 收到销毁请求 ${id}`);
        workerState.dispose();
        result = { disposed: true };
        break;

      case 'ping':
        console.log(`[ConversionWorker] 收到ping请求 ${id}`);
        result = {
          pong: true,
          status: workerState.getStatus(),
          timestamp: Date.now(),
        };
        break;

      default:
        throw new Error(`未知的消息类型: ${type}`);
    }

    const processingTime = performance.now() - startTime;

    // 发送成功响应
    const response = {
      id,
      success,
      result,
      metadata: {
        processingTime,
        threadType: 'Web Worker',
        workerInfo: 'ConversionWorker v1.0.0',
      },
    };

    self.postMessage(response);
  } catch (error) {
    const processingTime = performance.now() - startTime;
    console.error(`[ConversionWorker] 处理消息失败 ${id}:`, error);

    // 发送错误响应
    const errorResponse = {
      id,
      success: false,
      error: error.message || String(error),
      metadata: {
        processingTime,
        threadType: 'Web Worker',
        workerInfo: 'ConversionWorker v1.0.0 (Error)',
      },
    };

    self.postMessage(errorResponse);
  }
};

/**
 * 处理Worker错误
 */
self.onerror = function (error) {
  console.error('[ConversionWorker] Worker全局错误:', error);
};

/**
 * 处理未捕获的Promise rejection
 */
self.onunhandledrejection = function (event) {
  console.error('[ConversionWorker] 未处理的Promise rejection:', event.reason);
  event.preventDefault();
};

// Worker初始化完成
console.log('[ConversionWorker] Web Worker已启动，等待任务...');
