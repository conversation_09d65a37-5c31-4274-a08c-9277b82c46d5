/**
 * Runtime Convert Web Worker
 * 将Runtime Convert的重型计算迁移到Web Worker中执行，避免阻塞主线程
 */

import { RuntimeConverter } from '../core';
import type {
  TransformConfig,
  FileStructure,
  ConversionResult,
} from '../types';

// Worker上下文类型声明
declare const self: DedicatedWorkerGlobalScope;

/**
 * Worker消息类型定义
 */
interface WorkerMessage {
  id: string;
  type: 'convert' | 'dispose' | 'ping';
  payload?: {
    files?: FileStructure;
    config?: TransformConfig;
    options?: any;
  };
}

interface WorkerResponse {
  id: string;
  success: boolean;
  result?: ConversionResult;
  error?: string;
  metadata?: {
    processingTime: number;
    threadType: string;
    workerInfo: string;
  };
}

/**
 * Worker状态管理
 */
class WorkerState {
  private converter: RuntimeConverter | null = null;
  private isInitialized = false;
  private taskCount = 0;

  /**
   * 初始化转换器
   */
  initializeConverter(config?: TransformConfig): void {
    if (!this.converter) {
      this.converter = new RuntimeConverter(config);
      this.isInitialized = true;
      console.log('[ConversionWorker] Runtime Convert已初始化');
    }
  }

  /**
   * 执行转换任务
   */
  async executeConversion(
    files: FileStructure,
    config?: TransformConfig,
  ): Promise<ConversionResult> {
    if (!this.isInitialized) {
      this.initializeConverter(config);
    }

    if (!this.converter) {
      throw new Error('Runtime Convert初始化失败');
    }

    this.taskCount++;
    const taskId = this.taskCount;

    console.log(`[ConversionWorker] 开始执行转换任务 #${taskId}`);
    const startTime = performance.now();

    try {
      const result = await this.converter.convert(files);
      const processingTime = performance.now() - startTime;

      console.log(
        `[ConversionWorker] 转换任务 #${taskId} 完成，耗时: ${processingTime.toFixed(2)}ms`,
      );

      return {
        ...result,
        metadata: {
          ...result.metadata,
          processingTime,
          threadType: 'Web Worker',
          taskId: taskId.toString(),
        },
      };
    } catch (error) {
      const processingTime = performance.now() - startTime;
      console.error(
        `[ConversionWorker] 转换任务 #${taskId} 失败，耗时: ${processingTime.toFixed(2)}ms`,
        error,
      );
      throw error;
    }
  }

  /**
   * 销毁转换器
   */
  dispose(): void {
    if (this.converter) {
      try {
        this.converter.dispose?.();
      } catch (error) {
        console.warn('[ConversionWorker] 转换器销毁时出现警告:', error);
      }
      this.converter = null;
      this.isInitialized = false;
      console.log('[ConversionWorker] Runtime Convert已销毁');
    }
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      taskCount: this.taskCount,
      hasConverter: !!this.converter,
    };
  }
}

// 创建Worker状态实例
const workerState = new WorkerState();

/**
 * 处理主线程消息
 */
self.onmessage = async (event: MessageEvent<WorkerMessage>) => {
  const { id, type, payload } = event.data;
  const startTime = performance.now();

  try {
    let result: any;
    const success = true;

    switch (type) {
      case 'convert':
        if (!payload?.files) {
          throw new Error('缺少必需的files参数');
        }

        console.log(`[ConversionWorker] 收到转换请求 ${id}`);
        result = await workerState.executeConversion(
          payload.files,
          payload.config,
        );
        break;

      case 'dispose':
        console.log(`[ConversionWorker] 收到销毁请求 ${id}`);
        workerState.dispose();
        result = { disposed: true };
        break;

      case 'ping':
        console.log(`[ConversionWorker] 收到ping请求 ${id}`);
        result = {
          pong: true,
          status: workerState.getStatus(),
          timestamp: Date.now(),
        };
        break;

      default:
        throw new Error(`未知的消息类型: ${type}`);
    }

    const processingTime = performance.now() - startTime;

    // 发送成功响应
    const response: WorkerResponse = {
      id,
      success,
      result,
      metadata: {
        processingTime,
        threadType: 'Web Worker',
        workerInfo: 'ConversionWorker v1.0.0',
      },
    };

    self.postMessage(response);
  } catch (error) {
    const processingTime = performance.now() - startTime;
    console.error(`[ConversionWorker] 处理消息失败 ${id}:`, error);

    // 发送错误响应
    const errorResponse: WorkerResponse = {
      id,
      success: false,
      error: error instanceof Error ? error.message : String(error),
      metadata: {
        processingTime,
        threadType: 'Web Worker',
        workerInfo: 'ConversionWorker v1.0.0 (Error)',
      },
    };

    self.postMessage(errorResponse);
  }
};

/**
 * 处理Worker错误
 */
self.onerror = error => {
  console.error('[ConversionWorker] Worker全局错误:', error);
};

/**
 * 处理未捕获的Promise rejection
 */
self.onunhandledrejection = event => {
  console.error('[ConversionWorker] 未处理的Promise rejection:', event.reason);
  event.preventDefault();
};

// Worker初始化完成
console.log('[ConversionWorker] Web Worker已启动，等待任务...');
