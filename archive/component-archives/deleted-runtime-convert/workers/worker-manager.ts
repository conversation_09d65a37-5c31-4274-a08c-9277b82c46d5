/**
 * Worker管理器
 * 负责管理Web Worker的生命周期、任务调度和通信
 */

import type {
  TransformConfig,
  FileStructure,
  ConversionResult,
} from '../types';

interface WorkerTask {
  id: string;
  resolve: (result: ConversionResult) => void;
  reject: (error: Error) => void;
  timeout?: NodeJS.Timeout;
  startTime: number;
}

interface WorkerManagerConfig {
  /** Worker文件路径 */
  workerPath?: string;
  /** 任务超时时间(毫秒) */
  taskTimeout?: number;
  /** 最大重试次数 */
  maxRetries?: number;
  /** 是否启用Worker模式 */
  enableWorker?: boolean;
  /** Worker初始化超时时间(毫秒) */
  initTimeout?: number;
}

/**
 * Worker管理器类
 */
export class WorkerManager {
  private worker: Worker | null = null;
  private isInitialized = false;
  private isInitializing = false;
  private pendingTasks = new Map<string, WorkerTask>();
  private taskCounter = 0;
  private config: Required<WorkerManagerConfig>;

  constructor(config: WorkerManagerConfig = {}) {
    this.config = {
      workerPath:
        './src/routes/batch_processor/runtime_convert/workers/conversion-worker.js',
      taskTimeout: 30000, // 30秒
      maxRetries: 2,
      enableWorker: true,
      initTimeout: 10000, // 10秒
      ...config,
    };
  }

  /**
   * 初始化Worker
   */
  async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    if (this.isInitializing) {
      // 等待当前初始化完成
      return new Promise(resolve => {
        const checkInit = () => {
          if (this.isInitialized) {
            resolve(true);
          } else if (!this.isInitializing) {
            resolve(false);
          } else {
            setTimeout(checkInit, 100);
          }
        };
        checkInit();
      });
    }

    if (!this.config.enableWorker) {
      console.log('[WorkerManager] Worker模式已禁用');
      return false;
    }

    this.isInitializing = true;

    try {
      console.log('[WorkerManager] 正在初始化Web Worker...');

      // 创建Worker
      this.worker = new Worker(this.config.workerPath, {
        type: 'module',
        name: 'RuntimeConvertWorker',
      });

      // 设置Worker事件监听器
      this.setupWorkerListeners();

      // 发送ping测试Worker是否正常工作
      const pingResult = await this.pingWorker();

      if (pingResult) {
        this.isInitialized = true;
        console.log('[WorkerManager] Web Worker初始化成功');
        return true;
      } else {
        throw new Error('Worker ping测试失败');
      }
    } catch (error) {
      console.error('[WorkerManager] Web Worker初始化失败:', error);
      this.cleanup();
      return false;
    } finally {
      this.isInitializing = false;
    }
  }

  /**
   * 设置Worker事件监听器
   */
  private setupWorkerListeners(): void {
    if (!this.worker) {
      return;
    }

    this.worker.onmessage = event => {
      const { id, success, result, error, metadata } = event.data;

      const task = this.pendingTasks.get(id);
      if (!task) {
        console.warn(`[WorkerManager] 收到未知任务的响应: ${id}`);
        return;
      }

      // 清理任务
      this.pendingTasks.delete(id);
      if (task.timeout) {
        clearTimeout(task.timeout);
      }

      const processingTime = Date.now() - task.startTime;
      console.log(
        `[WorkerManager] 任务 ${id} 完成，总耗时: ${processingTime}ms`,
      );

      if (success) {
        task.resolve(result);
      } else {
        task.reject(new Error(error || '未知Worker错误'));
      }
    };

    this.worker.onerror = error => {
      console.error('[WorkerManager] Worker错误:', error);
      this.handleWorkerError(error);
    };

    this.worker.onmessageerror = error => {
      console.error('[WorkerManager] Worker消息错误:', error);
      this.handleWorkerError(error);
    };
  }

  /**
   * 处理Worker错误
   */
  private handleWorkerError(error: any): void {
    // 拒绝所有挂起的任务
    for (const [id, task] of this.pendingTasks) {
      task.reject(new Error(`Worker错误: ${error.message || error}`));
      if (task.timeout) {
        clearTimeout(task.timeout);
      }
    }
    this.pendingTasks.clear();

    // 重置状态
    this.isInitialized = false;
    this.cleanup();
  }

  /**
   * Ping Worker测试
   */
  private async pingWorker(): Promise<boolean> {
    if (!this.worker) {
      return false;
    }

    return new Promise(resolve => {
      const taskId = this.generateTaskId();
      const timeout = setTimeout(() => {
        resolve(false);
      }, this.config.initTimeout);

      const handlePong = (event: MessageEvent) => {
        const { id, success, result } = event.data;
        if (id === taskId) {
          clearTimeout(timeout);
          this.worker!.removeEventListener('message', handlePong);
          resolve(success && result?.pong === true);
        }
      };

      this.worker.addEventListener('message', handlePong);
      this.worker.postMessage({
        id: taskId,
        type: 'ping',
      });
    });
  }

  /**
   * 执行转换任务
   */
  async convert(
    files: FileStructure,
    config?: TransformConfig,
  ): Promise<ConversionResult> {
    // 确保Worker已初始化
    const isWorkerReady = await this.initialize();

    if (!isWorkerReady) {
      throw new Error('Worker初始化失败，无法执行转换任务');
    }

    if (!this.worker) {
      throw new Error('Worker未初始化');
    }

    const taskId = this.generateTaskId();
    console.log(`[WorkerManager] 开始执行转换任务: ${taskId}`);

    return new Promise<ConversionResult>((resolve, reject) => {
      const startTime = Date.now();

      // 设置超时
      const timeout = setTimeout(() => {
        this.pendingTasks.delete(taskId);
        reject(new Error(`转换任务超时: ${this.config.taskTimeout}ms`));
      }, this.config.taskTimeout);

      // 保存任务信息
      const task: WorkerTask = {
        id: taskId,
        resolve,
        reject,
        timeout,
        startTime,
      };
      this.pendingTasks.set(taskId, task);

      // 发送转换任务到Worker
      this.worker!.postMessage({
        id: taskId,
        type: 'convert',
        payload: {
          files,
          config,
        },
      });
    });
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    this.taskCounter++;
    return `task_${Date.now()}_${this.taskCounter}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isInitializing: this.isInitializing,
      pendingTaskCount: this.pendingTasks.size,
      hasWorker: !!this.worker,
      config: this.config,
    };
  }

  /**
   * 销毁Worker
   */
  async dispose(): Promise<void> {
    console.log('[WorkerManager] 正在销毁Worker...');

    // 取消所有挂起的任务
    for (const [id, task] of this.pendingTasks) {
      task.reject(new Error('Worker正在销毁'));
      if (task.timeout) {
        clearTimeout(task.timeout);
      }
    }
    this.pendingTasks.clear();

    // 发送销毁消息给Worker
    if (this.worker && this.isInitialized) {
      try {
        const taskId = this.generateTaskId();
        this.worker.postMessage({
          id: taskId,
          type: 'dispose',
        });

        // 等待一小段时间让Worker清理
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.warn('[WorkerManager] 发送销毁消息失败:', error);
      }
    }

    this.cleanup();
    console.log('[WorkerManager] Worker已销毁');
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
    this.isInitialized = false;
    this.isInitializing = false;
  }
}

/**
 * 全局Worker管理器实例
 */
let globalWorkerManager: WorkerManager | null = null;

/**
 * 获取全局Worker管理器实例
 */
export function getWorkerManager(config?: WorkerManagerConfig): WorkerManager {
  if (!globalWorkerManager) {
    globalWorkerManager = new WorkerManager(config);
  }
  return globalWorkerManager;
}

/**
 * 销毁全局Worker管理器
 */
export async function disposeGlobalWorkerManager(): Promise<void> {
  if (globalWorkerManager) {
    await globalWorkerManager.dispose();
    globalWorkerManager = null;
  }
}
