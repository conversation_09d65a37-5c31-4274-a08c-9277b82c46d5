const path = require('path');

module.exports = {
  root: true,
  env: {
    browser: true,
    es2020: true,
    node: true,
  },
  extends: ['eslint:recommended'],
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
  },
  plugins: ['prettier'],
  settings: {
    'import/resolver': {
      node: {
        paths: ['.eslint-rules'],
      },
    },
  },
  rules: {
    // Disable problematic no-autofix rules
    'no-autofix/@typescript-eslint/no-unnecessary-condition': 'off',
    'no-autofix/@typescript-eslint/no-unnecessary-boolean-literal-compare':
      'off',
    'no-autofix/react/jsx-no-leaked-render': 'off',

    // Prettier integration
    'prettier/prettier': 'error',

    // Temporarily disable common issues to reduce noise
    '@typescript-eslint/no-unused-vars': 'warn',
    '@typescript-eslint/no-explicit-any': 'warn',
    'no-unused-vars': 'warn',
    'no-useless-escape': 'warn',
    'no-constant-condition': 'warn',
    'no-fallthrough': 'warn',
    'no-case-declarations': 'warn',
    'no-prototype-builtins': 'warn',
    'no-control-regex': 'warn',
    'prefer-const': 'warn',
    '@typescript-eslint/no-var-requires': 'warn',
    '@typescript-eslint/ban-ts-comment': 'warn',
    '@typescript-eslint/no-this-alias': 'warn',

    // Additional rules from consolidated configs
    '@typescript-eslint/no-empty-interface': 'off',
    'require-await': 'off',
    'max-lines-per-function': 'off',
    
    // Custom rules to prevent markdown/emoji syntax errors
    'no-irregular-whitespace': 'error',
    'no-unexpected-multiline': 'error',
  },
  overrides: [
    {
      files: ['**/*.{ts,tsx}'],
      parser: '@typescript-eslint/parser',
      parserOptions: {
        ecmaVersion: 2020,
        sourceType: 'module',
        project: './tsconfig.json',
        ecmaFeatures: {
          jsx: true,
        },
      },
      extends: ['eslint:recommended', 'plugin:@typescript-eslint/recommended'],
      plugins: ['@typescript-eslint'],
    },
    {
      files: ['**/*.{jsx,tsx}'],
      extends: ['plugin:react/recommended', 'plugin:react-hooks/recommended'],
      plugins: ['react', 'react-hooks'],
      settings: {
        react: {
          version: 'detect',
        },
      },
    },
  ],
};
