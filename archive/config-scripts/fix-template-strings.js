const fs = require('fs');
const path = require('path');

// 需要修复的文件路径
const filePaths = [
  'src/routes/batch_processor/services/PromptUploader.ts'
];

// 修复模板字符串的函数
function fixTemplateStrings(content) {
  // 修复 \`...\` 为 `...`
  content = content.replace(/\\`/g, '`');
  
  // 修复 \$\{...\} 为 ${...}
  content = content.replace(/\\\$\{/g, '${');
  
  // 修复 \\\`\\\`\\\` 为 ```
  content = content.replace(/\\\\\`\\\\\`\\\\\`/g, '```');
  
  return content;
}

// 处理每个文件
filePaths.forEach(filePath => {
  const fullPath = path.join(__dirname, '..', filePath);
  
  if (fs.existsSync(fullPath)) {
    console.log(`修复文件: ${filePath}`);
    
    const content = fs.readFileSync(fullPath, 'utf-8');
    const fixedContent = fixTemplateStrings(content);
    
    fs.writeFileSync(fullPath, fixedContent, 'utf-8');
    console.log(`✅ 修复完成: ${filePath}`);
  } else {
    console.log(`⚠️  文件不存在: ${filePath}`);
  }
});

console.log('所有模板字符串修复完成！');