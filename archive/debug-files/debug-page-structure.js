// 批量处理器页面HTML结构检查脚本
// 在浏览器控制台中运行此脚本

console.log('🔍 开始检查批量处理器页面HTML结构...');

// 检查页面基本信息
console.log('📱 页面基本信息:', {
    url: window.location.href,
    title: document.title,
    userAgent: navigator.userAgent,
    viewport: `${window.innerWidth}x${window.innerHeight}`
});

// 查找所有图标元素
const icons = document.querySelectorAll('.icon, svg[class*="icon"]');
console.log(`📐 找到 ${icons.length} 个图标元素`);

// 检查每个图标的详细信息
icons.forEach((icon, index) => {
    const computedStyle = getComputedStyle(icon);
    const rect = icon.getBoundingClientRect();
    
    console.log(`🎯 图标 ${index + 1}:`, {
        element: icon,
        tagName: icon.tagName,
        className: icon.className,
        computedWidth: computedStyle.width,
        computedHeight: computedStyle.height,
        actualWidth: rect.width,
        actualHeight: rect.height,
        display: computedStyle.display,
        position: computedStyle.position,
        transform: computedStyle.transform,
        inlineStyle: icon.style.cssText,
        parent: icon.parentElement?.tagName,
        parentClass: icon.parentElement?.className
    });
});

// 检查CSS变量
console.log('🎨 检查CSS变量定义:');
const root = document.documentElement;
const computedStyle = getComputedStyle(root);

const iconSizes = ['xs', 'sm', 'md', 'lg', 'xl'];
iconSizes.forEach(size => {
    const varName = `--icon-size-${size}`;
    const value = computedStyle.getPropertyValue(varName);
    console.log(`${varName}: ${value || '❌ 未定义'}`);
});

// 检查右栏区域
const rightPanel = document.querySelector('.results-panel, .right-panel, [class*="right"]');
if (rightPanel) {
    console.log('🔍 右栏区域信息:', {
        element: rightPanel,
        className: rightPanel.className,
        width: rightPanel.offsetWidth,
        height: rightPanel.offsetHeight,
        children: rightPanel.children.length
    });
    
    // 检查右栏中的图标
    const rightIcons = rightPanel.querySelectorAll('.icon, svg[class*="icon"]');
    console.log(`📍 右栏中的图标数量: ${rightIcons.length}`);
    
    rightIcons.forEach((icon, index) => {
        const computedStyle = getComputedStyle(icon);
        const rect = icon.getBoundingClientRect();
        
        console.log(`🎯 右栏图标 ${index + 1}:`, {
            element: icon,
            className: icon.className,
            computedWidth: computedStyle.width,
            computedHeight: computedStyle.height,
            actualWidth: rect.width,
            actualHeight: rect.height,
            inlineStyle: icon.style.cssText,
            parentClass: icon.parentElement?.className
        });
    });
} else {
    console.log('❌ 未找到右栏区域元素');
}

// 检查按钮中的图标
const buttons = document.querySelectorAll('button');
console.log(`🔘 找到 ${buttons.length} 个按钮`);

buttons.forEach((button, index) => {
    const buttonIcons = button.querySelectorAll('.icon, svg[class*="icon"]');
    if (buttonIcons.length > 0) {
        console.log(`🔘 按钮 ${index + 1} 中的图标:`, {
            button: button,
            buttonClass: button.className,
            buttonText: button.textContent?.trim(),
            iconCount: buttonIcons.length
        });
        
        buttonIcons.forEach((icon, iconIndex) => {
            const computedStyle = getComputedStyle(icon);
            const rect = icon.getBoundingClientRect();
            
            console.log(`  📍 图标 ${iconIndex + 1}:`, {
                className: icon.className,
                computedWidth: computedStyle.width,
                computedHeight: computedStyle.height,
                actualWidth: rect.width,
                actualHeight: rect.height,
                inlineStyle: icon.style.cssText
            });
        });
    }
});

// 检查所有应用的样式表
console.log('📄 检查样式表:');
const stylesheets = document.styleSheets;
let iconRules = [];

for (let i = 0; i < stylesheets.length; i++) {
    try {
        const rules = stylesheets[i].cssRules || stylesheets[i].rules;
        for (let j = 0; j < rules.length; j++) {
            const rule = rules[j];
            if (rule.selectorText && rule.selectorText.includes('icon')) {
                iconRules.push({
                    selector: rule.selectorText,
                    styles: rule.style.cssText,
                    stylesheet: stylesheets[i].href || 'inline'
                });
            }
        }
    } catch (e) {
        console.log(`无法读取样式表 ${i}: ${e.message}`);
    }
}

console.log('🎨 图标相关CSS规则:', iconRules);

// 检查是否有内联样式
const elementsWithInlineStyles = document.querySelectorAll('[style]');
console.log(`🎭 有内联样式的元素数量: ${elementsWithInlineStyles.length}`);

elementsWithInlineStyles.forEach((element, index) => {
    if (element.style.cssText.includes('width') || element.style.cssText.includes('height')) {
        console.log(`📐 元素 ${index + 1} 的内联样式:`, {
            element: element,
            tagName: element.tagName,
            className: element.className,
            inlineStyle: element.style.cssText
        });
    }
});

console.log('✅ 页面结构检查完成');