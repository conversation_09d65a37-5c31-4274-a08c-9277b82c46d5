<!DOCTYPE html>
<html>
<head>
    <title>调试注入器</title>
</head>
<body>
    <h1>Lynx Preview 调试注入器</h1>
    <button onclick="injectDebugCode()">注入调试代码</button>
    <button onclick="checkReactApp()">检查React应用</button>
    <button onclick="forceNavigation()">强制导航</button>
    <div id="results"></div>

    <script>
        function log(message) {
            const div = document.createElement('div');
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            document.getElementById('results').appendChild(div);
            console.log(message);
        }

        function injectDebugCode() {
            // 打开lynx_preview页面并注入调试代码
            const popup = window.open('http://localhost:8082/lynx_preview', 'debug');
            
            setTimeout(() => {
                try {
                    // 注入调试脚本
                    const script = popup.document.createElement('script');
                    script.textContent = `
                        console.log('🔍 [INJECT] 调试脚本已注入');
                        console.log('🔍 [INJECT] 当前URL:', window.location.href);
                        console.log('🔍 [INJECT] document.readyState:', document.readyState);
                        console.log('🔍 [INJECT] React root element:', document.getElementById('root'));
                        console.log('🔍 [INJECT] Root children count:', document.getElementById('root')?.children.length);
                        
                        // 检查React
                        if (window.React) {
                            console.log('✅ [INJECT] React is available');
                        } else {
                            console.log('❌ [INJECT] React is NOT available');
                        }
                        
                        // 检查路由
                        if (window.history) {
                            console.log('🔍 [INJECT] Current pathname:', window.location.pathname);
                        }
                        
                        // 监听DOM变化
                        const observer = new MutationObserver(function(mutations) {
                            mutations.forEach(function(mutation) {
                                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                                    console.log('📝 [INJECT] DOM changed, added nodes:', mutation.addedNodes.length);
                                }
                            });
                        });
                        
                        observer.observe(document.getElementById('root'), {
                            childList: true,
                            subtree: true
                        });
                        
                        // 延迟检查
                        setTimeout(() => {
                            const root = document.getElementById('root');
                            console.log('⏰ [INJECT] 5秒后检查 - Root innerHTML length:', root?.innerHTML.length);
                            console.log('⏰ [INJECT] 5秒后检查 - Body classes:', document.body.className);
                        }, 5000);
                    `;
                    popup.document.head.appendChild(script);
                    log('✅ 调试代码已注入到popup窗口');
                } catch (error) {
                    log('❌ 注入失败: ' + error.message);
                }
            }, 2000);
        }

        function checkReactApp() {
            const popup = window.open('http://localhost:8082/lynx_preview', 'check');
            setTimeout(() => {
                try {
                    const root = popup.document.getElementById('root');
                    log(`Root element exists: ${!!root}`);
                    log(`Root children: ${root?.children.length || 0}`);
                    log(`Root innerHTML length: ${root?.innerHTML.length || 0}`);
                    log(`Document title: ${popup.document.title}`);
                    log(`URL: ${popup.location.href}`);
                    
                    // 检查加载的脚本
                    const scripts = Array.from(popup.document.querySelectorAll('script[src]'));
                    log(`Loaded scripts: ${scripts.length}`);
                    scripts.forEach(script => {
                        if (script.src.includes('lynx')) {
                            log(`Lynx script: ${script.src}`);
                        }
                    });
                } catch (error) {
                    log('❌ 检查失败: ' + error.message);
                }
            }, 3000);
        }

        function forceNavigation() {
            // 尝试通过History API强制导航
            const popup = window.open('http://localhost:8082/', 'force');
            setTimeout(() => {
                try {
                    popup.history.pushState({}, '', '/lynx_preview');
                    popup.location.reload();
                    log('✅ 强制导航到lynx_preview');
                } catch (error) {
                    log('❌ 强制导航失败: ' + error.message);
                }
            }, 1000);
        }
    </script>
</body>
</html>