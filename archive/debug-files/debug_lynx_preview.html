<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试 Lynx Preview 页面加载</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .debug-info {
            background: #f0f8f0;
            border: 1px solid #d0f0d0;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #fff0f0;
            border: 1px solid #f0d0d0;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <h1>🔍 Lynx Preview 页面调试工具</h1>
    
    <div class="debug-info">
        <h3>🎯 调试目标</h3>
        <p>URL: <strong>http://localhost:8082/lynx_preview</strong></p>
        <p>目标: 验证页面是否正常加载并显示内容</p>
    </div>

    <div class="debug-info">
        <h3>🧪 测试方法</h3>
        <button class="button" onclick="testDirectAccess()">1. 直接访问测试</button>
        <button class="button" onclick="testInIframe()">2. iframe 嵌入测试</button>
        <button class="button" onclick="testFetch()">3. Fetch API 测试</button>
        <button class="button" onclick="clearResults()">清空结果</button>
    </div>

    <div id="results"></div>

    <div class="iframe-container" id="iframeContainer" style="display: none;">
        <iframe id="testIframe" src=""></iframe>
    </div>

    <script>
        function log(message, isError = false) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `debug-info ${isError ? 'error' : ''}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            results.appendChild(div);
            console.log(message);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('iframeContainer').style.display = 'none';
        }

        function testDirectAccess() {
            log('🚀 启动直接访问测试...');
            try {
                window.open('http://localhost:8082/lynx_preview', '_blank');
                log('✅ 新窗口已打开，请检查是否显示内容');
                setTimeout(() => {
                    log('⏰ 请手动检查新窗口中的页面内容和控制台日志');
                }, 1000);
            } catch (error) {
                log(`❌ 直接访问失败: ${error.message}`, true);
            }
        }

        function testInIframe() {
            log('🖼️ 启动 iframe 嵌入测试...');
            try {
                const container = document.getElementById('iframeContainer');
                const iframe = document.getElementById('testIframe');
                
                iframe.onload = function() {
                    log('✅ iframe 加载完成');
                    try {
                        // 由于跨域限制，无法直接访问iframe内容
                        log('📝 iframe 已显示，请检查下方预览区域');
                    } catch (error) {
                        log(`⚠️ 无法检查 iframe 内容（跨域限制）: ${error.message}`);
                    }
                };

                iframe.onerror = function() {
                    log('❌ iframe 加载失败', true);
                };

                iframe.src = 'http://localhost:8082/lynx_preview';
                container.style.display = 'block';
                
                log('⏳ 正在加载 iframe...');
            } catch (error) {
                log(`❌ iframe 测试失败: ${error.message}`, true);
            }
        }

        async function testFetch() {
            log('🌐 启动 Fetch API 测试...');
            try {
                const response = await fetch('http://localhost:8082/lynx_preview');
                
                log(`📊 响应状态: ${response.status} ${response.statusText}`);
                log(`📋 响应头信息:`);
                
                for (const [key, value] of response.headers.entries()) {
                    log(`&nbsp;&nbsp;• ${key}: ${value}`);
                }

                if (response.ok) {
                    const html = await response.text();
                    const hasReactRoot = html.includes('id="root"');
                    const hasLynxAssets = html.includes('lynx_preview/page');
                    
                    log(`✅ HTML 长度: ${html.length} 字符`);
                    log(`🎯 包含 React 根节点: ${hasReactRoot ? '是' : '否'}`);
                    log(`📦 包含 Lynx 资源: ${hasLynxAssets ? '是' : '否'}`);
                    
                    if (hasReactRoot && hasLynxAssets) {
                        log('🎉 页面结构正常！可能是客户端渲染问题');
                    } else {
                        log('⚠️ 页面结构异常，需要进一步检查', true);
                    }
                } else {
                    log(`❌ HTTP 错误: ${response.status}`, true);
                }
            } catch (error) {
                log(`❌ Fetch 测试失败: ${error.message}`, true);
            }
        }

        // 页面加载完成后自动执行基础检查
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 调试工具已启动');
            log('💡 建议按顺序执行测试：Fetch API → iframe 嵌入 → 直接访问');
        });
    </script>
</body>
</html>