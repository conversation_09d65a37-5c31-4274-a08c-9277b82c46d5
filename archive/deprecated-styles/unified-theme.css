/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎨 UNIFIED THEME SYSTEM - 商务专业蓝灰主题
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 🌟 现代化批量处理器设计系统
 * ✨ 深浅蓝色渐变主调+层次灰色系统+精致阴影高光 * 🎯 响应式三栏布局+微动效交互+模块差异化对比 * 💫 商务专业权威+柔和舒适体验+无障碍友好
 *
 * @version 2.0
 * <AUTHOR> Agent
 * @updated 2025-06-17
 */

:root {
  /* ═══════════════════════════════════════════════════════════════════════════
   * 🌈 COLOR PALETTE - 色彩调色板
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 💼 主色调系统 - Professional Blue Gradient Palette */    /* 💼 极浅天蓝色 - 模块背景 */    /* 💼 浅天蓝色 - 卡片悬停背景 */    /* 💼 中浅天蓝色 - 边框色 */    /* 💼 中天蓝色 - 辅助按钮 */    /* 💼 深天蓝色 - 主要按钮起始 */    /* 💼 主天蓝色 - 品牌色核心 */    /* 💼 深主天蓝色 - 按钮悬停 */    /* 💼 更深天蓝色 - 按钮激活 */    /* 💼 深色天蓝色 - 渐变终点 */    /* 💼 最深天蓝色 - 强调色 */

  /* 💙 辅助蓝色系统 - Secondary Blue Palette */    /* 💙 极浅蓝色 - 信息背景 */    /* 💙 浅蓝色 - 信息边框 */    /* 💙 中浅蓝色 - 链接色 */    /* 💙 中蓝色 - 图标色 */    /* 💙 深蓝色 - 按钮色 */    /* 💙 主蓝色 - 强调色 */    /* 💙 深主蓝色 - 悬停色 */    /* 💙 更深蓝色 - 激活色 */

  /* 🔵 天蓝色系统 - Light Blue Palette (替代绿色) */    /* 🔵 极浅天蓝色 - 背景 */    /* 🔵 浅天蓝色 - 边框 */    /* 🔵 中浅天蓝 - 装饰 */    /* 🔵 中天蓝色 - 次要 */    /* 🔵 深天蓝色 - 主要 */    /* 🔵 标准天蓝 - 品牌蓝 */    /* 🔵 深品牌天蓝 - 悬停蓝 */    /* 🔵 更深天蓝 - 激活蓝 */
    /* 🟡 浅黄色系统 - Light Yellow Palette */    /* 🟡 极浅黄色 - 背景 */    /* 🟡 浅黄色 - 边框 */    /* 🟡 中浅黄 - 装饰 */    /* 🟡 中黄色 - 次要 */    /* 🟡 深黄色 - 主要 */    /* 🟡 标准黄 - 品牌黄 */    /* 🟡 深品牌黄 - 悬停黄 */    /* 🟡 更深黄 - 激活黄 */
    /* ⚪ 中性色系统 - Business Gray Palette */    /* ⚪ 纯白色 - 卡片背景 */    /* ⚪ 极浅灰 - 页面背景 */    /* ⚪ 浅灰色 - 分割线 */    /* ⚪ 中浅灰 - 边框色 */    /* ⚪ 中灰色 - 禁用边框 */    /* ⚪ 深中灰 - 占位符 */    /* ⚪ 深灰色 - 次要文字 */    /* ⚪ 更深灰 - 辅助文字 */    /* ⚪ 深色文字 - 主要文字 */    /* ⚪ 极深灰 - 标题文字 */    /* ⚪ 最深灰 - 重要文字 */

  /* 🥇 金色系统 - Gold Accent Palette (权威感提升) */    /* 🥇 极浅奶黄色 - 背景高光 */    /* 🥇 浅奶黄色 - 卡片背景 */    /* 🥇 中浅奶黄 - 边框色 */    /* 🥇 中奶黄色 - 装饰色 */    /* 🥇 浅奶黄色 - 按钮色 */    /* 🥇 标准浅奶黄 - 品牌金 */    /* 🥇 温和浅黄 - 悬停金 */    /* 🥇 柔和浅黄 - 激活金 */

    /* 🎯 状态色系统 - 商务主题优化版 */    /* ✅ 成功天空蓝色 - 成功状态 */    /* ✅ 极浅天蓝色 - 成功背景 */    /* ✅ 成功状态渐变 */    /* ✅ 成功高光渐变 */    /* ⚠️ 商务黄色 - 警告状态 */    /* ⚠️ 浅警告黄 - 警告背景 */      /* ❌ 失败暗金黄色 - 错误状态 */      /* ❌ 极浅浅浅黄色 - 错误背景 */      /* ❌ 主错误色 - 暗金色 */      /* ❌ 错误状态渐变 - 浅浅浅黄色到浅灰色 */      /* ❌ 错误高光渐变 */      /* ❌ 不可点击状态渐变 */      /* ℹ️ 商务蓝色 - 信息状态 */    /* ℹ️ 浅信息蓝 - 信息背景 */      /* 🔄 商务灰色 - 处理状态 */      /* 🔄 浅处理灰 - 处理背景 */

  /* ═══════════════════════════════════════════════════════════════════════════
   * 🌈 GRADIENT SYSTEM - 渐变系统
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 🎨 主背景渐变 - 商务蓝白渐变 */
  /* 🔘 按钮渐变系统 */
  /* 🃏 卡片渐变 - 半透明白色到浅天蓝色 */
  /* ✨ 特殊渐变 - 浅黄高光效果 */
  /* 💼💙 商务蓝色主题渐变 - 右栏专用 */
  /* ═══════════════════════════════════════════════════════════════════════════
   * 🌟 SHADOW SYSTEM - 阴影系统
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 🎭 基础阴影 - 商务蓝灰阴影系统 */
  /* 🔘 按钮专用阴影 */
  /* ✨ 特殊效果阴影 */
  /* ═══════════════════════════════════════════════════════════════════════════
   * 📝 TYPOGRAPHY SYSTEM - 字体系统
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 🔤 字体族 - 现代化系统字体栈 */
  /* 📏 字体大小 - 和谐比例系统 */    /* 📏 极小 - 辅助信息 */    /* 📏 小号 - 次要文字 */    /* 📏 基础 - 正文文字 */    /* 📏 大号 - 小标题 */    /* 📏 特大 - 主标题 */    /* 📏 超大 - 页面标题 */

  /* 💪 字体粗细 - 层次分明 */    /* 💪 正常 - 正文 */    /* 💪 中等 - 强调 */    /* 💪 半粗 - 小标题 */    /* 💪 粗体 - 主标题 */

  /* 📐 行高 - 舒适阅读 */    /* 📐 紧凑 - 标题 */    /* 📐 适中 - 副标题 */    /* 📐 正常 - 正文 */    /* 📐 宽松 - 长文本 */

  /* ═══════════════════════════════════════════════════════════════════════════
   * 📐 SPACING SYSTEM - 间距系统
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 📏 基础间距 - 8px 基准系统 */    /* 📏 极小间距 - 细节调整 */    /* 📏 小间距 - 紧密元素 */    /* 📏 中间距 - 标准间距 */    /* 📏 大间距 - 组件间距 */    /* 📏 特大间距 - 区块间距 */    /* 📏 超大间距 - 页面间距 */    /* 📏 巨大间距 - 主要分割 */

  /* 🎯 组件专用间距 */    /* 🃏 卡片间距 */    /* 🔘 按钮组间距 */    /* 📦 区块间距 */

  /* ═══════════════════════════════════════════════════════════════════════════
   * 🔲 BORDER SYSTEM - 边框系统
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 🔘 圆角系统 - 现代化圆角 */    /* 🔘 小圆角 - 按钮 */    /* 🔘 中圆角 - 输入框 */    /* 🔘 大圆角 - 卡片 */    /* 🔘 特大圆角 - 容器 */    /* 🔘 完全圆角 - 圆形 */

  /* 📏 边框宽度与颜色 */    /* 📏 标准边框宽度 */      /* 💼 极淡天蓝色边框 */      /* 💼 悬停时稍深边框 */      /* 💼 焦点时深边框 */

  /* ═══════════════════════════════════════════════════════════════════════════
   * ⚡ TRANSITION SYSTEM - 过渡动画系统
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 🎭 过渡时间 - 自然流畅的动画曲线 */    /* ⚡ 快速 - 微交互 */    /* ⚡ 正常 - 标准交互 */    /* ⚡ 缓慢 - 页面切换 */

  /* 🎯 专用过渡 */    /* 🎯 弹跳效果 */    /* 🎯 平滑效果 */      /* 🎯 微动效 - 按钮点击等 */

  /* ═══════════════════════════════════════════════════════════════════════════
   * 📐 LAYOUT DIMENSIONS - 布局尺寸系统
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 📏 栏宽度定义 */    /* 📏 左栏宽度 - 输入区域 */    /* 📏 右栏宽度 - 控制台 */    /* 📏 栏间距 - 分割间隙 */    /* 📏 栏内边距 - 内容边距 */

  /* 🎯 响应式断点 */    /* 🎯 小屏断点 */    /* 🎯 中屏断点 */    /* 🎯 大屏断点 */    /* 🎯 超大屏断点 */

  /* === Z-index层级 === *//* 添加:root中的RGB颜色变量，用于rgba动态透明度 */
  /* ═══════════════════════════════════════════════════════════════════════════
   * 📝 TEXT COLOR SYSTEM - 文本颜色系统 (修复缺失的变量)
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 文本颜色层次 */
  --text-primary: var(--color-gray-800);     /* 主要文本 - 深灰色 */
  --text-secondary: var(--color-gray-600);   /* 次要文本 - 中灰色 */
  --text-tertiary: var(--color-gray-500);    /* 三级文本 - 浅灰色 */
  --text-disabled: var(--color-gray-400);    /* 禁用文本 - 更浅灰色 */
  --text-inverse: var(--color-white);        /* 反色文本 - 白色 */
}

* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-family);
  font-feature-settings: 'kern' 1, 'liga' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  background: var(--gradient-main);
  color: var(--color-gray-800);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  min-height: 100vh;
}

::selection {
  background: rgba(102, 126, 234, 0.2);
  color: var(--color-gray-900);
}

*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
  border-radius: 4px;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg,
        var(--color-primary-300) 0%,
        var(--color-primary-500) 100%);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg,
        var(--color-primary-400) 0%,
        var(--color-primary-600) 100%);
}

.unified-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
    border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: var(--border-radius-lg);
  padding: var(--layout-padding);
  margin-bottom: var(--card-gap);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);

    position: relative;
}

.unified-card:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(232, 244, 253, 0.9) 100%);
    box-shadow: var(--shadow-elevated);
    transform: translateY(-2px);
    border-color: rgba(35, 146, 239, 0.3);
  }

  .unified-card:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.unified-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: var(--transition-normal);
  text-decoration: none;
  user-select: none;
}

.unified-button-primary {
  background: var(--gradient-button);
  color: var(--color-white);
  box-shadow: var(--shadow-button);
}

.unified-button-primary:hover {
  background: var(--gradient-button-hover);
  box-shadow: var(--shadow-button-hover);
  transform: translateY(-1px);
}

.unified-button-secondary {
  background: var(--color-white);
  color: var(--color-gray-700);
  border: var(--border-width) solid var(--border-color);
}

.unified-button-secondary:hover {
  background: linear-gradient(135deg,
        var(--color-primary-50) 0%,
          var(--color-primary-100) 100%);
  border-color: var(--color-primary-300);
  color: var(--color-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-elevated);
  }

  .unified-button-secondary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.unified-button-blue {
  background: linear-gradient(135deg,
        var(--color-blue-400) 0%,
        var(--color-blue-500) 100%);
  color: white;
  border-color: var(--color-blue-400);
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
}

.unified-button-blue:hover {
  background: linear-gradient(135deg,
        var(--color-blue-500) 0%,
        var(--color-blue-600) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

.unified-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  background: var(--color-white);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-md);
  transition: var(--transition-fast);
}

.unified-input:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: var(--shadow-focus);
    background: rgba(255, 255, 255, 1);
}

.batch-processor-layout {
  display: grid;
  min-height: 100vh;
    width: 100%;
    max-width: 100vw;
  margin: 0;
  padding: 12px;
  gap: 12px;
  box-sizing: border-box;
  background: var(--gradient-main);
  overflow-x: hidden;
    overflow-y: visible;

  grid-template-columns: 1fr 3fr 1fr;
  grid-template-rows: auto;

  min-width: 1024px;

  animation: layout-appear 0.4s ease-out;
}

.custom-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  }

.custom-scrollbar::-webkit-scrollbar {
  display: none;
}

.layout-sidebar,
.layout-main,
.layout-console {
  border-radius: var(--border-radius-lg);
  padding: 16px;
  box-sizing: border-box;
  min-height: calc(100vh - 24px);
    max-height: none;
    overflow: visible;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.layout-sidebar {
  background: linear-gradient(135deg,
        rgba(232, 244, 253, 0.95) 0%,
          rgba(255, 255, 255, 0.9) 100%);
  border: 1px solid rgba(35, 146, 239, 0.2);
  overflow-y: auto;
  overflow-x: hidden;
  animation: slide-in-left 0.5s ease-out 0.1s both;
  display: flex;
    flex-direction: column;

  scrollbar-width: none;
  -ms-overflow-style: none;
}

.layout-sidebar::-webkit-scrollbar {
  display: none;
}

.layout-sidebar>div {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px;
  flex: 1;

    min-height: 0;

}

.layout-sidebar .module-section {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(233, 30, 99, 0.1);
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s ease;
}

.layout-sidebar .module-section:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(233, 30, 99, 0.15);
  box-shadow: 0 1px 4px rgba(233, 30, 99, 0.1);
}

.layout-sidebar h3,
.layout-sidebar .section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-primary-600);
  margin: 0 0 8px 0;
  padding: 0 0 4px 0;
  border-bottom: 1px solid rgba(233, 30, 99, 0.1);
}

.layout-sidebar .list-item,
.layout-sidebar .module-item {
  padding: 8px 10px;
  border-radius: 4px;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.5);
  margin-bottom: 4px;
  font-size: 13px;
  line-height: 1.3;
}

.layout-sidebar .list-item:hover,
.layout-sidebar .module-item:hover {
  background: rgba(233, 30, 99, 0.06);
  border-color: rgba(233, 30, 99, 0.15);
  transform: translateX(2px);
  box-shadow: 0 1px 3px rgba(233, 30, 99, 0.1);
}

.layout-sidebar button {
  padding: 8px 14px;
  border-radius: 6px;
  border: 1px solid rgba(233, 30, 99, 0.3);
  background: rgba(255, 255, 255, 0.9);
  color: var(--color-primary-600);
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  width: 100%;
  margin-top: 6px;
}

.layout-sidebar button:hover {
  background: var(--color-primary-50);
  border-color: var(--color-primary-400);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(233, 30, 99, 0.15);
}

.layout-sidebar input,
.layout-sidebar select {
  padding: 8px 12px;
  border: 1px solid rgba(233, 30, 99, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  transition: all 0.2s ease;
  width: 100%;
  margin-bottom: 8px;
}

.layout-sidebar input:focus,
.layout-sidebar select:focus {
  border-color: var(--color-primary-400);
  box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
  background: rgba(255, 255, 255, 1);
}

.layout-main {
  background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.98) 0%,
          rgba(248, 250, 252, 0.95) 100%);
  border: 1px solid rgba(148, 163, 184, 0.15);
  overflow: visible;
  display: flex;
  flex-direction: column;
  gap: 0;
  animation: fade-in-up 0.5s ease-out 0.2s both;
}

.layout-main>div {
  min-height: auto;
    max-height: none;
    overflow-y: hidden;
  overflow-x: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 12px 16px;
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
  animation: fade-in-up 0.3s ease-out 0.5s both;

  scrollbar-width: none;
    -ms-overflow-style: none;
  }

.layout-main>div::-webkit-scrollbar {
  display: none;
}

.layout-main .space-y-6,
.layout-main .space-y-3 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px !important;
  padding: 0 !important;
  margin: 0 !important;
  min-height: 0;
}

.layout-main .space-y-6>*+*,
.layout-main .space-y-3>*+* {
  margin-top: 8px !important;
}

.layout-main .pb-8,
.layout-main .pb-4 {
  padding-bottom: 8px !important;
}

.layout-main .optimized-card {
  flex-shrink: 0;
  margin-bottom: 0;
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(148, 163, 184, 0.12);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.05),
    0 1px 2px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 100%;
  position: relative;
  overflow: hidden;
}

.layout-main .optimized-card:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(35, 146, 239, 0.3);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.08),
    0 2px 4px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.layout-main .optimized-card h3,
.layout-main .optimized-card .card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.layout-main .optimized-card:last-child {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.layout-main .optimized-card:last-child>div {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.layout-main textarea,
.layout-main .textarea-container {
  flex: 1;
  min-height: 120px;
  resize: vertical;
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 6px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.2s ease;
  font-size: 14px;
  line-height: 1.5;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.layout-main .optimized-card,
.layout-main .unified-card,
.layout-main form,
.layout-main .form-container {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.layout-main>div>*:first-child {
  margin-top: 0;
}

.layout-main .optimized-card:first-child,
.layout-main .unified-card:first-child {
  margin-top: 0;
}

.layout-main.compact {
  padding: 8px;
}

.layout-main.compact>div {
  padding: 4px 8px;
  gap: 8px;
}

.layout-main.compact .optimized-card {
  padding: 12px;
}

.layout-main>div:first-child {
  padding-top: 4px !important;
}

.layout-main .py-12,
.layout-main .py-8 {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

.layout-main .mb-4,
.layout-main .mb-6 {
  margin-bottom: 12px !important;
}

.layout-main textarea:focus,
.layout-main .textarea-container:focus-within {
  border-color: var(--color-primary-300);
  box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
  background: rgba(255, 255, 255, 1);
}

.layout-main .result-container {
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.15);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.layout-main .text-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: var(--text-secondary);
  background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.6) 0%,
          rgba(255, 255, 255, 0.8) 100%);
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.layout-main .text-center:hover {
  border-color: rgba(233, 30, 99, 0.25);
  background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.8) 0%,
          rgba(255, 255, 255, 0.95) 100%);
}

.layout-console {
  background: linear-gradient(135deg,
        rgba(227, 242, 253, 0.95) 0%,
          rgba(255, 255, 255, 0.9) 100%);
  border: 1px solid rgba(33, 150, 243, 0.2);
  overflow-y: auto;
  overflow-x: hidden;
  animation: slide-in-right 0.5s ease-out 0.3s both;

    scrollbar-width: none;

    -ms-overflow-style: none;

}

.layout-console::-webkit-scrollbar {
  display: none;

}

.layout-console>div {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px;
  min-height: 0;
    flex: 1;
  animation: fade-in-up 0.3s ease-out 0.5s both;
}

.layout-console .module-section {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(33, 150, 243, 0.1);
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s ease;
}

.layout-console .module-section:hover {
  background: rgba(255, 255, 255, 0.85);
  border-color: rgba(33, 150, 243, 0.2);
  box-shadow: 0 1px 4px rgba(33, 150, 243, 0.1);
}

.layout-console h3,
.layout-console .section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-blue-600);
  margin: 0 0 8px 0;
  padding: 0 0 4px 0;
  border-bottom: 1px solid rgba(33, 150, 243, 0.15);
  display: flex;
  align-items: center;
  gap: 6px;
}

.layout-console .stats-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 6px;
  padding: 12px;
  text-align: center;
  transition: all 0.2s ease;
  margin-bottom: 6px;
}

.layout-console .stats-card:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(33, 150, 243, 0.2);
  box-shadow: 0 2px 6px rgba(33, 150, 243, 0.1);
  transform: translateY(-1px);
}

.layout-console .stats-card .stat-number {
  font-size: 20px;
  font-weight: 700;
  color: var(--color-blue-600);
  margin-bottom: 2px;
}

.layout-console .stats-card .stat-label {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 500;
}

.layout-console .log-container {
  flex: 1;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.15);
  border-radius: 6px;
  padding: 8px;
  overflow-y: auto;
  min-height: 200px;
}

.layout-console .log-entry {
  padding: 8px 10px;
  border-radius: 4px;
  margin-bottom: 4px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
  line-height: 1.3;
  border-left: 2px solid transparent;
  transition: all 0.2s ease;
  background: rgba(248, 250, 252, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.08);
}

.layout-console .log-entry:hover {
  background: rgba(33, 150, 243, 0.04);
  border-left-color: var(--color-blue-400);
  border-color: rgba(33, 150, 243, 0.15);
  transform: translateX(-1px);
}

.layout-console .log-entry .log-time {
  font-size: 10px;
  color: var(--text-secondary);
  font-weight: 500;
  margin-bottom: 2px;
}

.layout-console .log-entry .log-content {
  color: var(--text-primary);
  word-break: break-word;
}

.layout-console .status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.layout-console .status-success {
  background: var(--color-blue-400);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
    -webkit-backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
}

@keyframes overlayFadeOut {
  from {
    opacity: 1;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
  to {
    opacity: 0;
    backdrop-filter: blur(0);
    -webkit-backdrop-filter: blur(0);
  }
}

@keyframes fade-slide-in {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes history-panel-in {
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes buttonHover {
  0% {
    transform: translateY(0) scale(1);
  }
  100% {
    transform: translateY(-2px) scale(1.02);
  }
}

@keyframes button-loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes button-pulse {
  0% {
    box-shadow:
      0 0 15px rgba(var(--color-primary-rgb), 0.5),
      0 0 30px rgba(var(--color-primary-rgb), 0.2);
  }
  100% {
    box-shadow:
      0 0 20px rgba(var(--color-primary-rgb), 0.7),
      0 0 40px rgba(var(--color-primary-rgb), 0.4);
  }
}

@keyframes blue-button-pulse {
  0% {
    box-shadow:
      0 0 15px rgba(var(--color-blue-rgb), 0.5),
      0 0 30px rgba(var(--color-blue-rgb), 0.2);
  }
  100% {
    box-shadow:
      0 0 20px rgba(var(--color-blue-rgb), 0.7),
      0 0 40px rgba(var(--color-blue-rgb), 0.4);
  }
}

@keyframes inputFocus {
  0% {
    box-shadow: 0 0 0 0 rgba(233, 30, 99, 0);
  }
  100% {
    box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
  }
}

@keyframes breathing {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0.4);
  }
  50% {
    box-shadow: 0 0 10px 4px rgba(var(--color-primary-rgb), 0.2);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0.4);
  }
}

@keyframes cardHover {
  0% {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  100% {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

@keyframes iconSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes iconWiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-5deg);
  }
  75% {
    transform: rotate(5deg);
  }
}

@keyframes gentle-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes gentle-glow {
  0%, 100% {
    filter: drop-shadow(0 0 3px rgba(173, 123, 233, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 6px rgba(173, 123, 233, 0.5));
  }
}

@keyframes settingsBackgroundFlow {
  0%, 100% {
    background-position:
      0% 0%,
      100% 20%,
      20% 80%,
      80% 100%,
      0 0,
      0 0;
  }
  25% {
    background-position:
      10% 10%,
      90% 30%,
      30% 70%,
      70% 90%,
      5px 5px,
      5px 5px;
  }
  50% {
    background-position:
      20% 15%,
      80% 35%,
      35% 65%,
      65% 85%,
      10px 10px,
      10px 10px;
  }
  75% {
    background-position:
      15% 25%,
      85% 25%,
      25% 75%,
      75% 95%,
      5px 15px,
      15px 5px;
  }
}

@keyframes historyBackgroundFlow {
  0%, 100% {
    background-position:
      0% 0%, 100% 15%, 45% 75%, 0 0, 0 0;
  }
  33% {
    background-position:
      10% 15%, 90% 25%, 55% 65%, 8px 8px, 8px 8px;
  }
  67% {
    background-position:
      20% 10%, 80% 35%, 35% 85%, 4px 12px, 12px 4px;
  }
}

@keyframes promptBackgroundFlow {
  0%, 100% {
    background-position:
      25% 35%, 75% 25%, 35% 65%, 0 0, 0 0;
  }
  33% {
    background-position:
      35% 25%, 65% 35%, 45% 55%, 8px 8px, 8px 8px;
  }
  67% {
    background-position:
      15% 45%, 85% 15%, 25% 75%, 4px 12px, 12px 4px;
  }
}

@keyframes buttonGlow {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  }
  50% {
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.25),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                0 0 20px rgba(139, 92, 246, 0.15) !important;
  }
}

.layout-console .status-error {
  background: var(--color-primary-400);
}

.layout-console .status-warning {
  background: var(--color-warning);
}

.layout-console .status-processing {
  background: var(--color-processing);
  animation: pulse 2s infinite;
}

.unified-hover-subtle {
  transition: var(--transition-normal);
}

.unified-hover-subtle:hover {
  background: linear-gradient(135deg,
        var(--color-primary-50) 0%,
          var(--color-primary-100) 100%);
  border-color: var(--color-primary-300);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(233, 30, 99, 0.15);
}

.icon {
  width: 1.25rem;
  height: 1.25rem;
  transition: var(--transition-fast);
  flex-shrink: 0;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.icon-primary {
  color: var(--color-primary-400);

}

.icon-secondary {
  color: var(--color-blue-400);

}

.icon-success {
  color: #64b5f6;

}

.icon-warning {
  color: #ffcc80;

}

.icon-error {
  color: #87ceeb;

}

.drawer-header {
  flex-shrink: 0;
  padding: 12px 16px 8px;
  border-bottom: 1px solid var(--color-gray-200);
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-white) 100%);
}

.drawer-header-gold {
  background: linear-gradient(135deg,
      var(--color-gold-50) 0%,
      rgba(251, 191, 36, 0.05) 50%,
      var(--color-white) 100%);
  border-bottom: 1px solid var(--color-gold-200);
}

.drawer-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 12px 16px;
  background: var(--color-white);
}

.drawer-section {
  margin-bottom: 12px;
  padding: 8px 12px;
  background: var(--color-gray-50);
  border: 1px solid var(--color-gray-200);
  border-radius: 6px;
}

.form-label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: var(--color-gray-700);
  margin-bottom: 4px;
  line-height: 1.3;
}

.form-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-gray-300);
  border-radius: 3px;
  background: var(--color-white);
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-checkbox:checked {
  background: var(--color-primary-500);
  border-color: var(--color-primary-500);
}

.icon:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
}

.icon-primary:hover {
  filter: drop-shadow(0 0 6px rgba(236, 64, 122, 0.3));

}

.icon-secondary:hover {
  filter: drop-shadow(0 0 6px rgba(66, 165, 245, 0.3));

}

.icon-monochrome:hover {
  color: #fff8dc;

  filter: drop-shadow(0 0 6px rgba(173, 123, 233, 0.3));

}

.icon-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-normal);
  background: linear-gradient(135deg,
        var(--color-primary-50) 0%,
        var(--color-blue-50) 100%);
  border: 1px solid rgba(233, 30, 99, 0.1);
}

.icon-container-primary {
  background: linear-gradient(135deg,
        var(--color-primary-50) 0%,
        var(--color-primary-100) 100%);
  border: 1px solid var(--color-primary-200);
}

.icon-container-secondary {
  background: linear-gradient(135deg,
        var(--color-blue-50) 0%,
        var(--color-blue-100) 100%);
  border: 1px solid var(--color-blue-200);
}

.icon-container:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 16px rgba(233, 30, 99, 0.15);
  border-color: var(--color-primary-300);
}

.icon-container-monochrome:hover {
  background: linear-gradient(135deg,
        rgba(248, 187, 217, 0.5) 0%,
          rgba(144, 202, 249, 0.5) 100%);
  box-shadow: 0 6px 16px rgba(142, 36, 170, 0.25);
}

.ai-drawer-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-800);
}

.gradient-text-title {
  color: var(--color-gray-800);
  background: linear-gradient(135deg,
      var(--color-primary-600),
      var(--color-purple-600));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;

}

@supports not (-webkit-background-clip: text) {
  .gradient-text-title {
    color: var(--color-primary-600);
    background: none;
  }
}

.ai-drawer-title .icon {
  color: #ffd700;

}

.ai-hint-card {
  background: linear-gradient(135deg,
        rgba(248, 187, 217, 0.1) 0%,
          rgba(144, 202, 249, 0.1) 100%);
  border: 1px solid rgba(142, 36, 170, 0.15);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.ai-hint-card .icon {
  color: #ffd700;

  margin-right: var(--spacing-xs);
}

.ai-textarea {
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid rgba(142, 36, 170, 0.2);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-normal);
}

.ai-textarea:focus {
  border-color: #ffd700;

  box-shadow: 0 0 0 3px rgba(173, 123, 233, 0.1);
  background: rgba(255, 255, 255, 1);
}

.ai-textarea:hover {
  border-color: rgba(173, 123, 233, 0.3);
}

.ai-button-group {
  display: flex;
  gap: var(--spacing-sm);
}

.ai-button-primary {
  background: linear-gradient(135deg,
        var(--color-primary-400) 0%,
        #ffd700 100%);

  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-normal);
  box-shadow: 0 4px 12px rgba(173, 123, 233, 0.25);
}

.ai-button-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(173, 123, 233, 0.35);
}

.ai-button-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #ffd700;

  border: 1px solid rgba(173, 123, 233, 0.3);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-normal);
}

.ai-button-secondary:hover {
  background: rgba(248, 187, 217, 0.2);
  border-color: #ffd700;
  transform: translateY(-1px);
}

.ai-drawer-title .icon:hover {
  animation: gentle-pulse 2s infinite;
}

.ai-button-primary .icon {
  color: rgba(255, 255, 255, 0.9);
}

.status-success {
  background: var(--color-success-light);
  color: var(--color-success);
  border-color: var(--color-success);
}

.status-success-gradient {
  background: linear-gradient(135deg,
        var(--color-success-light) 0%,
        var(--color-blue-50) 100%);
  border-color: var(--color-success);
}

.status-success-button {
  background: var(--color-success);
  color: var(--color-white);
}

.status-success-button:hover {
  background: var(--color-blue-600);
}

.status-error {
  background: var(--color-error-light);
  color: var(--color-error);
  border-color: var(--color-error);
}

.status-indicator .icon,
.status-indicator svg,
.status-indicator [class*="icon"] {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.status-error-gradient {
  background: linear-gradient(135deg,
        var(--color-error-light) 0%,
        var(--color-primary-50) 100%);
  border-color: var(--color-error);
}

.status-error-button {
  background: var(--color-error);
  color: var(--color-white);
}

.status-error-button:hover {
  background: var(--color-primary-600);
}

.status-warning {
  background: var(--color-warning-light);
  color: var(--color-warning);
  border-color: var(--color-warning);
}

.status-processing {
  background: var(--color-processing-light);
  color: var(--color-processing);
  border-color: var(--color-processing);
}

.content-spacing {
  gap: 18px;
}

.content-spacing-sm {
  gap: 10px;
}

.content-spacing-lg {
  gap: 28px;
}

.module-group:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(0, 0, 0, 0.12);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.module-group-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-dense {
  line-height: 1.3;
  font-size: 12px;
}

.info-comfortable {
  line-height: 1.5;
  font-size: 14px;
}

.info-spacious {
  line-height: 1.6;
  font-size: 15px;
}

.visual-hierarchy-1 {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.visual-hierarchy-2 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 10px;
}

.visual-hierarchy-3 {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.visual-hierarchy-4 {
  font-size: 13px;
  font-weight: 400;
  color: var(--text-tertiary);
  margin-bottom: 6px;
}

.interactive-element {
  transition: all 0.2s ease;
  cursor: pointer;
}

.interactive-element:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.interactive-element:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.divider {
  height: 1px;
  background: linear-gradient(to right,
        transparent 0%,
          rgba(148, 163, 184, 0.3) 50%,
          transparent 100%);
  margin: 16px 0;
}

.divider-thick {
  height: 2px;
  background: linear-gradient(to right,
        transparent 0%,
          rgba(148, 163, 184, 0.4) 50%,
          transparent 100%);
  margin: 20px 0;
}

.optimized-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--border-radius-lg);
  padding: var(--layout-padding);
  margin-bottom: var(--card-gap);
  transition: var(--transition-normal);
}

.optimized-card:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: var(--color-primary-300);
  transform: translateY(-1px);
}

.optimized-button-primary {
  background: linear-gradient(135deg,
        var(--color-primary-400) 0%,
        var(--color-primary-500) 100%);
  color: white;
  border-color: var(--color-primary-400);
  box-shadow: 0 2px 4px rgba(233, 30, 99, 0.2);
}

.optimized-button-primary:hover {
  background: linear-gradient(135deg,
        var(--color-primary-500) 0%,
        var(--color-primary-600) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(233, 30, 99, 0.3);
}

.optimized-button-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: var(--color-primary-600);
  border-color: rgba(233, 30, 99, 0.3);
}

.optimized-button-secondary:hover {
  background: var(--color-primary-50);
  border-color: var(--color-primary-400);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(233, 30, 99, 0.15);
}

.optimized-button-blue {
  background: linear-gradient(135deg,
        var(--color-blue-400) 0%,
        var(--color-blue-500) 100%);
  color: white;
  border-color: var(--color-blue-400);
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
}

.optimized-button-blue:hover {
  background: linear-gradient(135deg,
        var(--color-blue-500) 0%,
        var(--color-blue-600) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

.optimized-input:focus {
  border-color: var(--color-primary-400);
  box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
  background: rgba(255, 255, 255, 1);
  outline: none;
}

.optimized-input:hover {
  border-color: rgba(148, 163, 184, 0.4);
}

.optimized-textarea:focus {
  border-color: var(--color-primary-400);
  box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
  background: rgba(255, 255, 255, 1);
  outline: none;
}

.optimized-select:focus {
  border-color: var(--color-primary-400);
  box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
  background: rgba(255, 255, 255, 1);
  outline: none;
}

.optimized-button-primary {
  background: var(--gradient-button);
  color: var(--color-white);
}

.optimized-button-primary:hover {
  background: var(--gradient-button-hover);
  transform: translateY(-1px);
}

.optimized-button-secondary {
  background: var(--color-white);
  color: var(--color-gray-700);
  border: 1px solid var(--border-color);
}

.optimized-button-secondary:hover {
  background: var(--color-primary-50);
  border-color: var(--color-primary-300);
  color: var(--color-primary-700);
}

.batch-processor-layout.processing-mode {
  grid-template-columns: 1fr 3fr 1fr;
}

.batch-processor-layout.fullscreen {
  grid-template-columns: 1fr;
}

.batch-processor-layout.fullscreen .layout-sidebar,
.batch-processor-layout.fullscreen .layout-console {
  display: none;
}

@keyframes layout-appear {
  from {
    opacity: 0;
    transform: scale(0.98);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.history-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(227, 242, 253, 0.4) 100%);
  border-radius: 12px;
  margin: 16px;
}

.history-loading-state .icon {
  margin-bottom: 12px;
}

.history-loading-state p {
  color: var(--color-blue-500);
  font-size: var(--font-size-sm);
  margin: 0;
  font-weight: var(--font-weight-medium);
}

@keyframes pulse-glow {
  0% {
    box-shadow:
        0 4px 15px rgba(102, 126, 234, 0.4),
        0 0 0 0 rgba(102, 126, 234, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
  }
  100% {
    box-shadow:
        0 4px 15px rgba(102, 126, 234, 0.6),
        0 0 15px rgba(118, 75, 162, 0.5);
    border-color: rgba(255, 255, 255, 0.6);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes buttonHover {
  0% {
    transform: translateY(0) scale(1);
  }
  100% {
    transform: translateY(-2px) scale(1.02);
  }
}

@keyframes inputFocus {
  0% {
    box-shadow: 0 0 0 0 rgba(233, 30, 99, 0);
  }
  100% {
    box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
  }
}

@keyframes cardHover {
  0% {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  100% {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

@keyframes listItemHover {
  0% {
    transform: translateX(0);
    background-color: transparent;
  }
  100% {
    transform: translateX(4px);
    background-color: rgba(233, 30, 99, 0.05);
  }
}

@keyframes iconSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes iconPulse {
  0%,
    100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes iconWiggle {
  0%,
    100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-5deg);
  }
  75% {
    transform: rotate(5deg);
  }
}

@keyframes tabSwitch {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rippleEffect {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes gradientText {
  0%,
    100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes sparkle {
  0%,
    100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

::selection {
  background: rgba(102, 126, 234, 0.2);
  color: var(--color-gray-900);
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg,
        var(--color-primary-300) 0%,
        var(--color-primary-500) 100%);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg,
        var(--color-primary-400) 0%,
        var(--color-primary-600) 100%);
}

*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
  border-radius: 4px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(102, 126, 234, 0.2);
  border-top: 3px solid var(--color-primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-dots::after {
  content: '';
  animation: pulse 1.5s ease-in-out infinite;
}

.unified-theme-enhanced {

  --theme-version: '2.0-enhanced';
    --theme-status: '完美';
  }

  .enhanced-drawer-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center,
        rgba(99, 102, 241, 0.1) 0%,
        transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  @keyframes overlay-ripple {
    0% {
      transform: translate(-50%, -50%) scale(0);
      opacity: 1;
    }

    100% {
      transform: translate(-50%, -50%) scale(4);
      opacity: 0;
    }
  }

  .enhanced-drawer-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent);
    transition: left 0.6s ease;
    z-index: 1;
    pointer-events: none;
  }

  .enhanced-drawer-container .smooth-drawer-header {
    position: relative;
    overflow: hidden;
  }

  .enhanced-drawer-container .smooth-drawer-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        var(--color-primary-400),
        var(--color-blue-400),
        var(--color-primary-400));
    transform: scaleX(0);
    transition: transform 0.4s ease;
    transform-origin: left;
  }

  .enhanced-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    pointer-events: none;
    z-index: 1;
  }

  .enhanced-button:active::before {
    width: 300px;
    height: 300px;
    transition: width 0.1s ease, height 0.1s ease;
  }

  .enhanced-button:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .enhanced-button:active {
    transform: translateY(0) scale(0.98);
    transition: all 0.1s ease;
  }

  .enhanced-button.loading {
    pointer-events: none;
    position: relative;
  }

  .enhanced-button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: button-loading-spin 1s linear infinite;
    z-index: 2;
  }

  .enhanced-button.loading .button-text {
    opacity: 0;
  }

  @keyframes button-loading-spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .enhanced-button-primary {
    background: linear-gradient(135deg, var(--color-primary-400) 0%, var(--color-primary-600) 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 12px rgba(233, 30, 99, 0.3);
    position: relative;
    overflow: hidden;
  }

  .enhanced-button-primary::before {
    background: rgba(255, 255, 255, 0.2);
  }

  .enhanced-button-primary:hover {
    background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-700) 100%);
    box-shadow: 0 8px 25px rgba(233, 30, 99, 0.4);
  }

  .enhanced-button-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: var(--color-primary-600);
    border: 1px solid var(--color-primary-300);
    box-shadow: 0 2px 8px rgba(233, 30, 99, 0.1);
  }

  .enhanced-button-secondary::before {
    background: var(--color-primary-100);
  }

  .enhanced-button-secondary:hover {
    background: var(--color-primary-50);
    border-color: var(--color-primary-400);
    color: var(--color-primary-700);
    box-shadow: 0 6px 20px rgba(233, 30, 99, 0.2);
  }

  .enhanced-button-blue {
    background: linear-gradient(135deg, var(--color-blue-400) 0%, var(--color-blue-600) 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
  }

  .enhanced-button-blue::before {
    background: rgba(255, 255, 255, 0.2);
  }

  .enhanced-button-blue:hover {
    background: linear-gradient(135deg, var(--color-blue-500) 0%, var(--color-blue-700) 100%);
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
  }

      .enhanced-drawer-header {
        flex-shrink: 0 !important;
        padding: 20px 24px 16px 24px !important;
        background: linear-gradient(135deg,
            rgba(252, 228, 236, 0.3) 0%,
            rgba(255, 255, 255, 0.8) 50%,
            rgba(227, 242, 253, 0.3) 100%) !important;
        border-bottom: 1px solid rgba(233, 30, 99, 0.08) !important;
        backdrop-filter: blur(10px) !important;
        -webkit-backdrop-filter: blur(10px) !important;

      }

      .enhanced-drawer-content {
        flex: 1 !important;
        min-height: 0 !important;
        display: flex !important;
        flex-direction: column !important;
        overflow: hidden !important;
        padding: 0 !important;
}

.smooth-drawer-overlay {
  animation: overlayFadeIn 0.3s ease forwards;
}

.smooth-drawer-overlay.closing {
  animation: overlayFadeOut 0.3s ease forwards;
}

@keyframes fade-slide-in {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.history-panel-container {
  transform: translateX(20px);
  opacity: 0;
  animation: history-panel-in 0.5s ease-out 0.3s forwards;
}

@keyframes history-panel-in {
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.history-record-item:hover,
.history-record-item-modern:hover {
  transform: translateY(-3px) scale(1.01);
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.98) 0%,
      rgba(252, 228, 236, 0.15) 50%,
      rgba(227, 242, 253, 0.15) 100%);
  border-color: rgba(233, 30, 99, 0.2);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(233, 30, 99, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.3);
}

.history-record-title {
  font-size: 15px;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
    line-height: 1.3;
}

.history-record-title .icon {
  color: var(--color-primary-500);
  filter: drop-shadow(0 1px 2px rgba(233, 30, 99, 0.2));
}

.history-record-content {
  font-size: 13px;
  color: var(--color-gray-600);
  line-height: 1.5;
    margin-bottom: 8px;
    padding: 8px 12px;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  transition: all 0.2s ease;
}

.history-record-content:hover {
  background: rgba(248, 250, 252, 0.8);
  border-color: rgba(233, 30, 99, 0.15);
}

.history-record-timestamp {
  font-size: 11px;
  color: var(--color-gray-400);
  display: flex;
  align-items: center;
  gap: 4px;
    margin-bottom: 8px;
}

.history-record-timestamp .icon {
  color: var(--color-gray-400);
}

.history-action-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
  font-weight: 500;
  border: 1px solid transparent;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
    background: rgba(255, 255, 255, 0.85);
      color: var(--color-primary-600);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      min-height: 36px;
      box-sizing: border-box;
}

.history-action-btn:hover {
  transform: translateY(-1px);
  background: linear-gradient(135deg,
      var(--color-primary-50) 0%,
      var(--color-blue-50) 100%);
  border-color: var(--color-primary-200);
  color: var(--color-primary-700);
  box-shadow: 0 4px 12px rgba(233, 30, 99, 0.15);
}

.history-action-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(233, 30, 99, 0.1);
}

.history-action-btn .icon,
.history-action-btn svg,
.history-action-btn [class*="icon"] {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.history-action-btn:hover .icon,
.history-action-btn:hover svg,
.history-action-btn:hover [class*="icon"] {
  transform: scale(1.05);
}

.history-action-btn.primary {
  background: linear-gradient(135deg,
      var(--color-primary-400) 0%,
      var(--color-primary-500) 100%);
  color: white;
  border-color: var(--color-primary-400);
}

.history-action-btn.primary:hover {
  background: linear-gradient(135deg,
      var(--color-primary-500) 0%,
      var(--color-primary-600) 100%);
  box-shadow: 0 6px 16px rgba(233, 30, 99, 0.3);
}

.history-action-btn.secondary {
  background: linear-gradient(135deg,
      var(--color-blue-400) 0%,
      var(--color-blue-500) 100%);
  color: white;
  border-color: var(--color-blue-400);
}

.history-action-btn.secondary:hover {
  background: linear-gradient(135deg,
      var(--color-blue-500) 0%,
      var(--color-blue-600) 100%);
  box-shadow: 0 6px 16px rgba(33, 150, 243, 0.3);
}

.history-action-btn.danger {
  background: rgba(216, 27, 96, 0.05);
  color: var(--color-error);
  border-color: rgba(216, 27, 96, 0.2);
}

.history-action-btn.danger:hover {
  background: rgba(216, 27, 96, 0.1);
  border-color: var(--color-error);
  box-shadow: 0 4px 12px rgba(216, 27, 96, 0.2);
}

.history-panel-container {
  transform: translateX(20px);
  opacity: 0;
  animation: history-panel-in 0.5s ease-out 0.3s forwards;
}

@keyframes drawer-slide-in {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes drawer-slide-out {
  0% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
    transform: translateX(100%);
  }
}

@keyframes overlay-advanced-in {
  0% {
    opacity: 0;
    backdrop-filter: blur(0);
    -webkit-backdrop-filter: blur(0);
  }

  100% {
    opacity: 1;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
}

@keyframes overlay-advanced-out {
  0% {
    opacity: 1;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  100% {
    opacity: 0;
    backdrop-filter: blur(0);
    -webkit-backdrop-filter: blur(0);
  }
}

.drawer-glow-background::after {
  content: '';
  position: absolute;
  top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 25% 25%,
        rgba(0, 144, 255, 0.1) 0%,
        rgba(0, 144, 255, 0.05) 25%,
        rgba(0, 144, 255, 0) 50%);
  z-index: -1;
  backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);

    opacity: 0;
  transition: opacity 0.5s ease;
}

.drawer-glow-background.active::after {
  opacity: 1;
}

@keyframes drawer-glow-move {
  0% {
    transform: translate(-30%, -30%) rotate(0deg);
  }

  50% {
    transform: translate(10%, 10%) rotate(180deg);
  }

  100% {
    transform: translate(-10%, 20%) rotate(360deg);
  }
}

.drawer-header-interactive {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.drawer-header-interactive::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg,
      var(--color-primary-300),
      var(--color-blue-300),
      var(--color-primary-300));
  transform-origin: 0 50%;
  transform: scaleX(0);
  transition: transform 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.drawer-header-interactive.active::before {
  transform: scaleX(1);
}

.drawer-header-interactive:hover {
  background-color: rgba(var(--color-primary-rgb), 0.03);
}

.drawer-close-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.drawer-close-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle,
      rgba(var(--color-primary-rgb), 0.2),
      transparent);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
  z-index: -1;
}

.drawer-close-button:hover {
  transform: rotate(90deg) scale(1.1);
}

.drawer-close-button:active::before {
  width: 150px;
  height: 150px;
}

.hover-scale {
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-float {
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.hover-float:hover {
  transform: translateY(-4px);
}

.hover-shadow {
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.hover-shadow:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.press-effect:active {
  transform: scale(0.95);
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

.magnetic-hover {
  transition: transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
}

.fluid-scan::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(to right,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 50%,
      rgba(255, 255, 255, 0) 100%);
  transform: rotate(30deg) translateY(100%);
  animation: fluid-scan-anim 3s ease-in-out infinite;
}

@keyframes fluid-scan-anim {
  0% {
    transform: rotate(30deg) translateY(100%);
  }

  100% {
    transform: rotate(30deg) translateY(-100%);
  }
}

.elastic-click {
  transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.elastic-click:active {
  transform: scale(0.9);
  transition: transform 0.1s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.gradient-move:hover {
  background-position: 100% 0;
}

.focus-breathing:focus {
  animation: breathing 2s infinite ease-in-out;
}

@keyframes breathing {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0.4);
  }

  50% {
    box-shadow: 0 0 10px 4px rgba(var(--color-primary-rgb), 0.2);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0.4);
  }
}

.liquid-ripple::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center,
      rgba(255, 255, 255, 0.8),
      transparent 70%);
  opacity: 0;
  transform: scale(0);
  transition: opacity 0.6s, transform 0.6s;
}

.liquid-ripple:active::after {
  opacity: 0.3;
  transform: scale(2.5);
  transition: opacity 0.4s, transform 0.8s;
}

.button-glow-hover {
  box-shadow: 0 0 0 rgba(var(--color-primary-rgb), 0);
  transition: box-shadow 0.4s ease, transform 0.3s ease;
}

.button-glow-hover:hover {
  box-shadow:
    0 0 15px rgba(var(--color-primary-rgb), 0.4),
    0 0 30px rgba(var(--color-primary-rgb), 0.2);
  transform: translateY(-2px) scale(1.02);
}

.optimized-button-primary:hover,
.unified-button-primary:hover,
.ai-button-primary:hover {
  box-shadow:
    0 0 15px rgba(var(--color-primary-rgb), 0.5),
    0 0 30px rgba(var(--color-primary-rgb), 0.3);
  animation: button-pulse 1.5s infinite alternate;
}

.optimized-button-blue:hover {
  box-shadow:
    0 0 15px rgba(var(--color-blue-rgb), 0.5),
    0 0 30px rgba(var(--color-blue-rgb), 0.3);
  animation: blue-button-pulse 1.5s infinite alternate;
}

.optimized-button-secondary:hover,
.unified-button-secondary:hover,
.ai-button-secondary:hover {
  box-shadow:
    0 0 10px rgba(var(--color-primary-rgb), 0.2),
    0 0 20px rgba(var(--color-primary-rgb), 0.1);
}

.button-ripple-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle,
      rgba(255, 255, 255, 0.7) 0%,
      rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.01s linear, height 0.01s linear;
  z-index: -1;
  opacity: 0;
}

.button-ripple-effect:active::before {
  width: 300%;
  height: 300%;
  opacity: 1;
  transition: all 0.5s ease-out;
}

.optimized-button::after,
.unified-button::after,
.ai-button-primary::after,
.ai-button-secondary::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.4) 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.4s, opacity 0.8s;
}

.optimized-button:active::after,
.unified-button:active::after,
.ai-button-primary:active::after,
.ai-button-secondary:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

.optimized-button::before,
.unified-button::before,
.ai-button-primary::before,
.ai-button-secondary::before {
  content: '';
  position: absolute;
  inset: -3px;
  z-index: -1;
  background: linear-gradient(135deg,
      var(--color-primary-400),
      var(--color-blue-400),
      var(--color-primary-400));
  opacity: 0;
  border-radius: inherit;
  transition: opacity 0.4s ease;
}

.optimized-button:hover::before,
.unified-button:hover::before,
.ai-button-primary:hover::before,
.ai-button-secondary:hover::before {
  opacity: 0.6;
  animation: rotate-gradient 3s linear infinite;
}

@keyframes button-pulse {
  0% {
    box-shadow:
      0 0 15px rgba(var(--color-primary-rgb), 0.5),
      0 0 30px rgba(var(--color-primary-rgb), 0.2);
  }

  100% {
    box-shadow:
      0 0 20px rgba(var(--color-primary-rgb), 0.7),
      0 0 40px rgba(var(--color-primary-rgb), 0.4);
  }
}

@keyframes blue-button-pulse {
  0% {
    box-shadow:
      0 0 15px rgba(var(--color-blue-rgb), 0.5),
      0 0 30px rgba(var(--color-blue-rgb), 0.2);
  }

  100% {
    box-shadow:
      0 0 20px rgba(var(--color-blue-rgb), 0.7),
      0 0 40px rgba(var(--color-blue-rgb), 0.4);
  }
}

@keyframes rotate-gradient {
  0% {
    background-position: 0% 50%;
    background-size: 200% 200%;
  }

  50% {
    background-position: 100% 50%;
    background-size: 200% 200%;
  }

  100% {
    background-position: 0% 50%;
    background-size: 200% 200%;
  }
}

.optimized-button-primary,
.unified-button-primary,
.ai-button-primary {
  box-shadow: 0 4px 15px -3px rgba(var(--color-primary-rgb), 0.5);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.optimized-button:active,
.unified-button:active,
.ai-button-primary:active,
.ai-button-secondary:active {
  transform: translateY(2px) scale(0.97);
  transition: all 0.1s ease;
}

.ai-button-primary:active,
.ai-button-secondary:active {
  transform: translateY(2px) scale(0.97);
  transition: all 0.1s ease;
}

.btn-ripple::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.4) 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.4s, opacity 0.8s;
}

.btn-ripple:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

.btn-glow:hover {
  box-shadow:
    0 0 15px rgba(var(--color-primary-rgb), 0.5),
    0 0 30px rgba(var(--color-primary-rgb), 0.2);
  transform: translateY(-2px) scale(1.02);
}

.btn-enhanced:hover {
  transform: translateY(-2px);
  box-shadow:
    0 0 15px rgba(var(--color-primary-rgb), 0.5),
    0 0 30px rgba(var(--color-primary-rgb), 0.2);
}

.btn-enhanced:active {
  transform: translateY(2px) scale(0.97);
  transition: transform 0.1s ease;
}

.btn-enhanced::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.4) 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.4s, opacity 0.8s;
}

.btn-enhanced:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

@keyframes drawerSlideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes drawerSlideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes leftDrawerSlideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes leftDrawerSlideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

.enhanced-drawer {
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  z-index: 40;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 25px rgba(0, 0, 0, 0.15);
  background-color: var(--color-gray-50);
  border-radius: 0;
}

.enhanced-drawer-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

@media (max-width: 1280px) {
  .app-layout {
    grid-template-columns: 1fr 4fr 1fr;
  }
}

@media (max-width: 1024px) {
  .app-layout {
    grid-template-columns: 1fr 5fr 1fr;
  }
}

.enhanced-drawer-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.glass-card {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);

  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(31, 38, 135, 0.12),
    0 2px 8px rgba(31, 38, 135, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 20px;
}

.glass-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 12px 40px rgba(31, 38, 135, 0.15),
    0 4px 12px rgba(31, 38, 135, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  border-color: rgba(35, 146, 239, 0.3);
}

.glass-card-blue {
  background: var(--color-success-highlight);
  border: 1px solid rgba(100, 181, 246, 0.25);
}

.glass-card-gold {
  background: var(--color-error-highlight);
  border: 1px solid rgba(212, 175, 55, 0.25);
}

.btn-authority {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  border-radius: 8px;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  min-height: 44px;
  box-sizing: border-box;
}

.btn-authority::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
  transition: left 0.5s;
}

.btn-authority:hover::before {
  left: 100%;
}

.btn-primary-gold:hover::after {
  left: 150%;
}

.btn-authority.btn-primary-gold>*,
.btn-primary-gold>* {
  position: relative;
  z-index: 2;
}

.settings-drawer-content .btn-primary-gold {
  background: linear-gradient(135deg,
      var(--color-gold-400) 0%,
      var(--color-gold-500) 30%,
      var(--color-gold-600) 70%,
      var(--color-gold-700) 100%) !important;

  box-shadow:
    0 4px 16px rgba(255, 237, 127, 0.2),
    0 2px 8px rgba(255, 242, 168, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(255, 229, 86, 0.1) !important;
}

.settings-drawer-content .btn-primary-gold:hover {
  box-shadow:
    0 8px 32px rgba(255, 237, 127, 0.3),
    0 4px 16px rgba(255, 242, 168, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    inset 0 -1px 0 rgba(255, 229, 86, 0.15) !important;
}

.btn-secondary-glass {

  background: rgba(255, 255, 255, 0.9) !important;
  color: var(--color-primary-600) !important;
  border: 1px solid rgba(35, 146, 239, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;

  box-shadow: 0 2px 6px rgba(35, 146, 239, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
}

.btn-secondary-glass:hover {
  background: rgba(255, 255, 255, 0.95) !important;
  color: var(--color-primary-700) !important;
  border-color: rgba(35, 146, 239, 0.4) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(35, 146, 239, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.7) !important;
}

.btn-secondary-glass:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(35, 146, 239, 0.2),
    inset 0 2px 4px rgba(35, 146, 239, 0.1) !important;
}

.settings-drawer-content .btn-secondary-glass {
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(248, 250, 252, 0.9) 50%,
      rgba(241, 245, 249, 0.85) 100%) !important;

  border: 1px solid rgba(59, 130, 246, 0.25) !important;

  box-shadow:
    0 4px 16px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(59, 130, 246, 0.1) !important;
}

.settings-drawer-content .btn-secondary-glass:hover {
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 1) 0%,
      rgba(248, 250, 252, 0.95) 50%,
      rgba(241, 245, 249, 0.9) 100%) !important;

  border-color: rgba(59, 130, 246, 0.4) !important;

  box-shadow:
    0 8px 24px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    inset 0 -1px 0 rgba(59, 130, 246, 0.15) !important;
}

.btn-authority .icon,
.btn-authority svg,
.btn-authority [class*="icon"] {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 768px) {
  .btn-authority {
    padding: 10px 16px;
    font-size: 13px;
    gap: 6px;
    min-height: 40px;
  }

  .btn-authority .icon,
  .btn-authority svg,
  .btn-authority [class*="icon"] {
    width: 14px;
    height: 14px;
  }

  .history-action-btn .icon,
  .history-action-btn svg,
  .history-action-btn [class*="icon"] {
    width: 14px;
    height: 14px;
  }

  .status-indicator .icon,
  .status-indicator svg,
  .status-indicator [class*="icon"] {
    width: 12px;
    height: 12px;
  }
}

.icon,
svg,
[class*="icon"] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  stroke-width: 2;
  fill: none;
}

.icon-primary {
  color: var(--color-primary-500);
}

.icon-secondary {
  color: var(--color-blue-500);
}

.icon-success {
  color: var(--color-success);
}

.icon-warning {
  color: var(--color-warning);
}

.icon-error {
  color: var(--color-error);
}

.icon-processing {
  color: var(--color-processing);
}

.icon-neutral {
  color: var(--color-gray-500);
}

.icon-white {
  color: var(--color-white);
}

.icon-container-primary {
  color: var(--color-primary-600);
}

.layout-console .icon-success {
  color: var(--color-success);
}

.layout-console .icon-warning {
  color: var(--color-warning);
}

.layout-console .icon-error {
  color: var(--color-error);
}

.drawer-container .icon-primary,
.enhanced-drawer-container .icon-primary,
.drawer-header .icon-primary {
  color: var(--color-purple-500);
  filter: drop-shadow(0 1px 2px rgba(139, 92, 246, 0.1));
}

.drawer-container .icon-secondary,
.enhanced-drawer-container .icon-secondary {
  color: var(--color-purple-400);
}

.history-drawer .icon-primary {
  color: var(--color-blue-500);
}

.prompt-drawer .icon-primary {
  color: var(--color-purple-600);
}

.settings-drawer-content .icon-primary {
  color: var(--color-blue-600);
}

.settings-drawer-content .icon-accent {
  color: var(--color-gold-600);
}

.settings-drawer-content .icon-download {
  color: var(--color-blue-500);
}

.settings-drawer-content .icon-upload {
  color: var(--color-purple-500);
}

.monitoring-panel .icon-success {
  color: var(--color-success);
}

.monitoring-panel .icon-warning {
  color: var(--color-warning);
}

.monitoring-panel .icon-error {
  color: var(--color-error);
}

.monitoring-panel .icon-processing {
  color: var(--color-processing);
}

.query-input-panel .icon-primary {
  color: var(--color-primary-500);
}

.query-input-panel .icon-accent {
  color: var(--color-gold-400);
}

.results-panel .icon-success {
  color: var(--color-success);
}

.results-panel .icon-primary {
  color: var(--color-primary-500);
}

.layout-main .status-success .icon,
.layout-sidebar .status-success .icon {
  color: var(--color-success) !important;
}

.icon-processing,
[class*="processing"] .icon {
  color: var(--color-processing) !important;
}

.icon-error,
[class*="error"] .icon {
  color: var(--color-error) !important;
}

.icon-warning,
[class*="warning"] .icon {
  color: var(--color-warning) !important;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes processingPulse {

  0%,
  100% {
    opacity: 0.8;
    transform: rotate(0deg) scale(1);
  }

  50% {
    opacity: 1;
    transform: rotate(180deg) scale(1.05);
  }
}

.circular-progress .progress-ring {
  fill: none;
  stroke: var(--color-gray-200);
  stroke-width: 4;
}

.circular-progress .progress-bar {
  fill: none;
  stroke: var(--color-primary-500);
  stroke-width: 4;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.5s ease-in-out;
}

.circular-progress .progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--color-gray-700);
}

.status-indicator {
  display: flex !important;
  align-items: center !important;
  gap: 10px !important;
  padding: 12px 16px !important;
  border-radius: 10px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  border: 1px solid transparent !important;
  transition: all 0.3s ease !important;
  background: var(--color-success-light) !important;
  position: relative;
  overflow: hidden;
}

.status-success {
  color: var(--color-success) !important;
  border-color: rgba(100, 181, 246, 0.2) !important;
  background: var(--color-success-gradient) !important;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.2) 0%,
      transparent 50%,
      rgba(255, 255, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.status-indicator:hover::before {
  opacity: 1;
}

.status-indicator .icon,
.status-indicator svg {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.status-indicator:hover .icon,
.status-indicator:hover svg {
  transform: scale(1.1) rotate(5deg);
}

.status-success .w-5.h-5.rounded-full {
  box-shadow: 0 0 0 4px rgba(72, 187, 120, 0.1);
  animation: pulse-success 2s infinite;
}

@keyframes pulse-success {
  0% {
    box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.4);
  }

  70% {
    box-shadow: 0 0 0 6px rgba(72, 187, 120, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(72, 187, 120, 0);
  }
}

.status-warning {
  background: var(--color-warning-light);
  color: var(--color-warning);
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-error {
  background: var(--color-error-light);
  color: var(--color-error);
  border: 1px solid rgba(212, 175, 55, 0.3);
}

.status-processing {
  background: var(--color-processing-light);
  color: var(--color-processing);
  border: 1px solid rgba(107, 114, 128, 0.3);
}

.tooltip .tooltip-content {
  visibility: hidden;
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(15, 23, 42, 0.95);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

}

.tooltip .tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(15, 23, 42, 0.95);
}

.tooltip:hover .tooltip-content {
  visibility: visible;
  opacity: 1;
}

.quick-tool-btn {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  gap: 0.75rem !important;
  padding: 0.875rem 1.25rem !important;

  text-align: left !important;
  min-height: 44px !important;
  width: 100% !important;
}

.quick-tool-btn .icon,
.quick-tool-btn svg {
  width: 16px !important;
  height: 16px !important;
  flex-shrink: 0 !important;
  margin: 0 !important;
}

.quick-tool-btn span {
  flex: 1 !important;
  text-align: left !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

@media (max-width: 1440px) {
  .quick-tool-btn {
    padding: 0.75rem 1.125rem !important;

    gap: 0.625rem !important;
    min-height: 40px !important;
    font-size: 0.8125rem !important;
  }

  .quick-tool-btn .icon,
  .quick-tool-btn svg {
    width: 14px !important;
    height: 14px !important;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.sparkle-hover {
  position: relative;
  overflow: hidden;
}

.sparkle-hover::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.6) 50%,
      transparent 70%);
  animation: sparkleMove 3s ease-in-out infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.sparkle-hover:hover::before {
  opacity: 1;
}

.sparkle-hover::after {
  content: '✨';
  position: absolute;
  top: 50%;
  right: 10%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  opacity: 0;
  animation: sparkleFloat 2s ease-in-out infinite;
  pointer-events: none;
}

.sparkle-hover:hover::after {
  opacity: 1;
}

@keyframes sparkleMove {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(0deg);
  }

  50% {
    transform: translateX(100%) translateY(100%) rotate(180deg);
  }

  100% {
    transform: translateX(-100%) translateY(-100%) rotate(360deg);
  }
}

@keyframes sparkleFloat {

  0%,
  100% {
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }

  50% {
    transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
  }
}

@keyframes textGlow {
  0% {
    text-shadow:
      0 0 5px rgba(255, 255, 255, 0.8),
      0 0 10px rgba(100, 181, 246, 0.6),
      0 0 15px rgba(100, 181, 246, 0.4),
      0 0 20px rgba(100, 181, 246, 0.2);
  }

  100% {
    text-shadow:
      0 0 8px rgba(255, 255, 255, 1),
      0 0 15px rgba(100, 181, 246, 0.8),
      0 0 25px rgba(100, 181, 246, 0.6),
      0 0 35px rgba(100, 181, 246, 0.4);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

@media (max-width: 768px) {

  .glass-card {
    border-radius: 12px;
  }

  .btn-authority {
    padding: 0.625rem 1.25rem;
    font-size: 0.8125rem;
  }
}

.form-input-modern {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--color-gray-300);
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.form-input-modern:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(35, 146, 239, 0.1);
}

.form-input-gold:focus {
  border-color: var(--color-gold-500);
  box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
}

.typography-authority-title .subtitle {
  display: block;
  font-size: 1rem;
  font-weight: 500;
  margin-top: 0.5rem;
  color: rgba(107, 114, 128, 0.9);
  -webkit-text-fill-color: rgba(107, 114, 128, 0.9);

  text-shadow: none;
  letter-spacing: normal;
}

.auto-fit-title {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  overflow: visible;
}

.auto-fit-text {
  display: inline-block;
  white-space: nowrap;
  font-size: clamp(0.8rem, 4vw + 0.2rem, 2.25rem);
  line-height: 1.2;
  letter-spacing: clamp(-0.02em, -0.5vw + 0.01em, -0.025em);
  width: auto;
  min-width: 0;
  max-width: none;
  overflow: visible;
  font-weight: inherit;
  transition: all 0.3s ease;
}

@media (max-width: 768px) {
  .auto-fit-text {
    font-size: clamp(0.7rem, 5vw + 0.1rem, 1.875rem);
    letter-spacing: clamp(-0.015em, -0.3vw + 0.005em, -0.02em);
  }
}

@media (max-width: 480px) {
  .auto-fit-text {
    font-size: clamp(0.6rem, 6vw + 0.05rem, 1.5rem);
    letter-spacing: clamp(-0.01em, -0.2vw + 0.002em, -0.015em);
  }
}

@media (max-width: 320px) {
  .auto-fit-text {
    font-size: clamp(0.5rem, 8vw + 0.02rem, 1.25rem);
    letter-spacing: normal;
  }
}

@media (min-width: 1200px) {
  .auto-fit-text {
    font-size: clamp(2rem, 3vw + 1rem, 2.5rem);
    letter-spacing: clamp(-0.03em, -0.3vw - 0.01em, -0.025em);
  }
}

@media (min-width: 1600px) {
  .auto-fit-text {
    font-size: clamp(2.25rem, 2.5vw + 1.2rem, 2.75rem);
    letter-spacing: clamp(-0.035em, -0.2vw - 0.015em, -0.03em);
  }
}

.layout-sidebar .auto-fit-title {
  width: 100%;
  max-width: none;
}

.layout-sidebar .auto-fit-text {

  font-size: clamp(0.6rem, 4vw + 0.2rem, 1.8rem);
  text-align: left;
  width: auto;
  max-width: none;
}

@media (max-width: 280px) {
  .auto-fit-text::after {
    content: "LYNX 生成器";
    font-size: clamp(0.7rem, 10vw, 1rem);
    display: inline-block;
    white-space: nowrap;
    background: inherit;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .auto-fit-text {
    font-size: 0;
  }

  .gradient-text .auto-fit-text::after {
    background: inherit;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.auto-fit-text {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-variant-ligatures: common-ligatures;
  font-feature-settings: "kern" 1, "liga" 1;
}

.gradient-text .auto-fit-text {
  background: linear-gradient(135deg,
      #2392ef 0%,

      #3a7bd5 25%,

      #00d2ff 50%,

      var(--color-gold-500) 75%,

      #d4af37 100%

    );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0px 2px 10px rgba(35, 146, 239, 0.2);
  animation: gradientShift 8s ease infinite;
  background-size: 200% auto;
}

.auto-fit-text {
  transition:
    font-size 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    letter-spacing 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 0.2s ease;
}

.auto-fit-title:hover .auto-fit-text {
  transform: scale(1.02);
}

.auto-fit-title:focus-within .auto-fit-text {
  transform: scale(1.01);
  outline: none;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.btn-authority.btn-primary-gold,
.btn-authority.btn-primary-glass,
.btn-primary-gold,
.btn-primary-glass,
.w-full.h-14.btn-authority.btn-primary-gold,
.w-full.h-14.btn-authority.btn-primary-glass {

  background: linear-gradient(135deg,
      #ffd700 0%,

      #facc15 40%,

      #eab308 70%,

      #d4af37 100%

    ) !important;
  color: white !important;
  border: 1px solid rgba(234, 179, 8, 0.5) !important;
  border-radius: 8px !important;
  padding: 12px 20px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  box-shadow:
    0 4px 12px rgba(234, 179, 8, 0.3),
    0 2px 6px rgba(251, 191, 36, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(180, 83, 9, 0.1) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15) !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  letter-spacing: 0.5px !important;

  width: 100% !important;
  min-height: 3.5rem !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.btn-authority.btn-primary-gold:hover,
.btn-authority.btn-primary-glass:hover,
.btn-primary-gold:hover,
.btn-primary-glass:hover,
.w-full.h-14.btn-authority.btn-primary-gold:hover,
.w-full.h-14.btn-authority.btn-primary-glass:hover {
  background: linear-gradient(135deg,
      #ffdd33 0%,

      #fbd235 40%,

      #f59e0b 70%,

      #d97706 100%

    ) !important;
  border-color: rgba(245, 158, 11, 0.6) !important;
  transform: translateY(-3px) !important;

  box-shadow:
    0 8px 24px rgba(234, 179, 8, 0.4),
    0 4px 12px rgba(251, 191, 36, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
}

.btn-authority.btn-primary-gold::after,
.btn-authority.btn-primary-glass::after,
.btn-primary-gold::after,
.btn-primary-glass::after,
.w-full.h-14.btn-authority.btn-primary-gold::after,
.w-full.h-14.btn-authority.btn-primary-glass::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 80%;

  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.4),

      transparent);
  transform: skewX(-20deg);
  transition: left 0.7s ease-in-out;
  z-index: 1;
}

.btn-authority.btn-primary-gold:hover::after,
.btn-authority.btn-primary-glass:hover::after,
.btn-primary-gold:hover::after,
.btn-primary-glass:hover::after,
.w-full.h-14.btn-authority.btn-primary-gold:hover::after,
.w-full.h-14.btn-authority.btn-primary-glass:hover::after {
  left: 150%;
  transition: left 0.7s ease-in-out;
}

.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.progress-error {
  background: linear-gradient(135deg,
      var(--color-error) 0%,
      var(--color-warning) 50%,
      var(--color-error-light) 100%);
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.25);
  opacity: 0.8;
}

.progress-active {
  position: relative;
}

.progress-active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.4) 50%,
      transparent 100%);
  animation: shimmer 2s infinite;
}

.shimmer-highlight::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.6) 50%,
      transparent 100%);
  animation: shimmer 3s infinite;
}

.breathing-glow {
  animation: breathingGlow 3s ease-in-out infinite;
}

@keyframes breathingGlow {

  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5), 0 0 40px rgba(96, 165, 250, 0.25);
  }
}

.status-success-button {
  background: var(--color-success-gradient) !important;
  color: white !important;
  font-weight: 600;
}

.status-neutral-gradient {
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
}

.status-success-gradient {
  background: var(--color-success-highlight);
}

.status-error-gradient {
  background: var(--color-error-highlight);
}

.elastic-click {
  transition: transform 0.1s ease;
}

.elastic-click:active {
  transform: scale(0.95);
}

.liquid-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.liquid-ripple:hover::after {
  width: 100%;
  height: 100%;
}

.light-scan::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.light-scan:hover::before {
  left: 100%;
}

.magnetic-hover {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.magnetic-hover:hover {
  transform: translateY(-2px) scale(1.02);
}

.btn-authority.btn-primary-gold>*,
.btn-primary-gold>* {
  position: relative;
  z-index: 2;
}

@keyframes rocketFloat {

  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }

    50% {
      transform: translateY(-4px) rotate(5deg);
    }
}

@keyframes rocket-shake {

  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

button.btn-authority.btn-primary-gold,
button.btn-authority.btn-primary-glass,
.btn-authority.btn-primary-gold.w-full.h-14,
.btn-authority.btn-primary-glass,
div .btn-authority.btn-primary-gold,
div .btn-authority.btn-primary-glass {
  background: linear-gradient(135deg, #ffd700 0%, #facc15 40%, #eab308 70%, #d4af37 100%) !important;
  color: white !important;
  border: 1px solid rgba(234, 179, 8, 0.5) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(234, 179, 8, 0.3), 0 2px 6px rgba(251, 191, 36, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.4), inset 0 -1px 0 rgba(180, 83, 9, 0.1) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15) !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  letter-spacing: 0.5px !important;
}

button.btn-authority.btn-primary-gold:hover,
button.btn-authority.btn-primary-glass:hover,
.btn-authority.btn-primary-gold.w-full.h-14:hover,
.btn-authority.btn-primary-glass:hover,
div .btn-authority.btn-primary-gold:hover,
div .btn-authority.btn-primary-glass:hover {
  background: linear-gradient(135deg, #ffdd33 0%, #fbd235 40%, #f59e0b 70%, #d97706 100%) !important;
  border-color: rgba(245, 158, 11, 0.6) !important;
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 24px rgba(234, 179, 8, 0.4), 0 4px 12px rgba(251, 191, 36, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
}
