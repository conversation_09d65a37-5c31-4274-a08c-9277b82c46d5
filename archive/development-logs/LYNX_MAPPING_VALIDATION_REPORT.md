# Lynx映射规则验证报告

## 概述

本报告对比验证了pePromptLoader.ts中的Lynx映射规则与@byted-lynx/web-speedy-plugin的实际实现是否一致，重点检查了核心元素映射、事件系统映射、属性转换规则、RPX单位转换规则和条件渲染/循环语法。

## 验证结果总结

### ✅ 一致性良好的映射规则

#### 1. 核心元素映射
- **view → div**: 两个文件都正确映射为div，className为lynx-view
- **text → span**: 一致映射为span，className为lynx-text  
- **image → img**: 一致映射为img，支持src、mode、lazy-load等属性
- **input → input**: 保持原标签，支持完整的属性映射
- **scroll-view → div**: 正确映射为可滚动的div容器

#### 2. 事件系统映射
- **bindtap → onClick**: 冒泡事件正确映射
- **catch:tap → onClick**: 捕获事件正确映射，stopPropagation设为true
- **bindinput → onInput**: 表单事件正确映射
- **bindscroll → onScroll**: 滚动事件正确映射
- **bindtouchstart/touchmove/touchend**: 触摸事件系列正确映射

#### 3. 属性转换规则
- **class → className**: React标准属性转换
- **hover-class → data-hover-class**: Lynx特有属性转换
- **maxlength → maxLength**: 驼峰命名转换
- **for → htmlFor**: React标准转换

#### 4. RPX单位转换规则
- **设计稿宽度**: 两个文件都使用750px作为标准设计稿宽度
- **VW模式**: 100rpx → 13.333333vw (100/750*100)，公式一致
- **REM模式**: 100rpx → 2.666667rem (100/37.5)，公式一致
- **默认模式**: 都推荐使用VW模式作为默认转换

#### 5. 条件渲染和循环语法
- **tt:if/lx:if**: 两个文件都支持条件渲染指令
- **tt:for/lx:for**: 循环渲染指令语法一致
- **tt:key/lx:key**: React key属性映射一致

### ⚠️ 发现的不一致或缺失

#### 1. 元素映射覆盖度差异
**runtime_convert_parse5/mappings/index.ts** 拥有更完整的元素映射：
- ✅ 包含更多高级组件：movable-area、movable-view、live-player、live-pusher、camera、map等
- ✅ 包含降级映射机制：FALLBACK_ELEMENT_MAPPING
- ✅ 包含自闭合标签集合：SELF_CLOSING_TAGS

**pePromptLoader.ts** 相对简化：
- ⚠️ 缺少高级企业级组件的详细说明
- ⚠️ 没有明确的降级处理机制

#### 2. 事件映射完整性
**runtime_convert_parse5/mappings/index.ts** 更加完整：
- ✅ 包含appear/disappear特殊事件映射
- ✅ 包含动画相关事件：animationstart、animationend等
- ✅ 包含媒体事件：play、pause、timeupdate等

**pePromptLoader.ts** 相对基础：
- ⚠️ 主要关注核心交互事件
- ⚠️ 缺少专业多媒体和动画事件

#### 3. RPX转换模式支持
**runtime_convert_parse5/mappings/index.ts** 支持完整的RpxMode枚举：
```typescript
enum RpxMode {
  VW = 'vw',
  REM = 'rem', 
  PX = 'px',
  CALC = 'calc'
}
```

**pePromptLoader.ts** 提到了所有模式但没有枚举定义：
- ⚠️ 缺少RpxMode类型定义
- ⚠️ 缺少TTSSConversionConfig接口

#### 4. 属性映射细节
**runtime_convert_parse5/mappings/index.ts** 更加细致：
- ✅ 详细的attributeMapping配置
- ✅ 通用属性映射表：COMMON_ATTRIBUTE_MAPPING
- ✅ data-*和aria-*属性保留规则

**pePromptLoader.ts** 相对宏观：
- ⚠️ 属性转换规则描述不够细致
- ⚠️ 缺少边界情况处理

## 核心必须保留的映射规则

### 1. 基础元素映射（P0优先级）
```typescript
const CORE_ELEMENT_MAPPING = {
  view: { tag: 'div', className: 'lynx-view' },
  text: { tag: 'span', className: 'lynx-text' },
  image: { tag: 'img', className: 'lynx-image', selfClosing: true },
  input: { tag: 'input', className: 'lynx-input', selfClosing: true },
  button: { tag: 'button', className: 'lynx-button' },
  'scroll-view': { tag: 'div', className: 'lynx-scroll-view' }
};
```

### 2. 核心事件映射（P0优先级）
```typescript
const CORE_EVENT_MAPPING = {
  'bindtap': { reactEvent: 'onClick', stopPropagation: false },
  'catch:tap': { reactEvent: 'onClick', stopPropagation: true },
  'bindinput': { reactEvent: 'onInput', stopPropagation: false },
  'bindchange': { reactEvent: 'onChange', stopPropagation: false },
  'bindscroll': { reactEvent: 'onScroll', stopPropagation: false }
};
```

### 3. 核心属性转换（P0优先级）
```typescript
const CORE_ATTRIBUTE_MAPPING = {
  class: 'className',
  'hover-class': 'data-hover-class',
  maxlength: 'maxLength',
  for: 'htmlFor',
  style: 'style',
  id: 'id'
};
```

### 4. RPX转换规则（P0优先级）
```typescript
const RPX_CONVERSION = {
  designWidth: 750,
  defaultMode: 'vw',
  convertRpxToVw: (rpx) => `${((rpx / 750) * 100).toFixed(6)}vw`
};
```

### 5. 条件渲染和循环（P0优先级）
```typescript
const CORE_DIRECTIVES = {
  'tt:if': 'conditional',
  'tt:for': 'iteration', 
  'tt:key': 'optimization',
  'lx:if': 'conditional',
  'lx:for': 'iteration',
  'lx:key': 'optimization'
};
```

## 建议修复方案

### 1. 统一映射规则标准
- 以 **runtime_convert_parse5/mappings/index.ts** 为权威参考
- 在pePromptLoader.ts中补充缺失的高级组件说明
- 添加降级处理机制的描述

### 2. 完善事件映射
- 补充appear/disappear等特殊事件的说明
- 添加动画和多媒体事件的支持描述
- 明确事件冒泡和捕获的处理机制

### 3. 增强RPX转换说明
- 添加RpxMode枚举类型的描述
- 补充TTSSConversionConfig配置的说明
- 明确各种转换模式的适用场景

### 4. 细化属性映射
- 补充COMMON_ATTRIBUTE_MAPPING的详细说明
- 明确data-*和aria-*属性的处理规则
- 添加边界情况的处理说明

## 结论

总体而言，pePromptLoader.ts中的核心Lynx映射规则与@byted-lynx/web-speedy-plugin的实现基本一致，但在完整性和细节方面有所欠缺。**runtime_convert_parse5/mappings/index.ts** 提供了更完整和规范的映射实现，应该作为标准参考。

建议保持现有的核心映射规则不变，同时补充高级功能和边界情况的处理，以确保与web-speedy-plugin的完全兼容性。

---

**验证时间**: 2025-06-28  
**验证文件**: 
- `/src/routes/batch_processor/utils/pePromptLoader.ts`
- `/src/routes/batch_processor/runtime_convert_parse5/mappings/index.ts`  
- `/src/routes/batch_processor/deleted_lynx2web/docs/@byted-lynx-web-speedy-plugin完整映射表.md`