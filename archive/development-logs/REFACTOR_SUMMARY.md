# Prompt 系统重构总结

## 🎯 重构目标

1. **简化维护**：将复杂的 PE 变量系统替换为简单的 MD 文件
2. **统一规范**：分别维护 Web 和 Lynx 的完整规范
3. **增强转换**：为 Lynx convert 模式添加明确的转换说明
4. **Canvas 选项**：在调用处明确说明 Canvas 使用策略

## 📁 新的文件结构

```
RPC/
├── Web/
│   ├── WebPrompt.md          # Web 完整开发规范
│   ├── WebRPCCore.ts         # Web RPC 核心逻辑
│   └── WebRPCManager.ts      # Web RPC 管理器
├── Lynx/
│   ├── LynxPrompt.md         # Lynx 完整开发规范
│   ├── LynxRPCCore.ts        # Lynx RPC 核心逻辑
│   └── README.md             # Lynx 说明文档
├── PromptComposer.ts         # 重构后的提示词组合器
└── README.md                 # 主要说明文档
```

## 🔧 核心函数变更

### 1. Web 系统提示词生成
```typescript
generateWebSystemPrompt(useCanvasGeneration: boolean): string
```
- **变更**：直接读取 `Web/WebPrompt.md` 文件
- **新增**：Canvas 选项说明（鼓励使用 vs 限制使用）
- **移除**：复杂的变量组合逻辑

### 2. Lynx 系统提示词生成
```typescript
generateLynxSystemPrompt(useCanvasGeneration: boolean): string
```
- **变更**：直接读取 `Lynx/LynxPrompt.md` 文件
- **新增**：Canvas 选项说明（鼓励使用 vs 限制使用）
- **移除**：复杂的变量组合逻辑

### 3. Lynx 转换模式（新增）
```typescript
generateLynxConvertSystemPrompt(useCanvasGeneration: boolean, webCode: string): string
```
- **功能**：专门用于 Web 到 Lynx 的代码转换
- **特点**：在 Lynx 规范前添加转换说明
- **包含**：Canvas 选项说明 + 转换要求 + Web 代码 + Lynx 规范

### 4. Lynx 转换提示词生成（更新）
```typescript
generateLynxConversionPrompt(webCode: string, useCanvasGeneration: boolean): string
```
- **变更**：使用新的转换模式函数
- **新增**：Canvas 选项参数
- **简化**：直接返回完整的转换提示词

## 🎨 Canvas 选项说明

### Canvas 模式已启用
- **Web**：鼓励使用 HTML5 Canvas 实现复杂视觉效果
- **Lynx**：鼓励使用 lynx.krypton.createCanvasNG() 等 API
- **适用场景**：数据可视化、游戏、复杂动画

### Canvas 模式限制
- **Web**：优先使用 HTML/CSS 实现常规功能
- **Lynx**：优先使用 Lynx 原生组件
- **适用场景**：常规界面、表单、列表

## 🔄 转换模式特点

### 转换要求前缀
```
🔄 重要：这是一个代码转换任务，不是从零开始创建！

转换要求：
- 严格按照提供的原始代码结构和功能进行转换
- 不要擅自发挥或添加原始代码中没有的功能
- 保持原有的交互逻辑和视觉效果
- 确保转换后的代码功能完整，与原始代码等效
```

### 转换核心原则
1. **忠实转换**：严格按照原始代码结构
2. **功能对等**：确保功能完全一致
3. **移动端适配**：优化移动端体验
4. **不要创新**：不添加新功能
5. **保持简洁**：简单代码保持简洁

## 📝 MD 文件内容

### WebPrompt.md
- Web 完整开发规范
- HTML/CSS/JavaScript 最佳实践
- Canvas 渲染核心
- 移动端优化
- 文案复制功能
- 文字布局优化
- 存在性检查指导

### LynxPrompt.md
- Lynx 完整开发规范
- 移动端框架核心规则
- JSBridge 通信核心
- Canvas 生命周期管理
- Template API 详细使用
- 算法包集成规范
- 事件处理系统
- 性能优化与内存管理

## 🗑️ 已删除的文件

### PE 变量文件
- PromptVariables.ts
- TextLayoutPE.ts
- VisualEffectsPE.ts
- VisualAestheticsPE.ts
- 所有其他 *PE.ts 文件

### 过时文档
- 各种分析和优化报告
- 旧的架构文档
- 使用示例文件
- 测试文件

## 🚀 使用方法

### Web 代码生成
```typescript
const webPrompt = generateWebSystemPrompt(true); // Canvas 模式
const webPrompt = generateWebSystemPrompt(false); // 限制模式
```

### Lynx 代码生成
```typescript
const lynxPrompt = generateLynxSystemPrompt(true); // Canvas 模式
const lynxPrompt = generateLynxSystemPrompt(false); // 限制模式
```

### Lynx 代码转换
```typescript
const convertPrompt = generateLynxConversionPrompt(webCode, true); // Canvas 模式转换
const convertPrompt = generateLynxConversionPrompt(webCode, false); // 限制模式转换
```

## ✅ 重构收益

1. **维护简化**：只需维护 2 个 MD 文件，不再需要复杂的变量系统
2. **规范统一**：每个平台有完整的开发规范
3. **转换明确**：转换模式有明确的说明和要求
4. **Canvas 清晰**：调用处明确 Canvas 使用策略
5. **代码精简**：删除了大量过时和冗余的文件
6. **功能保留**：续传功能和智能分析功能完整保留

## 📋 已整合到 MD 文件的内容

### WebPrompt.md 新增内容：
- Canvas选项级视觉美学系统
- AI智能配色系统指导
- 动画效果系统（4秒循环）
- 毛玻璃与层次系统
- 交互反馈系统
- 网格与排版系统
- 动画增强指导
- 高级文字布局优化

### LynxPrompt.md 新增内容：
- Canvas选项级移动端视觉美学系统
- 移动端配色选择原则
- 移动端动画效果系统
- 移动端交互反馈系统
- 移动端网格与排版系统
- 移动端动画增强指导
- 移动端高级文字布局优化

## 🔧 保留的动态功能

以下功能因为需要动态生成，所以保留在 PromptComposer.ts 中：

1. **续传功能**：
   - `generateWebContinuationPrompt()` - Web 续传
   - `generateLynxContinuationPrompt()` - Lynx 续传

2. **智能分析功能**：
   - `analyzeUserIntent()` - 用户意图分析
   - `generatePremiumVisualPrompt()` - 顶级视觉设计
   - `generateIntelligentPrompt()` - 智能提示词生成

3. **动态组合功能**：
   - `generateCanvasOptionStylePrompt()` - 简化版，添加用户需求
   - `generateAnimationEnhancedPrompt()` - 简化版，添加动态内容
   - `generateStunningVisualPrompt()` - 简化版，添加动态内容
   - `generateTextLayoutOptimizedPrompt()` - 简化版，添加动态内容

## 🔮 后续维护

- **更新规范**：直接编辑对应的 MD 文件
- **添加功能**：在 MD 文件中添加新的规范说明
- **修复问题**：在 MD 文件中修正错误的规范
- **版本控制**：MD 文件的变更可以通过 Git 跟踪
- **动态功能**：续传和智能分析功能在 PromptComposer.ts 中维护
