<iframe srcdoc="<!DOCTYPE html>
<html lang=&quot;zh-CN&quot;>
<head>
  <meta charset=&quot;UTF-8&quot;>
  <meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;>
  <title>TTML预览 - 简化模式</title>
  <style>
    /* 基础样式重置 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
      background: #f8fafc;
      overflow-x: hidden;
      font-size: 14px;
      line-height: 1.6;
    }

    /* 作用域化CSS - 保持原始CSS完整性 */
    .container[data-v-wjn8r] {
  max-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 0 5.333333vw 0;
}
.header-section[data-v-wjn8r] {
  padding: 8.000000vw 5.333333vw 5.333333vw 5.333333vw;
}
.title-card[data-v-wjn8r] {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 3.200000vw;
  padding: 5.333333vw;
}
.icon-text[data-v-wjn8r] {
  margin-right: 3.200000vw;
  color: #667eea;
}
.title-content[data-v-wjn8r] {
  flex: 1;
}
.main-title[data-v-wjn8r] {
  font-size: 6.400000vw;
  font-weight: bold;
  color: #2d3748;
  line-height: 1.2;
  display: block;
  margin-bottom: 1.066667vw;
}
.subtitle[data-v-wjn8r] {
  font-size: 3.733333vw;
  color: #718096;
  display: block;
}
.section-title[data-v-wjn8r] {
  font-size: 4.800000vw;
  font-weight: bold;
  color: #ffffff;
  text-align: center;
  margin-bottom: 4.266667vw;
  display: block;
}
.multiplication-table[data-v-wjn8r] {
  margin: 5.333333vw 5.333333vw;
}
.table-grid[data-v-wjn8r] {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 2.666667vw;
  padding: 4.266667vw;
}
.table-row[data-v-wjn8r] {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3.200000vw;
}
.table-row[data-v-wjn8r]:last-child {
  margin-bottom: 0;
}
.equation-item[data-v-wjn8r] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2.133333vw 1.066667vw;
  border-radius: 1.600000vw;
  margin: 0 0.533333vw;
  transition: all 0.3s ease;
}
.equation-item.highlight[data-v-wjn8r] {
  background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
}
.equation[data-v-wjn8r] {
  font-size: 3.200000vw;
  color: #4a5568;
  margin-bottom: 1.066667vw;
  display: block;
}
.result[data-v-wjn8r] {
  font-size: 4.266667vw;
  font-weight: bold;
  color: #2d3748;
  display: block;
}
.patterns-section[data-v-wjn8r] {
  margin: 5.333333vw 5.333333vw;
}
.pattern-card[data-v-wjn8r] {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 2.666667vw;
  padding: 4.266667vw;
  margin-bottom: 4.266667vw;
}
.pattern-header[data-v-wjn8r] {
  display: flex;
  align-items: center;
  margin-bottom: 3.200000vw;
}
.pattern-icon[data-v-wjn8r] {
  color: #667eea;
  margin-right: 2.133333vw;
}
.pattern-title[data-v-wjn8r] {
  font-size: 4.266667vw;
  font-weight: bold;
  color: #2d3748;
  display: block;
}
.pattern-desc[data-v-wjn8r] {
  font-size: 3.733333vw;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 3.200000vw;
  display: block;
}
.example-box[data-v-wjn8r] {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border-radius: 2.133333vw;
  padding: 3.200000vw;
  border-left: 1.066667vw solid #2196f3;
}
.example-title[data-v-wjn8r] {
  font-size: 3.733333vw;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 1.600000vw;
  display: block;
}
.example-step[data-v-wjn8r] {
  font-size: 3.466667vw;
  color: #424242;
  margin-bottom: 1.600000vw;
  display: block;
}
.example-result[data-v-wjn8r] {
  font-size: 4.266667vw;
  font-weight: bold;
  color: #1976d2;
  display: block;
}
.digit-examples[data-v-wjn8r] {
  display: flex;
  flex-direction: column;
  gap: 2.133333vw;
}
.digit-item[data-v-wjn8r] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f7fafc;
  border-radius: 1.600000vw;
  padding: 2.133333vw 3.200000vw;
}
.digit-equation[data-v-wjn8r] {
  font-size: 3.733333vw;
  font-weight: bold;
  color: #2d3748;
  display: block;
}
.digit-arrow[data-v-wjn8r] {
  font-size: 3.200000vw;
  color: #718096;
  display: block;
}
.digit-sum[data-v-wjn8r] {
  font-size: 3.466667vw;
  color: #667eea;
  display: block;
}
.sequence-display[data-v-wjn8r] {
  display: flex;
  flex-wrap: wrap;
  gap: 2.133333vw;
  justify-content: center;
}
.sequence-item[data-v-wjn8r] {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f7fafc;
  border-radius: 1.600000vw;
  padding: 2.133333vw;
  min-width: 10.666667vw;
}
.sequence-number[data-v-wjn8r] {
  font-size: 4.266667vw;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 1.066667vw;
  display: block;
}
.digit-breakdown[data-v-wjn8r] {
  display: flex;
  gap: 1.066667vw;
}
.tens-digit[data-v-wjn8r] {
  font-size: 2.666667vw;
  color: #e53e3e;
  background: #fed7d7;
  border-radius: 0.800000vw;
  padding: 0.533333vw 1.066667vw;
  display: block;
}
.ones-digit[data-v-wjn8r] {
  font-size: 2.666667vw;
  color: #3182ce;
  background: #bee3f8;
  border-radius: 0.800000vw;
  padding: 0.533333vw 1.066667vw;
  display: block;
}
.memory-methods[data-v-wjn8r] {
  margin: 5.333333vw 5.333333vw;
}
.method-grid[data-v-wjn8r] {
  display: flex;
  flex-direction: column;
  gap: 3.200000vw;
}
.method-card[data-v-wjn8r] {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 2.666667vw;
  padding: 4.266667vw;
}
.method-header[data-v-wjn8r] {
  display: flex;
  align-items: center;
  margin-bottom: 2.666667vw;
}
.method-number[data-v-wjn8r] {
  width: 6.400000vw;
  height: 6.400000vw;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3.200000vw;
  font-weight: bold;
  margin-right: 2.133333vw;
}
.method-title[data-v-wjn8r] {
  font-size: 4.000000vw;
  font-weight: bold;
  color: #2d3748;
  display: block;
}
.method-content[data-v-wjn8r] {
  font-size: 3.733333vw;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 2.133333vw;
  display: block;
}
.method-example[data-v-wjn8r] {
  background: #f0fff4;
  border-radius: 1.600000vw;
  padding: 2.133333vw;
  border-left: 0.800000vw solid #38a169;
}
.example-label[data-v-wjn8r] {
  font-size: 3.200000vw;
  color: #38a169;
  font-weight: bold;
  margin-right: 1.066667vw;
  display: inline;
}
.example-text[data-v-wjn8r] {
  font-size: 3.466667vw;
  color: #2f855a;
  display: inline;
}
.practice-section[data-v-wjn8r] {
  margin: 5.333333vw 5.333333vw;
}
.practice-card[data-v-wjn8r] {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 2.666667vw;
  padding: 5.333333vw;
  text-align: center;
}
.practice-question[data-v-wjn8r] {
  font-size: 4.800000vw;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 4.266667vw;
  display: block;
}
.answer-options[data-v-wjn8r] {
  display: flex;
  flex-wrap: wrap;
  gap: 2.133333vw;
  justify-content: center;
  margin-bottom: 4.266667vw;
}
.option-btn[data-v-wjn8r] {
  background: #f7fafc;
  border: 0.266667vw solid #e2e8f0;
  border-radius: 1.600000vw;
  padding: 2.133333vw 3.200000vw;
  min-width: 10.666667vw;
  transition: all 0.3s ease;
}
.option-btn.selected[data-v-wjn8r] {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
}
.option-text[data-v-wjn8r] {
  font-size: 3.733333vw;
  color: #2d3748;
  font-weight: bold;
  display: block;
}
.option-btn.selected .option-text[data-v-wjn8r] {
  color: white;
}
.practice-result[data-v-wjn8r] {
  margin-top: 3.200000vw;
}
.result-text[data-v-wjn8r] {
  font-size: 4.266667vw;
  font-weight: bold;
  margin-bottom: 2.666667vw;
  display: block;
}
.result-text.correct[data-v-wjn8r] {
  color: #38a169;
}
.result-text.incorrect[data-v-wjn8r] {
  color: #e53e3e;
}
.next-btn[data-v-wjn8r] {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 1.600000vw;
  padding: 2.133333vw 4.266667vw;
  font-size: 3.733333vw;
  font-weight: bold;
}
.tips-section[data-v-wjn8r] {
  margin: 5.333333vw 5.333333vw;
}
.tips-card[data-v-wjn8r] {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 2.666667vw;
  padding: 4.266667vw;
}
.tips-header[data-v-wjn8r] {
  display: flex;
  align-items: center;
  margin-bottom: 3.200000vw;
}
.tips-icon[data-v-wjn8r] {
  font-size: 4.266667vw;
  margin-right: 2.133333vw;
  display: block;
}
.tips-title[data-v-wjn8r] {
  font-size: 4.266667vw;
  font-weight: bold;
  color: #2d3748;
  display: block;
}
.tips-list[data-v-wjn8r] {
  display: flex;
  flex-direction: column;
  gap: 2.133333vw;
}
.tip-item[data-v-wjn8r] {
  display: flex;
  align-items: flex-start;
}
.tip-bullet[data-v-wjn8r] {
  color: #667eea;
  font-size: 3.200000vw;
  margin-right: 1.600000vw;
  margin-top: 0.533333vw;
  display: block;
}
.tip-text[data-v-wjn8r] {
  font-size: 3.733333vw;
  color: #4a5568;
  line-height: 1.6;
  flex: 1;
  display: block;
}
  </style>
</head>
<body>
  <div id=&quot;lynx-app-container&quot; data-component-id=&quot;ab0falce&quot;>
    <div data-scroll-y=&quot;{{true}}&quot; overflowY=&quot;hidden&quot; class=&quot;container&quot; data-v-wjn8r=&quot;&quot;>{true}<div className=&quot;header-section&quot; data-v-v-3h80lsdy=&quot;&quot; data-v-wjn8r=&quot;&quot;><div className=&quot;title-card&quot; data-v-v-ntp05to3=&quot;&quot; data-v-wjn8r=&quot;&quot;><span className=&quot;icon-text&quot; data-v-v-viuy4ccr=&quot;&quot; data-v-wjn8r=&quot;&quot;><svg data-v-wjn8r=&quot;&quot;><path data-v-wjn8r=&quot;&quot;></path></svg></span><div className=&quot;title-content&quot; data-v-v-t0cfnkoh=&quot;&quot; data-v-wjn8r=&quot;&quot;><span className=&quot;main-title&quot; data-v-v-flyup1ti=&quot;&quot; data-v-wjn8r=&quot;&quot;></span><span className=&quot;subtitle&quot; data-v-v-4h8tufyh=&quot;&quot; data-v-wjn8r=&quot;&quot;></span></div></div></div><div className=&quot;multiplication-table&quot; data-v-v-cgmczruv=&quot;&quot; data-v-wjn8r=&quot;&quot;><span className=&quot;section-title&quot; data-v-v-pvvryiov=&quot;&quot; data-v-wjn8r=&quot;&quot;></span><div className=&quot;table-grid&quot; data-v-v-5h5d4svz=&quot;&quot; data-v-wjn8r=&quot;&quot;><div tt:for=&quot;{{tableRows}}&quot; tt:key=&quot;index&quot; className=&quot;table-row&quot; data-v-v-b5icgmig=&quot;&quot; data-v-wjn8r=&quot;&quot;>{tableRows.map((item, index) => ({tableRows}<div tt:for=&quot;{{item}}&quot; tt:key=&quot;id&quot; highlight=&quot;&quot; :=&quot;&quot; className=&quot;equation-item {{item.highlight ? &quot; data-v-v-zzazwhd7=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item.map((item, index) => ({item}{item.highlight ? 'highlight' : ''}<span className=&quot;equation&quot; data-v-v-d2mybuf1=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item.equation}</span><span className=&quot;result&quot; data-v-v-syhtxjaj=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item.result}</span></div></div></div></div><div className=&quot;patterns-section&quot; data-v-v-q3oi6tr2=&quot;&quot; data-v-wjn8r=&quot;&quot;><span className=&quot;section-title&quot; data-v-v-pk2y9erc=&quot;&quot; data-v-wjn8r=&quot;&quot;></span><div className=&quot;pattern-card&quot; data-v-v-l5h76zpi=&quot;&quot; data-v-wjn8r=&quot;&quot;><div className=&quot;pattern-header&quot; data-v-v-71p998p4=&quot;&quot; data-v-wjn8r=&quot;&quot;><span className=&quot;pattern-icon&quot; data-v-v-gevovb04=&quot;&quot; data-v-wjn8r=&quot;&quot;><svg data-v-wjn8r=&quot;&quot;><path data-v-wjn8r=&quot;&quot;></path></svg></span><span className=&quot;pattern-title&quot; data-v-v-q8k3onmn=&quot;&quot; data-v-wjn8r=&quot;&quot;></span></div><span className=&quot;pattern-desc&quot; data-v-v-5wro6rya=&quot;&quot; data-v-wjn8r=&quot;&quot;></span><div className=&quot;example-box&quot; data-v-v-7yvxhcya=&quot;&quot; data-v-wjn8r=&quot;&quot;><span className=&quot;example-title&quot; data-v-v-e1uw45vk=&quot;&quot; data-v-wjn8r=&quot;&quot;></span><span className=&quot;example-step&quot; data-v-v-ilqk5d2e=&quot;&quot; data-v-wjn8r=&quot;&quot;></span><span className=&quot;example-result&quot; data-v-v-iz1ub7fs=&quot;&quot; data-v-wjn8r=&quot;&quot;></span></div></div><div className=&quot;pattern-card&quot; data-v-v-c4bsnpl1=&quot;&quot; data-v-wjn8r=&quot;&quot;><div className=&quot;pattern-header&quot; data-v-v-g3aaa9ze=&quot;&quot; data-v-wjn8r=&quot;&quot;><span className=&quot;pattern-icon&quot; data-v-v-sm06rw1s=&quot;&quot; data-v-wjn8r=&quot;&quot;><svg data-v-wjn8r=&quot;&quot;><path data-v-wjn8r=&quot;&quot;></path></svg></span><span className=&quot;pattern-title&quot; data-v-v-iibr8fid=&quot;&quot; data-v-wjn8r=&quot;&quot;></span></div><span className=&quot;pattern-desc&quot; data-v-v-kys297vo=&quot;&quot; data-v-wjn8r=&quot;&quot;></span><div className=&quot;digit-examples&quot; data-v-v-gem2zr10=&quot;&quot; data-v-wjn8r=&quot;&quot;><div tt:for=&quot;{{digitExamples}}&quot; tt:key=&quot;index&quot; className=&quot;digit-item&quot; data-v-v-wvc4iu6v=&quot;&quot; data-v-wjn8r=&quot;&quot;>{digitExamples.map((item, index) => ({digitExamples}<span className=&quot;digit-equation&quot; data-v-v-00zokkt5=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item.number}</span><span className=&quot;digit-arrow&quot; data-v-v-35x4ipdh=&quot;&quot; data-v-wjn8r=&quot;&quot;></span><span className=&quot;digit-sum&quot; data-v-v-ccfeomp4=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item.digitSum}{item.sum}</span></div></div></div><div className=&quot;pattern-card&quot; data-v-v-8nx08gve=&quot;&quot; data-v-wjn8r=&quot;&quot;><div className=&quot;pattern-header&quot; data-v-v-o09596z7=&quot;&quot; data-v-wjn8r=&quot;&quot;><span className=&quot;pattern-icon&quot; data-v-v-tlcaozpz=&quot;&quot; data-v-wjn8r=&quot;&quot;><svg data-v-wjn8r=&quot;&quot;><path data-v-wjn8r=&quot;&quot;></path></svg></span><span className=&quot;pattern-title&quot; data-v-v-fschrx9q=&quot;&quot; data-v-wjn8r=&quot;&quot;></span></div><span className=&quot;pattern-desc&quot; data-v-v-qg7uwouo=&quot;&quot; data-v-wjn8r=&quot;&quot;></span><div className=&quot;sequence-display&quot; data-v-v-85efgpn2=&quot;&quot; data-v-wjn8r=&quot;&quot;><div tt:for=&quot;{{sequencePattern}}&quot; tt:key=&quot;index&quot; className=&quot;sequence-item&quot; data-v-v-n9s417js=&quot;&quot; data-v-wjn8r=&quot;&quot;>{sequencePattern.map((item, index) => ({sequencePattern}<span className=&quot;sequence-number&quot; data-v-v-wrobggaw=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item.result}</span><div className=&quot;digit-breakdown&quot; data-v-v-t2wkzqxf=&quot;&quot; data-v-wjn8r=&quot;&quot;><span className=&quot;tens-digit&quot; data-v-v-laad8qrj=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item.tens}</span><span className=&quot;ones-digit&quot; data-v-v-0uryntrn=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item.ones}</span></div></div></div></div></div><div className=&quot;memory-methods&quot; data-v-v-zhci7xd1=&quot;&quot; data-v-wjn8r=&quot;&quot;><span className=&quot;section-title&quot; data-v-v-x3h6tvhs=&quot;&quot; data-v-wjn8r=&quot;&quot;></span><div className=&quot;method-grid&quot; data-v-v-gf3ep4do=&quot;&quot; data-v-wjn8r=&quot;&quot;><div tt:for=&quot;{{memoryMethods}}&quot; tt:key=&quot;id&quot; className=&quot;method-card&quot; data-v-v-d39r0shp=&quot;&quot; data-v-wjn8r=&quot;&quot;>{memoryMethods.map((item, index) => ({memoryMethods}<div className=&quot;method-header&quot; data-v-v-hhd8895g=&quot;&quot; data-v-wjn8r=&quot;&quot;><span className=&quot;method-number&quot; data-v-v-wzbpnqpw=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item.id}</span><span className=&quot;method-title&quot; data-v-v-itktg7l6=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item.title}</span></div><span className=&quot;method-content&quot; data-v-v-yy1vndg7=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item.content}</span><div tt:if=&quot;{{item.example}}&quot; className=&quot;method-example&quot; data-v-v-fwj4w86o=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item.example &amp;&amp; ({item.example}<span className=&quot;example-label&quot; data-v-v-ylzr03zb=&quot;&quot; data-v-wjn8r=&quot;&quot;></span><span className=&quot;example-text&quot; data-v-v-mxnux810=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item.example}</span></div></div></div></div><div className=&quot;practice-section&quot; data-v-v-brwifpoi=&quot;&quot; data-v-wjn8r=&quot;&quot;><span className=&quot;section-title&quot; data-v-v-8i0yacoz=&quot;&quot; data-v-wjn8r=&quot;&quot;></span><div className=&quot;practice-card&quot; data-v-v-q3e83vx7=&quot;&quot; data-v-wjn8r=&quot;&quot;><span className=&quot;practice-question&quot; data-v-v-njvie1xh=&quot;&quot; data-v-wjn8r=&quot;&quot;>{currentQuestion.question}</span><div className=&quot;answer-options&quot; data-v-v-tm8jwqn8=&quot;&quot; data-v-wjn8r=&quot;&quot;><div tt:for=&quot;{{currentQuestion.options}}&quot; tt:key=&quot;index&quot; bindtap=&quot;selectAnswer&quot; data-answer=&quot;{{item}}&quot; selected=&quot;&quot; :=&quot;&quot; className=&quot;option-btn {{selectedAnswer === item ? &quot; data-v-v-6ks4dppc=&quot;&quot; data-v-wjn8r=&quot;&quot;>{currentQuestion.options.map((item, index) => ({currentQuestion.options}{item}{selectedAnswer === item ? 'selected' : ''}<span className=&quot;option-text&quot; data-v-v-h3ilf7la=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item}</span></div></div><div tt:if=&quot;{{showResult}}&quot; className=&quot;practice-result&quot; data-v-v-xjtkuomb=&quot;&quot; data-v-wjn8r=&quot;&quot;>{showResult &amp;&amp; ({showResult}<span correct=&quot;&quot; :=&quot;&quot; incorrect=&quot;&quot; className=&quot;result-text {{isCorrect ? &quot; data-v-v-nppcxm38=&quot;&quot; data-v-wjn8r=&quot;&quot;>{isCorrect ? 'correct' : 'incorrect'}{isCorrect ? '🎉 答对了！' : '再想想看～'}</span><button class=&quot;next-btn&quot; bindtap=&quot;nextQuestion&quot; data-v-wjn8r=&quot;&quot;></button></div></div></div><div className=&quot;tips-section&quot; data-v-v-77ngmr6h=&quot;&quot; data-v-wjn8r=&quot;&quot;><div className=&quot;tips-card&quot; data-v-v-mdasqev1=&quot;&quot; data-v-wjn8r=&quot;&quot;><div className=&quot;tips-header&quot; data-v-v-ci42c0un=&quot;&quot; data-v-wjn8r=&quot;&quot;><span className=&quot;tips-icon&quot; data-v-v-oa91lnru=&quot;&quot; data-v-wjn8r=&quot;&quot;></span><span className=&quot;tips-title&quot; data-v-v-bpsj2r8a=&quot;&quot; data-v-wjn8r=&quot;&quot;></span></div><div className=&quot;tips-list&quot; data-v-v-k5uzwdup=&quot;&quot; data-v-wjn8r=&quot;&quot;><div tt:for=&quot;{{learningTips}}&quot; tt:key=&quot;index&quot; className=&quot;tip-item&quot; data-v-v-pefaq0h8=&quot;&quot; data-v-wjn8r=&quot;&quot;>{learningTips.map((item, index) => ({learningTips}<span className=&quot;tip-bullet&quot; data-v-v-cf7irxfs=&quot;&quot; data-v-wjn8r=&quot;&quot;></span><span className=&quot;tip-text&quot; data-v-v-5qfa38bp=&quot;&quot; data-v-wjn8r=&quot;&quot;>{item}</span></div></div></div></div></div>
  </div>
</body>
</html>" sandbox="allow-scripts allow-same-origin allow-forms allow-pointer-lock allow-popups allow-modals allow-top-navigation-by-user-activation" title="9乘法表的规律和记忆方法 预览" style="width: 100%; height: 100%; border: none; pointer-events: auto; display: block; background: white; touch-action: pan-y pinch-zoom;"></iframe>