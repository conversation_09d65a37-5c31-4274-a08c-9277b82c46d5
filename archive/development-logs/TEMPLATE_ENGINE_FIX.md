# 🔧 TTML 模板引擎修复方案

## 📋 问题分析

从iframe内容分析，发现Parse5转换引擎存在严重的**模板解析缺陷**：

### 1. 模板语法未处理
```html
<!-- 问题：原始模板语法直接输出到HTML -->
<div class="lynx-view" data-v-xxx class="rank-item {{index === selectedIndex ? 'active' : ''}}"
     tt:for="{{countries}}" 
     tt:key="rank"
     data-bind-tap="selectCountry"
     data-index="{{index}}">
```

### 2. JavaScript代码未集成
```html
<!-- 问题：JavaScript代码被包装在FILE标签中，未执行 -->
<FILE name="index.js">
Card({
  data: { countries: [...] },
  selectCountry(e) { ... }
});
</FILE>
```

### 3. 数据绑定缺失
```html
<!-- 问题：插值表达式未计算 -->
<span class="lynx-text">{{item.rank}}</span>
<span class="lynx-text">{{item.name}}</span>
```

## 🚀 修复方案

### 方案A: 完整模板引擎（推荐）
创建包含数据绑定和模板解析的完整解决方案：

1. **模板解析器**: 处理 `tt:for`, `tt:if`, `{{}}` 语法
2. **数据绑定引擎**: 将JavaScript数据与模板关联
3. **运行时渲染**: 在iframe中执行动态渲染

### 方案B: 服务端预渲染
在服务端直接计算模板，生成静态HTML：

1. **解析JavaScript数据**
2. **计算模板循环和条件**
3. **生成最终HTML**

## 📁 需要修复的文件

1. `parse5-ttml-adapter.ts` - 核心模板处理逻辑
2. `html-generator.ts` - HTML生成和JavaScript集成
3. `template-engine.ts` (新建) - 专用模板引擎
4. `data-binder.ts` (新建) - 数据绑定逻辑

## 🎯 修复优先级

### P0 (高优先级)
- [x] CSS作用域修复 ✅
- [ ] 模板语法解析
- [ ] JavaScript数据提取
- [ ] 基础数据绑定

### P1 (中优先级)  
- [ ] 循环渲染 (tt:for)
- [ ] 条件渲染 (tt:if)
- [ ] 事件绑定 (data-bind-tap)

### P2 (低优先级)
- [ ] 高级模板特性
- [ ] 性能优化
- [ ] 错误处理增强

## 📊 当前状态

✅ **CSS处理**: 正常工作，作用域隔离生效  
❌ **模板解析**: 完全缺失，模板语法直接输出  
❌ **数据绑定**: 无运行时，静态字符串  
❌ **JavaScript集成**: 代码未提取和执行  

## 🔧 立即行动

需要立即实现完整的模板引擎来解决iframe空白和无交互的问题。