# 🔒 Claude CSS规则十诫

## 📜 永久法律条文 - Permanent CSS Laws

**这些规则是绝对的，违反必有恶果。任何CSS修改前必须先阅读 `.claude-layout-config.json`**

---

## 🚨 第一诫：配置文件至上
```
THOU SHALL READ .claude-layout-config.json BEFORE ANY CSS MODIFICATION
每次修改CSS前必须阅读配置文件，理解现有架构和禁忌
```

## 🚨 第二诫：禁止盲目创建
```
THOU SHALL NOT CREATE NEW CSS FILES WITHOUT EXTREME JUSTIFICATION  
禁止盲目新建CSS文件，必须编辑现有文件
```

## 🚨 第三诫：!important毒药
```
THOU SHALL NOT USE !important UNLESS LIFE-OR-DEATH EMERGENCY
!important是毒药，除非生死攸关的紧急情况否则禁用
```

## 🚨 第四诫：Grid布局神圣
```
THOU SHALL NOT MODIFY grid-template-columns UNDER ANY CIRCUMSTANCE
Grid布局的 grid-template-columns: 360px 1fr 340px 神圣不可侵犯
```

## 🚨 第五诫：抽屉隔离
```
THOU SHALL KEEP DRAWERS FIXED POSITIONED AND ISOLATED
抽屉必须使用 position: fixed，绝不影响主布局
```

## 🚨 第六诫：示例数据测试
```
THOU SHALL TEST SAMPLE DATA BUTTON AFTER EVERY CSS CHANGE
每次CSS修改后必须测试"示例数据"按钮，确保页面宽度不变
```

## 🚨 第七诫：现有文件优先
```
THOU SHALL MODIFY EXISTING FILES INSTEAD OF CREATING NEW ONES
优先修改现有CSS文件，而不是创建新文件
```

## 🚨 第八诫：语义化命名
```
THOU SHALL USE SEMANTIC CLASS NAMES, NOT UTILITY OVERRIDES
使用语义化类名，避免工具类覆盖
```

## 🚨 第九诫：布局测试协议
```
THOU SHALL FOLLOW THE SEVEN-STEP TESTING PROTOCOL
必须遵循七步测试协议验证布局稳定性
```

## 🚨 第十诫：疑则勿为
```
WHEN IN DOUBT, ASK THE USER INSTEAD OF CREATING CSS
有疑虑时询问用户，而不是盲目创建CSS
```

---

## ⚖️ 违规后果 - Violation Consequences

| 违规行为 | 立即后果 | 长期影响 |
|---------|---------|---------|
| 忽略配置文件 | Bug重现 | 布局系统不稳定 |
| 盲目新建CSS | 样式冲突 | 维护噩梦 |
| 滥用!important | 优先级混乱 | CSS级联破坏 |
| 修改Grid布局 | 页面崩溃 | 用户体验灾难 |
| 不测试示例数据 | 宽度缩窄Bug | 功能不可用 |

---

## 📋 强制工作流 - Mandatory Workflow

### 🔍 CSS修改前 (Before)
1. ✅ **阅读** `.claude-layout-config.json` 完整内容
2. ✅ **识别** 要修改的现有CSS文件
3. ✅ **检查** 当前页面布局正常
4. ✅ **确认** 修改不会影响Grid结构

### ⚙️ CSS修改中 (During) 
1. ✅ **编辑** 现有文件，绝不创建新文件
2. ✅ **避免** 使用!important除非紧急
3. ✅ **使用** 语义化类名修改
4. ✅ **测试** 增量修改效果

### ✅ CSS修改后 (After)
1. ✅ **测试** "示例数据"按钮功能
2. ✅ **验证** 三栏布局完整性
3. ✅ **检查** 抽屉不影响主布局
4. ✅ **测试** 响应式断点正常
5. ✅ **确保** 无意外滚动条

---

## 📂 现有CSS文件架构

### 🎯 主要文件
- **index-optimized.css**: 主样式文件，颜色主题变量
- **emergency-layout-fix.css**: 布局问题紧急修复
- **drawer-layout-optimization.css**: 抽屉布局优化

### 🎨 动画模块
- **enhanced-interactions.css**: 交互动画
- **blue-fresh-animations.css**: 蓝色清新动画

### 🛠️ 修改策略
- **布局问题** → 编辑 `emergency-layout-fix.css`
- **抽屉问题** → 编辑 `drawer-layout-optimization.css`
- **动画问题** → 编辑现有动画模块
- **颜色主题** → 编辑 `index-optimized.css` 变量

---

## 🎯 核心保护目标

### 🔒 绝对保护
- **Grid结构**: `360px 1fr 340px` 三栏布局
- **抽屉定位**: `position: fixed` 隔离
- **响应式**: 断点正常工作
- **功能性**: 示例数据按钮正常

### 🚫 绝对禁止
- 修改Grid模板列
- 创建新CSS文件  
- 滥用!important
- 给抽屉加固定宽度
- 忽略配置文件

---

## 💡 记忆口诀

```
配置先读，现有先改
Grid神圣，抽屉隔开  
!important毒，新建CSS坏
示例数据必须测，布局稳定才叫拽
```

---

**🔒 此文档与 `.claude-layout-config.json` 构成CSS修改的永久法律。违反必定导致Bug重现和系统不稳定。**

**遵守规则 = 稳定系统**  
**违反规则 = 无尽调试**