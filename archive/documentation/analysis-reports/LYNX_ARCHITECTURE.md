# Lynx代码批量生产与在线预览系统架构方案

## 1. 总体架构设计

### 1.1 系统整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        Batch Processor 系统                      │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌──────────┐ │
│  │  UI输入层   │  │ 提示词引擎  │  │ AI生成引擎  │  │ 预览系统 │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └──────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌──────────┐ │
│  │ 双向转换器  │  │ 代码编译器  │  │ 质量检查器  │  │ 存储引擎 │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └──────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  │ runtime_convert_parse5 │  │ Web Worker │  │ 错误处理器  │  │ 监控系统 │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └──────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 核心模块职责

| 模块 | 职责 | 输入 | 输出 |
|------|------|------|------|
| 提示词引擎 | 生成Lynx规则完备的提示词 | 用户需求、模板库 | 优化提示词 |
| AI生成引擎 | 调用Claude4生成Lynx代码 | 提示词、用户输入 | TTML/TTSS/JS |
| runtime_convert_parse5 | Lynx↔Web代码转换 | Lynx代码/Web代码 | 转换后代码 |
| 代码编译器 | 编译验证Lynx代码 | 源代码 | 编译结果/错误信息 |
| 预览系统 | 实时预览渲染效果 | 编译后代码 | 可视化界面 |
| 质量检查器 | 代码质量和规范检查 | 生成代码 | 质量报告 |

## 2. 现有架构分析

### 2.1 已有核心组件

#### 2.1.1 EnhancedBatchProcessorService
- 任务队列管理 (Map<string, EnhancedJob>)
- 并发控制 (ConcurrencyManager) 
- 流数据处理 (parseStreamData)
- 错误重试机制
- 性能监控集成

#### 2.1.2 runtime_convert_parse5
- 基于Parse5的HTML解析器
- 支持TTML/TTSS到HTML/CSS转换
- Web Worker架构支持
- 完整的错误处理和降级机制

#### 2.1.3 useBatchProcessor Hook
```typescript
const {
  start,           // 开始批处理
  stop,            // 停止处理  
  progress,        // 进度状态
  results,         // 处理结果
  isRunning,       // 运行状态
  retryFailed,     // 重试失败
  updateConfig,    // 更新配置
} = useBatchProcessor();
```

### 2.2 数据处理流程

```mermaid
graph TD
    A[用户输入查询] --> B[查询解析和去重]
    B --> C[任务队列创建]
    C --> D[并发管理器分发]
    D --> E[AI API调用]
    E --> F[流数据解析]
    F --> G[代码提取LynxCode/HTMLCode]
    G --> H{内容类型检测}
    H -->|LYNX| I[构建LYNX文件结构]
    H -->|HTML| J[runtime_convert_parse5处理]
    I --> K[CDN上传]
    J --> K
    K --> L[Playground URL生成]
    L --> M[结果回调更新]
```

## 3. 提示词优化方案

### 3.1 MasterLevelUIPromptLoader.ts 升级架构

```typescript
interface LynxPromptConfig {
  // 基础Lynx语法规则
  syntaxRules: LynxSyntaxRules;
  // DOM元素映射规则
  elementMappings: ElementMappingRules;
  // CSS属性映射规则
  cssMappings: CSSMappingRules;
  // 事件系统映射规则
  eventMappings: EventMappingRules;
  // JavaScript接口映射规则
  jsApiMappings: JSAPIMappingRules;
  // 性能优化规则
  optimizationRules: OptimizationRules;
  // 模板编译规则
  compilationRules: CompilationRules;
  // 渲染流水线规则
  renderingPipelineRules: RenderingRules;
}
```

### 3.2 Lynx核心规则集成

#### 3.2.1 DOM元素映射规则
```typescript
const DOM_MAPPING_RULES = {
  elements: {
    'view': {
      nativeComponent: 'View/UIView',
      attributes: ['id', 'class', 'style'],
      children: 'all',
      description: '基本视图容器，对应原生View组件'
    },
    'text': {
      nativeComponent: 'TextView/UILabel', 
      attributes: ['text', 'color', 'font-size'],
      children: 'text-only',
      description: '文本显示组件'
    },
    'image': {
      nativeComponent: 'ImageView/UIImageView',
      attributes: ['src', 'width', 'height', 'mode'],
      children: 'none',
      description: '图片显示组件'
    },
    'list': {
      nativeComponent: 'RecyclerView/UICollectionView',
      attributes: ['data', 'item-template'],
      children: 'list-item',
      description: '列表视图组件'
    }
  }
};
```

#### 3.2.2 CSS属性映射规则
```typescript
const CSS_MAPPING_RULES = {
  layout: {
    'width': 'ComputedCSSStyle::SetWidth()',
    'height': 'ComputedCSSStyle::SetHeight()',
    'margin': 'ComputedCSSStyle::SetMargin()',
    'padding': 'ComputedCSSStyle::SetPadding()',
    'position': 'ComputedCSSStyle::SetPosition()',
    'display': 'ComputedCSSStyle::SetDisplay()',
    'flex': 'ComputedCSSStyle::SetFlex()'
  },
  visual: {
    'background-color': 'ComputedCSSStyle::SetBackgroundColor()',
    'color': 'ComputedCSSStyle::SetColor()',
    'font-size': 'ComputedCSSStyle::SetFontSize()',
    'border': 'ComputedCSSStyle::SetBorder()',
    'opacity': 'ComputedCSSStyle::SetOpacity()'
  }
};
```

## 4. AI代码生成引擎设计

### 4.1 生成引擎架构

```typescript
class LynxCodeGenerationEngine {
  // 多轮对话生成
  async generateLynxCode(requirement: UserRequirement): Promise<LynxCodeBundle> {
    // 第一轮：生成TTML结构
    const ttml = await this.generateTTML(requirement);
    
    // 第二轮：基于TTML生成TTSS样式  
    const ttss = await this.generateTTSS(ttml, requirement);
    
    // 第三轮：基于TTML+TTSS生成JS逻辑
    const js = await this.generateJS(ttml, ttss, requirement);
    
    // 第四轮：生成配置JSON
    const config = await this.generateConfig(ttml, ttss, js);
    
    return { ttml, ttss, js, config };
  }
}
```

## 5. 双向转换引擎设计

### 5.1 基于现有runtime_convert_parse5优化

```typescript
// 纯前端实现的双向转换
class BiDirectionalConverter {
  // Lynx → Web转换（基于现有Parse5引擎优化）
  convertLynxToWeb(lynxCode: LynxCodeBundle): WebCodeBundle {
    // 1. 本地解析
    const ttmlAST = this.parseTTML(lynxCode.ttml);
    const ttssAST = this.parseTTSS(lynxCode.ttss);
    const jsAST = this.parseJS(lynxCode.js);
    
    // 2. 基于runtime_convert_parse5转换
    const htmlCode = this.convertTTMLToHTML(ttmlAST);
    const cssCode = this.convertTTSSToCSS(ttssAST);
    const webJSCode = this.convertLynxJSToWebJS(jsAST);
    
    return { html: htmlCode, css: cssCode, js: webJSCode };
  }
  
  // 本地HTML到TTML的映射
  private convertHTMLToTTML(htmlNodes: HTMLNode[]): string {
    const convertNode = (node: HTMLNode): string => {
      switch (node.tagName?.toLowerCase()) {
        case 'div':
          return `<view${this.convertAttributes(node.attributes)}>${node.children?.map(convertNode).join('') || ''}</view>`;
        case 'span':
        case 'p':
          return `<text${this.convertAttributes(node.attributes)}>${node.textContent || ''}</text>`;
        case 'img':
          return `<image${this.convertAttributes(node.attributes)}></image>`;
        case 'ul':
        case 'ol':
          return `<list${this.convertAttributes(node.attributes)}>${node.children?.map(convertNode).join('') || ''}</list>`;
        default:
          return `<view${this.convertAttributes(node.attributes)}>${node.children?.map(convertNode).join('') || ''}</view>`;
      }
    };
    
    return htmlNodes.map(convertNode).join('\n');
  }
}
```

## 6. 在线预览系统设计

### 6.1 基于现有iframe预览优化

```typescript
// 纯前端实现的预览渲染
class LynxPreviewRenderer {
  private previewContainer: HTMLIFrameElement;
  
  constructor(container: HTMLIFrameElement) {
    this.previewContainer = container;
  }
  
  // 本地渲染Lynx代码
  async renderLynxCode(lynxCode: LynxCodeBundle): Promise<void> {
    try {
      // 1. 使用runtime_convert_parse5转换为Web代码
      const webCode = this.runtimeConvertParse5.convert(lynxCode);
      
      // 2. 生成预览HTML
      const previewHTML = this.generatePreviewHTML(webCode);
      
      // 3. 渲染到iframe
      this.previewContainer.srcdoc = previewHTML;
      
      // 4. 等待加载完成
      await this.waitForLoad();
      
      // 5. 注入调试工具
      this.injectDebugTools();
      
    } catch (error) {
      this.renderErrorFallback(error);
    }
  }
  
  // 生成预览HTML（本地实现）
  private generatePreviewHTML(webCode: WebCodeBundle): string {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Lynx Preview</title>
  <style>
    /* 重置样式 */
    * { margin: 0; padding: 0; box-sizing: border-box; }
    
    /* 预览容器样式 */
    body { 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
      background: #f5f5f5;
      padding: 16px;
    }
    
    .lynx-preview-wrapper {
      max-width: 375px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      overflow: hidden;
      position: relative;
    }
    
    .lynx-preview-header {
      background: #333;
      color: white;
      padding: 8px 16px;
      font-size: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .lynx-preview-content {
      padding: 16px;
      min-height: 200px;
    }
    
    /* 用户自定义样式 */
    ${webCode.css}
  </style>
</head>
<body>
  <div class="lynx-preview-wrapper">
    <div class="lynx-preview-header">
      <span>📱 Lynx Preview</span>
      <span id="preview-status">✅ 正常</span>
    </div>
    <div class="lynx-preview-content">
      ${webCode.html}
    </div>
  </div>
  
  <script>
    // 预览增强脚本
    window.addEventListener('error', (e) => {
      document.getElementById('preview-status').textContent = '❌ 错误';
      console.error('Preview Error:', e);
    });
    
    // 用户自定义脚本
    try {
      ${webCode.js}
    } catch (error) {
      console.error('User Script Error:', error);
      document.getElementById('preview-status').textContent = '⚠️ JS错误';
    }
    
    // 向父窗口发送加载完成消息
    window.parent.postMessage({ type: 'PREVIEW_LOADED' }, '*');
  </script>
</body>
</html>
    `;
  }
}
```

## 7. 工作流管理器

### 7.1 基于现有useBatchProcessor优化

```typescript
// 增强现有工作流管理
class EnhancedLynxWorkflowManager {
  // 基于现有架构扩展
  async executeEnhancedWorkflow(requirement: string): Promise<WorkflowResult> {
    try {
      // Step 1: 使用优化后的提示词生成代码
      this.setStep('generating');
      const enhancedPrompt = this.masterLevelUIPromptLoader.generateLynxPrompt(requirement);
      const generatedCode = await this.claudeService.generateCodeBundle(enhancedPrompt);
      
      // Step 2: 质量检查
      this.setStep('validating');
      const validationResult = this.qualityChecker.validateSyntax(generatedCode);
      
      // Step 3: 转换预览
      this.setStep('converting');
      const webCode = this.runtimeConvertParse5.convert(generatedCode);
      
      // Step 4: 预览渲染
      this.setStep('previewing');
      await this.previewRenderer.renderLynxCode(generatedCode);
      
      // Step 5: 完成
      this.setStep('completed');
      
      return {
        success: true,
        code: generatedCode,
        webCode: webCode,
        validation: validationResult
      };
      
    } catch (error) {
      this.setStep('error');
      return {
        success: false,
        error: error.message
      };
    }
  }
}
```

## 8. MVP版本实施计划

### 8.1 第一阶段：提示词优化 (Week 1)
- [ ] 升级 MasterLevelUIPromptLoader.ts 集成Lynx规则
- [ ] 添加DOM元素映射规则
- [ ] 添加CSS属性映射规则  
- [ ] 集成到现有提示词抽屉组件

### 8.2 第二阶段：转换引擎优化 (Week 2)
- [ ] 修复runtime_convert_parse5的转换bugs
- [ ] 优化Web Worker转换性能
- [ ] 增强错误处理和降级机制
- [ ] 集成双向转换功能

### 8.3 第三阶段：预览系统优化 (Week 3)
- [ ] 优化iframe预览体验
- [ ] 添加实时预览功能
- [ ] 集成调试工具
- [ ] 完善错误展示

### 8.4 第四阶段：集成测试 (Week 4)
- [ ] 端到端功能测试
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 文档完善

## 9. 性能基准与监控

### 9.1 性能基准

| 指标 | 目标值 | 监控方式 |
|------|--------|----------|
| 代码生成时间 | < 10s | 实时监控 |
| 代码转换时间 | < 2s | 实时监控 |
| 预览渲染时间 | < 1s | 实时监控 |
| 代码质量评分 | > 85% | 定期检查 |
| 错误率 | < 5% | 实时监控 |
| 用户满意度 | > 4.0/5.0 | 定期调研 |

## 10. 总结

本架构方案基于现有的batch_processor架构进行渐进式优化，充分利用已有的：

1. **EnhancedBatchProcessorService** - 成熟的批处理服务
2. **runtime_convert_parse5** - 完整的转换引擎
3. **useBatchProcessor Hook** - 现代化的状态管理
4. **三栏布局UI** - 良好的用户界面

通过优化提示词系统、修复转换bugs、增强预览功能，实现生产级的Lynx代码批量生产与在线预览系统。

核心优势：
- ✅ 基于现有架构，风险低
- ✅ 渐进式优化，迭代快
- ✅ 充分利用现有投入
- ✅ 保持系统稳定性