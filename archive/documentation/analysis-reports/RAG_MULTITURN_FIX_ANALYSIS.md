# RAG 系统多轮对话能力分析与修复方案

## 1. 问题诊断：当前系统为何未能充分发挥多轮对话能力？

经过对 `RAGSystemManager.ts`、`AIAnalyzer.ts` 和 `SessionManager.ts` 的深入分析，我们发现当前 RAG 系统在处理多轮对话时存在一个核心瓶颈：**对话历史的“扁平化”处理**。

具体表现为：

- **上下文简单拼接**：在 `RAGSystemManager.ts` 的 `processQuery` 方法中，系统通过 `sessionManager.getConversationContext` 获取最近几轮的对话历史。然而，这个方法返回的是一个将历史查询和所选模块简单拼接在一起的**纯文本字符串**。
- **信息结构丢失**：这个扁平化的字符串随后被直接附加到当前用户查询的前面，形成一个 `enhancedQuery`。在这个过程中，对话的结构、轮次、用户与系统的交替关系等关键信息都丢失了，AI 模型接收到的只是一个混合了历史和当前信息的大文本块。
- **分析能力受限**：`AIAnalyzer.ts` 在接收到这个 `enhancedQuery` 后，将其整体作为 `{{USER_QUERY}}` 填入分析模板。由于缺乏结构化的历史信息，AI 模型难以准确区分哪些是历史背景，哪些是当前的核心指令。这直接导致了以下问题：
    - **指代消解困难**：模型很难理解用户查询中的“它”、“刚才那个”等指代词究竟指向历史中的哪个对象或意图。
    - **上下文继承不佳**：当用户希望在之前的基础上进行补充或修正时（例如，“继续，但使用蓝色主题”），模型可能无法准确地将“继续”与正确的历史上下文关联起来。
    - **意图修正失效**：当用户试图纠正模型的理解时（例如，“不，我不是要图表，是要一个列表”），模型可能因为无法清晰地对比当前指令与历史输出的差异而导致修正失败。

综上所述，当前的实现方式虽然引入了会话机制，但并未让 AI 模型以最高效的方式利用对话历史，从而限制了 RAG 系统在复杂、连续的多轮交互中的表现。

## 2. 根本原因：`AIAnalyzer` 的 Prompt 设计缺陷

问题的根源在于 **`AIAnalyzer.ts` 中 `analysisPromptTemplate` 的设计**。当前的 Prompt 模板只有一个 `{{USER_QUERY}}` 输入占位符，它没有为对话历史提供一个专门的、结构化的字段。

一个理想的分析模型需要清晰地知道：“这是用户之前的几轮对话记录”和“这是用户本轮的最新指令”。当前的扁平化处理方式混淆了这两者，迫使模型在没有明确指导的情况下自行从文本中分离历史与当前，大大增加了其理解负担和出错概率。

## 3. 修复方案：实现结构化对话历史传递

为了从根本上解决此问题，我们提出以下**禁止编写具体代码**的逻辑改造方案，核心思想是**将结构化的对话历史传递给 AI 分析器**。

### 第一步：改造 `RAGSystemManager.ts`

在 `RAGSystemManager.ts` 的 `processQuery` 方法中，需要进行如下逻辑调整：

1.  **停止调用 `sessionManager.getConversationContext`**：移除当前用于生成扁平化上下文文本的相关代码块。
2.  **获取结构化历史**：直接从 `session` 对象中获取完整的、结构化的 `conversationHistory` 数组。这个数组包含了每一轮对话的详细对象（`ConversationTurn`）。
3.  **修改 `aiAnalyzer.analyzeQuery` 的调用接口**：将 `aiAnalyzer.analyzeQuery` 的调用从传递一个扁平的 `enhancedQuery` 字符串，修改为传递两个独立的参数：原始的 `query` 字符串和 `conversationHistory` 数组。

### 第二步：改造 `AIAnalyzer.ts`

相应的，`AIAnalyzer.ts` 需要进行以下调整：

1.  **更新 `analyzeQuery` 方法签名**：修改 `analyzeQuery` 方法，使其能够接收一个可选的 `conversationHistory: ConversationTurn[]` 参数。

2.  **重构 `getAnalysisPrompt` 方法**：
    - 此方法需要增加逻辑来处理传入的 `conversationHistory`。
    - 如果 `conversationHistory` 存在且不为空，需要将其格式化为一个清晰、易于模型理解的字符串（例如，标记出用户和系统的角色，以及每一轮的关键信息）。
    - 将格式化后的历史字符串插入到 Prompt 模板中一个**新的占位符**，例如 `{{CONVERSATION_HISTORY}}`。
    - 如果没有历史记录，则该占位符可以被替换为空字符串。

3.  **升级 `analysisPromptTemplate` (核心)**：这是整个修复方案中最关键的一环。需要对默认的分析 Prompt 进行精心设计，引入 `{{CONVERSATION_HISTORY}}` 占位符，并添加明确的指导语。

    **新 Prompt 模板的设计思路示例：**

    ```
    # Lynx框架需求分析任务（多轮对话模式）

    ## 任务说明
    根据下面的【对话历史】和用户的【当前需求】，分析需要哪些技术模块来完成任务。

    ## 对话历史
    {{CONVERSATION_HISTORY}}

    ## 当前需求
    {{USER_QUERY}}

    ## 技术模块说明
    ...

    ## 分析要求
    1.  **优先理解【当前需求】**。
    2.  **结合【对话历史】**来理解【当前需求】中的指代（如“它”、“之前的”）、补充和修正。
    3.  如果【当前需求】与【对话历史】冲突，以【当前需求】为准。
    ...

    ## 输出格式
    ...
    ```

## 4. 预期效果

完成上述改造后，我们预期系统将获得以下显著提升：

- **更高的分析准确性**：AI 模型能够清晰地区分历史与当前，从而更准确地识别用户意图。
- **强大的上下文理解能力**：系统将能更好地处理指代、省略和修正等复杂的对话现象。
- **更流畅的多轮交互体验**：用户可以进行更自然、更连续的对话，逐步完善和细化自己的需求，而系统能够始终保持正确的上下文跟踪。
- **充分发挥 RAG 潜力**：结构化的历史使得 RAG 系统不仅能在单轮查询中检索信息，还能在多轮对话中动态调整其检索策略，真正实现“对话式”的代码生成辅助。