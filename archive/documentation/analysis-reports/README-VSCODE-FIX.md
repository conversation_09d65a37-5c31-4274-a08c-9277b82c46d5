# VSCode 目录不显示问题解决方案

## 问题描述
VSCode 无法正常显示项目目录结构，点击目录时显示"此文件未显示在文本编辑器中，因为它是一个目录"。

## 根本原因分析
1. **文件数量过多**: 项目包含 72,289 个 TypeScript/JavaScript 文件
2. **文件监控超限**: 超出 VSCode 的文件监控能力
3. **TypeScript 服务内存不足**: 需要更多内存来处理大型项目
4. **AppTranslocation 问题**: VSCode 通过 AppTranslocation 运行可能导致权限问题

## 解决方案

### 方案1：自动修复脚本 (推荐)

运行项目根目录中的修复脚本：

```bash
./fix-vscode-explorer.sh
```

### 方案2：手动修复步骤

1. **完全关闭 VSCode**
   ```bash
   pkill -f "Visual Studio Code"
   ```

2. **清理 VSCode 缓存**
   ```bash
   rm -rf ~/Library/Application\ Support/Code/logs/*
   rm -rf ~/Library/Application\ Support/Code/CachedData/*
   rm -rf ~/Library/Application\ Support/Code/User/workspaceStorage/*
   ```

3. **使用工作区文件打开项目**
   ```bash
   code search-so-ai.code-workspace
   ```

### 方案3：使用 Remote Development (终极方案)

如果以上方案都无效，可以尝试：

1. 安装 VSCode 的 Remote Development 扩展包
2. 使用 `code --folder-uri vscode-vfs://github/` 打开项目
3. 或者使用 Dev Container 环境

### 方案4：替代编辑器

临时解决方案：

1. **WebStorm**: 专业的 TypeScript IDE，处理大型项目能力更强
2. **Sublime Text**: 轻量级但功能强大的编辑器
3. **Vim/Neovim**: 终端编辑器，性能优异

## 预防措施

1. **定期清理**: 删除不必要的历史文件和缓存
2. **排除大目录**: 在 `.vscode/settings.json` 中排除不必要的监控目录
3. **增加内存限制**: 设置 `typescript.maxTsServerMemory` 为 8192
4. **禁用自动功能**: 关闭自动导入、自动完成等消耗性能的功能

## 验证修复

修复后，验证以下功能：

1. ✅ 文件资源管理器正常显示目录结构
2. ✅ 文件搜索功能正常工作 (`Cmd+P`)
3. ✅ 全局搜索功能正常 (`Cmd+Shift+F`)
4. ✅ TypeScript 智能提示正常工作
5. ✅ 文件导航功能正常 (`Cmd+T`)

## 应急操作

如果 VSCode 完全无法使用，可以使用终端命令进行基本操作：

```bash
# 查看项目结构
tree -L 3 -I 'node_modules|.git|dist|build'

# 搜索文件
find . -name "*.ts" -not -path "./node_modules/*" | head -20

# 编辑文件
vim src/routes/batch_processor/page.tsx

# 运行项目
pnpm dev
```

## 技术说明

此问题主要由于：
- 项目规模过大（72,289 个文件）
- macOS 文件监控限制
- VSCode 内存管理问题
- 复杂的符号链接结构

修复脚本通过优化文件监控、增加内存限制、清理缓存等方式解决问题。