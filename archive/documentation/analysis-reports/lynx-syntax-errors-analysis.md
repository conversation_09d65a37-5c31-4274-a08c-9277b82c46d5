# @byted-lynx/web-speedy-plugin Lynx 语法错误分析

## 概述

本文档分析了 `@byted-lynx/web-speedy-plugin` 包中所有的错误抛出情况，特别关注与 Lynx 语法规则、写法和模板相关的错误。通过详细分析源码，整理出了开发者在使用 Lynx 框架时可能遇到的各种语法错误和解决方案。

## 包信息

- **包名**: `@byted-lynx/web-speedy-plugin`
- **版本**: 5.1.1
- **位置**: `/Users/<USER>/repos/search-so-ai/node_modules/@byted-lynx/web-speedy-plugin`
- **功能**: 为 Lynx 框架提供 Web 运行时编译能力

## 错误分析

### 1. 配置重复错误

**文件**: `src/webruntime2Plugin.ts:82-93`

**错误信息**:
```javascript
throw new Error(`[web-runtime2] not allowed: please pass options to this dslPlugin directly
DO NOT DO THIS:
  module.exports = {
    // ...
    dslPlugin:webruntime2Plugin({// some configurations
    }),
    //..
    webruntime2Options: {
      // do not configure me twice
    }
  }
`);
```

**触发条件**: 
- 同时在 `webruntime2Plugin()` 和 `webruntime2Options` 中配置选项

**语法规则**: 
- 只能在 `webruntime2Plugin()` 中传入配置选项
- 不能在两个地方同时配置，避免配置冲突

**解决方案**:
```javascript
// ✅ 正确写法
module.exports = {
  dslPlugin: webruntime2Plugin({
    runtimeDslMode: 'ttml',
    spa: true,
    // 其他配置...
  }),
  // ❌ 不要这样做
  // webruntime2Options: { ... }
}
```

### 2. 入口文件缺失错误

**文件**: `src/webruntime2Plugin.ts:95`

**错误信息**:
```javascript
throw new Error(`no input found`);
```

**触发条件**: 
- 编译配置中没有定义 `input` 入口文件

**语法规则**: 
- 必须定义至少一个入口文件
- 入口文件支持 `.ts`、`.js` 格式，会自动转换为 `.ttml`

**解决方案**:
```javascript
// ✅ 正确配置
module.exports = {
  input: {
    'index': './src/index.ttml',
    'about': './src/pages/about.ttml'
  },
  // ...
}
```

### 3. 重复入口点错误

**文件**: `src/webruntime2Plugin.ts:209`

**错误信息**:
```javascript
throw new Error(`[webruntime2] duplicate entrypoint ${value}`);
```

**触发条件**: 
- 不同的入口名称指向同一个文件路径

**语法规则**: 
- 每个入口文件路径只能被引用一次
- 不同的入口名称必须对应不同的文件

**解决方案**:
```javascript
// ❌ 错误：重复的入口点
module.exports = {
  input: {
    'page1': './src/index.ttml',
    'page2': './src/index.ttml'  // 重复了
  }
}

// ✅ 正确：每个入口点对应不同文件
module.exports = {
  input: {
    'page1': './src/page1.ttml',
    'page2': './src/page2.ttml'
  }
}
```

### 4. SPA 模式子编译失败

**文件**: `src/webruntime2Plugin.ts:413`

**错误信息**:
```javascript
throw new Error('[web-runtime2] sub compile: spa failed.');
```

**触发条件**: 
- 启用 SPA 模式时子编译过程出现错误

**语法规则**: 
- SPA 模式需要额外的编译步骤
- 所有语法错误都必须在 SPA 编译前解决

**解决方案**:
- 检查 TTML 模板语法是否正确
- 确保所有依赖文件都能正确解析
- 检查 CSS 和 JS 语法是否符合规范

### 5. SPA 模式文件输出错误

**文件**: `src/webruntime2Plugin.ts:429-435`

**错误信息**:
```javascript
throw new Error('[web-runtime2] sub compile error: no js file emitted.');
// 或
throw new Error('[web-runtime2] sub compile error: no html file emitted.');
```

**触发条件**: 
- SPA 编译成功但没有生成预期的 JS 或 HTML 文件

**语法规则**: 
- SPA 模式必须同时生成 JS 和 HTML 文件
- 文件生成失败通常表示模板语法有问题

**解决方案**:
- 检查 TTML 模板是否有语法错误
- 确保组件导入路径正确
- 验证 CSS 样式语法是否正确

### 6. 文件解析错误

**文件**: `src/utils/resolveAndLoadOneFile.ts:15-17` 和 `src/resolveAndLoadOneFile.ts:15-17`

**错误信息**:
```javascript
throw new Error(
  `[webruntime2] cannot resolve file in ${resolveArgs.path} from ${resolveArgs.resolveDir} by ${resolveArgs.importer}`
);
```

**触发条件**: 
- 无法解析文件路径
- 导入路径不正确
- 文件不存在

**语法规则**: 
- 所有导入路径必须能够正确解析
- 支持相对路径和绝对路径
- 支持的文件扩展名: `.ttml`, `.jsx`, `.tsx`, `.js`, `.ts`, `.lepus`, `.css`, `.less`, `.sass`, `.scss`, `.ttss`, `.json`

**解决方案**:
```javascript
// ✅ 正确的导入语法
import Component from './components/MyComponent.ttml';
import styles from './styles/index.ttss';
import utils from '../utils/helper.js';

// ❌ 错误：路径不存在
import Component from './nonexistent/Component.ttml';
```

### 7. 文件加载错误

**文件**: `src/utils/resolveAndLoadOneFile.ts:27-29` 和 `src/resolveAndLoadOneFile.ts:27-29`

**错误信息**:
```javascript
throw new Error(
  `[webruntime2] cannot load file ${resolveArgs.path} in ${loadArgs.path}`
);
```

**触发条件**: 
- 文件路径解析成功但内容加载失败
- 文件权限问题
- 文件格式不支持

**语法规则**: 
- 文件必须具有正确的读取权限
- 文件内容必须符合对应格式的语法规范

**解决方案**:
- 检查文件是否存在且可读
- 验证文件内容语法是否正确
- 确保文件编码为 UTF-8

### 8. 主题表达式缺失错误

**文件**: `src/utils/generateSPAHtml.ts:42`

**错误信息**:
```javascript
throw new Error('themeExpr is not defined.');
```

**触发条件**: 
- 启用了暗黑模式但没有定义主题表达式

**语法规则**: 
- 使用暗黑模式时必须提供 `themeExpr` 配置
- 主题表达式用于在运行时切换主题

**解决方案**:
```javascript
// ✅ 正确配置暗黑模式
webruntime2Plugin({
  spa: true,
  darkMod: true,
  themeExpr: 'app.theme' // 必须提供主题表达式
})
```

## Lynx 语法最佳实践

### 1. 文件结构规范

```
project/
├── src/
│   ├── pages/
│   │   ├── index.ttml      # 页面模板
│   │   └── index.ttss      # 页面样式
│   ├── components/
│   │   ├── Button.ttml     # 组件模板
│   │   └── Button.ttss     # 组件样式
│   └── utils/
│       └── helper.js       # 工具函数
└── lynx.config.js          # 配置文件
```

### 2. 配置文件模板

```javascript
// lynx.config.js
const { webruntime2Plugin } = require('@byted-lynx/web-speedy-plugin');

module.exports = {
  // 入口文件配置
  input: {
    'index': './src/pages/index.ttml'
  },
  
  // 使用 webruntime2 插件
  dslPlugin: webruntime2Plugin({
    runtimeDslMode: 'ttml',  // 或 'reactlynx2', 'reactlynx3'
    spa: false,              // 是否启用 SPA 模式
    rpx: {
      rpxMode: 'vw',         // RPX 转换模式: 'px', 'vw', 'cqw', 'throw', 'ignore'
      designWidth: 750       // 设计稿宽度
    }
  }),
  
  // 不要在这里重复配置
  // webruntime2Options: { ... }  // ❌ 错误
};
```

### 3. TTML 模板语法示例

```html
<!-- 正确的 TTML 语法 -->
<template>
  <view class="container">
    <text class="title">{{title}}</text>
    <view tt:for="{{items}}" tt:key="{{item.id}}">
      <text>{{item.name}}</text>
    </view>
    <button bindtap="handleClick">点击</button>
  </view>
</template>

<script>
Component({
  data: {
    title: 'Hello Lynx',
    items: [
      { id: 1, name: 'Item 1' },
      { id: 2, name: 'Item 2' }
    ]
  },
  
  handleClick() {
    console.log('Button clicked');
  }
});
</script>

<style>
.container {
  padding: 20rpx;
}

.title {
  font-size: 32rpx;
  color: #333;
}
</style>
```

### 4. 组件导入规范

```javascript
// 在 TTML 中导入组件
import MyComponent from './components/MyComponent.ttml';
import { helper } from '../utils/helper.js';

// 在配置中注册组件
Component({
  usingComponents: {
    'my-component': './components/MyComponent.ttml'
  }
});
```

## 常见错误排查

### 1. 编译失败排查步骤

1. **检查配置文件**
   - 确保没有重复配置
   - 验证入口文件路径正确
   - 检查文件扩展名

2. **检查文件语法**
   - TTML 模板语法
   - TTSS 样式语法
   - JavaScript 语法

3. **检查依赖关系**
   - 导入路径是否正确
   - 文件是否存在
   - 权限是否正确

### 2. SPA 模式问题排查

1. **配置检查**
   - 确保 `spa: true`
   - 提供正确的 `themeExpr`（如果使用暗黑模式）

2. **模板检查**
   - 确保模板语法正确
   - 检查组件导入

3. **样式检查**
   - 验证 CSS 语法
   - 检查 RPX 单位使用

## 总结

`@byted-lynx/web-speedy-plugin` 的错误处理机制主要集中在以下几个方面：

1. **配置验证**: 防止重复配置和缺失配置
2. **文件解析**: 确保所有文件都能正确解析和加载
3. **编译过程**: 验证 SPA 模式的编译结果
4. **语法检查**: 确保 TTML 模板和样式语法正确

开发者在使用 Lynx 框架时，应该：
- 遵循配置规范，避免重复配置
- 确保文件路径和导入语句正确
- 使用正确的 TTML 和 TTSS 语法
- 在启用 SPA 模式时提供完整的配置选项

通过理解这些错误类型和解决方案，可以有效提高 Lynx 应用的开发效率和代码质量。