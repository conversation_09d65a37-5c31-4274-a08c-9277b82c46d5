# Lynx Framework Development Guide

## Lynx 核心语法规则 (P0)

### 禁止使用的 HTML 语法
```
禁止: <div> → 必须使用: <view>
禁止: <span> → 必须使用: <text>  
禁止: <img> → 必须使用: <image>
禁止: <ul>, <li> → 必须使用: <list>, <list-item>
禁止: onclick → 必须使用: bindtap
禁止: addEventListener → 必须使用: bind事件绑定
禁止: document.getElementById → 必须使用: this.selectComponent
禁止: window.requestAnimationFrame → 必须使用: lynx.requestAnimationFrame
禁止: 固定px单位用于布局 → 必须使用: rpx响应式单位
```

### 核心原则
- 文件标记：必须使用 <FILES> 和 <FILE> 标签格式标记内部组成文件
- UI绘制：优先使用 view 标签和 TTSS，必要时选择 canvas
- 高度分配：禁用100vh/100%，根据内容需求合理分配，父元素放在 scroll-view 元素里面,同时为父元素设定max-height:100vh;
- 调试支持：所有 js 方法增加 console.log 打印调用信息和数据变更
- 输出要求：禁止输出思考内容，直接输出完整Lynx代码
- 永远记得使用可选链操作符 ?.、进行空值检查、正确绑定 this

### 设计标准
- 信息层次：主次分明，重点突出，四级字体系统，布局紧凑
- 视觉和谐：色彩协调，元素比例优雅，风格统一
- 交互流畅：符合用户直觉，操作简单，反馈及时
- 细节精致：微交互丰富，动画流畅，禁止文字覆盖叠放

## Lynx 移动端框架核心规则

### 组件系统与布局
- 组件系统：view(容器) text(文本) image(图片) list(高性能列表)
- 布局引擎：默认column方向，rpx单位，Flexbox+Grid双引擎
- 事件系统：bindtap(点击) bindlongpress(长按) 支持冒泡/捕获
- 渲染机制：IFR即时首屏，增量渲染，原生性能

### View 与 TTSS 渲染规范

#### 基础渲染
- 状态管理：合理使用组件状态和 TTSS 变量，避免样式冲突
- 像素精确：使用rpx或px单位，确保多端显示一致
- 性能优先：使用条件渲染控制组件显隐，按需加载
- 内存优化：及时清理不再使用的组件和数据
- 永远记得使用可选链操作符 ?.、进行空值检查、正确绑定 this

#### 生命周期管理
- 创建：通过TTML声明组件
- 绑定：通过 TTSS 绑定样式和动态属性
- 资源管理：onHide 暂停资源，onShow 恢复资源
- 性能优化：合理组织组件结构，避免不必要的重绘

### 关键转换规则
- 标签映射：div→view, span→text, img→image, ul/li→list/list-item
- 事件转换：click→tap, addEventListener→bindXXX, window→lynx
- 样式适配：px→rpx(布局), 移除cursor/pointer-events, 默认flex-direction:column
- API替换：document.querySelector→lynx.selectNode

## Lynx 指令系统 (P0 强制语法)

### 条件渲染
```ttml
<!-- 条件渲染 -->
<view lx:if="{{showContent}}">
  <text>显示内容</text>
</view>
<view lx:elif="{{showOther}}">
  <text>其他内容</text>
</view>
<view lx:else>
  <text>默认内容</text>
</view>
```

### 列表渲染
```ttml
<!-- 基础循环 -->
<view lx:for="item in dataList" lx:key="item.id">
  <text>{{item.name}}</text>
</view>

<!-- 带索引循环 -->
<view lx:for="item, index in dataList" lx:key="item.id">
  <text>{{index + 1}}. {{item.name}}</text>
</view>
```

## Lynx 元素映射规则 (P0)

### 基础容器元素
```ttml
<!-- 基础容器 - 替代 div -->
<view class="container">内容</view>

<!-- 可滚动容器 - 必须设置滚动属性 -->
<scroll-view scroll-y="{{true}}" style="height: 600rpx;">
  <view>滚动内容</view>
</scroll-view>

<!-- 可移动区域容器 (P1) -->
<movable-area style="height: 400rpx; width: 400rpx;">
  <movable-view direction="all">可移动的内容</movable-view>
</movable-area>

<!-- 覆盖容器 (P1) -->
<cover-view>覆盖在原生组件上的容器</cover-view>
<cover-image src="{{imageUrl}}">覆盖在原生组件上的图片</cover-image>
```

### 文本和媒体元素
```ttml
<!-- 文本显示 - 替代 span -->
<text class="title">{{textContent}}</text>

<!-- 富文本显示 (P1) -->
<rich-text nodes="{{richTextNodes}}"></rich-text>

<!-- 图片显示 - 替代 img -->
<image src="{{imageUrl}}" mode="aspectFit" lazy-load="{{true}}" 
       bindload="handleImageLoad" binderror="handleImageError" 
       style="width: 200rpx; height: 200rpx;" />

<!-- 视频播放 (P1) -->
<video src="{{videoUrl}}" controls="{{true}}" autoplay="{{false}}" />

<!-- 音频播放 (P1) -->
<audio src="{{audioUrl}}" controls="{{true}}" />

<!-- 按钮交互 -->
<button bindtap="handleButtonTap" class="action-btn">
  <text>点击按钮</text>
</button>
```

### 表单元素
```ttml
<!-- 文本输入 -->
<input type="text" placeholder="请输入内容" 
       bindinput="handleInput" bindblur="handleBlur" 
       value="{{inputValue}}" maxlength="100" />

<!-- 文本域 -->
<textarea placeholder="请输入多行文本" 
          bindinput="handleTextareaInput" 
          value="{{textareaValue}}" maxlength="500" />

<!-- 单选框组 -->
<radio-group bindchange="handleRadioChange">
  <label class="radio-item" tt:for="{{radioList}}" tt:key="value">
    <radio value="{{item.value}}" checked="{{item.checked}}" />
    <text>{{item.label}}</text>
  </label>
</radio-group>

<!-- 复选框组 -->
<checkbox-group bindchange="handleCheckboxChange">
  <label class="checkbox-item" tt:for="{{checkboxList}}" tt:key="value">
    <checkbox value="{{item.value}}" checked="{{item.checked}}" />
    <text>{{item.label}}</text>
  </label>
</checkbox-group>

<!-- 开关 -->
<switch checked="{{switchValue}}" bindchange="handleSwitchChange" />

<!-- 滑块 -->
<slider min="0" max="100" value="{{sliderValue}}" 
        bindchange="handleSliderChange" />

<!-- 选择器 -->
<picker mode="selector" range="{{pickerArray}}" 
        bindchange="handlePickerChange">
  <view class="picker-container">
    <text>{{pickerText}}</text>
  </view>
</picker>
```

### 高级布局组件 (P1)
```ttml
<!-- 轮播图组件 -->
<swiper class="swiper-container" 
        indicator-dots="{{true}}" autoplay="{{true}}" 
        interval="3000" duration="500">
  <swiper-item tt:for="{{swiperList}}" tt:key="id">
    <image src="{{item.imageUrl}}" class="swiper-image" />
  </swiper-item>
</swiper>

<!-- 高性能列表 -->
<list class="list-container">
  <list-item tt:for="{{listData}}" tt:key="id" class="list-item">
    <view class="item-content">
      <text class="item-title">{{item.title}}</text>
      <text class="item-description">{{item.description}}</text>
    </view>
  </list-item>
</list>

<!-- 导航栏 -->
<navigation-bar title="页面标题" background-color="#ffffff" 
                color="#000000" />

<!-- 选项卡 -->
<tab-bar bindchange="handleTabChange" current="{{currentTab}}">
  <tab-bar-item tt:for="{{tabList}}" tt:key="index">
    <text>{{item.text}}</text>
  </tab-bar-item>
</tab-bar>
```

### 专业组件 (P2)
```ttml
<!-- 画布组件 (自定义绘图) -->
<canvas canvas-id="myCanvas" 
        bindtouchstart="handleTouchStart" 
        bindtouchmove="handleTouchMove" 
        bindtouchend="handleTouchEnd"
        style="width: 750rpx; height: 500rpx;" />
```

## Lynx 事件系统 (P0)

### 基础交互事件
```javascript
// 点击事件
bindtap="handleTap"

// 长按事件  
bindlongpress="handleLongPress"

// 触摸事件
bindtouchstart="handleTouchStart"
bindtouchmove="handleTouchMove" 
bindtouchend="handleTouchEnd"

// 输入事件
bindinput="handleInput"
bindblur="handleBlur" 
bindfocus="handleFocus"

// 滚动事件
bindscroll="handleScroll"
bindscrolltoupper="handleScrollToUpper"
bindscrolltolower="handleScrollToLower"

// 图片事件
bindload="handleImageLoad"
binderror="handleImageError"

// 表单事件
bindchange="handleChange"
bindsubmit="handleSubmit"
bindreset="handleReset"

// 页面事件
bindshow="handleShow"
bindhide="handleHide"
bindresize="handleResize"
```

### 事件绑定语法详解
```javascript
// 页面级别事件处理
Page({
  data: {
    message: 'Hello Lynx'
  },
  
  // 点击事件处理
  handleTap(e) {
    console.log('点击事件触发:', e);
    console.log('当前数据:', this.data);
    
    this.setData({
      message: '按钮已点击'
    });
  },
  
  // 输入事件处理
  handleInput(e) {
    console.log('输入值:', e.detail.value);
    
    this.setData({
      inputValue: e.detail.value
    });
  },
  
  // 滚动事件处理
  handleScroll(e) {
    console.log('滚动位置:', e.detail.scrollTop);
  }
});

// 组件级别事件处理
Component({
  properties: {
    title: String
  },
  
  data: {
    count: 0
  },
  
  methods: {
    handleIncrement() {
      console.log('增加计数');
      this.setData({
        count: this.data.count + 1
      });
      
      // 触发父组件事件
      this.triggerEvent('countchange', {
        count: this.data.count
      });
    }
  }
});
```

## Lynx TTSS 样式系统 (P0)

### 布局系统
```ttss
/* 基础容器布局 */
.container {
  display: flex;
  flex-direction: column; /* Lynx默认为column */
  align-items: center;
  justify-content: flex-start;
  padding: 30rpx;
  background-color: #ffffff;
}

/* 响应式单位 - 必须使用rpx */
.responsive-box {
  width: 750rpx;          /* 全屏宽度 */
  height: 300rpx;         /* 固定高度 */
  margin: 20rpx;          /* 外边距 */
  padding: 15rpx 30rpx;   /* 内边距 */
  border-radius: 10rpx;   /* 圆角 */
}

/* Flexbox 布局 */
.flex-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.flex-column {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
}
```

### 文本样式
```ttss
/* 文本层级系统 */
.title-primary {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.4;
  text-align: center;
}

.title-secondary {
  font-size: 30rpx;
  font-weight: 600;
  color: #666666;
  line-height: 1.3;
}

.body-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
}

.caption-text {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.5;
}
```

### 交互样式
```ttss
/* 按钮样式 */
.button-primary {
  background-color: #007AFF;
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  min-height: 88rpx; /* 确保触摸区域 */
}

.button-secondary {
  background-color: transparent;
  color: #007AFF;
  border: 2rpx solid #007AFF;
  padding: 18rpx 38rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 输入框样式 */
.input-field {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #E5E5E5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #ffffff;
  box-sizing: border-box;
}

.input-field:focus {
  border-color: #007AFF;
}
```

### 动画和过渡
```ttss
/* 过渡动画 */
.fade-in {
  opacity: 0;
  animation: fadeIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-up {
  transform: translateY(100%);
  animation: slideUp 0.3s ease-out forwards;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

/* 状态样式 */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

.disabled {
  opacity: 0.5;
  pointer-events: none;
}
```

## Lynx JavaScript API 系统 (P0)

### 页面生命周期
```javascript
Page({
  data: {
    // 页面数据
    title: '',
    loading: false,
    userInfo: null
  },
  
  // 页面加载完成
  onLoad(options) {
    console.log('页面加载:', options);
    this.initPageData();
  },
  
  // 页面显示
  onShow() {
    console.log('页面显示');
    this.refreshData();
  },
  
  // 页面隐藏
  onHide() {
    console.log('页面隐藏');
    this.pauseBackgroundTasks();
  },
  
  // 页面卸载
  onUnload() {
    console.log('页面卸载');
    this.cleanupResources();
  },
  
  // 页面滚动
  onPageScroll(e) {
    console.log('页面滚动:', e.scrollTop);
  },
  
  // 下拉刷新
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.handleRefresh();
  },
  
  // 上拉加载
  onReachBottom() {
    console.log('触底加载');
    this.loadMoreData();
  }
});
```

### 组件系统
```javascript
Component({
  // 组件属性
  properties: {
    title: {
      type: String,
      value: '默认标题'
    },
    data: {
      type: Array,
      value: []
    }
  },
  
  // 组件数据
  data: {
    visible: true,
    currentIndex: 0
  },
  
  // 组件生命周期
  lifetimes: {
    created() {
      console.log('组件创建');
    },
    
    attached() {
      console.log('组件附加到页面');
      this.initComponent();
    },
    
    detached() {
      console.log('组件从页面移除');
      this.cleanupComponent();
    }
  },
  
  // 组件方法
  methods: {
    initComponent() {
      console.log('初始化组件');
    },
    
    handleItemTap(e) {
      const { index } = e.currentTarget.dataset;
      console.log('点击项目:', index);
      
      this.setData({
        currentIndex: index
      });
      
      // 触发父组件事件
      this.triggerEvent('itemtap', {
        index: index,
        item: this.properties.data[index]
      });
    }
  }
});
```

### 数据管理
```javascript
// 数据更新
this.setData({
  title: '新标题',
  'user.name': '新名称', // 支持路径更新
  'list[0].checked': true // 支持数组更新
});

// 批量数据更新
this.setData({
  loading: false,
  data: newData,
  error: null
}, () => {
  console.log('数据更新完成');
});

// 获取组件实例
const component = this.selectComponent('#myComponent');
if (component) {
  component.setData({
    visible: true
  });
}
```

### 网络请求
```javascript
// HTTP 请求
lynx.request({
  url: 'https://api.example.com/data',
  method: 'GET',
  data: {
    page: 1,
    limit: 10
  },
  header: {
    'Content-Type': 'application/json'
  },
  success: (res) => {
    console.log('请求成功:', res.data);
    this.setData({
      list: res.data.list,
      loading: false
    });
  },
  fail: (err) => {
    console.error('请求失败:', err);
    this.setData({
      error: '网络请求失败',
      loading: false
    });
  }
});
```

### 存储管理
```javascript
// 本地存储
lynx.setStorageSync('userInfo', userInfo);
const userInfo = lynx.getStorageSync('userInfo');

// 异步存储
lynx.setStorage({
  key: 'settings',
  data: settingsData,
  success: () => {
    console.log('存储成功');
  }
});

lynx.getStorage({
  key: 'settings',
  success: (res) => {
    console.log('获取存储:', res.data);
  }
});
```

### 页面导航
```javascript
// 页面跳转
lynx.navigateTo({
  url: '/pages/detail/detail?id=123'
});

// 页面重定向
lynx.redirectTo({
  url: '/pages/login/login'
});

// 返回上级页面
lynx.navigateBack({
  delta: 1
});

// 切换Tab页面
lynx.switchTab({
  url: '/pages/home/<USER>'
});
```

## Lynx Canvas 绘图系统 (P2)

### Canvas 基础使用
```javascript
Page({
  data: {
    canvasId: 'myCanvas'
  },
  
  onReady() {
    this.initCanvas();
  },
  
  initCanvas() {
    const ctx = lynx.createCanvasContext(this.data.canvasId, this);
    
    // 设置画布样式
    ctx.setFillStyle('#FF0000');
    ctx.setStrokeStyle('#00FF00');
    ctx.setLineWidth(2);
    
    // 绘制矩形
    ctx.fillRect(10, 10, 100, 100);
    ctx.strokeRect(120, 10, 100, 100);
    
    // 绘制圆形
    ctx.beginPath();
    ctx.arc(75, 200, 50, 0, 2 * Math.PI);
    ctx.setFillStyle('#0000FF');
    ctx.fill();
    
    // 绘制文本
    ctx.setFontSize(20);
    ctx.setFillStyle('#000000');
    ctx.fillText('Hello Canvas', 10, 300);
    
    // 提交绘制
    ctx.draw();
  },
  
  // 触摸绘制
  handleTouchStart(e) {
    const { x, y } = e.touches[0];
    this.startPoint = { x, y };
  },
  
  handleTouchMove(e) {
    if (!this.startPoint) return;
    
    const { x, y } = e.touches[0];
    const ctx = lynx.createCanvasContext(this.data.canvasId, this);
    
    ctx.beginPath();
    ctx.moveTo(this.startPoint.x, this.startPoint.y);
    ctx.lineTo(x, y);
    ctx.setStrokeStyle('#000000');
    ctx.setLineWidth(2);
    ctx.stroke();
    ctx.draw(true); // 保留之前的绘制内容
    
    this.startPoint = { x, y };
  }
});
```

### 数据可视化图表
```javascript
// 绘制柱状图
drawBarChart(data) {
  const ctx = lynx.createCanvasContext('chartCanvas', this);
  const canvasWidth = 350;
  const canvasHeight = 250;
  const padding = 40;
  const barWidth = (canvasWidth - padding * 2) / data.length - 10;
  const maxValue = Math.max(...data.map(item => item.value));
  
  // 清空画布
  ctx.clearRect(0, 0, canvasWidth, canvasHeight);
  
  // 绘制坐标轴
  ctx.beginPath();
  ctx.moveTo(padding, padding);
  ctx.lineTo(padding, canvasHeight - padding);
  ctx.lineTo(canvasWidth - padding, canvasHeight - padding);
  ctx.setStrokeStyle('#CCCCCC');
  ctx.stroke();
  
  // 绘制柱状图
  data.forEach((item, index) => {
    const barHeight = (item.value / maxValue) * (canvasHeight - padding * 2);
    const x = padding + index * (barWidth + 10);
    const y = canvasHeight - padding - barHeight;
    
    // 绘制柱子
    ctx.setFillStyle('#007AFF');
    ctx.fillRect(x, y, barWidth, barHeight);
    
    // 绘制标签
    ctx.setFontSize(12);
    ctx.setFillStyle('#333333');
    ctx.fillText(item.label, x, canvasHeight - padding + 15);
    ctx.fillText(item.value.toString(), x, y - 5);
  });
  
  ctx.draw();
}
```

## Lynx 开发最佳实践

### 性能优化
```javascript
// 1. 合理使用条件渲染
// 优化前
<view tt:if="{{showContent}}">
  <expensive-component />
</view>

// 优化后
<expensive-component tt:if="{{showContent}}" />

// 2. 列表优化
// 使用 list 组件替代 view + tt:for
<list class="list">
  <list-item tt:for="{{items}}" tt:key="id">
    <view>{{item.title}}</view>
  </list-item>
</list>

// 3. 图片懒加载
<image src="{{item.imageUrl}}" lazy-load="{{true}}" />

// 4. 防抖和节流
debounce(func, delay) {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
},

// 使用防抖
handleSearch: debounce(function(e) {
  const query = e.detail.value;
  this.searchData(query);
}, 300)
```

### 错误处理
```javascript
// 全局错误处理
App({
  onError(error) {
    console.error('全局错误:', error);
    // 上报错误信息
    this.reportError(error);
  },
  
  reportError(error) {
    lynx.request({
      url: '/api/error/report',
      method: 'POST',
      data: {
        error: error.toString(),
        timestamp: Date.now(),
        page: getCurrentPages().pop()?.route
      }
    });
  }
});

// 组件错误边界
Component({
  methods: {
    safeExecute(fn, fallback) {
      try {
        return fn();
      } catch (error) {
        console.error('组件错误:', error);
        if (fallback) fallback(error);
        return null;
      }
    },
    
    handleAsyncOperation() {
      this.setData({ loading: true });
      
      lynx.request({
        url: '/api/data',
        success: (res) => {
          this.safeExecute(() => {
            this.setData({
              data: res.data,
              loading: false
            });
          });
        },
        fail: (error) => {
          console.error('请求失败:', error);
          this.setData({
            error: '加载失败',
            loading: false
          });
        }
      });
    }
  }
});
```

### 代码组织
```javascript
// 1. 模块化工具函数
// utils/format.js
export const formatDate = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

export const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 2. 数据管理
// store/index.js
class Store {
  constructor() {
    this.state = {
      user: null,
      theme: 'light'
    };
    this.listeners = [];
  }
  
  setState(newState) {
    this.state = { ...this.state, ...newState };
    this.listeners.forEach(listener => listener(this.state));
  }
  
  subscribe(listener) {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }
}

export const store = new Store();

// 3. 组件复用
// components/loading/loading.js
Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    text: {
      type: String,
      value: '加载中...'
    }
  },
  
  // 使用外部样式类
  externalClasses: ['custom-class']
});
```

### 调试和测试
```javascript
// 开发环境调试
const DEBUG = process.env.NODE_ENV === 'development';

function log(...args) {
  if (DEBUG) {
    console.log('[DEBUG]', ...args);
  }
}

// 性能监控
function performanceTrack(name, fn) {
  const start = Date.now();
  const result = fn();
  const end = Date.now();
  
  if (DEBUG) {
    console.log(`[PERFORMANCE] ${name}: ${end - start}ms`);
  }
  
  return result;
}

// 使用示例
Page({
  onLoad() {
    performanceTrack('页面初始化', () => {
      this.initData();
    });
  },
  
  handleTap() {
    log('按钮点击', this.data);
  }
});
```

## 必需文件结构

### 文件结构检查
必须生成以下4个文件：
- index.ttml (模板结构)
- index.ttss (样式定义)
- index.js (逻辑处理)
- index.json (组件配置)

### 标准输出格式
每次输出必须包含以下文件：

<FILES>
<FILE path="index.ttml">
<!-- TTML 模板结构 -->
</FILE>
<FILE path="index.ttss">
/* TTSS 样式定义 */
</FILE>
<FILE path="index.js">
// JavaScript 逻辑处理
</FILE>
<FILE path="index.json">
{
  "component": true
}
</FILE>
</FILES>

## 输出执行指令

执行任务流程：

1. 分析用户需求
2. 根据决策树选择技术方案（View+TTSS 或 Canvas）
3. 严格遵守 P0 强制语法规则
4. 按需使用 P1/P2 扩展特性
5. 直接输出完整的Lynx代码文件，使用<FILES>和<FILE>标签格式
6. 确保所有代码可直接运行，符合移动端最佳实践