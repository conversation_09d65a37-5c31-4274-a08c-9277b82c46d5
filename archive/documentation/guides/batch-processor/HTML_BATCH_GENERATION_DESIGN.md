# HTML 批量生成功能技术设计方案

## 设计概述

本文档详细描述了为 LYNX 批量生成器添加 HTML 代码批量生成功能的技术实现方案。设计遵循**双轨道处理架构**原则，确保现有 LYNX 功能完全不受影响，同时引入完整的 HTML 处理能力。

## 1. 系统架构设计

### 1.1 整体架构图

```mermaid
flowchart TD
    A[用户输入] --> B[内容类型检测器]
    B --> C{内容分类}
    C -->|LYNX| D[LYNX 处理轨道]
    C -->|HTML| E[HTML 处理轨道]
    C -->|Auto| F[智能路由选择]
    
    D --> G[现有 LYNX 流程]
    G --> H[Playground 上传]
    H --> I[LYNX 结果卡片]
    
    E --> J[HTML 流式解析]
    J --> K[HTML 文件提取]
    K --> L[CDN 压缩上传]
    L --> M[iframe 渲染预览]
    M --> N[HTML 结果卡片]
    
    F --> O[分类置信度评估]
    O --> C
```

### 1.2 模块依赖关系

```
BatchProcessorPage (主页面)
├── ContentTypeSelector (分类选择组件)
├── EnhancedBatchProcessorService (增强处理服务)
│   ├── ContentClassifier (内容分类器) [新增]
│   ├── LynxProcessor (LYNX 处理器) [现有]
│   └── HTMLProcessor (HTML 处理器) [新增]
├── ResultsPanel (结果面板)
│   ├── LynxResultCard (LYNX 结果卡片) [现有]
│   └── HTMLResultCard (HTML 结果卡片) [新增]
└── streamParser (流解析器) [增强]
    ├── extractLynxCode [现有]
    └── extractHTMLCode [新增]
```

## 2. 核心组件设计

### 2.1 内容分类器 (ContentClassifier)

#### 2.1.1 接口定义
```typescript
// src/routes/batch_processor/services/ContentClassifier.ts

export interface ClassificationResult {
  type: 'lynx' | 'html' | 'mixed' | 'unknown';
  confidence: number; // 0-1
  details: {
    lynxScore: number;
    htmlScore: number;
    indicators: {
      lynx: string[];
      html: string[];
    };
  };
}

export class ContentClassifier {
  // 主要分类方法
  static classifyContent(content: string): ClassificationResult;
  
  // 专项检测方法
  static detectLynxFeatures(content: string): LynxDetectionResult;
  static detectHTMLFeatures(content: string): HTMLDetectionResult;
  
  // 置信度计算
  static calculateConfidence(features: FeatureSet): number;
}
```

#### 2.1.2 检测规则实现
```typescript
class ContentClassifier {
  // LYNX 特征指标（沿用现有 LynxValidator 逻辑）
  private static readonly LYNX_INDICATORS = {
    structure: ['<FILES>', '<FILE'], // 权重: 0.4
    tags: ['<view', '<text', '<image', '<scroll-view', '<list'], // 权重: 0.3
    attributes: ['tt:for', 'tt:if', 'bindtap', 'catchtap'], // 权重: 0.2
    styles: ['rpx', 'enable-scroll', 'scroll-x'], // 权重: 0.1
    scripts: ['Card(', 'setData', 'onLoad', 'onShow'] // 权重: 0.1
  };

  // HTML 特征指标（新增）
  private static readonly HTML_INDICATORS = {
    structure: ['<!DOCTYPE html', '<html', '<head>', '<body>', '<meta'], // 权重: 0.4
    tags: ['<div', '<span', '<p>', '<h1>', '<h2>', '<h3>', '<section', '<article'], // 权重: 0.3
    attributes: ['class=', 'id=', 'style=', 'data-', 'src=', 'href='], // 权重: 0.2
    styles: ['<style>', 'background:', 'color:', 'font-', 'margin:', 'padding:'], // 权重: 0.1
    scripts: ['<script>', 'document.', 'window.', 'addEventListener', 'querySelector'] // 权重: 0.1
  };

  static classifyContent(content: string): ClassificationResult {
    const lynxResult = this.detectLynxFeatures(content);
    const htmlResult = this.detectHTMLFeatures(content);
    
    const lynxScore = lynxResult.confidence;
    const htmlScore = htmlResult.confidence;
    
    // 分类决策逻辑
    let type: 'lynx' | 'html' | 'mixed' | 'unknown';
    let confidence: number;
    
    if (lynxScore > 0.7 && htmlScore < 0.3) {
      type = 'lynx';
      confidence = lynxScore;
    } else if (htmlScore > 0.7 && lynxScore < 0.3) {
      type = 'html';
      confidence = htmlScore;
    } else if (lynxScore > 0.3 && htmlScore > 0.3) {
      type = 'mixed';
      confidence = Math.max(lynxScore, htmlScore);
    } else {
      type = 'unknown';
      confidence = Math.max(lynxScore, htmlScore);
    }
    
    return {
      type,
      confidence,
      details: {
        lynxScore,
        htmlScore,
        indicators: {
          lynx: lynxResult.foundIndicators,
          html: htmlResult.foundIndicators
        }
      }
    };
  }
}
```

### 2.2 HTML 处理器 (HTMLProcessor)

#### 2.2.1 接口定义
```typescript
// src/routes/batch_processor/services/HTMLProcessor.ts

export interface HTMLProcessResult {
  success: boolean;
  files: { [path: string]: string };
  cdnUrl?: string;
  downloadUrl?: string;
  previewContent?: string;
  error?: string;
  metadata?: {
    fileCount: number;
    totalSize: number;
    compressionRatio: number;
  };
}

export class HTMLProcessor {
  // 主处理方法
  async processHTMLContent(content: string): Promise<HTMLProcessResult>;
  
  // HTML 文件提取
  extractHTMLFiles(content: string): { [path: string]: string };
  
  // CDN 上传
  uploadHTMLToCDN(files: { [path: string]: string }): Promise<UploadResult>;
  
  // 预览生成
  generatePreviewContent(files: { [path: string]: string }): string;
}
```

#### 2.2.2 HTML 文件提取实现
```typescript
class HTMLProcessor {
  extractHTMLFiles(content: string): { [path: string]: string } {
    const files: { [path: string]: string } = {};
    
    // 策略1: <FILE> 标签格式（复用现有逻辑）
    const fileMatches = content.match(
      /<FILE[^>]*(?:path|name)="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/g
    );
    
    if (fileMatches && fileMatches.length > 0) {
      // 使用现有的 FILE 标签解析逻辑
      return this.parseFileTagStructure(content);
    }
    
    // 策略2: **filename** 格式
    if (content.includes('**') && content.includes('```')) {
      return this.parseMarkdownStructure(content);
    }
    
    // 策略3: 代码块格式
    const htmlCodeBlocks = this.extractCodeBlocks(content, ['html', 'html5']);
    const cssCodeBlocks = this.extractCodeBlocks(content, ['css']);
    const jsCodeBlocks = this.extractCodeBlocks(content, ['javascript', 'js']);
    
    if (htmlCodeBlocks.length > 0) {
      // 组合 HTML/CSS/JS 文件
      files['index.html'] = htmlCodeBlocks[0];
      
      if (cssCodeBlocks.length > 0) {
        files['style.css'] = cssCodeBlocks[0];
      }
      
      if (jsCodeBlocks.length > 0) {
        files['script.js'] = jsCodeBlocks[0];
      }
      
      return files;
    }
    
    // 策略4: 单文件模式（智能识别文件类型）
    return this.parseSingleHTMLFile(content);
  }
  
  private parseSingleHTMLFile(content: string): { [path: string]: string } {
    const files: { [path: string]: string } = {};
    
    // 智能推断文件类型
    let filename = 'index.html';
    let fileContent = content;
    
    // 如果内容包含完整的 HTML 结构
    if (content.includes('<html') || content.includes('<!DOCTYPE')) {
      filename = 'index.html';
    } 
    // 如果只包含 CSS
    else if (content.includes('{') && content.includes('}') && 
             (content.includes('color:') || content.includes('background:'))) {
      filename = 'style.css';
    }
    // 如果只包含 JavaScript
    else if (content.includes('function') || content.includes('const ') || 
             content.includes('document.')) {
      filename = 'script.js';
    }
    // 如果是 HTML 片段
    else if (content.includes('<div') || content.includes('<span')) {
      // 包装成完整的 HTML
      fileContent = this.wrapHTMLFragment(content);
      filename = 'index.html';
    }
    
    files[filename] = fileContent;
    return files;
  }
  
  private wrapHTMLFragment(fragment: string): string {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML 预览</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
    </style>
</head>
<body>
    ${fragment}
</body>
</html>`;
  }
}
```

### 2.3 HTML 渲染服务 (HTMLRenderService)

#### 2.3.1 iframe 安全渲染
```typescript
// src/routes/batch_processor/services/HTMLRenderService.ts

export class HTMLRenderService {
  static generateIframePreview(htmlContent: string): string {
    // 安全处理 HTML 内容
    const sanitizedHTML = this.sanitizeHTML(htmlContent);
    
    // 生成 iframe HTML
    return `
      <iframe 
        sandbox="allow-scripts allow-same-origin allow-forms"
        style="width: 100%; height: 400px; border: 1px solid #ddd; border-radius: 8px;"
        srcdoc="${this.escapeHTML(sanitizedHTML)}"
        loading="lazy">
      </iframe>
    `;
  }
  
  static sanitizeHTML(content: string): string {
    // 基本的 XSS 防护
    return content
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '') // 移除 script 标签
      .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '') // 移除事件处理器
      .replace(/javascript:/gi, ''); // 移除 javascript: 协议
  }
  
  static escapeHTML(content: string): string {
    return content
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');
  }
}
```

### 2.4 增强的流解析器 (streamParser)

#### 2.4.1 HTML 代码提取
```typescript
// src/routes/batch_processor/utils/streamParser.js (增强)

/**
 * 从流数据中提取HTML代码
 * 与现有的 extractLynxCode 并行存在
 */
function extractHTMLCode(content) {
  try {
    const startTime = performance.now();
    
    // 策略1: 提取 html 代码块
    const htmlCodeBlocks = extractCodeBlocks(content, 'html');
    if (htmlCodeBlocks.length > 0) {
      return {
        success: true,
        extractedContent: htmlCodeBlocks.join('\n\n'),
        metadata: {
          strategy: 'html代码块',
          codeBlocks: htmlCodeBlocks.length
        }
      };
    }
    
    // 策略2: 提取 HTML 相关的其他代码块
    const webLanguages = ['html', 'html5', 'css', 'javascript', 'js'];
    for (const lang of webLanguages) {
      const blocks = extractCodeBlocks(content, lang);
      if (blocks.length > 0) {
        return {
          success: true,
          extractedContent: blocks.join('\n\n'),
          metadata: {
            strategy: `${lang}代码块`,
            codeBlocks: blocks.length
          }
        };
      }
    }
    
    // 策略3: 直接内容识别（HTML 特征）
    if (this.hasHTMLFeatures(content)) {
      return {
        success: true,
        extractedContent: content,
        metadata: {
          strategy: '直接HTML内容识别'
        }
      };
    }
    
    return {
      success: false,
      error: 'No HTML content found'
    };
    
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 检测内容是否包含HTML特征
 */
function hasHTMLFeatures(content) {
  const htmlIndicators = [
    '<!DOCTYPE html', '<html', '<head>', '<body>',
    '<div', '<span', '<p>', 'class=', 'id=',
    '<style>', 'background:', 'color:',
    '<script>', 'document.', 'window.'
  ];
  
  return htmlIndicators.some(indicator => 
    content.toLowerCase().includes(indicator.toLowerCase())
  );
}

// 导出新函数
module.exports = {
  parseStreamData,
  extractCodeBlocks,
  extractLynxCode, // 现有
  extractHTMLCode, // 新增
  parseLynxCodeStructure,
  hasHTMLFeatures // 新增
};
```

## 3. 用户界面设计

### 3.1 内容类型选择器

#### 3.1.1 组件设计
```typescript
// src/routes/batch_processor/components/ContentTypeSelector.tsx

interface ContentTypeSelectorProps {
  selectedType: 'lynx' | 'web' | 'auto';
  onTypeChange: (type: 'lynx' | 'web' | 'auto') => void;
  autoDetectedType?: 'lynx' | 'web';
  confidence?: number;
  disabled?: boolean;
}

const ContentTypeSelector: React.FC<ContentTypeSelectorProps> = ({
  selectedType,
  onTypeChange,
  autoDetectedType,
  confidence,
  disabled
}) => {
  return (
    <div className="content-type-selector">
      <h3 className="typography-body-large mb-3 flex items-center gap-2">
        <Icon type="branch" color="primary" size="sm" />
        生成类型选择
      </h3>
      
      <div className="grid grid-cols-3 gap-2 mb-4">
        {/* 自动检测 */}
        <button
          onClick={() => onTypeChange('auto')}
          className={`type-selector-btn ${selectedType === 'auto' ? 'active' : ''}`}
          disabled={disabled}
        >
          <Icon type="auto" color={selectedType === 'auto' ? 'white' : 'primary'} size="sm" />
          <span>智能检测</span>
          {autoDetectedType && (
            <div className="auto-detected">
              <Icon type={autoDetectedType === 'lynx' ? 'mobile' : 'web'} size="xs" />
              <span className="confidence">{Math.round((confidence || 0) * 100)}%</span>
            </div>
          )}
        </button>
        
        {/* LYNX 生成 */}
        <button
          onClick={() => onTypeChange('lynx')}
          className={`type-selector-btn ${selectedType === 'lynx' ? 'active' : ''}`}
          disabled={disabled}
        >
          <Icon type="mobile" color={selectedType === 'lynx' ? 'white' : 'primary'} size="sm" />
          <span>LYNX 生成</span>
          <small>小程序代码</small>
        </button>
        
        {/* Web 生成 */}
        <button
          onClick={() => onTypeChange('web')}
          className={`type-selector-btn ${selectedType === 'web' ? 'active' : ''}`}
          disabled={disabled}
        >
          <Icon type="web" color={selectedType === 'web' ? 'white' : 'primary'} size="sm" />
          <span>Web 生成</span>
          <small>HTML 代码</small>
        </button>
      </div>
      
      {/* 说明文字 */}
      <div className="type-description">
        {selectedType === 'auto' && (
          <p className="text-sm text-gray-600">
            系统将自动分析内容类型并选择最适合的处理方式
          </p>
        )}
        {selectedType === 'lynx' && (
          <p className="text-sm text-gray-600">
            生成 LYNX 小程序代码，支持文件提取和 Playground 预览
          </p>
        )}
        {selectedType === 'web' && (
          <p className="text-sm text-gray-600">
            生成 HTML/CSS/JS 代码，支持 iframe 预览和 CDN 下载
          </p>
        )}
      </div>
    </div>
  );
};
```

#### 3.1.2 样式设计
```css
/* src/routes/batch_processor/components/ContentTypeSelector.module.css */

.content-type-selector {
  background: var(--color-white);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid var(--color-gray-200);
}

.type-selector-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 8px;
  background: var(--color-white);
  border: 2px solid var(--color-gray-200);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.type-selector-btn:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.type-selector-btn.active {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

.auto-detected {
  position: absolute;
  top: -8px;
  right: -8px;
  display: flex;
  align-items: center;
  gap: 2px;
  background: var(--color-success);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 12px;
}

.confidence {
  font-weight: bold;
}

.type-description {
  margin-top: 8px;
  padding: 8px;
  background: var(--color-gray-50);
  border-radius: 6px;
}
```

### 3.2 HTML 结果卡片

#### 3.2.1 组件设计
```typescript
// src/routes/batch_processor/components/HTMLResultCard.tsx

interface HTMLResultCardProps {
  result: ProcessResult & {
    htmlData: {
      cdnUrl: string;
      downloadUrl: string;
      previewContent: string;
      fileCount: number;
    };
  };
  index: number;
  onPreview: (url: string) => void;
  onDownload: (url: string) => void;
}

const HTMLResultCard: React.FC<HTMLResultCardProps> = ({
  result,
  index,
  onPreview,
  onDownload
}) => {
  const { htmlData, query, status } = result;
  
  return (
    <div className="html-result-card">
      {/* 卡片头部 */}
      <div className="card-header">
        <div className="card-index">#{index + 1}</div>
        <div className="card-type">
          <Icon type="web" color="primary" size="sm" />
          <span>HTML</span>
        </div>
        <div className="card-status">
          <Icon 
            type={status === 'success' ? 'check' : 'error'} 
            color={status === 'success' ? 'success' : 'error'} 
            size="sm" 
          />
        </div>
      </div>
      
      {/* 查询内容 */}
      <div className="card-query">
        <p className="query-text">{query}</p>
      </div>
      
      {/* HTML 预览 */}
      {status === 'success' && htmlData.previewContent && (
        <div className="card-preview">
          <div className="preview-header">
            <Icon type="eye" color="primary" size="sm" />
            <span>实时预览</span>
            <span className="file-count">{htmlData.fileCount} 个文件</span>
          </div>
          <div 
            className="preview-iframe-container"
            dangerouslySetInnerHTML={{
              __html: HTMLRenderService.generateIframePreview(htmlData.previewContent)
            }}
          />
        </div>
      )}
      
      {/* 操作按钮 */}
      {status === 'success' && (
        <div className="card-actions">
          <button
            onClick={() => onPreview(htmlData.cdnUrl)}
            className="btn-authority btn-secondary-glass flex items-center gap-2"
          >
            <Icon type="external-link" color="primary" size="sm" />
            <span>在线预览</span>
          </button>
          
          <button
            onClick={() => onDownload(htmlData.downloadUrl)}
            className="btn-authority btn-primary-gold flex items-center gap-2"
          >
            <Icon type="download" color="white" size="sm" />
            <span>下载代码</span>
          </button>
        </div>
      )}
      
      {/* 错误信息 */}
      {status === 'error' && (
        <div className="card-error">
          <Icon type="error" color="error" size="sm" />
          <span>{result.error || '处理失败'}</span>
        </div>
      )}
    </div>
  );
};
```

## 4. 数据流设计

### 4.1 双轨道数据流

```typescript
// src/routes/batch_processor/services/EnhancedBatchProcessorService.ts (修改)

export class EnhancedBatchProcessorService {
  // 扩展处理方法，支持内容类型路由
  private async processQuery(
    query: string,
    jobId: string,
    priority: JobPriority,
    forceType?: 'lynx' | 'html'
  ): Promise<ProcessResult> {
    
    // 1. 确定处理类型
    const contentType = forceType || this.determineContentType(query);
    
    // 2. 选择对应的 Workflow ID
    const workflowId = this.getWorkflowId(contentType);
    
    // 3. 调用 AI API
    const response = await this.callAIApi(messages, query, workflowId);
    
    // 4. 根据类型选择解析策略
    if (contentType === 'html') {
      return this.processHTMLResponse(response, query, jobId);
    } else {
      return this.processLynxResponse(response, query, jobId); // 现有逻辑
    }
  }
  
  private determineContentType(query: string): 'lynx' | 'html' {
    // 使用 ContentClassifier 进行分类
    const classification = ContentClassifier.classifyContent(query);
    
    if (classification.type === 'html') {
      return 'html';
    } else if (classification.type === 'lynx') {
      return 'lynx';
    } else if (classification.type === 'mixed') {
      // 混合内容，选择置信度更高的类型
      return classification.details.htmlScore > classification.details.lynxScore 
        ? 'html' : 'lynx';
    } else {
      // 未知内容，默认使用 LYNX（保持兼容性）
      return 'lynx';
    }
  }
  
  private getWorkflowId(contentType: 'lynx' | 'html'): string {
    const WORKFLOW_IDS = {
      lynx: 'fc02f6eb-26db-4c63-be62-483ab8abce34',
      html: 'ce03397a-001f-47de-96a2-c648f05d8668'
    };
    
    return WORKFLOW_IDS[contentType];
  }
  
  private async processHTMLResponse(
    response: string, 
    query: string, 
    jobId: string
  ): Promise<ProcessResult> {
    
    // 1. 解析流数据
    const parsedContent = parseStreamData(response);
    
    // 2. 提取 HTML 代码
    const { extractHTMLCode } = require('../utils/streamParser');
    const extractResult = extractHTMLCode(parsedContent);
    
    if (!extractResult.success) {
      throw new Error(`HTML代码提取失败: ${extractResult.error}`);
    }
    
    // 3. 使用 HTML 处理器处理
    const htmlProcessor = new HTMLProcessor();
    const processResult = await htmlProcessor.processHTMLContent(
      extractResult.extractedContent
    );
    
    if (!processResult.success) {
      throw new Error(`HTML处理失败: ${processResult.error}`);
    }
    
    // 4. 构建结果对象
    return {
      id: jobId,
      query,
      status: 'success',
      startTime: Date.now(),
      endTime: Date.now(),
      // HTML 专用字段
      htmlData: {
        cdnUrl: processResult.cdnUrl!,
        downloadUrl: processResult.downloadUrl!,
        previewContent: processResult.previewContent!,
        fileCount: processResult.metadata!.fileCount
      },
      metadata: processResult.metadata
    };
  }
}
```

### 4.2 结果数据结构

```typescript
// src/routes/batch_processor/types/index.ts (扩展)

export interface ProcessResult {
  id: string;
  query: string;
  status: 'success' | 'error' | 'processing' | 'pending';
  startTime: number;
  endTime?: number;
  processTime?: number;
  error?: string;
  
  // LYNX 相关字段（现有）
  playgroundUrl?: string;
  
  // HTML 相关字段（新增）
  htmlData?: {
    cdnUrl: string;
    downloadUrl: string;
    previewContent: string;
    fileCount: number;
  };
  
  // 通用元数据
  metadata?: {
    contentType?: 'lynx' | 'html';
    fileCount?: number;
    totalSize?: number;
    compressionRatio?: number;
    extractedContent?: string;
  };
}
```

## 5. 配置管理

### 5.1 Workflow ID 配置

```typescript
// src/routes/batch_processor/constants/workflows.ts (新增)

export const WORKFLOW_CONFIG = {
  // LYNX 处理工作流
  LYNX: {
    workflowId: 'fc02f6eb-26db-4c63-be62-483ab8abce34',
    name: 'LYNX 代码生成',
    description: '生成 LYNX 小程序代码，支持 Playground 预览',
    features: ['文件提取', 'Playground 上传', '代码验证']
  },
  
  // HTML 处理工作流
  HTML: {
    workflowId: 'ce03397a-001f-47de-96a2-c648f05d8668',
    name: 'Web 代码生成', 
    description: '生成 HTML/CSS/JS 代码，支持 iframe 预览',
    features: ['iframe 渲染', 'CDN 上传', '直接下载']
  }
};

export const getWorkflowConfig = (type: 'lynx' | 'html') => {
  return type === 'lynx' ? WORKFLOW_CONFIG.LYNX : WORKFLOW_CONFIG.HTML;
};
```

### 5.2 功能开关配置

```typescript
// src/routes/batch_processor/config/features.ts (新增)

export interface FeatureFlags {
  enableHTMLGeneration: boolean;
  enableAutoDetection: boolean;
  enableHTMLPreview: boolean;
  enableHTMLDownload: boolean;
  defaultContentType: 'lynx' | 'html' | 'auto';
}

export const DEFAULT_FEATURES: FeatureFlags = {
  enableHTMLGeneration: true,
  enableAutoDetection: true,
  enableHTMLPreview: true,
  enableHTMLDownload: true,
  defaultContentType: 'auto'
};

// 环境配置
export const getFeatureFlags = (): FeatureFlags => {
  if (process.env.NODE_ENV === 'development') {
    return {
      ...DEFAULT_FEATURES,
      // 开发环境可以启用所有功能
    };
  }
  
  return {
    ...DEFAULT_FEATURES,
    // 生产环境的配置
  };
};
```

## 6. 安全设计

### 6.1 HTML 内容安全

```typescript
// src/routes/batch_processor/security/HTMLSanitizer.ts (新增)

export class HTMLSanitizer {
  // XSS 防护规则
  private static readonly XSS_PATTERNS = [
    /<script[^>]*>[\s\S]*?<\/script>/gi,
    /on\w+\s*=\s*["'][^"']*["']/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /data:text\/html/gi
  ];
  
  // 允许的 HTML 标签
  private static readonly ALLOWED_TAGS = [
    'div', 'span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'strong', 'em', 'ul', 'ol', 'li', 'a', 'img', 'br', 'hr',
    'table', 'tr', 'td', 'th', 'tbody', 'thead', 'tfoot',
    'form', 'input', 'button', 'select', 'option', 'textarea'
  ];
  
  static sanitizeHTML(content: string): string {
    let sanitized = content;
    
    // 移除危险的脚本和事件处理器
    this.XSS_PATTERNS.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });
    
    // 验证标签白名单（可选）
    if (this.shouldValidateTags()) {
      sanitized = this.validateHTMLTags(sanitized);
    }
    
    return sanitized;
  }
  
  private static shouldValidateTags(): boolean {
    return process.env.NODE_ENV === 'production';
  }
  
  private static validateHTMLTags(content: string): string {
    // 实现标签白名单验证逻辑
    // 这里可以使用 DOMParser 或正则表达式
    return content; // 简化实现
  }
}
```

### 6.2 上传安全

```typescript
// src/routes/batch_processor/security/UploadValidator.ts (新增)

export class UploadValidator {
  // 文件大小限制
  private static readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private static readonly MAX_TOTAL_SIZE = 20 * 1024 * 1024; // 20MB
  
  // 允许的文件类型
  private static readonly ALLOWED_EXTENSIONS = [
    '.html', '.htm', '.css', '.js', '.json',
    '.png', '.jpg', '.jpeg', '.gif', '.svg',
    '.ico', '.woff', '.woff2', '.ttf'
  ];
  
  static validateFiles(files: { [path: string]: string }): ValidationResult {
    const errors: string[] = [];
    let totalSize = 0;
    
    // 检查文件数量
    if (Object.keys(files).length > 50) {
      errors.push('文件数量不能超过 50 个');
    }
    
    // 检查每个文件
    for (const [path, content] of Object.entries(files)) {
      // 检查文件扩展名
      const ext = this.getFileExtension(path);
      if (!this.ALLOWED_EXTENSIONS.includes(ext)) {
        errors.push(`不支持的文件类型: ${ext}`);
      }
      
      // 检查文件大小
      const fileSize = new Blob([content]).size;
      if (fileSize > this.MAX_FILE_SIZE) {
        errors.push(`文件 ${path} 大小超过限制 (${fileSize} > ${this.MAX_FILE_SIZE})`);
      }
      
      totalSize += fileSize;
    }
    
    // 检查总大小
    if (totalSize > this.MAX_TOTAL_SIZE) {
      errors.push(`总文件大小超过限制 (${totalSize} > ${this.MAX_TOTAL_SIZE})`);
    }
    
    return {
      valid: errors.length === 0,
      errors,
      totalSize
    };
  }
  
  private static getFileExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.');
    return lastDot === -1 ? '' : filename.substring(lastDot).toLowerCase();
  }
}

interface ValidationResult {
  valid: boolean;
  errors: string[];
  totalSize: number;
}
```

## 7. 性能优化

### 7.1 内容检测优化

```typescript
// src/routes/batch_processor/utils/PerformanceOptimizer.ts (新增)

export class PerformanceOptimizer {
  // 内容检测缓存
  private static detectionCache = new Map<string, ClassificationResult>();
  private static readonly CACHE_SIZE = 100;
  
  static getDetectionResult(content: string): ClassificationResult | null {
    const hash = this.generateContentHash(content);
    return this.detectionCache.get(hash) || null;
  }
  
  static cacheDetectionResult(content: string, result: ClassificationResult): void {
    const hash = this.generateContentHash(content);
    
    // LRU 缓存清理
    if (this.detectionCache.size >= this.CACHE_SIZE) {
      const firstKey = this.detectionCache.keys().next().value;
      this.detectionCache.delete(firstKey);
    }
    
    this.detectionCache.set(hash, result);
  }
  
  private static generateContentHash(content: string): string {
    // 简单的哈希函数，生产环境可以使用更复杂的算法
    let hash = 0;
    for (let i = 0; i < Math.min(content.length, 1000); i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为 32-bit 整数
    }
    return hash.toString();
  }
}
```

### 7.2 批量处理优化

```typescript
// 在 EnhancedBatchProcessorService 中添加性能优化

export class EnhancedBatchProcessorService {
  // 批量内容分类，避免重复检测
  private async batchClassifyContent(queries: string[]): Promise<Map<string, 'lynx' | 'html'>> {
    const results = new Map<string, 'lynx' | 'html'>();
    
    // 使用 Promise.all 并行处理
    const classifications = await Promise.all(
      queries.map(async (query) => {
        // 尝试从缓存获取
        const cached = PerformanceOptimizer.getDetectionResult(query);
        if (cached) {
          return { query, type: cached.type === 'html' ? 'html' : 'lynx' };
        }
        
        // 执行分类
        const result = ContentClassifier.classifyContent(query);
        
        // 缓存结果
        PerformanceOptimizer.cacheDetectionResult(query, result);
        
        return { 
          query, 
          type: result.type === 'html' ? 'html' : 'lynx' 
        };
      })
    );
    
    // 构建结果映射
    classifications.forEach(({ query, type }) => {
      results.set(query, type);
    });
    
    return results;
  }
}
```

## 8. 测试策略

### 8.1 单元测试

```typescript
// src/routes/batch_processor/tests/ContentClassifier.test.ts

describe('ContentClassifier', () => {
  describe('HTML 内容检测', () => {
    test('应该正确识别完整的 HTML 文档', () => {
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head><title>Test</title></head>
        <body><div>Hello World</div></body>
        </html>
      `;
      
      const result = ContentClassifier.classifyContent(htmlContent);
      expect(result.type).toBe('html');
      expect(result.confidence).toBeGreaterThan(0.8);
    });
    
    test('应该正确识别 HTML 片段', () => {
      const htmlFragment = `
        <div class="container">
          <h1>标题</h1>
          <p style="color: red;">内容</p>
        </div>
      `;
      
      const result = ContentClassifier.classifyContent(htmlFragment);
      expect(result.type).toBe('html');
    });
  });
  
  describe('LYNX 内容检测', () => {
    test('应该正确识别 LYNX 代码', () => {
      const lynxContent = `
        <view class="container">
          <text bindtap="handleTap">{{title}}</text>
          <scroll-view enable-scroll="true">
            <view tt:for="{{list}}" tt:key="index">
              {{item}}
            </view>
          </scroll-view>
        </view>
      `;
      
      const result = ContentClassifier.classifyContent(lynxContent);
      expect(result.type).toBe('lynx');
      expect(result.confidence).toBeGreaterThan(0.8);
    });
  });
  
  describe('混合内容处理', () => {
    test('应该处理包含 HTML 和 LYNX 特征的混合内容', () => {
      const mixedContent = `
        <div class="html-container">
          <view class="lynx-view">
            <text>{{message}}</text>
          </view>
        </div>
      `;
      
      const result = ContentClassifier.classifyContent(mixedContent);
      expect(result.type).toBe('mixed');
    });
  });
});
```

### 8.2 集成测试

```typescript
// src/routes/batch_processor/tests/HTMLProcessor.test.ts

describe('HTMLProcessor', () => {
  let processor: HTMLProcessor;
  
  beforeEach(() => {
    processor = new HTMLProcessor();
  });
  
  test('应该正确处理完整的 HTML 项目', async () => {
    const htmlProject = `
      <FILE path="index.html">
        <!DOCTYPE html>
        <html>
        <head>
          <title>Test App</title>
          <link rel="stylesheet" href="style.css">
        </head>
        <body>
          <div id="app">Hello World</div>
          <script src="script.js"></script>
        </body>
        </html>
      </FILE>
      
      <FILE path="style.css">
        body { font-family: Arial; }
        #app { color: blue; }
      </FILE>
      
      <FILE path="script.js">
        document.getElementById('app').onclick = function() {
          alert('Clicked!');
        };
      </FILE>
    `;
    
    const result = await processor.processHTMLContent(htmlProject);
    
    expect(result.success).toBe(true);
    expect(result.files).toHaveProperty('index.html');
    expect(result.files).toHaveProperty('style.css');
    expect(result.files).toHaveProperty('script.js');
    expect(result.metadata?.fileCount).toBe(3);
  });
});
```

## 9. 部署和运维

### 9.1 功能开关部署

```typescript
// 逐步启用功能的部署策略

// Phase 1: 只启用内容检测，不改变处理流程
const PHASE_1_CONFIG = {
  enableHTMLGeneration: false,
  enableAutoDetection: true,
  enableHTMLPreview: false,
  enableHTMLDownload: false,
  defaultContentType: 'lynx'
};

// Phase 2: 启用 HTML 处理，但作为可选功能
const PHASE_2_CONFIG = {
  enableHTMLGeneration: true,
  enableAutoDetection: true,
  enableHTMLPreview: true,
  enableHTMLDownload: false,
  defaultContentType: 'auto'
};

// Phase 3: 全功能启用
const PHASE_3_CONFIG = {
  enableHTMLGeneration: true,
  enableAutoDetection: true,
  enableHTMLPreview: true,
  enableHTMLDownload: true,
  defaultContentType: 'auto'
};
```

### 9.2 监控和告警

```typescript
// src/routes/batch_processor/monitoring/HTMLProcessingMonitor.ts

export class HTMLProcessingMonitor {
  // 关键指标监控
  static trackClassificationAccuracy(predicted: string, actual: string): void {
    // 记录分类准确率
  }
  
  static trackProcessingPerformance(contentType: 'lynx' | 'html', duration: number): void {
    // 记录处理性能
  }
  
  static trackErrorRate(contentType: 'lynx' | 'html', success: boolean): void {
    // 记录错误率
  }
  
  // 告警规则
  static checkAlerts(): void {
    // 检查性能下降
    // 检查错误率上升
    // 检查内存使用
  }
}
```

---

**文档版本**：v1.0  
**创建时间**：2025-06-21  
**更新时间**：2025-06-21  
**技术负责人**：Claude Code  
**架构审核**：待审核