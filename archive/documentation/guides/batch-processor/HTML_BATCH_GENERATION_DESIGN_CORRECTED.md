# HTML 批量生成功能技术设计方案（修正版）

## 设计概述

基于**单一工作流 + 智能后处理**架构，为 LYNX 批量生成器添加 HTML 代码批量生成功能。核心理念：
- **统一入口**：固定 Workflow ID，所有请求使用相同的 AI 服务
- **PE 驱动**：完全依赖用户提示词决定生成内容类型
- **智能分流**：根据 AI 返回的实际内容进行后处理分流
- **保持兼容**：现有 LYNX 功能零影响

## 1. 修正后的系统架构

### 1.1 整体架构图

```mermaid
flowchart TD
    A[用户查询 + 系统提示词] --> B[统一 AI 服务调用]
    B --> C[Workflow ID: fc02f6eb-26db-4c63-be62-483ab8abce34]
    C --> D[AI 生成内容]
    D --> E[流数据解析]
    E --> F[智能内容分析器]
    
    F --> G{内容类型识别}
    G -->|LYNX 特征明显| H[LYNX 处理管道]
    G -->|HTML 特征明显| I[HTML 处理管道]
    G -->|混合特征| J[混合内容处理]
    G -->|不确定| K[默认 LYNX 处理]
    
    H --> L[现有 LYNX 流程]
    L --> M[Playground 上传]
    M --> N[LYNX 结果卡片]
    
    I --> O[HTML 文件提取]
    O --> P[CDN 压缩上传]
    P --> Q[iframe 预览生成]
    Q --> R[HTML 结果卡片]
    
    J --> S[内容分割处理]
    S --> T[混合结果展示]
```

### 1.2 关键架构原则

```typescript
// 核心配置 - 永久不变
export const CORE_CONFIG = {
  WORKFLOW_ID: 'fc02f6eb-26db-4c63-be62-483ab8abce34', // 静态常量
  API_ENDPOINT: 'https://gen-ui.bytedance.net/agent/api/apaas/v2/chat',
  
  // 处理策略
  PROCESSING_STRATEGY: 'content-driven', // 内容驱动而非配置驱动
  FALLBACK_MODE: 'lynx', // 不确定时默认 LYNX 处理
};
```

## 2. 核心组件重新设计

### 2.1 统一处理服务 (EnhancedBatchProcessorService)

#### 2.1.1 修正后的处理流程
```typescript
// src/routes/batch_processor/services/EnhancedBatchProcessorService.ts (修改)

export class EnhancedBatchProcessorService {
  // 固定的 Workflow ID - 永远不变
  private static readonly WORKFLOW_ID = 'fc02f6eb-26db-4c63-be62-483ab8abce34';
  
  private async processQuery(
    query: string,
    jobId: string,
    priority: JobPriority
  ): Promise<ProcessResult> {
    
    console.log(`[BatchProcessor] 开始处理查询: ${query.substring(0, 50)}...`);
    
    // 1. 使用固定 Workflow ID 调用 AI（与现有逻辑相同）
    const response = await this.callAIApi([
      { role: 'system', content: this.systemPrompt },
      { role: 'user', content: query }
    ], query);
    
    // 2. 解析流数据（复用现有逻辑）
    const parsedContent = parseStreamData(response);
    if (!parsedContent) {
      throw new Error('流数据解析失败');
    }
    
    // 3. 智能分析内容类型（新增）
    const contentAnalysis = ContentAnalyzer.analyzeContent(parsedContent);
    console.log(`[BatchProcessor] 内容分析结果:`, contentAnalysis);
    
    // 4. 根据分析结果选择处理管道
    switch (contentAnalysis.recommendedProcessing) {
      case 'html-pipeline':
        return this.processAsHTML(parsedContent, query, jobId, contentAnalysis);
        
      case 'mixed-pipeline':
        return this.processAsMixed(parsedContent, query, jobId, contentAnalysis);
        
      case 'lynx-pipeline':
      default:
        // 使用现有的 LYNX 处理逻辑，保持完全兼容
        return this.processAsLynx(parsedContent, query, jobId);
    }
  }
  
  // 现有 LYNX 处理逻辑保持不变
  private async processAsLynx(
    parsedContent: string, 
    query: string, 
    jobId: string
  ): Promise<ProcessResult> {
    // 完全复用现有的 LYNX 处理逻辑
    const extractResult = extractLynxCode(parsedContent);
    // ... 现有处理流程
    
    return {
      id: jobId,
      query,
      status: 'success',
      contentType: 'lynx',
      playgroundUrl: playgroundUrl, // 现有字段保持
      metadata: extractResult.metadata
    };
  }
  
  // 新增 HTML 处理逻辑
  private async processAsHTML(
    parsedContent: string,
    query: string, 
    jobId: string,
    analysis: ContentAnalysisResult
  ): Promise<ProcessResult> {
    console.log(`[BatchProcessor] 使用 HTML 处理管道`);
    
    try {
      // 1. 提取 HTML 代码（新增）
      const extractResult = extractHTMLCode(parsedContent);
      if (!extractResult.success) {
        throw new Error(`HTML代码提取失败: ${extractResult.error}`);
      }
      
      // 2. 解析文件结构
      const htmlProcessor = new HTMLProcessor();
      const files = htmlProcessor.extractHTMLFiles(extractResult.extractedContent);
      
      if (Object.keys(files).length === 0) {
        throw new Error('未找到有效的HTML文件');
      }
      
      // 3. 上传到 CDN
      const uploadResult = await htmlProcessor.uploadHTMLToCDN(files);
      if (!uploadResult.success) {
        throw new Error(`CDN上传失败: ${uploadResult.error}`);
      }
      
      // 4. 生成预览内容
      const previewContent = htmlProcessor.generatePreviewContent(files);
      
      // 5. 构建结果对象
      return {
        id: jobId,
        query,
        status: 'success',
        contentType: 'html',
        // HTML 专用字段
        htmlData: {
          cdnUrl: uploadResult.cdnUrl,
          downloadUrl: uploadResult.downloadUrl,
          previewContent,
          files,
          fileCount: Object.keys(files).length
        },
        metadata: {
          ...extractResult.metadata,
          totalSize: Object.values(files).reduce((sum, content) => sum + content.length, 0)
        }
      };
      
    } catch (error) {
      console.warn(`[BatchProcessor] HTML 处理失败，回退到 LYNX 处理:`, error);
      // 回退到 LYNX 处理
      return this.processAsLynx(parsedContent, query, jobId);
    }
  }
}
```

### 2.2 内容分析器 (ContentAnalyzer)

#### 2.2.1 智能内容识别
```typescript
// src/routes/batch_processor/services/ContentAnalyzer.ts (新增)

export interface ContentAnalysisResult {
  type: 'lynx' | 'html' | 'mixed' | 'unknown';
  confidence: number;
  recommendedProcessing: 'lynx-pipeline' | 'html-pipeline' | 'mixed-pipeline';
  details: {
    lynxScore: number;
    htmlScore: number;
    foundFeatures: {
      lynx: string[];
      html: string[];
    };
  };
}

export class ContentAnalyzer {
  // HTML 特征指标
  private static readonly HTML_INDICATORS = {
    // 强 HTML 特征 (权重 0.4)
    structure: [
      '<!DOCTYPE html', '<html', '<head>', '<body>', '<meta',
      '<title>', '</html>', '</head>', '</body>'
    ],
    
    // HTML 标签 (权重 0.3) 
    tags: [
      '<div', '<span', '<p>', '<h1>', '<h2>', '<h3>', 
      '<section', '<article', '<nav', '<header', '<footer',
      '<button', '<input', '<form', '<table', '<ul', '<ol', '<li'
    ],
    
    // HTML 属性 (权重 0.2)
    attributes: [
      'class=', 'id=', 'style=', 'href=', 'src=', 'alt=',
      'data-', 'aria-', 'onclick=', 'onload=', 'placeholder='
    ],
    
    // CSS 特征 (权重 0.1)
    styles: [
      '<style>', '</style>', 'background:', 'color:', 'font-',
      'margin:', 'padding:', 'display:', 'position:', 'width:', 'height:'
    ],
    
    // JavaScript 特征 (权重 0.1)
    scripts: [
      '<script>', '</script>', 'document.', 'window.', 'console.',
      'addEventListener', 'getElementById', 'querySelector'
    ]
  };
  
  // LYNX 特征指标（复用现有 LynxValidator 逻辑）
  private static readonly LYNX_INDICATORS = {
    structure: ['<FILES>', '<FILE'], // 权重 0.4
    tags: ['<view', '<text', '<image', '<scroll-view', '<list'], // 权重 0.3
    attributes: ['tt:for', 'tt:if', 'bindtap', 'catchtap'], // 权重 0.2
    styles: ['rpx', 'enable-scroll', 'scroll-x'], // 权重 0.1
    scripts: ['Card(', 'setData', 'onLoad', 'onShow'] // 权重 0.1
  };
  
  static analyzeContent(content: string): ContentAnalysisResult {
    console.log(`[ContentAnalyzer] 开始分析内容类型，长度: ${content.length}`);
    
    // 计算 HTML 特征分数
    const htmlScore = this.calculateFeatureScore(content, this.HTML_INDICATORS);
    
    // 计算 LYNX 特征分数
    const lynxScore = this.calculateFeatureScore(content, this.LYNX_INDICATORS);
    
    console.log(`[ContentAnalyzer] 特征分数 - HTML: ${htmlScore.toFixed(3)}, LYNX: ${lynxScore.toFixed(3)}`);
    
    // 决策逻辑
    let type: ContentAnalysisResult['type'];
    let recommendedProcessing: ContentAnalysisResult['recommendedProcessing'];
    let confidence: number;
    
    if (htmlScore > 0.75 && lynxScore < 0.3) {
      // 明显的 HTML 内容
      type = 'html';
      recommendedProcessing = 'html-pipeline';
      confidence = htmlScore;
    } else if (lynxScore > 0.75 && htmlScore < 0.3) {
      // 明显的 LYNX 内容  
      type = 'lynx';
      recommendedProcessing = 'lynx-pipeline';
      confidence = lynxScore;
    } else if (htmlScore > 0.4 && lynxScore > 0.4) {
      // 混合内容
      type = 'mixed';
      recommendedProcessing = 'mixed-pipeline';
      confidence = Math.max(htmlScore, lynxScore);
    } else if (htmlScore > lynxScore && htmlScore > 0.5) {
      // HTML 略胜
      type = 'html';
      recommendedProcessing = 'html-pipeline';
      confidence = htmlScore;
    } else {
      // 默认或 LYNX 略胜，使用 LYNX 处理（保持兼容性）
      type = lynxScore > 0.2 ? 'lynx' : 'unknown';
      recommendedProcessing = 'lynx-pipeline';
      confidence = Math.max(lynxScore, 0.1);
    }
    
    console.log(`[ContentAnalyzer] 分析结果: ${type} (${confidence.toFixed(3)}) -> ${recommendedProcessing}`);
    
    return {
      type,
      confidence,
      recommendedProcessing,
      details: {
        lynxScore,
        htmlScore,
        foundFeatures: {
          lynx: this.getFoundFeatures(content, this.LYNX_INDICATORS),
          html: this.getFoundFeatures(content, this.HTML_INDICATORS)
        }
      }
    };
  }
  
  private static calculateFeatureScore(
    content: string, 
    indicators: typeof this.HTML_INDICATORS
  ): number {
    const weights = {
      structure: 0.4,
      tags: 0.3,
      attributes: 0.2,
      styles: 0.1,
      scripts: 0.1
    };
    
    let totalScore = 0;
    let maxScore = 0;
    
    Object.entries(indicators).forEach(([category, features]) => {
      const weight = weights[category as keyof typeof weights] || 0.1;
      const foundFeatures = features.filter(feature => 
        content.toLowerCase().includes(feature.toLowerCase())
      );
      
      const categoryScore = Math.min(foundFeatures.length / features.length, 1) * weight;
      totalScore += categoryScore;
      maxScore += weight;
    });
    
    return maxScore > 0 ? totalScore / maxScore : 0;
  }
  
  private static getFoundFeatures(
    content: string,
    indicators: typeof this.HTML_INDICATORS
  ): string[] {
    const found: string[] = [];
    
    Object.values(indicators).flat().forEach(feature => {
      if (content.toLowerCase().includes(feature.toLowerCase())) {
        found.push(feature);
      }
    });
    
    return found.slice(0, 10); // 限制返回数量
  }
}
```

### 2.3 HTML 处理器 (HTMLProcessor)

#### 2.3.1 HTML 文件提取和处理
```typescript
// src/routes/batch_processor/services/HTMLProcessor.ts (新增)

export interface HTMLProcessResult {
  success: boolean;
  cdnUrl?: string;
  downloadUrl?: string;
  previewContent?: string;
  files?: { [path: string]: string };
  error?: string;
  metadata?: {
    fileCount: number;
    totalSize: number;
    compressionRatio: number;
  };
}

export class HTMLProcessor {
  async uploadHTMLToCDN(files: { [path: string]: string }): Promise<HTMLProcessResult> {
    try {
      console.log(`[HTMLProcessor] 开始上传 ${Object.keys(files).length} 个文件到 CDN`);
      
      // 复用现有的上传服务，但配置为 HTML 模式
      const uploadService = new UploadService({
        enableLogging: true,
        mockMode: false,
        timeout: 180000,
        maxRetries: 3,
        retryInterval: 2000,
      });
      
      // 压缩优化（HTML 专用）
      const optimizedFiles = await this.optimizeHTMLFiles(files);
      
      // 使用现有的 CDN 上传逻辑
      const uploadResult = await uploadService.uploadToCDN(optimizedFiles);
      
      if (!uploadResult.success) {
        throw new Error(uploadResult.error || 'CDN 上传失败');
      }
      
      // 生成 HTML 专用的下载链接（不需要 Playground）
      const downloadUrl = `${uploadResult.cdnUrl}/download`;
      
      return {
        success: true,
        cdnUrl: uploadResult.cdnUrl,
        downloadUrl,
        files: optimizedFiles,
        metadata: {
          fileCount: Object.keys(optimizedFiles).length,
          totalSize: Object.values(optimizedFiles).reduce((sum, content) => sum + content.length, 0),
          compressionRatio: uploadResult.size ? 
            Object.values(files).reduce((sum, content) => sum + content.length, 0) / uploadResult.size : 1
        }
      };
      
    } catch (error) {
      console.error('[HTMLProcessor] CDN 上传失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
  
  extractHTMLFiles(content: string): { [path: string]: string } {
    console.log(`[HTMLProcessor] 开始提取 HTML 文件，内容长度: ${content.length}`);
    
    // 复用现有的文件解析逻辑（与 LYNX 相同的格式支持）
    const { parseLynxCodeStructure } = require('../utils/streamParser');
    let files = parseLynxCodeStructure(content);
    
    if (Object.keys(files).length === 0) {
      // 如果没有找到文件结构，尝试单文件模式
      files = this.parseSingleHTMLFile(content);
    }
    
    // HTML 专用的文件类型处理
    files = this.processHTMLFileTypes(files);
    
    console.log(`[HTMLProcessor] 提取完成，共 ${Object.keys(files).length} 个文件`);
    Object.keys(files).forEach(path => {
      console.log(`[HTMLProcessor] 文件: ${path} (${files[path].length} 字符)`);
    });
    
    return files;
  }
  
  private parseSingleHTMLFile(content: string): { [path: string]: string } {
    const files: { [path: string]: string } = {};
    
    // 检测是否是完整的 HTML 文档
    if (content.includes('<html') || content.includes('<!DOCTYPE')) {
      files['index.html'] = content;
    }
    // 检测是否是 HTML 片段，自动补全
    else if (content.includes('<div') || content.includes('<span') || content.includes('<p>')) {
      files['index.html'] = this.wrapHTMLFragment(content);
    }
    // 纯 CSS 内容
    else if (content.includes('{') && content.includes('}') && 
             (content.includes('color:') || content.includes('background:'))) {
      files['style.css'] = content;
    }
    // 纯 JavaScript 内容
    else if (content.includes('function') || content.includes('const ') || content.includes('document.')) {
      files['script.js'] = content;
    }
    // 默认作为 HTML 处理
    else {
      files['index.html'] = this.wrapHTMLFragment(content);
    }
    
    return files;
  }
  
  private wrapHTMLFragment(fragment: string): string {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML 预览</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 20px;
            color: #333;
        }
    </style>
</head>
<body>
    ${fragment}
</body>
</html>`;
  }
  
  private processHTMLFileTypes(files: { [path: string]: string }): { [path: string]: string } {
    const processed: { [path: string]: string } = {};
    
    Object.entries(files).forEach(([path, content]) => {
      // 根据内容自动推断文件扩展名
      let finalPath = path;
      
      if (!path.includes('.')) {
        // 没有扩展名，根据内容推断
        if (content.includes('<html') || content.includes('<!DOCTYPE') || 
            content.includes('<div') || content.includes('<body>')) {
          finalPath = `${path}.html`;
        } else if (content.includes('{') && content.includes('}') && 
                  (content.includes('color:') || content.includes('background:'))) {
          finalPath = `${path}.css`;
        } else if (content.includes('function') || content.includes('document.')) {
          finalPath = `${path}.js`;
        } else {
          finalPath = `${path}.html`;
        }
      }
      
      processed[finalPath] = content;
    });
    
    return processed;
  }
  
  generatePreviewContent(files: { [path: string]: string }): string {
    // 查找主 HTML 文件
    let mainHTML = files['index.html'] || files['main.html'];
    
    if (!mainHTML) {
      // 查找任意 HTML 文件
      const htmlFiles = Object.entries(files).filter(([path]) => 
        path.endsWith('.html') || path.endsWith('.htm')
      );
      
      if (htmlFiles.length > 0) {
        mainHTML = htmlFiles[0][1];
      }
    }
    
    if (!mainHTML) {
      // 如果没有 HTML 文件，创建一个简单的预览
      return this.createSimplePreview(files);
    }
    
    // 内联 CSS 和 JS（用于预览）
    let previewHTML = mainHTML;
    
    // 内联 CSS
    Object.entries(files).forEach(([path, content]) => {
      if (path.endsWith('.css')) {
        const cssFileName = path.split('/').pop();
        if (previewHTML.includes(`href="${cssFileName}"`) || 
            previewHTML.includes(`href="./${cssFileName}"`)) {
          previewHTML = previewHTML.replace(
            new RegExp(`<link[^>]*href=["'](?:\\.\/)?${cssFileName}["'][^>]*>`, 'g'),
            `<style>\n${content}\n</style>`
          );
        }
      }
    });
    
    // 内联 JavaScript
    Object.entries(files).forEach(([path, content]) => {
      if (path.endsWith('.js')) {
        const jsFileName = path.split('/').pop();
        if (previewHTML.includes(`src="${jsFileName}"`) || 
            previewHTML.includes(`src="./${jsFileName}"`)) {
          previewHTML = previewHTML.replace(
            new RegExp(`<script[^>]*src=["'](?:\\.\/)?${jsFileName}["'][^>]*></script>`, 'g'),
            `<script>\n${content}\n</script>`
          );
        }
      }
    });
    
    return previewHTML;
  }
  
  private createSimplePreview(files: { [path: string]: string }): string {
    const fileList = Object.entries(files).map(([path, content]) => {
      const preview = content.length > 200 ? content.substring(0, 200) + '...' : content;
      return `
        <div style="margin-bottom: 20px; border: 1px solid #ddd; border-radius: 8px; padding: 15px;">
          <h3 style="margin: 0 0 10px 0; color: #333;">${path}</h3>
          <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px;"><code>${this.escapeHTML(preview)}</code></pre>
        </div>
      `;
    }).join('');
    
    return `
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>文件预览</title>
          <style>
              body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 20px; }
              h1 { color: #333; }
          </style>
      </head>
      <body>
          <h1>📁 文件内容预览</h1>
          ${fileList}
      </body>
      </html>
    `;
  }
  
  private escapeHTML(content: string): string {
    return content
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }
  
  private async optimizeHTMLFiles(files: { [path: string]: string }): Promise<{ [path: string]: string }> {
    // HTML 专用的优化逻辑
    const optimized: { [path: string]: string } = {};
    
    Object.entries(files).forEach(([path, content]) => {
      let optimizedContent = content;
      
      if (path.endsWith('.html') || path.endsWith('.htm')) {
        // HTML 优化：移除多余空白，但保持可读性
        optimizedContent = this.optimizeHTML(content);
      } else if (path.endsWith('.css')) {
        // CSS 优化：移除注释和多余空白
        optimizedContent = this.optimizeCSS(content);
      } else if (path.endsWith('.js')) {
        // JS 优化：基础的空白处理
        optimizedContent = this.optimizeJS(content);
      }
      
      optimized[path] = optimizedContent;
    });
    
    return optimized;
  }
  
  private optimizeHTML(html: string): string {
    return html
      .replace(/<!--[\s\S]*?-->/g, '') // 移除注释
      .replace(/\s+/g, ' ') // 压缩空白
      .replace(/> </g, '><') // 移除标签间空白
      .trim();
  }
  
  private optimizeCSS(css: string): string {
    return css
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
      .replace(/\s+/g, ' ') // 压缩空白
      .replace(/; /g, ';') // 移除分号后空格
      .replace(/ {/g, '{') // 移除花括号前空格
      .trim();
  }
  
  private optimizeJS(js: string): string {
    // 基础的 JS 优化，避免破坏语法
    return js
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
      .replace(/\/\/.*$/gm, '') // 移除行注释
      .replace(/\n\s*\n/g, '\n') // 移除空行
      .trim();
  }
}
```

### 2.4 增强的流解析器 (streamParser)

#### 2.4.1 HTML 代码提取功能
```typescript
// src/routes/batch_processor/utils/streamParser.js (增强)

/**
 * 从流数据中提取 HTML 代码
 * 与现有的 extractLynxCode 并行存在
 */
function extractHTMLCode(content) {
  try {
    const startTime = performance.now();
    
    console.log('[streamParser] 🔍 开始提取 HTML 代码');
    console.log(`[streamParser] 📊 输入内容长度: ${content.length} 字符`);
    
    let extractedContent = '';
    let strategy = '';
    
    // 策略1: 提取 html/html5 代码块
    const htmlCodeBlocks = extractCodeBlocks(content, 'html');
    if (htmlCodeBlocks.length > 0) {
      extractedContent = htmlCodeBlocks.length === 1 
        ? htmlCodeBlocks[0] 
        : htmlCodeBlocks.join('\n\n');
      strategy = 'html代码块';
      console.log(`[streamParser] ✅ 策略1成功: 找到 ${htmlCodeBlocks.length} 个html代码块`);
    } else {
      // 策略2: 提取其他 Web 相关代码块
      const webLanguages = ['html5', 'css', 'javascript', 'js'];
      let found = false;
      
      for (const lang of webLanguages) {
        const blocks = extractCodeBlocks(content, lang);
        if (blocks.length > 0) {
          extractedContent = blocks.length === 1 ? blocks[0] : blocks.join('\n\n');
          strategy = `${lang}代码块`;
          console.log(`[streamParser] ✅ 策略2成功: 找到 ${blocks.length} 个${lang}代码块`);
          found = true;
          break;
        }
      }
      
      // 策略3: 直接内容识别（HTML 特征）
      if (!found && hasHTMLFeatures(content)) {
        extractedContent = content;
        strategy = '直接HTML内容识别';
        console.log('[streamParser] ✅ 策略3成功: 直接使用内容作为HTML代码');
      }
    }
    
    if (!extractedContent) {
      console.warn('[streamParser] ⚠️ 所有策略都失败，未找到任何可识别的HTML内容');
      return {
        success: false,
        error: 'No HTML content found'
      };
    }
    
    const endTime = performance.now();
    console.log('[streamParser] ✅ 提取 HTML 代码成功!');
    console.log(`[streamParser] 📊 使用策略: ${strategy}`);
    console.log(`[streamParser] 📏 提取内容长度: ${extractedContent.length} 字符`);
    console.log(`[streamParser] ⏱️ 处理耗时: ${(endTime - startTime).toFixed(2)}ms`);
    
    return {
      success: true,
      extractedContent,
      metadata: {
        strategy,
        codeBlocks: 1,
        totalLength: extractedContent.length,
        processingTime: endTime - startTime,
      },
    };
    
  } catch (error) {
    console.error('[streamParser] ❌ 提取 HTML 代码失败:', error);
    return {
      success: false,
      error: error.message || String(error),
    };
  }
}

/**
 * 检测内容是否包含 HTML 特征
 */
function hasHTMLFeatures(content) {
  const htmlIndicators = [
    '<!DOCTYPE html', '<html', '<head>', '<body>', '<meta',
    '<div', '<span', '<p>', '<h1>', '<h2>', '<h3>',
    'class=', 'id=', 'style=', 'href=', 'src=',
    '<style>', 'background:', 'color:', 'font-',
    '<script>', 'document.', 'window.', 'addEventListener'
  ];
  
  const lowerContent = content.toLowerCase();
  const foundIndicators = htmlIndicators.filter(indicator => 
    lowerContent.includes(indicator.toLowerCase())
  );
  
  console.log('[streamParser] 🔍 HTML特征检测:', foundIndicators);
  
  // 需要至少找到3个HTML特征才认为是HTML内容
  return foundIndicators.length >= 3;
}

// 导出新函数（保持向下兼容）
module.exports = {
  parseStreamData,
  extractCodeBlocks,
  extractLynxCode, // 现有
  extractHTMLCode, // 新增
  parseLynxCodeStructure,
  hasHTMLFeatures, // 新增
};
```

## 3. 用户界面设计

### 3.1 提示词模板管理

#### 3.1.1 提示词分类选择器
```typescript
// src/routes/batch_processor/components/PromptTemplateSelector.tsx (新增)

interface PromptTemplate {
  id: string;
  name: string;
  category: 'lynx' | 'web' | 'general';
  template: string;
  description: string;
  tags: string[];
}

interface PromptTemplateSelectorProps {
  currentPrompt: string;
  onPromptChange: (prompt: string) => void;
  onTemplateApply: (template: PromptTemplate) => void;
}

const PromptTemplateSelector: React.FC<PromptTemplateSelectorProps> = ({
  currentPrompt,
  onPromptChange,
  onTemplateApply
}) => {
  const [selectedCategory, setSelectedCategory] = useState<'lynx' | 'web' | 'general'>('general');
  const [templates] = useState<PromptTemplate[]>(getBuiltinTemplates());
  
  const filteredTemplates = templates.filter(t => 
    selectedCategory === 'general' || t.category === selectedCategory
  );
  
  return (
    <div className="prompt-template-selector">
      {/* 分类标签 */}
      <div className="template-categories mb-4">
        <div className="flex gap-2">
          <button
            onClick={() => setSelectedCategory('general')}
            className={`category-btn ${selectedCategory === 'general' ? 'active' : ''}`}
          >
            <Icon type="settings" size="sm" />
            通用模板
          </button>
          <button
            onClick={() => setSelectedCategory('lynx')}
            className={`category-btn ${selectedCategory === 'lynx' ? 'active' : ''}`}
          >
            <Icon type="mobile" size="sm" />
            LYNX 模板
          </button>
          <button
            onClick={() => setSelectedCategory('web')}
            className={`category-btn ${selectedCategory === 'web' ? 'active' : ''}`}
          >
            <Icon type="web" size="sm" />
            Web 模板
          </button>
        </div>
      </div>
      
      {/* 模板列表 */}
      <div className="template-list space-y-2">
        {filteredTemplates.map(template => (
          <div key={template.id} className="template-item">
            <div className="template-header">
              <h4 className="template-name">{template.name}</h4>
              <button
                onClick={() => onTemplateApply(template)}
                className="btn-authority btn-secondary-glass text-xs"
              >
                应用模板
              </button>
            </div>
            <p className="template-description text-sm text-gray-600">
              {template.description}
            </p>
            <div className="template-tags flex gap-1 mt-2">
              {template.tags.map(tag => (
                <span key={tag} className="tag">
                  {tag}
                </span>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// 内置模板定义
function getBuiltinTemplates(): PromptTemplate[] {
  return [
    {
      id: 'lynx-basic',
      name: 'LYNX 基础组件',
      category: 'lynx',
      template: `请生成LYNX小程序代码，要求：
1. 使用TTML标签（view, text, image等）
2. 使用TTSS样式（支持rpx单位）
3. 包含JavaScript逻辑（Card函数）
4. 使用tt:for, tt:if等LYNX特有语法
5. 代码结构清晰，注释详细

生成内容应该包含完整的LYNX组件代码。`,
      description: '生成标准的LYNX小程序组件，包含TTML、TTSS和JavaScript',
      tags: ['LYNX', '小程序', '基础组件']
    },
    {
      id: 'web-basic',
      name: 'HTML 网页组件',
      category: 'web',
      template: `请生成HTML网页代码，要求：
1. 使用标准HTML标签（div, span, p等）
2. 包含CSS样式（支持现代CSS特性）
3. 包含JavaScript交互逻辑
4. 响应式设计，适配移动端
5. 代码规范，结构清晰

生成内容应该包含完整的HTML、CSS和JavaScript代码。`,
      description: '生成标准的HTML网页组件，包含HTML、CSS和JavaScript',
      tags: ['HTML', '网页', '响应式']
    },
    {
      id: 'web-react',
      name: 'React 组件',
      category: 'web',
      template: `请生成React组件代码，要求：
1. 使用函数组件和Hooks
2. 包含TypeScript类型定义
3. 使用CSS Modules或Styled Components
4. 组件可复用，props类型完整
5. 包含必要的错误处理

生成内容应该是完整的React组件代码。`,
      description: '生成现代React组件，包含TypeScript和样式',
      tags: ['React', 'TypeScript', '组件']
    }
  ];
}
```

### 3.2 自适应结果展示

#### 3.2.1 统一结果卡片组件
```typescript
// src/routes/batch_processor/components/UnifiedResultCard.tsx (新增)

interface UnifiedResultCardProps {
  result: ProcessResult;
  index: number;
  onPreview: (result: ProcessResult) => void;
  onDownload: (result: ProcessResult) => void;
}

const UnifiedResultCard: React.FC<UnifiedResultCardProps> = ({
  result,
  index,
  onPreview,
  onDownload
}) => {
  const { contentType = 'lynx', status } = result;
  
  const getContentTypeIcon = () => {
    switch (contentType) {
      case 'html': return 'web';
      case 'lynx': return 'mobile';
      case 'mixed': return 'layers';
      default: return 'code';
    }
  };
  
  const getContentTypeLabel = () => {
    switch (contentType) {
      case 'html': return 'HTML';
      case 'lynx': return 'LYNX';
      case 'mixed': return '混合';
      default: return '代码';
    }
  };
  
  return (
    <div className={`unified-result-card status-${status} type-${contentType}`}>
      {/* 卡片头部 */}
      <div className="card-header">
        <div className="card-index">#{index + 1}</div>
        <div className="card-type">
          <Icon type={getContentTypeIcon()} color="primary" size="sm" />
          <span>{getContentTypeLabel()}</span>
        </div>
        <div className="card-status">
          <Icon 
            type={status === 'success' ? 'check' : status === 'error' ? 'error' : 'clock'} 
            color={status === 'success' ? 'success' : status === 'error' ? 'error' : 'warning'} 
            size="sm" 
          />
        </div>
      </div>
      
      {/* 查询内容 */}
      <div className="card-query">
        <p className="query-text">{result.query}</p>
      </div>
      
      {/* 内容预览 */}
      {status === 'success' && (
        <div className="card-preview">
          {contentType === 'html' && result.htmlData ? (
            <HTMLPreview htmlData={result.htmlData} />
          ) : contentType === 'lynx' && result.playgroundUrl ? (
            <LynxPreview playgroundUrl={result.playgroundUrl} />
          ) : contentType === 'mixed' ? (
            <MixedPreview result={result} />
          ) : (
            <DefaultPreview result={result} />
          )}
        </div>
      )}
      
      {/* 操作按钮 */}
      {status === 'success' && (
        <div className="card-actions">
          <button
            onClick={() => onPreview(result)}
            className="btn-authority btn-secondary-glass flex items-center gap-2"
          >
            <Icon type="eye" color="primary" size="sm" />
            <span>预览</span>
          </button>
          
          <button
            onClick={() => onDownload(result)}
            className="btn-authority btn-primary-gold flex items-center gap-2"
          >
            <Icon type="download" color="white" size="sm" />
            <span>
              {contentType === 'html' ? '下载代码' : '打开链接'}
            </span>
          </button>
        </div>
      )}
      
      {/* 错误信息 */}
      {status === 'error' && (
        <div className="card-error">
          <Icon type="error" color="error" size="sm" />
          <span>{result.error || '处理失败'}</span>
        </div>
      )}
    </div>
  );
};

// HTML 预览组件
const HTMLPreview: React.FC<{ htmlData: any }> = ({ htmlData }) => (
  <div className="html-preview">
    <div className="preview-header">
      <Icon type="web" color="primary" size="sm" />
      <span>HTML 预览</span>
      <span className="file-count">{htmlData.fileCount} 个文件</span>
    </div>
    <div 
      className="preview-iframe-container"
      dangerouslySetInnerHTML={{
        __html: generateSafeIframe(htmlData.previewContent)
      }}
    />
  </div>
);

// LYNX 预览组件（现有逻辑）
const LynxPreview: React.FC<{ playgroundUrl: string }> = ({ playgroundUrl }) => (
  <div className="lynx-preview">
    <div className="preview-header">
      <Icon type="mobile" color="primary" size="sm" />
      <span>LYNX 预览</span>
    </div>
    <div className="preview-placeholder">
      <Icon type="external-link" color="gray" size="lg" />
      <p>点击下方按钮访问 Playground 预览</p>
    </div>
  </div>
);

// 安全 iframe 生成
function generateSafeIframe(content: string): string {
  return `
    <iframe 
      sandbox="allow-scripts allow-same-origin"
      style="width: 100%; height: 300px; border: 1px solid #ddd; border-radius: 8px;"
      srcdoc="${content.replace(/"/g, '&quot;')}"
      loading="lazy">
    </iframe>
  `;
}
```

## 4. 数据流和状态管理

### 4.1 扩展的结果数据结构

```typescript
// src/routes/batch_processor/types/index.ts (扩展)

export interface ProcessResult {
  id: string;
  query: string;
  status: 'success' | 'error' | 'processing' | 'pending';
  startTime: number;
  endTime?: number;
  processTime?: number;
  error?: string;
  
  // 内容类型标识（新增）
  contentType?: 'lynx' | 'html' | 'mixed' | 'unknown';
  
  // LYNX 相关字段（现有，保持兼容）
  playgroundUrl?: string;
  
  // HTML 相关字段（新增）
  htmlData?: {
    cdnUrl: string;
    downloadUrl: string;
    previewContent: string;
    files: { [path: string]: string };
    fileCount: number;
  };
  
  // 混合内容字段（新增）
  mixedData?: {
    lynxPart?: {
      playgroundUrl: string;
      extractedFiles: string[];
    };
    htmlPart?: {
      cdnUrl: string;
      downloadUrl: string;
      previewContent: string;
      files: { [path: string]: string };
    };
  };
  
  // 通用元数据
  metadata?: {
    extractedContent?: string;
    fileCount?: number;
    totalSize?: number;
    compressionRatio?: number;
    contentAnalysis?: ContentAnalysisResult;
  };
}
```

### 4.2 状态管理增强

```typescript
// src/routes/batch_processor/hooks/useBatchProcessor.ts (修改)

export function useBatchProcessor() {
  // 现有状态保持不变
  const [results, setResults] = useState<ProcessResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState<BatchProgress>({
    total: 0,
    completed: 0,
    failed: 0,
    percentage: 0
  });
  
  // 新增：内容类型统计
  const [contentTypeStats, setContentTypeStats] = useState({
    lynx: 0,
    html: 0,
    mixed: 0,
    unknown: 0
  });
  
  // 处理结果更新
  const handleResultUpdate = useCallback((result: ProcessResult) => {
    setResults(prev => {
      const updated = [...prev];
      const index = updated.findIndex(r => r.id === result.id);
      
      if (index >= 0) {
        updated[index] = result;
      } else {
        updated.push(result);
      }
      
      // 更新内容类型统计
      const stats = { lynx: 0, html: 0, mixed: 0, unknown: 0 };
      updated.forEach(r => {
        if (r.status === 'success' && r.contentType) {
          stats[r.contentType]++;
        }
      });
      setContentTypeStats(stats);
      
      return updated;
    });
  }, []);
  
  // 现有的其他方法保持不变
  const start = useCallback((queries: string[], systemPrompt: string, config: BatchConfig) => {
    // 使用现有逻辑，但确保 Workflow ID 固定
    const service = new EnhancedBatchProcessorService({
      ...config,
      api: {
        ...config.api,
        workflowId: 'fc02f6eb-26db-4c63-be62-483ab8abce34' // 强制使用固定 ID
      }
    });
    
    service.setSystemPrompt(systemPrompt);
    service.onResultUpdate(handleResultUpdate);
    // ... 其他现有逻辑
  }, [handleResultUpdate]);
  
  return {
    start,
    stop,
    results,
    progress,
    contentTypeStats, // 新增
    isRunning,
    // ... 其他现有返回值
  };
}
```

## 5. 配置和部署

### 5.1 环境配置

```typescript
// src/routes/batch_processor/config/environment.ts (新增)

export interface EnvironmentConfig {
  workflowId: string;
  apiEndpoint: string;
  enableHTMLProcessing: boolean;
  enableContentAnalysis: boolean;
  enableMixedContent: boolean;
  cdnUploadEndpoint: string;
  maxFileSize: number;
  maxTotalSize: number;
}

export const getEnvironmentConfig = (): EnvironmentConfig => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return {
    // 核心配置 - 永远不变
    workflowId: 'fc02f6eb-26db-4c63-be62-483ab8abce34',
    apiEndpoint: 'https://gen-ui.bytedance.net/agent/api/apaas/v2/chat',
    
    // 功能开关
    enableHTMLProcessing: true,
    enableContentAnalysis: true,
    enableMixedContent: isDevelopment, // 开发环境启用混合内容
    
    // CDN 配置
    cdnUploadEndpoint: process.env.CDN_UPLOAD_ENDPOINT || 'https://cdn.example.com/upload',
    
    // 限制配置
    maxFileSize: 5 * 1024 * 1024, // 5MB
    maxTotalSize: 20 * 1024 * 1024, // 20MB
  };
};
```

### 5.2 渐进式部署策略

```typescript
// src/routes/batch_processor/config/featureFlags.ts (新增)

export interface FeatureFlags {
  htmlProcessingEnabled: boolean;
  contentAnalysisEnabled: boolean;
  htmlPreviewEnabled: boolean;
  cdnUploadEnabled: boolean;
  mixedContentEnabled: boolean;
}

export const getFeatureFlags = (): FeatureFlags => {
  // 可以通过环境变量或远程配置控制
  return {
    htmlProcessingEnabled: process.env.ENABLE_HTML_PROCESSING !== 'false',
    contentAnalysisEnabled: process.env.ENABLE_CONTENT_ANALYSIS !== 'false',
    htmlPreviewEnabled: process.env.ENABLE_HTML_PREVIEW !== 'false',
    cdnUploadEnabled: process.env.ENABLE_CDN_UPLOAD !== 'false',
    mixedContentEnabled: process.env.ENABLE_MIXED_CONTENT === 'true',
  };
};

// 使用示例
export const useFeatureFlags = () => {
  const [flags] = useState(getFeatureFlags());
  return flags;
};
```

## 6. 监控和调试

### 6.1 内容分析监控

```typescript
// src/routes/batch_processor/monitoring/ContentAnalysisMonitor.ts (新增)

export class ContentAnalysisMonitor {
  private static stats = {
    totalAnalyzed: 0,
    lynxDetected: 0,
    htmlDetected: 0,
    mixedDetected: 0,
    unknownDetected: 0,
    accuracyScore: 0
  };
  
  static recordAnalysis(analysis: ContentAnalysisResult, actualType?: string) {
    this.stats.totalAnalyzed++;
    
    switch (analysis.type) {
      case 'lynx':
        this.stats.lynxDetected++;
        break;
      case 'html':
        this.stats.htmlDetected++;
        break;
      case 'mixed':
        this.stats.mixedDetected++;
        break;
      case 'unknown':
        this.stats.unknownDetected++;
        break;
    }
    
    // 如果有实际类型，计算准确率
    if (actualType && actualType === analysis.type) {
      this.stats.accuracyScore = 
        (this.stats.accuracyScore * (this.stats.totalAnalyzed - 1) + 1) / 
        this.stats.totalAnalyzed;
    }
    
    // 每100次分析输出一次统计
    if (this.stats.totalAnalyzed % 100 === 0) {
      console.log('[ContentAnalysisMonitor] 统计报告:', this.stats);
    }
  }
  
  static getStats() {
    return { ...this.stats };
  }
  
  static resetStats() {
    this.stats = {
      totalAnalyzed: 0,
      lynxDetected: 0,
      htmlDetected: 0,
      mixedDetected: 0,
      unknownDetected: 0,
      accuracyScore: 0
    };
  }
}
```

---

**修正说明**：本设计方案基于正确的单一工作流架构重新设计，确保：
1. 使用固定的 Workflow ID，不因内容类型而改变
2. 完全依赖用户提示词驱动内容生成
3. 通过智能后处理实现内容类型的识别和分流
4. 保持现有 LYNX 功能完全不受影响
5. 提供渐进式的功能部署和开关控制

**文档版本**：v2.0 (修正版)  
**创建时间**：2025-06-21  
**更新时间**：2025-06-21  
**技术负责人**：Claude Code