# HTML 批量生成功能需求文档

## 项目概述

为现有的 LYNX 批量生成器扩展 HTML 代码批量生成功能，实现双轨道处理架构：
- **LYNX 轨道**：保持现有功能不变，继续支持 LYNX 代码生成、文件提取、Playground 上传
- **HTML 轨道**：新增 HTML 代码生成，直接使用 iframe 渲染卡片，CDN 上传及预览

## 1. 功能需求

### 1.1 核心功能
- **智能内容识别**：自动检测流数据内容类型（LYNX vs HTML）
- **双轨道处理**：根据内容类型选择对应的处理流程
- **HTML 渲染预览**：使用 iframe 直接渲染 HTML 代码卡片
- **CDN 上传管理**：HTML 代码压缩上传到 CDN，生成下载链接
- **分类按钮界面**：用户可手动选择处理类型（LYNX / Web）

### 1.2 具体功能特性

#### 1.2.1 HTML 内容检测
- 自动识别 HTML 标签（`<html>`, `<div>`, `<body>`, `<head>` 等）
- 检测 CSS 样式代码（`style=`, `<style>`, `.class`, `#id` 等）
- 识别 JavaScript 代码（`<script>`, `document.`, `window.`, `addEventListener` 等）
- 检测前端框架特征（React JSX, Vue 模板等）

#### 1.2.2 LYNX 内容检测（现有功能保持）
- 识别 TTML 标签（`<view>`, `<text>`, `<image>` 等）
- 检测 LYNX 特有属性（`tt:for`, `tt:if`, `bindtap` 等）
- 识别 TTSS 样式（`rpx`, `enable-scroll` 等）
- 检测 LYNX JavaScript（`Card()`, `setData`, `onLoad` 等）

#### 1.2.3 HTML 处理流程
1. **流数据解析**：提取 HTML 代码内容
2. **代码压缩**：优化 HTML/CSS/JS 代码（可选）
3. **CDN 上传**：将代码包上传到 CDN 存储
4. **iframe 渲染**：在卡片中直接渲染 HTML 预览
5. **链接生成**：提供 CDN 下载链接，无需 Playground

#### 1.2.4 用户界面增强
- **双分类按钮**：
  - 🎨 **Web 生成**：HTML/CSS/JS 代码生成
  - 📱 **LYNX 生成**：LYNX 代码生成（现有功能）
- **智能默认选择**：根据提示词内容智能预选类型
- **手动切换**：用户可随时切换处理类型

## 2. 技术需求

### 2.1 架构设计

#### 2.1.1 内容分类系统
```typescript
interface ContentClassifier {
  detectContentType(content: string): 'lynx' | 'html' | 'mixed' | 'unknown';
  getLynxConfidence(content: string): number; // 0-1
  getHTMLConfidence(content: string): number; // 0-1
  getClassificationDetails(content: string): ClassificationResult;
}
```

#### 2.1.2 双轨道处理架构
```typescript
interface DualTrackProcessor {
  // LYNX 轨道（现有）
  processLynxContent(content: string): Promise<LynxResult>;
  
  // HTML 轨道（新增）
  processHTMLContent(content: string): Promise<HTMLResult>;
  
  // 统一入口
  processContent(content: string, forceType?: 'lynx' | 'html'): Promise<ProcessResult>;
}
```

#### 2.1.3 Workflow ID 管理
```typescript
const WORKFLOW_CONFIG = {
  LYNX_GENERATION: 'fc02f6eb-26db-4c63-be62-483ab8abce34', // 现有
  WEB_GENERATION: 'ce03397a-001f-47de-96a2-c648f05d8668',  // HTML 使用
  AUTO_DETECT: 'dynamic' // 根据内容动态选择
};
```

### 2.2 HTML 处理管道

#### 2.2.1 HTML 内容检测器
```typescript
class HTMLContentDetector {
  static HTML_INDICATORS = {
    // HTML 结构标识符
    structure: ['<!DOCTYPE html', '<html', '<head>', '<body>', '<meta'],
    
    // HTML 标签
    tags: ['<div', '<span', '<p>', '<h1>', '<h2>', '<h3>', '<h4>', '<h5>', '<h6>'],
    
    // HTML 属性
    attributes: ['class=', 'id=', 'style=', 'data-', 'src=', 'href='],
    
    // CSS 相关
    styles: ['<style>', '.class', '#id', 'background:', 'color:', 'font-'],
    
    // JavaScript 相关
    scripts: ['<script>', 'document.', 'window.', 'addEventListener', 'querySelector'],
    
    // 前端框架
    frameworks: ['React.', 'Vue.', 'Angular.', 'componentDidMount', 'useState']
  };
  
  static detectHTMLFeatures(content: string): HTMLDetectionResult;
}
```

#### 2.2.2 HTML 文件解析器
```typescript
class HTMLFileExtractor {
  // 支持多种文件格式
  extractFiles(content: string): { [filename: string]: string };
  
  // 支持的文件分隔格式
  // 1. <FILE path="index.html">...</FILE>
  // 2. **index.html** \n```html ... ```
  // 3. // --- index.html --- \n...
  // 4. 单文件模式（自动推断文件类型）
}
```

#### 2.2.3 HTML 渲染服务
```typescript
class HTMLRenderService {
  // iframe 渲染配置
  generateIframePreview(htmlContent: string): string;
  
  // 安全处理
  sanitizeHTML(content: string): string;
  
  // 预览优化
  optimizeForPreview(content: string): string;
}
```

### 2.3 CDN 上传增强

#### 2.3.1 HTML 专用上传服务
```typescript
class HTMLUploadService extends UploadService {
  // HTML 文件压缩
  compressHTMLFiles(files: { [path: string]: string }): Promise<CompressResult>;
  
  // CDN 上传（不需要 Playground）
  uploadHTMLToCDN(files: { [path: string]: string }): Promise<{
    success: boolean;
    cdnUrl: string;
    downloadUrl: string; // 直接下载链接
    previewUrl?: string; // 可选的在线预览
  }>;
}
```

### 2.4 用户界面改进

#### 2.4.1 分类选择组件
```typescript
interface ContentTypeSelector {
  selectedType: 'lynx' | 'web' | 'auto';
  onTypeChange: (type: 'lynx' | 'web' | 'auto') => void;
  autoDetectedType?: 'lynx' | 'web';
  confidence?: number;
}
```

#### 2.4.2 结果展示增强
```typescript
interface ResultCard {
  type: 'lynx' | 'html';
  
  // LYNX 结果（现有）
  lynxData?: {
    playgroundUrl: string;
    extractedFiles: string[];
  };
  
  // HTML 结果（新增）
  htmlData?: {
    cdnUrl: string;
    downloadUrl: string;
    previewContent: string;
    fileCount: number;
  };
}
```

## 3. 实现规范

### 3.1 兼容性要求

#### 3.1.1 现有功能保护
- **零影响原则**：所有现有 LYNX 功能必须完全保持不变
- **向下兼容**：现有的 LYNX 处理流程不能被修改或破坏
- **数据隔离**：HTML 和 LYNX 的数据流完全分离
- **配置独立**：HTML 相关配置不影响 LYNX 配置

#### 3.1.2 渐进式实现
1. **第一阶段**：添加 HTML 内容检测，但仍使用 LYNX 流程处理
2. **第二阶段**：实现 HTML 专用处理流程
3. **第三阶段**：添加用户界面分类选择
4. **第四阶段**：优化和性能调优

### 3.2 性能要求

#### 3.2.1 检测性能
- 内容类型检测时间 < 50ms
- 支持大文件检测（最大 10MB）
- 内存使用优化，避免重复解析

#### 3.2.2 处理性能
- HTML 渲染预览生成 < 500ms
- CDN 上传与现有 LYNX 上传性能相当
- 并发处理能力保持现有水平（默认 5 个并发）

### 3.3 安全要求

#### 3.3.1 HTML 内容安全
- XSS 防护：iframe sandbox 限制
- 内容过滤：移除潜在恶意脚本
- 域名限制：CDN 链接域名白名单

#### 3.3.2 上传安全
- 文件大小限制：单文件最大 5MB
- 文件类型验证：只允许 HTML/CSS/JS/图片
- 内容扫描：基础的恶意代码检测

## 4. 业务逻辑

### 4.1 自动分类逻辑

#### 4.1.1 分类决策树
```
输入内容
    ├─ 包含 LYNX 特征 (confidence > 0.7)
    │   └─ 选择 LYNX 轨道
    ├─ 包含 HTML 特征 (confidence > 0.7)
    │   └─ 选择 HTML 轨道
    ├─ 混合内容 (both > 0.3)
    │   ├─ LYNX 特征更强 → LYNX 轨道
    │   └─ HTML 特征更强 → HTML 轨道
    └─ 未明确识别
        └─ 默认 LYNX 轨道（保持兼容）
```

#### 4.1.2 用户干预机制
- 用户可手动覆盖自动检测结果
- 提供"强制 LYNX"和"强制 HTML"选项
- 记住用户偏好，为相似内容提供建议

### 4.2 批量处理增强

#### 4.2.1 混合批次处理
- 同一批次可包含 LYNX 和 HTML 查询
- 每个查询独立分类和处理
- 结果展示区分不同类型

#### 4.2.2 统计和监控
- 分类准确率统计
- 处理成功率对比（LYNX vs HTML）
- 性能指标监控

## 5. 验收标准

### 5.1 功能验收

#### 5.1.1 核心功能
- [ ] HTML 内容自动检测准确率 > 90%
- [ ] LYNX 内容识别保持 100% 向下兼容
- [ ] HTML iframe 渲染正常显示
- [ ] CDN 上传成功率 > 95%
- [ ] 分类按钮功能正常

#### 5.1.2 边界情况
- [ ] 混合内容正确分类
- [ ] 大文件处理不崩溃
- [ ] 恶意代码安全过滤
- [ ] 网络异常恢复处理

### 5.2 性能验收
- [ ] 批量处理性能不下降
- [ ] 内存使用增长 < 20%
- [ ] UI 响应时间保持 < 100ms
- [ ] 并发处理能力不降低

### 5.3 兼容性验收
- [ ] 所有现有 LYNX 功能正常
- [ ] 历史数据完全兼容
- [ ] 配置文件向下兼容
- [ ] API 接口保持稳定

## 6. 风险评估

### 6.1 技术风险
- **内容分类错误**：影响用户体验，需要人工干预机制
- **性能下降**：新增检测逻辑可能影响处理速度
- **兼容性破坏**：修改共享代码可能影响现有功能

### 6.2 缓解措施
- 完善的自动化测试覆盖
- 分阶段部署和回滚机制
- 详细的性能监控和告警
- 用户反馈收集和快速响应

## 7. 实施计划

### 7.1 开发阶段
1. **Week 1-2**：HTML 内容检测器实现
2. **Week 3-4**：HTML 处理流程开发
3. **Week 5-6**：用户界面改进
4. **Week 7-8**：测试和优化

### 7.2 验证阶段
1. **单元测试**：每个模块独立测试
2. **集成测试**：双轨道处理流程测试
3. **性能测试**：大批量数据处理测试
4. **用户测试**：真实使用场景验证

### 7.3 部署阶段
1. **灰度发布**：小范围用户试用
2. **监控观察**：收集使用数据和反馈
3. **全量部署**：逐步扩大使用范围
4. **持续优化**：根据反馈进行改进

---

**文档版本**：v1.0  
**创建时间**：2025-06-21  
**更新时间**：2025-06-21  
**负责人**：Claude Code  
**审核状态**：待审核