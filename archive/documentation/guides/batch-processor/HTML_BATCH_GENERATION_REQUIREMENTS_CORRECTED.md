# HTML 批量生成功能需求文档（修正版）

## 项目概述

为现有的 LYNX 批量生成器扩展 HTML 代码批量生成功能，实现**智能后处理架构**：
- **统一工作流**：使用固定的 Workflow ID，不因内容类型而改变
- **PE 驱动**：完全以用户提示词为准，AI 根据提示词生成相应类型的代码
- **智能识别**：后端智能识别 AI 返回的实际内容类型（LYNX/HTML/混合）
- **差异化处理**：根据识别的内容类型选择相应的处理和渲染方式

## 1. 核心设计原则

### 1.1 单一工作流原则
- **Workflow ID**：永久使用 `fc02f6eb-26db-4c63-be62-483ab8abce34`
- **统一端点**：所有请求都发送到同一个 AI 服务端点
- **PE 决定内容**：AI 根据用户的系统提示词决定生成 LYNX 还是 HTML 代码

### 1.2 智能后处理原则
- **内容优先**：先获取 AI 返回内容，再分析内容类型
- **自适应处理**：根据实际内容类型选择最适合的处理方式
- **混合内容支持**：单个响应可能包含多种类型的代码

### 1.3 用户体验原则
- **提示词分类**：为用户提供 LYNX 和 Web 的提示词模板
- **智能预览**：根据内容类型自动选择最佳的预览方式
- **统一操作**：保持一致的批量处理操作体验

## 2. 功能需求

### 2.1 提示词管理增强

#### 2.1.1 分类提示词模板
```typescript
interface PromptTemplate {
  id: string;
  name: string;
  category: 'lynx' | 'web' | 'general';
  template: string;
  description: string;
  examples: string[];
}

// LYNX 提示词模板
const LYNX_TEMPLATES = [
  {
    id: 'lynx-basic',
    name: 'LYNX 基础组件',
    category: 'lynx',
    template: '请生成 LYNX 小程序代码，包含 TTML、TTSS 和 JavaScript。要求：...',
    description: '生成标准的 LYNX 小程序组件代码'
  }
];

// Web 提示词模板
const WEB_TEMPLATES = [
  {
    id: 'web-basic',
    name: 'HTML 网页组件',
    category: 'web', 
    template: '请生成 HTML 网页代码，包含 HTML、CSS 和 JavaScript。要求：...',
    description: '生成标准的 HTML 网页组件代码'
  }
];
```

#### 2.1.2 智能提示词建议
- 根据用户输入的查询内容，智能推荐合适的提示词模板
- 提供快速切换功能，用户可以在 LYNX 和 Web 模板间切换
- 支持自定义提示词保存和管理

### 2.2 智能内容识别

#### 2.2.1 流数据内容分析
```typescript
interface ContentAnalysisResult {
  type: 'lynx' | 'html' | 'mixed' | 'unknown';
  confidence: number;
  details: {
    lynxFeatures: string[];
    htmlFeatures: string[];
    fileTypes: string[];
  };
  recommendedProcessing: 'lynx-pipeline' | 'html-pipeline' | 'mixed-pipeline';
}
```

#### 2.2.2 混合内容处理策略
1. **内容分割**：将混合内容按文件或代码块分割
2. **分别处理**：每个部分按照其内容类型进行处理
3. **统一展示**：在结果卡片中展示不同类型的处理结果

### 2.3 HTML 专用处理管道

#### 2.3.1 HTML 内容提取
- 支持与 LYNX 相同的文件分割格式（`<FILE>`, `**filename**`, `// ---`）
- 智能识别 HTML/CSS/JS 代码块
- 自动补全不完整的 HTML 结构

#### 2.3.2 HTML 渲染预览
- 使用 iframe 安全渲染 HTML 内容
- 支持 CSS 样式和 JavaScript 交互
- 提供响应式预览（桌面/移动端视图）

#### 2.3.3 CDN 上传和分发
- 压缩 HTML/CSS/JS 文件
- 上传到 CDN 存储
- 生成直接下载链接（无需 Playground）
- 可选的在线预览链接

## 3. 技术实现需求

### 3.1 统一处理流程

```typescript
// 修正后的处理流程
class EnhancedBatchProcessorService {
  private async processQuery(query: string, jobId: string): Promise<ProcessResult> {
    // 1. 使用统一的 Workflow ID 调用 AI
    const response = await this.callAIApi(query, FIXED_WORKFLOW_ID);
    
    // 2. 解析流数据
    const parsedContent = parseStreamData(response);
    
    // 3. 智能分析内容类型
    const analysis = ContentAnalyzer.analyzeContent(parsedContent);
    
    // 4. 根据分析结果选择处理管道
    switch (analysis.recommendedProcessing) {
      case 'lynx-pipeline':
        return this.processAsLynx(parsedContent, query, jobId);
      case 'html-pipeline':
        return this.processAsHTML(parsedContent, query, jobId);
      case 'mixed-pipeline':
        return this.processAsMixed(parsedContent, query, jobId, analysis);
    }
  }
}
```

### 3.2 内容分析器

```typescript
class ContentAnalyzer {
  static analyzeContent(content: string): ContentAnalysisResult {
    const lynxScore = this.calculateLynxScore(content);
    const htmlScore = this.calculateHTMLScore(content);
    
    // 决策逻辑
    if (lynxScore > 0.8 && htmlScore < 0.3) {
      return { type: 'lynx', recommendedProcessing: 'lynx-pipeline' };
    } else if (htmlScore > 0.8 && lynxScore < 0.3) {
      return { type: 'html', recommendedProcessing: 'html-pipeline' };
    } else if (lynxScore > 0.3 && htmlScore > 0.3) {
      return { type: 'mixed', recommendedProcessing: 'mixed-pipeline' };
    } else {
      // 默认使用 LYNX 处理（保持兼容性）
      return { type: 'unknown', recommendedProcessing: 'lynx-pipeline' };
    }
  }
}
```

### 3.3 HTML 处理管道

```typescript
class HTMLProcessor {
  async processAsHTML(content: string, query: string, jobId: string): Promise<ProcessResult> {
    // 1. 提取 HTML 文件
    const files = this.extractHTMLFiles(content);
    
    // 2. 验证和优化文件
    const validatedFiles = this.validateAndOptimizeFiles(files);
    
    // 3. 上传到 CDN
    const uploadResult = await this.uploadHTMLToCDN(validatedFiles);
    
    // 4. 生成预览内容
    const previewContent = this.generatePreviewContent(validatedFiles);
    
    // 5. 构建结果
    return {
      id: jobId,
      query,
      status: 'success',
      contentType: 'html',
      htmlData: {
        cdnUrl: uploadResult.cdnUrl,
        downloadUrl: uploadResult.downloadUrl,
        previewContent,
        files: validatedFiles
      }
    };
  }
}
```

## 4. 用户界面需求

### 4.1 提示词管理界面

#### 4.1.1 分类标签页
```typescript
interface PromptManagementUI {
  // 标签页分类
  tabs: ['LYNX 模板', 'Web 模板', '通用模板', '我的模板'];
  
  // 快速切换按钮
  quickSwitch: {
    current: 'lynx' | 'web';
    onSwitch: (type: 'lynx' | 'web') => void;
  };
  
  // 模板预览和编辑
  templateEditor: {
    template: string;
    preview: string;
    onSave: () => void;
  };
}
```

#### 4.1.2 智能建议
- 在用户输入查询时，分析查询内容并推荐适合的提示词模板
- 提供一键应用模板功能
- 显示预期生成内容类型（LYNX/HTML）

### 4.2 结果展示界面

#### 4.2.1 自适应结果卡片
```typescript
interface AdaptiveResultCard {
  contentType: 'lynx' | 'html' | 'mixed';
  
  // LYNX 结果展示（现有功能保持）
  lynxView?: {
    playgroundUrl: string;
    extractedFiles: string[];
  };
  
  // HTML 结果展示
  htmlView?: {
    previewIframe: string;
    downloadUrl: string;
    fileList: string[];
  };
  
  // 混合内容展示
  mixedView?: {
    lynxPart: LynxViewData;
    htmlPart: HTMLViewData;
  };
}
```

#### 4.2.2 统一操作按钮
- **预览**：根据内容类型选择 Playground 或 iframe 预览
- **下载**：LYNX 使用 Playground 链接，HTML 使用 CDN 下载链接
- **分享**：生成适合的分享链接
- **编辑**：提供在线编辑入口（可选功能）

## 5. 配置和管理

### 5.1 静态配置

```typescript
// 系统配置常量
export const BATCH_PROCESSOR_CONFIG = {
  // 固定不变的 Workflow ID
  WORKFLOW_ID: 'fc02f6eb-26db-4c63-be62-483ab8abce34',
  
  // API 端点
  API_ENDPOINT: 'https://gen-ui.bytedance.net/agent/api/apaas/v2/chat',
  
  // 内容类型阈值
  CONTENT_TYPE_THRESHOLDS: {
    lynx_confidence: 0.8,
    html_confidence: 0.8,
    mixed_min_threshold: 0.3
  },
  
  // 处理选项
  PROCESSING_OPTIONS: {
    enable_html_processing: true,
    enable_mixed_content: true,
    default_fallback: 'lynx' // 未识别内容默认按 LYNX 处理
  }
};
```

### 5.2 提示词模板管理

```typescript
class PromptTemplateManager {
  // 预置模板
  static getBuiltinTemplates(): PromptTemplate[];
  
  // 用户自定义模板
  static getUserTemplates(): PromptTemplate[];
  static saveUserTemplate(template: PromptTemplate): void;
  static deleteUserTemplate(id: string): void;
  
  // 智能推荐
  static recommendTemplate(query: string): PromptTemplate[];
}
```

## 6. 兼容性和迁移

### 6.1 现有功能保护
- **零破坏**：所有现有的 LYNX 处理功能完全保持不变
- **默认行为**：未识别或不确定的内容默认使用 LYNX 处理流程
- **渐进增强**：HTML 功能作为增强特性，不影响核心功能

### 6.2 数据兼容性
- **历史数据**：现有的历史记录和配置完全兼容
- **结果格式**：扩展结果格式，向下兼容现有字段
- **API 接口**：保持现有 API 接口不变

## 7. 实施阶段

### 7.1 阶段一：内容识别
- 实现内容分析器
- 添加 HTML 内容识别逻辑
- 保持现有处理流程不变

### 7.2 阶段二：HTML 处理
- 实现 HTML 处理管道
- 添加 iframe 预览功能
- 实现 CDN 上传功能

### 7.3 阶段三：界面增强
- 添加提示词模板管理
- 实现自适应结果卡片
- 优化用户体验

### 7.4 阶段四：优化和扩展
- 性能优化
- 混合内容处理
- 高级功能扩展

## 8. 验收标准

### 8.1 核心功能
- [ ] 内容类型识别准确率 > 90%
- [ ] HTML iframe 渲染正常显示
- [ ] CDN 上传成功率 > 95%
- [ ] LYNX 功能完全不受影响
- [ ] 提示词模板功能正常

### 8.2 性能要求
- [ ] 内容分析时间 < 100ms
- [ ] 处理性能不低于现有水平
- [ ] 内存使用增长 < 15%

### 8.3 用户体验
- [ ] 界面响应时间 < 200ms
- [ ] 操作流程直观易用
- [ ] 错误处理友好清晰

---

**修正说明**：本文档基于单一工作流、PE 驱动的正确架构理解重新设计，确保与实际需求完全匹配。

**文档版本**：v2.0 (修正版)  
**创建时间**：2025-06-21  
**更新时间**：2025-06-21  
**负责人**：Claude Code