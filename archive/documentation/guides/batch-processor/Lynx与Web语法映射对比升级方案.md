# Lynx与Web语法映射对比升级方案 (高保真)

## 1. 目标

为解决 L2W (Lynx-to-Web) 渲染引擎的潜在保真度问题，本文档提供一份详尽、严格、可执行的组件、样式、API及事件的映射方案。该方案基于 `prompts` 文件夹内的所有技术规范，旨在消除渲染差异，确保 Web 端预览与 Lynx 原生环境在像素级和行为级上高度一致。

## 2. TTML 组件高保真映射

此映射表是 L2W 引擎的核心转换规则。所有转换必须严格遵守此处的约束。

| Lynx 组件 | Web 映射 | 属性/状态映射 | 关键约束与实现细节 |
| :--- | :--- | :--- | :--- |
| **view** | `<div>` | `class`, `style`, `id`, `bindtap` -> `onclick` | **布局**: 必须完全通过 Flexbox 实现。**事件**: 需模拟事件冒泡(`bind`)与捕获(`catch`)机制。**禁止**: 绝不能映射为 `div` 以外的任何块级元素。 |
| **text** | `<span>` | `class`, `style` | **内容**: 必须进行XML实体转义 (`&lt;`, `&gt;`, `&amp;` 等)。**嵌套**: 内部绝对禁止嵌套任何其他组件。**样式**: `line-height` 和 `text-align` 是解决垂直居中问题的关键。 |
| **image** | `<img>` | `src`, `mode` -> `object-fit`, `class`, `style` | **自闭合**: 必须作为自闭合标签处理。**mode映射**: `scaleToFill` -> `object-fit: fill;`, `aspectFit` -> `object-fit: contain;`, `aspectFill` -> `object-fit: cover;`。**懒加载**: `lazy-load` 属性需通过 `IntersectionObserver` API 实现。 |
| **scroll-view** | `<div>` | `scroll-y`/`scroll-x` -> `overflow-y`/`overflow-x: scroll`, `bindscroll` -> `onscroll` | **滚动**: 必须显式设置 `overflow` 属性。**性能**: 对于长列表，应实现虚拟滚动（Virtual Scrolling）以优化性能，模拟原生体验。 |
| **swiper** | `div` (Swiper.js) | `indicator-dots`, `autoplay`, `interval`, `circular` | **实现**: 推荐使用 [Swiper.js](https://swiperjs.com/) 库。**结构**: `swiper` 映射为 `swiper-container`，`swiper-item` 映射为 `swiper-slide`。属性需精确映射到 Swiper.js 的配置项。 |
| **input** | `<input>` | `value`, `placeholder`, `type`, `bindinput` -> `oninput` | **类型映射**: `text` -> `type="text"`, `number` -> `type="number"`, `password` -> `type="password"`。**自闭合**: 必须作为自闭合标签处理。 |
| **button** | `<button>` | `size`, `type`, `disabled`, `bindtap` -> `onclick` | **样式**: `type="primary"` 等应映射为特定的 CSS 类名（如 `.btn-primary`），而不是依赖浏览器的默认按钮样式。**替代方案**: 允许使用 `<view role="button">` 以获得更强的样式控制力。 |
| **canvas** | `<canvas>` | `canvas-id` | **API**: L2W 引擎需要提供一个 Canvas Context 的封装，使其 API (如 `createContext`) 与 Lynx 环境保持一致。**绘图**: 所有绘图指令都应直接转译为标准的 HTML5 Canvas API 调用。 |
| **lightcharts-canvas** | `<canvas>` (ECharts) | `bindinitchart`, `canvasName` | **见第 4 节：LightCharts 高保真映射方案** |

## 3. TTSS 样式高保真转换

样式的精确转换是视觉保真度的核心。L2W 引擎必须实现一个严格的 TTSS-to-CSS 转换器。

| TTSS 特性 | Web CSS 实现 | 关键约束与实现细节 |
| :--- | :--- | :--- |
| **单位 `rpx`** | `px` (基于固定宽度) | **转换逻辑**: 假定预览屏幕宽度为 `375px`，则 `1rpx = 375 / 750 = 0.5px`。所有 `rpx` 值必须在转换管道中乘以 `0.5` 并附加 `px` 单位。**禁止**: 禁止使用 `vw`，因为它会随浏览器窗口变化，导致与固定宽度的移动端设计稿不一致。 |
| **选择器** | CSS 子集 | **支持**: 仅支持类型选择器 (`view`)、类选择器 (`.class`)、ID 选择器 (`#id`)。**后代选择器**: 支持最多两级 (`.parent .child`)。**禁止**: 严格禁止通配符(`*`)、属性选择器(`[type=text]`)、多类选择器(`.a.b`)及兄弟选择器(`+`, `~`)。遇到禁用选择器时，L2W 引擎应在控制台打印警告。 |
| **样式继承** | 默认禁用 | **实现**: 为每个组件生成唯一的 `data-v-xxxxxxxx` 属性，并重写所有 CSS 规则，如 `.card` -> `.card[data-v-xxxxxxxx]`，从而实现作用域隔离，模拟 Lynx 的无继承行为。 |
| **禁用属性** | 过滤与警告 | **黑名单**: 转换器必须内置一个基于 `TTSSStrictConstraints.ts` 的黑名单。**处理**: 当遇到如 `backdrop-filter`, `display: grid`, `text-shadow`, `-webkit-*` 等禁用属性时，应直接忽略该条规则，并在控制台输出详细警告，指明违规的属性和所在的行。 |
| **Flexbox 布局** | 标准 Flexbox | **完全兼容**: TTSS 的 Flexbox 模型与 Web 标准一致，可直接映射。 |

## 4. LightCharts 高保真映射方案

基于 `LightChartPromptLoader.md`，`lightcharts-canvas` 组件的映射必须依赖 **Apache ECharts** 并遵循以下规则：

1.  **初始化流程**: 
    -   L2W 引擎在遇到 `<lightcharts-canvas>` 时，渲染一个带有 `canvas` 的 `<div>` 容器。
    -   通过 `MutationObserver` 监听 `bindinitchart` 属性的出现，触发初始化回调。
    -   在回调中，调用 `echarts.init()` 初始化图表实例。
    -   **延迟渲染**: 严格遵守文档建议，使用 `setTimeout(..., 100)` 延迟调用 `chart.setOption()`，以防止 Canvas 尚未就绪导致的渲染失败。

2.  **`setOption` 适配层**: 
    -   在调用 `echarts.setOption()` 之前，必须增加一个适配层。
    -   **强制 `useHTML: false`**: 自动检查 `option.tooltip`，如果 `useHTML` 未定义或为 `true`，则强制覆写为 `false`。
    -   **数据格式校验**: 检查 `series.data`，如果坐标轴为 `value` 类型，但数据不是二维数组 `[[x, y], ...]`，则在控制台抛出错误，阻止渲染，并提示正确的数据格式。
    -   **API 映射**: 将 `chart.on`, `chart.destroy` 等 LynxChart API 直接一对一映射到 ECharts 实例的同名方法。

## 5. API 与事件模拟

| Lynx API | Web 模拟实现 | 关键约束与实现细节 |
| :--- | :--- | :--- |
| **`Card` 对象** | JavaScript `class` | **生命周期**: L2W 引擎需在合适的时机（如组件挂载、卸载）手动调用 `onLoad`, `onShow`, `onUnload` 等模拟生命周期。**`this.setData`**: 必须实现为异步更新。调用后，将数据变更推入微任务队列，然后触发组件的重新渲染。 |
| **`lynx.request`** | `fetch` API | **封装**: 需封装 `fetch`，使其接受与 `lynx.request` 完全相同的参数对象（`url`, `method`, `success`, `fail`）。 |
| **`lynx.navigateTo`** | History API / Iframe | **实现**: 可以使用 `history.pushState` 结合页面内容动态替换，或在 `iframe` 内进行导航，以模拟页面栈。 |
| **`SystemInfo`** | 全局对象 | **数据**: 提供一个静态的 `window.SystemInfo` 对象，包含 `platform: 'web'`, `pixelRatio: window.devicePixelRatio`, `screenWidth: 375` (固定值) 等。 |
| **事件系统** | DOM Events | **`bind` vs `catch`**: `bindtap` 映射为 `element.addEventListener('click', handler, { capture: false })` (冒泡)。`catch:tap` 映射为 `element.addEventListener('click', handler, { capture: true })` (捕获)，并在处理函数中调用 `event.stopPropagation()`。 |

## 6. 结论

本升级方案通过定义详尽的映射规则和严格的约束，为 L2W 引擎的开发提供了高保真的实现蓝图。遵循此方案将最大限度地减少 Lynx 与 Web 之间的渲染差异，为 Lynx AI Studio 提供一个可靠、精确的在线预览平台。