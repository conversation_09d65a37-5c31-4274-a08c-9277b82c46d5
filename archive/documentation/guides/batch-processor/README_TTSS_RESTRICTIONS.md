# TTSS属性限制快速参考

## 🚨 重要警告

**在Lynx框架的TTSS样式文件中，严格禁止使用任何未明确允许的CSS属性！**

## ❌ 常见禁止属性

```css
/* 🚫 绝对禁止使用 */
backdrop-filter: blur(10px);    /* 背景滤镜 */
text-transform: uppercase;       /* 文本转换 */
filter: brightness(1.2);         /* 滤镜效果 */
user-select: none;              /* 用户选择 */
cursor: pointer;                /* 光标样式 */
outline: none;                  /* 轮廓 */
white-space: nowrap;            /* 空白处理 */
```

## ❌ 禁止使用伪类/伪元素

```css
/* 🚫 绝对禁止使用 */
.element:hover { }              /* 悬停状态 */
.element:focus { }              /* 焦点状态 */
.element::before { }            /* 前置伪元素 */
.element::after { }             /* 后置伪元素 */
```

## ✅ 允许使用的属性

```css
/* ✅ 推荐使用 */
display: flex;                  /* 布局 */
width: 750rpx;                  /* 尺寸(RPX) */
margin: 20rpx;                  /* 间距 */
background-color: #ffffff;      /* 背景 */
color: #333333;                 /* 文字颜色 */
font-size: 32rpx;              /* 字体大小 */
border-radius: 10rpx;          /* 圆角 */
transform: translateX(10px);    /* 变换 */
```

## 📖 完整文档

详细的限制规则请查看：[TTSS_ATTRIBUTE_RESTRICTIONS.md](./TTSS_ATTRIBUTE_RESTRICTIONS.md)

---

**开发提醒**: 违反这些限制可能导致渲染失败或不可预期的行为！