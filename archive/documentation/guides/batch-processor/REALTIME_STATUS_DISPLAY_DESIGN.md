# 实时状态展示系统详细设计

## 1. 系统架构概述

### 1.1 状态展示系统目标
- **实时反馈**：用户能够实时感知到批量处理的每个阶段
- **信息丰富**：展示 API 进度、内容转换、类型识别等详细信息
- **视觉友好**：符合主题配色，布局美观，动画流畅
- **空间限制**：严格控制在 300px 高度内，不累积旧状态

### 1.2 展示层级结构
```
实时状态容器 (300px 固定高度)
├── 状态标题栏 (40px) - 当前整体状态
├── 进度概览区 (80px) - 数量统计和进度条
├── 内容识别区 (60px) - LYNX/HTML 识别结果
├── 操作详情区 (100px) - API/解析/上传状态
└── 底部信息区 (20px) - 时间戳和额外信息
```

## 2. 组件设计规范

### 2.1 状态数据接口定义

#### 2.1.1 整体批处理状态
```typescript
interface BatchProcessingStatus {
  // 基础进度信息
  progress: {
    total: number;              // 总任务数
    completed: number;          // 已完成数（成功+失败）
    processing: number;         // 正在处理数
    pending: number;            // 等待处理数
    succeeded: number;          // 成功数
    failed: number;             // 失败数
    percentage: number;         // 总体完成百分比
    successRate: number;        // 成功率百分比
  };

  // 性能指标
  performance: {
    startTime: number;          // 开始时间戳
    currentTime: number;        // 当前时间戳
    elapsedTime: number;        // 已用时间（秒）
    estimatedRemaining: number; // 预计剩余时间（秒）
    throughput: number;         // 处理速度（任务/分钟）
    averageTaskTime: number;    // 平均单任务时间（秒）
  };

  // 当前处理任务信息
  currentTask: {
    id: string;                 // 当前任务ID
    query: string;              // 当前查询内容（截断显示）
    stage: 'api' | 'parsing' | 'analysis' | 'upload' | 'complete';
    stageProgress: number;      // 当前阶段进度 0-100
  } | null;
}
```

#### 2.1.2 内容识别状态
```typescript
interface ContentAnalysisStatus {
  // 识别结果
  detection: {
    type: 'lynx' | 'html' | 'mixed' | 'unknown' | 'analyzing';
    confidence: number;         // 置信度 0-1
    processingTime: number;     // 识别耗时（ms）
  };

  // 特征详情
  features: {
    lynxFeatures: Array<{
      name: string;             // 特征名称，如 "tt:for"
      count: number;            // 出现次数
      confidence: number;       // 该特征的置信度
    }>;
    htmlFeatures: Array<{
      name: string;             // 特征名称，如 "class="
      count: number;            // 出现次数
      confidence: number;       // 该特征的置信度
    }>;
  };

  // 处理建议
  recommendation: {
    pipeline: 'lynx-pipeline' | 'html-pipeline' | 'mixed-pipeline';
    reason: string;             // 推荐理由
    alternativeOptions: string[];// 备选方案
  };

  // 统计信息
  statistics: {
    totalAnalyzed: number;      // 已分析数量
    lynxDetected: number;       // 检测为 LYNX 的数量
    htmlDetected: number;       // 检测为 HTML 的数量
    mixedDetected: number;      // 检测为混合的数量
    analysisAccuracy: number;   // 分析准确率（如果有验证数据）
  };
}
```

#### 2.1.3 操作阶段状态
```typescript
interface OperationStageStatus {
  // API 调用阶段
  apiCall: {
    status: 'idle' | 'connecting' | 'sending' | 'receiving' | 'success' | 'error';
    progress: number;           // 0-100 进度
    startTime?: number;         // 开始时间
    duration?: number;          // 耗时（ms）
    requestSize?: number;       // 请求大小（bytes）
    responseSize?: number;      // 响应大小（bytes）
    errorMessage?: string;      // 错误信息
    retryCount?: number;        // 重试次数
  };

  // 内容解析阶段
  parsing: {
    status: 'idle' | 'extracting' | 'structuring' | 'validating' | 'success' | 'error';
    progress: number;           // 0-100 进度
    extractedBlocks: number;    // 已提取代码块数
    totalFiles: number;         // 解析出的文件总数
    currentFile?: string;       // 当前处理的文件名
    parsingStrategy?: string;   // 使用的解析策略
    errorMessage?: string;      // 错误信息
  };

  // 内容分析阶段
  analysis: {
    status: 'idle' | 'analyzing' | 'classifying' | 'success' | 'error';
    progress: number;           // 0-100 进度
    detectedType?: 'lynx' | 'html' | 'mixed';
    confidence?: number;        // 分析置信度
    featuresFound: number;      // 找到的特征数量
    processingTime?: number;    // 分析耗时
    errorMessage?: string;      // 错误信息
  };

  // 上传处理阶段
  upload: {
    status: 'idle' | 'preparing' | 'compressing' | 'uploading' | 'finalizing' | 'success' | 'error';
    progress: number;           // 0-100 进度
    originalSize?: number;      // 原始大小（bytes）
    compressedSize?: number;    // 压缩后大小（bytes）
    uploadedBytes?: number;     // 已上传字节数
    uploadSpeed?: number;       // 上传速度（bytes/s）
    cdnUrl?: string;           // CDN URL
    downloadUrl?: string;       // 下载链接
    errorMessage?: string;      // 错误信息
  };

  // 完成阶段
  completion: {
    status: 'pending' | 'generating' | 'success' | 'error';
    finalUrl?: string;          // 最终链接
    totalProcessingTime: number;// 总处理时间
    resultType: 'lynx' | 'html';// 最终结果类型
    successMessage?: string;    // 成功消息
    errorMessage?: string;      // 错误消息
  };
}
```

### 2.2 UI 组件设计

#### 2.2.1 状态标题栏组件
```typescript
interface StatusHeaderProps {
  stage: 'idle' | 'processing' | 'completed' | 'error';
  progress: number;
  currentTask?: string;
}

const StatusHeader: React.FC<StatusHeaderProps> = ({
  stage,
  progress,
  currentTask
}) => {
  const getStatusIcon = () => {
    switch (stage) {
      case 'processing':
        return (
          <div className="icon-container icon-container-primary w-4 h-4 mr-2 animate-spin">
            <svg className="icon icon-sm icon-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
          </div>
        );
      case 'completed':
        return (
          <div className="icon-container icon-container-success w-4 h-4 mr-2">
            <svg className="icon icon-sm icon-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"/>
            </svg>
          </div>
        );
      case 'error':
        return (
          <div className="icon-container icon-container-error w-4 h-4 mr-2">
            <svg className="icon icon-sm icon-error" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </div>
        );
      default:
        return (
          <div className="icon-container icon-container-gray w-4 h-4 mr-2">
            <svg className="icon icon-sm icon-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
        );
    }
  };

  const getStatusText = () => {
    switch (stage) {
      case 'processing':
        return `正在处理中... ${progress.toFixed(0)}%`;
      case 'completed':
        return '处理完成';
      case 'error':
        return '处理失败';
      default:
        return '准备就绪';
    }
  };

  return (
    <div className="flex items-center justify-between h-10 px-3 border-b border-gray-200">
      <h2 className="text-base font-medium flex items-center">
        {getStatusIcon()}
        <span className="truncate">{getStatusText()}</span>
      </h2>
      {currentTask && (
        <div className="text-xs text-gray-500 max-w-40 truncate">
          {currentTask}
        </div>
      )}
    </div>
  );
};
```

#### 2.2.2 进度概览组件
```typescript
interface ProgressOverviewProps {
  progress: BatchProcessingStatus['progress'];
  performance: BatchProcessingStatus['performance'];
}

const ProgressOverview: React.FC<ProgressOverviewProps> = ({
  progress,
  performance
}) => {
  return (
    <div className="px-3 py-2 space-y-3">
      {/* 主进度条 */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">总体进度</span>
          <span className="text-sm text-gray-600">
            {progress.completed}/{progress.total} ({progress.percentage.toFixed(0)}%)
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${progress.percentage}%` }}
          />
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-4 gap-2 text-center">
        <div className="bg-green-50 rounded-lg p-2">
          <div className="text-lg font-bold text-green-600">{progress.succeeded}</div>
          <div className="text-xs text-gray-600">成功</div>
        </div>
        <div className="bg-red-50 rounded-lg p-2">
          <div className="text-lg font-bold text-red-600">{progress.failed}</div>
          <div className="text-xs text-gray-600">失败</div>
        </div>
        <div className="bg-blue-50 rounded-lg p-2">
          <div className="text-lg font-bold text-blue-600">{progress.processing}</div>
          <div className="text-xs text-gray-600">处理中</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-2">
          <div className="text-lg font-bold text-gray-600">{progress.pending}</div>
          <div className="text-xs text-gray-600">等待</div>
        </div>
      </div>

      {/* 性能指标 */}
      <div className="flex justify-between text-xs text-gray-600">
        <span>⚡ {performance.throughput.toFixed(1)} 任务/分钟</span>
        <span>⏱️ 预计 {formatTime(performance.estimatedRemaining)}</span>
      </div>
    </div>
  );
};

function formatTime(seconds: number): string {
  if (seconds < 60) return `${seconds.toFixed(0)}秒`;
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分${seconds % 60}秒`;
  return `${Math.floor(seconds / 3600)}小时${Math.floor((seconds % 3600) / 60)}分钟`;
}
```

#### 2.2.3 内容识别展示组件
```typescript
interface ContentAnalysisDisplayProps {
  analysis: ContentAnalysisStatus;
}

const ContentAnalysisDisplay: React.FC<ContentAnalysisDisplayProps> = ({
  analysis
}) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'html':
        return '🌐';
      case 'lynx':
        return '📱';
      case 'mixed':
        return '🔄';
      case 'analyzing':
        return '🔍';
      default:
        return '❓';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  return (
    <div className="px-3 py-2 border-b border-gray-100">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-gray-700">内容识别</span>
        {analysis.detection.type !== 'analyzing' && (
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(analysis.detection.confidence)}`}>
            {getTypeIcon(analysis.detection.type)} {analysis.detection.type.toUpperCase()} {(analysis.detection.confidence * 100).toFixed(0)}%
          </div>
        )}
      </div>

      {/* 特征标签 */}
      {analysis.detection.type !== 'analyzing' && (
        <div className="flex flex-wrap gap-1 mb-2">
          {analysis.features.lynxFeatures.slice(0, 3).map((feature, index) => (
            <span key={`lynx-${index}`} className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-blue-100 text-blue-800">
              📱 {feature.name}
            </span>
          ))}
          {analysis.features.htmlFeatures.slice(0, 3).map((feature, index) => (
            <span key={`html-${index}`} className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-green-100 text-green-800">
              🌐 {feature.name}
            </span>
          ))}
        </div>
      )}

      {/* 处理建议 */}
      {analysis.recommendation && (
        <div className="text-xs text-gray-600">
          建议: {analysis.recommendation.pipeline.replace('-pipeline', '').toUpperCase()} 处理
        </div>
      )}
    </div>
  );
};
```

#### 2.2.4 操作状态详情组件
```typescript
interface OperationStatusDisplayProps {
  operations: OperationStageStatus;
}

const OperationStatusDisplay: React.FC<OperationStatusDisplayProps> = ({
  operations
}) => {
  const getStageIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <span className="text-green-500">✓</span>;
      case 'error':
        return <span className="text-red-500">✗</span>;
      case 'idle':
        return <span className="text-gray-400">○</span>;
      default:
        return <span className="text-blue-500 animate-spin">⟳</span>;
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="px-3 py-2 space-y-2">
      {/* API 调用状态 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {getStageIcon(operations.apiCall.status)}
          <span className="text-sm text-gray-700">API 调用</span>
        </div>
        <div className="text-xs text-gray-500">
          {operations.apiCall.status === 'success' && operations.apiCall.duration && (
            <span>{(operations.apiCall.duration / 1000).toFixed(1)}s</span>
          )}
          {operations.apiCall.status !== 'idle' && operations.apiCall.status !== 'success' && operations.apiCall.status !== 'error' && (
            <span>{operations.apiCall.progress}%</span>
          )}
        </div>
      </div>

      {/* 内容解析状态 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {getStageIcon(operations.parsing.status)}
          <span className="text-sm text-gray-700">内容解析</span>
        </div>
        <div className="text-xs text-gray-500">
          {operations.parsing.totalFiles > 0 && (
            <span>{operations.parsing.totalFiles} 文件</span>
          )}
          {operations.parsing.status !== 'idle' && operations.parsing.status !== 'success' && operations.parsing.status !== 'error' && (
            <span>{operations.parsing.progress}%</span>
          )}
        </div>
      </div>

      {/* 上传状态 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {getStageIcon(operations.upload.status)}
          <span className="text-sm text-gray-700">CDN 上传</span>
        </div>
        <div className="text-xs text-gray-500">
          {operations.upload.status === 'success' && operations.upload.compressedSize && (
            <span>{formatBytes(operations.upload.compressedSize)}</span>
          )}
          {operations.upload.status !== 'idle' && operations.upload.status !== 'success' && operations.upload.status !== 'error' && (
            <span>{operations.upload.progress}%</span>
          )}
        </div>
      </div>

      {/* 完成状态 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {getStageIcon(operations.completion.status)}
          <span className="text-sm text-gray-700">处理完成</span>
        </div>
        <div className="text-xs text-gray-500">
          {operations.completion.status === 'success' && (
            <span>{(operations.completion.totalProcessingTime / 1000).toFixed(1)}s</span>
          )}
        </div>
      </div>
    </div>
  );
};
```

## 3. 数据流和更新策略

### 3.1 状态更新流程
```typescript
class RealtimeStatusManager {
  private updateInterval: NodeJS.Timeout | null = null;
  private subscribers: Set<(status: BatchProcessingStatus) => void> = new Set();

  startStatusUpdates() {
    this.updateInterval = setInterval(() => {
      const currentStatus = this.collectCurrentStatus();
      this.notifySubscribers(currentStatus);
    }, 500); // 500ms 更新间隔
  }

  stopStatusUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  subscribe(callback: (status: BatchProcessingStatus) => void) {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  private collectCurrentStatus(): BatchProcessingStatus {
    // 从各个服务收集当前状态
    return {
      progress: this.getProgressStatus(),
      performance: this.getPerformanceMetrics(),
      currentTask: this.getCurrentTaskInfo()
    };
  }

  private notifySubscribers(status: BatchProcessingStatus) {
    this.subscribers.forEach(callback => {
      try {
        callback(status);
      } catch (error) {
        console.error('Status update callback error:', error);
      }
    });
  }
}
```

### 3.2 状态持久化策略
```typescript
interface StatusHistory {
  sessionId: string;
  startTime: number;
  endTime?: number;
  totalTasks: number;
  finalStats: {
    succeeded: number;
    failed: number;
    totalTime: number;
    averageTaskTime: number;
    contentTypeDistribution: {
      lynx: number;
      html: number;
      mixed: number;
    };
  };
}

class StatusHistoryManager {
  private static readonly STORAGE_KEY = 'batch_processor_status_history';
  private static readonly MAX_HISTORY = 20;

  static saveSession(session: StatusHistory) {
    try {
      const history = this.getHistory();
      history.unshift(session);
      
      // 限制历史记录数量
      const trimmed = history.slice(0, this.MAX_HISTORY);
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(trimmed));
    } catch (error) {
      console.error('保存状态历史失败:', error);
    }
  }

  static getHistory(): StatusHistory[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('加载状态历史失败:', error);
      return [];
    }
  }

  static getAverageMetrics(): {
    averageSuccessRate: number;
    averageProcessingTime: number;
    commonContentType: 'lynx' | 'html' | 'mixed';
  } {
    const history = this.getHistory();
    if (history.length === 0) {
      return {
        averageSuccessRate: 0,
        averageProcessingTime: 0,
        commonContentType: 'lynx'
      };
    }

    const totalSessions = history.length;
    const totalSuccessRate = history.reduce((sum, session) => {
      return sum + (session.finalStats.succeeded / (session.finalStats.succeeded + session.finalStats.failed));
    }, 0);

    const totalProcessingTime = history.reduce((sum, session) => {
      return sum + session.finalStats.averageTaskTime;
    }, 0);

    // 计算最常见的内容类型
    const typeCount = { lynx: 0, html: 0, mixed: 0 };
    history.forEach(session => {
      Object.entries(session.finalStats.contentTypeDistribution).forEach(([type, count]) => {
        typeCount[type as keyof typeof typeCount] += count;
      });
    });

    const commonContentType = Object.entries(typeCount).reduce((a, b) => 
      typeCount[a[0] as keyof typeof typeCount] > typeCount[b[0] as keyof typeof typeCount] ? a : b
    )[0] as 'lynx' | 'html' | 'mixed';

    return {
      averageSuccessRate: totalSuccessRate / totalSessions,
      averageProcessingTime: totalProcessingTime / totalSessions,
      commonContentType
    };
  }
}
```

## 4. 错误处理和异常展示

### 4.1 错误状态设计
```typescript
interface ErrorDisplayConfig {
  showErrorDetails: boolean;      // 是否显示详细错误
  errorAutoHide: boolean;         // 错误是否自动隐藏
  errorHideDelay: number;         // 自动隐藏延迟（ms）
  allowRetry: boolean;            // 是否允许重试
  maxRetryCount: number;          // 最大重试次数
}

const ErrorDisplay: React.FC<{
  error: string;
  config: ErrorDisplayConfig;
  onRetry?: () => void;
  onDismiss?: () => void;
}> = ({ error, config, onRetry, onDismiss }) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (config.errorAutoHide && config.errorHideDelay > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        onDismiss?.();
      }, config.errorHideDelay);

      return () => clearTimeout(timer);
    }
  }, [config, onDismiss]);

  if (!isVisible) return null;

  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-2">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-red-800">处理出错</h3>
          {config.showErrorDetails && (
            <div className="mt-1 text-sm text-red-700">{error}</div>
          )}
        </div>
        <div className="ml-3 flex gap-2">
          {config.allowRetry && onRetry && (
            <button
              onClick={onRetry}
              className="text-xs bg-red-100 hover:bg-red-200 text-red-800 px-2 py-1 rounded transition-colors"
            >
              重试
            </button>
          )}
          <button
            onClick={() => {
              setIsVisible(false);
              onDismiss?.();
            }}
            className="text-xs text-red-600 hover:text-red-800"
          >
            ×
          </button>
        </div>
      </div>
    </div>
  );
};
```

### 4.2 网络异常处理
```typescript
class NetworkStatusMonitor {
  private static isOnline = navigator.onLine;
  private static listeners: Set<(online: boolean) => void> = new Set();

  static init() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.notifyListeners(true);
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.notifyListeners(false);
    });
  }

  static subscribe(callback: (online: boolean) => void) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  static isNetworkOnline(): boolean {
    return this.isOnline;
  }

  private static notifyListeners(online: boolean) {
    this.listeners.forEach(callback => {
      try {
        callback(online);
      } catch (error) {
        console.error('Network status callback error:', error);
      }
    });
  }
}

// 网络状态指示器组件
const NetworkStatus: React.FC = () => {
  const [isOnline, setIsOnline] = useState(NetworkStatusMonitor.isNetworkOnline());

  useEffect(() => {
    return NetworkStatusMonitor.subscribe(setIsOnline);
  }, []);

  if (isOnline) return null;

  return (
    <div className="bg-orange-50 border border-orange-200 rounded-lg p-2 mb-2">
      <div className="flex items-center">
        <svg className="h-4 w-4 text-orange-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"/>
        </svg>
        <span className="text-sm text-orange-800">网络连接异常，请检查网络状态</span>
      </div>
    </div>
  );
};
```

## 5. 性能优化和用户体验

### 5.1 动画和过渡效果
```css
/* 状态切换动画 */
.status-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 进度条动画 */
.progress-bar {
  transition: width 0.5s ease-out;
}

/* 状态卡片进入动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.status-card-enter {
  animation: slideInUp 0.3s ease-out;
}

/* 错误状态闪烁提醒 */
@keyframes errorPulse {
  0%, 100% {
    border-color: #fca5a5;
  }
  50% {
    border-color: #f87171;
  }
}

.error-pulse {
  animation: errorPulse 1s ease-in-out 3;
}

/* 成功状态绿色闪光 */
@keyframes successFlash {
  0% {
    background-color: rgba(16, 185, 129, 0.1);
  }
  50% {
    background-color: rgba(16, 185, 129, 0.3);
  }
  100% {
    background-color: rgba(16, 185, 129, 0.1);
  }
}

.success-flash {
  animation: successFlash 0.6s ease-in-out;
}
```

### 5.2 响应式设计适配
```css
/* 移动端适配 */
@media (max-width: 768px) {
  .status-container {
    height: 250px; /* 移动端稍微减少高度 */
  }

  .status-grid {
    grid-template-columns: repeat(2, 1fr); /* 移动端改为2列 */
  }

  .status-text {
    font-size: 11px; /* 移动端字体稍小 */
  }

  .progress-stats {
    flex-direction: column; /* 移动端竖直排列 */
    gap: 4px;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .status-container {
    background-color: #1f2937;
    border-color: #374151;
  }

  .status-text {
    color: #e5e7eb;
  }

  .status-card {
    background-color: #374151;
    border-color: #4b5563;
  }
}
```

这个设计确保了实时状态展示系统能够：
1. **空间高效**：严格控制在 300px 高度内
2. **信息丰富**：展示处理的每个关键阶段
3. **视觉友好**：符合主题配色和设计规范
4. **性能优良**：优化的更新策略和动画效果
5. **用户友好**：清晰的状态反馈和错误处理