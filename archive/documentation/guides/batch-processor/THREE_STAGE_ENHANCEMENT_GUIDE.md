# 三阶段深化增强使用指南

## 快速开始

### 1. 基础使用

```typescript
import { buildThreeStagePrompt } from '../prompts/ModularPromptLoader';

// 生成三阶段增强prompt
const userQuery = "创建一个数据可视化仪表板";
const enhancedPrompt = buildThreeStagePrompt(userQuery);

// 使用增强prompt调用AI接口
const result = await aiService.generate(enhancedPrompt);
```

### 2. 在现有服务中集成

```typescript
import { ThreeStageEnhancementAdapter } from '../services/ThreeStageEnhancementAdapter';

class YourBatchProcessor {
  private enhancer = new ThreeStageEnhancementAdapter({
    enableThreeStage: true,
    qualityEnhancement: {
      enableDeepAnalysis: true,
      enableDesignOptimization: true,
      enableCodeExcellence: true,
    }
  });

  async processTask(userQuery: string) {
    // 构建增强prompt
    const systemPrompt = this.enhancer.buildEnhancedSystemPrompt(userQuery);
    
    // 调用现有的AI处理逻辑
    return await this.callAI(systemPrompt);
  }
}
```

## 配置选项

### 启用/禁用功能

```typescript
// 完全启用
const adapter = new ThreeStageEnhancementAdapter({
  enableThreeStage: true,
  qualityEnhancement: {
    enableDeepAnalysis: true,        // 深度需求分析
    enableDesignOptimization: true,  // 设计优化
    enableCodeExcellence: true,      // 代码卓越性
  }
});

// 选择性启用
const adapter = new ThreeStageEnhancementAdapter({
  enableThreeStage: true,
  qualityEnhancement: {
    enableDeepAnalysis: true,        // 只启用深度分析
    enableDesignOptimization: false,
    enableCodeExcellence: false,
  }
});
```

### 性能优化配置

```typescript
const adapter = new ThreeStageEnhancementAdapter({
  enableThreeStage: true,
  performanceOptions: {
    enablePromptCaching: true,       // 启用缓存
    enableStatisticsLogging: true,   // 启用统计
    maxPromptLength: 50000,          // 最大长度限制
  }
});
```

## 使用场景

### 适合使用三阶段增强的场景

✅ **高复杂度UI需求**
- 数据可视化应用
- 电商购物系统  
- 管理后台界面
- 多功能整合应用

✅ **质量要求较高的项目**
- 生产环境应用
- 客户展示项目
- 关键业务功能

### 不推荐使用的场景

❌ **简单功能需求**
- 基础计算器
- 简单表单
- 单一功能组件

❌ **快速原型验证**
- 概念验证
- 技术可行性测试

## 最佳实践

### 1. 智能模式切换

```typescript
class SmartBatchProcessor {
  shouldUseThreeStage(userQuery: string): boolean {
    // 基于查询复杂度自动判断
    const complexity = this.calculateComplexity(userQuery);
    return complexity > 0.5; // 复杂度阈值
  }

  async processTask(userQuery: string) {
    const useThreeStage = this.shouldUseThreeStage(userQuery);
    
    if (useThreeStage) {
      return await this.processWithThreeStage(userQuery);
    } else {
      return await this.processWithStandard(userQuery);
    }
  }
}
```

### 2. 批量处理优化

```typescript
async processBatchTasks(tasks: string[]) {
  const results = [];
  
  for (const task of tasks) {
    const complexity = this.calculateComplexity(task);
    
    // 复杂任务使用三阶段，简单任务使用标准模式
    if (complexity > 0.6) {
      results.push(await this.processWithThreeStage(task));
    } else {
      results.push(await this.processWithStandard(task));
    }
  }
  
  return results;
}
```

### 3. 错误处理和降级

```typescript
async processWithFallback(userQuery: string) {
  try {
    // 尝试三阶段增强模式
    return await this.processWithThreeStage(userQuery);
  } catch (error) {
    console.warn('三阶段模式失败，降级到标准模式', error);
    // 降级到标准模式
    return await this.processWithStandard(userQuery);
  }
}
```

## 性能监控

### 获取统计信息

```typescript
import { getEnhancedPromptStats } from '../prompts/ModularPromptLoader';

const stats = getEnhancedPromptStats(userQuery);
console.log('Prompt统计:', {
  长度: stats.promptLength,
  增强比例: stats.improvementRatio,
  复杂度: stats.complexityScore,
  缓存命中: stats.cacheHit
});
```

### 性能分析

```typescript
const adapter = new ThreeStageEnhancementAdapter({
  enableThreeStage: true,
  performanceOptions: {
    enableStatisticsLogging: true,
  }
});

// 获取性能统计
const performanceData = adapter.getPerformanceStats();
console.log('性能统计:', performanceData);
```

## 示例代码

### 完整集成示例

```typescript
import { 
  ThreeStageEnhancementAdapter,
  buildThreeStagePrompt 
} from '../services/ThreeStageEnhancementAdapter';

export class EnhancedBatchProcessor {
  private adapter: ThreeStageEnhancementAdapter;

  constructor() {
    this.adapter = new ThreeStageEnhancementAdapter({
      enableThreeStage: true,
      qualityEnhancement: {
        enableDeepAnalysis: true,
        enableDesignOptimization: true,
        enableCodeExcellence: true,
      },
      performanceOptions: {
        enablePromptCaching: true,
        enableStatisticsLogging: true,
      }
    });
  }

  async processTask(userQuery: string) {
    // 智能判断是否使用三阶段
    const shouldEnhance = this.adapter.shouldUseThreeStage(userQuery);
    
    if (shouldEnhance) {
      console.log('使用三阶段深化模式处理复杂任务');
      const enhancedPrompt = this.adapter.buildEnhancedSystemPrompt(userQuery);
      return await this.callAI(enhancedPrompt);
    } else {
      console.log('使用标准模式处理简单任务');
      const standardPrompt = getMasterLevelLynxPromptContent();
      return await this.callAI(standardPrompt);
    }
  }

  private async callAI(prompt: string) {
    // 这里是你的AI接口调用逻辑
    return await yourAIService.generate(prompt);
  }
}
```

## 故障排除

### 常见问题

**Q: 三阶段模式生成的prompt过长怎么办？**
A: 可以通过配置`maxPromptLength`限制长度，或者在复杂度较低时自动降级到标准模式。

**Q: 性能较慢怎么优化？**
A: 启用prompt缓存，对重复查询可以显著提升性能。

**Q: 如何判断是否需要使用三阶段模式？**
A: 系统会自动计算查询复杂度，也可以手动设置阈值进行控制。

### 调试技巧

```typescript
// 启用详细日志
const adapter = new ThreeStageEnhancementAdapter({
  enableThreeStage: true,
  performanceOptions: {
    enableStatisticsLogging: true,
  }
});

// 查看生成的prompt内容
const prompt = adapter.buildEnhancedSystemPrompt(userQuery);
console.log('生成的prompt:', prompt);

// 查看统计信息
const stats = getEnhancedPromptStats(userQuery);
console.log('统计信息:', stats);
```

## 更新说明

三阶段深化增强功能已完全集成到现有的ModularPromptLoader系统中，保持100%向后兼容性。无需修改现有代码即可使用新功能。