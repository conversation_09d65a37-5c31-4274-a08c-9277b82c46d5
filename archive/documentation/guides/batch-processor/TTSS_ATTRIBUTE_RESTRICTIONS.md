# TTSS 属性使用限制规范

## 🚫 严格禁令 - 必须遵守

**CRITICAL**: 在Lynx框架的TTSS样式文件中，严格禁止使用任何未在本文档中明确列出的CSS属性！

### ❌ 禁止使用的属性 (但不限于)

以下属性严格禁止在TTSS文件中使用：

```css
/* 🚫 严格禁止的CSS属性 */
backdrop-filter: blur(10px);           /* 背景滤镜 - 不支持 */
text-transform: uppercase;              /* 文本转换 - 不支持 */
filter: brightness(1.2);                /* 滤镜效果 - 不支持 */
clip-path: polygon(50% 0%, 0% 100%);    /* 裁剪路径 - 不支持 */
mask: url(#mask);                       /* 遮罩 - 不支持 */
object-fit: cover;                      /* 对象适配 - 不支持 */
object-position: center;                /* 对象位置 - 不支持 */
scroll-behavior: smooth;                /* 滚动行为 - 不支持 */
user-select: none;                      /* 用户选择 - 不支持 */
pointer-events: none;                   /* 指针事件 - 不支持 */
cursor: pointer;                        /* 光标样式 - 不支持 */
resize: both;                          /* 调整大小 - 不支持 */
outline: none;                         /* 轮廓 - 不支持 */
appearance: none;                      /* 外观 - 不支持 */
-webkit-appearance: none;              /* WebKit外观 - 不支持 */
-moz-appearance: none;                 /* Firefox外观 - 不支持 */
tab-size: 4;                          /* Tab大小 - 不支持 */
white-space: nowrap;                   /* 空白处理 - 不支持 */
word-break: break-all;                 /* 单词断行 - 不支持 */
word-wrap: break-word;                 /* 单词换行 - 不支持 */
hyphens: auto;                         /* 连字符 - 不支持 */
columns: 2;                            /* 多列布局 - 不支持 */
column-gap: 20px;                      /* 列间距 - 不支持 */
break-inside: avoid;                   /* 分页控制 - 不支持 */
orphans: 2;                            /* 孤儿行 - 不支持 */
widows: 2;                             /* 寡妇行 - 不支持 */
page-break-before: always;             /* 分页符 - 不支持 */
mix-blend-mode: multiply;              /* 混合模式 - 不支持 */
isolation: isolate;                    /* 隔离 - 不支持 */
will-change: transform;                /* 性能提示 - 不支持 */
contain: layout;                       /* 包含 - 不支持 */
overscroll-behavior: contain;          /* 过度滚动 - 不支持 */
scroll-snap-type: x mandatory;         /* 滚动捕捉 - 不支持 */
touch-action: pan-y;                   /* 触摸动作 - 不支持 */
```

### ❌ 禁止使用的伪类和伪元素

```css
/* 🚫 严格禁止的伪类和伪元素 */
.element:hover { }                     /* 悬停状态 - 不支持 */
.element:focus { }                     /* 焦点状态 - 不支持 */
.element:active { }                    /* 激活状态 - 不支持 */
.element:visited { }                   /* 访问状态 - 不支持 */
.element:nth-child(2n+1) { }          /* 第n个子元素 - 不支持 */
.element:first-child { }               /* 第一个子元素 - 不支持 */
.element:last-child { }                /* 最后一个子元素 - 不支持 */
.element::before { }                   /* 前置伪元素 - 不支持 */
.element::after { }                    /* 后置伪元素 - 不支持 */
.element::first-line { }               /* 首行伪元素 - 不支持 */
.element::first-letter { }             /* 首字母伪元素 - 不支持 */
.element::selection { }                /* 选择伪元素 - 不支持 */
```

### ❌ 禁止使用的高级选择器

```css
/* 🚫 严格禁止的选择器 */
.parent > .child { }                   /* 直接子选择器 - 不支持 */
.element + .sibling { }                /* 相邻兄弟选择器 - 不支持 */
.element ~ .sibling { }                /* 通用兄弟选择器 - 不支持 */
.element[attr^="value"] { }            /* 属性选择器 - 不支持 */
.element[attr$="value"] { }            /* 属性结尾选择器 - 不支持 */
.element[attr*="value"] { }            /* 属性包含选择器 - 不支持 */
```

## ✅ 允许使用的TTSS属性

### 📦 布局属性 (Layout)

```css
/* ✅ 允许的布局属性 */
display: flex;                         /* 显示类型 */
flex-direction: column;                /* Flex方向 */
flex-wrap: wrap;                      /* Flex换行 */
justify-content: center;              /* 主轴对齐 */
align-items: center;                  /* 交叉轴对齐 */
align-content: center;                /* 多行对齐 */
flex: 1;                             /* Flex缩放 */
flex-grow: 1;                        /* Flex增长 */
flex-shrink: 0;                      /* Flex收缩 */
flex-basis: auto;                    /* Flex基础 */
align-self: center;                  /* 自身对齐 */
order: 1;                           /* 排序 */
position: relative;                  /* 定位类型 */
top: 0;                             /* 顶部偏移 */
right: 0;                           /* 右侧偏移 */
bottom: 0;                          /* 底部偏移 */
left: 0;                            /* 左侧偏移 */
z-index: 1;                         /* 层叠顺序 */
```

### 📏 尺寸属性 (Dimensions)

```css
/* ✅ 允许的尺寸属性 */
width: 100px;                       /* 宽度 */
height: 100px;                      /* 高度 */
max-width: 500px;                   /* 最大宽度 */
max-height: 500px;                  /* 最大高度 */
min-width: 100px;                   /* 最小宽度 */
min-height: 100px;                  /* 最小高度 */
```

### 📐 间距属性 (Spacing)

```css
/* ✅ 允许的间距属性 */
margin: 10px;                       /* 外边距 */
margin-top: 10px;                   /* 上外边距 */
margin-right: 10px;                 /* 右外边距 */
margin-bottom: 10px;                /* 下外边距 */
margin-left: 10px;                  /* 左外边距 */
padding: 10px;                      /* 内边距 */
padding-top: 10px;                  /* 上内边距 */
padding-right: 10px;                /* 右内边距 */
padding-bottom: 10px;               /* 下内边距 */
padding-left: 10px;                 /* 左内边距 */
```

### 🎨 颜色和背景 (Colors & Background)

```css
/* ✅ 允许的颜色和背景属性 */
color: #333333;                     /* 文字颜色 */
background-color: #ffffff;          /* 背景颜色 */
background-image: url('image.png'); /* 背景图片 */
background-size: cover;             /* 背景尺寸 */
background-repeat: no-repeat;       /* 背景重复 */
background-position: center;        /* 背景位置 */
opacity: 0.5;                      /* 透明度 */
```

### 🔤 文本属性 (Typography)

```css
/* ✅ 允许的文本属性 */
font-size: 16px;                   /* 字体大小 */
font-weight: bold;                 /* 字体粗细 */
font-family: 'PingFang SC';        /* 字体族 */
line-height: 1.5;                 /* 行高 */
text-align: center;               /* 文本对齐 */
color: #333333;                   /* 文字颜色 */
```

### 🖼️ 边框属性 (Border)

```css
/* ✅ 允许的边框属性 */
border: 1px solid #ddd;           /* 边框 */
border-width: 1px;                /* 边框宽度 */
border-style: solid;              /* 边框样式 */
border-color: #ddd;               /* 边框颜色 */
border-top: 1px solid #ddd;       /* 上边框 */
border-right: 1px solid #ddd;     /* 右边框 */
border-bottom: 1px solid #ddd;    /* 下边框 */
border-left: 1px solid #ddd;      /* 左边框 */
border-radius: 5px;               /* 边框圆角 */
```

### 🔄 变换属性 (Transform)

```css
/* ✅ 允许的变换属性 */
transform: translateX(10px);       /* X轴平移 */
transform: translateY(10px);       /* Y轴平移 */
transform: translate(10px, 10px);  /* 平移 */
transform: scale(1.5);            /* 缩放 */
transform: rotate(45deg);         /* 旋转 */
transform-origin: center;         /* 变换原点 */
```

### 📱 RPX单位 (Lynx专用)

```css
/* ✅ 允许的RPX单位 */
width: 750rpx;                    /* RPX宽度 */
height: 200rpx;                   /* RPX高度 */
margin: 20rpx;                    /* RPX外边距 */
padding: 15rpx;                   /* RPX内边距 */
font-size: 32rpx;                /* RPX字体大小 */
border-radius: 10rpx;            /* RPX边框圆角 */
```

## 📋 合规检查清单

在编写TTSS样式时，请严格按照以下清单检查：

### ✅ 必须检查项

- [ ] 只使用本文档"允许使用"部分列出的CSS属性
- [ ] 没有使用任何"禁止使用"部分列出的属性
- [ ] 没有使用伪类或伪元素选择器
- [ ] 没有使用高级CSS选择器
- [ ] 优先使用RPX单位进行移动端适配
- [ ] 使用Flexbox进行布局（display: flex）
- [ ] 遵循Lynx框架的命名约定

### ❌ 禁止检查项

- [ ] 确认没有使用 `backdrop-filter`
- [ ] 确认没有使用 `text-transform`
- [ ] 确认没有使用 `filter`
- [ ] 确认没有使用 `clip-path`
- [ ] 确认没有使用 `user-select`
- [ ] 确认没有使用 `:hover` 等伪类
- [ ] 确认没有使用 `::before` 等伪元素

## 🔍 代码审查要点

### 正确示例 ✅

```css
.container {
  display: flex;
  flex-direction: column;
  width: 750rpx;
  height: 400rpx;
  background-color: #ffffff;
  padding: 20rpx;
  border-radius: 10rpx;
}

.text {
  font-size: 32rpx;
  color: #333333;
  text-align: center;
  line-height: 1.5;
}

.button {
  width: 200rpx;
  height: 80rpx;
  background-color: #007AFF;
  border-radius: 8rpx;
  margin: 20rpx 0;
}
```

### 错误示例 ❌

```css
/* 🚫 错误：使用了禁止的属性 */
.container {
  backdrop-filter: blur(10px);        /* 禁止使用 */
  text-transform: uppercase;           /* 禁止使用 */
  user-select: none;                  /* 禁止使用 */
}

.text:hover {                         /* 禁止使用伪类 */
  color: red;
}

.button::before {                     /* 禁止使用伪元素 */
  content: "";
}
```

## 📖 参考文档

- [Lynx框架官方文档](https://developer.byte-dance.net/docs/lynx/)
- [Template-Assembler v3.0 规范](../runtime_convert_parse5/README.md)
- [RPX单位转换规则](../runtime_convert_parse5/TTML_TTSS_CONVERSION_RULES_COMPARISON.md)

---

**重要提醒**: 本规范是强制性的，任何违反此规范的TTSS代码都可能导致渲染失败或不可预期的行为。请在开发过程中严格遵守这些限制。