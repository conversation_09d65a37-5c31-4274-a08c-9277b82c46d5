# Lynx到HTML自动转换器技术方案

## 方案概述

基于PE规则开发一个客户端转换器，将AI生成的Lynx代码自动转换为可在Web浏览器中预览的HTML代码。这种方案避免了让AI生成双份代码的成本，通过程序化转换实现预览功能。

## 转换规则分析

### 1. 文件结构转换

**输入格式 (Lynx):**
```
<FILES>
<FILE path="index.ttml"><!-- TTML内容 --></FILE>
<FILE path="index.ttss">/* TTSS样式 */</FILE>
<FILE path="index.js">// JS逻辑</FILE>
<FILE path="index.json">{"component": true}</FILE>
</FILES>
```

**输出格式 (HTML):**
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>/* 转换后的CSS */</style>
</head>
<body>
    <!-- 转换后的HTML结构 -->
    <script>/* 转换后的JavaScript */</script>
</body>
</html>
```

### 2. TTML到HTML标签映射

| Lynx TTML | Web HTML | 转换规则 |
|-----------|----------|----------|
| `<view>` | `<div>` | 直接替换，保留所有属性 |
| `<text>` | `<span>` | 直接替换，处理文本内容 |
| `<image>` | `<img>` | src属性保持，添加alt属性 |
| `<scroll-view>` | `<div class="scroll-container">` | 添加滚动样式 |
| `<list>` | `<div class="list-container">` | 转换为普通容器 |
| `<list-item>` | `<div class="list-item">` | 转换为列表项 |
| `<canvas>` | `<canvas>` | 保持标签，调整API调用 |

### 3. 模板语法转换

**tt:for 循环语法:**
```html
<!-- Lynx TTML -->
<view tt:for="{{items}}" tt:key="id">
  <text>{{item.name}}</text>
</view>

<!-- 转换为HTML + JavaScript -->
<div id="list-container"></div>
<script>
const items = [/* 数据 */];
const container = document.getElementById('list-container');
items.forEach((item, index) => {
  const div = document.createElement('div');
  const span = document.createElement('span');
  span.textContent = item.name;
  div.appendChild(span);
  container.appendChild(div);
});
</script>
```

**tt:if 条件渲染:**
```html
<!-- Lynx TTML -->
<view tt:if="{{showContent}}">
  <text>内容</text>
</view>

<!-- 转换为HTML + JavaScript -->
<div id="conditional-content" style="display: none;">
  <span>内容</span>
</div>
<script>
const showContent = true; // 从data中获取
document.getElementById('conditional-content').style.display = showContent ? 'block' : 'none';
</script>
```

### 4. TTSS到CSS转换规则

**单位转换:**
```css
/* Lynx TTSS */
.container {
  width: 750rpx;
  height: 200rpx;
  font-size: 28rpx;
}

/* 转换为CSS (1rpx = 0.5px in 750rpx base) */
.container {
  width: 375px;
  height: 100px;
  font-size: 14px;
}
```

**布局转换:**
```css
/* Lynx默认column布局 */
.lynx-view {
  display: flex;
  flex-direction: column; /* Lynx默认 */
}

/* Lynx特有属性移除 */
.scroll-container {
  /* enable-scroll: true; <- 移除 */
  overflow-y: auto; /* 转换为标准CSS */
}
```

**选择器兼容性:**
```css
/* 不支持的选择器转换 */
/* .parent .child.active <- 多类选择器不支持 */
.parent .child[data-active="true"] /* 转换为属性选择器 */
```

### 5. JavaScript逻辑转换

**生命周期函数映射:**
```javascript
// Lynx生命周期
Card({
  data: { count: 0 },
  onLoad() { /* 初始化 */ },
  onShow() { /* 显示时 */ },
  onHide() { /* 隐藏时 */ },
  handleTap() { /* 点击事件 */ }
});

// 转换为Web JavaScript
class ComponentLogic {
  constructor() {
    this.data = { count: 0 };
    this.onLoad();
    document.addEventListener('DOMContentLoaded', () => this.onShow());
    document.addEventListener('visibilitychange', () => {
      document.hidden ? this.onHide() : this.onShow();
    });
  }
  
  onLoad() { /* 初始化逻辑 */ }
  onShow() { /* 页面显示逻辑 */ }
  onHide() { /* 页面隐藏逻辑 */ }
  
  handleTap(event) { /* 点击事件处理 */ }
  
  setData(newData, callback) {
    Object.assign(this.data, newData);
    this.updateDOM();
    if (callback) callback();
  }
  
  updateDOM() {
    // 更新DOM元素以反映数据变化
  }
}

const component = new ComponentLogic();
```

**事件系统转换:**
```javascript
// Lynx事件绑定
// <view bindtap="handleTap" data-id="{{item.id}}">

// 转换为Web事件
document.addEventListener('click', function(event) {
  const target = event.target.closest('[data-tap-handler]');
  if (target) {
    const handler = target.getAttribute('data-tap-handler');
    const id = target.dataset.id;
    component[handler](event, { id });
  }
});
```

## 实现架构

### 核心转换器模块

```javascript
class LynxToHtmlTransformer {
  constructor() {
    this.tagMap = {
      'view': 'div',
      'text': 'span', 
      'image': 'img',
      'scroll-view': 'div'
    };
    
    this.rpxRatio = 0.5; // 1rpx = 0.5px (750rpx base)
  }
  
  transform(lynxFiles) {
    const ttml = this.extractFile(lynxFiles, '.ttml');
    const ttss = this.extractFile(lynxFiles, '.ttss');
    const js = this.extractFile(lynxFiles, '.js');
    
    const html = this.transformTTML(ttml);
    const css = this.transformTTSS(ttss);
    const javascript = this.transformJS(js);
    
    return this.generateHTML(html, css, javascript);
  }
  
  transformTTML(ttml) {
    // 1. 替换标签名
    let html = this.replaceTagNames(ttml);
    
    // 2. 处理模板语法
    html = this.processTemplateDirectives(html);
    
    // 3. 转换事件绑定
    html = this.transformEventBindings(html);
    
    return html;
  }
  
  transformTTSS(ttss) {
    // 1. 转换rpx单位
    let css = this.convertRpxUnits(ttss);
    
    // 2. 处理Lynx特有属性
    css = this.handleLynxSpecificProperties(css);
    
    // 3. 添加默认样式
    css = this.addDefaultStyles(css);
    
    return css;
  }
  
  transformJS(js) {
    // 1. 转换Card语法
    let javascript = this.transformCardSyntax(js);
    
    // 2. 转换生命周期
    javascript = this.transformLifecycle(javascript);
    
    // 3. 转换API调用
    javascript = this.transformAPICalls(javascript);
    
    return javascript;
  }
}
```

### 模板指令处理器

```javascript
class TemplateDirectiveProcessor {
  processTtFor(element, forExpression) {
    const { array, item, index } = this.parseForExpression(forExpression);
    
    // 生成JavaScript代码来动态创建元素
    return `
      <div id="for-container-${this.generateId()}"></div>
      <script>
        (function() {
          const container = document.getElementById('for-container-${this.id}');
          const ${array} = component.data.${array} || [];
          ${array}.forEach((${item}, ${index}) => {
            const element = document.createElement('${element.tagName.toLowerCase()}');
            // 设置元素内容和属性
            ${this.generateElementContent(element, item, index)}
            container.appendChild(element);
          });
        })();
      </script>
    `;
  }
  
  processTtIf(element, condition) {
    const id = this.generateId();
    
    return `
      <${element.tagName.toLowerCase()} id="if-${id}" style="display: none;">
        ${element.innerHTML}
      </${element.tagName.toLowerCase()}>
      <script>
        (function() {
          const element = document.getElementById('if-${id}');
          const condition = ${this.transformCondition(condition)};
          element.style.display = condition ? 'block' : 'none';
        })();
      </script>
    `;
  }
}
```

### 样式转换器

```javascript
class StyleTransformer {
  convertRpxUnits(css) {
    return css.replace(/(\d+(\.\d+)?)rpx/g, (match, value) => {
      const pxValue = parseFloat(value) * this.rpxRatio;
      return `${pxValue}px`;
    });
  }
  
  addDefaultStyles(css) {
    const defaults = `
      /* Lynx默认样式 */
      .lynx-view {
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      
      .lynx-text {
        display: inline-block;
        word-wrap: break-word;
      }
      
      .scroll-container {
        overflow: auto;
        -webkit-overflow-scrolling: touch;
      }
      
      /* 移动端优化 */
      * {
        -webkit-tap-highlight-color: transparent;
        -webkit-user-select: none;
        user-select: none;
      }
      
      /* 8px网格系统支持 */
      .grid-8 * {
        margin: 0;
        padding: 0;
      }
    `;
    
    return defaults + '\n' + css;
  }
}
```

## 集成方案

### 1. 在batch_processor中集成

```javascript
// 在streamParser.js中添加转换功能
import { LynxToHtmlTransformer } from './lynxTransformer';

class EnhancedStreamParser {
  constructor() {
    this.transformer = new LynxToHtmlTransformer();
  }
  
  parseAIResponse(response) {
    const lynxFiles = this.extractLynxFiles(response);
    const htmlPreview = this.transformer.transform(lynxFiles);
    
    return {
      lynxCode: lynxFiles,
      htmlPreview: htmlPreview,
      playgroundUrl: this.generatePlaygroundUrl(lynxFiles)
    };
  }
}
```

### 2. 预览组件开发

```javascript
// PreviewPanel组件
const PreviewPanel = ({ result }) => {
  const [previewMode, setPreviewMode] = useState('lynx');
  
  return (
    <div className="preview-panel">
      <div className="preview-controls">
        <button 
          onClick={() => setPreviewMode('lynx')}
          className={previewMode === 'lynx' ? 'active' : ''}
        >
          Lynx Playground
        </button>
        <button 
          onClick={() => setPreviewMode('web')}
          className={previewMode === 'web' ? 'active' : ''}
        >
          Web Preview
        </button>
      </div>
      
      <div className="preview-content">
        {previewMode === 'lynx' ? (
          <iframe 
            src={result.playgroundUrl} 
            className="lynx-preview"
          />
        ) : (
          <iframe 
            srcDoc={result.htmlPreview}
            className="web-preview"
          />
        )}
      </div>
    </div>
  );
};
```

## 技术优势

1. **成本控制**: 避免AI生成双份代码的Token成本
2. **实时转换**: 客户端转换，响应速度快
3. **可定制**: 转换规则可以根据需要调整
4. **维护性**: 转换逻辑集中管理，易于维护

## 技术挑战

1. **复杂语法**: 复杂的tt:for和tt:if嵌套处理
2. **Canvas转换**: Canvas API差异处理复杂
3. **动画效果**: Lynx动画到CSS动画的转换
4. **性能优化**: 大量DOM操作的性能问题

## 实现优先级

### 阶段一：基础转换
- [x] 基本标签映射 (view→div, text→span)
- [x] 简单样式转换 (rpx单位转换)
- [x] 基础事件绑定转换

### 阶段二：模板语法
- [ ] tt:for循环语法处理
- [ ] tt:if条件渲染处理
- [ ] 数据绑定和更新机制

### 阶段三：高级特性
- [ ] Canvas转换支持
- [ ] 复杂动画效果转换
- [ ] 生命周期函数完整支持

## 预期效果

通过这个转换器，用户可以：
1. 生成Lynx代码后立即查看Web预览效果
2. 在同一界面对比Lynx和Web两种渲染效果
3. 无需额外的AI调用成本即可实现预览功能
4. 获得可直接在Web项目中使用的HTML代码

这种方案在成本控制和功能实现之间取得了良好的平衡。