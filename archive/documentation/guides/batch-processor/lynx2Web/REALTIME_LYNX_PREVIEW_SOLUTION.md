# 批处理器实时Lynx预览解决方案

## 方案背景

基于对batch_processor的深度分析，发现当前系统在extract、压缩、upload之前的AI响应确实是**未分离的原始格式**。这为我们提供了绝佳的机会，可以直接在Web Worker中对原始AI响应进行实时解析和转换，生成Web预览。

## 关键发现

### 1. AI响应原始格式分析

```javascript
// 流式响应格式（来自streamParser.js分析）
{"id":"xxx","choices":[{"delta":{"content":"<FILES>\n<FILE path=\"index.ttml\">\n"}}]}
{"id":"xxx","choices":[{"delta":{"content":"<view class=\"container\">\n"}}]}
{"id":"xxx","choices":[{"delta":{"content":"  <text>Hello World</text>\n"}}]}
{"id":"xxx","choices":[{"delta":{"content":"</view>\n</FILE>\n"}}]}
{"id":"xxx","choices":[{"delta":{"content":"<FILE path=\"index.ttss\">\n"}}]}
{"id":"xxx","choices":[{"delta":{"content":".container { padding: 20rpx; }\n"}}]}
{"id":"xxx","choices":[{"delta":{"content":"</FILE>\n</FILES>"}}]}
```

**核心特点：**
- AI响应是**流式传输**，包含完整的`<FILES>`标签结构
- 内容通过多个JSON片段拼接而成
- 包含转义字符（`\n`、`\<`、`\>`等）
- **在extract步骤之前就可以获取到完整内容**

### 2. 当前处理流程的时机分析

```mermaid
graph TD
    A[AI API响应] --> B[流式JSON解析]
    B --> C[内容拼接]
    C --> D[extractLynxCode - 代码提取]
    D --> E[parseLynxCodeStructure - 文件分离]
    E --> F[压缩打包]
    F --> G[CDN上传]
    G --> H[生成预览链接]
    
    I[🎯 最佳介入点] --> C
    J[Web Worker转换] --> I
    K[实时Web预览] --> J
```

**最佳介入时机：** 在步骤C（内容拼接完成）之后，步骤D（代码提取）之前

## 实时预览方案设计

### 方案架构

```javascript
// 1. 在EnhancedBatchProcessorService中添加实时预览钩子
class EnhancedBatchProcessorService {
  async processQuery(query, systemPrompt, options = {}) {
    // ... 现有逻辑 ...
    
    // 🎯 新增：实时预览处理
    if (options.enableRealTimePreview) {
      const previewManager = new RealTimePreviewManager();
      
      // 在内容拼接完成后立即处理
      const rawContent = extractResult.extractedContent || '';
      
      // 异步生成Web预览（不阻塞主流程）
      previewManager.generatePreview(rawContent, query.id)
        .then(previewHTML => {
          // 通过事件系统通知UI更新
          this.eventEmitter.emit('preview-ready', {
            queryId: query.id,
            previewHTML: previewHTML,
            timestamp: Date.now()
          });
        })
        .catch(error => {
          console.warn('[RealTimePreview] 预览生成失败:', error);
        });
    }
    
    // 继续原有流程...
  }
}
```

### 核心组件：RealTimePreviewManager

```javascript
class RealTimePreviewManager {
  constructor() {
    this.workerPool = new TransformWorkerPool();
    this.previewCache = new Map();
  }
  
  async generatePreview(rawAIContent, queryId) {
    try {
      // 步骤1：预处理AI响应内容
      const preprocessed = this.preprocessAIContent(rawAIContent);
      
      // 步骤2：在Web Worker中解析和转换
      const transformResult = await this.workerPool.transform({
        id: queryId,
        content: preprocessed,
        options: {
          format: 'raw-ai-response',
          enableFallback: true,
          timeout: 10000 // 10秒超时
        }
      });
      
      // 步骤3：生成可嵌入的HTML
      const embedHTML = this.createEmbeddableHTML(transformResult);
      
      // 步骤4：缓存结果
      this.previewCache.set(queryId, {
        html: embedHTML,
        timestamp: Date.now(),
        stats: transformResult.stats
      });
      
      return embedHTML;
      
    } catch (error) {
      // 生成错误降级HTML
      return this.generateErrorHTML(error, queryId);
    }
  }
  
  preprocessAIContent(rawContent) {
    // 清理转义字符
    let cleaned = rawContent
      .replace(/\\n/g, '\n')
      .replace(/\\"/g, '"')
      .replace(/\\</g, '<')
      .replace(/\\>/g, '>');
    
    // 检测和修复<FILES>标签结构
    if (!cleaned.includes('<FILES>')) {
      // 如果没有<FILES>标签，尝试智能包装
      if (cleaned.includes('<FILE')) {
        cleaned = `<FILES>\n${cleaned}\n</FILES>`;
      }
    }
    
    // 修复不完整的标签
    cleaned = this.repairIncompleteTags(cleaned);
    
    return cleaned;
  }
  
  repairIncompleteTags(content) {
    // 修复常见的不完整标签问题
    const repairs = [
      // 修复未闭合的<FILE>标签
      {
        pattern: /<FILE\s+path="([^"]+)"[^>]*>(?![\s\S]*<\/FILE>)/g,
        fix: (match, path) => match + '\n<!-- FILE CONTENT -->\n</FILE>'
      },
      // 修复未闭合的<FILES>标签
      {
        pattern: /<FILES>(?![\s\S]*<\/FILES>)/g,
        fix: (match) => match + '\n<!-- FILES CONTENT -->\n</FILES>'
      }
    ];
    
    let repaired = content;
    repairs.forEach(repair => {
      repaired = repaired.replace(repair.pattern, repair.fix);
    });
    
    return repaired;
  }
}
```

### Web Worker转换器增强

```javascript
// realtime-lynx-transformer.js
class RealtimeLynxTransformerWorker {
  constructor() {
    this.parser = new AIResponseParser();
    this.converter = new LynxToHTMLConverter();
  }
  
  async transformRawAIResponse(content, options) {
    const startTime = performance.now();
    const warnings = [];
    
    try {
      // 步骤1：解析AI响应格式
      const parsedFiles = this.parser.parseAIResponse(content, {
        onWarning: (warning) => warnings.push(warning),
        strictMode: false // 允许部分内容解析
      });
      
      // 步骤2：转换为HTML
      const htmlResult = await this.converter.convertToHTML(parsedFiles, {
        enableRealTimeMode: true,
        fallbackStrategy: 'graceful-degradation'
      });
      
      const endTime = performance.now();
      
      return {
        success: true,
        html: htmlResult,
        warnings: warnings,
        stats: {
          parseTime: endTime - startTime,
          filesDetected: Object.keys(parsedFiles).length,
          htmlSize: htmlResult.length
        }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error.message,
        fallbackHTML: this.generateMinimalHTML(content),
        stats: { parseTime: performance.now() - startTime }
      };
    }
  }
}

class AIResponseParser {
  parseAIResponse(rawContent, options = {}) {
    const files = {};
    
    try {
      // 方法1：解析<FILES>标签结构
      if (rawContent.includes('<FILES>')) {
        return this.parseFilesTagStructure(rawContent, options);
      }
      
      // 方法2：解析单个<FILE>标签
      if (rawContent.includes('<FILE')) {
        return this.parseFileTagStructure(rawContent, options);
      }
      
      // 方法3：智能内容检测
      return this.intelligentContentDetection(rawContent, options);
      
    } catch (error) {
      if (options.onWarning) {
        options.onWarning(`解析失败，使用降级模式: ${error.message}`);
      }
      
      // 降级：将整个内容作为单个文件
      return {
        'index.ttml': this.extractTTMLContent(rawContent),
        'index.ttss': this.extractTTSSContent(rawContent),
        'index.js': this.extractJSContent(rawContent)
      };
    }
  }
  
  parseFilesTagStructure(content, options) {
    const files = {};
    
    // 提取<FILES>内容
    const filesMatch = content.match(/<FILES>([\s\S]*?)<\/FILES>/);
    if (!filesMatch) {
      throw new Error('无法解析<FILES>标签');
    }
    
    const filesContent = filesMatch[1];
    
    // 解析各个<FILE>标签
    const filePattern = /<FILE\s+path="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/g;
    let match;
    
    while ((match = filePattern.exec(filesContent)) !== null) {
      const [, path, content] = match;
      files[path] = content.trim();
    }
    
    if (Object.keys(files).length === 0) {
      throw new Error('未找到有效的FILE标签');
    }
    
    return files;
  }
  
  intelligentContentDetection(content, options) {
    const files = {};
    
    // 检测TTML内容
    if (this.containsTTMLContent(content)) {
      files['index.ttml'] = this.extractTTMLContent(content);
    }
    
    // 检测TTSS内容
    if (this.containsTTSSContent(content)) {
      files['index.ttss'] = this.extractTTSSContent(content);
    }
    
    // 检测JavaScript内容
    if (this.containsJSContent(content)) {
      files['index.js'] = this.extractJSContent(content);
    }
    
    // 如果没有检测到任何已知格式，创建基础结构
    if (Object.keys(files).length === 0) {
      files['index.ttml'] = this.generateBasicTTML(content);
      files['index.ttss'] = this.generateBasicTTSS();
    }
    
    return files;
  }
  
  containsTTMLContent(content) {
    const tttmlIndicators = [
      '<view', '<text', '<image', '<scroll-view',
      'tt:for', 'tt:if', 'bindtap'
    ];
    return tttmlIndicators.some(indicator => content.includes(indicator));
  }
  
  containsTTSSContent(content) {
    const ttssIndicators = [
      'rpx', 'display: flex', 'flex-direction',
      '.', '#', 'background', 'color:'
    ];
    return ttssIndicators.some(indicator => content.includes(indicator));
  }
}
```

### 实时预览UI集成

```javascript
// ResultsPanel组件增强
const EnhancedResultsPanel = ({ results, onPreviewReady }) => {
  const [realTimePreviews, setRealTimePreviews] = useState(new Map());
  
  useEffect(() => {
    // 监听实时预览事件
    const handlePreviewReady = (event) => {
      const { queryId, previewHTML } = event.detail;
      setRealTimePreviews(prev => new Map(prev.set(queryId, previewHTML)));
    };
    
    document.addEventListener('preview-ready', handlePreviewReady);
    return () => document.removeEventListener('preview-ready', handlePreviewReady);
  }, []);
  
  return (
    <div className="results-panel">
      {results.map(result => (
        <div key={result.id} className="result-item">
          <div className="result-header">
            <h3>{result.query}</h3>
            <div className="preview-controls">
              <button 
                onClick={() => showLynxPlayground(result.playgroundUrl)}
                className="btn-lynx-preview"
              >
                Lynx Playground
              </button>
              {realTimePreviews.has(result.id) && (
                <button 
                  onClick={() => showWebPreview(result.id)}
                  className="btn-web-preview"
                >
                  Web Preview ⚡
                </button>
              )}
            </div>
          </div>
          
          {/* 实时Web预览区域 */}
          {realTimePreviews.has(result.id) && (
            <div className="realtime-preview-container">
              <iframe
                srcDoc={realTimePreviews.get(result.id)}
                className="realtime-preview-frame"
                sandbox="allow-scripts allow-same-origin"
                title={`Web预览 - ${result.query}`}
              />
            </div>
          )}
          
          {/* 原有内容显示 */}
          <div className="result-content">
            {result.extractedContent}
          </div>
        </div>
      ))}
    </div>
  );
};
```

### 性能优化策略

#### 1. 并发控制

```javascript
class TransformWorkerPool {
  constructor() {
    this.workers = [];
    this.maxWorkers = Math.min(navigator.hardwareConcurrency || 2, 4);
    this.queue = [];
    this.activeTransforms = new Map();
  }
  
  async transform(task) {
    // 检查是否有相似任务正在处理（去重）
    const similarTask = this.findSimilarTask(task);
    if (similarTask) {
      return this.waitForSimilarTask(similarTask);
    }
    
    // 获取可用Worker
    const worker = await this.getAvailableWorker();
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Transform timeout'));
      }, task.options.timeout || 10000);
      
      worker.postMessage({
        type: 'TRANSFORM_RAW_AI_RESPONSE',
        payload: task
      });
      
      const handleMessage = (event) => {
        clearTimeout(timeout);
        worker.removeEventListener('message', handleMessage);
        this.releaseWorker(worker);
        
        if (event.data.success) {
          resolve(event.data);
        } else {
          reject(new Error(event.data.error));
        }
      };
      
      worker.addEventListener('message', handleMessage);
    });
  }
  
  findSimilarTask(task) {
    // 基于内容哈希检测相似任务
    const contentHash = this.quickHash(task.content);
    return Array.from(this.activeTransforms.values())
      .find(activeTask => activeTask.contentHash === contentHash);
  }
}
```

#### 2. 缓存策略

```javascript
class PreviewCacheManager {
  constructor() {
    this.cache = new Map();
    this.maxSize = 50;
    this.maxAge = 5 * 60 * 1000; // 5分钟
  }
  
  get(key) {
    const cached = this.cache.get(key);
    if (!cached) return null;
    
    // 检查是否过期
    if (Date.now() - cached.timestamp > this.maxAge) {
      this.cache.delete(key);
      return null;
    }
    
    // 更新访问时间
    cached.lastAccessed = Date.now();
    return cached.value;
  }
  
  set(key, value) {
    // 清理过期项
    this.cleanup();
    
    // 如果缓存满了，删除最久未访问的项
    if (this.cache.size >= this.maxSize) {
      this.evictOldest();
    }
    
    this.cache.set(key, {
      value: value,
      timestamp: Date.now(),
      lastAccessed: Date.now()
    });
  }
}
```

### 错误处理和降级

```javascript
class GracefulFallbackHandler {
  generateErrorHTML(error, queryId) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>预览错误</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            padding: 20px;
            background: #f8f9fa;
          }
          .error-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
          }
          .retry-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
          }
        </style>
      </head>
      <body>
        <div class="error-container">
          <h3>⚠️ 实时预览生成失败</h3>
          <p><strong>错误原因:</strong> ${error.message}</p>
          <p>请等待完整处理完成后查看Lynx Playground预览。</p>
          <button class="retry-button" onclick="parent.postMessage({type: 'retry-preview', queryId: '${queryId}'}, '*')">
            重试预览
          </button>
        </div>
      </body>
      </html>
    `;
  }
  
  generatePartialHTML(parsedContent) {
    // 即使解析不完整，也尝试生成基础预览
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>部分预览</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            padding: 16px;
            background: #f8f9fa;
            line-height: 1.5;
          }
          .partial-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 16px;
          }
          .content-preview {
            background: white;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
          }
        </style>
      </head>
      <body>
        <div class="partial-notice">
          <strong>📋 部分预览</strong> - AI响应可能尚未完成，显示当前可解析的内容
        </div>
        <div class="content-preview">
          ${this.convertPartialContentToHTML(parsedContent)}
        </div>
      </body>
      </html>
    `;
  }
}
```

## 集成方案

### 在现有系统中的集成点

1. **EnhancedBatchProcessorService.processQuery()** - 添加实时预览钩子
2. **ResultsPanel组件** - 增加实时预览显示
3. **streamParser.js** - 可选择性增强<FILES>标签处理
4. **新增Worker文件** - `realtime-lynx-transformer.js`

### 实施步骤

1. **阶段1**: 创建Web Worker转换器
2. **阶段2**: 在EnhancedBatchProcessorService中添加预览钩子
3. **阶段3**: 修改ResultsPanel支持实时预览显示
4. **阶段4**: 优化性能和错误处理
5. **阶段5**: 添加用户配置选项（可选择开启/关闭实时预览）

## 预期效果

通过这个方案，用户可以：

1. **实时看到Web预览** - 无需等待完整处理和上传
2. **快速验证效果** - 在批量处理过程中快速检查结果质量
3. **降低延迟** - 预览生成与文件处理并行进行
4. **增强体验** - 提供即时反馈，提升用户体验

该方案充分利用了batch_processor现有的处理流程，在最小改动的基础上实现了实时预览功能。