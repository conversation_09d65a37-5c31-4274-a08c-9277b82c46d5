# Lynx到Web预览简化实用方案

## 方案概述

基于现有batch_processor系统，添加一个轻量级的Lynx到Web预览功能。遵循"简单实用"原则，不过度设计，直接集成到现有流程中。

## 核心原则

- ✅ **简单直接**: 转换成功显示预览，失败显示具体错误信息
- ✅ **复用现有**: 使用现有upload接口、存储策略、UI组件
- ✅ **轻量集成**: 最小代码量，不影响现有性能
- ❌ **避免过度**: 不做多级转换、多设备适配、复杂缓存策略

## 实现方案

### 1. 核心组件结构

```
src/routes/batch_processor/
├── services/
│   └── SimpleWebPreviewService.js     # 主服务类
├── workers/
│   └── simple-lynx-converter.js       # Web Worker转换器
├── components/
│   ├── WebPreviewButton.jsx           # 预览按钮组件
│   ├── WebPreviewModal.jsx            # 放大预览弹窗
│   └── ThumbnailPreview.jsx           # 缩略图预览
└── utils/
    └── lynx-validator.js              # Lynx内容验证
```

### 2. 主服务类实现

```javascript
// src/routes/batch_processor/services/SimpleWebPreviewService.js
export class SimpleWebPreviewService {
  constructor() {
    this.worker = null;
    this.uploadService = null; // 复用现有的UploadService
  }
  
  /**
   * 检查内容是否包含Lynx代码
   */
  hasLynxContent(content) {
    if (!content || typeof content !== 'string') return false;
    
    const lynxIndicators = [
      '<FILES>',
      '<FILE',
      '<view',
      '<text',
      '<scroll-view',
      'tt:for',
      'tt:if',
      'bindtap',
      '.ttss',
      'rpx'
    ];
    
    return lynxIndicators.some(indicator => content.includes(indicator));
  }
  
  /**
   * 转换Lynx到HTML
   */
  async convertToWeb(content, resultId) {
    // 1. 验证是否包含Lynx内容
    if (!this.hasLynxContent(content)) {
      return {
        success: false,
        error: 'NO_LYNX_CONTENT',
        message: '内容中未检测到Lynx代码'
      };
    }
    
    // 2. 创建Worker（如果不存在）
    if (!this.worker) {
      this.worker = new Worker('/workers/simple-lynx-converter.js');
    }
    
    // 3. 执行转换
    try {
      const result = await this.executeConversion(content, resultId);
      
      if (result.success) {
        // 4. 生成截图并上传
        const screenshot = await this.captureAndUploadScreenshot(result.html, resultId);
        
        return {
          success: true,
          html: result.html,
          screenshot: screenshot
        };
      } else {
        return result;
      }
    } catch (error) {
      return {
        success: false,
        error: 'CONVERSION_ERROR',
        message: error.message
      };
    }
  }
  
  /**
   * 执行转换（Promise包装Worker）
   */
  executeConversion(content, resultId) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('转换超时'));
      }, 10000); // 10秒超时
      
      this.worker.onmessage = (event) => {
        clearTimeout(timeout);
        resolve(event.data);
      };
      
      this.worker.onerror = (error) => {
        clearTimeout(timeout);
        reject(error);
      };
      
      this.worker.postMessage({
        type: 'CONVERT',
        content: content,
        resultId: resultId
      });
    });
  }
  
  /**
   * 截图并上传到CDN
   */
  async captureAndUploadScreenshot(html, resultId) {
    try {
      // 创建临时iframe生成截图
      const iframe = document.createElement('iframe');
      iframe.style.cssText = `
        position: absolute;
        top: -9999px;
        left: -9999px;
        width: 375px;
        height: 667px;
        border: none;
      `;
      
      document.body.appendChild(iframe);
      iframe.srcdoc = html;
      
      // 等待加载完成
      await new Promise(resolve => {
        iframe.onload = resolve;
        setTimeout(resolve, 2000); // 最多等待2秒
      });
      
      // 使用html2canvas截图
      const canvas = await html2canvas(iframe.contentDocument.body, {
        width: 375,
        height: 667,
        scale: 1
      });
      
      // 清理iframe
      document.body.removeChild(iframe);
      
      // 转换为Blob
      const blob = await new Promise(resolve => {
        canvas.toBlob(resolve, 'image/jpeg', 0.8);
      });
      
      // 使用现有的upload接口上传
      if (!this.uploadService) {
        const { UploadService } = await import('./UploadService.ts');
        this.uploadService = new UploadService();
      }
      
      const uploadResult = await this.uploadService.uploadFile(blob, `screenshot-${resultId}.jpg`);
      
      return {
        url: uploadResult.url,
        width: 375,
        height: 667
      };
      
    } catch (error) {
      console.warn('[SimpleWebPreview] Screenshot capture failed:', error);
      return null;
    }
  }
}
```

### 3. Web Worker转换器

```javascript
// src/routes/batch_processor/workers/simple-lynx-converter.js

/**
 * 简单的Lynx到HTML转换器
 * 只做基础转换，失败直接返回错误
 */
class SimpleLynxConverter {
  constructor() {
    this.tagMap = {
      'view': 'div',
      'text': 'span',
      'image': 'img',
      'scroll-view': 'div'
    };
  }
  
  /**
   * 主转换方法
   */
  convert(content) {
    try {
      // 1. 解析文件结构
      const files = this.parseFiles(content);
      
      // 2. 检查必要文件
      if (!files['index.ttml'] && !Object.keys(files).some(f => f.endsWith('.ttml'))) {
        throw new Error('未找到TTML文件');
      }
      
      // 3. 转换各个文件
      const html = this.convertTTML(files);
      const css = this.convertTTSS(files);
      const js = this.convertJS(files);
      
      // 4. 生成完整HTML
      const fullHTML = this.generateHTML(html, css, js);
      
      return {
        success: true,
        html: fullHTML
      };
      
    } catch (error) {
      return {
        success: false,
        error: 'PARSE_ERROR',
        message: error.message
      };
    }
  }
  
  /**
   * 解析文件结构
   */
  parseFiles(content) {
    const files = {};
    
    // 移除<FILES>包装
    let processedContent = content;
    if (content.includes('<FILES>')) {
      const match = content.match(/<FILES>([\s\S]*?)<\/FILES>/);
      if (match) {
        processedContent = match[1];
      }
    }
    
    // 解析<FILE>标签
    const filePattern = /<FILE\s+path="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/g;
    let match;
    
    while ((match = filePattern.exec(processedContent)) !== null) {
      const [, path, content] = match;
      files[path] = content.trim();
    }
    
    // 如果没有找到FILE标签，尝试直接解析
    if (Object.keys(files).length === 0) {
      if (this.looksLikeTTML(content)) {
        files['index.ttml'] = content;
      } else {
        throw new Error('无法识别文件格式');
      }
    }
    
    return files;
  }
  
  /**
   * 转换TTML到HTML
   */
  convertTTML(files) {
    const tttmlFile = files['index.ttml'] || Object.values(files).find(content => 
      this.looksLikeTTML(content)
    );
    
    if (!tttmlFile) {
      throw new Error('未找到TTML内容');
    }
    
    let html = tttmlFile;
    
    // 基础标签转换
    Object.entries(this.tagMap).forEach(([lynxTag, htmlTag]) => {
      const regex = new RegExp(`<${lynxTag}(\\s[^>]*)?>`, 'g');
      html = html.replace(regex, `<${htmlTag}$1>`);
      html = html.replace(new RegExp(`</${lynxTag}>`, 'g'), `</${htmlTag}>`);
    });
    
    // 处理事件绑定
    html = html.replace(/bindtap="([^"]*)"/g, 'onclick="$1()"');
    html = html.replace(/bindlongpress="([^"]*)"/g, 'oncontextmenu="$1(); return false;"');
    
    // 处理模板语法（简单替换）
    html = html.replace(/\{\{([^}]+)\}\}/g, '<span data-bind="$1">[$1]</span>');
    html = html.replace(/tt:for="[^"]*"/g, '');
    html = html.replace(/tt:if="[^"]*"/g, '');
    html = html.replace(/tt:key="[^"]*"/g, '');
    
    return html;
  }
  
  /**
   * 转换TTSS到CSS
   */
  convertTTSS(files) {
    const ttssFile = files['index.ttss'] || Object.values(files).find(content => 
      this.looksLikeTTSS(content)
    );
    
    if (!ttssFile) {
      return this.getDefaultCSS();
    }
    
    let css = ttssFile;
    
    // rpx单位转换（1rpx = 0.5px on 375px width）
    css = css.replace(/(\d+(\.\d+)?)rpx/g, (match, value) => {
      return `${parseFloat(value) * 0.5}px`;
    });
    
    // 移除Lynx特有属性
    css = css.replace(/enable-scroll\s*:\s*[^;]+;/g, '');
    css = css.replace(/scroll-[xy]\s*:\s*[^;]+;/g, '');
    
    return css;
  }
  
  /**
   * 转换JS（简单处理）
   */
  convertJS(files) {
    const jsFile = files['index.js'] || Object.values(files).find(content => 
      this.looksLikeJS(content)
    );
    
    if (!jsFile) {
      return this.getDefaultJS();
    }
    
    // 简单的语法转换
    let js = jsFile;
    js = js.replace(/Card\s*\(\s*\{/, 'const component = {');
    js = js.replace(/this\.setData/g, 'component.setData');
    
    return js;
  }
  
  /**
   * 生成完整HTML
   */
  generateHTML(html, css, js) {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lynx Preview</title>
    <style>
        /* 默认样式 */
        body {
            margin: 0;
            padding: 16px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f8f9fa;
        }
        
        /* Lynx兼容样式 */
        div {
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }
        
        span {
            display: inline-block;
        }
        
        /* 用户样式 */
        ${css}
    </style>
</head>
<body>
    ${html}
    
    <script>
        // 基础功能
        function setData(data) {
            console.log('setData called:', data);
        }
        
        // 用户脚本
        ${js}
        
        // 错误处理
        window.onerror = function(msg, url, line) {
            console.error('Preview Error:', msg, 'at line', line);
        };
    </script>
</body>
</html>`;
  }
  
  /**
   * 内容类型检测
   */
  looksLikeTTML(content) {
    return content.includes('<view') || content.includes('<text') || content.includes('tt:');
  }
  
  looksLikeTTSS(content) {
    return content.includes('rpx') || content.includes('flex-direction') || content.includes('.');
  }
  
  looksLikeJS(content) {
    return content.includes('Card(') || content.includes('setData') || content.includes('function');
  }
  
  /**
   * 默认样式和脚本
   */
  getDefaultCSS() {
    return `
      .container { padding: 20px; }
      .text { color: #333; font-size: 14px; }
    `;
  }
  
  getDefaultJS() {
    return `
      console.log('Lynx preview loaded');
    `;
  }
}

// Worker消息处理
const converter = new SimpleLynxConverter();

self.onmessage = function(event) {
  const { type, content, resultId } = event.data;
  
  if (type === 'CONVERT') {
    const result = converter.convert(content);
    self.postMessage(result);
  }
};
```

### 4. UI组件实现

```javascript
// src/routes/batch_processor/components/WebPreviewButton.jsx
import React, { useState } from 'react';
import { SimpleWebPreviewService } from '../services/SimpleWebPreviewService';

export const WebPreviewButton = ({ result, onPreviewGenerated }) => {
  const [loading, setLoading] = useState(false);
  const [preview, setPreview] = useState(null);
  const [error, setError] = useState(null);
  
  const previewService = new SimpleWebPreviewService();
  
  const handleGeneratePreview = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const previewResult = await previewService.convertToWeb(
        result.extractedContent,
        result.id
      );
      
      if (previewResult.success) {
        setPreview(previewResult);
        onPreviewGenerated?.(result.id, previewResult);
      } else {
        setError(previewResult.message);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  if (error) {
    return (
      <div className="web-preview-error">
        <span className="error-text">预览失败: {error}</span>
        <button onClick={handleGeneratePreview} className="retry-btn">
          重试
        </button>
      </div>
    );
  }
  
  if (preview) {
    return (
      <ThumbnailPreview 
        preview={preview}
        resultId={result.id}
      />
    );
  }
  
  return (
    <button 
      onClick={handleGeneratePreview}
      disabled={loading}
      className="web-preview-btn"
    >
      {loading ? '生成中...' : 'Web预览'}
    </button>
  );
};

// src/routes/batch_processor/components/ThumbnailPreview.jsx
import React, { useState } from 'react';
import { WebPreviewModal } from './WebPreviewModal';

export const ThumbnailPreview = ({ preview, resultId }) => {
  const [showModal, setShowModal] = useState(false);
  
  return (
    <>
      <div className="thumbnail-preview" onClick={() => setShowModal(true)}>
        <div className="thumbnail-container">
          {preview.screenshot ? (
            <img 
              src={preview.screenshot.url}
              alt="Web预览"
              className="thumbnail-image"
            />
          ) : (
            <iframe
              srcDoc={preview.html}
              className="thumbnail-iframe"
              title={`预览-${resultId}`}
            />
          )}
          <div className="thumbnail-overlay">
            <span>点击放大</span>
          </div>
        </div>
      </div>
      
      {showModal && (
        <WebPreviewModal
          preview={preview}
          resultId={resultId}
          onClose={() => setShowModal(false)}
        />
      )}
    </>
  );
};

// src/routes/batch_processor/components/WebPreviewModal.jsx
import React from 'react';

export const WebPreviewModal = ({ preview, resultId, onClose }) => {
  return (
    <div className="web-preview-modal" onClick={onClose}>
      <div className="modal-content" onClick={e => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Web预览 - {resultId}</h3>
          <button onClick={onClose} className="close-btn">×</button>
        </div>
        <div className="modal-body">
          <iframe
            srcDoc={preview.html}
            className="full-preview-iframe"
            title={`完整预览-${resultId}`}
          />
        </div>
      </div>
    </div>
  );
};
```

### 5. 集成到ResultsPanel

```javascript
// 在现有的ResultsPanel中添加Web预览功能
import { WebPreviewButton } from './WebPreviewButton';

const ResultsPanel = ({ results }) => {
  const [webPreviews, setWebPreviews] = useState(new Map());
  
  const handlePreviewGenerated = (resultId, preview) => {
    setWebPreviews(prev => new Map(prev.set(resultId, preview)));
  };
  
  return (
    <div className="results-panel">
      {results.map(result => (
        <div key={result.id} className="result-item">
          <div className="result-header">
            <h3>{result.query}</h3>
            <div className="preview-controls">
              <a 
                href={result.playgroundUrl}
                target="_blank"
                className="playground-btn"
              >
                Lynx Playground
              </a>
              <WebPreviewButton
                result={result}
                onPreviewGenerated={handlePreviewGenerated}
              />
            </div>
          </div>
          
          <div className="result-content">
            {result.extractedContent}
          </div>
        </div>
      ))}
    </div>
  );
};
```

### 6. 样式文件

```css
/* Web预览相关样式 */
.web-preview-btn {
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.web-preview-btn:hover {
  background: #0056b3;
}

.web-preview-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.web-preview-error {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-text {
  color: #dc3545;
  font-size: 12px;
}

.retry-btn {
  padding: 4px 8px;
  background: #ffc107;
  color: #212529;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 11px;
}

.thumbnail-preview {
  cursor: pointer;
  margin-top: 8px;
}

.thumbnail-container {
  position: relative;
  width: 120px;
  height: 160px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  overflow: hidden;
}

.thumbnail-image,
.thumbnail-iframe {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: none;
}

.thumbnail-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0,0,0,0.7);
  color: white;
  text-align: center;
  padding: 4px;
  font-size: 11px;
  opacity: 0;
  transition: opacity 0.3s;
}

.thumbnail-container:hover .thumbnail-overlay {
  opacity: 1;
}

.web-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90vw;
  height: 90vh;
  max-width: 600px;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #dee2e6;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
}

.modal-body {
  flex: 1;
  padding: 16px;
}

.full-preview-iframe {
  width: 100%;
  height: 100%;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}
```

## 集成步骤

### 1. 添加Worker文件
将`simple-lynx-converter.js`放到`public/workers/`目录

### 2. 在EnhancedBatchProcessorService中集成
```javascript
// 在processQuery方法的结果处理部分添加
if (result.success && result.playgroundUrl) {
  // 异步生成Web预览（不阻塞主流程）
  this.generateWebPreviewAsync(result).catch(console.warn);
}
```

### 3. 修改ResultsPanel
导入WebPreviewButton组件并添加到预览控制区域

### 4. 添加必要的依赖
```bash
npm install html2canvas
```

## 特性说明

### ✅ 支持的功能
- 基础Lynx标签转换（view、text、image等）
- rpx单位转换
- 简单事件绑定转换
- 移动端缩略图预览（375×667）
- 点击放大查看完整预览
- 截图自动上传CDN存储

### ❌ 不支持的功能
- 复杂模板语法（tt:for、tt:if）
- 多设备适配
- 复杂动画效果
- Canvas高级功能
- 多层转换策略

### 错误处理
- 无Lynx内容：显示"内容中未检测到Lynx代码"
- 解析失败：显示具体错误信息
- 转换超时：10秒超时，显示"转换超时"
- 截图失败：降级为iframe预览

这个方案保持了简单实用的特点，完全基于现有系统，最小化代码量和性能影响。