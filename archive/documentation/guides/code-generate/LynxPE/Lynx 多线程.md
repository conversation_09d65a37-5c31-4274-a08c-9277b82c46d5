https://bytedance.larkoffice.com/docx/VX5CdJl6WoJJnFxnOn8cPKVGn3d



1. Lynx Card的基本概念和功能概述
1.1 什么是Lynx Card
Lynx 是字节跳动开发的高性能跨端解决方案，它利用 Web 技术栈来快速构建 Native 视图。而 Lynx Card 是 Lynx 的一个特定应用场景，专门设计用于将 Lynx 页面嵌入到客户端视图内部。
Lynx Card 模式的核心理念是"轻量级设计"，侧重于重展示，轻交互的场景，其数据更新主要由客户端触发，并采用了精简的渲染架构以最快速地展示视图。
1.2 核心概念与特性
基础架构特点
- 独立运行实例：在 Lynx Card 模式下，取消了小程序中的 "App" 和 "Page" 概念，每个 "Card" 作为一个独立运行的实例存在，Card 之间没有直接关联。
- 精简渲染架构：为了适应卡片嵌入场景，Lynx Card 设计了精简的渲染架构，以最快速地展示视图。
- UI 下沉：UI 下沉到 C++，避免 JavaOnlyMap 和 UI Ops 耗时，提升渲染性能。
性能优化机制
- 预创建能力：对于特定配置的 Lynx 卡片，支持卡片预创建，在卡片插入后创建容器并加载 Lynx，使前端页面可以提前加载。
- Engine 复用：同一模板的卡片可以共用一个 Engine，减少资源消耗。
- LynxGroup：针对同一 native 页面有多个 LynxView 的场景，引入分组概念，同一组的 LynxView 共享同一个 JS 引擎 Context，加快启动速度。
功能支持
- 生命周期回调：支持客户端监听卡片的生命周期回调。
- 交互处理：
  - 支持点击事件的客户端回调
  - 在某些场景下，允许前端事件里通过 setNative Props/worklet 修改节点样式或执行 bridge 调用
  - 支持 CustomEvent，例如 Image 的 load 事件以客户端回调方式返回
- 组件支持：
  - 内置组件支持 text、view、image、scrollview，且不允许外部覆盖
  - 支持新增自定义组件，但需要重新适配
- 样式与排版：
  - 要求使用 SimpleStyling 编写样式，属性均使用 built-in attribute
  - 支持 linear、flex 排版能力
  - 不支持层级调整（如 z-index, position:fixed, position:sticky, translateZ）
  - 不支持客户端 updateViewPort 操作，只允许内容撑开
- 动画能力：只支持平台层 keyframes 动画
1.3 接入方式
Lynx Card 主要有两种接入方式：
无需端上开发
- 前端完成页面开发后，由服务器下发正确的 FeedEventCardInfo 信息即可在端上展示卡片
- 默认使用主框架的 AlienCardTemplateFactory 进行相关配置
- 适用于简单展示场景，无需额外客户端开发工作
需要端上额外开发
- 如果业务有特定的业务逻辑需求，如监听卡片生命周期或自定义卡片容器配置，需要增加客户端逻辑
- 可以通过实现 AbsAlienCardFactory 抽象类
- 业务方还可以通过实现 AbsAlienCardCallback 来监听卡片的生命周期
1.4 使用场景
Lynx Card 主要适用于以下场景：
- 承接 AI 搜索的部分模块
- 适用于 lynx 页面嵌入到客户端视图内的场景
- 重展示、轻交互的业务需求
- 需要高性能渲染且资源消耗较低的卡片展示
1.5 安全合规
Lynx 容器支持完善的安全合规管控机制：
- 资源验证：可执行代码资源全局加签&验签
- 调用鉴权：端能力调用鉴权能力
- 身份认证：通过在 Lynx 资源内容末尾增加前端安全身份标识 fe_id 实现身份认证，证明资源可信
2. Lynx Card的函数定义方式和语法详解
在 Lynx Card 的开发模式中，函数或行为的定义是构建交互功能的关键部分。本章节将详细介绍 Lynx Card 中函数的定义方式、调用方式以及相关语法元素。
2.1 函数/行为的定义方式
在 Lynx Card 中，函数或行为主要定义在卡片的配置文件（例如 index.json 或 config.json）的 actions 部分。这些定义允许开发者声明 JSB (JavaScript Bridge) 调用、数据操作、逻辑运算以及其他行为。
actions 对象是 Lynx Card 中所有函数/行为的集合，每个键值对定义了一个具名的行为或函数。键是行为的名称，值是其定义。
函数/行为定义主要有两种形式：
通过 method 定义 JSB 调用
用于声明对客户端提供的 JSB 能力的调用。
主要特点：
- 需要指定 method 字段，值为要调用的 JSB 方法名
- params 字段用于传递参数，值是一个对象
- 可选的 callback 字段，值是一个 expression，在 JSB 调用返回后执行
通过 expression 定义逻辑或内部调用
用于执行运算、调用其他 actions 或实现更复杂的逻辑。
主要特点：
- expression 的值可以是字符串（单个表达式）或数组（多个表达式按顺序执行）
- 支持 $call 调用其他 actions
- 支持 $setData 更新页面数据
- 支持使用运算符和访问数据
2.1.1 通过 method 定义 JSB 调用示例
"showToast": {
  "method": "x.showToast",
  "params": {
    "message": "hello world",
    "type": "success",
    "icon": "success",
    "duration": 1000
  }
},
"vibrate": {
  "method": "x.vibrate",
  "params": { "duration": "$.data.count", "style": "heavy" },
  "callback": {
    "if": {
      "expression": "$.vibrate.code === 1"
    },
    "then": {
      "expression": "$call('stringAddWithParam', {str: $.vibrate.data.recvJsCallTime})"
    }
  }
}

2.1.2 通过 expression 定义逻辑或内部调用示例
"add": {
  "expression": "$setData({count: $.data.count + 1})"
},
"stringAdd": {
  "expression": "$setData({ calcValue: @str('defaultString') + $.data.count })"
},
"highCaller": {
  "expression": ["ajax1", "ajax2"],
  "then": {
    "expression": ["ajax1"]
  }
}

2.2 函数/行为的调用方式
Lynx Card 中定义的函数/行为可以通过以下两种主要方式调用：
2.2.1 通过 TTML 元素的事件绑定
使用 x-bindtap 属性将元素的点击事件绑定到 actions 中定义的行为。
<view x-bindtap="showToast">
    <text>显示提示</text>
</view>
<view x-bindtap="add">
    <text>增加计数</text>
</view>

- x-bindtap 的值是 actions 中定义的行为名称
- 可以传递参数，例如 x-bindtap="call('request1', { p: {{item}} })"
2.2.2 通过 $call 在 expression 中调用
在其他 actions 的 expression、callback 或容器事件 (onShow/onHide) 的 expression 中，可以使用 $call 语法调用 actions。
"onShow": {
  "expression": ["$call('showToast')"]
},
"vibrate": {
  "method": "x.vibrate",
  "params": { "duration": "$.data.count", "style": "heavy" },
  "callback": {
    "then": {
      "expression": "$call('stringAddWithParam', {str: $.vibrate.data.recvJsCallTime})"
    }
  }
}

- $call 的第一个参数是要调用的 action 名称
- 第二个参数是一个对象，用于传递参数给被调用的 action
2.3 其他相关语法元素
Lynx Card 中还支持多种语法元素，用于数据访问、参数传递、条件判断和运算：
2.3.1 $.* (全局数据访问)
用于访问配置文件中的所有属性以及命名 $id 的对象数据。
- $.data 特指当前页面的 data 属性
- 例如：$.data.count 或 $.request1.data
2.3.2 @* (参数传递)
用于在表达式中引用传递进来的参数。
- 表达式格式：@property(defaultValue)
  - property 是参数的 key
  - defaultValue 是默认值（可选）
- 例如：@fold 或 @clickProp1("666")
2.3.3 条件语句
允许在 actions 中执行条件逻辑。
"conditionalAction": {
  "if": {
    "expression": "$.data.count > 10"
  },
  "then": {
    "expression": "$call('showToast')"
  },
  "else": {
    "expression": "$call('vibrate')"
  }
}

- if 包含条件表达式
- then 包含条件为真时执行的表达式
- else 包含条件为假时执行的表达式（可选）
2.3.4 运算符
在 expression 中支持使用多种运算符进行计算：
- 算术运算符：+, -, *, /
- 比较运算符：>, <, ==, ===, !=, !==
- 逻辑运算符：&&, ||, !
注意：在 Lynx Card 中使用表达式时，需要遵循特定的语法规则，确保表达式的正确性和可读性。复杂的逻辑建议拆分为多个小的 action，通过 $call 组合使用。
3. Lynx Card的使用模式和最佳实践
Lynx Card 模式是一种专为创建独立、高性能动态卡片而设计的开发和运行时模型，它充分利用了 Lynx 跨平台渲染服务的优势。本章节将详细介绍 Lynx Card 的使用模式和最佳实践，帮助开发者更高效地开发和优化 Lynx Card 应用。
3.1 关键特性和改进
Lynx Card 模式相比传统小程序模式有以下关键特性和改进：
Lynx Card 模式取消了传统小程序的"App"和"Page"概念，每个"Card"实例都是一个独立的运行时实体，卡片之间没有直接关联，这种设计使得卡片更加轻量化和高效。
独立实例设计
- 每个 Card 作为独立运行实例存在，没有全局状态共享
- 简化了卡片间的依赖关系，降低了复杂度
- 更适合嵌入式场景和轻交互需求
LynxGroup 性能优化
- 针对单个原生页面上有多个 LynxView 的场景引入 LynxGroup 概念
- 同一组内的 LynxView 共享同一个 JavaScript 引擎上下文
- 只需为该组初始化一次 JS 运行时环境，显著加快启动速度
- 建议将同一原生页面上的 LynxView 添加到同一组中
改进的项目结构
- 工程目录结构经过优化，更适合业务团队的典型用例
- 包含一个用于存放多个卡片的 cards 目录
- 每个卡片都有自己的资源、JS、JSON、Ttml（模板）和 Ttss（样式）文件
- 全局的 project.config.json 文件用于项目配置
3.2 开发工具与流程
开发工具选择
小程序 IDE
- 传统的开发环境
- 提供完整的小程序开发功能
- 适合熟悉小程序开发的开发者
LynxStudio（推荐）
- 基于 VSCode 的专用开发环境
- 提供更现代的开发体验
- 支持更丰富的编辑和调试功能
- 更好的性能和扩展性
项目创建流程
1. 创建新项目
  - 在 IDE 中选择"Card"模式
  - 指定项目路径和名称
  - 系统会自动生成项目基础结构
2. 导入现有项目
  - 也可以导入现有的 Lynx 项目
  - 需要确保项目结构符合 Card 模式的要求
3. 项目结构说明
  - project.config.json：全局项目配置文件
  - cards/ 目录：存放所有卡片
  - 每个卡片目录包含：
    - index.json：卡片配置文件
    - index.ttml：模板文件
    - index.ttss：样式文件
    - index.js：JavaScript 逻辑文件（可选）
3.3 下发方案
Lynx Card 有两种主要的下发方案，各有优缺点：
Gecko 下载方案
- 配置流程：
  - 在 Gecko 中添加通道
  - 在设置中进行配置
  - 将模板上传到 Gecko
- 服务器实现：
  - 发送 cell_type 210
  - 在 raw_data 中包含 lynx_server 数据
  - 提供 template_channel 和 template_key
- 优缺点：
  - 优点：配置简单，适合静态模板
  - 缺点：依赖于 Gecko 的下载时机和成功率
Feed 接口下载方案
- 配置流程：
  - 在 Lynx 卡片配置平台中配置模板
  - 设置模板名称和版本
- 服务器实现：
  - 发送 cell_type 211
  - 在 raw_data 中包含 lynx_server 数据
  - 提供 lynx_template_name
- 优缺点：
  - 优点：提高了动态性和稳定性
  - 缺点：可能影响 Feed 请求的持续时间和成功率，尤其是首次请求
最佳实践：对于需要频繁更新的动态卡片，推荐使用 Feed 接口下载方案；对于较为稳定的卡片，可以考虑使用 Gecko 下载方案。
3.4 性能优化建议
基于 Lynx Cards 和 Native Cards 的性能比较，提出以下优化建议：
启用异步布局
- Lynx Cards 在启用异步布局时，创建和布局性能优于 Native Cards
- 建议在配置中启用异步布局功能，特别是对于复杂卡片
优化冷启动性能
- Lynx Cards 的冷启动性能可能不如 Native Cards
- 建议使用预创建能力和 LynxGroup 来提升冷启动性能
- 对于频繁使用的卡片，可以考虑预加载策略
复用优化
- 利用 Lynx Cards 的复用性能优势
- 对于相同模板的卡片，尽量使用 Engine 复用机制
- 合理设计 LynxGroup，将同一页面的卡片归入同一组
内存使用优化
- 监控内存使用情况，避免内存泄漏
- 对于不再需要的卡片，及时释放资源
- 使用轻量级的数据结构和资源
性能黄金法则：在 Lynx Card 开发中，应始终遵循"轻量级设计"原则，专注于"重展示，轻交互"的场景，避免在卡片中实现过于复杂的逻辑和交互。
4. 多线程环境下Lynx Card的定义延迟问题及其原因分析
在多线程环境下，Lynx Card 可能会遇到函数定义延迟的问题，这对于依赖及时响应的交互式应用可能会产生影响。本章节将分析这一问题及其可能的原因。
4.1 问题描述
需要注意的是，关于 Lynx Card 在多线程环境下存在定义延迟问题的具体细节在公开资料中较为有限。以下分析基于 Lynx 框架的一般多线程特性和常见的多线程编程挑战。
多线程环境下的函数定义延迟问题主要表现为：
- 在某些情况下，函数定义（如在 actions 中定义的行为）可能无法立即在其他线程中可用
- 这种延迟可能导致用户交互响应不及时，或者在某些情况下出现未定义函数的错误
- 当多个 Lynx Card 实例在同一页面上同时运行时，这个问题可能更为明显
4.2 可能的原因分析
4.2.1 多线程渲染模型的影响
Lynx 采用了多线程渲染模型，将模板渲染过程的部分工作分派到子线程上。这种设计虽然提高了性能，但也带来了一些挑战：
- 线程间通信延迟：主线程（UI线程）和负责解析与执行JavaScript的线程之间的通信需要时间
- 线程同步问题：当函数定义在一个线程中完成，但需要在另一个线程中使用时，可能会出现同步问题
- 资源竞争：多个线程可能同时访问和修改共享资源，导致意外行为
4.2.2 JavaScript引擎的单线程特性
JavaScript 本身是单线程执行的，Lynx Card 中的函数定义依赖于 JavaScript 引擎：
- 当 JavaScript 引擎在一个线程中执行代码时，其他线程可能无法立即访问新定义的函数
- 即使 Lynx 使用多线程渲染，JavaScript 执行环境仍然是单线程的
- 这种单线程与多线程混合的架构可能导致函数定义的可见性延迟
4.2.3 LynxGroup 和共享上下文的复杂性
Lynx 引入了 LynxGroup 概念，允许多个 LynxView 共享同一个 JavaScript 引擎上下文：
LynxGroup 的优势
- 提高性能，减少资源消耗
- 只需初始化一次 JS 运行时环境
- 加快卡片启动速度
LynxGroup 的挑战
- 增加了上下文共享的复杂性
- 可能导致函数定义的延迟传播
- 需要额外的同步机制
当多个卡片共享同一个 JavaScript 上下文时，函数定义的传播和同步变得更加复杂，可能导致定义延迟问题。
4.2.4 异步加载和执行
Lynx Card 支持异步加载和执行，这可能导致函数定义的时序问题：
- 卡片资源可能以异步方式加载
- 函数定义可能在某些组件初始化后才完成
- 如果在函数完全定义前尝试调用它，可能会导致错误或延迟响应
4.3 解决思路
虽然公开资料中关于具体解决方案的信息有限，但基于多线程编程的一般原则，可以考虑以下解决思路：
- 确保函数预定义：在卡片初始化阶段预先定义所有可能需要的函数，避免运行时动态定义
- 使用回调机制：对于可能受到定义延迟影响的函数调用，使用回调或Promise机制确保在函数可用时才执行
- 适当的线程同步：在关键点使用适当的线程同步机制，确保函数定义在需要时可用
- 监控和重试机制：实现监控机制，在函数调用失败时进行适当的重试
最佳实践：在开发 Lynx Card 时，尽量在卡片初始化阶段完成所有函数定义，避免在运行时动态创建函数。对于需要动态创建的函数，考虑使用事件监听或回调机制确保其可用性。
5. Lynx Card的线程安全性考量
Lynx 框架采用了多线程架构以提升性能，但这也带来了线程安全方面的挑战。本章节将探讨 Lynx Card 在多线程环境下的线程安全性考量，帮助开发者更好地理解和应对潜在的线程安全问题。
5.1 多线程模型概述
Lynx 的多线程模型设计初衷是为了提高渲染性能和用户体验：
Lynx 的多线程模型将模板渲染过程的部分工作（如 Element Tree 创建、Element Resolve 和 Layout）分派到子线程上，使主（UI）线程主要负责 UI 渲染操作（UI Flush, Draw），从而减轻主线程的负载并支持异步预加载能力。
线程策略配置
Lynx 提供了多种线程策略配置选项，可以通过 LynxViewBuilderParams.parsUri 中的参数 thread_strategy 进行设置：
基本线程策略
- ALL_ON_UI：所有操作都在 UI 线程上执行
- MOST_ON_TASM：大部分操作在 TASM 线程上执行
- PART_ON_LAYOUT：部分布局操作在专门的布局线程上执行
高级线程策略
- MULTI_THREADS：使用多个线程并行处理不同任务
- 自定义策略：根据业务需求定制线程分配策略
- 混合策略：结合不同策略的优点
选择合适的线程策略对于平衡性能和线程安全至关重要。一般而言，对于简单的卡片，可以使用 ALL_ON_UI 策略以避免线程安全问题；而对于复杂的卡片，可以考虑使用多线程策略以提高性能，但需要更加注意线程安全问题。
5.2 线程安全挑战
在 Lynx Card 的多线程环境中，存在以下线程安全挑战：
5.2.1 共享数据访问
- 问题：多个线程同时访问和修改共享数据可能导致数据不一致或竞态条件
- 影响：可能导致 UI 显示异常、功能错误或应用崩溃
- 常见场景：多个卡片访问同一个全局状态或共享资源
5.2.2 线程间同步
- 问题：线程间的执行顺序不确定，可能导致依赖关系被破坏
- 影响：函数调用时序错误，导致预期外的行为
- 常见场景：一个线程定义的函数被另一个线程调用时
5.2.3 死锁风险
- 问题：多个线程互相等待对方持有的资源，导致程序无法继续执行
- 影响：应用卡死或响应缓慢
- 常见场景：复杂的线程交互场景，特别是在初始化期间
5.3 线程安全解决方案
基于 Lynx 框架的特性和多线程编程的最佳实践，以下是一些提高 Lynx Card 线程安全性的解决方案：
5.3.1 使用 shared_mutext
为了保证在多个 TASM 线程活跃时的线程安全，Lynx 框架建议使用 shared_mutext 机制进行线程同步。
- 适用场景：多个线程需要访问和修改同一个共享资源
- 实现方式：在访问共享资源前获取锁，访问完成后释放锁
- 注意事项：过度使用锁可能导致性能下降，应当谨慎平衡线程安全和性能
5.3.2 异步操作设计
Lynx Card 中的许多操作被设计为异步的，以适应多线程环境：
- 客户端数据访问：如获取共享数据 (getSessionStorageItem) 被设计为异步操作
- 回调机制：使用回调函数处理异步操作的结果，避免阻塞线程
- Promise 模式：对于复杂的异步操作序列，可以考虑使用 Promise 模式进行管理
5.3.3 线程隔离
- 数据隔离：尽可能减少线程间共享的数据，每个线程维护自己的状态
- 消息传递：通过消息队列或事件系统在线程间传递数据，而非直接共享
- 线程本地存储：使用线程本地存储机制存储线程特定的数据
5.3.4 避免复杂的线程交互
- 简化设计：避免复杂的线程间依赖关系
- 明确的线程职责：每个线程有明确的职责，减少线程间交互
- 批处理操作：将多个小操作合并为一个批处理操作，减少线程切换
5.4 历史线程问题及教训
历史崩溃报告表明，Lynx 中曾出现过线程问题，特别是死锁，有时与初始化期间或主线程与子线程之间的锁定机制有关：
- 初始化死锁：在组件初始化期间，多个线程可能互相等待对方持有的资源
- 线程间通信问题：主线程与子线程之间的通信不畅可能导致状态不一致
- 资源释放问题：线程退出时未正确释放资源可能导致内存泄漏或其他线程访问已释放的资源
经验教训：在 Lynx Card 开发中，应当特别注意初始化阶段的线程安全问题，确保资源的正确初始化和释放，以及线程间通信的可靠性。定期进行线程安全审查和压力测试可以帮助发现潜在的线程问题。
5.5 线程安全最佳实践
基于上述分析，以下是在 Lynx Card 开发中保证线程安全的最佳实践：
1. 选择合适的线程策略：根据卡片的复杂度和性能需求选择适当的线程策略
2. 最小化共享状态：减少线程间共享的数据，使用消息传递代替直接共享
3. 使用适当的同步机制：在必要时使用 shared_mutext 等同步机制
4. 异步操作设计：将可能阻塞的操作设计为异步，使用回调或 Promise 处理结果
5. 避免复杂的线程交互：保持简单的线程模型，明确线程职责
6. 全面的测试：进行多线程环境下的压力测试和边界测试
7. 监控和日志：实现有效的监控和日志机制，及时发现线程问题
6. 总结与建议
6.1 Lynx Card 技术特点总结
Lynx Card 作为字节跳动开发的高性能跨端解决方案 Lynx 的特定应用场景，具有以下核心技术特点：
Lynx Card 采用了"轻量级设计"理念，专注于"重展示，轻交互"的场景，通过精简渲染架构、独立实例设计和多种性能优化机制，实现了高效的卡片渲染和交互体验。
架构设计亮点
- 独立运行实例：取消了传统小程序的"App"和"Page"概念，每个"Card"作为独立实体存在
- 精简渲染架构：针对卡片嵌入场景优化，以最快速地展示视图
- 多线程渲染模型：将渲染任务分派到不同线程，减轻主线程负载
性能优化机制
- 预创建能力：支持卡片预创建，提前加载前端页面
- Engine 复用：同一模板的卡片共用一个 Engine，减少资源消耗
- LynxGroup：同一组的 LynxView 共享 JS 引擎 Context，加快启动速度
- UI 下沉：UI 下沉到 C++，避免 JavaOnlyMap 和 UI Ops 耗时
功能与语法特性
- 丰富的函数定义方式：支持通过 method 定义 JSB 调用和通过 expression 定义逻辑
- 灵活的调用机制：支持通过 TTML 元素事件绑定和 $call 在表达式中调用
- 完善的语法元素：支持全局数据访问、参数传递、条件语句和各种运算符
6.2 应用场景与价值
Lynx Card 在以下场景中展现了独特价值：
适用场景
- AI 搜索的部分模块
- Lynx 页面嵌入到客户端视图内
- 重展示、轻交互的业务需求
- 需要高性能渲染的卡片展示
业务价值
- 提高渲染性能和用户体验
- 减少资源消耗，优化内存使用
- 简化开发流程，提高开发效率
- 增强跨平台兼容性
Lynx Card 通过其轻量级设计和高性能特性，为嵌入式卡片场景提供了理想的解决方案，特别适合需要快速渲染和展示的业务场景。
6.3 潜在挑战与解决思路
在使用 Lynx Card 过程中，开发者可能面临以下挑战：
多线程环境下的定义延迟问题
- 挑战：函数定义可能无法立即在其他线程中可用，导致交互响应不及时
- 解决思路：
  - 在卡片初始化阶段预先定义所有可能需要的函数
  - 使用回调或 Promise 机制确保函数在可用时才执行
  - 实施适当的监控和重试机制
线程安全性问题
- 挑战：多线程环境下可能出现共享数据访问冲突、线程同步问题和死锁风险
- 解决思路：
  - 使用 shared_mutext 等同步机制
  - 采用异步操作设计和线程隔离策略
  - 避免复杂的线程交互，保持简单的线程模型
性能优化平衡
- 挑战：在保证功能正常的前提下实现最佳性能
- 解决思路：
  - 启用异步布局提升创建和布局性能
  - 使用预创建能力和 LynxGroup 优化冷启动性能
  - 合理利用 Engine 复用机制减少资源消耗
6.4 最佳实践建议
基于对 Lynx Card 技术特点和潜在挑战的分析，提出以下最佳实践建议：
核心原则：在 Lynx Card 开发中，始终遵循"轻量级设计"理念，专注于"重展示，轻交互"的场景，避免在卡片中实现过于复杂的逻辑和交互。
开发与设计建议
1. 合理选择下发方案：
  - 对于需要频繁更新的动态卡片，使用 Feed 接口下载方案
  - 对于较为稳定的卡片，考虑使用 Gecko 下载方案
2. 优化函数定义与调用：
  - 在卡片初始化阶段完成所有函数定义
  - 对于复杂逻辑，拆分为多个小的 action，通过 $call 组合使用
  - 使用异步操作和回调机制处理可能的延迟问题
3. 线程安全策略：
  - 根据卡片复杂度选择合适的线程策略
  - 最小化线程间共享的数据，使用消息传递代替直接共享
  - 在必要时使用适当的同步机制，如 shared_mutext
4. 性能优化措施：
  - 启用异步布局功能，特别是对于复杂卡片
  - 合理设计 LynxGroup，将同一页面的卡片归入同一组
  - 监控内存使用情况，及时释放不再需要的资源
5. 测试与监控：
  - 进行多线程环境下的压力测试和边界测试
  - 实现有效的监控和日志机制，及时发现潜在问题
  - 定期进行线程安全审查和性能评估
通过遵循这些最佳实践，开发者可以充分发挥 Lynx Card 的优势，创建高性能、稳定可靠的卡片应用，为用户提供流畅的交互体验。