Lynx 框架中存在以下特殊的 CSS 规则和禁止使用的属性，基于参考信息归纳如下：
特殊 CSS 规则 ：
后代选择器限制 ：默认仅支持二级后代选择器（如.parent .child），超过层级可能不生效
。
样式隔离机制 ：组件间 CSS 类名隔离，父组件无法直接覆盖子组件样式，需通过单独 CSS 文件或 props 传递
。
布局默认行为 ：
默认使用 Flex 布局 （非传统盒模型），推荐用 linear 布局 优化性能
。
box-sizing 默认为border-box（Web 默认为content-box），影响宽度 / 高度计算
。
position 默认为relative（Web 默认为static），且fixed定位仅作用于<scroll-view>父节点
。
单位与继承 ：
使用 rpx 作为响应式单位
。
文字样式继承 默认关闭（需配置enableCSSInheritance），文字属性需直接应用于<text>组件
。
伪类 / 伪元素限制 ：
伪类仅支持:not，且影响性能，需谨慎使用
。
伪元素（如::after）仅对<text>组件有效，且需开关支持（默认关闭）
。
禁止或不推荐的 CSS 属性 ：
布局相关 ：
grid布局 不支持 
。
gap属性 不支持 
。
display: inline 无效 ，元素默认为块级（除<text>内元素）
。
性能敏感属性 ：
box-shadow：增加额外层级，开销大，推荐用图片替代
。
opacity：可能触发离屏渲染，导致性能下降，建议用color的 alpha 值替代
。
不一致的border-radius：参数不一致时增加内存消耗，推荐参数统一
。
选择器限制 ：
属性选择器（如[attr]） 不支持 
。
多类选择器（如.foo.bar） 不支持 
。
组合选择器（除后代选择器外）在 Lynx 2.9 前 不支持 
。
其他禁用属性 ：
filter（如阴影 / 模糊） 不支持 
。
z-index：需用transformZ模拟，效果有限

 不需要使用 webkit 前缀
。