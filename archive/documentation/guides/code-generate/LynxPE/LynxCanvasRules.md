Lynx Canvas 代码规则与最佳实践
1. Lynx Canvas 基本概念和架构
1.1 基本概念
Lynx Canvas 是 Lynx 框架中的核心渲染组件，构建于自研的跨平台互动引擎 Krypton 之上。它为前端开发者提供了标准的 2D (W3C Canvas API) 和 WebGL (1.0 及常用 2.0 扩展) 图形接口，使开发者能够使用 Web 技术栈快速构建高性能的原生视图。
Lynx Canvas 的主要能力包括：
- 标准的 2D 和 WebGL 图形接口
- 多媒体能力 (Image, Video, Audio, Camera, RTC)
- 算法能力 (人脸识别, SLAM, 美颜, 滤镜等)
- 传感器、网络访问、手势交互、文件 I/O 和 XR 等功能
它广泛应用于应用程序中的各种互动场景，包括信息流广告、UG 活动、电商、直播和小游戏等。(Canvas-NG 基于 Krypton)：
- Krypton 运行在与 Lynx 卡片相同的 JSContext 中，无需复杂的 Shared VM 和双 Context 机制
- WebGL 和 2D API 通过 Napi Binding 暴露给 JS 层
- Krypton 将 OpenGL 指令批量处理到 CommandBuffer 中，在专用的 GPU 线程上执行，显著降低 JS 线程负载
- CanvasElement 对象的创建与 UI 节点 (CanvasView) 的创建流程解耦，提高了稳定性
- 新架构提供了显著的性能提升，尤其是在复杂渲染场景下
1.3 渲染后端
Lynx Canvas  (Canvas-NG) 基于 Krypton
- 在 Krypton 内部，最初有 Canvas 2D Full (Skia, 已不再维护) 和 Canvas 2D Lite (nanovg) 等后端
- RenderKit 是另一个专门用于页面级渲染的自渲染引擎，与组件级的 Canvas 不同
- 有一个提议是将 Skity（一个轻量级 2D 矢量渲染引擎）集成到 Krypton 中，以取代 nanovg 作为主要的 2D 后端
2. Lynx Canvas API 使用规范
2.1 Canvas 标签与视图
在 Lynx 中使用 Canvas，需要在 ttml 中添加 <canvas /> 标签：
<canvas style="width: 160px; height: 160px;" name="firstCanvas"></canvas>

注意事项：
- 通过 name 属性在 JS 代码中查找和使用 Canvas，name 属性不允许重复
- 如需为 Canvas 添加边框、圆角等样式，由于 Android 使用 TextureView 实现（不支持这些样式），需要在外部 View 中嵌套 Canvas 标签
- 如需切圆角，可在外部 View 上添加 clip-radius="true" 属性
- Canvas 不支持使用 ttif 或放置在包含 ttif 的容器中，可使用 visibility:hidden 替代
- LynxView 中应至少包含一个 Canvas 标签（即使不需要也可以隐藏），以触发帧刷新（requestAnimationFrame）时钟
2.2 创建 Canvas 对象


// 创建一个 Canvas Element
// 默认宽高为 300x150，需要调用 AttachToCanvasView 才能将绘制内容上屏
const canvasNG = lynx.createCanvasNG();

// 创建一个离屏 Canvas Element，默认宽高为 300x150，通常用于离屏渲染
const offscreenCanvas = lynx.createOffscreenCanvas(width, height);

注意事项：
- 如果对应的 View 尚未完成排版或未上屏，lynx.createCanvas 接口可能返回 null
- 可以在适当延迟或在 requestAnimationFrame 回调中重新尝试创建
- 一般在 LynxView 退出时会统一销毁 Canvas
- 如果在使用过程中创建了较多 Canvas 且不销毁，建议主动调用 Canvas 对象的 dispose() 方法进行销毁，以减少内存占用
2.3 主要 API 使用
Lynx Canvas 提供了一系列 lynx.xxx API 用于各种操作：
系统信息：
// 获取设备相关信息
const pixelRatio = SystemInfo.pixelRatio;  // 屏幕密度
const screenWidth = SystemInfo.pixelWidth;  // 屏幕像素宽度
const screenHeight = SystemInfo.pixelHeight;  // 屏幕像素高度

多媒体相关：
// 创建图片
const image = lynx.createImage("https://example.com/image.png");
// 修改图片源
image.src = "https://example.com/another-image.png";

// 创建视频（纯音频文件也可）
const video = lynx.createVideo("https://example.com/video.mp4");
// 监听首帧回调（1.5版本起）
video.addEventListener("candraw", () => {
  console.log("视频首帧已加载");
});

// 创建音频（使用音频处理模块）
const audio = lynx.createAudio("https://example.com/audio.wav");
// 修改音频源（使用 setLynxSrc 而非 src）
audio.setLynxSrc("https://example.com/another-audio.wav");
// 注意：安卓暂不支持 mp3 格式

// 创建音频上下文，支持更多音频处理
const audioContext = lynx.getAudioContext();

设备相关：
// 请求摄像头/麦克风
lynx.getUserMedia(
  { video: true, audio: false },
  (stream) => {
    // 成功回调
    const video = stream.getVideoTracks()[0];
    // 使用完毕后释放资源
    video.dispose();
  },
  (error) => {
    // 错误回调
    console.error("获取摄像头失败:", error);
  }
);

// 获取陀螺仪数据
const hardwareManager = lynx.getHardWareManager();
lynx.startGyroscope(100);  // 设置更新间隔为100ms
const gyroscopeData = lynx.getGyroscopeData();

文件相关：
// 异步加载文件
lynx.readFile(
  "path/to/file.json",
  (data) => {
    console.log("文件内容:", data);
  },
  { encoding: "utf-8" }  // 指定编码，返回字符串
);

// 同步加载文件
const fileData = lynx.readFileSync("path/to/file.bin");  // 默认返回 ArrayBuffer
const fileText = lynx.readFileSync("path/to/file.txt", { encoding: "utf-8" });  // 返回字符串

定时相关：
// 注册帧刷新
const requestID = lynx.requestAnimationFrame((timestamp) => {
  // 绘制动画帧
});
// 取消注册帧刷新
lynx.cancelAnimationFrame(requestID);

// 标准定时器
const timeoutID = lynx.setTimeout(() => {
  // 延时执行
}, 1000);
lynx.clearTimeout(timeoutID);

const intervalID = lynx.setInterval(() => {
  // 定时执行
}, 1000);
lynx.clearInterval(intervalID);

Effect 相关：
// 获取 effect 接口，用于访问 Effect 算法结果
const effect = lynx.getEffect();
// 创建人脸检测器
const faceDetector = effect.createFaceDetector();
// 创建手势检测器
const handDetector = effect.createHandDetector();
// 创建肢体识别器
const skeletonDetector = effect.createSkeletonDetector();

// 获取 Amazing 对象，访问 Amazing 绑定到 JS 环境中的所有 API
const amaz = lynx.getAmaz();

2.4 事件处理
Canvas 对象支持添加事件监听器：
canvas.addEventListener("touchstart", function(e) {
  // 处理触摸事件
  const x = e.touches[0].x;
  const y = e.touches[0].y;
  console.log(`触摸坐标: (${x}, ${y})`);
});

注意事项：
- 事件返回的坐标是基于 Canvas 坐标系的，原点在左上角
- 坐标宽高为 canvas.touch_width 和 canvas.touch_height，默认是未乘以缩放比的逻辑像素
- 如与外部 View 有手势冲突，可添加 block-native-event="{{true}}" 属性
2.5 生命周期与资源管理
在 LynxView 的生命周期回调中需要注意 Canvas 及相关资源的管理：
// 在 onLoad 中创建资源


// 在 onHide 中暂停资源
onHide() {
  if (this.video) {
    this.video.pause();
  }
}

// 在 onShow 中恢复资源
onShow() {
  if (this.video) {
    this.video.play();
  }
}

// 在 onDestroy 中释放资源
onDestroy() {
  if (this.canvas) {
    this.canvas.dispose();
    this.canvas = null;
  }
  
  if (this.video) {
    this.video.dispose();
    this.video = null;
  }
  
  // 标识卡片生命周期结束
  this.isDestroyed = true;
}

// 异步操作中检查生命周期
someAsyncFunction() {
  lynx.getUserMedia({ video: true }, (stream) => {
    // 检查组件是否已销毁
    if (this.isDestroyed) {
      stream.dispose();
      return;
    }
    
    // 正常处理流
    this.videoStream = stream;
  });
}

1. 代码结构和组织方式
3.1 高层架构
Lynx Canvas 的代码结构围绕分层架构组织：
1. JS API 层：提供与 W3C Canvas API 标准对齐的接口
2. 桥接机制：通过 Napi Binding 将 JS API 调用桥接到原生代码
3. 原生组件：如 UICanvas、TextureView/SurfaceView 等
4. 渲染引擎：基于 OpenGL ES 的底层渲染引擎（早期版本中的 nanovg/Skia，Canvas-NG 中的 Krypton）
5. 事件处理和动画回调机制：处理用户交互和动画帧更新
在新的 Canvas-NG 架构中：
- Krypton 和 Lynx 卡片运行在同一个 JSContext 中
- WebGL 和 2D API 使用 Napi Binding 绑定到 JS 层，绑定代码从 IDL 声明文件自动生成
- Krypton 将 OpenGL 指令批量处理到 CommandBuffer 中，在 GPU 线程上执行
- CanvasElement 对象的创建与 UI 节点 (CanvasView) 的创建解耦
3.2 渲染管线和实现
渲染管线的工作流程：
1. 开发者在 JS 环境中使用标签声明 Canvas 元素，并通过 createCanvas 等方法获取 Canvas 对象
2. JS 方法调用通过 LynxRuntime 桥接到原生端
3. 原生端找到与声明标签关联的 UICanvas，并创建原生 Canvas 对象（Drawable）
4. Drawable 持有 Surface，负责初始化 EGL 环境
5. 绘图操作转换为 OpenGL ES 命令执行
6. 事件通过 OnTouchListener 在原生 View 上捕获，并分发到 JS 环境处理
7. 动画回调通过原生端管理，帧事件分发到 JS 环境触发绘图操作
3.3 API 差异和注意事项
Lynx Canvas API 与标准 HTML Canvas API 存在一些差异：
CanvasElement：
- 需要使用 attachToCanvasView 和 detachFromCanvasView 进行显式绑定和解绑
- clientWidth 和 clientHeight 初始值为 0
- WebGL 的 getContext 需要 antialias 和 enableMSAA 都为 true 才能启用 MSAA
- getBoundingClientRect 在布局完成前返回 null
- toDataURL 的 encoderOptions 只支持 0-1 范围
- resize 事件直接反映 canvas 节点 surface 宽度和高度的变化
WebGLContextAttributes：
- 仅支持 antialias 和 enableMSAA 用于 MSAA
- getContextAttributes() 当前不返回正确的值
ImageElement：
- src 支持 base64 和 gecko 缓存策略
VideoElement：
- 具有用于视频流的 srcObject
- 用于定位的 currentTime
- 用于渲染到 canvas 的 paintTo
- 用于相机流时间戳的 getTimestamp
1. 性能优化建议
4.1 资源管理优化
1. 主动释放资源
  - 主动调用 dispose() 方法销毁不再使用的 Canvas 对象
  - 在 onDestroy 生命周期中释放所有资源
  - 对于摄像头/麦克风等资源，使用完毕后立即释放
2. 生命周期管理
  - 在 onHide 中暂停音视频播放，减少后台资源消耗
  - 在 onShow 中恢复音视频播放
  - 对于异步操作，检查组件是否已销毁，避免在销毁后执行操作
4.2 渲染性能优化
1. 使用 Canvas-NG
  - 在 Lynx 2.10 版本后，优先使用 lynx.createCanvasNG 创建 Canvas
  - Canvas-NG 将 OpenGL 指令在 GPU 线程执行，减轻 JS 线程负载
2. 离屏渲染
  - 对于复杂静态内容，使用离屏 Canvas 预渲染后复制到主 Canvas
const offscreenCanvas = lynx.createOffscreenCanvas(width, height);
const offscreenCtx = offscreenCanvas.getContext('2d');
// 在离屏 Canvas 上绘制复杂内容
offscreenCtx.drawComplexContent();

// 在主 Canvas 上绘制离屏 Canvas 内容
const mainCtx = mainCanvas.getContext('2d');
mainCtx.drawImage(offscreenCanvas, 0, 0);

3. 批量处理绘图操作
  - 减少状态切换，批量执行相似的绘图操作
// 不推荐：频繁切换状态
ctx.fillStyle = 'red';
ctx.fillRect(10, 10, 20, 20);
ctx.fillStyle = 'blue';
ctx.fillRect(40, 10, 20, 20);
ctx.fillStyle = 'red';
ctx.fillRect(70, 10, 20, 20);

// 推荐：批量处理相同状态的操作
ctx.fillStyle = 'red';
ctx.fillRect(10, 10, 20, 20);
ctx.fillRect(70, 10, 20, 20);
ctx.fillStyle = 'blue';
ctx.fillRect(40, 10, 20, 20);

5. 常见错误和避免方法
5.1 创建和初始化问题
1. 页面加载 canvas 报错
  - 原因：需要客户端接入 lynx 的 canvas 扩展模块，并对特定的 lynxview 打开开关
  - 解决方案：在 schema 中添加参数 &enable_canvas=1
2. lynx.createCanvas 失败
  - 原因：排版未完成或 View 未上屏
  - 解决方案：
    - 添加适当延迟
    - 在 requestAnimationFrame 回调中重试
    - 在异步接口（如 getNodeRef）的回调中重试调用
3. 多个 canvas 下，部分实例交互卡死、不出图或不同 app 数据不一致
  - 原因：创建第二个同名 canvas 会导致第二个变成离屏 canvas
  - 解决方案：canvas 使用随机生成的 id，canvas-ng 已解决此问题
5.2 渲染和显示问题
1. canvas 息屏后再亮屏图表不展示 (Android)
  - 原因：部分安卓设备兼容性问题，息屏后 TextureView context 会被清掉
  - 解决方案：
    - Lynx Canvas 新版本已修复此问题
    - 旧版本前端可在 onShow 时先将 canvas visibility 设为 false 再设为 true 规避
2. canvas 曲线图粗细不一致
  - 原因：图例 hover 时会加粗曲线，触摸经过时会触发 hover 事件
  - 解决方案：修改 hover.lineStyle
3. Touch 事件与外部 view 有手势冲突
  - 解决方案：添加 block-native-event="{{true}}" 属性
5.3 内存和资源问题
1. Canvas 内存占用问题
  - 原因：使用过程中创建较多 Canvas 不销毁
  - 解决方案：主动调用 Canvas 对象的 dispose() 销毁以减少内存占用
2. getUserMedia / requestCamera 调用前，人脸、手势等识别功能有问题
  - 解决方案：在调用 lynx.getUserMedia 或 lynx.requestCamera 前，用 canvas 随便画点什么
5.4 版本和兼容性问题
1. Canvas 不支持某些机型
  - 原因：需要支持 openGLES 3+
  - 解决方案：检查 lynx.helium() 返回值，如果返回空建议做降级处理
6. 最佳实践总结
1. 创建和使用 Canvas
  - 使用随机或唯一的 name 属性
  - 在 Lynx 2.10 版本后优先使用 lynx.createCanvasNG
  - 确保在视图完成排版后再创建 Canvas 对象
2. 资源管理
  - 遵循完整的生命周期管理，在 onDestroy 中释放所有资源
  - 主动调用 dispose() 方法销毁不再使用的对象
  - 对于异步操作，检查组件是否已销毁
3. 性能优化
  - 使用批量绘制减少状态切换
  - 复杂静态内容使用离屏 Canvas 预渲染
  - 利用 Canvas-NG 的 GPU 线程渲染能力
4. 错误处理
  - 实现降级方案，检查 lynx.helium() 返回值
  - 对 Canvas 创建失败进行重试处理
  - 在异步操作中添加适当的错误处理
5. 版本适配
  - 了解不同版本的 API 差异和限制
  - 根据 Lynx 版本选择合适的 API 和处理方式
  - 关注官方更新和修复的问题




1. Lynx 使用双线程模型，Canvas 元素和视图是分离的
Lynx Canvas API 与 HTML 标准存在一些差异：
CanvasElement：需要使用 attachToCanvasView 和 detachFromCanvasView 显式绑定与解绑 CanvasView
WebGLContextAttributes：antialias 和 enableMSAA 需同时为 true 才启用 MSAA
不支持的特性：不要使用 roundrect、23 年以后的 canvas 新方法、globalCompositeOperation、不规则 shadow 等
其他限制：不要使用 createSelectorQuery，在 setupCanvas 时，应使用 lynx.createCanvasNG() 并监听 resize 事件来设置 canvas 的宽高和获取 context

技术限制：
1. 不要使用 globalCompositeOperation
2. 不要使用 23 年以后的 Canvas 新方法
3. 不要使用 roundrect
4. 优先使用兼容性高的旧方法
Canvas 元素创建：
使用 lynx.krypton.createCanvasNG() 创建 Canvas 元素
不要使用已弃用的 lynx.createCanvas() 方法
事件处理：
resize 事件监听器必须在 attachToCanvasView 之前设置
触摸事件应使用 touchstart、touchmove 和 touchend
尺寸处理：
Canvas 视图的尺寸通过 style 属性设置
Canvas 元素的尺寸需要手动设置，并考虑设备像素比
使用 createSelectorQuery().select('#canvas-id').invoke({method: 'boundingClientRect', ...}) 获取实际尺寸
性能优化：
避免在每一帧都重新绘制整个画布
使用离屏 Canvas 进行复杂绘制


示例：Page({
  data: {
    canvas: null,
    context: null,
    isDrawing: false,
    lastX: 0,
    lastY: 0
  },
  
  onLoad() {
    this.setupCanvas();
  },
  
  setupCanvas() {
    // 创建 Canvas 元素
    const canvas = lynx.krypton.createCanvasNG();
    
    // 监听 resize 事件（必须在 attachToCanvasView 之前）
    canvas.addEventListener('resize', ({ width, height }) => {
      canvas.width = width;
      canvas.height = height;
      
      // 重新设置绘图上下文
      const context = canvas.getContext('2d');
      context.lineWidth = 2;
      context.strokeStyle = '#3498db';
      context.lineJoin = 'round';
      context.lineCap = 'round';
      
      this.setData({
        context: context
      });
    });
    
    // 获取 Canvas 视图的实际尺寸
    this.createSelectorQuery()
      .select('#drawing-canvas')
      .invoke({
        method: 'boundingClientRect',
        success: (res) => {
          // 获取设备像素比
          const pixelRatio = SystemInfo.pixelRatio || 1;
          
          // 设置 Canvas 元素的像素尺寸
          canvas.width = res.width * pixelRatio;
          canvas.height = res.height * pixelRatio;
          
          // 绑定到 Canvas 视图
          canvas.attachToCanvasView('drawing-canvas');
          
          // 获取绘图上下文
          const context = canvas.getContext('2d');
          context.lineWidth = 2;
          context.strokeStyle = '#3498db';
          context.lineJoin = 'round';
          context.lineCap = 'round';
          
          this.setData({
            canvas: canvas,
            context: context
          });
        }
      })
      .exec();
  },
  
  // 触摸开始事件
  touchStart(e) {
    const touch = e.touches[0];
    this.setData({
      isDrawing: true,
      lastX: touch.x,
      lastY: touch.y
    });
  },
  
  // 触摸移动事件
  touchMove(e) {
    if (!this.data.isDrawing) return;
    
    const touch = e.touches[0];
    const ctx = this.data.context;
    
    ctx.beginPath();
    ctx.moveTo(this.data.lastX, this.data.lastY);
    ctx.lineTo(touch.x, touch.y);
    ctx.stroke();
    
    this.setData({
      lastX: touch.x,
      lastY: touch.y
    });
  },
  
  // 触摸结束事件
  touchEnd() {
    this.setData({
      isDrawing: false
    });
  },
  
  // 清除画布
  clearCanvas() {
    if (this.data.canvas && this.data.context) {
      const ctx = this.data.context;
      ctx.clearRect(0, 0, this.data.canvas.width, this.data.canvas.height);
    }
  },
  
  // 页面卸载时解绑 Canvas
  onUnload() {
    if (this.data.canvas) {
      this.data.canvas.detachFromCanvasView();
    }
  }
});


Lynx Canvas 开发要点
正确创建和绑定 Canvas 元素：使用 lynx.krypton.createCanvasNG() 创建 Canvas 元素，并使用 attachToCanvasView() 绑定到视图
处理尺寸设置：手动设置 Canvas 元素的 width 和 height，并监听 resize 事件以处理尺寸变化
考虑设备像素比：乘以 SystemInfo.pixelRatio 确保在高分辨率设备上显示清晰
避免使用不支持的特性：不要使用 roundrect、23 年以后的 canvas 新方法、globalCompositeOperation 和不规则 shadow 等
正确处理生命周期：在页面卸载时解绑 Canvas 元素，避免内存泄漏
性能优化：使用离屏 Canvas 进行复杂绘制，避免在每一帧都重新绘制整个画布

---
title: SystemInfo
import { Tab, Tabs } from 'rspress/theme';
SystemInfo
SystemInfo 是一个普通的 JavaScript 对象，其中含有当前系统和设备的相关信息。
语法
declare const SystemInfo: {
  /**
   * The version of the Lynx SDK.
   * @example '2.4', '2.10'
   */
  readonly lynxSdkVersion: string;

  /**
   * The current operating system version.
   */
  readonly osVersion: string;

  /**
   * The physical pixel height of the real device.
   */
  readonly pixelHeight: number;

  /**
   * The physical pixel width of the real device.
   */
  readonly pixelWidth: number;

  /**
   * The physical pixel ratio of the real device.
   */
  readonly pixelRatio: number;

  /**
   * The platform of the current device.
   */
  readonly platform: 'Android' | 'iOS' | 'macOS' | 'pc' | 'headless';

  /**
   * The JavaScript engine currently used.
   * @note Not available in lepus
   */
  readonly runtimeType: 'v8' | 'jsc' | 'quickjs';
};
属性
lynxSdkVersion
当前 Lynx 版本号，格式为 major.minor。
例如：2.4, 2.10。
osVersion
当前操作系统版本。
> [systemVersion | Apple Developer Documentation](https://developer.apple.com/documentation/uikit/uidevice/1620043-systemversion?language=objc)
```objc
[[UIDevice currentDevice].systemVersion UTF8String]
```
等于 [`Build.VERSION.SDK_INT`](https://developer.android.com/reference/android/os/Build.VERSION#SDK_INT)。
> [API Levels | Android NDK | Android Developers](https://developer.android.com/ndk/reference/group/apilevels#android_get_device_api_level)

```cpp
std::to_string(android_get_device_api_level())
```
pixelHeight
当前设备屏幕的绝对高度，以物理像素为单位。
> [UIScreen | Apple Developer Documentation](https://developer.apple.com/documentation/uikit/uiscreen?language=objc)
```objc
CGSize screenSize;
if (!CGSizeEqualToSize(builder.screenSize, CGSizeZero)) {
screenSize = builder.screenSize;
} else {
screenSize = [UIScreen mainScreen].bounds.size;
}
const CGFloat scale = [UIScreen mainScreen].scale;
// highlight-next-line
const CGFloat pixelHeight = screenSize.height * scale;
```
> [DisplayMetrics | Android Developers](https://developer.android.com/reference/android/util/DisplayMetrics)
```java
DisplayMetrics dm = context.getResources().getDisplayMetrics();
// highlight-next-line
int pixelHeight = dm.heightPixels;
```
pixelWidth
当前设备屏幕的绝对宽度，以物理像素为单位。
> [UIScreen | Apple Developer Documentation](https://developer.apple.com/documentation/uikit/uiscreen?language=objc)
```objc
CGSize screenSize;
if (!CGSizeEqualToSize(builder.screenSize, CGSizeZero)) {
screenSize = builder.screenSize;
} else {
screenSize = [UIScreen mainScreen].bounds.size;
}
const CGFloat scale = [UIScreen mainScreen].scale;
// highlight-next-line
const CGFloat pixelWidth = screenSize.width * scale;
```
> [DisplayMetrics | Android Developers](https://developer.android.com/reference/android/util/DisplayMetrics)
```java
DisplayMetrics dm = context.getResources().getDisplayMetrics();
// highlight-next-line
int pixelWidth = dm.widthPixels;
```
pixelRatio
当前设备的像素比例。
> [UIScreen | Apple Developer Documentation](https://developer.apple.com/documentation/uikit/uiscreen?language=objc)
```objc
const CGFloat pixelRatio = [UIScreen mainScreen].scale;
```
> [DisplayMetrics | Android Developers](https://developer.android.com/reference/android/util/DisplayMetrics#density)
```java
DisplayMetrics dm = context.getResources().getDisplayMetrics();
// highlight-next-line
float pixelRatio = dm.density;
```
platform
当前设备对应的平台。
可能的值：
1. iOS
2. Android
3. macOS
4. pc (对应 Windows 系统)
5. headless (对应 @byted-lynx/testing)
runtimeType
目前使用的 JavaScript 引擎。
:::info
该值只能在 JS 线程获得，在 lepus 线程为 undefined。
:::
可能的值：
1. quickjs
2. jsc
3. v8
更对细节内容见 JavaScript 环境。
兼容性
import { LegacyCompatTable } from '@lynx';









https://bytedance.larkoffice.com/docx/FQ8Fden17oMHZLx6bf2c8dmjnJg



Lynx Canvas 技术细节与最佳实践报告
根据提供的搜索信息，以下是关于 Lynx Canvas 在屏幕物理像素处理、屏幕物理宽度和高度处理、文字绘制规则与最佳实践、图形绘制规则与最佳实践以及最佳设计规范方面的综合报告。
1. Lynx Canvas 如何处理屏幕的物理宽度和高度
LynxView 作为 Lynx 内容（包括 Canvas）的容器，其屏幕宽度和高度默认情况下会设置为设备的真实物理屏幕尺寸。这个物理尺寸是计算 rpx 单位的基础。rpx 单位的转换规则通常是 1rpx = 屏幕宽度 / 750 或 1rpx = 屏幕宽度 / 375，这取决于项目的设计稿标准。
开发者可以通过客户端和前端接口来自定义 LynxView 使用的屏幕宽度和高度，从而覆盖默认的物理尺寸。前端提供了 getScreenMetricsOverride 接口，它接收当前屏幕的物理像素宽度和高度，允许在 Lynx 使用这些值之前进行修改。
对于 Canvas 组件本身：
- 通过 lynx.createCanvas 创建 Canvas 时，可以传入可选的 width 和 height 参数（单位为像素）。
- 如果未指定这些参数，Canvas 将默认使用其父级 Canvas View 的像素宽度和高度，这些值会乘以 pixelRatio。这些值对应于 canvas.width 和 canvas.height 属性。
- SystemInfo 对象提供了 screenWidth 和 screenHeight 属性，它们代表屏幕的像素宽度和高度，这两个值同样乘以了屏幕密度 (pixelRatio)。
需要注意的是，Canvas 的触摸事件处理基于 canvas.touch_width 和 canvas.touch_height，这两个值源于 Canvas View 的宽度和高度，不受创建 Canvas 时设置的像素宽度和高度的影响。
2. Lynx Canvas 如何处理屏幕的物理像素（例如，设备像素比 DPR）
关于 Lynx Canvas 如何具体处理设备像素比 (DPR) 的详细信息，在提供的搜索结果中并未明确提及。
然而，从屏幕宽度和高度的处理方式中可以看出，pixelRatio (可以理解为 DPR) 在计算 Canvas 最终像素尺寸时扮演了角色。例如，SystemInfo 提供的 screenWidth 和 screenHeight 是乘以 pixelRatio 后的值，并且 Canvas 在未指定宽高时，其默认宽高也会乘以 pixelRatio。这表明 Lynx Canvas 会考虑设备的像素密度，以确保在不同DPR的屏幕上获得清晰的渲染效果。但具体的内部转换机制和开发者可控的DPR相关API细节，在当前信息中尚不明确。
3. Lynx Canvas 中文字绘制的规则和最佳实践
Lynx Canvas 中的文字绘制，特别是在礼物特效和动画引擎的动态文本场景中，通常采用以下机制：将文字首先绘制到一个 Canvas 上，然后将这个 Canvas 作为纹理贴图应用到动画元素的网格 (mesh) 上。这是通过 CanvasTexture 实现的。
支持的文字属性：
在 Canvas 上绘制文字时，支持多种属性配置，包括：
- 方向 (direction): 如横向、竖向。
- 大小 (font_size/size): 文字的像素大小。
- 颜色 (color): 文字颜色。
- 行数 (lines/line count): 支持多行显示，但自动换行功能需要开发者自行实现。
- 样式 (font_style): 如加粗、普通、斜体等。
- 描边 (border): 包括描边颜色和宽度。
- 对齐方式 (text_align/alignment): 支持左对齐 (left)、右对齐 (right)、居中对齐 (center)。justify 对齐需要自行实现。Canvas 原生的 ctx.textAlign 属性支持 left, right, center。
- 字体 (font package/font): 需要加载字体包以使用自定义字体。
- 字重 (font weight): 文字的粗细。
- 字距 (letter spacing): 字符间距。
- 边距 (margin): 文字的边距。
- 行距 (line height): 行高。
- 截断方式 (truncation): 文字溢出时的处理方式。
- 位置 (position): 文字的绘制位置。
- 变量 (variables): 支持在文本内容中使用变量。
- 默认值 (default value): 支持为文本属性设置默认值。
绘制流程 (以动态文本为例)：
1. 配置动态文本: 设置上述支持的各项文字属性。
2. 测量: 计算每个字符在当前样式下的宽度，为后续的布局和绘制做准备。
3. 自适应: 如果多个文本元素在同一行且具有相同的对齐方式和基线，它们可能被视为一组进行自适应处理。
4. 绘制计划: 根据对齐方式、截断方式等，计算每个字符或文本块在 Canvas 上的具体行数和绘制位置 (例如 left 坐标)。
5. 绘制: 调用 Canvas 的绘图 API (如 fillText) 将文字按照计划绘制到图层上。
挑战与注意事项：
- 复杂排版实现: 自动换行、两端对齐 (justify) 等复杂的文本排版功能，Canvas 原生 API 支持有限，通常需要开发者自行实现逻辑。
- 行内混合样式: 在同一行内显示多种不同样式（如不同字体、大小、颜色）的文本，需要精确计算每个文本片段的位置和大小。
- 字体加载: 使用自定义字体前，必须确保字体文件已成功加载。可以通过 lynx.krypton.readFile 读取字体文件，然后使用 lynx.krypton.loadFont 同步加载。
性能：
虽然未直接提供针对文字绘制的详细性能优化指南，但提及了以下几点可能与性能相关：
- Lynx Canvas 启用了新的矢量渲染引擎 Skity。
- 在动画引擎中，通过 CanvasTexture 实现动态文本并支持参与合批 (batching)，这有助于减少绘制调用，提升渲染效率。
4. Lynx Canvas 中图形绘制的规则和最佳实践
Lynx Canvas 支持标准的 Canvas 2D 和 WebGL 1.0 接口，并提供部分 WebGL 2.0 扩展能力。其底层基于自研的跨平台互动引擎 Krypton (新架构 Canvas-NG)。
绘制能力与限制：
- 基本图形与样式: 支持 Canvas 2D API 提供的标准图形绘制（如矩形、路径、圆形等）和样式设置（颜色、线宽、填充等）。
- 阴影效果: 在 Lynx 环境下，阴影效果仅支持文字和无圆角的矩形。不支持不规则图形的阴影。
- globalCompositeOperation: 不支持使用此属性来改变图形的混合模式。
- roundrect: 不支持 roundrect 方法绘制圆角矩形，需要通过其他方式（如 arcTo 或路径组合）实现。
- API 兼容性: 建议优先使用兼容性较好的、成熟的 Canvas API，避免使用过于新的或实验性的方法（特别是 2023 年以后新增的 Canvas API）。
- 内置图表库: @byted/lynx-lightcharts 库提供了多种常用的图表类型（如折线图、饼图、柱状图等）和组件，大部分可在 Lynx 中使用。部分图表（如地图、K线图）在特定版本后内置，其他可能需要按需构建。
抗锯齿 (Anti-aliasing)：
- 对于 WebGL Context，要启用 MSAA (多重采样抗锯齿)，需要同时将 WebGLContextAttributes 中的 antialias 和 enableMSAA 属性设置为 true。
性能优化与实践：
- 架构优势: 新的 Canvas-NG 架构 (基于 Krypton) 通过将 OpenGL 指令进行 CommandBuffer Batching 并在 GPU 线程执行，减轻了 JS 线程的压力，从而在复杂渲染场景下有显著的性能提升。
- 面积图卡顿优化: 对于数据量大且交互频繁的面积图，像素拾取可能导致性能问题。可以通过设置图表系列 (series) 中 areaStyle 和 lineStyle 的 pointerEvents: 'none' 来禁用不必要的事件检测，以优化性能。
- 应用切后台白屏问题: 应用从后台切换回前台时，Canvas 内容（如图表）可能出现白屏。可以通过监听 lynx.getJSModule('GlobalEventEmitter').addListener('onShow', () => chart.refresh()); 事件，在 onShow 回调中调用图表的刷新方法 (如 chart.refresh()) 来解决。
- console.log 陷阱: 在 Lynx 环境下，如果使用 console.log 打印包含循环引用的对象，可能会导致应用卡死。
- Canvas 尺寸检查: 确保 Canvas 元素的实际宽高不为 0，否则可能导致渲染异常或报错（如扫码功能）。
Lynx Canvas API 特性与差异 (与标准 Web Canvas 相比)：
- CanvasElement 绑定: 需要使用 attachToCanvasView 和 detachFromCanvasView 方法显式地将 CanvasElement 实例与页面中的 <canvas> 视图进行绑定和解绑。
- clientWidth/clientHeight: CanvasElement 的这两个属性初始值为 0。
- getContext: 获取 WebGL 上下文时，WebGLContextAttributes 中的 antialias 和 enableMSAA 需同时为 true 才能启用 MSAA。
- getBoundingClientRect: 仅在 Canvas 视图完成排版后才能返回有效的 DOMRect 对象，否则返回 null。
- toDataURL: encoderOptions 参数（用于控制图片质量）仅支持 0 到 1 的区间。
- resize 事件: CanvasElement 上的 resize 事件直接反映其绑定的 Canvas 视图（surface）宽高的变化。
- WebGLContextAttributes: 除了 antialias 和 enableMSAA (用于 MSAA)，其他标准 WebGLContextAttributes 属性可能不被支持。getContextAttributes() 方法可能不会返回预期的正确值。
- ImageElement: src 属性支持 base64 编码的图像数据和 gecko 缓存策略。
- VideoElement: 提供了 srcObject (处理视频流)、currentTime (支持跳转播放)、paintTo (将视频帧渲染到 Canvas)、getTimestamp (获取相机流时间戳) 等特定属性和方法。Lynx 提供了独立的 AudioElement 和 VideoElement 对象，支持流式渲染和硬件加速。
- 移除的属性: 旧版本中存在的 touch_width 和 touch_height 属性已被移除。
使用注意事项：
- Canvas name 属性: <canvas> 标签的 name 属性必须在页面中唯一，用于 CanvasElement 与 Canvas 视图的绑定。
- Formatter 函数: 由于 Lynx 环境对 Function 构造函数的使用限制，图表库中的 formatter 不能使用 lodash 模板字符串语法 (如 <%=name%>)，应使用函数形式的 formatter。
- Tooltip 配置: 在使用图表库时，tooltip.useHTML 属性应设置为 false。自定义 tooltip 内容可以使用图表库提供的富文本功能。
- TTML/ReactLynx 集成: 在 TTML 或 ReactLynx 项目中使用如 Lightcharts 这样的 Canvas 库，通常需要通过 npm 安装相应的包 (如 @byted/lynx-lightcharts)。在测试或预览时，确保 schema 参数中包含了启用 Canvas 的参数，如 enable_canvas=1 和 enable_canvas_optimize=1。
5. Lynx Canvas 的最佳设计规范示例或设计指南
Lynx Canvas 基于自研的跨平台互动引擎 Krypton，提供了标准的 2D 和 WebGL 图形接口及多媒体能力。它并非 Lynx 的默认内置模块，需要与 Krypton 模块集成才能使用。
由于 Lynx 的架构与 Web 环境存在差异（例如，Lynx 采用双线程模型，JS 执行与 <canvas> 标签解析渲染在不同线程；缺乏完整的 DOM 和 BOM 对象），一些 Web 端的 Canvas 使用习惯可能不完全适用。
核心技术指南与注意事项：
1. 启用 Canvas 环境:
  - Canvas 是 Lynx 中的可选特性，默认不开启，以避免不必要的开销（内存占用、首屏加载时间）。
  - 使用 Canvas 前，必须由容器（客户端）显式启用。这通常通过 schema URL 中的参数控制，例如 enable_canvas=1 和 enable_canvas_optimize=1。
  - enable_canvas_optimize=1 参数用于指定使用 Krypton 作为 Canvas 实现。尽管旧的 Helium 实现已不再维护，且 Lynx 2.9 版本后仅存在 Krypton，但为兼容旧版本，仍建议携带此参数。
2. Canvas View 与 Canvas Element 的关系:
  - 在 TTML 或 JSX 中使用的 <canvas /> 标签，在 Lynx 中会被解析并生成一个平台级的视图 (View)，称为 Canvas View。
  - 与 Web 环境中的 HTMLCanvasElement 对应的对象，在 Lynx 中称为 Canvas Element。
  - 与 Web 中 Canvas 元素和其渲染上下文紧密耦合不同，Lynx 中的 Canvas Element 和 Canvas View 位于不同线程，是相对独立的、松耦合的关系。它们通常通过 <canvas /> 标签上的 name 属性进行关联。
3. 创建 Canvas Element 实例:Lynx 提供了多种创建 Canvas Element 实例的接口：
  - lynx.krypton.createCanvasNG(): (Lynx 2.10+ 版本可用) 此方法返回一个初始未绑定到任何 Canvas View 的 Canvas Element 实例。需要后续调用 attachToCanvasView(name) 方法将其与指定 name 的 Canvas View 绑定。创建的实例不会自动获取 Canvas View 的尺寸，必须手动设置其 width 和 height 属性。此方法支持 Canvas Element 的复用，推荐在 Canvas Element 可能先于 Canvas View 创建，或创建时不知道目标 Canvas View name 的场景下使用。
  - 构造函数 (new lynx.krypton.CanvasElement(name)): (Lynx 2.6+ 版本可用) 创建时需要传入目标 <canvas /> 标签的 name 属性。同样需要手动设置 width 和 height。
  - lynx.createCanvas(name): (Lynx 2.6+ 版本可用，但不推荐使用) 此方法虽然便捷，但容易引发问题。它可能返回一个 Canvas Element 实例，也可能返回 null。如果成功返回实例，其 width 和 height 属性已自动设置为匹配 Canvas View 的尺寸，无需手动设置。
4. 正确设置宽度和高度:
  - 对于通过 createCanvasNG 或构造函数创建的 Canvas Element，必须手动设置其 width 和 height 属性（单位为像素）。
  - 由于 Lynx 的 <canvas /> 标签不支持直接设置 width/height 属性（只能通过 style 设置其布局尺寸），且 Canvas Element 无法直接获取 Canvas View 的渲染尺寸，因此通常需要查询 Canvas View 的实际尺寸。
  - 可以使用 Lynx 的 SelectorQuery API，配合 <canvas /> 标签的 id，调用 boundingClientRect() 方法获取其布局尺寸。该方法返回的尺寸单位是 rpx (响应式像素)，需要乘以 SystemInfo.pixelRatio (即 DPR) 才能得到用于 Canvas Element width 和 height 的实际像素值。
5. 监听尺寸变化:
  - 如果 Canvas View 的尺寸发生变化（例如，由于布局调整或屏幕旋转），对应的 Canvas Element 的 width 和 height 必须相应更新，否则会导致绘制内容拉伸或裁剪。
  - Lynx Canvas Element 提供了 resize 事件。开发者应监听此事件，并在事件回调中更新 Canvas Element 的 width 和 height，然后重新绘制 Canvas 内容。
6. 关于 name 属性和多实例的注意事项:
  - 一个 <canvas /> 标签的 name 属性只能设置一次。
  - 通过构造函数或 lynx.createCanvas 创建的 Canvas Element 实例的 name 属性是只读的。
  - createCanvasNG 创建的实例可以通过 detachFromCanvasView() 和 attachToCanvasView(newName) 来解绑并重新绑定到不同的 Canvas View。
  - 如果多个 Canvas Element 实例共享同一个 name，则最终渲染到对应 Canvas View 的内容将来自最后创建的那个实例。
  - 如果多个 <canvas /> 标签共享同一个 name，则一个具有该 name 的 Canvas Element 实例会将其内容渲染到所有对应的 Canvas View 上。
7. Canvas 图表性能最佳实践:
  - 推荐使用官方或社区优化的图表库，如 Lynx LightCharts。
  - 按需引入和优化图表库的依赖，减少不必要的代码体积和计算。
  - 对于复杂的 Canvas 性能问题，可以咨询相关的技术支持或社区。
总结来说，使用 Lynx Canvas 进行设计和开发时，需要深入理解其特有的双线程架构、Canvas View 与 Canvas Element 的分离与松耦合关系，并掌握正确的 Canvas Element 创建、尺寸设置与更新、以及事件处理机制。针对如图表这样的具体应用场景，选择合适的库并遵循性能最佳实践至关重要。