https://bytedance.larkoffice.com/file/Du95bqVdzonywWxeV41cbYzunhf




1. Lynx如何使用复制粘贴功能
Lynx框架通过内置的<copy>组件实现复制功能，使用非常简单：

基本使用步骤
在Lynx页面中引入<copy>组件
设置要复制的文本内容（通过text属性）
添加复制成功的回调处理（可选）
自定义复制按钮或触发元素（作为<copy>的子元素）
代码示例
<template>
  <view>
    <copy 
      :text="textToCopy" 
      @success="handleCopySuccess" 
      @error="handleCopyError"
      toastText="复制成功！"
    >
      <button>点击复制</button>
    </copy>
    <text>要复制的内容: {{ textToCopy }}</text>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        textToCopy: "这是将被复制到剪贴板的文本内容"
      };
    },
    methods: {
      handleCopySuccess() {
        console.log("文本已成功复制到剪贴板");
        // 可以在这里添加额外的成功处理逻辑
      },
      handleCopyError(event) {
        console.error("复制失败:", event.detail.errMsg);
        // 处理错误情况
      }
    }
  };
</script>
API参数说明
text: (必需) 字符串，指定要复制的文本内容
toastText: (可选) 字符串，复制成功时显示的提示文字，默认为"已复制到粘贴板"
@success: (可选) 事件处理函数，复制成功时触发
@error: (可选) 事件处理函数，复制失败时触发，会传递错误信息
动态获取文本内容
如果需要动态获取页面元素的文本内容进行复制，可以结合Lynx提供的文本处理API：

getText(): 获取元素内文本并清除前后空格
getValue(): 获取表单元素的value属性
2. Lynx可调用的抖音APP标准bridge
根据现有资料，Lynx与抖音APP的bridge接口没有完整的公开列表。以下是已知的部分bridge接口信息：

通用机制
Lynx通过JSBridge (NativeModule)机制与原生应用（Android/iOS抖音APP）进行通信。开发者可以通过device.app.callMethod方法调用Native接口。

部分已知的bridge接口
Android平台
Pitaya Lynx SDK提供的API：

runTask: 运行指定算法包
registerAppLogRunEventCallback: 监听Applog触发的算法包运行回调
removeAppLogRunEventCallback: 解除注册回调
registerMessageHandler: 监听算法包内发送的信息
removeMessageHandler: 解除注册消息监听
registerMessageHandler2: 支持多次触发回调的消息监听
registerAppLogRunEventCallback2: 支持多次触发回调的事件监听
应用接口示例：

openLynxPage: 通过schema打开Lynx页面
closePage: 关闭当前页面
login: 登录
logout: 登出
setEnvToProd: 切换到线上环境
setPPEChannel: 切换到PPE环境
setBOEChannel: 切换到BOE环境
getCurrentEnv: 获取当前App环境
clearGeckoX: 清除GeckoX资源
iOS平台
Pitaya Lynx SDK提供的API（与Android类似）：

runTask
registerAppLogRunEventCallback
removeAppLogRunEventCallback
registerMessageHandler
removeMessageHandler
入参和出参
由于没有完整的API文档，无法提供所有bridge接口的详细入参和出参。一般情况下：

入参：可以是基本数据类型、JSON对象或数组
出参：通常通过Promise或回调函数返回，可能是基本数据类型、JSON对象或数组
获取完整bridge列表的途径
要获取完整的bridge接口列表及其入参和出参，建议：

查阅抖音内部的"抖音Lynx Bridge API"文档
参考"抖音Lynx Schema & globalProps参数约定"文档
查看Pitaya Lynx SDK的完整API文档
咨询抖音内部的Lynx开发团队
通用参数访问
Lynx页面可以通过this.data.__globalProps访问通用参数，包括：

设备型号(device_model)
设备ID(deviceId)
应用ID(aid)
App版本(appVersion)
系统版本(os_version)
屏幕尺寸信息
状态栏高度
是否刘海屏(is_iphone_x_series)
是否Pad设备(is_pad_device) 等