[使用说明](https://bytedance.larkoffice.com/docx/WonYdhTMeoIb9dxmR27cnBENnx7)
本文档提供了一套针对Claude 3.7优化的Lynx卡片开发规范提示文本。当您需要让Claude 3.7生成符合Lynx规范的卡片代码时，只需将本文档中"提示文本"部分的内容复制，作为指令提供给Claude 3.7，即可获得符合规范且可成功编译和渲染的Lynx卡片代码。
使用步骤：
1. 复制下方"提示文本"部分的全部内容
2. 将其作为指令提供给Claude 3.7，同时附上您对Lynx卡片的具体需求
3. Claude 3.7将基于这些规范生成符合要求的Lynx卡片代码

---
提示文本
以下是Lynx卡片开发的规范提示文本，请在生成Lynx卡片时严格遵循这些规则：
框架概述
Lynx是一款高性能跨端解决方案，利用Web技术栈快速构建Native视图。核心理念是"数据驱动渲染"，优势在于"首屏直出"和高性能视图更新。Lynx卡片由TTML、TTSS、JS和JSON四部分组成。
1. TTML结构和语法规则
1.1 基本结构（必须遵循）
- 根节点必须是<view>。
- 元素必须正确闭合（<image />或<view></view>）。
- 标签名必须全小写。
<view>
  <text>{{message}}</text>
</view>

1.2 数据绑定（必须遵循）
- 使用{{expression}}进行数据绑定。
- 支持简单表达式计算（如{{a + b}}）。
<view>
  <text>{{user.name}}</text>
  <text>{{isActive ? '活跃' : '非活跃'}}</text>
</view>

1.3 条件渲染（必须遵循）
- 使用tt:if、tt:elif和tt:else控制元素渲染。
- 条件表达式必须是布尔值。
<view tt:if="{{condition}}">条件为真时显示</view>

1.4 列表渲染（必须遵循）
- 使用tt:for进行列表渲染。
- 建议使用tt:key指定唯一标识符。
<view tt:for="{{items}}" tt:key="id">
  {{item.name}}
</view>

1.5 事件绑定（必须遵循）
- 使用bind绑定冒泡事件，catch:绑定非冒泡事件。
- 事件处理函数必须在JS中定义。
<view bindtap="handleTap">点击事件（冒泡）</view>

1.6 组件使用（必须遵循）
- 使用内置组件（view、text、image等）。
- 自定义组件需要在JSON配置中声明。
<view>
  <text>基础文本</text>
  <image src="{{imageUrl}}" />
</view>

2. TTSS样式规则
2.1 基本语法（必须遵循）
- 选择器和声明块必须正确格式化。
- 每个属性声明以分号结束。
.container {
  width: 100%;
  height: 100%;
}

2.2 选择器（必须遵循）
- 支持类型选择器（view、text）、类选择器（.className）、ID选择器（#id）等。
2.3 单位（必须遵循）
- 支持px、rpx、%、vh、vw。
- 数值0可以省略单位。
.box {
  width: 100px;
  height: 50%;
}

2.4 布局属性（必须遵循）
- 默认使用Flex布局。
- 常用属性：flex-direction、justify-content、align-items。
.flex-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

3. JavaScript功能和限制
3.1 卡片注册（必须遵循）
- 使用Card对象注册卡片。
- 在params中指定初始数据、生命周期回调和事件处理。
Card({
  data: {
    message: "hello world"
  },
  onLoad: function() {
    // 页面加载时执行
  },
  handleTap: function(e) {
    // 处理点击事件
  }
});

3.2 数据管理（必须遵循）
- 初始数据在data对象中定义。
- 使用this.setData()更新数据。
Card({
  data: {
    count: 0
  },
  increment: function() {
    this.setData({
      count: this.data.count + 1
    });
  }
});

3.3 生命周期方法（必须遵循）
- onLoad、onShow、onHide、onUnload。
Card({
  onLoad: function() {
    console.log('Card loaded');
  }
});

3.4 事件处理（必须遵循）
- 事件处理函数接收事件对象e。
Card({
  handleTap: function(e) {
    console.log('Tap event', e.target.dataset);
  }
});

4. 数据绑定和交互规则
4.1 数据传递（必须遵循）
- Native通过updateData向Lynx卡片传递数据，数据以native_data为key。
// Native端调用
lynxView.updateData("{\"native_data\": {\"ui_data\": {\"title\": \"新标题\"}}}");

// Lynx卡片接收
Card({
  onLoad: function() {
    console.log(this.data.native_data.ui_data.title);
  }
});

4.2 事件通信（必须遵循）
- 通过注册Module（"bridge"）与Native通信。
// 注册Module
const bridge = require('@byted-lynx/bridge');

// 调用Native方法
bridge.callNative('methodName', params, (res) => {
  console.log('Native返回结果', res);
});

5. 编译和渲染要求
5.1 项目结构（必须遵循）
- 包含project.config.json配置文件。
- 每个卡片包含同名的JS、TTML、TTSS和JSON文件。
project/
├── project.config.json
├── card1/
│   ├── index.js
│   ├── index.json
│   ├── index.ttml
│   ├── index.ttss

5.2 数据更新与渲染（必须遵循）
- 数据更新会同步到Lepus引擎。
- Lepus引擎生成新的Virtual DOM树。
- Diff算法比较新旧VDOM树，驱动视图差量更新。
5.3 性能优化（强烈建议）
- 减小template.js文件大小。
- 减少首屏需要渲染的节点数量和层级深度。
- 避免不必要的setData调用。
6. JSON配置（必须遵循）
6.1 项目配置（project.config.json）
- 配置项目类型、名称和卡片定义。
{
  "projectname": "MyLynxProject",
  "compileType": "card",
  "cards": {
    "card1": "./card1/index"
  }
}

6.2 卡片配置（index.json）
- 描述卡片的属性和使用的组件。
{
  "component": false,
  "usingComponents": {
    "my-component": "../components/my-component/index"
  }
}

7. Lynx组件参考
以下是Lynx框架中可用的所有组件及其功能的详细信息，按照逻辑类别组织。
7.1 容器类组件
7.1.1 view
基本功能和用途：基础容器组件，类似于HTML中的div，用于布局和包含其他组件。
主要属性：
- style：设置样式，支持flexbox布局
- className：引用外部样式类
- id：组件唯一标识符
- bindtap/catchtap：绑定点击事件（冒泡/非冒泡）
使用示例：
<view style="{{flexDirection: 'column'}}" bindtap="handleTap">
  <!-- 其他组件 -->
</view>

7.1.2 scroll-view
基本功能和用途：可滚动的视图容器，用于展示超出屏幕的内容。
主要属性：
- scroll-x：允许横向滚动
- scroll-y：允许纵向滚动
- scroll-top：设置竖向滚动条位置
- scroll-left：设置横向滚动条位置
- bindscroll：滚动事件回调
使用示例：
<scroll-view scroll-y="true" style="height: 300px;" bindscroll="onScroll">
  <!-- 滚动内容 -->
</scroll-view>

7.1.3 swiper
基本功能和用途：滑块视图容器，用于实现轮播图等效果。
主要属性：
- indicator-dots：是否显示指示点
- autoplay：是否自动切换
- interval：自动切换时间间隔
- duration：滑动动画时长
- bindchange：current改变事件
使用示例：
<swiper indicator-dots="true" autoplay="true" interval="3000">
  <view>滑块内容1</view>
  <view>滑块内容2</view>
</swiper>

7.1.4 list
基本功能和用途：列表容器，用于高效渲染大量数据。
主要属性：
- scroll-y：允许纵向滚动
- bindscroll：滚动事件回调
- bindscrolltolower：滚动到底部事件
使用示例：
<list bindscrolltolower="loadMore">
  <view tt:for="{{items}}" tt:key="id">{{item.name}}</view>
</list>

7.1.5 overlay
基本功能和用途：覆盖层容器，用于创建模态框、弹出菜单等。
主要属性：
- show：是否显示覆盖层
- z-index：层级
- bindtap：点击事件
使用示例：
<overlay show="{{showOverlay}}" bindtap="closeOverlay">
  <!-- 覆盖层内容 -->
</overlay>

7.2 文本和媒体类组件
7.2.1 text
基本功能和用途：文本组件，用于显示文字内容。
主要属性：
- selectable：文本是否可选
- space：显示连续空格
- decode：是否解码
使用示例：
<text selectable="true">{{message}}</text>

7.2.2 inline-text
基本功能和用途：行内文本组件，可以与其他文本组件内联显示。
主要属性：
- 与text组件类似
使用示例：
<view>
  <inline-text>前缀</inline-text>
  <inline-text>后缀</inline-text>
</view>

7.2.3 image
基本功能和用途：图片组件，用于展示图片。
主要属性：
- src：图片资源地址
- mode：图片裁剪、缩放模式
- lazy-load：是否懒加载
- bindload：图片加载完成事件
- binderror：图片加载失败事件
使用示例：
<image src="{{imageUrl}}" mode="aspectFit" bindload="onImageLoad"></image>

7.2.4 filter-image
基本功能和用途：带滤镜效果的图片组件。
主要属性：
- 与image组件类似
- filter：滤镜效果
使用示例：
<filter-image src="{{imageUrl}}" filter="blur(5px)"></filter-image>

7.2.5 inline-image
基本功能和用途：行内图片组件，可以与文本内联显示。
主要属性：
- 与image组件类似
使用示例：
<view>
  <inline-text>文本</inline-text>
  <inline-image src="icon.png"></inline-image>
</view>

7.3 表单类组件
7.3.1 input
基本功能和用途：输入框组件，用于文本输入。
主要属性：
- value：输入框的内容
- type：输入框类型（text, number, idcard, digit等）
- password：是否是密码类型
- placeholder：输入框为空时占位符
- bindinput：输入事件
- bindfocus：聚焦事件
- bindblur：失去焦点事件
使用示例：
<input type="text" placeholder="请输入" value="{{inputValue}}" bindinput="onInput"></input>

7.3.2 textarea
基本功能和用途：多行文本输入框。
主要属性：
- value：输入框的内容
- placeholder：输入框为空时占位符
- auto-height：是否自动增高
- bindinput：输入事件
使用示例：
<textarea placeholder="请输入多行文本" value="{{textareaValue}}" bindinput="onTextareaInput"></textarea>

7.3.3 picker
基本功能和用途：从底部弹起的滚动选择器。
主要属性：
- range：选择器的可选项数组
- value：当前选择的下标
- bindchange：选择改变事件
使用示例：
<picker range="{{array}}" value="{{index}}" bindchange="onPickerChange">
  <view>当前选择：{{array[index]}}</view>
</picker>

7.4 交互类组件
7.4.1 button
基本功能和用途：按钮组件。
主要属性：
- size：按钮大小
- type：按钮类型（primary, default, warn）
- disabled：是否禁用
- loading：是否显示加载图标
- bindtap：点击事件
使用示例：
<button type="primary" bindtap="onButtonTap">确定</button>

7.4.2 switch
基本功能和用途：开关选择器。
主要属性：
- checked：是否选中
- disabled：是否禁用
- bindchange：状态变化事件
使用示例：
<switch checked="{{switchChecked}}" bindchange="onSwitchChange"></switch>

7.4.3 slider
基本功能和用途：滑动选择器。
主要属性：
- min：最小值
- max：最大值
- step：步长
- value：当前值
- bindchange：完成一次拖动后触发
- bindchanging：拖动过程中触发
使用示例：
<slider min="0" max="100" value="{{sliderValue}}" bindchange="onSliderChange"></slider>

7.5 其他特殊组件
7.5.1 frame
基本功能和用途：用于在当前页面中嵌入另一个Lynx页面。
主要属性：
- src：嵌入页面的URL
- data：传递给嵌入页面的初始数据
- sync：控制上屏同步/异步
- bindload：资源加载监听
- binderror：错误捕获
- bts-mode：控制BTS线程共享/隔离
- sandbox：沙箱模式，控制BTS线程和Group隔离
使用示例：
<view>
  <frame
    id="my-iframe"
    src="https://template.js"
    style="{{width: '100%'}}"
    className="frame"
    data="{{frameData}}"
    binderror="onError"
    bindload="onLoad"
  />
</view>

7.5.2 x-element
基本功能和用途：高级UI组件，需要在容器中注册后使用。
主要属性：
- 根据具体的x-element组件定义
使用示例：
<x-element-name prop1="value1" prop2="value2"></x-element-name>

请确保生成的Lynx卡片符合所有"必须遵循"的规则，以确保可以成功编译和渲染。

---
最佳实践建议
为了获得最佳的Lynx卡片生成效果，建议您在向Claude 3.7提供上述提示文本的同时，也明确说明以下信息：
1. 卡片的具体功能和用途
2. 卡片需要展示的数据结构
3. 卡片的交互需求
4. 卡片的样式和布局要求
5. 是否需要与Native端进行通信
提供越详细的需求描述，Claude 3.7生成的Lynx卡片代码就越符合您的期望



在 Lynx 框架中， 无法直接在模板中调用 data 对象中定义的函数 。根据参考信息，Lynx 的模板与逻辑交互需通过以下两种机制实现：
1. 事件绑定 ****
操作方式 ：在 .ttml 模板中，通过 bindtap 等事件属性绑定 data 中定义的函数名，例如 bindtap="handleClick"。
原理 ：事件触发时，Lynx 会调用 JS 文件中对应的函数（如 handleClick）。
示例 ：
javascript
// index.js
data: {
  handleClick: function() { /* 处理逻辑 */ }
}

html
<!-- index.ttml -->
<button bindtap="handleClick">点击</button>


1. 模板函数（Template API） ****
操作方式 ：
定义函数 ：在独立文件（如 utils.lepus）中定义函数并导出：
javascript
// utils.lepus
export function formatTime(timestamp) { /* 处理逻辑 */ }

注册函数 ：在 index.json 中声明模板函数模块：
json
{
  "usingTemplateAPI": {
    "templateFunctions": [
      { "path": "utils.lepus", "name": "utils" }
    ]
  }
}

模板调用 ：在 .ttml 中通过 {{ utils.formatTime(data.timestamp) }} 调用。
优势 ：避免首屏闪烁，提升性能（直接在主线程处理）。


辅助说明
限制 ：data 中的函数仅支持事件绑定， 不可作为计算属性在模板表达式内调用 （如 {{ handleClick() }} 无效）。
替代方案 ：复杂逻辑需通过 Template API 的预处理函数 提前处理数据（参考信息 2 的 “数据预处理” 部分）
。
性能优化 ：高频调用的函数建议使用模板函数而非事件绑定，减少 JS 与 Native 层通信开销

在 Lynx 框架中， bindtap 是用于 绑定点击事件 的属性，其核心作用与使用方法如下：
功能定义
bindtap 用于为 Lynx 组件（如 <view>、<text>）绑定 点击事件回调函数 ，当用户点击元素时触发执行。事件传播遵循冒泡规则（从子元素向父元素传递）
。
基础用法
在 TTML 模板中声明 bindtap 属性并关联 JS 中的事件处理函数：
html
<view bindtap={handleClick}>点击区域</view>

在 JS 文件中定义函数：
javascript
handleClick(event) {
  console.log('点击事件触发', event.detail);
}

其中 event 参数包含点击坐标、时间戳等信息
。
阻止事件冒泡
若需阻止事件冒泡，需改用 catchtap 属性：
html
<view catchtap={handleClick}>不会触发父元素点击</view>

此属性在捕获阶段拦截事件，阻止向上层传递
。
注意事项
自定义组件兼容性 ：部分高阶组件（如 @lynx/card）要求使用 x-bindtap 替代 bindtap
。
重复触发问题 ：早期版本中嵌套组件可能导致 bindtap 触发两次，建议升级至 1.0.1+ 版本 或改用 catchtap
。
数据传递 ：可通过 data-* 属性（如 data-id="123"）向事件回调传递自定义参数
。
辅助信息
除此之外，还为你找到以下内容：
事件传播机制 ：Lynx 事件分 捕获阶段 （capture-bind/capture-catch）和 冒泡阶段 （bind/catch），catchtap 在捕获阶段即拦截事件
。
性能优化 ：复杂页面中建议减少嵌套的 bindtap 绑定，改用事件委托或全局监听（如 global-bindtap）
。
埋点集成 ：可通过 data-report-click-name 等声明式属性与 bindtap 结合，实现点击埋点自动上报
。