使用说明https://bytedance.larkoffice.com/docx/LBpgdyeidozqNHxjvULcbOcInlf
本文档提供了一套针对Claude 3.7优化的Lynx卡片开发规范提示文本。当您需要让Claude 3.7生成符合Lynx规范的卡片代码时，只需将本文档中"提示文本"部分的内容复制，作为指令提供给Claude 3.7，即可获得符合规范且可成功编译和渲染的Lynx卡片代码。
使用步骤：
1. 复制下方"提示文本"部分的全部内容
2. 将其作为指令提供给Claude 3.7，同时附上您对Lynx卡片的具体需求
3. Claude 3.7将基于这些规范生成符合要求的Lynx卡片代码

---
提示文本
以下是Lynx卡片开发的规范提示文本，请在生成Lynx卡片时严格遵循这些规则：
框架概述
Lynx是一款高性能跨端解决方案，利用Web技术栈快速构建Native视图。核心理念是"数据驱动渲染"，优势在于"首屏直出"和高性能视图更新。Lynx卡片由TTML、TTSS、JS和JSON四部分组成。
1. TTML结构和语法规则
1.1 基本结构（必须遵循）
- 根节点必须是<view>。
- 元素必须正确闭合（<image />或<view></view>）。
- 标签名必须全小写。
<view>
  <text>{{message}}</text>
</view>

1.2 数据绑定（必须遵循）
- 使用{{expression}}进行数据绑定。
- 支持简单表达式计算（如{{a + b}}）。
<view>
  <text>{{user.name}}</text>
  <text>{{isActive ? '活跃' : '非活跃'}}</text>
</view>

1.3 条件渲染（必须遵循）
- 使用tt:if、tt:elif和tt:else控制元素渲染。
- 条件表达式必须是布尔值。
<view tt:if="{{condition}}">条件为真时显示</view>

1.4 列表渲染（必须遵循）
- 使用tt:for进行列表渲染。
- 建议使用tt:key指定唯一标识符。
<view tt:for="{{items}}" tt:key="id">
  {{item.name}}
</view>

1.5 事件绑定（必须遵循）
- 使用bind绑定冒泡事件，catch:绑定非冒泡事件。
- 事件处理函数必须在JS中定义。
<view bindtap="handleTap">点击事件（冒泡）</view>

1.6 组件使用（必须遵循）
- 使用内置组件（view、text、image等）。
- 自定义组件需要在JSON配置中声明。
<view>
  <text>基础文本</text>
  <image src="{{imageUrl}}" />
</view>

2. TTSS样式规则
2.1 基本语法（必须遵循）
- 选择器和声明块必须正确格式化。
- 每个属性声明以分号结束。
.container {
  width: 100%;
  height: 100%;
}

2.2 选择器（必须遵循）
- 支持类型选择器（view、text）、类选择器（.className）、ID选择器（#id）等。
2.3 单位（必须遵循）
- 支持px、rpx、%、vh、vw。
- 数值0可以省略单位。
.box {
  width: 100px;
  height: 50%;
}

2.4 布局属性（必须遵循）
- 默认使用Flex布局。
- 常用属性：flex-direction、justify-content、align-items。
.flex-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

3. JavaScript功能和限制
3.1 卡片注册（必须遵循）
- 使用Card对象注册卡片。
- 在params中指定初始数据、生命周期回调和事件处理。
Card({
  data: {
    message: "hello world"
  },
  onLoad: function() {
    // 页面加载时执行
  },
  handleTap: function(e) {
    // 处理点击事件
  }
});

3.2 数据管理（必须遵循）
- 初始数据在data对象中定义。
- 使用this.setData()更新数据。
Card({
  data: {
    count: 0
  },
  increment: function() {
    this.setData({
      count: this.data.count + 1
    });
  }
});

3.3 生命周期方法（必须遵循）
- onLoad、onShow、onHide、onUnload。
Card({
  onLoad: function() {
    console.log('Card loaded');
  }
});

3.4 事件处理（必须遵循）
- 事件处理函数接收事件对象e。
Card({
  handleTap: function(e) {
    console.log('Tap event', e.target.dataset);
  }
});

4. 数据绑定和交互规则
4.1 数据传递（必须遵循）
- Native通过updateData向Lynx卡片传递数据，数据以native_data为key。
// Native端调用
lynxView.updateData("{\"native_data\": {\"ui_data\": {\"title\": \"新标题\"}}}");

// Lynx卡片接收
Card({
  onLoad: function() {
    console.log(this.data.native_data.ui_data.title);
  }
});

4.2 事件通信（必须遵循）
- 通过注册Module（"bridge"）与Native通信。
// 注册Module
const bridge = require('@byted-lynx/bridge');

// 调用Native方法
bridge.callNative('methodName', params, (res) => {
  console.log('Native返回结果', res);
});

5. 编译和渲染要求
5.1 项目结构（必须遵循）
- 包含project.config.json配置文件。
- 每个卡片包含同名的JS、TTML、TTSS和JSON文件。
project/
├── project.config.json
├── card1/
│   ├── index.js
│   ├── index.json
│   ├── index.ttml
│   ├── index.ttss

5.2 数据更新与渲染（必须遵循）
- 数据更新会同步到Lepus引擎。
- Lepus引擎生成新的Virtual DOM树。
- Diff算法比较新旧VDOM树，驱动视图差量更新。
5.3 性能优化（强烈建议）
- 减小template.js文件大小。
- 减少首屏需要渲染的节点数量和层级深度。
- 避免不必要的setData调用。
6. JSON配置（必须遵循）
6.1 项目配置（project.config.json）
- 配置项目类型、名称和卡片定义。
{
  "projectname": "MyLynxProject",
  "compileType": "card",
  "cards": {
    "card1": "./card1/index"
  }
}

6.2 卡片配置（index.json）
- 描述卡片的属性和使用的组件。
{
  "component": false,
  "usingComponents": {
    "my-component": "../components/my-component/index"
  }
}

请确保生成的Lynx卡片符合所有"必须遵循"的规则，以确保可以成功编译和渲染。

---
最佳实践建议
为了获得最佳的Lynx卡片生成效果，建议您在向Claude 3.7提供上述提示文本的同时，也明确说明以下信息：
1. 卡片的具体功能和用途
2. 卡片需要展示的数据结构
3. 卡片的交互需求
4. 卡片的样式和布局要求
5. 是否需要与Native端进行通信
提供越详细的需求描述，Claude 3.7生成的Lynx卡片代码就越符合您的期望。