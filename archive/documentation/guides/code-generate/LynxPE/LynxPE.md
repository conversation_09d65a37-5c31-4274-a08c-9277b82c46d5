# Lynx PE 开发指南

本指南汇总了 Lynx 框架的开发规范和最佳实践，帮助开发者更高效地构建 Lynx 应用。

## 目录

1. [Lynx SFC（单文件组件）规范](./LynxSFC.md)
2. [Lynx 文件结构规范](./LynxFiles.md)
3. [Lynx 数据与通信规范](./LynxDataProps.md)
4. [Lynx Canvas 开发规范](./LynxCanvasRules.md)
5. [Lynx 通用开发规则](./LynxRules.md)
6. [Lynx JSBridge 通信规范](./LynxBridge.md)
7. [综合开发规范](./LynxPEUpdated.md)

## 概述

Lynx 是一个高性能的跨平台开发框架，提供了类似 Web 的开发体验，同时能够构建原生级别性能的应用。

### 主要特性

- **单文件组件**: 使用类似 Vue 的单文件组件结构
- **响应式布局**: 使用 rpx 作为响应式单位，适配不同屏幕尺寸
- **高性能渲染**: 基于 Krypton 引擎的 Canvas 渲染能力
- **丰富的 API**: 提供多媒体、传感器、文件 I/O 等丰富 API

### 重要原则

1. **生命周期管理**: 正确处理组件和资源的生命周期，避免内存泄漏
2. **数据流管理**: 遵循单向数据流原则，合理使用状态管理
3. **性能优化**: 减少不必要的渲染，批量处理操作，使用离屏渲染
4. **错误处理**: 实现适当的错误处理和降级策略
5. **平台兼容**: 注意 iOS 和 Android 平台差异，使用通用 API

## 快速入门

如需快速了解 Lynx 开发规范，建议查看[综合开发规范](./LynxPEUpdated.md)文档，该文档整合了各部分的核心规则和最佳实践。

对于特定领域的详细规范，请参考相应的子文档：

- 组件开发：[Lynx SFC 规范](./LynxSFC.md)
- 文件结构：[Lynx 文件规范](./LynxFiles.md)
- 数据通信：[Lynx 数据规范](./LynxDataProps.md)
- 图形渲染：[Lynx Canvas 规范](./LynxCanvasRules.md)
- 原生通信：[Lynx JSBridge 规范](./LynxBridge.md)

