# Lynx 开发规范综合指南

## 一、LynxSFC（单文件组件）核心规则

### 1. 基本结构
- 单文件组件包含三个主要部分：`<template>`、`<script>` 和 `<style>`
- 组件应遵循单一职责原则，每个组件专注解决一个特定问题
- 文件命名使用 PascalCase（如 UserProfile.lynx）

### 2. 模板部分规则
- 使用 Lynx 特定标签：`<view>`、`<text>`、`<image>`、`<button>`、`<scroll-view>` 等
- 数据绑定使用双大括号：`{{变量名}}`
- 事件绑定使用 `@事件名="处理函数"` 或 `bindtap="处理函数"`
- 条件渲染使用 `tt:if="{{条件}}"` 和 `tt:else`
- 列表渲染使用 `tt:for="{{数组}}" tt:key="id"`

### 3. 样式部分规则
- 支持类似 CSS 的语法，但有特定扩展
- 使用 rpx 作为响应式单位（根据屏幕宽度自动调整）
- 支持样式作用域限定（scoped styles）
- 避免内联样式，优先使用类选择器和外部样式文件
- 默认使用 Flex 布局模型

### 4. 脚本部分规则
- 组件逻辑通过 `export default {}` 导出
- 数据定义使用 `data()` 函数返回对象
- 方法定义在 `methods` 对象中
- 生命周期钩子包括：`created`、`attached`、`ready`、`detached`
- 支持混入（Mixins）机制复用组件逻辑
- 使用 `setData()` 方法更新数据并触发视图重新渲染

### 5. 组件通信规则
- 父组件向子组件传递数据：通过属性（props）
- 子组件向父组件通信：通过事件（triggerEvent）
- 无直接关系组件通信：使用状态管理或事件总线
- 避免组件层级过深导致的数据传递复杂问题

## 二、Lynx JSBridge 核心规则

### 1. JSBridge 基本概念
- JSBridge 是 JavaScript 与 Native 之间通信的桥梁
- 支持双向通信：JS调用Native功能，Native触发JS执行
- 基于 JSI (JavaScript Interface) 和 NAPI (Node-API) 实现
- 包含三个主要部分：NativeModule、JSModule、Lynx Internal API

### 2. NativeModule 使用规范
- 将 Native 能力封装成模块暴露给 JS 侧
- 支持数据双向传递，JS 可调用 Native 方法并获取返回值
- 常用方法：`x.showToast`、`x.request`、`tt.getUserInfo`、`tt.openSchema`
- 参数格式：支持基本类型（int、bool、string）和复杂类型（Object、Array）

### 3. JSModule 使用规范
- 将 JS 能力封装成模块暴露给 Native 侧
- 数据单向传递：Native 到 JS
- 使用 `registerModule` 注册自定义模块
- 通过 `GlobalEventEmitter` 实现事件监听和触发

### 4. 网络请求规范
```javascript
// 标准网络请求
x.request({
  url: 'https://api.example.com/data',
  method: 'GET',
  params: { id: 123 },
  header: { 'Accept': 'application/json' }
}, (response) => {
  if (response.code == 1) {
    console.log('请求成功', response.data);
  }
});
```

### 5. UI交互规范
```javascript
// Toast提示
x.showToast({
  message: '操作成功',
  type: 'success',
  duration: 2000
});

// 模态弹窗
tt.showModal({
  title: '提示',
  content: '确定要执行此操作吗？',
  success: (res) => {
    if (res.confirm) {
      // 用户点击确定
    }
  }
});
```

### 6. 事件监听规范
```javascript
// 注册事件监听
this.getJSModule('GlobalEventEmitter').addListener('networkChange', this.handleNetworkChange, this);

// 移除事件监听（避免内存泄漏）
this.getJSModule('GlobalEventEmitter').removeListener('networkChange', this.handleNetworkChange);
```

### 7. 算法包集成规范（Pitaya Lynx SDK）
```javascript
// 导入Pitaya模块
import { PTYLynxModule } from '@ba-intel/pitaya-lynx';

// 创建模块实例
const ptyLynxModule = new PTYLynxModule();

// 运行算法包
ptyLynxModule.runTask({
  taskName: 'recommendation_algorithm',
  taskVersion: '1.0.0',
  taskParams: { userId: '12345' }
}, (result) => {
  console.log('算法包运行结果', result);
});

// 注册消息处理
ptyLynxModule.registerMessageHandler((message) => {
  console.log('收到算法包消息', message);
});
```

### 8. 常见错误处理
- **参数问题**：确保参数数量、顺序、类型正确
- **时机问题**：在正确的生命周期调用JSBridge方法
- **内存泄漏**：及时移除事件监听器
- **异步处理**：正确处理回调函数和Promise

## 三、LynxPE（页面开发）核心规则

### 1. 文件组织
- 相关文件（ttml、ttss、js、json）保持在同一目录下，使用相同基本名称
- 页面文件一般包括：index.ttml、index.ttss、index.js、index.json

### 2. 页面生命周期
- `onLoad`：页面加载时触发
- `onShow`：页面显示时触发
- `onReady`：页面初次渲染完成时触发
- `onHide`：页面隐藏时触发
- `onUnload`：页面卸载时触发

### 3. 数据更新和管理
- 使用 `this.setData({key: value})` 更新数据并触发视图更新
- 支持 Reduck 状态管理（基于 Redux 的状态管理库）
- 新项目推荐使用 Jotai、Zustand 或 Valtio 作为状态管理方案
- 遵循单向数据流原则，保持数据流向的清晰性

## 三、Lynx Canvas 开发规则

### 1. 基本概念
- Lynx Canvas 是框架中的核心渲染组件，构建于自研的跨平台互动引擎 Krypton 之上
- 支持标准的 2D 和 WebGL 图形接口，多媒体能力和算法能力
- 使用双线程模型，Canvas 元素和视图是分离的

### 2. Canvas 标签与视图
- 在 ttml 中添加 `<canvas style="width: 160px; height: 160px;" name="canvasName"></canvas>` 标签
- name 属性不允许重复，用于在 JS 代码中查找 Canvas
- 如需添加边框、圆角等样式，需要在外部 View 中嵌套 Canvas 标签
- Canvas 不支持使用 ttif 或放置在包含 ttif 的容器中，可使用 visibility:hidden 替代

### 3. Canvas 创建和使用
- 使用 `lynx.krypton.createCanvasNG()` 创建 Canvas 元素（推荐）
- 不要使用已弃用的 `lynx.createCanvas()` 方法
- 需要调用 `attachToCanvasView()` 将 Canvas 元素绑定到视图
- resize 事件监听器必须在 attachToCanvasView 之前设置
- 处理尺寸时考虑设备像素比：乘以 SystemInfo.pixelRatio 确保在高分辨率设备上显示清晰

### 4. Canvas 生命周期管理
- 在 onLoad 中创建资源
- 在 onHide 中暂停资源（如视频播放）
- 在 onShow 中恢复资源
- 在 onDestroy 中释放资源，调用 canvas.dispose()
- 异步操作中检查组件是否已销毁，避免在销毁后执行操作
- 页面卸载时解绑 Canvas 元素：canvas.detachFromCanvasView()

### 5. Canvas API 限制与差异
- 需要使用 attachToCanvasView 和 detachFromCanvasView 进行显式绑定和解绑
- WebGL 的 getContext 需要 antialias 和 enableMSAA 都为 true 才能启用 MSAA
- 不支持的特性：不要使用 roundrect、23 年以后的 canvas 新方法、globalCompositeOperation、不规则 shadow 等
- 触摸事件应使用 touchstart、touchmove 和 touchend

### 6. Canvas 性能优化
- 使用批量绘制减少状态切换
- 复杂静态内容使用离屏 Canvas 预渲染：`lynx.createOffscreenCanvas(width, height)`
- 避免在每一帧都重新绘制整个画布
- Canvas-NG 将 OpenGL 指令在 GPU 线程执行，减轻 JS 线程负载

### 7. Canvas 错误处理
- 实现降级方案，检查 lynx.helium() 返回值
- 对 Canvas 创建失败进行重试处理（添加适当延迟或在 requestAnimationFrame 回调中重试）
- 在 schema 中添加参数 &enable_canvas=1 启用 canvas 扩展
- 使用随机生成的 id 命名 canvas，避免多个同名 canvas 导致的问题

## 四、开发最佳实践

### 1. 性能优化
- 使用懒加载组件，减少初始加载时间
- 避免过度渲染，合理使用条件渲染
- 减少不必要的数据绑定和监听
- 合理使用 Forest 资源加载方案，优化资源加载
- 使用虚拟列表处理大量数据展示

### 2. 代码组织和结构化
- 保持适当的组件粒度，既不过于庞大也不过于细碎
- 将复杂逻辑抽离到单独的函数或模块
- 按功能或业务域组织组件目录结构
- 使用一致的命名约定（PascalCase 命名组件）

### 3. 样式管理
- 使用类选择器和外部样式文件，避免内联样式
- 注意 TTSS 与标准 CSS 的差异（如 rpx 单位、选择器限制等）
- 使用作用域样式（scoped styles）避免样式冲突

### 4. 资源管理
- 在组件销毁时释放所有资源（如 Canvas、视频、音频等）
- 在 `onHide` 生命周期中暂停资源消耗（如暂停视频播放）
- 对于异步操作，检查组件是否已销毁，避免在销毁后执行操作

### 5. 跨平台兼容性
- 注意 iOS 和 Android 平台差异，使用通用 API
- 在 Android 平台调用 Native 方法时需指定完整包名
- 在 iOS 平台调用 Native 方法时只需指定类名
- 实现适当的降级方案，应对不同设备和版本的兼容性问题

## 五、Lynx Canvas 示例代码

```javascript
Page({
  data: {
    canvas: null,
    context: null,
    isDrawing: false,
    lastX: 0,
    lastY: 0
  },
  
  onLoad() {
    this.setupCanvas();
  },
  
  setupCanvas() {
    // 创建 Canvas 元素
    const canvas = lynx.krypton.createCanvasNG();
    
    // 监听 resize 事件（必须在 attachToCanvasView 之前）
    canvas.addEventListener('resize', ({ width, height }) => {
      canvas.width = width;
      canvas.height = height;
      
      // 重新设置绘图上下文
      const context = canvas.getContext('2d');
      context.lineWidth = 2;
      context.strokeStyle = '#3498db';
      context.lineJoin = 'round';
      context.lineCap = 'round';
      
      this.setData({
        context: context
      });
    });
    
    // 获取 Canvas 视图的实际尺寸
    this.createSelectorQuery()
      .select('#drawing-canvas')
      .invoke({
        method: 'boundingClientRect',
        success: (res) => {
          // 获取设备像素比
          const pixelRatio = SystemInfo.pixelRatio || 1;
          
          // 设置 Canvas 元素的像素尺寸
          canvas.width = res.width * pixelRatio;
          canvas.height = res.height * pixelRatio;
          
          // 绑定到 Canvas 视图
          canvas.attachToCanvasView('drawing-canvas');
          
          // 获取绘图上下文
          const context = canvas.getContext('2d');
          context.lineWidth = 2;
          context.strokeStyle = '#3498db';
          context.lineJoin = 'round';
          context.lineCap = 'round';
          
          this.setData({
            canvas: canvas,
            context: context
          });
        }
      })
      .exec();
  },
  
  // 触摸开始事件
  touchStart(e) {
    const touch = e.touches[0];
    this.setData({
      isDrawing: true,
      lastX: touch.x,
      lastY: touch.y
    });
  },
  
  // 触摸移动事件
  touchMove(e) {
    if (!this.data.isDrawing) return;
    
    const touch = e.touches[0];
    const ctx = this.data.context;
    
    ctx.beginPath();
    ctx.moveTo(this.data.lastX, this.data.lastY);
    ctx.lineTo(touch.x, touch.y);
    ctx.stroke();
    
    this.setData({
      lastX: touch.x,
      lastY: touch.y
    });
  },
  
  // 触摸结束事件
  touchEnd() {
    this.setData({
      isDrawing: false
    });
  },
  
  // 清除画布
  clearCanvas() {
    if (this.data.canvas && this.data.context) {
      const ctx = this.data.context;
      ctx.clearRect(0, 0, this.data.canvas.width, this.data.canvas.height);
    }
  },
  
  // 页面卸载时解绑 Canvas
  onUnload() {
    if (this.data.canvas) {
      this.data.canvas.detachFromCanvasView();
      this.data.canvas = null;
    }
  }
});
``` 