# Code Generate 模块文档

**最后更新**: 2025-08-10  

本目录包含代码生成模块的所有技术文档，包括架构设计、功能说明、开发指南和故障排除等内容。

## 文档导航

### 架构文档

- [架构概览](architectural-overview.md) - 整体架构、数据流和关键组件说明
- [数据流与交互流程](../数据流与交互流程.md) - 详细的数据流向和交互过程
- [Web/Lynx架构设计](architecture/WEB_LYNX_ARCHITECTURE.md) - Web和Lynx系统的并行架构
- [Context管理架构](architecture/CONTEXT_MANAGEMENT.md) - 状态管理系统设计
- [Claude流处理架构](architecture/CLAUDE_STREAM_PROCESSING.md) - 流式数据处理机制

### 功能文档

- [功能列表](../功能列表.md) - 所有功能和组件的详细说明
- [RPC系统说明](../RPC/README.md) - RPC请求处理和数据流
- [状态保存系统](standards/STATE_PRESERVATION.md) - Lynx Tab状态保存机制

### 开发指南

- [组件开发规范](standards/COMPONENT_STANDARDS.md) - 组件开发的标准和最佳实践
- [Hook使用指南](HOOK_USAGE_GUIDE.md) - React Hook使用规范和注意事项
- [性能优化指南](PERFORMANCE_OPTIMIZATION_GUIDE.md) - 代码优化和性能提升建议

### 维护与故障排除

- [变更日志](../Changelog.md) - 所有版本的变更记录
- [常见问题分析](troubleshooting/COMMON_ISSUES.md) - 常见问题及解决方案
- [保守优化计划](CONSERVATIVE_OPTIMIZATION_PLAN.md) - 性能优化路线图

## 文档分类说明

- **architecture/** - 架构设计相关文档
- **guides/** - 开发指南和最佳实践
- **standards/** - 编码标准和规范
- **troubleshooting/** - 问题排查和修复指南
- **changelog/** - 变更记录和版本说明
- **api/** - API接口文档

## 文档维护指南

1. 所有新功能必须更新相关文档
2. 架构变更需要更新架构文档并获得架构组批准
3. 重要修复需要在变更日志中记录
4. 文档需要按照[文档规范](DOCUMENT_RULES/README.md)进行更新
