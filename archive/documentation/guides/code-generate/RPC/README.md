# RPC 系统架构文档

日期: 2025-06-05
状态: approved
版本: 1.1
责任人: RPC系统组

## 系统概览

RPC（Remote Procedure Call）系统是代码生成功能的核心组件，负责与AI模型通信并管理代码生成的状态。系统分为两个完全并行且独立的流程：

1. **WebRPC流程** - 负责Web代码的生成和管理
2. **LynxRPC流程** - 负责Lynx代码的生成和管理

> **重要：** WebRPC和LynxRPC是并行触发的独立流程，而非串行执行。两者使用完全独立的上下文和状态管理，互不干扰。

## 架构设计

系统采用React Context + Reducer模式实现，替代了原有的EventBus模式，主要组件包括：

### 核心上下文组件

- **WebRPCContext** - 管理Web代码生成的状态和操作
- **LynxContext** - 管理Lynx代码生成的状态和操作

### 服务层

- **WebRPCService** - Web代码生成API调用和响应处理 
- **LynxRPCService** - Lynx代码生成API调用和响应处理

### 数据解析

- **claudeStreamParser** - 统一的Claude流式数据解析器，处理AI模型返回的流式数据

### 流程示意图

```
用户操作
   │
   ├──────► WebRPC流程 ───► WebRPCContext ───► 状态更新
   │           │
   │           └──────────► Web代码生成
   │
   └──────► LynxRPC流程 ──► LynxContext ────► 状态更新
               │
               └──────────► Lynx代码生成
```

## 数据流管理

- 每个RPC服务管理自己的API调用和响应处理
- 使用统一的流式响应处理器，支持实时更新代码
- 版本控制机制防止过期更新覆盖新内容
- 全局备份机制防止状态丢失
- Context API注册系统确保状态更新与视图同步

## 关键特性

1. **完全隔离的数据流** - Web和Lynx流程互不干扰
2. **流式代码更新** - 实时显示生成的代码
3. **会话管理** - 支持多会话并在会话间切换
4. **代码版本控制** - 防止旧版本覆盖新版本
5. **全局备份机制** - 防止意外状态丢失
6. **自动上传到CDN** - 代码生成完成后自动创建可预览的URL
7. **续传支持** - 支持代码生成中断后的续传

## 流式数据解析

系统使用统一的`claudeStreamParser`模块处理所有Claude API返回的流式数据，实现了：

1. **格式统一处理** - 处理来自不同API版本和格式的响应
2. **批处理优化** - 批量处理数据块，减少UI更新频率
3. **内容提取** - 安全地从各种格式中提取代码内容
4. **续传检测** - 自动检测代码是否需要续传
5. **错误恢复** - 处理流式传输中的错误和中断

## 使用指南

### WebRPC使用

```jsx
import { useWebRPC } from 'src/routes/refactor_code_generate/contexts/WebRPCContext';

function MyComponent() {
  const { 
    state, 
    updateWebCode, 
    setWebRPCLoading,
    setWebCodeComplete,
    setWebRPCError
  } = useWebRPC();
  
  // 使用state和方法...
}
```

### LynxRPC使用

```jsx
import { useLynx } from 'src/routes/refactor_code_generate/contexts/LynxContext';

function MyComponent() {
  const { 
    state,
    actions: {
      updateCode,
      setLoading,
      setComplete,
      setError,
      setPlaygroundUrl
    }
  } = useLynx();
  
  // 使用state和actions...
}
```

### Web转Lynx

当Web代码生成完成后，用户可以点击"转换为Lynx"按钮触发转换：

```jsx
const { state: webState } = useWebRPC();
const { actions: lynxActions } = useLynx();

// 在按钮点击事件处理器中
const handleConvert = () => {
  if (webState.webCode) {
    // 设置Lynx加载状态
    lynxActions.setLoading(true);
    lynxActions.setError(null);
    lynxActions.setComplete(false);

    // 调用转换函数 - 注意：这会创建新的会话ID
    const lynxSessionId = generateUniqueId();
    lynxActions.setSessionId(lynxSessionId);
    
    // 使用Web代码作为输入，但使用独立的会话ID
    convertWebCodeToLynx(
      webState.webCode,
      lynxSessionId,
      {
        updateLynxCode: lynxActions.updateCode,
        setLynxCodeComplete: lynxActions.setComplete,
        setLynxRPCLoading: lynxActions.setLoading,
        setLynxRPCError: lynxActions.setError,
        setLynxPlaygroundUrl: lynxActions.setPlaygroundUrl
      }
    );
  }
};
```

## 批处理机制

为提高性能，系统实现了统一的批处理机制：

```typescript
class StreamBatchProcessor {
  private batchedContent = '';
  private updateTimer: NodeJS.Timeout | null = null;
  
  processStreamUpdate(content: string) {
    // 累加内容
    this.batchedContent += content;
    
    // 根据内容大小决定更新策略
    if (content.length > 1000) {
      // 大块内容立即更新
      this.flushBatchedContent();
    } else if (content.length > 100) {
      // 中等内容50ms防抖
      this.scheduleUpdate(50);
    } else {
      // 小内容100ms防抖
      this.scheduleUpdate(100);
    }
  }
  
  private scheduleUpdate(delay: number) {
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
    }
    
    this.updateTimer = setTimeout(() => this.flushBatchedContent(), delay);
  }
  
  private flushBatchedContent() {
    if (this.batchedContent) {
      // 更新代码，使用版本控制
      const version = Date.now();
      this.updateCallback(this.batchedContent, version);
      this.batchedContent = '';
    }
  }
}
```

## 错误处理

系统内置了全面的错误处理机制：

1. **请求错误** - 自动捕获并显示API请求错误
2. **解析错误** - 处理流式数据解析中的错误
3. **上传错误** - 处理代码上传到CDN时的错误
4. **状态错误** - 防御性编程避免状态管理错误

## 最佳实践

1. 始终使用Context API访问和更新状态，避免直接操作DOM或localStorage
2. Web和Lynx流程应保持完全独立，不应相互依赖
3. 生成中的代码只在传输完成后写入localStorage
4. 流式数据处理应遵循批处理原则，避免过于频繁的状态更新
5. 使用统一的claudeStreamParser处理所有流式数据
6. 新功能应扩展现有Context API而非创建新的全局状态
7. 使用版本控制机制避免旧数据覆盖新数据
8. 为每个RPC流程创建独立的会话ID

## 相关文档

- [Web和Lynx代码生成架构](/docs/architecture/WEB_LYNX_ARCHITECTURE.md)
- [WebRPC和LynxRPC并行架构实现](/docs/RPC/WebRPC_LynxRPC_并行架构实现.md)
- [Context状态管理](/docs/architecture/CONTEXT_MANAGEMENT.md)
- [错误处理策略](/docs/architecture/ERROR_HANDLING_STRATEGY.md)

---

*本文档描述了RPC系统的整体架构和使用方法，作为开发人员的参考指南。* 