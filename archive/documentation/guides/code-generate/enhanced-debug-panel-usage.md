# 增强版 Lynx Debug Panel 使用说明

## 概述

增强版 Lynx Debug Panel 在原有功能基础上新增了以下调试功能：

- 📏 **代码大小分析** - 显示代码的字节数、行数、字符数和单词数
- 🔧 **函数参数枚举** - 分析代码中所有函数的入参和出参信息
- 🔍 **原始数据展示** - 显示从接口获得的原始响应数据
- 🔍 **解析数据展示** - 显示经过处理后的解析数据

## 如何使用

### 1. 打开调试面板

点击页面右上角的 `🐛 Debug` 按钮打开调试面板。

### 2. 启用高级模式

在调试面板标题栏中点击 `🔬` 按钮启用高级模式，此时按钮会变为绿色。

### 3. 查看新增功能

启用高级模式后，面板会显示以下新增区域：

#### 📏 Code Size Analysis
显示当前代码的统计信息：
- **Bytes**: 代码的字节大小
- **Lines**: 代码行数
- **Chars**: 字符总数
- **Words**: 单词数量

#### 🔧 Function Parameters
显示代码中检测到的函数信息：
- 函数名称和类型（function/method/arrow/async）
- 函数位置（行号和列号）
- 输入参数列表（参数名、类型、是否可选、默认值）

#### 🔍 Data Analysis
显示原始和解析数据：
- **Raw Response**: 从 API 接收的原始响应数据
- **Parsed Data**: 经过处理后的解析数据

## API 使用

### 基础调试数据发送

```javascript
// 发送基础调试数据
sendLynxDebugData({
  contentLength: 1500,
  estimatedTotalSize: 5000,
  progressPercent: 30,
  safeProgress: 30,
  jsonParseSuccess: true,
  throttleBlocked: false,
  chunkCount: 5
}, codeContent); // 第二个参数是代码内容，用于分析
```

### 高级调试数据发送

```javascript
// 发送包含原始和解析数据的调试信息
sendLynxDebugDataWithAnalysis(
  {
    contentLength: 1500,
    progressPercent: 50,
    // ... 其他基础数据
  },
  rawResponseData,    // 原始响应数据
  parsedData,         // 解析后的数据
  codeContent         // 代码内容
);
```

## 新增接口说明

### DebugData 接口扩展

```typescript
interface DebugData {
  // ... 原有字段
  
  // 🆕 新增字段
  rawResponseData?: any;           // 原始接口响应数据
  parsedData?: any;               // 解析后的数据
  codeSize?: {                    // 代码大小统计
    bytes: number;
    lines: number;
    characters: number;
    wordsCount: number;
  };
  functionParams?: FunctionParamInfo[]; // 函数参数信息
}
```

### FunctionParamInfo 接口

```typescript
interface FunctionParamInfo {
  functionName: string;           // 函数名
  inputParams: ParameterInfo[];   // 输入参数
  outputParams: ParameterInfo[];  // 输出参数（暂未实现）
  location: {                     // 函数位置
    line: number;
    column: number;
  };
  type: 'function' | 'method' | 'arrow' | 'async'; // 函数类型
}
```

### ParameterInfo 接口

```typescript
interface ParameterInfo {
  name: string;          // 参数名
  type: string;          // 参数类型
  defaultValue?: any;    // 默认值
  isOptional: boolean;   // 是否可选
  description?: string;  // 参数描述（暂未实现）
}
```

## 测试

运行测试脚本验证功能：

```javascript
// 在浏览器控制台中运行
// 加载测试脚本后，可以使用以下命令进行测试

// 基础测试
window.testLynxDebugPanel.basic();

// 高级测试
window.testLynxDebugPanel.advanced();

// 错误情况测试
window.testLynxDebugPanel.error();

// 流式数据测试
window.testLynxDebugPanel.streaming();

// 自定义测试
window.testLynxDebugPanel.custom(yourCode, rawData, parsedData);
```

## 导出功能

点击 `💾 Export` 按钮可以导出包含所有新增信息的详细调试报告，报告包含：

- 系统统计信息
- 代码大小分析
- 函数参数分析
- 原始和解析数据样本
- 历史调试数据

## 注意事项

1. **性能考虑**: 函数参数分析仅在代码小于 50KB 时执行，避免影响性能
2. **数据限制**: 原始和解析数据在显示时会截断到合理长度
3. **兼容性**: 新功能向后兼容，不影响现有的调试数据发送
4. **内存管理**: 调试面板最多保留最近 20 条调试记录

## 故障排除

如果新功能不显示：

1. 确保已启用高级模式（🔬 按钮为绿色）
2. 确保发送调试数据时包含了代码内容参数
3. 检查浏览器控制台是否有错误信息
4. 尝试刷新页面重新加载组件
