# React数据流最佳实践指南

## 概述

本文档定义了项目中React数据流的最佳实践和规范，以确保代码的可维护性、性能和稳定性。正确理解和应用React数据流原则是构建高质量应用的基础。

## 核心原则

### 1. 单向数据流

React的基础是单向数据流，数据从父组件流向子组件。这意味着：

- 父组件通过props将数据传递给子组件
- 子组件不能直接修改父组件的状态
- 子组件通过回调函数通知父组件状态变化

```tsx
// ✅ 单向数据流示例
function Parent() {
  const [count, setCount] = useState(0);
  
  return (
    <Child 
      count={count} 
      onIncrement={() => setCount(count + 1)} 
    />
  );
}

function Child({ count, onIncrement }) {
  return (
    <div>
      <div>Count: {count}</div>
      <button onClick={onIncrement}>Increment</button>
    </div>
  );
}
```

### 2. 声明式UI

React采用声明式而非命令式编程范式，UI应该是状态的函数：`UI = f(state)`。

```tsx
// ✅ 声明式UI - 基于状态渲染
function TabComponent() {
  const [activeTab, setActiveTab] = useState('home');
  
  return (
    <div>
      <nav>
        <button onClick={() => setActiveTab('home')}>Home</button>
        <button onClick={() => setActiveTab('profile')}>Profile</button>
      </nav>
      
      {activeTab === 'home' && <HomeContent />}
      {activeTab === 'profile' && <ProfileContent />}
    </div>
  );
}

// ❌ 命令式UI - 直接操作DOM
function BadTabComponent() {
  const [activeTab, setActiveTab] = useState('home');
  
  const showHome = () => {
    setActiveTab('home');
    document.getElementById('home').style.display = 'block';
    document.getElementById('profile').style.display = 'none';
  };
  
  const showProfile = () => {
    setActiveTab('profile');
    document.getElementById('home').style.display = 'none';
    document.getElementById('profile').style.display = 'block';
  };
  
  return (
    <div>
      <nav>
        <button onClick={showHome}>Home</button>
        <button onClick={showProfile}>Profile</button>
      </nav>
      
      <div id="home">Home Content</div>
      <div id="profile" style={{ display: 'none' }}>Profile Content</div>
    </div>
  );
}
```

### 3. 状态提升

共享状态应提升到最近的共同父组件：

```tsx
// ✅ 状态提升示例
function ParentWithSharedState() {
  const [sharedData, setSharedData] = useState('');
  
  return (
    <>
      <ChildA data={sharedData} updateData={setSharedData} />
      <ChildB data={sharedData} />
    </>
  );
}

// ❌ 错误：使用全局变量共享状态
let globalSharedData = ''; // 全局变量，不要这样做！

function BadChildA() {
  globalSharedData = 'updated'; // 直接修改全局变量
  // ...
}

function BadChildB() {
  console.log(globalSharedData); // 读取全局变量
  // ...
}
```

## 状态管理策略

根据状态的范围和复杂度，选择合适的管理策略：

### 1. 组件本地状态 (useState)

适用于：
- 组件内部私有状态
- 不需要共享的简单状态
- UI控制状态

```tsx
function Counter() {
  // 本地状态示例
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  
  return (
    <>
      {isVisible && (
        <div>
          <span>Count: {count}</span>
          <button onClick={() => setCount(count + 1)}>Increment</button>
        </div>
      )}
      <button onClick={() => setIsVisible(!isVisible)}>
        {isVisible ? 'Hide' : 'Show'}
      </button>
    </>
  );
}
```

### 2. 复杂组件状态 (useReducer)

适用于：
- 复杂的状态逻辑
- 多个子值的状态
- 状态转换较复杂的场景

```tsx
// 定义reducer
function todosReducer(state, action) {
  switch (action.type) {
    case 'ADD_TODO':
      return [...state, { id: Date.now(), text: action.text, completed: false }];
    case 'TOGGLE_TODO':
      return state.map(todo =>
        todo.id === action.id ? { ...todo, completed: !todo.completed } : todo
      );
    default:
      return state;
  }
}

function TodoApp() {
  // 使用useReducer管理复杂状态
  const [todos, dispatch] = useReducer(todosReducer, []);
  
  const addTodo = (text) => {
    dispatch({ type: 'ADD_TODO', text });
  };
  
  const toggleTodo = (id) => {
    dispatch({ type: 'TOGGLE_TODO', id });
  };
  
  return (
    <div>
      <TodoForm onAdd={addTodo} />
      <TodoList todos={todos} onToggle={toggleTodo} />
    </div>
  );
}
```

### 3. 跨组件状态共享 (Context)

适用于：
- 全局主题、用户信息、语言等
- 跨多层组件的数据
- 避免过度使用props传递

```tsx
// 创建Context
const ThemeContext = createContext('light');

// 提供Context
function App() {
  const [theme, setTheme] = useState('light');
  
  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      <Main />
    </ThemeContext.Provider>
  );
}

// 消费Context
function ThemedButton() {
  const { theme, setTheme } = useContext(ThemeContext);
  
  return (
    <button 
      style={{ background: theme === 'light' ? 'white' : 'black' }}
      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
    >
      Toggle Theme
    </button>
  );
}
```

## 数据流错误模式

### 1. 直接操作DOM (禁止)

直接操作DOM违反React数据流原则，会导致React状态和实际DOM不一致。

```tsx
// ❌ 错误：直接操作DOM
function DirectDOMManipulation() {
  const toggleVisibility = () => {
    // 直接操作DOM，禁止！
    const element = document.querySelector('.target');
    if (element) {
      element.style.display = element.style.display === 'none' ? 'block' : 'none';
    }
  };
  
  return (
    <>
      <div className="target">Target Content</div>
      <button onClick={toggleVisibility}>Toggle</button>
    </>
  );
}

// ✅ 正确：使用状态控制可见性
function StateBasedVisibility() {
  const [isVisible, setIsVisible] = useState(true);
  
  return (
    <>
      {isVisible && <div>Target Content</div>}
      <button onClick={() => setIsVisible(!isVisible)}>Toggle</button>
    </>
  );
}
```

### 2. 使用全局变量

使用全局变量破坏React的数据流，导致难以追踪的bug。

```tsx
// ❌ 错误：使用全局变量
let globalCounter = 0; // 全局变量

function GlobalVariableComponent() {
  const incrementGlobal = () => {
    globalCounter++; // 修改全局变量
    // 需要手动强制刷新
    forceRender();
  };
  
  return (
    <div>
      <span>Global Count: {globalCounter}</span>
      <button onClick={incrementGlobal}>Increment</button>
    </div>
  );
}

// ✅ 正确：使用状态
function StateBasedCounter() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <span>Count: {count}</span>
      <button onClick={() => setCount(count + 1)}>Increment</button>
    </div>
  );
}
```

### 3. 强制刷新

使用`forceUpdate`或`useState`技巧强制刷新组件是反模式，应使用状态驱动视图。

```tsx
// ❌ 错误：使用强制刷新
function ForceRenderComponent() {
  const [, forceRender] = useState({});
  
  const handleClick = () => {
    // 修改外部数据
    someExternalData = 'new value';
    // 强制刷新
    forceRender({});
  };
  
  return (
    <div>
      <span>{someExternalData}</span>
      <button onClick={handleClick}>Update External Data</button>
    </div>
  );
}

// ✅ 正确：使用状态
function StateBasedComponent() {
  const [data, setData] = useState('initial value');
  
  return (
    <div>
      <span>{data}</span>
      <button onClick={() => setData('new value')}>Update Data</button>
    </div>
  );
}
```

### 4. 不受控组件滥用

在React中，受控组件是首选模式，不受控组件应只在特定场景使用。

```tsx
// ❌ 错误：不必要的不受控组件
function UncontrolledInput() {
  const inputRef = useRef(null);
  
  const handleSubmit = () => {
    // 从DOM读取值
    console.log(inputRef.current.value);
  };
  
  return (
    <div>
      <input ref={inputRef} defaultValue="" />
      <button onClick={handleSubmit}>Submit</button>
    </div>
  );
}

// ✅ 正确：受控组件
function ControlledInput() {
  const [value, setValue] = useState('');
  
  const handleSubmit = () => {
    // 使用状态中的值
    console.log(value);
  };
  
  return (
    <div>
      <input 
        value={value} 
        onChange={e => setValue(e.target.value)} 
      />
      <button onClick={handleSubmit}>Submit</button>
    </div>
  );
}
```

## 数据流与组件设计

### 1. 容器组件与展示组件分离

将数据逻辑与UI展示分离，提高可复用性：

```tsx
// 容器组件：负责数据逻辑
function UserListContainer() {
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    setIsLoading(true);
    fetchUsers()
      .then(data => {
        setUsers(data);
        setIsLoading(false);
      })
      .catch(err => {
        setError(err);
        setIsLoading(false);
      });
  }, []);
  
  return (
    <UserList 
      users={users} 
      isLoading={isLoading} 
      error={error} 
    />
  );
}

// 展示组件：纯UI，不关心数据来源
function UserList({ users, isLoading, error }) {
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <ul>
      {users.map(user => (
        <li key={user.id}>{user.name}</li>
      ))}
    </ul>
  );
}
```

### 2. 组合模式

使用组合而非继承构建组件层次结构：

```tsx
// ✅ 组合模式示例
function Dialog({ title, children, footerContent }) {
  return (
    <div className="dialog">
      <header className="dialog-header">
        <h2>{title}</h2>
      </header>
      <div className="dialog-body">
        {children}
      </div>
      <footer className="dialog-footer">
        {footerContent}
      </footer>
    </div>
  );
}

// 使用组合模式
function LoginDialog() {
  return (
    <Dialog 
      title="Login" 
      footerContent={<button>Close</button>}
    >
      <LoginForm />
    </Dialog>
  );
}
```

## 最佳实践总结

1. **遵循单向数据流**：状态向下流，事件向上传递
2. **状态即单一数据源**：UI应该是状态的纯函数
3. **禁止直接操作DOM**：所有UI变化应通过状态更新实现
4. **提升共享状态**：共享状态应提升到最近的共同父组件
5. **使用Context管理全局状态**：避免prop drilling
6. **避免强制刷新**：正确管理状态，不需要强制重渲染
7. **分离关注点**：容器组件处理数据，展示组件渲染UI
8. **组合优于继承**：使用组合模式构建组件层次结构

## 推荐工具与模式

- **React DevTools**：调试React组件和性能
- **状态管理库**：Context + useReducer是标准方案，复杂应用可考虑Redux或Zustand
- **Immer**：简化不可变状态更新
- **解构传递props**：使用`{...props}`传递剩余props
- **条件渲染**：使用三元运算符、&&或提取条件函数处理复杂条件

## 结语

理解并正确实践React数据流原则是构建高质量React应用的基础。禁止直接操作DOM，避免使用全局变量，使用状态驱动UI，这些都是保证应用稳定性和可维护性的关键实践。 