---
status: draft
version: 1.1
last_updated: 2023-10-31
owner: refactor_team
---

# 会话列表回溯功能

## 功能概述

会话列表回溯功能允许用户查看、搜索和重新激活历史生成会话，提高了代码生成的连续性和可追溯性。用户可以在不同的历史会话之间切换，查看之前生成的代码，并继续在特定会话上进行后续开发。该功能适用于 Web 代码生成和 Lynx 代码生成两种场景。

## 用户交互流程

### 基本流程

1. 用户在代码生成界面看到一个"历史会话"按钮
2. 点击按钮后，弹出历史会话列表面板
3. 用户可以看到按时间排序的历史会话列表，包含以下信息：
   - 会话创建时间
   - 会话ID（可选显示）
   - 会话类型（Web/Lynx）
   - 代码片段预览（前几行）
   - 代码长度信息
4. 用户可以通过点击列表项查看详细信息
5. 用户可以通过"激活"按钮切换到选定的历史会话
6. 切换后，编辑器会加载该会话的代码，并允许用户继续工作

### 进阶交互

1. **搜索功能**：用户可以通过关键词搜索历史会话中的代码内容
2. **会话标记**：用户可以为重要会话添加标记或备注
3. **会话比较**：用户可以选择两个会话进行代码差异比较
4. **会话合并**：用户可以从多个历史会话中选择代码片段合并

## 技术实现方案

### 数据存储

1. **混合存储策略**：
   - **当前/最近会话**：使用 LocalStorage 存储，确保快速访问
   - **历史会话**：使用 IndexedDB 存储，支持大量数据和复杂查询

2. **IndexedDB 数据库设计**：
   ```typescript
   interface SessionInfo {
     id: string;                 // 会话唯一标识
     type: 'web' | 'lynx';       // 会话类型
     code: string;               // 生成的代码
     timestamp: number;          // 创建时间戳
     lastActiveTime: number;     // 最后活跃时间
     tags?: string[];            // 用户标记（可选）
     notes?: string;             // 用户备注（可选）
     prompt?: string;            // 原始提示（可选）
   }
   
   // 数据库结构
   const DB_NAME = 'SessionHistoryDB';
   const DB_VERSION = 1;
   const SESSIONS_STORE = 'sessions';
   ```

3. **LocalStorage 键值**：
   - 保持现有的键，用于存储当前会话：
   ```typescript
   // 当前Web会话
   WEB_CODE: 'generatedWebCode',
   WEB_TIMESTAMP: 'webCodeTimestamp',
   WEB_SESSION_ID: 'webSessionId',
   
   // 当前Lynx会话
   LYNX_CODE: 'generatedLynxCode',
   LYNX_TIMESTAMP: 'lynxCodeTimestamp',
   LYNX_SESSION_ID: 'lynxSessionId',
   ```

4. **存储同步机制**：
   - 新会话创建时，同时更新 LocalStorage 和 IndexedDB
   - 切换历史会话时，从 IndexedDB 加载并更新到 LocalStorage
   - 会话完成时，确保保存到 IndexedDB 用于长期存储

### 核心组件

1. **SessionDBManager**：管理 IndexedDB 操作的核心类
   ```typescript
   class SessionDBManager {
     private db: IDBDatabase | null = null;
     
     // 初始化数据库
     async init(): Promise<void>;
     
     // 创建/更新会话
     async saveSession(session: SessionInfo): Promise<string>;
     
     // 获取会话列表
     async getSessionList(type?: 'web' | 'lynx'): Promise<SessionInfo[]>;
     
     // 获取特定会话
     async getSession(id: string): Promise<SessionInfo | null>;
     
     // 更新会话
     async updateSession(id: string, updates: Partial<SessionInfo>): Promise<boolean>;
     
     // 删除会话
     async deleteSession(id: string): Promise<boolean>;
     
     // 搜索会话
     async searchSessions(query: string, type?: 'web' | 'lynx'): Promise<SessionInfo[]>;
     
     // 清理旧会话
     async cleanupOldSessions(maxAgeDays: number, keepCount: number): Promise<number>;
   }
   ```

2. **SessionManager**：统一管理 localStorage 和 IndexedDB 存储
   ```typescript
   class SessionManager {
     private dbManager: SessionDBManager;
     
     constructor() {
       this.dbManager = new SessionDBManager();
       this.dbManager.init().catch(console.error);
     }
     
     // 创建新会话 - 同时保存到 localStorage 和 IndexedDB
     async createSession(type: 'web' | 'lynx', code: string, prompt?: string): Promise<string>;
     
     // 获取会话列表 - 从 IndexedDB 获取
     async getSessionList(type?: 'web' | 'lynx'): Promise<SessionInfo[]>;
     
     // 获取当前会话 - 优先从 localStorage 获取
     getCurrentSession(type: 'web' | 'lynx'): SessionInfo | null;
     
     // 激活历史会话 - 从 IndexedDB 加载到 localStorage
     async activateSession(id: string): Promise<boolean>;
     
     // 其他方法...
   }
   ```

3. **SessionHistoryPanel**：会话历史列表UI组件
4. **SessionDetailView**：会话详情UI组件
5. **SessionCompareView**：会话比较UI组件

### 集成方案

1. **与现有RPC集成**：
   - 在 WebRPCCore 和 LynxRPCCore 中添加会话管理接口
   - 每次生成代码时自动创建/更新会话信息到 LocalStorage（当前）和 IndexedDB（历史）
   - 在上下文中维护当前活跃会话ID

2. **存储接口**：
   ```typescript
   // 在 WebRPCCore.ts 和 LynxRPCCore.ts 中添加
   
   // 保存会话到 localStorage 和 IndexedDB
   async saveSession(code: string, sessionId?: string): Promise<string> {
     // 生成或使用提供的会话ID
     const id = sessionId || generateSessionId();
     
     // 创建会话信息对象
     const sessionInfo: SessionInfo = {
       id,
       type: this.type, // 'web' 或 'lynx'
       code,
       timestamp: Date.now(),
       lastActiveTime: Date.now()
     };
     
     // 1. 保存到 localStorage (当前会话)
     const storageKey = this.type === 'web' ? LOCAL_STORAGE_KEYS.WEB_CODE : LOCAL_STORAGE_KEYS.LYNX_CODE;
     const timestampKey = this.type === 'web' ? LOCAL_STORAGE_KEYS.WEB_TIMESTAMP : LOCAL_STORAGE_KEYS.LYNX_TIMESTAMP;
     const sessionIdKey = this.type === 'web' ? LOCAL_STORAGE_KEYS.WEB_SESSION_ID : LOCAL_STORAGE_KEYS.LYNX_SESSION_ID;
     
     localStorage.setItem(storageKey, code);
     localStorage.setItem(timestampKey, String(sessionInfo.timestamp));
     localStorage.setItem(sessionIdKey, id);
     
     // 2. 保存到 IndexedDB (历史会话)
     try {
       await SessionManager.getInstance().dbManager.saveSession(sessionInfo);
     } catch (error) {
       console.error('保存会话到IndexedDB失败:', error);
       // 失败时不影响主要功能继续运行
     }
     
     return id;
   }
   
   // 从 localStorage 或 IndexedDB 加载会话
   async loadSession(sessionId: string): Promise<string | null> {
     // 检查是否是当前会话
     const currentSessionIdKey = this.type === 'web' ? LOCAL_STORAGE_KEYS.WEB_SESSION_ID : LOCAL_STORAGE_KEYS.LYNX_SESSION_ID;
     const currentSessionId = localStorage.getItem(currentSessionIdKey);
     
     if (sessionId === currentSessionId) {
       // 从 localStorage 加载当前会话
       const storageKey = this.type === 'web' ? LOCAL_STORAGE_KEYS.WEB_CODE : LOCAL_STORAGE_KEYS.LYNX_CODE;
       return localStorage.getItem(storageKey);
     } else {
       // 从 IndexedDB 加载历史会话
       try {
         const session = await SessionManager.getInstance().dbManager.getSession(sessionId);
         if (session) {
           // 更新会话的最后活跃时间
           await SessionManager.getInstance().dbManager.updateSession(sessionId, {
             lastActiveTime: Date.now()
           });
           return session.code;
         }
       } catch (error) {
         console.error('从IndexedDB加载会话失败:', error);
       }
       return null;
     }
   }
   ```

## 数据流说明

1. **创建会话流程**：
   - 用户提交代码生成请求
   - RPC服务生成代码
   - 代码生成完成后:
     1. 保存到 localStorage 作为当前会话
     2. 同时保存到 IndexedDB 作为历史记录
   - 将会话ID存储在上下文中

2. **切换会话流程**：
   - 用户从历史列表选择会话
   - 系统从 IndexedDB 加载选定会话
   - 将加载的会话更新到 localStorage 和上下文状态
   - 更新编辑器内容并设置当前活跃会话ID

3. **会话管理流程**：
   - 系统定期检查 IndexedDB 存储使用情况
   - 根据配置自动清理过期或不常用会话
   - 提供用户手动管理界面

## UI组件设计

### 会话列表面板

```jsx
<SessionHistoryPanel
  sessions={sessions}
  activeSessionId={activeSessionId}
  onSelect={handleSessionSelect}
  onActivate={handleSessionActivate}
  onDelete={handleSessionDelete}
  onSearch={handleSearch}
/>
```

### 会话详情视图

```jsx
<SessionDetailView
  session={selectedSession}
  onActivate={handleSessionActivate}
  onEdit={handleSessionEdit}
  onExport={handleSessionExport}
/>
```

### 会话比较视图

```jsx
<SessionCompareView
  sourceSession={sourceSession}
  targetSession={targetSession}
  onApplyChanges={handleApplyChanges}
/>
```

## 可行性和优势

1. **可行性**：
   - 可以基于现有的 RPC 架构和数据流进行扩展
   - IndexedDB 已广泛支持，适合存储大量代码历史
   - 保留 localStorage 存储当前会话，确保兼容性和性能

2. **优势**：
   - 突破 localStorage 的存储限制，支持更多历史会话
   - 提供更强大的查询和索引能力
   - 异步操作不阻塞主线程，提升性能
   - 保持与现有代码的兼容性，平滑过渡

## 实施计划

1. **阶段一**：基础功能实现
   - 实现 SessionDBManager 用于 IndexedDB 操作
   - 实现 SessionManager 整合 localStorage 和 IndexedDB
   - 添加基本的会话列表UI
   - 集成到 WebRPCCore 和 LynxRPCCore

2. **阶段二**：高级功能开发
   - 实现基于 IndexedDB 索引的高效搜索和过滤
   - 添加会话比较功能
   - 开发会话标记和注释功能

3. **阶段三**：优化和扩展
   - 性能优化和存储管理策略
   - 添加导出/导入功能
   - 用户体验改进和测试 