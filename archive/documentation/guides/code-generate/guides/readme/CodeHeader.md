# CodeHeader 组件

---
status: approved
version: 1.1
last_updated: 2023-05-28
owner: UI团队
---

## 概述

CodeHeader 组件是代码生成页面的顶部导航组件，提供代码类型切换、预览控制和操作按钮功能。该组件注重简洁性和功能性，为用户提供必要的操作而不引入视觉干扰。

## 核心功能

1. **代码类型切换** - 提供Web代码和Lynx代码的标签页切换
2. **移动预览切换** - 控制移动预览模式的开启和关闭
3. **Playground打开** - 在Lynx代码生成完成后提供打开Playground按钮
4. **链接复制** - 复制Playground链接功能
5. **视图刷新** - 刷新当前视图的功能

## 组件设计

组件设计遵循简洁美学原则，遵循"少即是多"的设计理念：

- **无冗余文本** - 移除所有非必要的文本和标题
- **直观图标** - 使用图标代替文本，减少认知负担
- **简洁标签** - 标签文本简化为最小必要内容（如"Web"而非"Web 代码"）
- **功能聚焦** - 所有UI元素都有明确的功能目的
- **状态反馈** - 提供适当的状态反馈（如代码生成进度）

## 代码结构

```jsx
<div className="code-header">
  <div className="tab-container">
    <Tabs
      activeKey={effectiveActiveTab}
      onChange={handleTabChange}
      tabBarExtraContent={
        <div className="tab-extra-content">
          {/* 移动预览按钮 */}
          {/* Playground按钮 */}
          {/* 复制链接按钮 */}
          {/* 刷新视图按钮 */}
        </div>
      }
    >
      <TabPane tab="Web" itemKey="web" />
      <TabPane tab="Lynx" itemKey="lynx" />
      <TabPane tab="预览" itemKey="mobile" />
    </Tabs>
  </div>
  {/* 状态指示器 */}
</div>
```

## 状态管理

CodeHeader 使用多个 Context 管理状态：

1. **WebRPCContext** - 获取Web代码生成状态
2. **LynxContext** - 获取Lynx代码生成状态
3. **TabStateContext** - 管理活动标签状态
4. **UIStateContext** - 控制UI布局状态

## 最近更新

### 2023-05-28: UI精简优化

- 删除了标题区域和相关状态变量
- 移除了标题编辑功能和相关逻辑
- 简化了标签文本内容，从"Web 代码"简化为"Web"等
- 移除了Lynx代码完成通知组件
- 精简了相关CSS样式

这些修改显著提升了界面的简洁性，减少了不必要的文本信息，让用户可以更专注于代码内容本身。

## 用法示例

```jsx
// 在Preview组件中使用CodeHeader
<CodeHeader lynxPlaygroundUrl={lynxState.playgroundUrl || undefined} />
```

## 交互说明

1. **标签切换** - 点击标签切换代码视图
2. **移动预览** - 点击移动设备图标切换到移动预览模式
3. **Playground** - Lynx代码生成完成后，可点击"Playground"按钮在新窗口打开
4. **复制链接** - 点击复制图标复制Playground链接
5. **刷新视图** - 点击刷新图标重新加载当前视图

## 注意事项

- CodeHeader组件依赖多个Context，确保在所有必要Provider内部使用
- 样式依赖CodeHeader.scss文件
- Playground按钮仅在有Lynx代码且生成完成时可用 