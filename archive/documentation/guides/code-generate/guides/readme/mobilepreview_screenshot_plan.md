# MobilePreview 截图存储方案分析

## 需求描述
历史会话目前只保存 share URL 和 playground URL，需要增加 MobilePreview 的截图功能，使得用户可以在历史会话中查看移动端预览的效果截图。

## 方案对比

### 方案1: Canvas 截图方案

**实现原理：**
使用 html2canvas 库将 MobilePreview 组件渲染为 Canvas，然后导出为图片数据（Data URL）并保存。

**实现步骤：**
1. 在 MobilePreview 组件中添加截图功能，使用 html2canvas 将整个组件内容转换为 Canvas
2. 将 Canvas 导出为 base64 编码的图片（Data URL）
3. 将这个 Data URL 存储到 Context 和 localStorage 中，与 share URL 和 playground URL 一起保存

**优点：**
- 完全前端实现，无需额外的网络请求和服务端支持
- 即时生成，无需等待上传完成
- 减少网络带宽占用，无需额外的网络请求
- 不依赖外部服务，不会因为上传服务不可用而失败
- 本地存储后可以立即使用，加载快速

**缺点：**
- base64 编码的图片会占用较大的存储空间，localStorage 有容量限制（通常为 5MB）
- 对于复杂页面，html2canvas 可能无法完美捕获所有渲染细节，特别是对于 iframe 内的内容
- 性能消耗，特别是对于较复杂的预览内容，浏览器性能较差的设备上可能导致卡顿
- 如果预览内容包含跨域资源，可能会因为 CORS 限制而无法正确捕获

### 方案2: CDN 上传方案

**实现原理：**
与当前代码中的 uploadFilesToCDN 类似，但上传的是截图而非代码文件。

**实现步骤：**
1. 在 MobilePreview 组件中添加截图功能，使用 html2canvas 将组件内容转换为 Canvas
2. 将 Canvas 导出为 Blob 对象
3. 创建 FormData，将 Blob 作为文件上传到 CDN
4. 保存返回的 CDN URL 到 Context 和 localStorage

**优点：**
- 不占用本地存储空间，只存储 URL
- 可以处理较大的图片，不受 localStorage 容量限制
- 图片通过 CDN 加载，可能会比从 localStorage 加载更快（取决于网络状况）
- 图片可以被其他环境或用户访问（如需要分享给他人）

**缺点：**
- 依赖网络请求，增加了额外的带宽消耗
- 依赖外部 CDN 服务，服务不可用时功能会失败
- 有额外的等待时间（上传完成后才能获取 URL）
- 需要处理上传错误、重试等复杂逻辑
- CDN 存储可能有成本考量，特别是大量使用时

### 方案3: 混合方案（优化存储空间）

**实现原理：**
结合 Canvas 截图的即时性和 CDN 存储的空间优势。

**实现步骤：**
1. 初始使用 Canvas 截图并保存为 Data URL，但降低图片质量或尺寸以减小存储占用
2. 同时启动后台上传任务，将完整质量的图片上传至 CDN
3. 上传完成后，用 CDN URL 替换本地存储的 Data URL

**优点：**
- 用户体验更好，截图立即可用
- 最终不占用大量本地存储空间
- 可以平衡质量与存储空间

**缺点：**
- 实现复杂度高，需要管理两种存储状态和转换逻辑
- 仍有网络依赖

### 方案4: WebP 压缩方案

**实现原理：**
使用 WebP 格式而非 PNG/JPEG 来存储截图，大幅降低存储空间需求。

**实现步骤：**
1. 使用 html2canvas 生成 Canvas
2. 使用 Canvas 的 toDataURL 方法，指定输出格式为 WebP，并设置合适的压缩质量
3. 将压缩后的 Data URL 存储在 Context 和 localStorage

**优点：**
- 仍然是纯前端实现，无网络依赖
- WebP 格式比 PNG/JPEG 节省 30-70% 存储空间
- 保持较好的图像质量

**缺点：**
- 较老的浏览器可能不支持 WebP 格式
- 仍然受 localStorage 总体容量限制

## 方案建议

考虑项目需求和复杂度，建议采用**方案1（Canvas 截图）+ WebP压缩**的组合方案：

1. 使用 html2canvas 截取 MobilePreview 组件
2. 输出为 WebP 格式（如果浏览器支持）或 JPEG 格式（质量降低到 0.7-0.8）
3. 将压缩后的 Data URL 存储到 Context 和 localStorage

这种方案可以:
- 保持简单的实现逻辑
- 没有外部依赖
- 通过使用 WebP 和压缩减少存储空间占用
- 提供即时的用户体验，无需等待上传

如果测试发现 localStorage 仍然容易超出限制，可以考虑结合后台上传方式，或限制保存的历史会话数量。

## 实施建议

1. 添加截图功能到 MobilePreview 组件，暴露一个函数如 `captureScreenshot()`
2. 在历史会话保存时触发截图并存储
3. 保存一个合适的缩略图尺寸（如 600px 宽），以平衡质量和存储空间
4. 实现一个清理机制，当历史会话过多时，删除最旧的截图数据 