---
status: approved
version: 1.0
last_updated: 2025-09-15
owner: Team
---

# Web和Lynx代码生成指南

## 1. Web与Lynx关系

在Code Generate模块中，Web代码和Lynx代码生成是并行的独立系统：

- **并行触发**: Web和Lynx代码生成可以同时触发，互不影响
- **独立数据流**: 两个系统各自维护独立的数据流和状态
- **统一管理**: 通过`CodeGenerationUnifiedContextV2`统一管理状态
- **手动转换**: 用户可以通过"转换成Lynx"按钮将Web代码转换成Lynx代码

## 2. Web代码生成

### 2.1 生成流程

1. 用户通过Chat组件输入prompt
2. `useWebRPCUnified().sendMessage`处理请求，开始流式生成
3. 统一Context更新`isWebRPCLoading`和`webCodeProgress`
4. Web代码通过批量更新写入`webCode`
5. 代码在WebCodeHighlight组件中显示
6. 生成完成后更新`isWebCodeComplete`状态和存储到localStorage

### 2.2 特殊功能

- **自动续传**: 自动检测代码是否完整，需要时执行续传
- **代码编辑**: 支持直接编辑生成的代码
- **分享功能**: 可以生成代码分享链接
- **移动预览**: 在模拟手机界面中查看Web代码效果

## 3. Lynx代码生成

### 3.1 生成流程

1. 用户点击"转换成Lynx"按钮
2. `useLynxRPCUnified().convertFromWeb`处理请求，开始转换
3. 统一Context更新`isLynxRPCLoading`和`lynxCodeProgress`
4. Lynx代码通过批量更新写入`lynxCode`
5. 代码在LynxCodeHighlight组件中显示
6. 代码完成后更新`isLynxCodeComplete`状态和存储到localStorage

### 3.2 特殊功能

- **代码/预览切换**: 可以在代码视图和预览之间切换
- **Playground集成**: 支持在内嵌Playground中预览Lynx代码
- **文件解构**: 支持自动解构和上传Lynx代码文件

## 4. 最佳实践

### 4.1 生成优质Web代码的提示技巧

- 提供明确的需求描述
- 指定目标设备和浏览器
- 明确是否使用特定框架或库
- 指定代码风格和复杂度需求

### 4.2 生成高质量Lynx代码的提示技巧

- 优先使用Canvas元素进行布局
- 指定需要的交互效果
- 明确表达性能需求
- 说明目标设备类型

## 5. 常见问题与解决方案

### 5.1 代码生成问题

- **代码不完整**: 尝试使用"重新生成"按钮
- **生成内容与预期不符**: 修改提示词，提供更明确的描述
- **生成速度慢**: 减少代码复杂度需求，分步骤生成

### 5.2 Lynx预览问题

- **预览加载失败**: 检查Lynx代码是否生成完成
- **切换视图失败**: 确认当前是否有可用的Lynx代码
- **功能在预览中不工作**: 检查代码兼容性和API使用是否正确

## 6. 参考资源

- [Web代码示例集](../guides/web/)
- [Lynx代码示例集](../guides/lynx/)
- [生成优化技巧](../guides/prompt_optimization.md)
