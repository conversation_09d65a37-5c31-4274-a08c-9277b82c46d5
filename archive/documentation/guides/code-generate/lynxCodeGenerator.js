#!/usr/bin/env node

/**
 * Lynx 和 Web 代码生成器
 *
 * 此脚本用于根据 QueryList 中的查询自动生成 Lynx 和 Web 代码，
 * 并将结果保存到相应的文件夹中。
 *
 * 使用方法：
 * 1. 安装依赖：pnpm install node-fetch dotenv fs-extra chalk readline
 * 2. 运行脚本：node lynxCodeGenerator.js
 */

const fs = require('fs-extra');
const path = require('path');
const fetch = require('node-fetch');
const chalk = require('chalk');
const readline = require('readline');
require('dotenv').config();

// 配置信息
const CONFIG = {
  // 目标文件夹
  LYNX_CODE_DIR: path.resolve(__dirname, 'lynxCode'),
  WEB_CODE_DIR: path.resolve(__dirname, 'webCode'),
  QUERY_LIST_PATH: path.resolve(__dirname, '../RPC/QueryList.md'),

  // API配置
  AI_API_URL: 'https://gen-ui.bytedance.net/agent/api/apaas/v2/chat',
  WEB_WORKFLOW_ID: 'ce03397a-001f-47de-96a2-c648f05d8668',
  LYNX_WORKFLOW_ID: 'fc02f6eb-26db-4c63-be62-483ab8abce34',

  // 模板配置
  WEB_TEMPLATE_PATH: path.resolve(__dirname, 'templates/web_template.md'),
  LYNX_TEMPLATE_PATH: path.resolve(__dirname, 'templates/lynx_template.md'),
};

// 创建命令行交互界面
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

/**
 * 主函数
 */
async function main() {
  try {
    console.log(chalk.blue('=== Lynx 和 Web 代码生成器 ==='));

    // 确保目标文件夹存在
    await fs.ensureDir(CONFIG.LYNX_CODE_DIR);
    await fs.ensureDir(CONFIG.WEB_CODE_DIR);

    // 读取查询列表
    const queries = await loadQueries();

    if (queries.length === 0) {
      console.error(chalk.red('错误: 未找到任何查询'));
      process.exit(1);
    }

    console.log(chalk.green(`成功加载 ${queries.length} 个查询`));

    // 随机选择一个查询
    const randomIndex = Math.floor(Math.random() * queries.length);
    const randomQuery = queries[randomIndex];

    console.log(chalk.yellow(`已选择随机查询: "${randomQuery}"`));

    // 询问用户是否接受此查询或手动输入
    rl.question(
      chalk.cyan('接受此查询? (Y/n), 或输入一个新查询: '),
      async answer => {
        let finalQuery = randomQuery;

        if (answer.toLowerCase() === 'n') {
          rl.question(chalk.cyan('请输入您的查询: '), async customQuery => {
            if (customQuery.trim()) {
              finalQuery = customQuery.trim();
            }
            await processQuery(finalQuery);
            rl.close();
          });
        } else if (answer.trim() && answer.toLowerCase() !== 'y') {
          finalQuery = answer.trim();
          await processQuery(finalQuery);
          rl.close();
        } else {
          await processQuery(finalQuery);
          rl.close();
        }
      },
    );
  } catch (error) {
    console.error(chalk.red('发生错误:'), error);
    process.exit(1);
  }
}

/**
 * 处理指定的查询
 * @param {string} query 要处理的查询
 */
async function processQuery(query) {
  console.log(chalk.blue(`\n开始处理查询: "${query}"`));

  // 文件名处理
  const fileName = `${query}.md`;
  const lynxFilePath = path.join(CONFIG.LYNX_CODE_DIR, fileName);
  const webFilePath = path.join(CONFIG.WEB_CODE_DIR, fileName);

  // 检查文件是否已存在
  const lynxExists = await fs.pathExists(lynxFilePath);
  const webExists = await fs.pathExists(webFilePath);

  if (lynxExists && webExists) {
    console.log(chalk.yellow('警告: 此查询的代码文件已存在'));

    const confirmOverwrite = await askQuestion('是否覆盖已有文件? (y/N): ');
    if (confirmOverwrite.toLowerCase() !== 'y') {
      console.log(chalk.blue('操作已取消'));
      return;
    }
  }

  // 创建进度指示器
  console.log(chalk.blue('开始生成代码...'));

  try {
    // 并行生成代码
    const [lynxCode, webCode] = await Promise.all([
      generateLynxCode(query),
      generateWebCode(query),
    ]);

    // 准备文件内容
    const lynxFileContent = formatLynxMarkdown(query, lynxCode);
    const webFileContent = formatWebMarkdown(query, webCode);

    // 保存文件
    await fs.writeFile(lynxFilePath, lynxFileContent);
    await fs.writeFile(webFilePath, webFileContent);

    console.log(chalk.green('\n✅ 代码生成完成!'));
    console.log(chalk.green(`Lynx代码已保存至: ${lynxFilePath}`));
    console.log(chalk.green(`Web代码已保存至: ${webFilePath}`));
  } catch (error) {
    console.error(chalk.red('生成代码时发生错误:'), error);
  }
}

/**
 * 读取查询列表
 * @returns {Promise<string[]>} 查询列表
 */
async function loadQueries() {
  try {
    const content = await fs.readFile(CONFIG.QUERY_LIST_PATH, 'utf-8');
    const lines = content
      .split('\n')
      .filter(line => line.trim() && line.trim() !== 'query');
    return lines;
  } catch (error) {
    console.error(chalk.red('读取查询列表失败:'), error);
    return [];
  }
}

/**
 * 生成Lynx代码
 * @param {string} query 查询内容
 * @returns {Promise<string>} 生成的Lynx代码
 */
async function generateLynxCode(query) {
  console.log(chalk.blue('生成Lynx代码...'));

  try {
    const response = await fetch(CONFIG.AI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'aws_sdk_claude4_opus',
        workflow_id: CONFIG.LYNX_WORKFLOW_ID,
        messages: [
          {
            role: 'user',
            content: query,
          },
        ],
      }),
    });

    if (!response.ok) {
      throw new Error(`API响应错误: ${response.status} ${response.statusText}`);
    }

    const result = await response.text();
    console.log(chalk.green('Lynx代码生成完成'));
    return result;
  } catch (error) {
    console.error(chalk.red('生成Lynx代码失败:'), error);
    return '生成失败，请重试';
  }
}

/**
 * 生成Web代码
 * @param {string} query 查询内容
 * @returns {Promise<string>} 生成的Web代码
 */
async function generateWebCode(query) {
  console.log(chalk.blue('生成Web代码...'));

  try {
    const response = await fetch(CONFIG.AI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'aws_sdk_claude4_opus',
        workflow_id: CONFIG.WEB_WORKFLOW_ID,
        messages: [
          {
            role: 'user',
            content: query,
          },
        ],
      }),
    });

    if (!response.ok) {
      throw new Error(`API响应错误: ${response.status} ${response.statusText}`);
    }

    const result = await response.text();
    console.log(chalk.green('Web代码生成完成'));
    return result;
  } catch (error) {
    console.error(chalk.red('生成Web代码失败:'), error);
    return '生成失败，请重试';
  }
}

/**
 * 格式化Lynx代码为Markdown文档
 * @param {string} query 查询内容
 * @param {string} code 生成的代码
 * @returns {string} 格式化后的Markdown内容
 */
function formatLynxMarkdown(query, code) {
  return `# ${query} - Lynx代码示例

## 查询内容
\`\`\`
${query}
\`\`\`

## 生成的Lynx代码

\`\`\`
${code}
\`\`\`

## 代码说明

此Lynx代码由AI自动生成，基于查询 "${query}"。请根据需要进行修改和优化。
`;
}

/**
 * 格式化Web代码为Markdown文档
 * @param {string} query 查询内容
 * @param {string} code 生成的代码
 * @returns {string} 格式化后的Markdown内容
 */
function formatWebMarkdown(query, code) {
  return `# ${query} - Web代码示例

## 查询内容
\`\`\`
${query}
\`\`\`

## 生成的Web代码

\`\`\`html
${code}
\`\`\`

## 代码说明

此Web代码由AI自动生成，基于查询 "${query}"。请根据需要进行修改和优化。
`;
}

/**
 * 封装readline.question为Promise
 * @param {string} question 问题
 * @returns {Promise<string>} 用户输入
 */
function askQuestion(question) {
  return new Promise(resolve => {
    rl.question(chalk.cyan(question), answer => {
      resolve(answer);
    });
  });
}

// 运行主函数
main();
