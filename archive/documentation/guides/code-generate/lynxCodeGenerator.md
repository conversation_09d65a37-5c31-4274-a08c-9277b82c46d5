# Lynx 和 Web 代码生成器

## 简介

`lynxCodeGenerator.js` 是一个命令行工具，用于根据 QueryList 中的查询自动生成 Lynx 和 Web 代码，并将结果保存到相应的文件夹中。该工具旨在简化开发过程，帮助开发者快速生成示例代码，为比较 Lynx 和 Web 开发提供参考。

## 功能特性

- 从 QueryList 中随机选择查询或接受自定义查询
- 并行生成 Lynx 和 Web 代码，提高效率
- 格式化生成的代码并保存为 Markdown 文件
- 交互式命令行界面，易于使用
- 彩色输出，提高可读性
- 文件覆盖保护机制

## 安装

### 前提条件

- Node.js 14.0 或更高版本
- 使用 pnpm 作为包管理器

### 安装依赖

```bash
cd src/routes/code_generate/docs
pnpm install node-fetch dotenv fs-extra chalk readline
```

## 使用方法

1. 导航到脚本所在目录：

```bash
cd src/routes/code_generate/docs
```

2. 运行脚本：

```bash
node lynxCodeGenerator.js
```

3. 按照提示操作：
   - 脚本会从 QueryList 中随机选择一个查询
   - 您可以接受该查询（按 Y 或 Enter）
   - 您可以拒绝该查询（按 N），然后输入自定义查询
   - 您也可以直接输入新查询

4. 等待代码生成完成：
   - 脚本会并行生成 Lynx 和 Web 代码
   - 生成的代码会自动保存到相应文件夹

## 目录结构

```
docs/
├── lynxCode/           # 存放生成的 Lynx 代码
├── webCode/            # 存放生成的 Web 代码
├── lynxCodeGenerator.js   # 代码生成器脚本
└── templates/          # 模板文件（可选）
    ├── web_template.md
    └── lynx_template.md
```

## 配置说明

脚本中的 `CONFIG` 对象包含以下配置项：

- `LYNX_CODE_DIR`: Lynx 代码保存目录
- `WEB_CODE_DIR`: Web 代码保存目录
- `QUERY_LIST_PATH`: QueryList 文件路径
- `AI_API_URL`: AI API 地址
- `WEB_WORKFLOW_ID`: Web 代码生成工作流 ID
- `LYNX_WORKFLOW_ID`: Lynx 代码生成工作流 ID
- `WEB_TEMPLATE_PATH`: Web 模板文件路径（可选）
- `LYNX_TEMPLATE_PATH`: Lynx 模板文件路径（可选）

## 工作流程

1. 读取 QueryList 中的查询列表
2. 随机选择一个查询或接受用户输入
3. 使用 AI API 并行生成 Lynx 和 Web 代码
4. 格式化生成的代码并保存为 Markdown 文件
5. 输出结果路径

## 示例输出

生成的 Lynx 代码文件格式如下：

```markdown
# 实现一个倒计时计时器 - Lynx代码示例

## 查询内容
```
实现一个倒计时计时器
```

## 生成的Lynx代码

```
<FILES>
<FILE path="/src/index.js">
// Lynx 代码内容
</FILE>
</FILES>
```

## 代码说明

此Lynx代码由AI自动生成，基于查询 "实现一个倒计时计时器"。请根据需要进行修改和优化。

生成的 Web 代码文件格式类似。

## 疑难解答

### 常见问题

1. **API 调用失败**
   - 检查网络连接
   - 确认 API URL 和工作流 ID 是否正确

2. **文件保存失败**
   - 检查目录权限
   - 确认文件名是否包含非法字符

3. **生成的代码格式不正确**
   - 检查 API 返回的数据格式
   - 调整格式化函数

## 贡献指南

欢迎对本工具进行改进和扩展。建议的改进方向包括：

- 添加批量生成功能
- 实现进度条显示
- 添加代码质量检查
- 提供 Web 界面

## 许可证

内部工具，仅供团队使用。