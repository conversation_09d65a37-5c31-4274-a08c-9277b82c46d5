# Lynx 和 Web 代码生成器 - 设计文档

## 1. 项目概述

Lynx 和 Web 代码生成器是一个命令行工具，用于根据 QueryList 中的查询自动生成 Lynx 和 Web 代码，并将结果保存到文档库中。该工具旨在帮助开发者快速获取各种场景下的代码示例，提高开发效率和代码质量，同时便于比较 Lynx 和 Web 开发的异同。

### 1.1 核心目标

- 自动化生成 Lynx 和 Web 代码示例，减少手动编写示例的工作量
- 提供统一的代码风格和最佳实践，确保示例代码质量
- 构建完整的代码示例库，方便开发者查阅和学习
- 创建 Lynx 和 Web 代码的对照示例，便于理解两种开发方式的差异

### 1.2 主要功能

- 从 QueryList.md 读取查询列表
- 随机选择或接受自定义查询
- 并行生成 Lynx 和 Web 代码
- 将生成的代码保存为格式化的 Markdown 文件
- 提供交互式命令行界面

## 2. 系统架构

### 2.1 整体架构

```
┌───────────────┐     ┌───────────────────┐     ┌───────────────┐
│               │     │                   │     │               │
│  QueryList.md │────▶│  lynxCodeGenerator│────▶│  生成的代码文件 │
│               │     │                   │     │               │
└───────────────┘     └───────────────────┘     └───────────────┘
                              │  ▲
                              │  │
                              ▼  │
                       ┌─────────────────┐
                       │                 │
                       │   AI API 服务   │
                       │                 │
                       └─────────────────┘
```

### 2.2 核心组件

1. **命令行界面 (CLI)**：提供用户交互界面
2. **查询处理模块**：负责读取和处理查询
3. **代码生成模块**：负责调用 AI API 生成代码
4. **文件存储模块**：负责将生成的代码保存为文件
5. **错误处理模块**：负责处理和报告错误

### 2.3 数据流

1. 从 QueryList.md 读取查询列表
2. 随机选择一个查询或接受用户输入
3. 并行调用 AI API 生成 Lynx 和 Web 代码
4. 格式化生成的代码
5. 将格式化后的代码保存为 Markdown 文件
6. 输出结果和状态信息

## 3. 详细设计

### 3.1 查询处理模块

查询处理模块负责读取 QueryList.md 文件，解析查询列表，并提供随机选择或接受用户输入的功能：

```javascript
/**
 * 读取查询列表
 * @returns {Promise<string[]>} 查询列表
 */
async function loadQueries() {
  try {
    const content = await fs.readFile(CONFIG.QUERY_LIST_PATH, 'utf-8');
    const lines = content.split('\n').filter(line => line.trim() && line.trim() !== 'query');
    return lines;
  } catch (error) {
    console.error('读取查询列表失败:', error);
    return [];
  }
}
```

随机选择查询的实现：

```javascript
// 随机选择一个查询
const randomIndex = Math.floor(Math.random() * queries.length);
const randomQuery = queries[randomIndex];
```

用户交互的实现使用 readline 模块：

```javascript
// 创建命令行交互界面
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 询问用户是否接受此查询或手动输入
rl.question('接受此查询? (Y/n), 或输入一个新查询: ', async (answer) => {
  let finalQuery = randomQuery;
  
  if (answer.toLowerCase() === 'n') {
    rl.question('请输入您的查询: ', async (customQuery) => {
      // 处理自定义查询
    });
  } else if (answer.trim() && answer.toLowerCase() !== 'y') {
    finalQuery = answer.trim();
    // 处理用户输入的查询
  } else {
    // 处理默认查询
  }
});
```

### 3.2 代码生成模块

代码生成模块负责调用 AI API 生成 Lynx 和 Web 代码：

#### Lynx 代码生成：

```javascript
/**
 * 生成Lynx代码
 * @param {string} query 查询内容
 * @returns {Promise<string>} 生成的Lynx代码
 */
async function generateLynxCode(query) {
  try {
    const response = await fetch(CONFIG.AI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'aws_sdk_claude4_opus',
        workflow_id: CONFIG.LYNX_WORKFLOW_ID,
        messages: [
          {
            role: 'user',
            content: query
          }
        ]
      }),
    });
    
    if (!response.ok) {
      throw new Error(`API响应错误: ${response.status} ${response.statusText}`);
    }
    
    const result = await response.text();
    return result;
  } catch (error) {
    console.error('生成Lynx代码失败:', error);
    return '生成失败，请重试';
  }
}
```

#### Web 代码生成：

```javascript
/**
 * 生成Web代码
 * @param {string} query 查询内容
 * @returns {Promise<string>} 生成的Web代码
 */
async function generateWebCode(query) {
  try {
    const response = await fetch(CONFIG.AI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'aws_sdk_claude4_opus',
        workflow_id: CONFIG.WEB_WORKFLOW_ID,
        messages: [
          {
            role: 'user',
            content: query
          }
        ]
      }),
    });
    
    // 同上，处理响应和错误
  } catch (error) {
    // 错误处理
  }
}
```

#### 并行处理：

```javascript
// 并行生成代码
const [lynxCode, webCode] = await Promise.all([
  generateLynxCode(query),
  generateWebCode(query)
]);
```

### 3.3 文件存储模块

文件存储模块负责将生成的代码格式化并保存为 Markdown 文件：

```javascript
/**
 * 格式化Lynx代码为Markdown文档
 * @param {string} query 查询内容
 * @param {string} code 生成的代码
 * @returns {string} 格式化后的Markdown内容
 */
function formatLynxMarkdown(query, code) {
  return `# ${query} - Lynx代码示例

## 查询内容
\`\`\`
${query}
\`\`\`

## 生成的Lynx代码

\`\`\`
${code}
\`\`\`

## 代码说明

此Lynx代码由AI自动生成，基于查询 "${query}"。请根据需要进行修改和优化。
`;
}
```

保存文件的实现：

```javascript
// 保存文件
await fs.writeFile(lynxFilePath, lynxFileContent);
await fs.writeFile(webFilePath, webFileContent);
```

### 3.4 错误处理模块

错误处理模块负责处理和报告各种错误：

```javascript
try {
  // 操作代码
} catch (error) {
  console.error(chalk.red('发生错误:'), error);
  process.exit(1);
}
```

对于文件已存在的情况，提供覆盖确认机制：

```javascript
// 检查文件是否已存在
const lynxExists = await fs.pathExists(lynxFilePath);
const webExists = await fs.pathExists(webFilePath);

if (lynxExists && webExists) {
  console.log(chalk.yellow(`警告: 此查询的代码文件已存在`));
  
  const confirmOverwrite = await askQuestion('是否覆盖已有文件? (y/N): ');
  if (confirmOverwrite.toLowerCase() !== 'y') {
    console.log(chalk.blue('操作已取消'));
    return;
  }
}
```

## 4. 实现细节

### 4.1 文件格式

生成的 Markdown 文件采用以下格式：

```markdown
# 查询内容 - Lynx/Web代码示例

## 查询内容
```
查询内容
```

## 生成的Lynx/Web代码

```
代码内容
```

## 代码说明

此代码由AI自动生成，基于查询 "查询内容"。请根据需要进行修改和优化。
```

### 4.2 命令行交互

命令行交互使用 readline 模块实现，并使用 chalk 模块提供彩色输出：

```javascript
// 封装readline.question为Promise
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(chalk.cyan(question), (answer) => {
      resolve(answer);
    });
  });
}
```

### 4.3 依赖项

脚本依赖以下 Node.js 模块：

- fs-extra：文件操作增强版
- path：路径处理
- node-fetch：HTTP 请求
- chalk：彩色命令行输出
- readline：命令行交互
- dotenv：环境变量管理

## 5. 未来扩展

### 5.1 待实现功能

1. **批量生成功能**：允许批量处理多个查询
2. **进度条显示**：使用 progress 或类似模块显示生成进度
3. **代码质量检查**：集成代码检查工具，确保生成的代码质量
4. **Web 界面**：提供 Web 界面，便于浏览和管理生成的代码
5. **模板系统**：允许自定义代码模板
6. **代码比较视图**：提供 Lynx 和 Web 代码的比较视图

### 5.2 优化方向

1. **错误重试**：添加自动重试机制，提高稳定性
2. **缓存机制**：缓存 API 响应，减少请求次数
3. **代码格式化**：集成代码格式化工具，提高代码可读性
4. **测试覆盖**：添加单元测试和集成测试
5. **文档自动生成**：自动生成 API 文档和用户指南

## 6. 结论

Lynx 和 Web 代码生成器是一个简单但强大的工具，通过自动化生成代码示例，可以大大提高开发效率和代码质量。该工具采用模块化设计，易于维护和扩展，可以根据需要添加新功能和优化现有功能。

通过比较 Lynx 和 Web 代码示例，开发者可以更好地理解两种开发方式的异同，选择最适合自己项目的技术路线。 