# Mobile模拟器Resize功能设计方案

## 需求
为移动端模拟器外侧增加resize选项，允许用户调整模拟器的边框大小，提升用户体验。

## 现状分析
当前MobilePreview组件使用固定宽高，不支持调整大小：
- 宽度固定为min(360px, 95%)
- 高度固定为min(737px, 95vh)
- 在小屏幕上已有一些响应式设计

## 实现方案

### 1. 功能概述
- 添加缩放控制按钮面板
- 支持预设尺寸（小、中、大）
- 添加调整大小手柄，支持拖拽调整

### 2. 具体实现

#### 状态管理
- 添加设备尺寸状态（宽度、高度）
- 添加当前使用的预设模式状态
- 添加是否正在调整大小的状态

#### UI组件
- 添加预设大小选择按钮组
  - 小屏(iPhone SE): 320x568
  - 中屏(默认): 360x737 
  - 大屏(iPhone Plus): 414x896
- 添加右下角调整大小的拖拽手柄
- 添加调整过程中的尺寸指示器

#### 交互设计
- 点击预设按钮立即调整到对应尺寸
- 按住调整手柄可自由拖拽调整大小
- 调整时显示当前尺寸的实时指示器
- 设置最小/最大尺寸限制，确保良好的使用体验

### 3. 技术实现细节
- 使用React的useRef和useState管理DOM引用和状态
- 使用原生事件处理拖拽功能(mousedown, mousemove, mouseup)
- 维持设备的宽高比例，确保模拟器外观一致性
- 缓存上次调整的自定义尺寸

### 4. 交互流程
1. 用户进入Mobile预览页面，默认显示中等尺寸的模拟器
2. 用户可点击预设按钮，立即切换到对应尺寸
3. 用户可拖拽右下角手柄，自由调整尺寸
4. 调整过程中显示实时尺寸提示
5. 松开鼠标后保存当前尺寸

### 5. 优势
- 提供更灵活的预览体验
- 适应不同屏幕尺寸的设备测试需求
- 保持设计的美观性和易用性

## 实现计划
1. 更新MobilePreview组件，添加尺寸调整相关状态和方法
2. 添加预设尺寸按钮组和拖拽手柄
3. 实现拖拽调整逻辑
4. 实现尺寸指示器
5. 优化交互体验和样式 