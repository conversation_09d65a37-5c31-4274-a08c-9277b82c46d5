# 页面进入时重复日志问题分析报告

## 问题概述

从截图可以看到，页面刚进入时出现大量重复的日志条目，主要表现为：
- `[useLynxStateOptimized] 状态变化检测` 重复出现
- `[LynxCodeHighlight] 从localStorage恢复代码` 重复出现  
- `[UnifiedContext] 从localStorage恢复Web/Lynx代码` 重复出现
- 相同的日志内容在短时间内重复打印多次

## 根本原因分析

### 1. 组件重复挂载问题

**问题位置**: `src/routes/code_generate/contexts/CodeGenerationUnifiedContextV2.tsx:533-573`

```typescript
useEffect(() => {
  const initializeFromStorage = async () => {
    // 存储初始化逻辑
    const webCode = WebCodeStorage.getCode();
    if (webCode && webCode.length > 0) {
      logger.info(`[UnifiedContext] 从localStorage恢复Web代码，长度: ${webCode.length}`);
      // ...
    }
  };
  
  const timer = setTimeout(initializeFromStorage, 100);
  return () => clearTimeout(timer);
}, []); // 空依赖数组
```

**问题分析**:
- 该 `useEffect` 在每次组件挂载时都会执行
- 如果组件因为某种原因重复挂载，就会导致重复的初始化日志
- 延迟100ms的设计可能导致时序问题

### 2. 状态选择器过度触发

**问题位置**: `src/routes/code_generate/contexts/CodeGenerationUnifiedContextV2.tsx:619-654`

```typescript
export const useLynxStateOptimized = () => {
  return useSelector(
    (state) => state.lynx,
    (a, b) => {
      // 状态比较逻辑
      const isEqual = (/* 复杂的比较逻辑 */);
      
      // 🚨 调试：记录状态比较结果
      if (!isEqual) {
        console.debug('[useLynxStateOptimized] 状态变化检测:', {
          // 详细的变化信息
        });
      }
      
      return isEqual;
    }
  );
};
```

**问题分析**:
- 状态比较函数在每次状态变化时都会执行
- 调试日志在开发环境下会频繁打印
- 如果状态频繁变化，会导致大量重复日志

### 3. 自定义事件重复触发

**问题位置**: `src/routes/code_generate/contexts/CodeGenerationUnifiedContextV2.tsx:776-810`

**✅ 问题已解决**: 所有自定义事件已被移除，使用React Context回调机制替代

详细修复信息请参考: `custom-events-removal-summary.md`

### 4. localStorage 恢复逻辑重复执行

**问题位置**: `src/routes/code_generate/components/LynxCodeHighlight.tsx:1548-1584`

```typescript
// 紧急恢复机制：从localStorage恢复代码
const storedCode = localStorage.getItem('lynx-code-backup');
if (storedCode && storedCode.length > 100) {
  const isValidCode = /* 验证逻辑 */;
  
  if (isValidCode) {
    logger.info(
      `[LynxCodeHighlight] 🔄 从localStorage紧急恢复代码: ${storedCode.length}字符`,
    );
    
    // 立即触发Context更新
    if (updateLynxCode) {
      setTimeout(() => {
        updateLynxCode(storedCode, Date.now());
      }, 0);
    }
  }
}
```

**问题分析**:
- 多个组件都有localStorage恢复逻辑
- 如果多个组件同时尝试恢复，会产生重复日志
- 恢复逻辑没有去重机制

## 具体触发场景

### 场景1: 页面首次加载
1. `CodeGenerationUnifiedProviderV2` 组件挂载
2. `useEffect` 触发存储初始化
3. 从localStorage恢复Web和Lynx代码
4. 触发状态更新，导致 `useLynxStateOptimized` 记录变化
5. `LynxCodeHighlight` 组件挂载，也尝试从localStorage恢复
6. 产生重复的恢复日志

### 场景2: 开发环境热重载
1. 代码变更触发热重载
2. 组件重新挂载，但localStorage中仍有数据
3. 重复执行初始化逻辑
4. 产生与首次加载相同的日志

### 场景3: 标签页切换
1. 用户在不同标签页间切换
2. 组件的显示/隐藏状态变化
3. 某些组件可能重新挂载
4. 重复执行初始化逻辑

## 日志去重机制分析

**当前机制**: `src/routes/code_generate/utils/logger.ts:131-167`

```typescript
const shouldLogMessage = (level: LogLevel, logKey: string): boolean => {
  // 检查是否是重复日志
  if (recentLogs[logKey]) {
    recentLogs[logKey].count++;
    recentLogs[logKey].timestamp = now;
    
    // 只有在达到显示阈值时才显示
    if (recentLogs[logKey].count % MAX_DUPLICATE_COUNT === 0) {
      console.log(
        `[日志系统] 检测到重复日志，已跳过${recentLogs[logKey].count - 1}条相似记录`,
      );
      return true;
    }
    return false;
  }
  
  // 新日志，添加到记录中
  recentLogs[logKey] = { timestamp: now, count: 1 };
  return true;
};
```

**问题分析**:
- 去重机制基于消息指纹，但指纹生成可能不够精确
- `DEDUPLICATION_WINDOW = 200ms` 可能太短
- `MAX_DUPLICATE_COUNT = 5` 意味着每5条重复日志才会显示一次提示

## 性能影响评估

### 1. 内存影响
- 重复的状态更新会增加内存使用
- 大量的日志对象会占用内存
- 事件监听器重复注册可能导致内存泄漏

### 2. 渲染性能影响
- 频繁的状态变化会触发不必要的重渲染
- 组件重复挂载会影响首屏加载性能
- 大量的console.log会影响开发环境性能

### 3. 用户体验影响
- 开发者控制台被大量重复日志污染
- 难以找到真正有用的调试信息
- 可能掩盖真正的错误信息

## 解决方案

### 方案1: 优化组件挂载逻辑
1. 添加挂载状态检查，防止重复初始化
2. 使用全局标志位确保初始化只执行一次
3. 优化组件的依赖数组，减少不必要的重新挂载

### 方案2: 改进日志去重机制
1. 增加去重时间窗口到1000ms
2. 改进消息指纹算法，提高去重精度
3. 添加基于调用栈的去重逻辑

### 方案3: 优化状态管理
1. 减少不必要的状态变化通知
2. 合并相关的状态更新操作
3. 添加状态变化的防抖机制

### 方案4: 改进localStorage恢复逻辑
1. 统一localStorage恢复逻辑到单一位置
2. 添加恢复状态标志，防止重复恢复
3. 优化恢复时机，避免与其他初始化冲突

## 推荐的修复优先级

### 高优先级 (立即修复)
1. **统一localStorage恢复逻辑** - 防止多个组件重复恢复
2. **改进日志去重机制** - 立即减少日志污染
3. **添加初始化状态检查** - 防止重复初始化

### 中优先级 (短期修复)
1. **优化状态选择器** - 减少不必要的状态变化通知
2. **改进事件触发机制** - 避免重复事件触发
3. **添加组件挂载防护** - 防止异常的重复挂载

### 低优先级 (长期优化)
1. **重构初始化流程** - 设计更合理的初始化架构
2. **添加性能监控** - 监控和预防类似问题
3. **优化开发体验** - 提供更好的调试工具

## 监控和预防措施

### 1. 添加性能监控
- 监控组件挂载频率
- 监控状态变化频率
- 监控日志产生频率

### 2. 添加开发工具
- 提供日志过滤工具
- 添加组件挂载可视化
- 提供状态变化追踪工具

### 3. 改进开发流程
- 添加代码审查检查点
- 建立性能测试基准
- 定期进行性能分析

## 结论

重复日志问题主要由以下几个因素共同造成：
1. 组件重复挂载导致的重复初始化
2. 多个组件独立执行localStorage恢复逻辑
3. 状态选择器过度触发调试日志
4. ✅ ~~自定义事件重复触发~~ (已解决)
5. 日志去重机制不够完善

**当前状态**:
- ✅ 自定义事件问题已完全解决，所有自定义事件已替换为React Context回调
- 🔄 建议继续修复localStorage恢复逻辑和改进日志去重机制

详细的自定义事件修复信息请参考: `custom-events-removal-summary.md`

## 具体修复方案

### 修复方案1: 统一localStorage恢复逻辑

**问题**: 多个组件独立执行localStorage恢复，导致重复日志

**解决方案**: 创建统一的存储恢复管理器

```typescript
// src/routes/code_generate/utils/storageRecoveryManager.ts
class StorageRecoveryManager {
  private static instance: StorageRecoveryManager;
  private recoveryStatus = {
    web: false,
    lynx: false,
    initialized: false
  };

  static getInstance() {
    if (!StorageRecoveryManager.instance) {
      StorageRecoveryManager.instance = new StorageRecoveryManager();
    }
    return StorageRecoveryManager.instance;
  }

  async recoverWebCode(): Promise<string | null> {
    if (this.recoveryStatus.web) {
      return null; // 已经恢复过了
    }

    const webCode = WebCodeStorage.getCode();
    if (webCode && webCode.length > 0) {
      this.recoveryStatus.web = true;
      logger.info(`[StorageRecovery] 恢复Web代码，长度: ${webCode.length}`);
      return webCode;
    }
    return null;
  }

  async recoverLynxCode(): Promise<string | null> {
    if (this.recoveryStatus.lynx) {
      return null; // 已经恢复过了
    }

    const lynxCode = LynxCodeStorage.getCode();
    if (lynxCode && lynxCode.length > 0) {
      this.recoveryStatus.lynx = true;
      logger.info(`[StorageRecovery] 恢复Lynx代码，长度: ${lynxCode.length}`);
      return lynxCode;
    }
    return null;
  }

  markInitialized() {
    this.recoveryStatus.initialized = true;
  }

  isInitialized() {
    return this.recoveryStatus.initialized;
  }

  reset() {
    this.recoveryStatus = { web: false, lynx: false, initialized: false };
  }
}

export const storageRecoveryManager = StorageRecoveryManager.getInstance();
```

### 修复方案2: 改进日志去重机制

**问题**: 当前去重机制时间窗口太短，精度不够

**解决方案**: 优化去重算法

```typescript
// src/routes/code_generate/utils/logger.ts 修改
// 增加去重时间窗口
const DEDUPLICATION_WINDOW = 1000; // 从200ms增加到1000ms
// 降低重复阈值
const MAX_DUPLICATE_COUNT = 3; // 从5降低到3

// 改进消息指纹算法
const createMessageFingerprint = (message: any, namespace: string): string => {
  let baseMessage = typeof message === 'string' ? message : JSON.stringify(message);

  // 移除时间戳、随机数等变化的部分
  baseMessage = baseMessage
    .replace(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/g, 'TIMESTAMP') // ISO时间戳
    .replace(/\d{13}/g, 'TIMESTAMP') // Unix时间戳
    .replace(/长度:\s*\d+/g, '长度:N') // 代码长度
    .replace(/版本:\s*\d+/g, '版本:N') // 版本号
    .replace(/操作ID:\s*[a-zA-Z0-9-]+/g, '操作ID:ID') // 操作ID
    .replace(/会话:\s*[a-zA-Z0-9-]+/g, '会话:SESSION'); // 会话ID

  return `${namespace}:${baseMessage}`;
};

// 添加基于调用栈的去重
const getCallerFingerprint = (): string => {
  const err = new Error();
  const stack = err.stack?.split('\n') || [];
  // 取前3层调用栈作为指纹
  return stack.slice(1, 4).join('|');
};
```

### 修复方案3: 优化状态选择器调试日志

**问题**: `useLynxStateOptimized` 产生过多调试日志

**解决方案**: 添加日志级别控制和采样

```typescript
// src/routes/code_generate/contexts/CodeGenerationUnifiedContextV2.tsx 修改
export const useLynxStateOptimized = () => {
  return useSelector(
    (state) => state.lynx,
    (a, b) => {
      const isEqual = (/* 现有比较逻辑 */);

      // 🚨 修复：添加日志级别控制和采样
      if (!isEqual && process.env.NODE_ENV === 'development') {
        // 使用采样机制，只记录部分状态变化
        const shouldLog = Math.random() < 0.1; // 10%采样率

        if (shouldLog) {
          logger.debug('[useLynxStateOptimized] 状态变化检测 (采样):', {
            lynxCodeChanged: a.lynxCode !== b.lynxCode,
            lynxCodeLengthA: a.lynxCode?.length || 0,
            lynxCodeLengthB: b.lynxCode?.length || 0,
            versionChanged: a.codeVersion !== b.codeVersion,
            loadingChanged: a.isLynxRPCLoading !== b.isLynxRPCLoading,
            completeChanged: a.isLynxCodeComplete !== b.isLynxCodeComplete,
          });
        }
      }

      return isEqual;
    }
  );
};
```

### 修复方案4: 添加初始化状态检查

**问题**: 组件重复挂载导致重复初始化

**解决方案**: 使用全局状态管理初始化

```typescript
// src/routes/code_generate/contexts/CodeGenerationUnifiedContextV2.tsx 修改
export const CodeGenerationUnifiedProviderV2: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(unifiedReducer, initialState);

  // 🚨 修复：添加初始化状态检查
  const initializationRef = useRef({
    started: false,
    completed: false,
    timestamp: 0
  });

  useEffect(() => {
    const initializeFromStorage = async () => {
      // 防止重复初始化
      if (initializationRef.current.started) {
        logger.debug('[UnifiedContext] 初始化已在进行中，跳过重复调用');
        return;
      }

      initializationRef.current.started = true;
      initializationRef.current.timestamp = Date.now();

      try {
        // 使用统一的恢复管理器
        const recoveryManager = storageRecoveryManager;

        if (!recoveryManager.isInitialized()) {
          const webCode = await recoveryManager.recoverWebCode();
          if (webCode) {
            dispatch({ type: 'WEB_UPDATE_CODE', code: webCode });
            dispatch({ type: 'WEB_SET_COMPLETE', isComplete: true });
          }

          const lynxCode = await recoveryManager.recoverLynxCode();
          if (lynxCode) {
            dispatch({ type: 'LYNX_UPDATE_CODE', code: lynxCode });
            dispatch({ type: 'LYNX_SET_COMPLETE', isComplete: true });
          }

          recoveryManager.markInitialized();
          logger.info('[UnifiedContext] 存储初始化完成');
        } else {
          logger.debug('[UnifiedContext] 存储已初始化，跳过恢复');
        }

        initializationRef.current.completed = true;
      } catch (error) {
        logger.error('[UnifiedContext] 存储初始化失败:', error);
        initializationRef.current.started = false; // 重置状态以允许重试
      }
    };

    // 🚨 修复：移除延迟，直接执行
    initializeFromStorage();
  }, []); // 保持空依赖数组

  // ... 其余代码保持不变
};
```

### 修复方案5: 优化事件触发机制

**问题**: 自定义事件重复触发

**解决方案**: 添加事件去重和合并机制

```typescript
// src/routes/code_generate/utils/eventManager.ts
class EventManager {
  private static instance: EventManager;
  private pendingEvents = new Map<string, any>();
  private eventTimers = new Map<string, NodeJS.Timeout>();

  static getInstance() {
    if (!EventManager.instance) {
      EventManager.instance = new EventManager();
    }
    return EventManager.instance;
  }

  dispatchEvent(eventName: string, detail: any, delay = 0) {
    const eventKey = `${eventName}:${JSON.stringify(detail)}`;

    // 清除之前的定时器
    if (this.eventTimers.has(eventKey)) {
      clearTimeout(this.eventTimers.get(eventKey)!);
    }

    // 设置新的定时器
    const timer = setTimeout(() => {
      try {
        const event = new CustomEvent(eventName, { detail });
        window.dispatchEvent(event);
        logger.debug(`[EventManager] 触发事件: ${eventName}`);
      } catch (error) {
        logger.error(`[EventManager] 触发事件失败: ${eventName}`, error);
      } finally {
        this.eventTimers.delete(eventKey);
        this.pendingEvents.delete(eventKey);
      }
    }, delay);

    this.eventTimers.set(eventKey, timer);
    this.pendingEvents.set(eventKey, detail);
  }

  cancelEvent(eventName: string, detail: any) {
    const eventKey = `${eventName}:${JSON.stringify(detail)}`;

    if (this.eventTimers.has(eventKey)) {
      clearTimeout(this.eventTimers.get(eventKey)!);
      this.eventTimers.delete(eventKey);
      this.pendingEvents.delete(eventKey);
    }
  }

  cleanup() {
    this.eventTimers.forEach(timer => clearTimeout(timer));
    this.eventTimers.clear();
    this.pendingEvents.clear();
  }
}

export const eventManager = EventManager.getInstance();
```

## 实施计划

### 第一阶段 (立即实施)
1. **部署改进的日志去重机制** - 立即减少日志污染
2. **添加StorageRecoveryManager** - 防止重复恢复
3. **优化状态选择器日志** - 减少调试日志数量

### 第二阶段 (1-2天内)
1. **重构初始化逻辑** - 添加状态检查
2. **部署EventManager** - 优化事件触发
3. **添加性能监控** - 监控修复效果

### 第三阶段 (1周内)
1. **全面测试修复效果** - 确保问题解决
2. **优化开发工具** - 提供更好的调试体验
3. **建立预防机制** - 防止类似问题再次发生

## 验证方法

### 1. 日志数量验证
- 页面加载后观察控制台日志数量
- 统计重复日志的减少比例
- 确认关键日志仍然正常显示

### 2. 功能验证
- 确认localStorage恢复功能正常
- 确认状态管理功能正常
- 确认组件渲染性能正常

### 3. 性能验证
- 测量页面加载时间
- 监控内存使用情况
- 检查是否有内存泄漏

通过以上修复方案的实施，可以有效解决页面进入时的重复日志问题，提升开发体验和应用性能。

## 🚨 紧急修复：移除所有自定义事件

### 问题发现
代码中大量使用了自定义事件（CustomEvent），这违反了代码原则。需要立即将所有自定义事件替换为React Context。

### 自定义事件使用清单

#### 1. 核心组件中的自定义事件

**LynxViewModeContext.tsx (行228-253)**
```typescript
// ❌ 违反原则：使用自定义事件
window.addEventListener('lynx-view-mode-change', handleCustomViewModeChange);
```

**CodeGenerationUnifiedContextV2.tsx (行771-810)**
```typescript
// ❌ 违反原则：触发自定义事件
const event = new CustomEvent('lynxCodeUpdated', { detail: {...} });
window.dispatchEvent(event);

// ❌ 违反原则：延迟触发事件
const checkEvent = new CustomEvent('lynx-data-flow-check', { detail: {...} });
window.dispatchEvent(checkEvent);
```

**Chat.tsx (行452-474, 1256-1293)**
```typescript
// ❌ 违反原则：监听自定义事件
window.addEventListener('web-code-regen', handleWebCodeRegenEvent);
window.addEventListener('fix-web-code', handleFixWebCodeEvent);
```

**MobilePreview.tsx (行914-928)**
```typescript
// ❌ 违反原则：触发自定义事件
const chatRegenEvent = new CustomEvent('fix-web-code', { detail: {...} });
window.dispatchEvent(chatRegenEvent);
```

**LynxCodeHighlight.tsx (行652-656)**
```typescript
// ❌ 违反原则：监听多个自定义事件
window.addEventListener('lynx-code-updated', handleLynxCodeUpdated);
window.addEventListener('lynx-storage-ready', handleLynxStorageReady);
window.addEventListener('lynx-data-flow-check', handleDataFlowCheck);
```

**LynxTabContent.tsx (行180, 714-722)**
```typescript
// ❌ 违反原则：触发自定义事件
window.dispatchEvent(new CustomEvent('lynxUIStateUpdate', { detail }));
window.dispatchEvent(new CustomEvent('lynxIframeLoaded', { detail: {...} }));
```

**WebCodeHighlight.tsx (行937-943)**
```typescript
// ❌ 违反原则：监听自定义事件
window.addEventListener('web-storage-ready', handleStorageReady);
window.addEventListener('web-code-loaded-from-storage', handleCodeLoadedFromStorage);
```

### 重构方案：创建事件通信Context

#### 1. 创建统一的事件通信Context

```typescript
// src/routes/code_generate/contexts/EventCommunicationContext.tsx
interface EventCommunicationContextType {
  // Lynx相关事件
  triggerLynxCodeUpdate: (code: string, version?: number, sessionId?: string) => void;
  triggerLynxStorageReady: () => void;
  triggerLynxDataFlowCheck: (code: string, version: number) => void;

  // Web相关事件
  triggerWebCodeRegen: (sessionId?: string) => void;
  triggerWebCodeFix: (prompt: string, errorInfo: any) => void;
  triggerWebStorageReady: () => void;

  // UI相关事件
  triggerLynxViewModeChange: (mode: 'code' | 'playground', source: string) => void;
  triggerLynxUIStateUpdate: (detail: any) => void;
  triggerLynxIframeLoaded: (detail: any) => void;

  // 事件监听器注册
  onLynxCodeUpdate: (callback: (code: string, version?: number, sessionId?: string) => void) => () => void;
  onWebCodeRegen: (callback: (sessionId?: string) => void) => () => void;
  onWebCodeFix: (callback: (prompt: string, errorInfo: any) => void) => () => void;
  onLynxViewModeChange: (callback: (mode: 'code' | 'playground', source: string) => void) => () => void;
  // ... 其他事件监听器
}
```

#### 2. 重构具体组件

**重构LynxViewModeContext.tsx**
```typescript
// ✅ 使用Context替代自定义事件
const { onLynxViewModeChange } = useEventCommunication();

useEffect(() => {
  const unsubscribe = onLynxViewModeChange((mode, source) => {
    logger.info(`[LynxViewMode] 收到视图模式切换: ${mode} | 来源: ${source}`);
    setViewMode(mode, source);
  });

  return unsubscribe;
}, []);
```

**重构CodeGenerationUnifiedContextV2.tsx**
```typescript
// ✅ 使用Context替代自定义事件
const { triggerLynxCodeUpdate, triggerLynxDataFlowCheck } = useEventCommunication();

// 在updateCode方法中
updateCode: (code: string, version?: number, sessionId?: string) => {
  const safeCode = typeof code === 'string' ? code : String(code);

  // ✅ 使用Context方法替代自定义事件
  triggerLynxCodeUpdate(safeCode, version, sessionId);

  // ✅ 延迟触发数据流检查
  setTimeout(() => {
    triggerLynxDataFlowCheck(safeCode, version || Date.now());
  }, 100);

  dispatch({ type: 'LYNX_UPDATE_CODE', code: safeCode, version, sessionId });
}
```

**重构Chat.tsx**
```typescript
// ✅ 使用Context替代自定义事件监听
const { onWebCodeRegen, onWebCodeFix } = useEventCommunication();

useEffect(() => {
  const unsubscribeRegen = onWebCodeRegen((sessionId) => {
    log.info(`[事件监听] 收到web-code-regen事件，sessionId:${sessionId || '未提供'}`);
    regenerateWebCode('重新生成Web代码');
  });

  const unsubscribeFix = onWebCodeFix((prompt, errorInfo) => {
    log.info('接收到修复代码事件', { promptLength: prompt.length });
    handleFixWebCodeSend(prompt);
  });

  return () => {
    unsubscribeRegen();
    unsubscribeFix();
  };
}, [regenerateWebCode, handleFixWebCodeSend]);
```

**重构MobilePreview.tsx**
```typescript
// ✅ 使用Context替代自定义事件触发
const { triggerWebCodeFix } = useEventCommunication();

const handleFixCode = useCallback((errorInfo: any) => {
  try {
    const currentCode = webCode || webState.webCode;
    if (!currentCode || !errorInfo.detail) {
      logger.error('没有可修复的代码或错误信息');
      return;
    }

    const fixPrompt = `修复以下代码中的错误，错误信息是: ${errorInfo.detail}`;

    setTimeout(() => {
      api.web.setLoading(true);
      api.web.setComplete(false);

      // ✅ 使用Context方法替代自定义事件
      triggerWebCodeFix(fixPrompt, errorInfo);

      logger.info('已触发代码修复', { prompt: `${fixPrompt.substring(0, 100)}...` });
    }, 0);
  } catch (e) {
    logger.error('触发代码修复失败:', e);
  }
}, [webCode, webState.webCode, api, triggerWebCodeFix]);
```

### 立即行动计划

#### 第一步：创建EventCommunicationContext (30分钟)
1. 创建Context定义和Provider
2. 实现事件注册和触发机制
3. 添加到主Provider中

#### 第二步：重构核心组件 (60分钟)
1. 重构CodeGenerationUnifiedContextV2.tsx
2. 重构LynxViewModeContext.tsx
3. 重构Chat.tsx

#### 第三步：重构UI组件 (45分钟)
1. 重构LynxCodeHighlight.tsx
2. 重构LynxTabContent.tsx
3. 重构WebCodeHighlight.tsx
4. 重构MobilePreview.tsx

#### 第四步：测试和验证 (30分钟)
1. 验证所有功能正常
2. 确认没有自定义事件残留
3. 测试重复日志问题是否解决

### 验证清单

- [ ] 移除所有CustomEvent创建
- [ ] 移除所有window.dispatchEvent调用
- [ ] 移除所有window.addEventListener自定义事件监听
- [ ] 移除所有window.removeEventListener自定义事件清理
- [ ] 确保所有组件间通信使用React Context
- [ ] 验证功能完整性
- [ ] 确认重复日志问题解决

### 预期效果

1. **代码合规性**: 完全符合"禁止使用自定义事件"的原则
2. **性能提升**: 减少事件监听器开销，提升性能
3. **调试友好**: React DevTools可以追踪Context变化
4. **类型安全**: TypeScript可以提供完整的类型检查
5. **重复日志减少**: 消除事件相关的重复日志
