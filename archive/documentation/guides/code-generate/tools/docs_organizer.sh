#!/bin/bash

# 文档整理脚本
# 用于整理 code_generate/deleted/docs 目录下的文档

echo "=== 开始整理文档 ==="

# 工作目录
BASE_DIR="../deleted/docs"
TARGET_DIR="../docs"

# 创建新的目录结构
echo "创建目录结构..."
mkdir -p $TARGET_DIR/{architecture,changelog,guides,api,workflow,tools}
mkdir -p $TARGET_DIR/archived/{outdated,duplicated,auxiliary}

# 复制并整理文档
echo "整理文档内容..."

# 复制changelog文件
echo "整理 changelog 文件..."
cp $BASE_DIR/docs/changelog/*.md $TARGET_DIR/changelog/

# 复制并整理 RPC 文档
echo "整理 RPC 文档..."
mkdir -p $TARGET_DIR/architecture/rpc
[ -d "$BASE_DIR/docs/RPC" ] && cp -r $BASE_DIR/docs/RPC/* $TARGET_DIR/architecture/rpc/

# 复制并整理分析文档
echo "整理分析文档..."
mkdir -p $TARGET_DIR/architecture/analysis
[ -d "$BASE_DIR/docs/analysis" ] && cp -r $BASE_DIR/docs/analysis/* $TARGET_DIR/architecture/analysis/

# 整理工作流文档
echo "整理工作流文档..."
[ -d "$BASE_DIR/docs/workflow" ] && cp -r $BASE_DIR/docs/workflow/* $TARGET_DIR/workflow/

# 整理DOCUMENT_RULES
echo "整理文档规则..."
mkdir -p $TARGET_DIR/guides/rules
[ -d "$BASE_DIR/docs/DOCUMENT_RULES" ] && cp -r $BASE_DIR/docs/DOCUMENT_RULES/* $TARGET_DIR/guides/rules/

# 整理ReadMe文档
echo "整理README文档..."
mkdir -p $TARGET_DIR/guides/readme
[ -d "$BASE_DIR/docs/readme" ] && cp -r $BASE_DIR/docs/readme/* $TARGET_DIR/guides/readme/

# 整理归档文件
echo "整理归档文件..."
# 复制已经归档的文件
[ -d "$BASE_DIR/archived/outdated" ] && cp -r $BASE_DIR/archived/outdated/* $TARGET_DIR/archived/outdated/
[ -d "$BASE_DIR/archived/duplicated" ] && cp -r $BASE_DIR/archived/duplicated/* $TARGET_DIR/archived/duplicated/
[ -d "$BASE_DIR/archived/auxiliary" ] && cp -r $BASE_DIR/archived/auxiliary/* $TARGET_DIR/archived/auxiliary/

# 创建 README.md 文件
echo "创建目录说明文件..."

# 主README文件
cat > $TARGET_DIR/README.md << 'EOL'
# 项目文档中心

本目录包含项目的所有文档资料，按照以下分类组织：

## 目录结构

- **architecture/** - 系统架构文档
  - **analysis/** - 系统分析文档
  - **rpc/** - RPC相关文档
- **changelog/** - 变更记录
- **guides/** - 使用指南
  - **rules/** - 文档规范
  - **readme/** - 功能说明
- **api/** - API文档
- **workflow/** - 工作流程
- **tools/** - 文档工具
- **archived/** - 归档文档
  - **outdated/** - 过时文档
  - **duplicated/** - 重复文档
  - **auxiliary/** - 辅助文档

## 文档规范

所有文档应遵循 [guides/rules](guides/rules) 目录下定义的规范，包括命名、格式和内容要求。

## 文档索引

请参考以下索引文件快速查找相关文档：

1. [架构文档索引](architecture/README.md)
2. [变更记录索引](changelog/README.md)
3. [API文档索引](api/README.md)
4. [使用指南索引](guides/README.md)

## 维护

文档由文档管理团队维护，如需修改请遵循文档变更流程。
EOL

# 架构目录README
cat > $TARGET_DIR/architecture/README.md << 'EOL'
# 架构文档

本目录包含系统架构相关的文档，包括：

## 核心架构

- [Web/Lynx架构设计](WEB_LYNX_ARCHITECTURE.md)
- [Context管理架构](CONTEXT_MANAGEMENT.md)
- [错误处理策略](ERROR_HANDLING_STRATEGY.md)
- [Claude流处理架构](CLAUDE_STREAM_PROCESSING.md)

## 分析文档

分析目录包含对系统各方面的深入分析：

- [系统性能分析](analysis/PERFORMANCE_ANALYSIS.md)
- [数据流分析](analysis/DATA_FLOW_ANALYSIS.md)
- [错误分析](analysis/ERROR_ANALYSIS.md)

## RPC文档

RPC目录包含远程过程调用相关的设计和实现文档：

- [WebRPC设计](rpc/WEB_RPC_DESIGN.md)
- [LynxRPC设计](rpc/LYNX_RPC_DESIGN.md)
- [WebRPC和LynxRPC并行架构](rpc/WEB_LYNX_RPC_PARALLEL.md)

## 文档索引

| 文档名称 | 描述 | 最后更新 |
|---------|------|---------|
| WEB_LYNX_ARCHITECTURE.md | Web和Lynx架构设计 | 2025-05-30 |
| CONTEXT_MANAGEMENT.md | Context管理架构 | 2025-05-30 |
| ERROR_HANDLING_STRATEGY.md | 错误处理策略 | 2025-05-30 |
| CLAUDE_STREAM_PROCESSING.md | Claude流处理架构 | 2025-05-30 |
EOL

# Changelog目录README
cat > $TARGET_DIR/changelog/README.md << 'EOL'
# 变更记录

本目录包含系统各组件的变更记录，按照时间顺序记录重要更新和修复。

## 主要变更记录

- [Context更新](CONTEXT_UPDATES.md) - Context相关的更新和改进
- [UI组件更新](UI_COMPONENT_UPDATES.md) - UI组件的更新和改进
- [错误处理更新](ERROR_HANDLING_UPDATES.md) - 错误处理机制的更新和改进
- [Claude流更新](CLAUDE_STREAM_UPDATES.md) - Claude流处理的更新和改进

## 完整变更日志

请参考 [CHANGELOG.md](CHANGELOG.md) 获取项目完整的变更记录。

## 文档整理记录

- [文档结构整理](docs_restructure.md) - 2025-05-29的文档结构整理
- [文档整理完成报告](docs_organization_completed.md) - 2025-05-30的文档整理完成报告
EOL

# 归档目录README
cat > $TARGET_DIR/archived/README.md << 'EOL'
# 归档文档

本目录包含已归档的文档，不再主动维护。这些文档按以下类别整理：

## 归档分类

- **outdated/** - 过时文档，内容已不再适用于当前系统架构
- **duplicated/** - 重复文档，内容已合并到其他文档中
- **auxiliary/** - 辅助文档，仅作为分析或临时参考用

## 归档文档查询

如需查询特定归档文档，请使用文档搜索工具或参考相应分类目录中的索引文件。

## 归档策略

文档归档遵循以下原则：

1. 超过6个月未更新且内容已过时的文档
2. 内容已完全合并到其他文档中的重复文档
3. 仅用于临时分析和参考的文档

如需将归档文档恢复到活跃状态，请联系文档管理团队。
EOL

# 创建并合并相似主题的changelog
echo "整合相似主题的changelog文档..."

# 合并Claude流相关的changelog
cat > $TARGET_DIR/changelog/CLAUDE_STREAM_UPDATES.md << 'EOL'
# Claude流处理更新日志

本文档整合了所有Claude流处理相关的更新记录。

## 2025-07-07 数据前缀处理优化

- 添加了对data:前缀的自动检测和处理
- 优化了数据块解析逻辑
- 增加了兼容性处理机制

参考: [数据前缀处理优化](2024-07-07-数据前缀处理优化.md)

## 2025-06-15 Claude流自动续传功能

- 实现了自动检测流结束的机制
- 添加了流续传的自动触发功能
- 优化了流处理性能

参考: [Claude流自动续传](CLAUDE_STREAM_SSE_AUTO_CONTINUATION.md)

## 2025-06-06 Claude流存储优化

- 改进了流数据的存储机制
- 实现了更高效的缓存策略
- 减少了内存占用

参考: [Claude流存储优化](CLAUDE_STREAM_STORAGE_OPTIMIZATION.md)

## 2025-06-01 Claude流解析器统一

- 统一了Web和Lynx的流解析器
- 实现了通用的数据处理接口
- 简化了代码维护

参考: [Claude流解析器统一](claude_stream_parser_unification.md)

## 2025-05-30 Claude流解析器修复

- 修复了解析器无法处理特殊字符的问题
- 改进了错误处理机制
- 增加了日志记录功能

参考: [Claude流解析器修复](claude_stream_parser_fix.md)

## 2025-05-25 Claude流对象处理改进

- 改进了对JSON对象的处理
- 添加了对嵌套对象的支持
- 优化了数据提取逻辑

参考: [Claude流对象处理](claude_stream_parser_object_handling.md)
EOL

# 创建工具目录文件
cat > $TARGET_DIR/tools/README.md << 'EOL'
# 文档工具

本目录包含用于管理和维护项目文档的工具脚本。

## 可用工具

- **docs_organizer.sh** - 文档整理脚本，用于整理和重组文档结构
- **update_docs.sh** - 文档更新脚本，用于批量更新文档
- **clean_archived.sh** - 归档清理脚本，用于清理过期归档文档

## 使用方法

所有脚本都可以在此目录中执行。例如：

```bash
# 执行文档整理
./docs_organizer.sh

# 更新文档
./update_docs.sh

# 清理归档
./clean_archived.sh
```

## 注意事项

1. 请在执行脚本前备份重要文档
2. 脚本可能需要根据项目结构进行调整
3. 如有问题请联系文档管理团队
EOL

# 复制本脚本到工具目录
cp "$0" $TARGET_DIR/tools/

echo "=== 文档整理完成 ==="
echo "新文档结构已创建在: $TARGET_DIR"
echo "请检查整理结果并进行必要的调整" 