# Web转Lynx PE配置优化文档

## 修改概述

根据用户需求，对 web convert to lynx 的 PE（提示引擎）配置进行了优化，使其包含 lynx 语法规则但不包含过多的设计风格，专注于代码转换而非UI设计。

## 主要修改内容

### 1. 更新 `LYNX_SYSTEM_PROMPT` (src/routes/code_generate/RPC/prompts.ts)

**修改前问题：**
- 包含大量设计风格描述
- 强调视觉效果和动画
- 容易让AI增加设计想象空间

**修改后改进：**
- 专注于Lynx语法规范
- 强调代码转换而非设计
- 明确转换规则和要求

**核心变更：**
```typescript
// 移除了大量设计相关内容，如：
// - "创建精致动感的设计，活泼和谐的配色"
// - "添加精美阴影和光效，创造深度感和层次感"
// - "实现流畅的页面转场动画"

// 替换为专注转换的内容：
// - "专注于Web代码到Lynx代码的精确转换"
// - "保持功能完整性"
// - "不添加额外的设计元素"
```

### 2. 增强转换规则 (src/routes/code_generate/RPC/prompts.ts)

**新增内容：**
- 导入 `LYNX_PROGRAMMING_ENVIRONMENT` 从 `lynxRules.ts`
- 添加详细的HTML标签转换规则
- 添加CSS样式转换规则
- 添加JavaScript转换规则
- 添加事件系统转换规则
- 添加性能优化转换规则

**具体规则：**
```
1. HTML标签转换：
   - div → view
   - span → text  
   - img → image
   - ul/li → list/list-item 或 scroll-view/view
   - input → x-input
   - button → view(bindtap) 或保持button标签

2. CSS样式转换：
   - background-color → backgroundColor (驼峰命名)
   - 移除cursor, pointer-events等Web特有属性
   - 布局默认方向从row调整为column
   - 单位换算：px转换为dp

3. JavaScript转换：
   - addEventListener → bindXXX事件绑定
   - click事件替换为tap事件
   - window引用替换为lynx
   - document.querySelector替换为lynx.selectNode
```

### 3. 优化 `buildConversionPrompt` 方法

**关键改进：**
- 添加 🚨 重要标记强调转换要求
- 明确"不要添加额外的设计元素"
- 强调"这是一个语法转换任务，不是UI设计任务"
- 添加转换原则说明

**新增转换要求：**
```
## 🚨 转换要求（严格遵守）：
1. 严格按照Lynx语法规范进行转换
2. 保持所有原有功能和交互逻辑
3. 🚨 重要：不要添加额外的设计元素、视觉效果或动画
4. 🚨 重要：不要增加设计想象空间，专注于代码转换
5. 🚨 重要：不要优化或美化UI，保持原样转换
```

### 4. 更新 LynxRPCService.ts 中的转换提示

**手动转换模式优化：**
- 移除"最大程度保持原始设计风格和视觉效果"
- 移除"使用Lynx的Canvas功能实现复杂视觉效果"
- 移除"精确转换所有动画和过渡效果"
- 添加明确的转换原则说明

**自动转换模式优化：**
- 简化提示内容
- 专注于语法转换
- 明确不添加设计元素的要求

### 5. 更新 CodeHeader.tsx 中的转换按钮提示

**修改前：**
```typescript
content="转换成Lynx代码"
```

**修改后：**
```typescript
content="将Web代码转换为Lynx语法格式，保持功能不变"
```

## 预期效果

1. **减少设计干扰：** AI不会再添加额外的视觉效果或动画
2. **专注语法转换：** 转换过程更加精确，专注于代码结构
3. **保持功能完整：** 确保原有功能在转换后完全保留
4. **提高转换质量：** 减少不必要的复杂度，提高代码可读性

## 使用方式

转换功能的使用方式保持不变：
1. 在Web标签页生成代码后
2. 点击"转换成Lynx"按钮
3. 系统将使用优化后的PE配置进行转换
4. 转换结果将在Lynx标签页显示

## 注意事项

- 转换后的代码专注于功能实现，不包含额外的设计元素
- 如需要视觉优化，建议在转换完成后手动调整
- 转换过程保持原有的交互逻辑和数据流
