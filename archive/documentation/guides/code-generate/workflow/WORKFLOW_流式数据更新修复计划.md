---
status: review
version: 1.0
last_updated: 2025-05-25
owner: system
---

# 流式数据更新修复计划

## 问题概述

流式代码生成过程中，CodeHighlight 组件无法实时显示更新内容，仅在完整接收后显示。同时存在大量重复日志、Provider嵌套等问题，严重影响用户体验和系统性能。

## 修复任务清单

### 1. 统一和优化 rpcSharedUtils.ts 中的流式数据处理 (优先级：最高)

- [x] 检查 `extractContentFromJson` 函数，确认其已经支持 source 参数区分 web/lynx
- [x] 确认 `reasoningStore` 对象已存在，能够分别存储 web 和 lynx 的思考内容
- [ ] 优化 `extractContentFromJson` 函数实现，简化代码，并增加对元数据标记的特殊处理：
  ```typescript
  export function extractContentFromJson(jsonContent: string, source: 'web' | 'lynx' = 'web'): string {
    if (!jsonContent) return '';
    
    // 移除data:前缀（如果存在）
    let content = jsonContent.trim();
    if (content.startsWith('data:')) {
      content = content.substring(5).trim();
    }
    
    try {
      // 尝试解析JSON
      const parsed = JSON.parse(content);
      
      // 检查流式数据标准结构: choices[0].delta
      if (parsed.choices && Array.isArray(parsed.choices) && parsed.choices.length > 0 && parsed.choices[0].delta) {
        const delta = parsed.choices[0].delta;
        
        // 处理 reasoning_content: 收集但不直接返回
        if (typeof delta.reasoning_content === 'string') {
          reasoningStore[source] += delta.reasoning_content;
          return ''; // 思考内容不立即作为代码内容返回
        }
        
        // 特别处理：检测并处理元数据标记
        if (typeof delta.content === 'string') {
          const extractedContent = delta.content;
          // 检测"ESTIMATED_SIZE"标记并通知进度条，但不将其添加到代码中
          if (extractedContent.includes('ESTIMATED_SIZE:')) {
            // 提取大小信息并通知进度组件
            const sizeMatch = extractedContent.match(/ESTIMATED_SIZE:\s*(\d+[KMG]?B)/i);
            if (sizeMatch && sizeMatch[1]) {
              // 触发进度更新事件，但不返回此内容作为代码
              notifyProgressEstimate(source, sizeMatch[1]);
              return ''; // 不将这部分元数据添加到代码中
            }
          }
          return extractedContent; // 返回正常代码内容
        }
      }
      
      // 如果所有尝试都失败，返回空字符串
      return '';
    } catch (error) {
      // JSON解析失败时检查是否看起来像代码
      // 简化这部分逻辑，避免过度复杂的判断
      return '';
    }
  }
  
  // 新增：处理大小估计信息的辅助函数
  function notifyProgressEstimate(source: 'web' | 'lynx', sizeEstimate: string): void {
    // 触发事件或更新Context，通知UI组件更新进度条
    console.log(`[${source.toUpperCase()}] 代码大小估计: ${sizeEstimate}`);
    // 这里可以发布事件或直接更新Context状态
  }
  ```

### 2. 添加进度元数据处理函数 (优先级：高)

- [ ] 实现专门的元数据解析和处理函数：
  ```typescript
  /**
   * 处理代码中的元数据标记
   * @param content 代码内容
   * @param source 数据源类型 ('web' | 'lynx')
   * @returns 清理后的代码内容（不包含元数据标记）
   */
  export function processMetadata(content: string, source: 'web' | 'lynx'): string {
    // 处理大小估计标记
    if (content.includes('ESTIMATED_SIZE:')) {
      const sizeMatch = content.match(/ESTIMATED_SIZE:\s*(\d+[KMG]?B)/i);
      if (sizeMatch && sizeMatch[1]) {
        // 发送事件通知进度条组件
        emitEvent(`${source}_size_estimate`, { size: sizeMatch[1] });
        
        // 从内容中移除这个标记（可能是整行或注释块）
        return content.replace(/\/\/?\s*ESTIMATED_SIZE:.*$/gm, '')
                     .replace(/\/\*\s*ESTIMATED_SIZE:.*?\*\//g, '');
      }
    }
    
    return content;
  }
  
  /**
   * 发送事件通知UI组件
   * @param eventType 事件类型
   * @param payload 事件数据
   */
  function emitEvent(eventType: string, payload: any): void {
    // 根据项目结构选择合适的通知方式
    // 1. 如果使用Context API
    if (source === 'web' && webRPCContext) {
      webRPCContext.updateMetadata({ sizeEstimate: payload.size });
    } else if (source === 'lynx' && lynxRPCContext) {
      lynxRPCContext.updateMetadata({ sizeEstimate: payload.size });
    }
    
    // 2. 记录日志
    console.log(`[元数据] ${eventType}: ${JSON.stringify(payload)}`);
  }
  ```

### 3. 修改 WebRPCCore.ts 和 LynxRPCCore.ts 中的流处理函数，实现完全实时更新，同时保护续传功能 (优先级：最高)

- [ ] 修改 WebRPCCore.ts 的 processWebRPCStream 方法，移除所有批处理逻辑，保留续传检测和元数据处理：
  ```typescript
  private async processWebRPCStream(stream: ReadableStream<Uint8Array>, initialState: WebRPCState, ...): Promise<WebRPCState> {
    // 重置 web 的思考内容收集器
    resetReasoningContent('web');
    
    let code = initialState.code;
    
    try {
      const reader = stream.getReader();
      
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          // 流结束，添加收集的思考内容（如果有）
          const reasoningContent = getFormattedReasoningContent('web');
          if (reasoningContent) {
            code = reasoningContent + code;
            // 最终更新
            onUpdate?.({ ...state, code, timestamp: Date.now() });
          }
          
          // 关键：在流结束时检查是否需要续传
          if (code.includes('<CONTINUE_TOKEN>') || detectNeedsContinuation(code)) {
            console.log('[WebRPCCore][续传] 流结束时检测到续传标记，准备进行续传');
            onComplete?.({ 
              ...state, 
              code, 
              isComplete: false, // 标记为未完成，需要续传
              timestamp: Date.now() 
            });
            return { ...state, code, isComplete: false, timestamp: Date.now() };
          }
          
          // 正常完成，不需要续传
          onComplete?.({ ...state, code, isComplete: true, timestamp: Date.now() });
          break;
        }
        
        // 处理数据块
        const chunk = new TextDecoder().decode(value, { stream: true });
        const extractedContent = extractContentFromJson(chunk, 'web');
        
        // 收到内容立即更新，不做任何缓冲或批处理
        if (extractedContent) {
          // 注意：确保元数据标记(如ESTIMATED_SIZE)已在extractContentFromJson中处理，不会被添加到代码中
          code += extractedContent;
          // 每次收到新内容都立即更新状态
          onUpdate?.({ ...state, code, timestamp: Date.now() });
          
          // 在流处理过程中也检查续传标记
          if (code.includes('<CONTINUE_TOKEN>') || detectNeedsContinuation(code)) {
            console.log('[WebRPCCore][续传] 流处理中检测到续传标记');
            // 注意：不立即触发续传，等待流结束
          }
        }
      }
      
      // 返回最终状态...
    } catch (error) {
      // 错误处理...
    }
  }
  ```

- [ ] 同理修改 LynxRPCCore.ts 中的流处理函数，确保同样处理元数据标记

### 4. 增强续传标记检测函数 (优先级：高)

- [ ] 检查并改进 `detectNeedsContinuation` 函数，确保能准确识别需要续传的情况：
  ```typescript
  export function detectNeedsContinuation(content: string): boolean {
    // 明确的续传标记
    if (content.includes('<CONTINUE_TOKEN>')) {
      return true;
    }
    
    // 过滤掉元数据标记后再检查代码完整性
    const contentWithoutMetadata = content.replace(/\/\/?\s*ESTIMATED_SIZE:.*$/gm, '')
                                         .replace(/\/\*\s*ESTIMATED_SIZE:.*?\*\//g, '');
    
    // 代码不完整的迹象
    const incompleteMarkers = [
      'to be continued',
      '未完待续',
      '...', // 省略号在末尾
      'continued in next part',
      '接下来继续'
    ];
    
    for (const marker of incompleteMarkers) {
      if (contentWithoutMetadata.toLowerCase().includes(marker)) {
        return true;
      }
    }
    
    // 语法不完整的迹象（如未闭合的括号）
    const openBrackets = (contentWithoutMetadata.match(/\{/g) || []).length;
    const closeBrackets = (contentWithoutMetadata.match(/\}/g) || []).length;
    if (openBrackets > closeBrackets) {
      // 有未闭合的括号
      return true;
    }
    
    return false;
  }
  ```

- [ ] 创建或改进 `detectLynxNeedsContinuation` 函数，专门处理 Lynx 代码的续传检测
- [ ] 确保这些函数在检测续传需求时正确处理和排除元数据标记

### 5. 彻底移除所有批处理和缓冲区逻辑，保留续传逻辑和元数据处理 (优先级：最高)

- [ ] 删除 WebRPCCore.ts 中的所有缓冲区代码：
  ```typescript
  // 删除所有这些代码
  let updateBuffer = '';
  let lastUpdateTime = Date.now();
  const UPDATE_INTERVAL_MS = 30;
  const MIN_UPDATE_SIZE = 50;
  ```

- [ ] 删除 WebRPCCore.ts 中的所有批处理条件判断代码
- [ ] 同理删除 LynxRPCCore.ts 中的所有批处理和缓冲区代码
- [ ] 确保在删除批处理逻辑的同时，不删除续传相关的代码和判断
- [ ] 确保保留对元数据标记的处理逻辑

### 6. 完全删除安全分割点检测逻辑，保留续传检测和元数据处理 (优先级：高)

- [ ] 从 WebRPCCore.ts 中移除所有安全分割点检测相关代码：
  ```typescript
  // 删除所有类似下面的代码：
  const isSafeSplitPoint = checkSafeSplitPoint(code + extractedContent);
  
  // 以及所有相关的条件判断
  if ((timeThresholdMet || sizeThresholdMet || completionMarkerPresent) && isSafeSplitPoint) {
    // 应用更新
  }
  ```

- [ ] 删除 WebRPCCore.ts 中定义和计算 `isSafeSplitPoint` 的所有代码
- [ ] 同理从 LynxRPCCore.ts 中删除所有安全分割点相关逻辑
- [ ] 确保在删除安全分割点检测的同时，保留代码中对 `<CONTINUE_TOKEN>` 的检测逻辑
- [ ] 确保不影响对元数据标记（如 ESTIMATED_SIZE）的处理

### 7. 修复和增强续传处理逻辑 (优先级：最高)

- [ ] 检查 WebRPCCore.ts 中的 handleContinuation 方法，确保其功能完整：
  ```typescript
  private async handleContinuation(originalPrompt: string, previousCode: string): Promise<void> {
    // 设置标志，防止重复续传
    if (this.isContinuing) {
      console.log('[WebRPCCore][续传] 已有续传进行中，忽略新的续传请求');
      return;
    }
    
    this.isContinuing = true;
    console.log('[WebRPCCore][续传] 开始续传处理');
    
    try {
      // 处理代码，移除续传标记和元数据标记
      let cleanedCode = previousCode.replace(/<CONTINUE_TOKEN>/g, '')
                                   .replace(/\/\/?\s*ESTIMATED_SIZE:.*$/gm, '')
                                   .replace(/\/\*\s*ESTIMATED_SIZE:.*?\*\//g, '');
      
      // 构建续传消息
      const messages = [
        {
          role: 'system',
          content: WEB_SYSTEM_PROMPT || SYSTEM_PROMPT
        },
        {
          role: 'assistant',
          content: cleanedCode
        },
        {
          role: 'user',
          content: `请继续生成代码，确保完整实现所有功能。以下是原始需求：${originalPrompt}`
        }
      ];
      
      // 发起续传请求
      const stream = await this.fetchWebRPCStream(
        messages,
        WORKFLOW_IDS.WEB_GENERATION,
        this.abortController?.signal
      );
      
      if (!stream) {
        throw new Error('无法获取续传流');
      }
      
      // 处理续传流
      await this.processWebRPCStream(
        stream,
        { ...createInitialWebRPCState(), code: cleanedCode },
        state => {
          // 同样确保实时更新
          this.manager.handleStreamUpdate(state.code);
        },
        state => {
          this.manager.handleStreamComplete();
          this.isContinuing = false;
        },
        error => {
          this.manager.handleError(error);
          this.isContinuing = false;
        }
      );
    } catch (error) {
      console.error('[WebRPCCore][续传] 续传处理出错:', error);
      this.manager.handleError(error instanceof Error ? error : new Error(String(error)));
      this.isContinuing = false;
    }
  }
  ```

- [ ] 为 LynxRPCCore.ts 实现类似的 handleContinuation 方法，确保也能正确处理元数据标记
- [ ] 确保续传功能能够正确传递原始请求和之前生成的代码
- [ ] 添加防重复续传的机制，防止多次触发续传

### 8. 提供进度条和元数据更新支持 (优先级：高)

- [ ] 在 WebRPCContext 和 LynxRPCContext 中添加元数据支持：
  ```typescript
  // 添加到 WebRPCContext.tsx
  interface WebRPCMetadata {
    sizeEstimate?: string;  // 代码大小估计 (如 "15KB")
    progressPercentage?: number; // 生成进度百分比
  }
  
  // 在 Context 状态中添加元数据字段
  interface WebRPCContextState {
    // ... 现有字段
    metadata: WebRPCMetadata;
  }
  
  // 在 Context API 中添加更新元数据的方法
  const WebRPCContext = React.createContext<WebRPCContextValue>({
    // ... 现有内容
    updateMetadata: () => {},
  });
  
  // 在 Provider 中实现元数据更新方法
  const updateMetadata = useCallback((newMetadata: Partial<WebRPCMetadata>) => {
    dispatch({
      type: 'UPDATE_METADATA',
      payload: newMetadata
    });
  }, [dispatch]);
  ```

- [ ] 确保 Web 和 Lynx 的进度条组件能正确响应元数据更新

### 9. 检查 Chat.tsx 中 Provider 嵌套问题 (优先级：高)

- [ ] 查找 Chat.tsx 中可能嵌套的 WebRPCProvider：
  ```tsx
  // 查找并移除类似下面的代码
  <WebRPCProvider>
    {/* Chat 组件内容 */}
  </WebRPCProvider>
  ```

- [ ] 确保只在 page.tsx 或应用根组件中使用一次 WebRPCProvider
- [ ] 检查 RPC 调用链路，确保续传请求能够正确调用和处理

### 10. 删除所有配置参数，强制实时更新，保留续传配置 (优先级：高)

- [ ] 删除 rpcSharedUtils.ts 中可能存在的批处理配置常量：
  ```typescript
  // 删除类似这样的代码
  export const STREAM_UPDATE_CONFIG = {
    UPDATE_INTERVAL_MS: 30,
    MIN_UPDATE_SIZE: 50
  };
  ```

- [ ] 确保 WebRPCCore.ts 和 LynxRPCCore.ts 中没有使用任何延迟更新或批处理的配置参数
- [ ] 保留与续传功能相关的配置，例如 `WORKFLOW_IDS` 常量：
  ```typescript
  // 保留这类配置
  export const WORKFLOW_IDS = {
    WEB_GENERATION: 'ce03397a-001f-47de-96a2-c648f05d8668',
    LYNX_CONVERSION: 'fc02f6eb-26db-4c63-be62-483ab8abce34'
  };
  ```

### 11. 优化日志记录，确保续传相关日志清晰可见 (优先级：中)

- [ ] 调整 WebRPCCore.ts 和 LynxRPCCore.ts 中的日志级别，减少 TRACE 和 DEBUG 级别的日志
- [ ] 移除或合并频繁触发的日志记录点，如更新日志（可能会因实时更新变得更多）
- [ ] 优化 Provider 挂载日志，避免频繁打印
- [ ] 确保续传相关的日志清晰且有足够的信息：
  ```typescript
  console.log(`[WebRPCCore][续传] 检测到续传标记 | 代码长度: ${code.length} | 标记位置: ${code.indexOf('<CONTINUE_TOKEN>')} | 时间: ${new Date().toISOString()}`);
  ```
- [ ] 为元数据处理添加清晰的日志：
  ```typescript
  console.log(`[WebRPCCore][元数据] 检测到代码大小估计: ${sizeEstimate} | 时间: ${new Date().toISOString()}`);
  ```

### 12. 开发测试工具验证修复效果和续传功能 (优先级：必要)

- [ ] 在 rpcSharedUtils.ts 中添加模拟数据生成函数，包括续传场景和元数据标记：
  ```typescript
  export function generateMockStreamData(
    count: number = 10, 
    delay: number = 20, 
    includeContinueToken: boolean = false,
    includeSizeEstimate: boolean = false
  ): Function {
    const samples = [];
    
    // 添加大小估计标记（通常在开始位置）
    if (includeSizeEstimate) {
      samples.push(JSON.stringify({
        id: `mock-${Date.now()}-estimate`,
        created: Date.now(),
        model: "mock_model",
        choices: [{
          delta: { content: "// ESTIMATED_SIZE: 15KB\n" },
          index: 0,
          stop_reason: ""
        }],
        usage: {}
      }));
    }
    
    // 添加内容块
    for (let i = 0; i < count; i++) {
      // 在最后添加续传标记
      const content = i === count - 1 && includeContinueToken
        ? '<CONTINUE_TOKEN>\n'
        : (i % 3 === 0 ? 
           `function example${i}() {\n` : 
           (i % 3 === 1 ? `  console.log("test${i}");\n` : `}\n`));
      
      samples.push(JSON.stringify({
        id: `mock-${Date.now()}-${i}`,
        created: Date.now(),
        model: "mock_model",
        choices: [{
          delta: { content },
          index: 0,
          stop_reason: ""
        }],
        usage: {}
      }));
    }
    
    // 返回一个模拟的流式数据发送函数
    return (callback) => {
      let index = 0;
      const interval = setInterval(() => {
        if (index >= samples.length) {
          clearInterval(interval);
          callback(null, true); // 完成
          return;
        }
        callback(samples[index], false); // 发送数据
        index++;
      }, delay); // 每 delay 毫秒发送一条数据
      
      return () => clearInterval(interval); // 返回清理函数
    };
  }
  ```

- [ ] 创建特定的测试用例，验证元数据处理和续传功能是否正常工作：
  ```typescript
  // 测试元数据和续传功能
  async function testMetadataAndContinuation() {
    console.log('=== 测试元数据处理和续传功能 ===');
    const mockStream1 = generateMockStreamData(10, 20, true, true); // 包含续传标记和大小估计
    
    let accumulatedCode = '';
    let sizeEstimate = '';
    let continuationTriggered = false;
    
    // 第一轮数据
    await new Promise((resolve) => {
      mockStream1((data, done) => {
        if (done) {
          console.log('第一轮数据完成，检查是否触发续传...');
          resolve(null);
          return;
        }
        
        try {
          const parsed = JSON.parse(data);
          const content = parsed.choices[0].delta.content;
          
          // 检查是否包含大小估计
          if (content.includes('ESTIMATED_SIZE:')) {
            const match = content.match(/ESTIMATED_SIZE:\s*(\d+[KMG]?B)/i);
            if (match && match[1]) {
              sizeEstimate = match[1];
              console.log(`检测到大小估计: ${sizeEstimate}`);
            }
            // 注意：不将大小估计添加到代码中
            return;
          }
          
          accumulatedCode += content;
          console.log(`收到数据: ${content} | 当前累积代码长度: ${accumulatedCode.length}`);
          
          if (accumulatedCode.includes('<CONTINUE_TOKEN>')) {
            console.log('检测到续传标记！');
            continuationTriggered = true;
          }
        } catch (e) {
          console.error('解析数据失败:', e);
        }
      });
    });
    
    console.log(`代码大小估计: ${sizeEstimate || '未提供'}`);
    console.log(`是否需要续传: ${continuationTriggered ? '是' : '否'}`);
    
    // ... 后续续传测试 ...
    
    console.log('=== 测试完成 ===');
  }
  ```

- [ ] 创建测试用例，验证 Web 和 Lynx 的元数据处理和续传功能都能正确工作

## 具体修改文件

1. `src/routes/refactor_code_generate/utils/rpcSharedUtils.ts` 
   - 更新 extractContentFromJson 函数，删除批处理配置
   - 增强 detectNeedsContinuation 和 detectLynxNeedsContinuation 函数
   - 添加元数据处理函数
   - 保留 WORKFLOW_IDS 常量

2. `src/routes/refactor_code_generate/RPC/Web/WebRPCCore.ts` 
   - 修改流处理逻辑，实现完全实时更新，删除安全分割点检测
   - 增强 handleContinuation 方法，确保续传功能正常
   - 删除所有批处理和缓冲逻辑，保留续传检测
   - 添加元数据处理支持

3. `src/routes/refactor_code_generate/RPC/Lynx/LynxRPCCore.ts` 
   - 同 WebRPCCore.ts 修改流处理逻辑，实现完全实时更新
   - 确保 Lynx 的续传功能正常工作
   - 添加相同的元数据处理支持

4. `src/routes/refactor_code_generate/contexts/WebRPCContext.tsx` 和 `src/routes/refactor_code_generate/contexts/LynxRPCContext.tsx`
   - 添加元数据支持
   - 确保进度条可以接收和显示代码大小估计

5. `src/routes/refactor_code_generate/components/Chat.tsx` 
   - 检查并移除嵌套Provider
   - 确保与续传功能相关的UI正常显示

6. `src/routes/refactor_code_generate/docs/streaming_data_test.md` 
   - 更新测试文档，添加实时更新测试用例
   - 添加续传功能测试用例
   - 添加元数据处理测试用例

## 流程图：Web和Lynx数据流与元数据处理

```
接收流式数据 -> 提取内容(extractContentFromJson) 
                       |
                       v
             检查是否包含元数据标记 -> [是] -> 处理元数据(不添加到代码) -> 更新进度显示
                       |
                       v
                     [否] -> 立即更新UI
                       |
                       v
              检查续传标记 -> [是] -> 流结束时触发续传
                       |
                       v
                     [否] -> 正常结束
```

## 实施顺序和验证方法

1. 首先更新 `rpcSharedUtils.ts` 中的函数，特别是增强续传检测函数和添加元数据处理函数
2. 更新 Context 文件，添加元数据支持
3. 然后修改 WebRPCCore.ts 和 LynxRPCCore.ts 中的流处理逻辑，实现完全实时更新，彻底删除安全分割点检测和批处理逻辑，同时保留和增强续传功能及元数据处理
4. 检查并修复 Chat.tsx 中的嵌套 Provider 问题
5. 创建简单的模拟数据测试工具，验证每个数据包都能立即触发更新
6. 专门测试元数据处理功能，确保大小估计标记能被正确提取并用于更新进度条
7. 专门测试续传功能，确保能够正确检测续传标记并触发续传流程
8. 记录 Web 和 Lynx 更新的时间点和内容，确保完全实时无延迟

## 原则遵守

1. **数据流完全隔离**：确保 Web 和 Lynx 代码生成流程互不影响
2. **从源头修复**：修改核心流处理逻辑，实现真正实时的更新
3. **使用Context**：确保状态管理都通过 Context 实现，不使用 eventbus
4. **避免全局变量**：不使用 window 或全局对象存储状态
5. **完全实时更新**：收到内容立即更新，不做任何批处理、缓冲或延迟处理
6. **保护续传功能**：确保 Web 和 Lynx 的自动续传功能正常工作
7. **正确处理元数据**："ESTIMATED_SIZE: 15KB"等元数据标记不作为代码显示，而是用于更新进度条 