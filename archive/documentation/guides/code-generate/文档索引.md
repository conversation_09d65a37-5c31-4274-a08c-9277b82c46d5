# Code Generate 文档索引

本文档提供了Code Generate模块的文档索引，按类别组织。最后更新时间: 2025年 6月10日 星期二 16时28分28秒 CST

## 架构文档
- [PERFORMANCE_OPTIMIZATION_GUIDE.md](./architecture/PERFORMANCE_OPTIMIZATION_GUIDE.md) - 🚀 性能优化实施指南
- [WEB_LYNX_ARCHITECTURE_COMPLETE.md](./architecture/WEB_LYNX_ARCHITECTURE_COMPLETE.md) - Web和Lynx完整架构文档
- [2024-07-07-数据前缀处理优化.md](./architecture/2024-07-07-数据前缀处理优化.md) - ---
- [FIXED_BUGLIST.md](./architecture/FIXED_BUGLIST.md) - 修复BUG记录
- [UI优化.md](./architecture/UI优化.md) - UI 优化记录
- [WEB_LYNX_ARCHITECTURE.md](./architecture/WEB_LYNX_ARCHITECTURE.md) - Web/Lynx架构设计
- [claude-json-parser-fix.md](./architecture/claude-json-parser-fix.md) - Claude JSON解析器修复
- [architectural-overview.md](./architecture/architectural-overview.md) - 代码生成模块架构概览
- [WebRPC_LynxRPC_架构状态分析_2025_06_07.md](./architecture/WebRPC_LynxRPC_架构状态分析_2025_06_07.md) - WebRPC和LynxRPC架构状态分析
- [UNIFIED_CLAUDE_PARSER_SOLUTION.md](./architecture/UNIFIED_CLAUDE_PARSER_SOLUTION.md) - 统一Claude流式数据解析器解决方案 v2.0
- [CONTEXT_VARIABLES_GUIDE.md](./architecture/CONTEXT_VARIABLES_GUIDE.md) - 🔥 Context 和变量混淆问题解决指南
- [WORKFLOW_WebRPC_LynxRPC简化数据流方案_实施细则.md](./architecture/WORKFLOW_WebRPC_LynxRPC简化数据流方案_实施细则.md) - ---
- [归档整合方案.md](./architecture/归档整合方案.md) - 文档归档整合方案
- [ANALYSIS_性能优化方案.md](./architecture/ANALYSIS_性能优化方案.md) - ---
- [ARCHITECTURE_Refactor_Lynx_Decoupling.md](./architecture/ARCHITECTURE_Refactor_Lynx_Decoupling.md) - ---
- [lynx_code_extract_fix.md](./architecture/lynx_code_extract_fix.md) - ---
- [SWITCH_FIX_SUMMARY.md](./architecture/SWITCH_FIX_SUMMARY.md) - Switch 切换问题修复总结
- [organize_docs_plan.md](./architecture/organize_docs_plan.md) - 文档整理计划
- [LynxBridge.md](./architecture/LynxBridge.md) - https://bytedance.larkoffice.com/file/Qp68b3HFXodsocx5zR5cpMnbnYf
- [文档索引_更新版.md](./architecture/文档索引_更新版.md) - ---
- [文档归档计划.md](./architecture/文档归档计划.md) - 文档归档计划
- [EDIT_SAVE_OPTIMIZATION_REPORT.md](./architecture/EDIT_SAVE_OPTIMIZATION_REPORT.md) - 编辑保存功能优化报告
- [DOCUMENT_UPDATES_2025_06_06.md](./architecture/DOCUMENT_UPDATES_2025_06_06.md) - 2025-06-06 文档更新记录
- [文档清理摘要.md](./architecture/文档清理摘要.md) - 文档清理摘要报告
- [WebRPC_LynxRPC_并行架构实现.md](./architecture/WebRPC_LynxRPC_并行架构实现.md) - WebRPC和LynxRPC并行架构实现指南
- [CHANGELOG_20250615_DATA_PREFIX_FIX.md](./architecture/CHANGELOG_20250615_DATA_PREFIX_FIX.md) - 变更日志：修改对 data: 前缀内容的处理 - 2025-06-15
- [CONTEXT_UPDATES.md](./architecture/CONTEXT_UPDATES.md) - Context状态管理更新日志
- [69-实现一个倒计时计时器.md](./architecture/69-实现一个倒计时计时器.md) - 实现一个倒计时计时器 - Lynx代码示例
- [ANALYSIS_性能优化方案.md](./architecture/ANALYSIS_性能优化方案.md) - ---
- [ANALYSIS_日志系统重复日志问题分析.md](./architecture/ANALYSIS_日志系统重复日志问题分析.md) - ---
- [project_status_report.md](./architecture/project_status_report.md) - 项目状态报告
- [智能代码错误检测与修复.md](./architecture/智能代码错误检测与修复.md) - ---
- [ANALYSIS_流式数据更新问题分析.md](./architecture/ANALYSIS_流式数据更新问题分析.md) - ---
- [docs_restructure.md](./architecture/docs_restructure.md) - ---
- [RENAME_PLAN.md](./architecture/RENAME_PLAN.md) - ---
- [custom-events-removal-summary.md](./architecture/custom-events-removal-summary.md) - 自定义事件移除完成报告
- [Changelog.md](./architecture/Changelog.md) - 变更日志
- [OPTIMIZED_REFACTOR_PLAN.md](./architecture/OPTIMIZED_REFACTOR_PLAN.md) - 🚀 优化重构方案：高性能 + 高可维护性
- [ANALYSIS_日志系统重复日志问题分析.md](./architecture/ANALYSIS_日志系统重复日志问题分析.md) - ---
- [WEB_LYNX_ARCHITECTURE_COMPLETE.md](./architecture/WEB_LYNX_ARCHITECTURE_COMPLETE.md) - Web和Lynx完整架构文档
- [BUTTONS_FUNCTIONALITY_UPDATE.md](./architecture/BUTTONS_FUNCTIONALITY_UPDATE.md) - 按钮功能完善与优化记录
- [ARCHITECTURE_LogFlow-RPCtoPreview.md](./architecture/ARCHITECTURE_LogFlow-RPCtoPreview.md) - ---
- [DOCUMENT_RULES.md](./architecture/DOCUMENT_RULES.md) - 代码重构文档规范
- [lynx-iframe-fix-summary.md](./architecture/lynx-iframe-fix-summary.md) - Lynx Tab iframe 重复上传和反复刷新问题修复总结
- [document_update_summary_20250807.md](./architecture/document_update_summary_20250807.md) - ---
- [LynxCanvasRules.md](./architecture/LynxCanvasRules.md) - Lynx Canvas 代码规则与最佳实践
- [LYNX_PROCESSING_FLOW.md](./architecture/LYNX_PROCESSING_FLOW.md) - Lynx代码处理流程架构分析
- [WEBCODE_PARSE_ERROR_FIX.md](./architecture/WEBCODE_PARSE_ERROR_FIX.md) - WebCode 解析错误修复报告
- [LynxPE.md](./architecture/LynxPE.md) - Lynx PE 开发指南
- [fix_mobilepreview_unmount.md](./architecture/fix_mobilepreview_unmount.md) - 修复MobilePreview组件频繁卸载问题
- [API_RPC服务.md](./architecture/API_RPC服务.md) - ---
- [ERROR_HANDLING_STRATEGY.md](./architecture/ERROR_HANDLING_STRATEGY.md) - 错误处理策略
- [repeated-logs-analysis.md](./architecture/repeated-logs-analysis.md) - 页面进入时重复日志问题分析报告
- [REFACTOR_RECOMMENDATION.md](./architecture/REFACTOR_RECOMMENDATION.md) - 🔥 Context和变量重构建议
- [WORKFLOW_WebRPC_LynxRPC简化数据流方案.md](./architecture/WORKFLOW_WebRPC_LynxRPC简化数据流方案.md) - ---
- [log-optimization-summary.md](./architecture/log-optimization-summary.md) - 日志优化完成总结
- [hooks-error-analysis.md](./architecture/hooks-error-analysis.md) - Code Generate Hooks 错误分析报告
- [CONTEXT_MANAGEMENT.md](./architecture/CONTEXT_MANAGEMENT.md) - Context管理架构
- [project_status_report.md](./architecture/project_status_report.md) - 项目状态报告
- [cleanup_summary.md](./architecture/cleanup_summary.md) - 文档整理总结报告
- [DOCUMENT_UPDATE_INFO.md](./architecture/DOCUMENT_UPDATE_INFO.md) - ---
- [lynx_playground_auto_switch_fix.md](./architecture/lynx_playground_auto_switch_fix.md) - ---
- [COMMON_ISSUES.md](./architecture/COMMON_ISSUES.md) - 常见问题分析
- [LYNX_CODESIZE_WARNING_CONTRADICTION_FIX.md](./architecture/LYNX_CODESIZE_WARNING_CONTRADICTION_FIX.md) - Lynx代码大小与警告日志矛盾修复
- [docs_organization_completed.md](./architecture/docs_organization_completed.md) - 文档整理完成报告
- [FIXED_WebToLynx_Conversion_Process_20250801.md](./architecture/FIXED_WebToLynx_Conversion_Process_20250801.md) - ---
- [lynxCodeGenerator_design.md](./architecture/lynxCodeGenerator_design.md) - Lynx 和 Web 代码生成器 - 设计文档
- [web-lynx-duplicate-packet-analysis.md](./architecture/web-lynx-duplicate-packet-analysis.md) - Web vs Lynx 重复数据包问题分析
- [webcode_share_multiencode_fix.md](./architecture/webcode_share_multiencode_fix.md) - Web代码分享URL多重编码问题修复
- [COMPLETE_REFACTOR_PLAN.md](./architecture/COMPLETE_REFACTOR_PLAN.md) - 🔥 代码生成模块完整重构计划
- [prompt.md](./architecture/prompt.md) - Prompt Engineering (PE) 系统文档
- [STORAGE_OPTIMIZATION.md](./architecture/STORAGE_OPTIMIZATION.md) - ---
- [页面功能综述.md](./architecture/页面功能综述.md) - refactor_code_generate 页面功能综述
- [WORKFLOW_SessionHistoryTracking.md](./architecture/WORKFLOW_SessionHistoryTracking.md) - ---
- [MobilePreview_Architecture.md](./architecture/MobilePreview_Architecture.md) - MobilePreview 组件架构文档
- [web-to-lynx-conversion-update.md](./architecture/web-to-lynx-conversion-update.md) - Web转Lynx PE配置优化文档
- [COMMON_ERROR_PATTERNS.md](./architecture/COMMON_ERROR_PATTERNS.md) - ---
- [claude_stream_parser_integration.md](./architecture/claude_stream_parser_integration.md) - ---
- [claude_stream_sse_optimization.md](./architecture/claude_stream_sse_optimization.md) - Claude流解析器SSE格式优化
- [post_chat_hook.md](./architecture/post_chat_hook.md) - 聊天后文档更新指南
- [data-processing-analysis.md](./architecture/data-processing-analysis.md) - 流式数据处理完整性分析报告
- [lynx_code_extract_upload_fix.md](./architecture/lynx_code_extract_upload_fix.md) - ---
- [数据流与交互流程.md](./architecture/数据流与交互流程.md) - 代码生成页面数据流与交互流程分析
- [STREAM_PARSER_OPTIMIZATION.md](./architecture/STREAM_PARSER_OPTIMIZATION.md) - 流解析器优化变更记录
- [动态缓冲区实现原理.md](./architecture/动态缓冲区实现原理.md) - 动态缓冲区实现原理与风险分析
- [69-三角函数.md](./architecture/69-三角函数.md) - 三角函数 - Lynx代码示例
- [README.md](./architecture/README.md) - RPC 系统架构文档
- [ERROR_HANDLING_UPDATES.md](./architecture/ERROR_HANDLING_UPDATES.md) - 错误处理更新日志
- [claude_stream_logger_integration.md](./architecture/claude_stream_logger_integration.md) - Claude流解析器集成彩色日志系统
- [WebRPC_LynxRPC_Parallel_Clarification.md](./architecture/WebRPC_LynxRPC_Parallel_Clarification.md) - WebRPC和LynxRPC并行架构描述澄清
- [lynx_rpc_provider_hierarchy_fix.md](./architecture/lynx_rpc_provider_hierarchy_fix.md) - LynxRPCProvider层次结构问题修复报告
- [FIXED_Lynx_Switch_Tab_Solution.md](./architecture/FIXED_Lynx_Switch_Tab_Solution.md) - 修复：Lynx Tab中的Switch切换问题
- [REFACTOR_IMPLEMENTATION_EXAMPLE.md](./architecture/REFACTOR_IMPLEMENTATION_EXAMPLE.md) - 🚀 重构实施示例
- [fix-status-summary.md](./architecture/fix-status-summary.md) - Code Generate Hooks 错误分析与修复状态总结
- [CLAUDE_STREAM_SSE_AUTO_CONTINUATION.md](./architecture/CLAUDE_STREAM_SSE_AUTO_CONTINUATION.md) - ---
- [docs_organization_report.md](./architecture/docs_organization_report.md) - ---
- [文档索引.md](./architecture/文档索引.md) - 代码重构文档索引
- [WORKFLOW_流式数据更新修复计划.md](./architecture/WORKFLOW_流式数据更新修复计划.md) - ---
- [lynx_code_streaming_update_fix.md](./architecture/lynx_code_streaming_update_fix.md) - ---
- [quick-fix-guide.md](./architecture/quick-fix-guide.md) - 重复日志问题快速修复指南
- [uncaught-errors-analysis.md](./architecture/uncaught-errors-analysis.md) - 统一架构 Uncaught Errors 深度分析报告
- [CLAUDE_STREAM_STORAGE_OPTIMIZATION.md](./architecture/CLAUDE_STREAM_STORAGE_OPTIMIZATION.md) - ---
- [ARCHITECTURE_WebCodeThinkingHandling.md](./architecture/ARCHITECTURE_WebCodeThinkingHandling.md) - ---
- [智能代码错误检测与修复.md](./architecture/智能代码错误检测与修复.md) - ---
- [architecture_update_20250722.md](./architecture/architecture_update_20250722.md) - 根据上述分析，已执行对 `deleted/components/LynxPreview.tsx` 文件的编辑，移除冗余的 `renderEditButton` 函数。 
- [claude_stream_parser_object_handling.md](./architecture/claude_stream_parser_object_handling.md) - Claude Stream Parser 对象处理优化
- [docs_reorganization_plan.md](./architecture/docs_reorganization_plan.md) - 文档整理计划
- [ANALYSIS_SWITCH_TAB_ISSUE.md](./architecture/ANALYSIS_SWITCH_TAB_ISSUE.md) - 分析：Lynx Tab中的Switch切换问题
- [Lynx 多线程.md](./architecture/Lynx 多线程.md) - https://bytedance.larkoffice.com/docx/VX5CdJl6WoJJnFxnOn8cPKVGn3d
- [hooks-fix-summary.md](./architecture/hooks-fix-summary.md) - Hooks 错误修复总结
- [claude_stream_parser_optimization.md](./architecture/claude_stream_parser_optimization.md) - Claude流式数据解析器优化
- [STATE_PRESERVATION.md](./architecture/STATE_PRESERVATION.md) - 标签页状态保留机制
- [RENDER_PERFORMANCE_ANALYSIS.md](./architecture/RENDER_PERFORMANCE_ANALYSIS.md) - 🔍 重构对渲染性能的详细影响分析
- [document_organization_plan.md](./architecture/document_organization_plan.md) - 文档与文件夹整理方案
- [LynxPEUpdated.md](./architecture/LynxPEUpdated.md) - Lynx 开发规范综合指南
- [claude_stream_parser_fix.md](./architecture/claude_stream_parser_fix.md) - Claude流式数据解析器修复日志
- [README_2025-06-03T20-28-16-144Z.md](./architecture/README_2025-06-03T20-28-16-144Z.md) - refactor_code_generate 项目文档
- [2023-12-01-lynx-pink-yellow-theme.md](./architecture/2023-12-01-lynx-pink-yellow-theme.md) - ---
- [document_organization_report.md](./architecture/document_organization_report.md) - 文档整理报告
- [CLAUDE_STREAM_STORAGE_OPTIMIZATION_CHANGELOG.md](./architecture/CLAUDE_STREAM_STORAGE_OPTIMIZATION_CHANGELOG.md) - ---
- [FLOW_OPTIMIZATION.md](./architecture/FLOW_OPTIMIZATION.md) - 流处理优化
- [docs_reorganization_plan_updated.md](./architecture/docs_reorganization_plan_updated.md) - 文档整理计划（更新版）
- [DOCUMENT_UPDATES_2025_06_05.md](./architecture/DOCUMENT_UPDATES_2025_06_05.md) - 文档更新日志 (2025-06-05)
- [context-provider-fix.md](./architecture/context-provider-fix.md) - Context Provider修复
- [ARCHITECTURE_LogFlow-RPCtoPreview-Enhancements.md](./architecture/ARCHITECTURE_LogFlow-RPCtoPreview-Enhancements.md) - ---
- [provider_nesting_fix.md](./architecture/provider_nesting_fix.md) - Provider嵌套和Hooks顺序问题修复
- [CONSERVATIVE_OPTIMIZATION_PLAN.md](./architecture/CONSERVATIVE_OPTIMIZATION_PLAN.md) - 🎯 保守优化方案：高性能 + 高可维护性
- [DOCS_RESTRUCTURE_2025-06-06.md](./architecture/DOCS_RESTRUCTURE_2025-06-06.md) - 文档结构整理 - 2025-06-06
- [claude_stream_parser_test_results.md](./architecture/claude_stream_parser_test_results.md) - Claude流数据解析器测试结果
- [ANALYSIS_Architecture_Improvement_20250807.md](./architecture/ANALYSIS_Architecture_Improvement_20250807.md) - 架构问题分析与改进方案
- [ARCHITECTURE_Code_Lynx_Decoupling.md](./architecture/ARCHITECTURE_Code_Lynx_Decoupling.md) - ---
- [lynx-transmission-fix.md](./architecture/lynx-transmission-fix.md) - Lynx代码传输问题修复方案
- [final-summary.md](./architecture/final-summary.md) - 重复日志问题修复完成总结
- [debug_object_object_issue.md](./architecture/debug_object_object_issue.md) - [object Object] 问题调试方案
- [ANALYSIS_流式数据更新问题分析.md](./architecture/ANALYSIS_流式数据更新问题分析.md) - ---
- [lynx_streaming_enhancement.md](./architecture/lynx_streaming_enhancement.md) - ---
- [文档清单.md](./architecture/文档清单.md) - refactor_code_generate 文档清单
- [claude4_object_format_fix.md](./architecture/claude4_object_format_fix.md) - Claude 4 API格式处理错误修复
- [LynxDataProps.md](./architecture/LynxDataProps.md) - 1. Lynx的通信规则
- [debug_object_object_fix.md](./architecture/debug_object_object_fix.md) - [object Object] 问题修复方案
- [WebRPC_LynxRPC_并行架构实现.md](./architecture/WebRPC_LynxRPC_并行架构实现.md) - WebRPC和LynxRPC并行架构实现指南
- [lynx_web_parallel_fix.md](./architecture/lynx_web_parallel_fix.md) - ---
- [claude_stream_lynx_logging.md](./architecture/claude_stream_lynx_logging.md) - Claude 流处理器日志增强 - Lynx 无数据问题排查
- [整理方案.md](./architecture/整理方案.md) - ---
- [CLAUDE_STREAM_PROCESSING.md](./architecture/CLAUDE_STREAM_PROCESSING.md) - Claude流处理架构
- [FIXED_Lynx_Playground_Auto_Display_20250725.md](./architecture/FIXED_Lynx_Playground_Auto_Display_20250725.md) - ---
- [REACT_DATA_FLOW_GUIDE.md](./architecture/REACT_DATA_FLOW_GUIDE.md) - React数据流最佳实践指南
- [69-声母表和韵母表.md](./architecture/69-声母表和韵母表.md) - 声母表和韵母表 - Lynx代码示例
- [PERFORMANCE_ANALYSIS.md](./architecture/PERFORMANCE_ANALYSIS.md) - 🚀 性能分析：Context重构的渲染影响
- [file_organization_report.md](./architecture/file_organization_report.md) - 文件组织报告
- [CLAUDE4_OPTIMIZATION_ANALYSIS.md](./architecture/CLAUDE4_OPTIMIZATION_ANALYSIS.md) - Claude4 流式数据处理优化分析
- [文档审核清单_2025_06_07.md](./architecture/文档审核清单_2025_06_07.md) - WebRPC和LynxRPC关系文档审核清单

## API接口文档
- [refined-生日文案.md](./api/refined-生日文案.md) - 生日文案 (Lynx版本)
- [refined-生日文案祝自己.md](./api/refined-生日文案祝自己.md) - 生日文案祝自己 (Lynx版本)
- [生日祝福语.md](./api/生日祝福语.md) - 生日祝福语 - Web代码示例
- [MIGRATION_UPDATE.md](./api/MIGRATION_UPDATE.md) - 函数迁移更新日志
- [LynxSFC.md](./api/LynxSFC.md) - 1. 基本概念和结构
- [refined-朋友圈文案.md](./api/refined-朋友圈文案.md) - 朋友圈文案 (Lynx版本)
- [清朝皇帝顺序表.md](./api/清朝皇帝顺序表.md) - 清朝皇帝顺序表 - Lynx代码示例
- [REGISTRATION_OPTIMIZATION.md](./api/REGISTRATION_OPTIMIZATION.md) - 组件注册卸载优化 (2023-05-29)
- [2024年出生人口.md](./api/2024年出生人口.md) - 2024年出生人口 - Lynx代码示例
- [LynxRules.md](./api/LynxRules.md) - 本指南旨在提供Lynx框架中常用文件格式的规范和最佳实践，包括index.ttml（模板文件）、index.ttss（样式文件）、index.js（JavaScript逻辑文件）和index.json（配置文件）。由于官方文档信息有限，本指南结合了已知的Lynx框架特性和跨平台框架的通用最佳实践。
- [lynxCodeGenerator.md](./api/lynxCodeGenerator.md) - Lynx 和 Web 代码生成器
- [refined-大九九乘法口诀表.md](./api/refined-大九九乘法口诀表.md) - 大九九乘法口诀表 (Lynx版本)
- [Claude37_API_Optimization_Guide.md](./api/Claude37_API_Optimization_Guide.md) - Claude 3.7 API参数优化指南
- [react18-warnings-fix.md](./api/react18-warnings-fix.md) - React 18 警告修复
- [69-生日快乐祝福语-new.md](./api/69-生日快乐祝福语-new.md) - 生日快乐祝福语 - Lynx代码示例
- [朋友圈文案.md](./api/朋友圈文案.md) - 朋友圈文案 - Lynx代码示例
- [claude_stream_object_fix.md](./api/claude_stream_object_fix.md) - Claude流解析器对象类型修复
- [refined-元朝巅峰时期版图.md](./api/refined-元朝巅峰时期版图.md) - 元朝巅峰时期版图 (Lynx版本)
- [refined-生日祝福语.md](./api/refined-生日祝福语.md) - 生日祝福语 (Lynx版本)
- [生日快乐文案.md](./api/生日快乐文案.md) - 生日快乐文案 - Lynx代码示例
- [69-唐朝灭亡后是哪个朝代.md](./api/69-唐朝灭亡后是哪个朝代.md) - 唐朝灭亡后是哪个朝代 - Lynx代码示例
- [TOAST_SIMPLIFICATION.md](./api/TOAST_SIMPLIFICATION.md) - Toast 系统简化文档
- [refined-三角函数.md](./api/refined-三角函数.md) - 三角函数 (Lynx版本)
- [code_action_buttons.md](./api/code_action_buttons.md) - 代码操作按钮功能和逻辑分析
- [TOAST_MIGRATION_GUIDE.md](./api/TOAST_MIGRATION_GUIDE.md) - Toast 系统迁移指南
- [CLAUDE_STREAM_SSE_AUTO_CONTINUATION_CHANGELOG.md](./api/CLAUDE_STREAM_SSE_AUTO_CONTINUATION_CHANGELOG.md) - ---
- [README_OPTIMIZATION.md](./api/README_OPTIMIZATION.md) - 代码优化与结构改进指南
- [REACT18_FIX_SUMMARY.md](./api/REACT18_FIX_SUMMARY.md) - React 18 Warning Fix - Complete Solution
- [refined-明朝巅峰时期版图.md](./api/refined-明朝巅峰时期版图.md) - 明朝巅峰时期版图 (Lynx版本)
- [contentProcessors_simplify.md](./api/contentProcessors_simplify.md) - ContentProcessors 模块简化
- [refined-朋友圈文案高级感干净.md](./api/refined-朋友圈文案高级感干净.md) - 朋友圈文案高级感干净 (Lynx版本)
- [refined-生日快乐文案.md](./api/refined-生日快乐文案.md) - 生日快乐文案 (Lynx版本)
- [refined-儿子生日文案男孩专用.md](./api/refined-儿子生日文案男孩专用.md) - 儿子生日文案男孩专用 (Lynx版本)
- [69-2025第一季度gdp.md](./api/69-2025第一季度gdp.md) - 2025第一季度gdp - Lynx Direct PE代码示例
- [API_RPC服务.md](./api/API_RPC服务.md) - ---
- [生日快乐祝福语.md](./api/生日快乐祝福语.md) - 生日快乐祝福语 - Lynx代码示例
- [error_handling_improvements.md](./api/error_handling_improvements.md) - Claude流数据错误处理优化
- [tab_switch_logic_fix.md](./api/tab_switch_logic_fix.md) - 标签切换逻辑优化与修复
- [PE_INTEGRATION_SUMMARY.md](./api/PE_INTEGRATION_SUMMARY.md) - Lynx PE 整合与优化总结
- [RPC_README.md](./api/RPC_README.md) - AI流式数据处理架构分析与实现
- [React_Warning_Fix_Summary.md](./api/React_Warning_Fix_Summary.md) - React Warning Fix Summary
- [cleanup_summary.md](./api/cleanup_summary.md) - 代码清理总结报告
- [LynxDom.md](./api/LynxDom.md) - [使用说明](https://bytedance.larkoffice.com/docx/WonYdhTMeoIb9dxmR27cnBENnx7)
- [refined-个性签名短句干净.md](./api/refined-个性签名短句干净.md) - 个性签名短句干净 (Lynx版本)
- [react18-warning-fix.md](./api/react18-warning-fix.md) - React 18 Warning Suppression System
- [LynxFiles.md](./api/LynxFiles.md) - 本指南旨在提供Lynx框架中常用文件格式的规范和最佳实践，包括index.ttml（模板文件）、index.ttss（样式文件）、index.js（JavaScript逻辑文件）和index.json（配置文件）。由于官方文档信息有限，本指南结合了已知的Lynx框架特性和跨平台框架的通用最佳实践。
- [claude4-api-compatibility.md](./api/claude4-api-compatibility.md) - Claude 4 API兼容性更新
- [ERROR_NOTIFICATION_INTEGRATION.md](./api/ERROR_NOTIFICATION_INTEGRATION.md) - 错误通知系统集成指南
- [LYNX_PE_CONFIGURATION_SUMMARY.md](./api/LYNX_PE_CONFIGURATION_SUMMARY.md) - Lynx PE 配置调整总结
- [TOAST_UPGRADE_SUMMARY.md](./api/TOAST_UPGRADE_SUMMARY.md) - Toast 系统升级总结
- [contentProcessors_fix.md](./api/contentProcessors_fix.md) - ContentProcessors 模块命名冲突修复
- [69-2024年出生人口.md](./api/69-2024年出生人口.md) - 2024年出生人口 - Lynx代码示例
- [refined-中国真实人口有多少.md](./api/refined-中国真实人口有多少.md) - 中国真实人口有多少 (Lynx版本)
- [CLEANUP_CLAUDE_PARSER.md](./api/CLEANUP_CLAUDE_PARSER.md) - Claude解析器优化方案
- [UNIFIED_TOAST_SYSTEM_SUMMARY.md](./api/UNIFIED_TOAST_SYSTEM_SUMMARY.md) - 统一 Toast 系统总结
- [Claude37_Optimization_Summary.md](./api/Claude37_Optimization_Summary.md) - Claude 3.7 API参数优化总结
- [生日文案祝自己.md](./api/生日文案祝自己.md) - 生日文案祝自己 - Web代码示例
- [README.md](./api/README.md) - 待办事项列表
- [refined-拼音26个字母读法表.md](./api/refined-拼音26个字母读法表.md) - 拼音26个字母读法表 (Lynx版本)
- [claude_stream_parser_unification.md](./api/claude_stream_parser_unification.md) - Claude解析器统一重构
- [event-listener-to-context-refactor.md](./api/event-listener-to-context-refactor.md) - 🚀 事件监听器到 Context 重构完成报告
- [claude-4-streaming-analysis.md](./api/claude-4-streaming-analysis.md) - Claude 4.0流式接口重复数据包技术分析与优化
- [refined-清朝皇帝顺序表.md](./api/refined-清朝皇帝顺序表.md) - 清朝皇帝顺序表 (Lynx版本)
- [chat_generation_behavior_fix.md](./api/chat_generation_behavior_fix.md) - Chat组件代码生成行为修复
- [web_only_code_generation_fix.md](./api/web_only_code_generation_fix.md) - Web代码生成按钮修复
- [Toast管理方案.md](./api/Toast管理方案.md) - Toast 消息管理方案
- [69-清朝皇帝顺序表.md](./api/69-清朝皇帝顺序表.md) - 清朝皇帝顺序表 - Lynx代码示例
- [initialization-fix-summary.md](./api/initialization-fix-summary.md) - 页面加载时初始化错误修复总结
- [claude_stream_parser_continuation_detection.md](./api/claude_stream_parser_continuation_detection.md) - Claude 流式数据解析器优化 - 续传检测增强
- [ARCHITECTURE_WebCodeThinkingHandling.md](./api/ARCHITECTURE_WebCodeThinkingHandling.md) - ---
- [cleanup_report.md](./api/cleanup_report.md) - 代码清理报告
- [CONTEXT_USAGE_GUIDELINES.md](./api/CONTEXT_USAGE_GUIDELINES.md) - React Context使用规范
- [百数表.md](./api/百数表.md) - 百数表 - Lynx代码示例
- [refined-两块大白兔奶糖的热量.md](./api/refined-两块大白兔奶糖的热量.md) - 两块大白兔奶糖的热量 (Lynx版本)
- [refined-县城gdp排名全国.md](./api/refined-县城gdp排名全国.md) - 县城gdp排名全国 (Lynx版本)
- [refined-中国现在有多少亿人口.md](./api/refined-中国现在有多少亿人口.md) - 中国现在有多少亿人口 (Lynx版本)
- [UNIFIED_TOAST_USAGE.md](./api/UNIFIED_TOAST_USAGE.md) - 统一 Toast 系统使用指南
- [fix_codehighlight_display.md](./api/fix_codehighlight_display.md) - 修复CodeHighlight显示问题
- [unused_files_analysis.md](./api/unused_files_analysis.md) - refactor_code_generate目录未被使用的文件分析
- [69-发朋友圈的文案.md](./api/69-发朋友圈的文案.md) - 发朋友圈的文案 - Lynx代码示例
- [生日文案.md](./api/生日文案.md) - 生日文案 - Lynx代码示例
- [唐朝灭亡后是哪个朝代.md](./api/唐朝灭亡后是哪个朝代.md) - 唐朝灭亡后是哪个朝代 - Lynx代码示例
- [refined-唐朝灭亡后是哪个朝代.md](./api/refined-唐朝灭亡后是哪个朝代.md) - 唐朝灭亡后是哪个朝代 (Lynx版本)
- [claude-duplicate-packet-fix.md](./api/claude-duplicate-packet-fix.md) - Claude 4.0重复数据包修复方案
- [FIXED_Claude_Stream_Parser_HTML_Content_20250729.md](./api/FIXED_Claude_Stream_Parser_HTML_Content_20250729.md) - Claude流式解析器HTML内容处理问题修复
- [refined-中国实际人口有多少人.md](./api/refined-中国实际人口有多少人.md) - 中国实际人口有多少人 (Lynx版本)
- [refined-声母表和韵母表.md](./api/refined-声母表和韵母表.md) - 声母表和韵母表 (Lynx版本)
- [CodeHeader.md](./api/CodeHeader.md) - CodeHeader 组件
- [LYNX_REALTIME_DISPLAY_FIX.md](./api/LYNX_REALTIME_DISPLAY_FIX.md) - Lynx 实时显示修复方案
- [refined-生日快乐祝福语.md](./api/refined-生日快乐祝福语.md) - 生日快乐祝福语 (Lynx版本)
- [CODE_QUALITY_IMPROVEMENTS.md](./api/CODE_QUALITY_IMPROVEMENTS.md) - 🔍 代码质量改进：注释和语义化
- [lynx_code_generator_summary.md](./api/lynx_code_generator_summary.md) - Lynx 代码生成器项目总结
- [WORKFLOW_流式数据更新问题修复方案.md](./api/WORKFLOW_流式数据更新问题修复方案.md) - ---
- [实现一个倒计时计时器.md](./api/实现一个倒计时计时器.md) - 实现一个倒计时计时器 - Web代码示例
- [refined-实现一个倒计时计时器.md](./api/refined-实现一个倒计时计时器.md) - 实现一个倒计时计时器 (Lynx版本)
- [refined-指差法.md](./api/refined-指差法.md) - 指差法 (Lynx版本)
- [RPC-claude-support-added.md](./api/RPC-claude-support-added.md) - Claude 4支持优化
- [refined-2025第一季度gdp.md](./api/refined-2025第一季度gdp.md) - 2025第一季度gdp (Lynx版本)
- [message_handler_optimization.md](./api/message_handler_optimization.md) - 消息处理器优化与组件渲染循环修复
- [refined-百数表.md](./api/refined-百数表.md) - 百数表 (Lynx版本)
- [2025第一季度gdp.md](./api/2025第一季度gdp.md) - 2025第一季度gdp - Lynx代码示例
- [FIXED_Lynx_Playground_Auto_Display_20250725_2025-06-03T20-28-16-130Z.md](./api/FIXED_Lynx_Playground_Auto_Display_20250725_2025-06-03T20-28-16-130Z.md) - Lynx标签页Playground自动显示功能修复报告
- [refined-26个拼音字母表.md](./api/refined-26个拼音字母表.md) - 26个拼音字母表 (Lynx版本)
- [个性签名短句干净.md](./api/个性签名短句干净.md) - 个性签名短句干净
- [enhanced-debug-panel-usage.md](./api/enhanced-debug-panel-usage.md) - 增强版 Lynx Debug Panel 使用说明
- [Toast修复示例.md](./api/Toast修复示例.md) - Toast 重复显示问题修复示例
- [HOOK_USAGE_GUIDE.md](./api/HOOK_USAGE_GUIDE.md) - 🚀 新Hook使用指南
- [BUG分析-Switch切换和Toast重复.md](./api/BUG分析-Switch切换和Toast重复.md) - Lynx Tab 视图切换与 Toast 通知 Bug 分析
- [CLAUDE_PARSER_MIGRATION_COMPLETE.md](./api/CLAUDE_PARSER_MIGRATION_COMPLETE.md) - Claude流解析器完全迁移日志
- [refined-明朝皇帝顺序表.md](./api/refined-明朝皇帝顺序表.md) - 明朝皇帝顺序表 (Lynx版本)
- [refined-2024年出生人口.md](./api/refined-2024年出生人口.md) - 2024年出生人口 (Lynx版本)
- [RPC_TEST_RESPONSE_DATA.md](./api/RPC_TEST_RESPONSE_DATA.md) - {"id":"20250528173834E5D903460A34D4AEBBC7","created":1748453921,"model":"aws_sdk_claude4_opus","choices":[{"delta":{"content":"ESTIMATED_SIZE:"},"index":0,"stop_reason":""}],"usage":{}}
- [JSDOC_TEMPLATES.md](./api/JSDOC_TEMPLATES.md) - 📝 JSDoc注释模板和规范
- [CLAUDE_STREAM_UPDATES.md](./api/CLAUDE_STREAM_UPDATES.md) - Claude流处理更新日志
- [refined-发朋友圈的文案.md](./api/refined-发朋友圈的文案.md) - 发朋友圈的文案 (Lynx版本)
- [REEXTRACT_UPLOAD_DEBUG_GUIDE.md](./api/REEXTRACT_UPLOAD_DEBUG_GUIDE.md) - 重新解构上传功能调试指南
- [CONTEXT_PROVIDER_FIX.md](./api/CONTEXT_PROVIDER_FIX.md) - Context Provider 结构修复
- [LynxCopy.md](./api/LynxCopy.md) - https://bytedance.larkoffice.com/file/Du95bqVdzonywWxeV41cbYzunhf

## 问题分析
- [MobilePreview-file-structure-cleanup.md](./analysis/MobilePreview-file-structure-cleanup.md) - ---
- [UI优化.md](./analysis/UI优化.md) - UI 优化记录
- [MobilePreview-ui-restored.md](./analysis/MobilePreview-ui-restored.md) - MobilePreview UI恢复
- [69-指差法.md](./analysis/69-指差法.md) - 查询：指差法
- [2023-12-04-tab-click-fix.md](./analysis/2023-12-04-tab-click-fix.md) - ---
- [FIXED_Tab_Reorder_Mobile_First_20250731.md](./analysis/FIXED_Tab_Reorder_Mobile_First_20250731.md) - ---
- [resizable_sidebar_fix.md](./analysis/resizable_sidebar_fix.md) - 修复sidebar拖拽调整大小功能
- [ARCHITECTURE_Refactor_Lynx_Decoupling.md](./analysis/ARCHITECTURE_Refactor_Lynx_Decoupling.md) - ---
- [CLAUDE37_JSON_STREAM_FIX.md](./analysis/CLAUDE37_JSON_STREAM_FIX.md) - Claude 3.7 JSON流式数据处理修复方案
- [FIXED_UI_Layout_StrongFix_20250731.md](./analysis/FIXED_UI_Layout_StrongFix_20250731.md) - ---
- [CHANGELOG_LynxTabSwitch_Fix_20250606.md](./analysis/CHANGELOG_LynxTabSwitch_Fix_20250606.md) - Lynx Tab 切换修复记录 (2025-06-06)
- [webcode-optimization-strategy.md](./analysis/webcode-optimization-strategy.md) - Web Code 字符丢失问题优化策略
- [69-中国实际人口有多少人.md](./analysis/69-中国实际人口有多少人.md) - 中国实际人口有多少人 - Lynx代码示例
- [chat_scrolling_fix.md](./analysis/chat_scrolling_fix.md) - CodeHighlight滚动问题修复
- [69-两块大白兔奶糖的热量.md](./analysis/69-两块大白兔奶糖的热量.md) - 两块大白兔奶糖的热量 - Lynx代码示例
- [MobilePreview-UI-enhanced.md](./analysis/MobilePreview-UI-enhanced.md) - MobilePreview UI 增强
- [COMPONENT_SAFETY_RULES.md](./analysis/COMPONENT_SAFETY_RULES.md) - 🛡️ 组件安全编码规则
- [INFINITE_RERENDER_SOLUTION_SUMMARY.md](./analysis/INFINITE_RERENDER_SOLUTION_SUMMARY.md) - 🎯 无限重渲染问题解决方案总结
- [tab_selection_fix.md](./analysis/tab_selection_fix.md) - Tab选择状态错位问题修复
- [codehighlight_scroll_fix.md](./analysis/codehighlight_scroll_fix.md) - CodeHighlight 滚动问题修复
- [UI_ENHANCEMENT.md](./analysis/UI_ENHANCEMENT.md) - UI界面美化升级记录
- [统一问题分析.md](./analysis/统一问题分析.md) - 重构代码生成功能问题分析
- [UI_COMPONENT_UPDATES.md](./analysis/UI_COMPONENT_UPDATES.md) - UI组件更新日志
- [FIXED_Lynx_Less_Files_Extraction_20250728.md](./analysis/FIXED_Lynx_Less_Files_Extraction_20250728.md) - Lynx Less文件解析问题修复
- [FIXED_LynxTab_Icon_Change_20250731.md](./analysis/FIXED_LynxTab_Icon_Change_20250731.md) - ---
- [69-拼音26个字母读法表.md](./analysis/69-拼音26个字母读法表.md) - 拼音26个字母读法表 - Lynx代码示例
- [post_chat_hook.md](./analysis/post_chat_hook.md) - 聊天后文档更新指南
- [README.md](./analysis/README.md) - FIXED文档目录
- [MobilePreview-ui-restored-20250724.md](./analysis/MobilePreview-ui-restored-20250724.md) - MobilePreview UI 恢复与优化
- [指差法.md](./analysis/指差法.md) - 查询：指差法
- [中国真实人口有多少.md](./analysis/中国真实人口有多少.md) - ```lynx
- [FIXED_Lynx_Switch_Tab_20250606.md](./analysis/FIXED_Lynx_Switch_Tab_20250606.md) - 修复Lynx Tab中的Switch切换问题
- [component_undefined_class_fix.md](./analysis/component_undefined_class_fix.md) - LynxCodeHighlight组件undefined类名修复
- [PREVENT_INFINITE_RERENDER_GUIDE.md](./analysis/PREVENT_INFINITE_RERENDER_GUIDE.md) - 🚨 防止无限重渲染错误指南
- [mobile-preview-resize-feature.md](./analysis/mobile-preview-resize-feature.md) - Mobile模拟器Resize功能设计方案
- [architecture_update_20250722.md](./analysis/architecture_update_20250722.md) - 根据上述分析，已执行对 `deleted/components/LynxPreview.tsx` 文件的编辑，移除冗余的 `renderEditButton` 函数。 
- [ANALYSIS_SWITCH_TAB_ISSUE.md](./analysis/ANALYSIS_SWITCH_TAB_ISSUE.md) - 分析：Lynx Tab中的Switch切换问题
- [chat-ui-simplified.md](./analysis/chat-ui-simplified.md) - Chat组件样式简化修复方案
- [FIXED_UI_Layout_Optimization_20250731.md](./analysis/FIXED_UI_Layout_Optimization_20250731.md) - ---
- [两块大白兔奶糖的热量.md](./analysis/两块大白兔奶糖的热量.md) - ```lynx
- [chat-ui-readability-fix.md](./analysis/chat-ui-readability-fix.md) - Chat组件文字可读性修复方案
- [LYNX_ERROR_HANDLING_FIX.md](./analysis/LYNX_ERROR_HANDLING_FIX.md) - Lynx错误处理日志级别修复
- [69-大九九乘法口诀表.md](./analysis/69-大九九乘法口诀表.md) - 大九九乘法口诀表 - Lynx代码示例
- [FIXED_PreviewTsx_Complete_Refactor_20250731.md](./analysis/FIXED_PreviewTsx_Complete_Refactor_20250731.md) - ---
- [FIXED_BUGS.md](./analysis/FIXED_BUGS.md) - 已修复的Bug列表
- [lynx-tab-flicker-fix.md](./analysis/lynx-tab-flicker-fix.md) - Lynx Tab 闪动问题修复报告
- [中国实际人口有多少人.md](./analysis/中国实际人口有多少人.md) - // 中国实际人口有多少人
- [ARCHITECTURE_Code_Lynx_Decoupling.md](./analysis/ARCHITECTURE_Code_Lynx_Decoupling.md) - ---
- [mobilepreview_screenshot_plan.md](./analysis/mobilepreview_screenshot_plan.md) - MobilePreview 截图存储方案分析
- [FIXED_PreviewTsx_Syntax_Error_20250731.md](./analysis/FIXED_PreviewTsx_Syntax_Error_20250731.md) - ---
- [FIXED_Lynx_Switch_Toast_20250729.md](./analysis/FIXED_Lynx_Switch_Toast_20250729.md) - Lynx标签页视图切换和Toast重复问题修复
- [MobilePreview-ui-improved.md](./analysis/MobilePreview-ui-improved.md) - ---
- [ESLINT_AUTO_FIX_CONFIG.md](./analysis/ESLINT_AUTO_FIX_CONFIG.md) - ---
- [web_code_share_url_fix.md](./analysis/web_code_share_url_fix.md) - Web代码分享URL加载失败问题修复
- [MobilePreview-tabs-fixed-20250724.md](./analysis/MobilePreview-tabs-fixed-20250724.md) - MobilePreview 导航标签宽度修复

## 已修复问题
- [生日祝福语.md](./fixed/生日祝福语.md) - 生日祝福语 - Lynx代码示例
- [FIXED_Tab_Reorder_Mobile_First_20250731.md](./fixed/FIXED_Tab_Reorder_Mobile_First_20250731.md) - ---
- [lynx_code_extract_fix.md](./fixed/lynx_code_extract_fix.md) - ---
- [LynxCSS.md](./fixed/LynxCSS.md) - Lynx 框架中存在以下特殊的 CSS 规则和禁止使用的属性，基于参考信息归纳如下：
- [FIXED_UI_Layout_StrongFix_20250731.md](./fixed/FIXED_UI_Layout_StrongFix_20250731.md) - ---
- [CHANGELOG.md](./fixed/CHANGELOG.md) - 变更日志
- [chat_scrolling_fix.md](./fixed/chat_scrolling_fix.md) - CodeHighlight滚动问题修复
- [声母表和韵母表.md](./fixed/声母表和韵母表.md) - 声母表和韵母表 - Lynx代码示例
- [69-中国现在有多少亿人口.md](./fixed/69-中国现在有多少亿人口.md) - 中国现在有多少亿人口 - Lynx代码示例
- [朋友圈文案高级感干净.md](./fixed/朋友圈文案高级感干净.md) - 朋友圈文案高级感干净 - Web代码示例
- [儿子生日文案男孩专用.md](./fixed/儿子生日文案男孩专用.md) - <!DOCTYPE html>
- [lynx_playground_auto_switch_fix.md](./fixed/lynx_playground_auto_switch_fix.md) - ---
- [FIXED_Lynx_Less_Files_Extraction_20250728.md](./fixed/FIXED_Lynx_Less_Files_Extraction_20250728.md) - Lynx Less文件解析问题修复
- [FIXED_LynxTab_Icon_Change_20250731.md](./fixed/FIXED_LynxTab_Icon_Change_20250731.md) - ---
- [FIXED_WebToLynx_Conversion_Process_20250801.md](./fixed/FIXED_WebToLynx_Conversion_Process_20250801.md) - ---
- [webcode_share_multiencode_fix.md](./fixed/webcode_share_multiencode_fix.md) - Web代码分享URL多重编码问题修复
- [lynx_code_extract_upload_fix.md](./fixed/lynx_code_extract_upload_fix.md) - ---
- [README.md](./fixed/README.md) - FIXED文档目录
- [lynx_rpc_provider_hierarchy_fix.md](./fixed/lynx_rpc_provider_hierarchy_fix.md) - LynxRPCProvider层次结构问题修复报告
- [FIXED_Lynx_Switch_Tab_Solution.md](./fixed/FIXED_Lynx_Switch_Tab_Solution.md) - 修复：Lynx Tab中的Switch切换问题
- [FIXED_Lynx_Switch_Tab_20250606.md](./fixed/FIXED_Lynx_Switch_Tab_20250606.md) - 修复Lynx Tab中的Switch切换问题
- [lynx_code_streaming_update_fix.md](./fixed/lynx_code_streaming_update_fix.md) - ---
- [FIXED_UI_Layout_Optimization_20250731.md](./fixed/FIXED_UI_Layout_Optimization_20250731.md) - ---
- [中国现在有多少亿人口.md](./fixed/中国现在有多少亿人口.md) - 中国现在有多少亿人口 - Lynx代码示例
- [FIXED_PreviewTsx_Complete_Refactor_20250731.md](./fixed/FIXED_PreviewTsx_Complete_Refactor_20250731.md) - ---
- [69-生日祝福语.md](./fixed/69-生日祝福语.md) - 生日祝福语 - Lynx代码示例
- [FIXED_Claude_Stream_Parser_HTML_Content_20250729.md](./fixed/FIXED_Claude_Stream_Parser_HTML_Content_20250729.md) - Claude流式解析器HTML内容处理问题修复
- [FIXED_PreviewTsx_Syntax_Error_20250731.md](./fixed/FIXED_PreviewTsx_Syntax_Error_20250731.md) - ---
- [lynx_streaming_enhancement.md](./fixed/lynx_streaming_enhancement.md) - ---
- [2025第一季度gdp.md](./fixed/2025第一季度gdp.md) - 2025第一季度gdp - Web代码示例
- [FIXED_Lynx_Playground_Auto_Display_20250725_2025-06-03T20-28-16-130Z.md](./fixed/FIXED_Lynx_Playground_Auto_Display_20250725_2025-06-03T20-28-16-130Z.md) - Lynx标签页Playground自动显示功能修复报告
- [lynx_web_parallel_fix.md](./fixed/lynx_web_parallel_fix.md) - ---
- [FIXED_Lynx_Switch_Toast_20250729.md](./fixed/FIXED_Lynx_Switch_Toast_20250729.md) - Lynx标签页视图切换和Toast重复问题修复
- [69-生日快乐祝福语.md](./fixed/69-生日快乐祝福语.md) - 生日快乐祝福语 - Lynx代码示例
- [FIXED_Lynx_Playground_Auto_Display_20250725.md](./fixed/FIXED_Lynx_Playground_Auto_Display_20250725.md) - ---
- [LynxGeneral.md](./fixed/LynxGeneral.md) - 使用说明https://bytedance.larkoffice.com/docx/LBpgdyeidozqNHxjvULcbOcInlf
- [web_code_share_url_fix.md](./fixed/web_code_share_url_fix.md) - Web代码分享URL加载失败问题修复
- [RPC_TEST_RESPONSE_DATA.md](./fixed/RPC_TEST_RESPONSE_DATA.md) - {"id":"20250528173834E5D903460A34D4AEBBC7","created":1748453921,"model":"aws_sdk_claude4_opus","choices":[{"delta":{"content":"ESTIMATED_SIZE:"},"index":0,"stop_reason":""}],"usage":{}}
- [三角函数.md](./fixed/三角函数.md) - 三角函数 - Lynx代码示例
- [69-个性签名短句干净.md](./fixed/69-个性签名短句干净.md) - 个性签名短句干净 - Lynx代码示例

## 功能指南
- [WORKFLOW_WebRPC_LynxRPC简化数据流方案_实施细则.md](./guides/WORKFLOW_WebRPC_LynxRPC简化数据流方案_实施细则.md) - ---
- [WORKFLOW_WebRPC_LynxRPC简化数据流方案.md](./guides/WORKFLOW_WebRPC_LynxRPC简化数据流方案.md) - ---
- [WORKFLOW_SessionHistoryTracking.md](./guides/WORKFLOW_SessionHistoryTracking.md) - ---
- [CodeHeader.md](./guides/CodeHeader.md) - CodeHeader 组件
- [mobilepreview_screenshot_plan.md](./guides/mobilepreview_screenshot_plan.md) - MobilePreview 截图存储方案分析
- [README.md](./guides/README.md) - WORKFLOW文档目录
- [WORKFLOW_流式数据更新修复计划.md](./guides/WORKFLOW_流式数据更新修复计划.md) - ---
- [changelog.md](./guides/changelog.md) - 
- [WORKFLOW_流式数据更新问题修复方案.md](./guides/WORKFLOW_流式数据更新问题修复方案.md) - ---
- [REACT_DATA_FLOW_GUIDE.md](./guides/REACT_DATA_FLOW_GUIDE.md) - React数据流最佳实践指南

## 功能说明
- [README.md](./readme/README.md) - readme 目录
- [功能列表.md](./readme/功能列表.md) - 功能列表

## 变更记录
- [MobilePreview-file-structure-cleanup.md](./changelog/MobilePreview-file-structure-cleanup.md) - ---
- [2024-07-07-数据前缀处理优化.md](./changelog/2024-07-07-数据前缀处理优化.md) - ---
- [MIGRATION_UPDATE.md](./changelog/MIGRATION_UPDATE.md) - 函数迁移更新日志
- [FIXED_BUGLIST.md](./changelog/FIXED_BUGLIST.md) - 修复BUG记录
- [MobilePreview-ui-restored.md](./changelog/MobilePreview-ui-restored.md) - MobilePreview UI恢复
- [claude-json-parser-fix.md](./changelog/claude-json-parser-fix.md) - Claude JSON解析器修复
- [2023-12-04-tab-click-fix.md](./changelog/2023-12-04-tab-click-fix.md) - ---
- [resizable_sidebar_fix.md](./changelog/resizable_sidebar_fix.md) - 修复sidebar拖拽调整大小功能
- [SWITCH_FIX_SUMMARY.md](./changelog/SWITCH_FIX_SUMMARY.md) - Switch 切换问题修复总结
- [REGISTRATION_OPTIMIZATION.md](./changelog/REGISTRATION_OPTIMIZATION.md) - 组件注册卸载优化 (2023-05-29)
- [react18-warnings-fix.md](./changelog/react18-warnings-fix.md) - React 18 警告修复
- [DOCUMENT_UPDATES_2025_06_06.md](./changelog/DOCUMENT_UPDATES_2025_06_06.md) - 2025-06-06 文档更新记录
- [CHANGELOG_20250615_DATA_PREFIX_FIX.md](./changelog/CHANGELOG_20250615_DATA_PREFIX_FIX.md) - 变更日志：修改对 data: 前缀内容的处理 - 2025-06-15
- [claude_stream_object_fix.md](./changelog/claude_stream_object_fix.md) - Claude流解析器对象类型修复
- [CONTEXT_UPDATES.md](./changelog/CONTEXT_UPDATES.md) - Context状态管理更新日志
- [69-百数表.md](./changelog/69-百数表.md) - 百数表 - Lynx代码示例
- [docs_restructure.md](./changelog/docs_restructure.md) - ---
- [CHANGELOG.md](./changelog/CHANGELOG.md) - 变更日志
- [BUTTONS_FUNCTIONALITY_UPDATE.md](./changelog/BUTTONS_FUNCTIONALITY_UPDATE.md) - 按钮功能完善与优化记录
- [CLAUDE_STREAM_SSE_AUTO_CONTINUATION_CHANGELOG.md](./changelog/CLAUDE_STREAM_SSE_AUTO_CONTINUATION_CHANGELOG.md) - ---
- [MobilePreview-UI-enhanced.md](./changelog/MobilePreview-UI-enhanced.md) - MobilePreview UI 增强
- [tab_selection_fix.md](./changelog/tab_selection_fix.md) - Tab选择状态错位问题修复
- [contentProcessors_simplify.md](./changelog/contentProcessors_simplify.md) - ContentProcessors 模块简化
- [codehighlight_scroll_fix.md](./changelog/codehighlight_scroll_fix.md) - CodeHighlight 滚动问题修复
- [fix_mobilepreview_unmount.md](./changelog/fix_mobilepreview_unmount.md) - 修复MobilePreview组件频繁卸载问题
- [县城gdp排名全国.md](./changelog/县城gdp排名全国.md) - 县城gdp排名全国 - Lynx代码示例
- [UI_ENHANCEMENT.md](./changelog/UI_ENHANCEMENT.md) - UI界面美化升级记录
- [UI_COMPONENT_UPDATES.md](./changelog/UI_COMPONENT_UPDATES.md) - UI组件更新日志
- [error_handling_improvements.md](./changelog/error_handling_improvements.md) - Claude流数据错误处理优化
- [tab_switch_logic_fix.md](./changelog/tab_switch_logic_fix.md) - 标签切换逻辑优化与修复
- [26个拼音字母表.md](./changelog/26个拼音字母表.md) - <!DOCTYPE html>
- [docs_organization_completed.md](./changelog/docs_organization_completed.md) - 文档整理完成报告
- [STORAGE_OPTIMIZATION.md](./changelog/STORAGE_OPTIMIZATION.md) - ---
- [claude4-api-compatibility.md](./changelog/claude4-api-compatibility.md) - Claude 4 API兼容性更新
- [contentProcessors_fix.md](./changelog/contentProcessors_fix.md) - ContentProcessors 模块命名冲突修复
- [claude_stream_parser_integration.md](./changelog/claude_stream_parser_integration.md) - ---
- [claude_stream_sse_optimization.md](./changelog/claude_stream_sse_optimization.md) - Claude流解析器SSE格式优化
- [CLEANUP_CLAUDE_PARSER.md](./changelog/CLEANUP_CLAUDE_PARSER.md) - Claude解析器优化方案
- [STREAM_PARSER_OPTIMIZATION.md](./changelog/STREAM_PARSER_OPTIMIZATION.md) - 流解析器优化变更记录
- [README.md](./changelog/README.md) - WORKFLOW文档目录
- [ERROR_HANDLING_UPDATES.md](./changelog/ERROR_HANDLING_UPDATES.md) - 错误处理更新日志
- [claude_stream_logger_integration.md](./changelog/claude_stream_logger_integration.md) - Claude流解析器集成彩色日志系统
- [WebRPC_LynxRPC_Parallel_Clarification.md](./changelog/WebRPC_LynxRPC_Parallel_Clarification.md) - WebRPC和LynxRPC并行架构描述澄清
- [claude_stream_parser_unification.md](./changelog/claude_stream_parser_unification.md) - Claude解析器统一重构
- [MobilePreview-ui-restored-20250724.md](./changelog/MobilePreview-ui-restored-20250724.md) - MobilePreview UI 恢复与优化
- [CLAUDE_STREAM_SSE_AUTO_CONTINUATION.md](./changelog/CLAUDE_STREAM_SSE_AUTO_CONTINUATION.md) - ---
- [component_undefined_class_fix.md](./changelog/component_undefined_class_fix.md) - LynxCodeHighlight组件undefined类名修复
- [claude_stream_parser_continuation_detection.md](./changelog/claude_stream_parser_continuation_detection.md) - Claude 流式数据解析器优化 - 续传检测增强
- [CLAUDE_STREAM_STORAGE_OPTIMIZATION.md](./changelog/CLAUDE_STREAM_STORAGE_OPTIMIZATION.md) - ---
- [claude_stream_parser_object_handling.md](./changelog/claude_stream_parser_object_handling.md) - Claude Stream Parser 对象处理优化
- [claude_stream_parser_optimization.md](./changelog/claude_stream_parser_optimization.md) - Claude流式数据解析器优化
- [claude_stream_parser_fix.md](./changelog/claude_stream_parser_fix.md) - Claude流式数据解析器修复日志
- [LYNX_ERROR_HANDLING_FIX.md](./changelog/LYNX_ERROR_HANDLING_FIX.md) - Lynx错误处理日志级别修复
- [2023-12-01-lynx-pink-yellow-theme.md](./changelog/2023-12-01-lynx-pink-yellow-theme.md) - ---
- [fix_codehighlight_display.md](./changelog/fix_codehighlight_display.md) - 修复CodeHighlight显示问题
- [CLAUDE_STREAM_STORAGE_OPTIMIZATION_CHANGELOG.md](./changelog/CLAUDE_STREAM_STORAGE_OPTIMIZATION_CHANGELOG.md) - ---
- [FLOW_OPTIMIZATION.md](./changelog/FLOW_OPTIMIZATION.md) - 流处理优化
- [DOCUMENT_UPDATES_2025_06_05.md](./changelog/DOCUMENT_UPDATES_2025_06_05.md) - 文档更新日志 (2025-06-05)
- [context-provider-fix.md](./changelog/context-provider-fix.md) - Context Provider修复
- [provider_nesting_fix.md](./changelog/provider_nesting_fix.md) - Provider嵌套和Hooks顺序问题修复
- [2023-10-31-lynx-progress-color.md](./changelog/2023-10-31-lynx-progress-color.md) - ---
- [FIXED_BUGS.md](./changelog/FIXED_BUGS.md) - 已修复的Bug列表
- [rename_verification.md](./changelog/rename_verification.md) - 重命名验证报告
- [DOCS_RESTRUCTURE_2025-06-06.md](./changelog/DOCS_RESTRUCTURE_2025-06-06.md) - 文档结构整理 - 2025-06-06
- [claude_stream_parser_test_results.md](./changelog/claude_stream_parser_test_results.md) - Claude流数据解析器测试结果
- [debug_object_object_issue.md](./changelog/debug_object_object_issue.md) - [object Object] 问题调试方案
- [claude4_object_format_fix.md](./changelog/claude4_object_format_fix.md) - Claude 4 API格式处理错误修复
- [debug_object_object_fix.md](./changelog/debug_object_object_fix.md) - [object Object] 问题修复方案
- [实现一个倒计时计时器.md](./changelog/实现一个倒计时计时器.md) - 实现一个倒计时计时器 - Lynx代码示例
- [RPC-claude-support-added.md](./changelog/RPC-claude-support-added.md) - Claude 4支持优化
- [message_handler_optimization.md](./changelog/message_handler_optimization.md) - 消息处理器优化与组件渲染循环修复
- [MobilePreview-ui-improved.md](./changelog/MobilePreview-ui-improved.md) - ---
- [claude_stream_lynx_logging.md](./changelog/claude_stream_lynx_logging.md) - Claude 流处理器日志增强 - Lynx 无数据问题排查
- [ESLINT_AUTO_FIX_CONFIG.md](./changelog/ESLINT_AUTO_FIX_CONFIG.md) - ---
- [CLAUDE_PARSER_MIGRATION_COMPLETE.md](./changelog/CLAUDE_PARSER_MIGRATION_COMPLETE.md) - Claude流解析器完全迁移日志
- [CLAUDE_STREAM_UPDATES.md](./changelog/CLAUDE_STREAM_UPDATES.md) - Claude流处理更新日志
- [CONTEXT_PROVIDER_FIX.md](./changelog/CONTEXT_PROVIDER_FIX.md) - Context Provider 结构修复
- [MobilePreview-tabs-fixed-20250724.md](./changelog/MobilePreview-tabs-fixed-20250724.md) - MobilePreview 导航标签宽度修复

