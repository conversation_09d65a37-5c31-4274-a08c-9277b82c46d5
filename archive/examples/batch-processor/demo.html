<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>L2W Engine Demo</title>
  <style>
    body { font-family: sans-serif; margin: 20px; }
    #app-container {
      border: 1px solid #ccc;
      padding: 10px;
      width: 375px; /* Simulate a mobile screen width */
      height: 667px; /* Simulate a mobile screen height */
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
  </style>
</head>
<body>

  <h1>L2W Engine Demo</h1>
  <p>This page demonstrates the Lynx-to-Web rendering engine.</p>

  <div id="app-container"></div>

  <!-- The bundled demo script will be included here -->
  <!-- For a real test, you would need a bundler like Webpack or Vite -->
  <script type="module" src="./demo.js"></script>

</body>
</html>