import { L2WEngine } from './index';

// --- Sample Lynx Application ---

// 1. Sample TTML
const ttml = `
  <view class="container">
    <text class="title">{{title}}</text>
    <view class="button" bindtap="handleTap">
      <text>Click Me</text>
    </view>
  </view>
`;

// 2. Sample TTSS
const ttss = `
  .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #f0f0f0;
    height: 100%;
  }
  .title {
    font-size: 40rpx;
    color: #333;
    margin-top: 50rpx;
  }
  .button {
    margin-top: 30rpx;
    padding: 20rpx 40rpx;
    background-color: #007aff;
    border-radius: 10rpx;
  }
  .button text {
    color: #ffffff;
  }
`;

// 3. Sample Data
const data = {
  title: 'Welcome to L2W Engine!',
};

// 4. Sample Event Handlers
// These need to be on the window object for the event system to find them.
(window as any).lynxEventHandlers = {
  handleTap: (event: any) => {
    console.log('Button tapped!', event);
    alert('But<PERSON> was clicked!');
  },
};

// --- Engine Initialization ---

document.addEventListener('DOMContentLoaded', () => {
  const container = document.getElementById('app-container');

  if (container) {
    // Instantiate the engine
    const engine = new L2WEngine(container);

    // Render the Lynx app
    engine.render(ttml, ttss, data);
  } else {
    console.error('App container not found!');
  }
});