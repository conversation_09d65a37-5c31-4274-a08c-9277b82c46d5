/**
 * 三阶段深化模式集成示例
 * Three-Stage Enhancement Integration Example
 * 
 * 展示如何在现有的EnhancedBatchProcessorService中最小化集成三阶段深化功能
 * 保持完全的向后兼容性，提供可选的增强模式
 */

import { 
  ThreeStageEnhancementAdapter, 
  createEnabledThreeStageAdapter,
  ThreeStageEnhancementConfig 
} from '../services/ThreeStageEnhancementAdapter';

import { BatchConfig, ProcessResult } from '../types';

/**
 * 增强版批处理配置接口
 */
export interface EnhancedBatchConfig extends BatchConfig {
  // 三阶段增强选项（可选）
  threeStageOptions?: {
    enable: boolean;
    config?: ThreeStageEnhancementConfig;
  };
}

/**
 * 最小化集成示例类
 * 展示如何在不修改现有代码的情况下集成三阶段功能
 */
export class EnhancedBatchProcessorWithThreeStage {
  private threeStageAdapter: ThreeStageEnhancementAdapter;
  
  constructor() {
    // 创建三阶段适配器（默认关闭）
    this.threeStageAdapter = new ThreeStageEnhancementAdapter({
      enableThreeStage: false, // 默认关闭，保持向后兼容
    });
  }

  /**
   * 增强版任务处理方法
   * 在现有逻辑基础上添加三阶段支持
   */
  async processTaskWithEnhancement(
    userQuery: string,
    config: EnhancedBatchConfig
  ): Promise<ProcessResult> {
    
    // 检查是否启用三阶段模式
    const enableThreeStage = config.threeStageOptions?.enable || false;
    
    if (enableThreeStage) {
      // 更新适配器配置
      if (config.threeStageOptions?.config) {
        this.threeStageAdapter.updateConfig({
          enableThreeStage: true,
          ...config.threeStageOptions.config,
        });
      } else {
        this.threeStageAdapter.updateConfig({ enableThreeStage: true });
      }
    }

    // 构建增强版system prompt
    const enhancedSystemPrompt = this.threeStageAdapter.buildEnhancedSystemPrompt(
      userQuery,
      config
    );

    // 这里调用现有的处理逻辑，只是替换了system prompt
    return await this.processWithEnhancedPrompt(
      userQuery,
      enhancedSystemPrompt,
      config
    );
  }

  /**
   * 使用增强prompt进行处理（模拟现有逻辑）
   */
  private async processWithEnhancedPrompt(
    userQuery: string,
    systemPrompt: string,
    config: EnhancedBatchConfig
  ): Promise<ProcessResult> {
    
    const startTime = Date.now();
    
    try {
      // 这里是模拟的AI调用逻辑
      // 在实际集成中，这里应该是现有的AI接口调用
      console.log('Processing with enhanced prompt...');
      console.log(`Prompt length: ${systemPrompt.length} characters`);
      console.log(`Three-stage enabled: ${config.threeStageOptions?.enable || false}`);
      
      // 模拟处理结果
      const result: ProcessResult = {
        id: `task-${Date.now()}`,
        status: 'completed',
        lynxCode: '// Enhanced Lynx code would be here',
        webCode: '// Enhanced Web code would be here',
        error: null,
        timestamp: Date.now(),
        processingTime: Date.now() - startTime,
      };

      return result;
      
    } catch (error) {
      console.error('Enhanced processing failed:', error);
      
      // 错误时可以降级到标准模式
      if (this.threeStageAdapter.getConfig().fallbackOptions?.fallbackOnError) {
        console.warn('Falling back to standard processing mode');
        return await this.processWithStandardMode(userQuery, config);
      }
      
      throw error;
    }
  }

  /**
   * 标准模式处理（降级方案）
   */
  private async processWithStandardMode(
    userQuery: string,
    config: BatchConfig
  ): Promise<ProcessResult> {
    // 使用原有的标准prompt进行处理
    console.log('Processing with standard mode...');
    
    return {
      id: `task-${Date.now()}`,
      status: 'completed',
      lynxCode: '// Standard Lynx code',
      webCode: '// Standard Web code',
      error: null,
      timestamp: Date.now(),
      processingTime: 100,
    };
  }

  /**
   * 批量处理任务（支持三阶段增强）
   */
  async processBatchTasks(
    tasks: Array<{ query: string; config: EnhancedBatchConfig }>
  ): Promise<ProcessResult[]> {
    
    const results: ProcessResult[] = [];
    
    for (const task of tasks) {
      try {
        const result = await this.processTaskWithEnhancement(
          task.query,
          task.config
        );
        results.push(result);
      } catch (error) {
        console.error(`Task processing failed: ${task.query}`, error);
        results.push({
          id: `task-${Date.now()}`,
          status: 'failed',
          lynxCode: '',
          webCode: '',
          error: error.message,
          timestamp: Date.now(),
          processingTime: 0,
        });
      }
    }
    
    return results;
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return this.threeStageAdapter.getPerformanceStats();
  }

  /**
   * 启用三阶段模式
   */
  enableThreeStageMode(config?: ThreeStageEnhancementConfig): void {
    this.threeStageAdapter.updateConfig({
      enableThreeStage: true,
      ...config,
    });
  }

  /**
   * 禁用三阶段模式
   */
  disableThreeStageMode(): void {
    this.threeStageAdapter.updateConfig({
      enableThreeStage: false,
    });
  }
}

/**
 * 使用示例函数
 */
export async function demonstrateThreeStageIntegration() {
  // 创建增强版处理器
  const processor = new EnhancedBatchProcessorWithThreeStage();
  
  // 示例1：标准模式处理
  console.log('=== 示例1：标准模式处理 ===');
  const standardResult = await processor.processTaskWithEnhancement(
    '创建一个简单的待办事项应用',
    {
      concurrency: 1,
      timeout: 10000,
      threeStageOptions: {
        enable: false, // 关闭三阶段模式
      },
    }
  );
  console.log('标准模式结果:', standardResult);

  // 示例2：三阶段深化模式处理
  console.log('\n=== 示例2：三阶段深化模式处理 ===');
  const enhancedResult = await processor.processTaskWithEnhancement(
    '创建一个具有数据可视化功能的复杂仪表板应用，包含图表、统计分析和交互式界面',
    {
      concurrency: 1,
      timeout: 15000,
      threeStageOptions: {
        enable: true, // 启用三阶段模式
        config: {
          qualityEnhancement: {
            enableDeepAnalysis: true,
            enableDesignOptimization: true,
            enableCodeExcellence: true,
          },
          performanceOptions: {
            enableStatisticsLogging: true,
          },
        },
      },
    }
  );
  console.log('三阶段模式结果:', enhancedResult);

  // 示例3：批量处理（混合模式）
  console.log('\n=== 示例3：批量处理（混合模式） ===');
  const batchResults = await processor.processBatchTasks([
    {
      query: '简单的计算器应用',
      config: {
        concurrency: 1,
        timeout: 10000,
        threeStageOptions: { enable: false }, // 简单任务用标准模式
      },
    },
    {
      query: '复杂的电商购物车系统，包含商品展示、价格计算、优惠券系统和支付界面',
      config: {
        concurrency: 1,
        timeout: 20000,
        threeStageOptions: { enable: true }, // 复杂任务用三阶段模式
      },
    },
  ]);
  console.log('批量处理结果:', batchResults);

  // 获取性能统计
  const stats = processor.getPerformanceStats();
  console.log('\n=== 性能统计 ===');
  console.log(stats);
}

/**
 * 快速创建启用三阶段的处理器
 */
export function createThreeStageEnabledProcessor(): EnhancedBatchProcessorWithThreeStage {
  const processor = new EnhancedBatchProcessorWithThreeStage();
  processor.enableThreeStageMode({
    qualityEnhancement: {
      enableDeepAnalysis: true,
      enableDesignOptimization: true,
      enableCodeExcellence: true,
    },
    performanceOptions: {
      enablePromptCaching: true,
      enableStatisticsLogging: true,
    },
  });
  return processor;
}

export default EnhancedBatchProcessorWithThreeStage;