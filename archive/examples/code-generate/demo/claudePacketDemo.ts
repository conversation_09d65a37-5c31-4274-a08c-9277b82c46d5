/**
 * Claude包序列检测器演示
 *
 * 展示基于真实Claude接口格式差异的精准检测效果
 */

import {
  ClaudePacketSequenceDetector,
  processClaudePacketSequence,
  resetClaudePacketSequence,
} from '../claudePacketSequenceDetector';

/**
 * 模拟真实的Claude接口数据包
 */
function createMockPackets() {
  // 正常的增量包（基于真实格式）
  const normalPackets = [
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"<!DOCTYPE html>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n<html lang=\\"en\\">"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n<head>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n  <meta charset=\\"UTF-8\\">"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n  <meta name=\\"viewport\\" content=\\"width=device-width, initial-scale=1.0\\">"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n  <title>Test Page</title>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n</head>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n<body>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n  <h1>Hello World</h1>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n  <p>This is a test page.</p>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n</body>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n</html>"}}]}',
  ];

  // 最后的重复包（包含所有之前的内容）
  const duplicatePacket =
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"<!DOCTYPE html>\\n<html lang=\\"en\\">\\n<head>\\n  <meta charset=\\"UTF-8\\">\\n  <meta name=\\"viewport\\" content=\\"width=device-width, initial-scale=1.0\\">\\n  <title>Test Page</title>\\n</head>\\n<body>\\n  <h1>Hello World</h1>\\n  <p>This is a test page.</p>\\n</body>\\n</html>"}}]}';

  return { normalPackets, duplicatePacket };
}

/**
 * 演示没有检测器的情况（会出现重复）
 */
function demoWithoutDetector() {
  console.log('\n🚫 演示：没有检测器的情况（会出现重复）');
  console.log('='.repeat(60));

  const { normalPackets, duplicatePacket } = createMockPackets();
  let accumulator = '';

  // 处理正常包
  normalPackets.forEach((packet, index) => {
    const jsonStr = packet.substring(5).trim();
    const parsed = JSON.parse(jsonStr);
    const { content } = parsed.choices[0].delta;
    accumulator += content;
    console.log(`包 ${index + 1}: +${content.length}字符`);
  });

  console.log(`\n正常包处理完成，总长度: ${accumulator.length}字符`);

  // 处理重复包（没有检测）
  const jsonStr = duplicatePacket.substring(5).trim();
  const parsed = JSON.parse(jsonStr);
  const duplicateContent = parsed.choices[0].delta.content;
  accumulator += duplicateContent; // 直接累积，导致重复

  console.log(`\n❌ 重复包累积后，总长度: ${accumulator.length}字符`);
  console.log(`❌ 重复包大小: ${duplicateContent.length}字符`);
  console.log('❌ 结果：内容重复了！');

  return accumulator;
}

/**
 * 演示使用检测器的情况（阻止重复）
 */
function demoWithDetector() {
  console.log('\n✅ 演示：使用检测器的情况（阻止重复）');
  console.log('='.repeat(60));

  const detector = new ClaudePacketSequenceDetector();
  const { normalPackets, duplicatePacket } = createMockPackets();
  let accumulator = '';

  // 处理正常包
  normalPackets.forEach((packet, index) => {
    const result = detector.processPacket(packet);
    if (result) {
      accumulator += result;
      console.log(`包 ${index + 1}: +${result.length}字符`);
    }
  });

  console.log(`\n正常包处理完成，总长度: ${accumulator.length}字符`);

  // 处理重复包（使用检测器）
  const result = detector.processPacket(duplicatePacket);
  if (result === null) {
    console.log('\n🎯 重复包被检测器阻止！');
  } else {
    accumulator += result;
    console.log(`\n重复包累积: +${result.length}字符`);
  }

  console.log(`\n✅ 最终总长度: ${accumulator.length}字符`);
  console.log('✅ 结果：没有重复内容！');

  // 输出统计信息
  const stats = detector.getStats();
  const analysis = detector.getPacketAnalysis();

  console.log('\n📊 检测统计:');
  console.log(`  - 总包数: ${stats.totalPackets}`);
  console.log(`  - 平均包大小: ${stats.averagePacketSize.toFixed(1)}字符`);
  console.log(`  - 正常包: ${analysis.normalPackets}`);
  console.log(`  - 大包: ${analysis.largePackets}`);
  console.log(`  - HTML包: ${analysis.htmlPackets}`);

  return accumulator;
}

/**
 * 演示便捷函数的使用
 */
function demoConvenienceFunctions() {
  console.log('\n🔧 演示：便捷函数的使用');
  console.log('='.repeat(60));

  // 重置检测器
  resetClaudePacketSequence();

  const { normalPackets, duplicatePacket } = createMockPackets();
  let accumulator = '';

  // 使用便捷函数处理包
  [...normalPackets, duplicatePacket].forEach((packet, index) => {
    const result = processClaudePacketSequence(packet);
    if (result !== null) {
      accumulator += result;
      console.log(`包 ${index + 1}: ✅ 处理成功 (+${result.length}字符)`);
    } else {
      console.log(`包 ${index + 1}: 🚫 被阻止（重复包）`);
    }
  });

  console.log(`\n最终内容长度: ${accumulator.length}字符`);
}

/**
 * 主演示函数
 */
export function runClaudePacketDemo() {
  console.log('🎯 Claude包序列检测器演示');
  console.log('基于真实的Claude接口格式差异进行精准检测');
  console.log('='.repeat(80));

  // 演示1：没有检测器的情况
  const withoutDetector = demoWithoutDetector();

  // 演示2：使用检测器的情况
  const withDetector = demoWithDetector();

  // 演示3：便捷函数的使用
  demoConvenienceFunctions();

  // 对比结果
  console.log('\n📈 对比结果:');
  console.log('='.repeat(60));
  console.log(`没有检测器: ${withoutDetector.length}字符 (包含重复)`);
  console.log(`使用检测器: ${withDetector.length}字符 (无重复)`);
  console.log(`节省字符数: ${withoutDetector.length - withDetector.length}`);
  console.log(
    `重复率: ${(((withoutDetector.length - withDetector.length) / withoutDetector.length) * 100).toFixed(1)}%`,
  );

  console.log('\n🎉 演示完成！检测器成功阻止了重复内容的累积。');
}

// 直接运行演示
runClaudePacketDemo();
