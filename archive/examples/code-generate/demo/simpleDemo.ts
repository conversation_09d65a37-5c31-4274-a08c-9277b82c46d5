/**
 * 简化的Claude包序列检测器演示
 * 不依赖外部logger，直接使用console
 */

// 简化的检测器实现（用于演示）
interface PacketInfo {
  index: number;
  length: number;
  contentLength: number;
  timestamp: number;
  hasCompleteHtml: boolean;
  isLikelyIncremental: boolean;
}

class SimpleClaudePacketDetector {
  private packets: PacketInfo[] = [];
  private totalContentLength = 0;

  processPacket(chunk: string): string | null {
    if (!chunk || typeof chunk !== 'string') {
      return null;
    }

    // 只处理data:格式的包
    if (!chunk.startsWith('data:')) {
      return chunk;
    }

    try {
      const jsonStr = chunk.substring(5).trim();
      if (jsonStr === '[DONE]') {
        return null;
      }

      const parsed = JSON.parse(jsonStr);
      const deltaContent = parsed?.choices?.[0]?.delta?.content;

      if (!deltaContent || typeof deltaContent !== 'string') {
        return null;
      }

      // 分析包的特征
      const packetInfo: PacketInfo = {
        index: this.packets.length,
        length: chunk.length,
        contentLength: deltaContent.length,
        timestamp: Date.now(),
        hasCompleteHtml:
          deltaContent.includes('<!DOCTYPE html') &&
          deltaContent.includes('</html>'),
        isLikelyIncremental: this.isLikelyIncrementalContent(deltaContent),
      };

      // 检测是否为重复包
      if (this.isLastDuplicatePacket(packetInfo, deltaContent)) {
        console.warn('🚫 检测到重复数据包，已阻止累积', {
          packetIndex: packetInfo.index,
          contentLength: packetInfo.contentLength,
          totalPackets: this.packets.length,
        });
        return null;
      }

      // 记录正常包
      this.packets.push(packetInfo);
      this.totalContentLength += deltaContent.length;

      return deltaContent;
    } catch (error) {
      console.debug('解析data包失败:', error);
      return null;
    }
  }

  private isLastDuplicatePacket(
    currentPacket: PacketInfo,
    content: string,
  ): boolean {
    // 检测条件1：内容长度异常大
    if (currentPacket.contentLength > 100) {
      // 降低阈值用于演示
      if (this.packets.length > 3) {
        const avgPacketSize = this.totalContentLength / this.packets.length;
        if (currentPacket.contentLength > avgPacketSize * 3) {
          // 降低倍数用于演示
          console.log(
            `🔍 检测到异常大的包: ${currentPacket.contentLength} vs 平均 ${avgPacketSize.toFixed(1)}`,
          );
          return true;
        }
      }
    }

    // 检测条件2：包含完整HTML且已经处理过HTML
    if (currentPacket.hasCompleteHtml) {
      const hasProcessedHtml = this.packets.some(p => p.hasCompleteHtml);
      if (hasProcessedHtml) {
        console.log('🔍 检测到重复的完整HTML文档');
        return true;
      }
    }

    // 检测条件3：内容长度接近累积总长度
    if (
      this.totalContentLength > 50 && // 降低阈值用于演示
      currentPacket.contentLength > this.totalContentLength * 0.8
    ) {
      console.log(
        `🔍 检测到内容长度接近累积总长度: ${currentPacket.contentLength} vs ${this.totalContentLength}`,
      );
      return true;
    }

    return false;
  }

  private isLikelyIncrementalContent(content: string): boolean {
    if (content.length > 1000) {
      return false;
    }
    if (content.includes('<!DOCTYPE html') && content.includes('</html>')) {
      return false;
    }
    return true;
  }

  getStats() {
    return {
      totalPackets: this.packets.length,
      totalContentLength: this.totalContentLength,
      averagePacketSize:
        this.packets.length > 0
          ? this.totalContentLength / this.packets.length
          : 0,
    };
  }

  reset() {
    this.packets = [];
    this.totalContentLength = 0;
  }
}

/**
 * 创建模拟数据包
 */
function createMockPackets() {
  const normalPackets = [
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"<!DOCTYPE html>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n<html lang=\\"en\\">"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n<head>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n  <title>Test Page</title>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n</head>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n<body>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n  <h1>Hello World</h1>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n</body>"}}]}',
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"\\n</html>"}}]}',
  ];

  const duplicatePacket =
    'data: {"id":"msg_01K_claude4_sonnet","choices":[{"delta":{"content":"<!DOCTYPE html>\\n<html lang=\\"en\\">\\n<head>\\n  <title>Test Page</title>\\n</head>\\n<body>\\n  <h1>Hello World</h1>\\n</body>\\n</html>"}}]}';

  return { normalPackets, duplicatePacket };
}

/**
 * 演示没有检测器的情况
 */
function demoWithoutDetector() {
  console.log('\n🚫 演示：没有检测器的情况（会出现重复）');
  console.log('='.repeat(60));

  const { normalPackets, duplicatePacket } = createMockPackets();
  let accumulator = '';

  // 处理正常包
  normalPackets.forEach((packet, index) => {
    const jsonStr = packet.substring(5).trim();
    const parsed = JSON.parse(jsonStr);
    const { content } = parsed.choices[0].delta;
    accumulator += content;
    console.log(`包 ${index + 1}: +${content.length}字符`);
  });

  console.log(`\n正常包处理完成，总长度: ${accumulator.length}字符`);

  // 处理重复包（没有检测）
  const jsonStr = duplicatePacket.substring(5).trim();
  const parsed = JSON.parse(jsonStr);
  const duplicateContent = parsed.choices[0].delta.content;
  accumulator += duplicateContent;

  console.log(`\n❌ 重复包累积后，总长度: ${accumulator.length}字符`);
  console.log(`❌ 重复包大小: ${duplicateContent.length}字符`);
  console.log('❌ 结果：内容重复了！');

  return accumulator;
}

/**
 * 演示使用检测器的情况
 */
function demoWithDetector() {
  console.log('\n✅ 演示：使用检测器的情况（阻止重复）');
  console.log('='.repeat(60));

  const detector = new SimpleClaudePacketDetector();
  const { normalPackets, duplicatePacket } = createMockPackets();
  let accumulator = '';

  // 处理正常包
  normalPackets.forEach((packet, index) => {
    const result = detector.processPacket(packet);
    if (result) {
      accumulator += result;
      console.log(`包 ${index + 1}: +${result.length}字符`);
    }
  });

  console.log(`\n正常包处理完成，总长度: ${accumulator.length}字符`);

  // 处理重复包（使用检测器）
  const result = detector.processPacket(duplicatePacket);
  if (result === null) {
    console.log('\n🎯 重复包被检测器阻止！');
  } else {
    accumulator += result;
    console.log(`\n重复包累积: +${result.length}字符`);
  }

  console.log(`\n✅ 最终总长度: ${accumulator.length}字符`);
  console.log('✅ 结果：没有重复内容！');

  // 输出统计信息
  const stats = detector.getStats();
  console.log('\n📊 检测统计:');
  console.log(`  - 总包数: ${stats.totalPackets}`);
  console.log(`  - 平均包大小: ${stats.averagePacketSize.toFixed(1)}字符`);

  return accumulator;
}

/**
 * 主演示函数
 */
function runDemo() {
  console.log('🎯 Claude包序列检测器演示');
  console.log('基于真实的Claude接口格式差异进行精准检测');
  console.log('='.repeat(80));

  // 演示1：没有检测器的情况
  const withoutDetector = demoWithoutDetector();

  // 演示2：使用检测器的情况
  const withDetector = demoWithDetector();

  // 对比结果
  console.log('\n📈 对比结果:');
  console.log('='.repeat(60));
  console.log(`没有检测器: ${withoutDetector.length}字符 (包含重复)`);
  console.log(`使用检测器: ${withDetector.length}字符 (无重复)`);
  console.log(`节省字符数: ${withoutDetector.length - withDetector.length}`);
  console.log(
    `重复率: ${(((withoutDetector.length - withDetector.length) / withoutDetector.length) * 100).toFixed(1)}%`,
  );

  console.log('\n🎉 演示完成！检测器成功阻止了重复内容的累积。');
}

// 运行演示
runDemo();
