# 26个拼音字母表 - Lynx Direct PE代码示例

## 查询内容
```
26个拼音字母表
```

## 生成的Lynx Direct PE代码

```
<FILES>
<FILE path="/src/index.js">
export const card = {
  data: {
    // 拼音字母数据
    letters: [
      { char: 'a', pinyin: 'a', type: '单韵母', pronunciation: '阿', examples: ['啊(a)', '阿(ā)', '安(ān)'] },
      { char: 'b', pinyin: 'bo', type: '声母', pronunciation: '玻', examples: ['爸(bà)', '北(běi)', '本(běn)'] },
      { char: 'c', pinyin: 'ce', type: '声母', pronunciation: '刺', examples: ['擦(cā)', '从(cóng)', '匆(cōng)'] },
      { char: 'd', pinyin: 'de', type: '声母', pronunciation: '得', examples: ['大(dà)', '懂(dǒng)', '动(dòng)'] },
      { char: 'e', pinyin: 'e', type: '单韵母', pronunciation: '厄', examples: ['饿(è)', '额(é)', '恩(ēn)'] },
      { char: 'f', pinyin: 'fo', type: '声母', pronunciation: '佛', examples: ['发(fā)', '方(fāng)', '丰(fēng)'] },
      { char: 'g', pinyin: 'ge', type: '声母', pronunciation: '哥', examples: ['高(gāo)', '工(gōng)', '国(guó)'] },
      { char: 'h', pinyin: 'he', type: '声母', pronunciation: '喝', examples: ['好(hǎo)', '黑(hēi)', '红(hóng)'] },
      { char: 'i', pinyin: 'yi', type: '单韵母', pronunciation: '衣', examples: ['一(yī)', '以(yǐ)', '亿(yì)'] },
      { char: 'j', pinyin: 'ji', type: '声母', pronunciation: '基', examples: ['加(jiā)', '金(jīn)', '近(jìn)'] },
      { char: 'k', pinyin: 'ke', type: '声母', pronunciation: '科', examples: ['开(kāi)', '看(kàn)', '口(kǒu)'] },
      { char: 'l', pinyin: 'le', type: '声母', pronunciation: '乐', examples: ['来(lái)', '蓝(lán)', '浪(làng)'] },
      { char: 'm', pinyin: 'mo', type: '声母', pronunciation: '摸', examples: ['妈(mā)', '猫(māo)', '名(míng)'] },
      { char: 'n', pinyin: 'ne', type: '声母', pronunciation: '呢', examples: ['那(nà)', '难(nán)', '年(nián)'] },
      { char: 'o', pinyin: 'o', type: '单韵母', pronunciation: '喔', examples: ['哦(ò)', '欧(ōu)', '藕(ǒu)'] },
      { char: 'p', pinyin: 'po', type: '声母', pronunciation: '坡', examples: ['拍(pāi)', '跑(pǎo)', '朋(péng)'] },
      { char: 'q', pinyin: 'qi', type: '声母', pronunciation: '欺', examples: ['七(qī)', '前(qián)', '去(qù)'] },
      { char: 'r', pinyin: 're', type: '声母', pronunciation: '日', examples: ['然(rán)', '热(rè)', '人(rén)'] },
      { char: 's', pinyin: 'se', type: '声母', pronunciation: '丝', examples: ['三(sān)', '色(sè)', '丝(sī)'] },
      { char: 't', pinyin: 'te', type: '声母', pronunciation: '特', examples: ['他(tā)', '天(tiān)', '听(tīng)'] },
      { char: 'u', pinyin: 'wu', type: '单韵母', pronunciation: '乌', examples: ['屋(wū)', '无(wú)', '五(wǔ)'] },
      { char: 'v', pinyin: 'v/ü', type: '单韵母', pronunciation: '迂', examples: ['鱼(yú)', '玉(yù)', '雨(yǔ)'] },
      { char: 'w', pinyin: 'wu', type: '声母辅助字母', pronunciation: '物', examples: ['挖(wā)', '湾(wān)', '万(wàn)'] },
      { char: 'x', pinyin: 'xi', type: '声母', pronunciation: '希', examples: ['西(xī)', '小(xiǎo)', '休(xiū)'] },
      { char: 'y', pinyin: 'yi', type: '声母辅助字母', pronunciation: '衣', examples: ['牙(yá)', '言(yán)', '月(yuè)'] },
      { char: 'z', pinyin: 'zi', type: '声母', pronunciation: '资', examples: ['在(zài)', '早(zǎo)', '走(zǒu)'] }
    ],
    
    // 声母列表
    shengmu: ['b', 'p', 'm', 'f', 'd', 't', 'n', 'l', 'g', 'k', 'h', 'j', 'q', 'x', 'zh', 'ch', 'sh', 'r', 'z', 'c', 's', 'y', 'w'],
    
    // 韵母列表
    danYunmu: ['a', 'o', 'e', 'i', 'u', 'ü'],
    fuYunmu: ['ai', 'ei', 'ui', 'ao', 'ou', 'iu', 'ie', 'üe', 'er'],
    biYunmu: ['an', 'en', 'in', 'un', 'ün', 'ang', 'eng', 'ing', 'ong'],
    
    // 当前选中的字母
    selectedLetter: null,
    selectedLetterDetail: null,
    
    // 展示状态
    activeTab: 'overview', // overview, categories, detail
  },
  
  // 组件挂载时执行
  mounted() {
    // 默认选中第一个字母
    this.selectLetter('a');
  },
  
  methods: {
    // 选择字母
    selectLetter(letter) {
      this.selectedLetter = letter;
      
      // 查找并设置字母详情
      const letterDetail = this.letters.find(item => item.char === letter);
      if (letterDetail) {
        this.selectedLetterDetail = letterDetail;
        // 自动切换到详情标签
        this.activeTab = 'detail';
      }
    },
    
    // 切换标签页
    switchTab(tab) {
      this.activeTab = tab;
    },
    
    // 获取字母样式类
    getLetterClass(letter) {
      const classes = ['letter-card'];
      if (this.selectedLetter === letter) {
        classes.push('selected');
      }
      
      // 根据类型添加不同的样式
      const letterObj = this.letters.find(item => item.char === letter);
      if (letterObj) {
        if (letterObj.type.includes('声母')) {
          classes.push('consonant');
        } else if (letterObj.type.includes('韵母')) {
          classes.push('vowel');
        }
      }
      
      return classes.join(' ');
    },
    
    // 处理发音示范
    pronounceLetter(letter) {
      // 实际应用中可以播放音频
      const message = `正在发音: ${letter}`;
      lynx.showToast({
        content: message,
        duration: 1000
      });
    }
  }
}
</FILE>

<FILE path="/src/index.ttml">
<view class="pinyin-container">
  <!-- 顶部标题区域 -->
  <view class="header">
    <text class="title">26个汉语拼音字母表</text>
    <text class="subtitle">标准汉语拼音学习与参考</text>
  </view>
  
  <!-- 导航标签页 -->
  <view class="tabs">
    <view class="tab {{activeTab === 'overview' ? 'active' : ''}}" 
          bindtap="switchTab" 
          data-tab="overview">
      <text>字母总览</text>
    </view>
    <view class="tab {{activeTab === 'categories' ? 'active' : ''}}" 
          bindtap="switchTab" 
          data-tab="categories">
      <text>分类展示</text>
    </view>
    <view class="tab {{activeTab === 'detail' ? 'active' : ''}}" 
          bindtap="switchTab" 
          data-tab="detail">
      <text>字母详情</text>
    </view>
  </view>
  
  <!-- 内容区域 -->
  <scroll-view class="content-scroll" scroll-y="true">
    <!-- 字母总览面板 -->
    <view class="panel {{activeTab === 'overview' ? 'active' : 'hidden'}}">
      <view class="section alphabet-section">
        <view class="section-header">
          <text class="section-title">拼音字母表</text>
          <text class="section-subtitle">点击字母查看详情</text>
        </view>
        
        <view class="alphabet-grid">
          <view tt:for="{{letters}}" 
                tt:key="char"
                class="{{getLetterClass(item.char)}}" 
                bindtap="selectLetter" 
                data-letter="{{item.char}}">
            <text class="letter-char">{{item.char}}</text>
            <text class="letter-pinyin">{{item.pinyin}}</text>
          </view>
        </view>
        
        <view class="legend">
          <view class="legend-item">
            <view class="legend-color consonant"></view>
            <text class="legend-text">声母</text>
          </view>
          <view class="legend-item">
            <view class="legend-color vowel"></view>
            <text class="legend-text">韵母</text>
          </view>
        </view>
      </view>
      
      <view class="section intro-section">
        <view class="section-header">
          <text class="section-title">拼音简介</text>
        </view>
        
        <view class="intro-content">
          <text class="intro-text">汉语拼音由26个字母组成，包括声母、韵母和特殊字母。拼音是标准汉语(普通话)的注音系统，帮助正确发音。</text>
          <text class="intro-text">声母(23个)：是音节开头的辅音；韵母(24个)：由单韵母、复韵母和鼻韵母组成，是音节中携带声调的部分。</text>
        </view>
      </view>
    </view>
    
    <!-- 分类展示面板 -->
    <view class="panel {{activeTab === 'categories' ? 'active' : 'hidden'}}">
      <view class="section category-section">
        <!-- 声母部分 -->
        <view class="category-item">
          <view class="category-header">
            <text class="category-title">声母 (23个)</text>
            <text class="category-desc">音节起始的辅音部分</text>
          </view>
          <view class="category-letters">
            <view tt:for="{{shengmu}}" 
                  tt:key="item" 
                  class="category-letter" 
                  bindtap="selectLetter" 
                  data-letter="{{item}}">
              <text>{{item}}</text>
            </view>
          </view>
        </view>
        
        <!-- 单韵母部分 -->
        <view class="category-item">
          <view class="category-header">
            <text class="category-title">单韵母 (6个)</text>
            <text class="category-desc">单一元音组成的韵母</text>
          </view>
          <view class="category-letters">
            <view tt:for="{{danYunmu}}" 
                  tt:key="item" 
                  class="category-letter vowel" 
                  bindtap="selectLetter" 
                  data-letter="{{item}}">
              <text>{{item}}</text>
            </view>
          </view>
        </view>
        
        <!-- 复韵母部分 -->
        <view class="category-item">
          <view class="category-header">
            <text class="category-title">复韵母 (9个)</text>
            <text class="category-desc">由两个或多个元音组成的韵母</text>
          </view>
          <view class="category-letters wrap">
            <view tt:for="{{fuYunmu}}" 
                  tt:key="item" 
                  class="category-letter vowel" 
                  bindtap="pronounceLetter" 
                  data-letter="{{item}}">
              <text>{{item}}</text>
            </view>
          </view>
        </view>
        
        <!-- 鼻韵母部分 -->
        <view class="category-item">
          <view class="category-header">
            <text class="category-title">鼻韵母 (9个)</text>
            <text class="category-desc">带有鼻音成分的韵母</text>
          </view>
          <view class="category-letters wrap">
            <view tt:for="{{biYunmu}}" 
                  tt:key="item" 
                  class="category-letter vowel" 
                  bindtap="pronounceLetter" 
                  data-letter="{{item}}">
              <text>{{item}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="section rules-section">
        <view class="section-header">
          <text class="section-title">拼音规则</text>
        </view>
        
        <view class="rules-content">
          <view class="rule-item">
            <text class="rule-title">声调规则</text>
            <text class="rule-text">普通话有四个声调(阴平、阳平、上声、去声)和一个轻声。声调符号标在主要元音上。</text>
          </view>
          
          <view class="rule-item">
            <text class="rule-title">拼写规则</text>
            <text class="rule-text">声母和韵母组合成音节，一般是声母在前，韵母在后。部分音节只有韵母没有声母。</text>
          </view>
          
          <view class="rule-item">
            <text class="rule-title">特殊情况</text>
            <text class="rule-text">字母'w'和'y'只在音节开头使用，且后面必须接韵母。字母'v/ü'在书写中常用'u'代替。</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 字母详情面板 -->
    <view class="panel {{activeTab === 'detail' ? 'active' : 'hidden'}}">
      <view tt:if="{{selectedLetterDetail}}" class="letter-detail-card">
        <view class="detail-header">
          <view class="detail-char">{{selectedLetterDetail.char}}</view>
          <view class="detail-info">
            <text class="detail-pinyin">拼音: {{selectedLetterDetail.pinyin}}</text>
            <text class="detail-type">类型: {{selectedLetterDetail.type}}</text>
          </view>
        </view>
        
        <view class="detail-body">
          <view class="detail-section">
            <text class="detail-section-title">发音方法</text>
            <text class="detail-pronunciation">{{selectedLetterDetail.pronunciation}}</text>
          </view>
          
          <view class="detail-section">
            <text class="detail-section-title">常用例字</text>
            <view class="examples-list">
              <view tt:for="{{selectedLetterDetail.examples}}" 
                    tt:key="item" 
                    class="example-item">
                <text>{{item}}</text>
              </view>
            </view>
          </view>
          
          <view class="detail-section">
            <text class="detail-section-title">拼音搭配</text>
            <text class="detail-combinations">
              {{selectedLetterDetail.type.includes('声母') ? '可与多数韵母组合成音节' : '可与多数声母组合成音节'}}
            </text>
          </view>
        </view>
        
        <view class="detail-footer">
          <view class="detail-action" bindtap="pronounceLetter" data-letter="{{selectedLetterDetail.char}}">
            <text>发音示范</text>
          </view>
        </view>
      </view>
      
      <view tt:else class="no-selection">
        <text>请从字母总览或分类中选择一个字母查看详情</text>
      </view>
      
      <view class="section tips-section">
        <view class="section-header">
          <text class="section-title">学习技巧</text>
        </view>
        
        <view class="tips-content">
          <view class="tip-item">
            <text class="tip-number">1</text>
            <text class="tip-text">字母发音需遵循标准，不同于英语字母发音</text>
          </view>
          <view class="tip-item">
            <text class="tip-number">2</text>
            <text class="tip-text">掌握声母韵母的分类与组合规律</text>
          </view>
          <view class="tip-item">
            <text class="tip-number">3</text>
            <text class="tip-text">注意四声变化及轻声的使用</text>
          </view>
          <view class="tip-item">
            <text class="tip-number">4</text>
            <text class="tip-text">多听多读多练习，培养语感</text>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>
</FILE>

<FILE path="/src/index.ttss">
.pinyin-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #f8f8f8;
}

.header {
  padding: 20dp;
  background-color: #4a6fa5;
  align-items: center;
}

.title {
  font-size: 22dp;
  font-weight: bold;
  color: white;
  text-align: center;
}

.subtitle {
  font-size: 14dp;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 5dp;
  text-align: center;
}

/* 标签页样式 */
.tabs {
  display: flex;
  flex-direction: row;
  background-color: white;
  border-bottom: 1dp solid #eee;
}

.tab {
  flex: 1;
  padding: 12dp 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 3dp solid transparent;
}

.tab.active {
  border-bottom-color: #4a6fa5;
}

.tab text {
  font-size: 14dp;
  color: #333;
}

.tab.active text {
  color: #4a6fa5;
  font-weight: bold;
}

/* 内容区域 */
.content-scroll {
  flex: 1;
}

.panel {
  padding: 10dp;
}

.panel.hidden {
  display: none;
}

.section {
  margin-bottom: 16dp;
  background-color: white;
  border-radius: 10dp;
  overflow: hidden;
  box-shadow: 0 2dp 5dp rgba(0, 0, 0, 0.05);
}

.section-header {
  padding: 15dp;
  border-bottom: 1dp solid #eeeeee;
}

.section-title {
  font-size: 16dp;
  font-weight: bold;
  color: #333;
}

.section-subtitle {
  font-size: 12dp;
  color: #666;
  margin-top: 4dp;
}

/* 字母总览样式 */
.alphabet-grid {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 10dp;
}

.letter-card {
  width: 60dp;
  height: 60dp;
  margin: 5dp;
  border-radius: 8dp;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2dp 4dp rgba(0, 0, 0, 0.05);
}

.letter-card.selected {
  background-color: #4a6fa5;
}

.letter-card.consonant {
  background-color: #e3f2fd;
}

.letter-card.vowel {
  background-color: #fff8e1;
}

.letter-card.selected .letter-char,
.letter-card.selected .letter-pinyin {
  color: white;
}

.letter-char {
  font-size: 22dp;
  font-weight: bold;
  color: #333;
}

.letter-pinyin {
  font-size: 10dp;
  color: #666;
  margin-top: 3dp;
}

.legend {
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding: 0 10dp 15dp 10dp;
}

.legend-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0 10dp;
}

.legend-color {
  width: 12dp;
  height: 12dp;
  margin-right: 5dp;
  border-radius: 2dp;
}

.legend-color.consonant {
  background-color: #e3f2fd;
}

.legend-color.vowel {
  background-color: #fff8e1;
}

.legend-text {
  font-size: 12dp;
  color: #666;
}

/* 简介部分 */
.intro-content {
  padding: 15dp;
}

.intro-text {
  font-size: 14dp;
  color: #333;
  line-height: 1.5;
  margin-bottom: 10dp;
}

/* 分类展示样式 */
.category-item {
  padding: 15dp;
  border-bottom: 1dp solid #eeeeee;
}

.category-item:last-child {
  border-bottom: none;
}

.category-header {
  margin-bottom: 10dp;
}

.category-title {
  font-size: 16dp;
  font-weight: bold;
  color: #333;
}

.category-desc {
  font-size: 12dp;
  color: #666;
  margin-top: 3dp;
}

.category-letters {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.category-letters.wrap {
  flex-wrap: wrap;
}

.category-letter {
  min-width: 36dp;
  height: 36dp;
  margin: 5dp;
  border-radius: 6dp;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
}

.category-letter.vowel {
  background-color: #fff8e1;
}

.category-letter text {
  font-size: 14dp;
  color: #333;
}

/* 规则部分 */
.rules-content {
  padding: 15dp;
}

.rule-item {
  margin-bottom: 12dp;
}

.rule-title {
  font-size: 14dp;
  font-weight: bold;
  color: #333;
  margin-bottom: 5dp;
}

.rule-text {
  font-size: 13dp;
  color: #666;
  line-height: 1.5;
}

/* 字母详情样式 */
.letter-detail-card {
  border-radius: 10dp;
  overflow: hidden;
  background-color: white;
}

.detail-header {
  display: flex;
  flex-direction: row;
  padding: 20dp;
  background-color: #4a6fa5;
}

.detail-char {
  width: 60dp;
  height: 60dp;
  background-color: white;
  border-radius: 30dp;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30dp;
  font-weight: bold;
  color: #4a6fa5;
  margin-right: 15dp;
}

.detail-info {
  flex: 1;
  justify-content: center;
}

.detail-pinyin {
  font-size: 18dp;
  color: white;
  margin-bottom: 5dp;
}

.detail-type {
  font-size: 14dp;
  color: rgba(255, 255, 255, 0.8);
}

.detail-body {
  padding: 15dp;
}

.detail-section {
  margin-bottom: 15dp;
}

.detail-section-title {
  font-size: 14dp;
  font-weight: bold;
  color: #333;
  margin-bottom: 8dp;
}

.detail-pronunciation {
  font-size: 14dp;
  color: #666;
  line-height: 1.5;
}

.examples-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.example-item {
  background-color: #f5f7fa;
  padding: 8dp 12dp;
  border-radius: 15dp;
  margin-right: 8dp;
  margin-bottom: 8dp;
}

.example-item text {
  font-size: 13dp;
  color: #333;
}

.detail-combinations {
  font-size: 14dp;
  color: #666;
  line-height: 1.5;
}

.detail-footer {
  padding: 15dp;
  border-top: 1dp solid #eeeeee;
  display: flex;
  justify-content: center;
}

.detail-action {
  background-color: #4a6fa5;
  padding: 10dp 20dp;
  border-radius: 20dp;
}

.detail-action text {
  color: white;
  font-size: 14dp;
}

.no-selection {
  padding: 30dp;
  text-align: center;
}

.no-selection text {
  font-size: 14dp;
  color: #999;
}

/* 学习技巧样式 */
.tips-content {
  padding: 15dp;
}

.tip-item {
  display: flex;
  flex-direction: row;
  margin-bottom: 10dp;
}

.tip-number {
  width: 20dp;
  height: 20dp;
  background-color: #4a6fa5;
  border-radius: 10dp;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12dp;
  color: white;
  margin-right: 10dp;
}

.tip-text {
  flex: 1;
  font-size: 14dp;
  color: #333;
  line-height: 1.5;
}
</FILE>

<FILE path="/src/index.json">
{
  "name": "26个拼音字母表",
  "width": 600,
  "height": 800
}
</FILE>
</FILES>
```

## 代码说明

这个Lynx Direct PE版本的26个拼音字母表应用实现了以下功能：

1. 展示26个汉语拼音字母的总览和分类（声母、单韵母、复韵母、鼻韵母）
2. 提供交互式字母选择功能，点击字母可查看详细信息
3. 每个字母包含详细信息：类型、发音方法和使用示例
4. 提供拼音学习技巧参考

**Lynx Direct PE改进：**

1. 使用Vue风格的组件化结构，以`export const card`导出应用对象
2. 采用Vue的模板语法，使用v-for、v-if、:class、:key等指令替代旧版的for、if和字符串拼接
3. 使用@click事件绑定代替旧版的onclick
4. 事件处理函数接收直接参数，避免使用dataset传递数据
5. 改进CSS样式，添加过渡动画和更现代的设计
6. 新增标签页切换功能，使内容组织更加清晰
7. 优化移动端体验，包括触摸反馈和更合理的间距设计
8. 使用Vue方法计算样式类，实现更灵活的样式控制
</rewritten_file>