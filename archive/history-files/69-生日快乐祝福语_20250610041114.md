# 生日快乐祝福语 - Lynx代码示例

## 查询内容
```
生日快乐祝福语
```

## 生成的Lynx代码

```
<FILES>
<FILE path="/src/index.js">
Card({
  data: {
    greetings: [
      {
        title: '温馨祝福',
        messages: [
          '愿你的生日充满阳光和欢笑，愿你的未来充满希望和成功。生日快乐！',
          '在这特别的日子里，祝你所有的梦想都能实现，所有的希望都能如愿。生日快乐！',
          '愿这一年带给你无尽的快乐、无尽的幸福、无尽的健康。生日快乐！',
          '祝你生日的每一分钟都充满欢乐，每一个愿望都能实现。生日快乐！',
          '愿你的生日像鲜花一样芬芳，像阳光一样灿烂，像美酒一样醇香。生日快乐！'
        ]
      },
      {
        title: '友情祝福',
        messages: [
          '感谢你一直以来的陪伴，愿你的生日充满欢笑和美好的回忆。生日快乐，我的朋友！',
          '友谊是生命中最珍贵的礼物，愿你的生日充满友情的温暖。生日快乐！',
          '在你生日的这一天，愿我们的友谊地久天长，愿你的生活幸福美满。生日快乐！',
          '朋友是上天赐予的礼物，而你是我收到的最好的礼物。生日快乐！',
          '愿我们的友谊像美酒一样，越陈越香。祝你生日快乐，我亲爱的朋友！'
        ]
      },
      {
        title: '家人祝福',
        messages: [
          '愿你的生日充满家人的爱和关怀，愿你的每一天都像生日一样特别。生日快乐！',
          '你是我们家庭的骄傲和喜悦，愿你的生日和未来的每一天都充满幸福。生日快乐！',
          '在这特别的日子里，感谢你为家庭带来的所有欢乐和爱。生日快乐！',
          '家人的爱是最温暖的阳光，愿这阳光永远照耀着你。生日快乐！',
          '无论你走到哪里，家人的爱和祝福都会一直陪伴着你。生日快乐！'
        ]
      },
      {
        title: '浪漫祝福',
        messages: [
          '愿我的爱像星星一样，即使在最黑暗的夜晚也能为你指引方向。生日快乐，我的爱！',
          '你的笑容是我最大的幸福，愿你的生日和每一天都充满笑容。生日快乐！',
          '感谢你走进我的生活，让我的世界变得如此美丽。生日快乐，我亲爱的！',
          '你是我生命中最美丽的风景，愿你的生日如你一样美丽。生日快乐！',
          '愿我们的爱情像美酒一样，越陈越香。生日快乐，我的挚爱！'
        ]
      },
      {
        title: '幽默祝福',
        messages: [
          '又老了一岁，但别担心，你看起来还是那么年轻！生日快乐！',
          '生日蛋糕上的蜡烛越多，许愿的机会就越多！生日快乐！',
          '别数蜡烛了，那只会让你更累！享受你的生日吧！',
          '年龄只是一个数字，重要的是你的心态！生日快乐，永远年轻！',
          '祝你生日快乐，愿你的愿望像你的年龄一样增长——越来越多！'
        ]
      }
    ],
    currentCategory: 0,
    currentMessage: 0,
    isAnimating: false,
    showCopySuccess: false,
    bgGradient: [
      'linear-gradient(135deg, #ffafbd, #ffc3a0)',
      'linear-gradient(135deg, #a1c4fd, #c2e9fb)',
      'linear-gradient(135deg, #d4fc79, #96e6a1)',
      'linear-gradient(135deg, #ff9a9e, #fad0c4)',
      'linear-gradient(135deg, #ffecd2, #fcb69f)'
    ]
  },
  
  onLoad() {
    // 初始化Canvas背景
    this.initCanvas();
  },
  
  // 初始化动画背景
  initCanvas() {
    try {
      const canvas = lynx.krypton.createCanvasNG();
      
      canvas.addEventListener('resize', ({width, height}) => {
        canvas.width = width;
        canvas.height = height;
        
        const ctx = canvas.getContext('2d');
        
        // 创建气泡效果
        const bubbles = [];
        for (let i = 0; i < 30; i++) {
          bubbles.push({
            x: Math.random() * width,
            y: Math.random() * height,
            radius: Math.random() * 15 + 5,
            color: `rgba(255, 255, 255, ${Math.random() * 0.3 + 0.1})`,
            speed: Math.random() * 0.5 + 0.2
          });
        }
        
        // 动画循环
        function animate() {
          // 清除画布
          ctx.clearRect(0, 0, width, height);
          
          // 绘制渐变背景
          const gradient = ctx.createLinearGradient(0, 0, width, height);
          gradient.addColorStop(0, '#ffafbd');
          gradient.addColorStop(1, '#ffc3a0');
          ctx.fillStyle = gradient;
          ctx.fillRect(0, 0, width, height);
          
          // 绘制气泡
          bubbles.forEach(bubble => {
            ctx.beginPath();
            ctx.arc(bubble.x, bubble.y, bubble.radius, 0, Math.PI * 2);
            ctx.fillStyle = bubble.color;
            ctx.fill();
            
            // 移动气泡
            bubble.y -= bubble.speed;
            
            // 循环回到底部
            if (bubble.y + bubble.radius < 0) {
              bubble.y = height + bubble.radius;
              bubble.x = Math.random() * width;
            }
          });
          
          lynx.requestAnimationFrame(animate);
        }
        
        animate();
      });
      
      // 绑定到Canvas视图
      canvas.attachToCanvasView('bg-canvas');
    } catch (error) {
      console.error('Canvas初始化失败:', error);
    }
  },
  
  // 切换祝福语类别
  changeCategory(event) {
    const index = event.currentTarget.dataset.index;
    this.setData({
      currentCategory: index,
      currentMessage: 0,
      isAnimating: true
    });
    
    // 动画结束后重置状态
    setTimeout(() => {
      this.setData({
        isAnimating: false
      });
    }, 500);
  },
  
  // 显示下一条祝福语
  nextMessage() {
    const category = this.data.currentCategory;
    const messagesCount = this.data.greetings[category].messages.length;
    let nextIndex = (this.data.currentMessage + 1) % messagesCount;
    
    this.setData({
      currentMessage: nextIndex,
      isAnimating: true
    });
    
    // 动画结束后重置状态
    setTimeout(() => {
      this.setData({
        isAnimating: false
      });
    }, 500);
  },
  
  // 显示上一条祝福语
  prevMessage() {
    const category = this.data.currentCategory;
    const messagesCount = this.data.greetings[category].messages.length;
    let prevIndex = (this.data.currentMessage - 1 + messagesCount) % messagesCount;
    
    this.setData({
      currentMessage: prevIndex,
      isAnimating: true
    });
    
    // 动画结束后重置状态
    setTimeout(() => {
      this.setData({
        isAnimating: false
      });
    }, 500);
  },
  
  // 复制祝福语
  copyMessage() {
    const category = this.data.currentCategory;
    const messageIndex = this.data.currentMessage;
    const message = this.data.greetings[category].messages[messageIndex];
    
    lynx.setClipboardData({
      data: message,
      success: () => {
        this.setData({
          showCopySuccess: true
        });
        
        // 3秒后隐藏提示
        setTimeout(() => {
          this.setData({
            showCopySuccess: false
          });
        }, 3000);
      },
      fail: (err) => {
        console.error('复制失败:', err);
      }
    });
  },
  
  // 分享祝福语
  shareMessage() {
    const category = this.data.currentCategory;
    const messageIndex = this.data.currentMessage;
    const message = this.data.greetings[category].messages[messageIndex];
    
    lynx.shareAppMessage({
      title: '生日祝福语',
      desc: message,
      path: '/pages/index/index'
    });
  }
})
</FILE>

<FILE path="/src/index.ttml">
<view class="container">
  <!-- 背景Canvas -->
  <canvas id="bg-canvas" class="bg-canvas"></canvas>
  
  <view class="header">
    <text class="title">生日快乐祝福语</text>
    <text class="subtitle">为特别的日子送上最美好的祝福</text>
  </view>
  
  <view class="categories">
    <scroll-view scroll-x="true" class="category-scroll">
      <view class="category-list">
        <block tt:for="{{greetings}}" tt:key="index">
          <view class="category-item {{currentCategory === index ? 'active' : ''}}" 
                bindtap="changeCategory" 
                data-index="{{index}}"
                style="background: {{bgGradient[index]}}">
            <text>{{item.title}}</text>
          </view>
        </block>
      </view>
    </scroll-view>
  </view>
  
  <view class="greeting-card {{isAnimating ? 'animate' : ''}}" style="background: {{bgGradient[currentCategory]}}">
    <view class="decorations">
      <view class="decoration dec1"></view>
      <view class="decoration dec2"></view>
      <view class="decoration dec3"></view>
    </view>
    
    <view class="greeting-content">
      <text class="greeting-text">{{greetings[currentCategory].messages[currentMessage]}}</text>
    </view>
    
    <view class="greeting-navigation">
      <view class="nav-button" bindtap="prevMessage">
        <text class="nav-icon">←</text>
      </view>
      <view class="message-counter">
        <text>{{currentMessage + 1}}/{{greetings[currentCategory].messages.length}}</text>
      </view>
      <view class="nav-button" bindtap="nextMessage">
        <text class="nav-icon">→</text>
      </view>
    </view>
  </view>
  
  <view class="actions">
    <view class="action-button" bindtap="copyMessage">
      <text class="action-icon">📋</text>
      <text class="action-text">复制</text>
    </view>
    <view class="action-button" bindtap="shareMessage">
      <text class="action-icon">📤</text>
      <text class="action-text">分享</text>
    </view>
  </view>
  
  <view class="copy-success {{showCopySuccess ? 'show' : ''}}">
    <text>复制成功！</text>
  </view>
</view>
</FILE>

<FILE path="/src/index.ttss">
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20dp;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.bg-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24dp;
  width: 100%;
  position: relative;
  z-index: 2;
}

.title {
  fontSize: 28dp;
  fontWeight: bold;
  color: #ffffff;
  textShadow: 0 2dp 4dp rgba(0,0,0,0.2);
  marginBottom: 8dp;
}

.subtitle {
  fontSize: 16dp;
  color: rgba(255,255,255,0.9);
  textAlign: center;
}

.categories {
  width: 100%;
  marginBottom: 24dp;
}

.category-scroll {
  width: 100%;
}

.category-list {
  display: flex;
  flexDirection: row;
  padding: 4dp;
}

.category-item {
  padding: 12dp 20dp;
  marginRight: 12dp;
  borderRadius: 24dp;
  backgroundColor: rgba(255,255,255,0.2);
  backdropFilter: blur(8dp);
  transition: transform 0.3s, box-shadow 0.3s;
  boxShadow: 0 4dp 8dp rgba(0,0,0,0.1);
}

.category-item.active {
  transform: scale(1.05);
  boxShadow: 0 6dp 12dp rgba(0,0,0,0.15);
}

.category-item text {
  color: #ffffff;
  fontSize: 16dp;
  fontWeight: 500;
}

.greeting-card {
  width: 100%;
  borderRadius: 24dp;
  padding: 24dp;
  marginBottom: 24dp;
  boxShadow: 0 8dp 16dp rgba(0,0,0,0.12);
  position: relative;
  overflow: hidden;
  transition: transform 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.greeting-card.animate {
  transform: translateY(-8dp) scale(0.98);
}

.decorations {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
  opacity: 0.5;
}

.decoration {
  position: absolute;
  borderRadius: 50%;
  backgroundColor: rgba(255,255,255,0.3);
}

.dec1 {
  width: 100dp;
  height: 100dp;
  top: -30dp;
  right: -30dp;
}

.dec2 {
  width: 60dp;
  height: 60dp;
  bottom: 30dp;
  left: -20dp;
}

.dec3 {
  width: 40dp;
  height: 40dp;
  bottom: -10dp;
  right: 40dp;
}

.greeting-content {
  minHeight: 120dp;
  display: flex;
  alignItems: center;
  justifyContent: center;
  padding: 16dp 8dp;
}

.greeting-text {
  fontSize: 18dp;
  color: #ffffff;
  textAlign: center;
  lineHeight: 1.5;
  textShadow: 0 1dp 2dp rgba(0,0,0,0.1);
}

.greeting-navigation {
  display: flex;
  flexDirection: row;
  alignItems: center;
  justifyContent: space-between;
  marginTop: 16dp;
}

.nav-button {
  width: 44dp;
  height: 44dp;
  borderRadius: 22dp;
  backgroundColor: rgba(255,255,255,0.3);
  display: flex;
  alignItems: center;
  justifyContent: center;
}

.nav-icon {
  fontSize: 20dp;
  color: #ffffff;
}

.message-counter {
  padding: 4dp 12dp;
  borderRadius: 16dp;
  backgroundColor: rgba(255,255,255,0.2);
}

.message-counter text {
  fontSize: 14dp;
  color: #ffffff;
}

.actions {
  display: flex;
  flexDirection: row;
  alignItems: center;
  justifyContent: center;
  width: 100%;
}

.action-button {
  display: flex;
  flexDirection: column;
  alignItems: center;
  margin: 0 20dp;
}

.action-icon {
  fontSize: 24dp;
  marginBottom: 8dp;
}

.action-text {
  fontSize: 14dp;
  color: rgba(255,255,255,0.9);
}

.copy-success {
  position: fixed;
  bottom: 40dp;
  left: 50%;
  transform: translateX(-50%) translateY(100dp);
  backgroundColor: rgba(0,0,0,0.7);
  color: #ffffff;
  padding: 12dp 24dp;
  borderRadius: 24dp;
  transition: transform 0.3s;
  opacity: 0;
}

.copy-success.show {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}
</FILE>

<FILE path="/src/index.json">
{
  "component": true,
  "usingComponents": {}
}
</FILE>