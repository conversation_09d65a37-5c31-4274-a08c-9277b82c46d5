# Lynx Preview 页面架构设计文档

## 1. 项目概述

### 1.1 目标
创建一个独立的 `lynx_preview` 页面，实现在线解析和转换 Lynx 代码为 Web 预览，支持：
- CDN URL 参数输入（如：`https%3A%2F%2Flf3-static.bytednsdoc.com%2Fobj%2Feden-cn%2Fnuvogeh7hpqhpq%2Fso-web-code%2Flynx_1751037482925_r7h50jj9.zip`）
- Playground URL 输入（如：`https://playground.cn.goofy.app/?useSpeedy=true&exampleType=ttml-nodiff&layout=preview&project=...`）
- 完全按照 `@byted-lynx/web-speedy-plugin` 的转换规则进行 TTML 和 TTSS 转换

### 1.2 核心挑战分析
基于对现有实现的深入分析，发现关键问题：

**deleted_lynx2web 的局限性：**
- ❌ 转换覆盖率仅约 30%，缺少高级语法支持
- ❌ 自定义实现与官方规则存在差异
- ❌ 无法处理复杂的组件化和模块系统

**runtime_convert_parse5 的局限性：**
- ❌ 基于 Parse5 的 HTML5 解析器，需要预处理 TTML 语法
- ❌ 缺少完整的模块解析和依赖管理
- ❌ 样式作用域系统简化，无法达到生产级别

**@byted-lynx/web-speedy-plugin 的优势：**
- ✅ 官方转换引擎，100% TTML 兼容性
- ✅ 完整的模块系统和依赖解析
- ✅ 企业级样式作用域和优化
- ❌ 但依赖 Node.js 环境，无法直接在浏览器中运行

## 2. 架构设计策略

### 2.1 混合架构方案
采用 **服务端 + 浏览器端混合架构**，结合两者优势：

```
┌─────────────────────────────────────────────────────────────┐
│                    Lynx Preview 架构                         │
├─────────────────────────────────────────────────────────────┤
│  前端界面层 (Browser)                                        │
│  ├── URL 解析器 (CDN/Playground URL)                        │
│  ├── 文件下载器 (Zip 解压缩)                                │
│  ├── 预览界面 (实时渲染)                                     │
│  └── 错误处理 (降级机制)                                     │
├─────────────────────────────────────────────────────────────┤
│  转换引擎层 (Hybrid)                                         │
│  ├── 主引擎: Web-Speedy-Plugin API (服务端)                 │
│  ├── 备用引擎: Enhanced Parse5 (浏览器端)                   │
│  └── 智能路由 (根据复杂度选择引擎)                          │
├─────────────────────────────────────────────────────────────┤
│  数据处理层                                                  │
│  ├── 文件解析器 (TTML/TTSS/JS)                             │
│  ├── 依赖分析器 (组件引用关系)                              │
│  └── 缓存管理器 (转换结果缓存)                              │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件设计

#### 2.2.1 URL 解析器 (URLResolver)
```typescript
interface URLResolver {
  // 解析 CDN URL
  parseCDNUrl(url: string): Promise<{
    downloadUrl: string;
    metadata: {
      filename: string;
      size: number;
      type: 'zip' | 'single';
    };
  }>;
  
  // 解析 Playground URL
  parsePlaygroundUrl(url: string): Promise<{
    projectUrl: string;
    config: {
      useSpeedy: boolean;
      exampleType: string;
      layout: string;
      sdkVersion: string;
    };
  }>;
}
```

#### 2.2.2 文件处理器 (FileProcessor)
```typescript
interface FileProcessor {
  // 下载并解压 ZIP 文件
  downloadAndExtract(url: string): Promise<FileStructure>;
  
  // 解析文件结构
  parseFileStructure(files: Record<string, string>): {
    ttml: string[];
    ttss: string[];
    js: string[];
    json: string[];
    assets: string[];
  };
  
  // 分析依赖关系
  analyzeDependencies(files: FileStructure): DependencyGraph;
}
```

#### 2.2.3 转换引擎路由器 (TransformRouter)
```typescript
interface TransformRouter {
  // 智能选择转换引擎
  selectEngine(complexity: ComplexityAnalysis): 'web-speedy' | 'parse5';
  
  // 执行转换
  transform(files: FileStructure, engine: string): Promise<TransformResult>;
  
  // 降级处理
  fallbackTransform(files: FileStructure, error: Error): Promise<TransformResult>;
}
```

## 3. 技术实现方案

### 3.1 主转换引擎：Web-Speedy-Plugin API
创建服务端 API 端点，封装 `@byted-lynx/web-speedy-plugin`：

```typescript
// /api/lynx-transform
export async function POST(request: Request) {
  const { files, config } = await request.json();
  
  // 使用官方 web-speedy-plugin
  const result = await webSpeedyPlugin.transform(files, {
    runtimeDslMode: 'ttml',
    rpx: {
      rpxMode: 'vw',
      designWidth: 750
    },
    pageConfig: {
      webBumpAllSelectorSpecificity: true,
      enableCSSInheritance: true,
      useLepusNG: true
    }
  });
  
  return Response.json(result);
}
```

### 3.2 备用转换引擎：Enhanced Parse5
基于现有 `runtime_convert_parse5`，增强功能：

```typescript
class EnhancedParse5Engine extends Parse5TransformEngine {
  // 增强的映射规则（从 web-speedy-plugin 提取）
  private comprehensiveMappings = COMPREHENSIVE_LYNX_MAPPING;
  
  // 改进的样式作用域系统
  private enhancedScopeSystem = new EnhancedScopeSystem();
  
  // 模块依赖解析
  private moduleResolver = new ModuleResolver();
}
```

### 3.3 智能引擎选择策略
```typescript
class ComplexityAnalyzer {
  analyze(files: FileStructure): ComplexityScore {
    const score = {
      componentCount: this.countComponents(files),
      dependencyDepth: this.analyzeDependencyDepth(files),
      advancedFeatures: this.detectAdvancedFeatures(files),
      fileSize: this.calculateTotalSize(files)
    };
    
    // 复杂度评分
    if (score.advancedFeatures > 5 || score.dependencyDepth > 3) {
      return 'high'; // 使用 web-speedy-plugin
    } else {
      return 'low';  // 使用 enhanced-parse5
    }
  }
}
```

## 4. 用户界面设计

### 4.1 页面布局
```
┌─────────────────────────────────────────────────────────────┐
│  Lynx Preview - 在线转换工具                                 │
├─────────────────────────────────────────────────────────────┤
│  输入区域                                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ URL 输入框                                              │ │
│  │ [CDN URL] [Playground URL] [本地上传]                   │ │
│  │ ┌─────────────────────────────────────────────────────┐ │ │
│  │ │ https://lf3-static.bytednsdoc.com/obj/eden-cn/...   │ │ │
│  │ └─────────────────────────────────────────────────────┘ │ │
│  │ [解析] [清空]                                           │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  处理状态                                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ ✅ URL 解析完成                                         │ │
│  │ ⏳ 下载文件中... (2.3MB / 5.1MB)                       │ │
│  │ 🔄 转换中... (使用 Web-Speedy-Plugin)                  │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  预览区域                                                    │
│  ┌─────────────────┬─────────────────────────────────────────┐ │
│  │ 文件结构        │ 预览窗口                                │ │
│  │ 📁 src/         │ ┌─────────────────────────────────────┐ │ │
│  │  📄 index.ttml  │ │                                     │ │ │
│  │  🎨 index.ttss  │ │        Web 预览                     │ │ │
│  │  ⚙️ index.js    │ │                                     │ │ │
│  │ 📁 components/  │ │                                     │ │ │
│  │  📄 card.ttml   │ │                                     │ │ │
│  └─────────────────┴─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 交互功能
- **实时预览**：文件解析完成后立即显示预览
- **文件浏览**：左侧文件树，点击查看源码
- **错误提示**：详细的错误信息和修复建议
- **性能监控**：显示转换耗时和引擎选择原因
- **导出功能**：导出转换后的 HTML/CSS/JS 代码

## 5. 错误处理和降级机制

### 5.1 多层降级策略
```
主引擎失败 → 备用引擎 → 基础转换 → 错误展示
     ↓           ↓          ↓         ↓
Web-Speedy → Parse5 → Simple → Error UI
```

### 5.2 错误分类处理
- **网络错误**：重试机制 + 离线提示
- **解析错误**：语法高亮 + 错误定位
- **转换错误**：降级处理 + 部分预览
- **渲染错误**：错误边界 + 安全模式

## 6. 性能优化策略

### 6.1 缓存机制
- **URL 缓存**：相同 URL 的文件缓存
- **转换缓存**：相同内容的转换结果缓存
- **预览缓存**：渲染结果缓存

### 6.2 异步处理
- **流式下载**：大文件分块下载
- **并行转换**：多文件并行处理
- **懒加载**：按需加载预览内容

## 7. 下一步实施计划

### 阶段一：基础架构 (1-2 周)
1. 创建 `lynx_preview` 路由和基础页面
2. 实现 URL 解析器和文件下载器
3. 搭建基础 UI 框架

### 阶段二：转换引擎 (2-3 周)
1. 集成 Web-Speedy-Plugin API
2. 增强 Parse5 引擎
3. 实现智能路由选择

### 阶段三：用户体验 (1-2 周)
1. 完善预览界面
2. 添加错误处理
3. 性能优化和测试

这个架构设计充分考虑了现有实现的优缺点，采用混合方案确保既有完整的转换能力，又有良好的用户体验。
