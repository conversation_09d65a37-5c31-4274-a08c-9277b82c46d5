# Lynx Preview 页面架构设计文档

## 1. 项目概述

### 1.1 目标
创建一个独立的 `lynx_preview` 页面，实现在线解析和转换 Lynx 代码为 Web 预览，支持：

- CDN URL 参数输入（如：`https%3A%2F%2Flf3-static.bytednsdoc.com%2Fobj%2Feden-cn%2Fnuvogeh7hpqhpq%2Fso-web-code%2Flynx_1751037482925_r7h50jj9.zip`）
- Playground URL 输入（如：`https://playground.cn.goofy.app/?useSpeedy=true&exampleType=ttml-nodiff&layout=preview&project=...`）
- 完全按照 `@byted-lynx/web-speedy-plugin` 的转换规则进行 TTML 和 TTSS 转换

### 1.2 核心设计理念
**纯前端 Web-Speedy-Plugin 实现**

基于对现有实现的深入分析，我们采用全新的设计理念：

**设计目标：**

- ✅ 创建一个完全在浏览器中运行的 `@byted-lynx/web-speedy-plugin` 实现
- ✅ 100% 兼容官方转换规则和映射逻辑
- ✅ 支持完整的 TTML/TTSS/Lepus 语法
- ✅ 实现企业级的样式作用域和模块系统
- ✅ 无需服务端依赖，纯 Web Worker 架构

**技术策略：**

- 🔧 **逆向工程**：从官方文档和现有实现中提取完整的转换规则
- 🔧 **AST 重构**：使用浏览器兼容的 AST 解析器重新实现转换逻辑
- 🔧 **模块化设计**：将转换引擎拆分为独立的、可测试的模块
- 🔧 **Worker 架构**：使用 Web Worker 确保转换过程不阻塞主线程

## 2. 纯前端架构设计

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                Lynx Preview 纯前端架构                       │
├─────────────────────────────────────────────────────────────┤
│  用户界面层 (Main Thread)                                   │
│  ├── URL 解析器 (CDN/Playground URL)                        │
│  ├── 文件下载器 (Zip 解压缩)                                │
│  ├── 预览界面 (实时渲染)                                     │
│  └── 进度监控 (转换状态追踪)                                │
├─────────────────────────────────────────────────────────────┤
│  转换引擎层 (Web Worker)                                     │
│  ├── Browser-Web-Speedy-Engine (核心转换引擎)               │
│  ├── TTML-AST-Parser (TTML 语法解析器)                      │
│  ├── TTSS-Processor (样式处理器)                            │
│  ├── Lepus-Transformer (脚本转换器)                         │
│  └── Module-Resolver (模块依赖解析器)                       │
├─────────────────────────────────────────────────────────────┤
│  映射规则层 (Static Data)                                   │
│  ├── Element-Mappings (元素映射表)                          │
│  ├── Directive-Mappings (指令映射表)                        │
│  ├── Style-Mappings (样式映射表)                            │
│  └── Component-Mappings (组件映射表)                        │
├─────────────────────────────────────────────────────────────┤
│  工具库层 (Utilities)                                       │
│  ├── AST-Utils (AST 操作工具)                              │
│  ├── Scope-Generator (作用域生成器)                         │
│  ├── Cache-Manager (缓存管理器)                             │
│  └── Error-Handler (错误处理器)                             │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 Browser-Web-Speedy-Engine 核心设计

#### 2.2.1 主转换引擎 (BrowserWebSpeedyEngine)

```typescript
// src/routes/lynx_preview/engines/BrowserWebSpeedyEngine.ts
export class BrowserWebSpeedyEngine {
  private ttmlParser: TTMLASTParser;
  private ttssProcessor: TTSSProcessor;
  private lepusTransformer: LepusTransformer;
  private moduleResolver: ModuleResolver;
  private scopeGenerator: ScopeGenerator;

  constructor(config: WebSpeedyConfig) {
    this.ttmlParser = new TTMLASTParser(config.ttmlOptions);
    this.ttssProcessor = new TTSSProcessor(config.rpx, config.pageConfig);
    this.lepusTransformer = new LepusTransformer(config.pageConfig);
    this.moduleResolver = new ModuleResolver();
    this.scopeGenerator = new ScopeGenerator();
  }

  /**
   * 主转换方法 - 完全兼容 @byted-lynx/web-speedy-plugin
   */
  async transform(files: FileStructure, options: TransformOptions): Promise<TransformResult> {
    // 1. 模块依赖分析
    const dependencyGraph = this.moduleResolver.analyze(files);

    // 2. 生成组件作用域 (tagV)
    const componentScopes = this.generateComponentScopes(files);

    // 3. 按依赖顺序转换文件
    const transformResults = await this.transformInDependencyOrder(
      files,
      dependencyGraph,
      componentScopes
    );

    // 4. 生成最终的 Web 运行时代码
    return this.generateWebRuntime(transformResults, options);
  }

  /**
   * 生成组件作用域 - 兼容官方 tagV 生成算法
   */
  private generateComponentScopes(files: FileStructure): Record<string, string> {
    const scopes: Record<string, string> = {};

    Object.entries(files).forEach(([filePath, content]) => {
      if (filePath.endsWith('.ttml') || filePath.endsWith('.ttss')) {
        // 使用与官方相同的哈希算法
        scopes[filePath] = this.scopeGenerator.generateTagV(filePath, content);
      }
    });

    return scopes;
  }
}
```

#### 2.2.2 TTML AST 解析器 (TTMLASTParser)

```typescript
// src/routes/lynx_preview/engines/TTMLASTParser.ts
export class TTMLASTParser {
  private elementMappings: ElementMappingTable;
  private directiveMappings: DirectiveMappingTable;

  constructor(options: TTMLOptions) {
    // 加载完整的官方映射规则
    this.elementMappings = OFFICIAL_ELEMENT_MAPPINGS;
    this.directiveMappings = OFFICIAL_DIRECTIVE_MAPPINGS;
  }

  /**
   * 解析 TTML 为 JSX AST - 完全兼容官方转换逻辑
   */
  parse(ttml: string, componentId: string): TTMLParseResult {
    // 1. 词法分析 - 使用自定义 TTML 词法分析器
    const tokens = this.tokenize(ttml);

    // 2. 语法分析 - 构建 TTML AST
    const ast = this.buildAST(tokens);

    // 3. 语义分析 - 处理指令和组件引用
    const semanticAST = this.analyzeSemantics(ast, componentId);

    // 4. 转换为 JSX AST - 应用映射规则
    const jsxAST = this.transformToJSX(semanticAST);

    return {
      jsxAST,
      dependencies: this.extractDependencies(ast),
      components: this.extractComponents(ast),
      metadata: {
        elementCount: this.countElements(ast),
        directiveCount: this.countDirectives(ast)
      }
    };
  }

  /**
   * TTML 词法分析器 - 处理 Lynx 特有语法
   */
  private tokenize(ttml: string): TTMLToken[] {
    const tokens: TTMLToken[] = [];
    let position = 0;

    while (position < ttml.length) {
      // 处理标签
      if (ttml[position] === '<') {
        const tagToken = this.parseTag(ttml, position);
        tokens.push(tagToken);
        position = tagToken.end;
        continue;
      }

      // 处理文本内容
      if (ttml[position] !== '<') {
        const textToken = this.parseText(ttml, position);
        tokens.push(textToken);
        position = textToken.end;
        continue;
      }

      position++;
    }

    return tokens;
  }

  /**
   * 转换为 JSX AST - 应用官方映射规则
   */
  private transformToJSX(ast: TTTMLAST): JSXAST {
    return this.transformNode(ast.root);
  }

  private transformNode(node: TTMLNode): JSXNode {
    // 1. 元素映射
    if (node.type === 'element') {
      const mapping = this.elementMappings[node.tagName];
      if (!mapping) {
        throw new Error(`未知的 TTML 元素: ${node.tagName}`);
      }

      return {
        type: 'JSXElement',
        openingElement: {
          type: 'JSXOpeningElement',
          name: { type: 'JSXIdentifier', name: mapping.htmlTag },
          attributes: this.transformAttributes(node.attributes, mapping)
        },
        children: node.children.map(child => this.transformNode(child)),
        closingElement: mapping.selfClosing ? null : {
          type: 'JSXClosingElement',
          name: { type: 'JSXIdentifier', name: mapping.htmlTag }
        }
      };
    }

    // 2. 指令处理
    if (node.type === 'directive') {
      const mapping = this.directiveMappings[node.directiveName];
      if (!mapping) {
        throw new Error(`未知的 TTML 指令: ${node.directiveName}`);
      }

      return mapping.transform(node, this);
    }

    // 3. 文本节点
    if (node.type === 'text') {
      return {
        type: 'JSXText',
        value: node.value
      };
    }

    throw new Error(`未知的节点类型: ${node.type}`);
  }
}
```

#### 2.2.3 TTSS 样式处理器 (TTSSProcessor)

```typescript
// src/routes/lynx_preview/engines/TTSSProcessor.ts
export class TTSSProcessor {
  private rpxConfig: RpxConfig;
  private pageConfig: PageConfig;

  constructor(rpxConfig: RpxConfig, pageConfig: PageConfig) {
    this.rpxConfig = rpxConfig;
    this.pageConfig = pageConfig;
  }

  /**
   * 处理 TTSS 样式 - 完全兼容官方转换规则
   */
  process(ttss: string, tagV: string, filePath: string): TTSSProcessResult {
    // 1. CSS 解析
    const cssAST = this.parseCSS(ttss);

    // 2. RPX 单位转换
    const convertedAST = this.convertRpxUnits(cssAST);

    // 3. 样式作用域化
    const scopedAST = this.applyScopeToCSS(convertedAST, tagV);

    // 4. 选择器优先级提升 (webBumpAllSelectorSpecificity)
    const bumpedAST = this.pageConfig.webBumpAllSelectorSpecificity
      ? this.bumpSelectorSpecificity(scopedAST)
      : scopedAST;

    // 5. CSS 继承处理 (enableCSSInheritance)
    const inheritanceAST = this.pageConfig.enableCSSInheritance
      ? this.processCSSInheritance(bumpedAST)
      : bumpedAST;

    // 6. 生成最终 CSS
    const finalCSS = this.generateCSS(inheritanceAST);

    return {
      css: finalCSS,
      cssInJS: this.generateCSSInJS(inheritanceAST),
      metadata: {
        ruleCount: this.countRules(cssAST),
        rpxConversions: this.countRpxConversions(cssAST),
        scopedSelectors: this.countScopedSelectors(scopedAST)
      }
    };
  }

  /**
   * RPX 单位转换 - 兼容官方转换算法
   */
  private convertRpxUnits(cssAST: CSSAST): CSSAST {
    const { rpxMode, designWidth } = this.rpxConfig;

    return this.traverseAST(cssAST, (node) => {
      if (node.type === 'declaration' && node.value.includes('rpx')) {
        node.value = node.value.replace(/(\d+(?:\.\d+)?)rpx/g, (match, value) => {
          const rpxValue = parseFloat(value);

          switch (rpxMode) {
            case 'vw':
              return `${((rpxValue / designWidth) * 100).toFixed(6)}vw`;
            case 'rem':
              return `${(rpxValue / 37.5).toFixed(6)}rem`;
            case 'px':
              return `${(rpxValue * 0.5).toFixed(2)}px`;
            default:
              return match;
          }
        });
      }
      return node;
    });
  }

  /**
   * 样式作用域化 - 兼容官方 tagV 作用域系统
   */
  private applyScopeToCSS(cssAST: CSSAST, tagV: string): CSSAST {
    return this.traverseAST(cssAST, (node) => {
      if (node.type === 'rule') {
        node.selectors = node.selectors.map(selector => {
          // 跳过全局选择器
          if (selector.startsWith(':global(')) {
            return selector.replace(':global(', '').replace(')', '');
          }

          // 添加组件作用域
          return `.lynx-${tagV} ${selector}`;
        });
      }
      return node;
    });
  }
}
```

#### 2.2.4 Lepus 脚本转换器 (LepusTransformer)

```typescript
// src/routes/lynx_preview/engines/LepusTransformer.ts
export class LepusTransformer {
  private pageConfig: PageConfig;

  constructor(pageConfig: PageConfig) {
    this.pageConfig = pageConfig;
  }

  /**
   * 转换 Lepus 脚本 - 兼容官方转换逻辑
   */
  transform(lepusCode: string, componentId: string): LepusTransformResult {
    // 1. 解析 Lepus AST
    const ast = this.parseLepusAST(lepusCode);

    // 2. 处理 Card 语法
    const cardTransformed = this.transformCardSyntax(ast);

    // 3. 处理数据绑定
    const dataBindingTransformed = this.transformDataBinding(cardTransformed);

    // 4. 处理生命周期方法
    const lifecycleTransformed = this.transformLifecycleMethods(dataBindingTransformed);

    // 5. 生成 Web 兼容的 JavaScript
    const webJS = this.generateWebJS(lifecycleTransformed, componentId);

    return {
      javascript: webJS,
      exports: this.extractExports(ast),
      dependencies: this.extractDependencies(ast),
      metadata: {
        hasCard: this.hasCardSyntax(ast),
        hasDataBinding: this.hasDataBinding(ast),
        hasLifecycle: this.hasLifecycleMethods(ast)
      }
    };
  }

  /**
   * 转换 Card 语法为 Web 组件
   */
  private transformCardSyntax(ast: LepusAST): LepusAST {
    return this.traverseAST(ast, (node) => {
      if (node.type === 'CallExpression' && node.callee.name === 'Card') {
        // Card({ ... }) -> class Component extends React.Component { ... }
        return {
          type: 'ClassDeclaration',
          id: { type: 'Identifier', name: 'Component' },
          superClass: {
            type: 'MemberExpression',
            object: { type: 'Identifier', name: 'React' },
            property: { type: 'Identifier', name: 'Component' }
          },
          body: this.transformCardBody(node.arguments[0])
        };
      }
      return node;
    });
  }

  /**
   * 转换数据绑定语法
   */
  private transformDataBinding(ast: LepusAST): LepusAST {
    return this.traverseAST(ast, (node) => {
      // this.setData({ ... }) -> this.setState({ ... })
      if (node.type === 'CallExpression' &&
          node.callee.type === 'MemberExpression' &&
          node.callee.property.name === 'setData') {
        node.callee.property.name = 'setState';
      }

      // this.data.xxx -> this.state.xxx
      if (node.type === 'MemberExpression' &&
          node.object.type === 'MemberExpression' &&
          node.object.property.name === 'data') {
        node.object.property.name = 'state';
      }

      return node;
    });
  }
}
```

## 3. 官方映射规则实现

### 3.1 元素映射表 (OFFICIAL_ELEMENT_MAPPINGS)

```typescript
// src/routes/lynx_preview/mappings/element-mappings.ts
export const OFFICIAL_ELEMENT_MAPPINGS: ElementMappingTable = {
  // 基础容器元素
  'view': {
    htmlTag: 'div',
    defaultProps: {
      className: 'lynx-view'
    },
    attributeMappings: {
      'class': 'className',
      'hover-class': 'data-hover-class',
      'hover-start-time': 'data-hover-start-time',
      'hover-stay-time': 'data-hover-stay-time'
    }
  },

  // 文本元素
  'text': {
    htmlTag: 'span',
    defaultProps: {
      className: 'lynx-text'
    },
    attributeMappings: {
      'class': 'className',
      'selectable': 'data-selectable',
      'space': 'data-space',
      'decode': 'data-decode'
    }
  },

  // 图片元素
  'image': {
    htmlTag: 'img',
    selfClosing: true,
    defaultProps: {
      className: 'lynx-image'
    },
    attributeMappings: {
      'src': 'src',
      'mode': 'data-mode',
      'webp': 'data-webp',
      'lazy-load': 'loading', // lazy-load="true" -> loading="lazy"
      'show-menu-by-longpress': 'data-show-menu'
    },
    attributeTransforms: {
      'lazy-load': (value: string) => value === 'true' ? 'lazy' : 'eager'
    }
  },

  // 滚动容器
  'scroll-view': {
    htmlTag: 'div',
    defaultProps: {
      className: 'lynx-scroll-view'
    },
    attributeMappings: {
      'scroll-x': 'data-scroll-x',
      'scroll-y': 'data-scroll-y',
      'upper-threshold': 'data-upper-threshold',
      'lower-threshold': 'data-lower-threshold',
      'scroll-top': 'data-scroll-top',
      'scroll-left': 'data-scroll-left',
      'scroll-into-view': 'data-scroll-into-view',
      'scroll-with-animation': 'data-scroll-with-animation',
      'enable-back-to-top': 'data-enable-back-to-top',
      'enable-flex': 'data-enable-flex',
      'scroll-anchoring': 'data-scroll-anchoring'
    }
  },

  // 列表元素
  'list': {
    htmlTag: 'div',
    defaultProps: {
      className: 'lynx-list'
    },
    attributeMappings: {
      'class': 'className'
    }
  },

  'list-item': {
    htmlTag: 'div',
    defaultProps: {
      className: 'lynx-list-item'
    },
    attributeMappings: {
      'class': 'className',
      'key': 'key'
    }
  },

  // 输入元素
  'input': {
    htmlTag: 'input',
    selfClosing: true,
    defaultProps: {
      className: 'lynx-input'
    },
    attributeMappings: {
      'value': 'value',
      'type': 'type',
      'password': 'data-password',
      'placeholder': 'placeholder',
      'placeholder-style': 'data-placeholder-style',
      'placeholder-class': 'data-placeholder-class',
      'disabled': 'disabled',
      'maxlength': 'maxLength',
      'cursor-spacing': 'data-cursor-spacing',
      'auto-focus': 'autoFocus',
      'focus': 'data-focus',
      'confirm-type': 'data-confirm-type',
      'confirm-hold': 'data-confirm-hold',
      'cursor': 'data-cursor',
      'selection-start': 'data-selection-start',
      'selection-end': 'data-selection-end',
      'adjust-position': 'data-adjust-position',
      'hold-keyboard': 'data-hold-keyboard'
    }
  },

  // 按钮元素
  'button': {
    htmlTag: 'button',
    defaultProps: {
      className: 'lynx-button'
    },
    attributeMappings: {
      'size': 'data-size',
      'type': 'data-type',
      'plain': 'data-plain',
      'disabled': 'disabled',
      'loading': 'data-loading',
      'form-type': 'data-form-type',
      'open-type': 'data-open-type',
      'hover-class': 'data-hover-class',
      'hover-start-time': 'data-hover-start-time',
      'hover-stay-time': 'data-hover-stay-time',
      'lang': 'data-lang',
      'session-from': 'data-session-from',
      'send-message-title': 'data-send-message-title',
      'send-message-path': 'data-send-message-path',
      'send-message-img': 'data-send-message-img',
      'app-parameter': 'data-app-parameter',
      'show-message-card': 'data-show-message-card'
    }
  }
};
```

### 3.2 指令映射表 (OFFICIAL_DIRECTIVE_MAPPINGS)

```typescript
// src/routes/lynx_preview/mappings/directive-mappings.ts
export const OFFICIAL_DIRECTIVE_MAPPINGS: DirectiveMappingTable = {
  // 条件渲染指令
  'lx:if': {
    type: 'conditional',
    transform: (node: TTMLNode, parser: TTMLASTParser) => {
      const condition = parser.parseExpression(node.directiveValue);
      return {
        type: 'ConditionalExpression',
        test: condition,
        consequent: parser.transformNode(node),
        alternate: null
      };
    }
  },

  'lx:elif': {
    type: 'conditional',
    transform: (node: TTMLNode, parser: TTMLASTParser) => {
      const condition = parser.parseExpression(node.directiveValue);
      return {
        type: 'ConditionalExpression',
        test: condition,
        consequent: parser.transformNode(node),
        alternate: null // 由条件链处理器处理
      };
    }
  },

  'lx:else': {
    type: 'conditional',
    transform: (node: TTMLNode, parser: TTMLASTParser) => {
      return parser.transformNode(node);
    }
  },

  // 循环渲染指令
  'lx:for': {
    type: 'iteration',
    transform: (node: TTMLNode, parser: TTMLASTParser) => {
      const forExpression = parser.parseForExpression(node.directiveValue);
      return {
        type: 'CallExpression',
        callee: {
          type: 'MemberExpression',
          object: forExpression.iterable,
          property: { type: 'Identifier', name: 'map' }
        },
        arguments: [
          {
            type: 'ArrowFunctionExpression',
            params: [
              { type: 'Identifier', name: forExpression.item },
              { type: 'Identifier', name: forExpression.index || 'index' }
            ],
            body: parser.transformNode(node)
          }
        ]
      };
    }
  },

  'lx:key': {
    type: 'optimization',
    transform: (node: TTMLNode, parser: TTMLASTParser) => {
      // lx:key 转换为 React key 属性
      const keyExpression = parser.parseExpression(node.directiveValue);
      node.attributes.push({
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name: 'key' },
        value: {
          type: 'JSXExpressionContainer',
          expression: keyExpression
        }
      });
      return parser.transformNode(node);
    }
  },

  // 事件绑定指令
  'bindtap': {
    type: 'event',
    transform: (node: TTMLNode, parser: TTMLASTParser) => {
      const handler = parser.parseExpression(node.directiveValue);
      node.attributes.push({
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name: 'onClick' },
        value: {
          type: 'JSXExpressionContainer',
          expression: handler
        }
      });
      return parser.transformNode(node);
    }
  },

  'catch:tap': {
    type: 'event',
    transform: (node: TTMLNode, parser: TTMLASTParser) => {
      const handler = parser.parseExpression(node.directiveValue);
      node.attributes.push({
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name: 'onClick' },
        value: {
          type: 'JSXExpressionContainer',
          expression: {
            type: 'ArrowFunctionExpression',
            params: [{ type: 'Identifier', name: 'e' }],
            body: {
              type: 'BlockStatement',
              body: [
                {
                  type: 'ExpressionStatement',
                  expression: {
                    type: 'CallExpression',
                    callee: {
                      type: 'MemberExpression',
                      object: { type: 'Identifier', name: 'e' },
                      property: { type: 'Identifier', name: 'stopPropagation' }
                    },
                    arguments: []
                  }
                },
                {
                  type: 'ExpressionStatement',
                  expression: {
                    type: 'CallExpression',
                    callee: handler,
                    arguments: [{ type: 'Identifier', name: 'e' }]
                  }
                }
              ]
            }
          }
        }
      });
      return parser.transformNode(node);
    }
  },

  'bindlongpress': {
    type: 'event',
    transform: (node: TTMLNode, parser: TTMLASTParser) => {
      const handler = parser.parseExpression(node.directiveValue);
      // 长按事件需要特殊处理，转换为 onMouseDown + 定时器
      node.attributes.push({
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name: 'onMouseDown' },
        value: {
          type: 'JSXExpressionContainer',
          expression: {
            type: 'ArrowFunctionExpression',
            params: [{ type: 'Identifier', name: 'e' }],
            body: parser.generateLongPressHandler(handler)
          }
        }
      });
      return parser.transformNode(node);
    }
  }
};
```

## 4. Web Worker 架构实现

### 4.1 主 Worker 文件 (lynx-transform-worker.js)

```typescript
// src/routes/lynx_preview/workers/lynx-transform-worker.ts
import { BrowserWebSpeedyEngine } from '../engines/BrowserWebSpeedyEngine';
import { OFFICIAL_ELEMENT_MAPPINGS } from '../mappings/element-mappings';
import { OFFICIAL_DIRECTIVE_MAPPINGS } from '../mappings/directive-mappings';

interface WorkerMessage {
  id: string;
  type: 'TRANSFORM' | 'HEALTH_CHECK' | 'CONFIGURE';
  payload: any;
}

interface TransformPayload {
  files: FileStructure;
  options: WebSpeedyConfig;
}

class LynxTransformWorker {
  private engine: BrowserWebSpeedyEngine;
  private isInitialized = false;

  constructor() {
    this.initializeEngine();
  }

  /**
   * 初始化转换引擎
   */
  private async initializeEngine() {
    try {
      // 默认配置 - 完全兼容 @byted-lynx/web-speedy-plugin
      const defaultConfig: WebSpeedyConfig = {
        runtimeDslMode: 'ttml',
        spa: false,
        rpx: {
          rpxMode: 'vw',
          designWidth: 750
        },
        pageConfig: {
          useLepusNG: true,
          lepusStrict: false,
          lepusNullPropAsUndef: true,
          enableCSSInheritance: true,
          enableComponentNullProp: true,
          defaultDisplayLinear: true,
          defaultOverflowVisible: true,
          enableTextOverflow: true,
          webBumpAllSelectorSpecificity: true
        },
        ttmlOptions: {
          enableInferComponent: true,
          plugins: [],
          usingComponents: {}
        }
      };

      this.engine = new BrowserWebSpeedyEngine(defaultConfig);
      this.isInitialized = true;

      console.log('🚀 [LynxTransformWorker] 转换引擎初始化完成');
    } catch (error) {
      console.error('❌ [LynxTransformWorker] 引擎初始化失败:', error);
      throw error;
    }
  }

  /**
   * 处理主线程消息
   */
  async handleMessage(event: MessageEvent<WorkerMessage>) {
    const { id, type, payload } = event.data;

    try {
      let result: any;

      switch (type) {
        case 'TRANSFORM':
          result = await this.handleTransform(payload as TransformPayload);
          break;

        case 'HEALTH_CHECK':
          result = { status: 'healthy', initialized: this.isInitialized };
          break;

        case 'CONFIGURE':
          result = await this.handleConfigure(payload);
          break;

        default:
          throw new Error(`未知的消息类型: ${type}`);
      }

      this.postMessage({
        id,
        success: true,
        result
      });

    } catch (error) {
      this.postMessage({
        id,
        success: false,
        error: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * 处理转换请求
   */
  private async handleTransform(payload: TransformPayload): Promise<TransformResult> {
    if (!this.isInitialized) {
      throw new Error('转换引擎未初始化');
    }

    const { files, options } = payload;

    console.log('🔄 [LynxTransformWorker] 开始转换', {
      fileCount: Object.keys(files).length,
      options: options
    });

    const startTime = performance.now();

    // 执行转换
    const result = await this.engine.transform(files, options);

    const endTime = performance.now();
    const transformTime = endTime - startTime;

    console.log('✅ [LynxTransformWorker] 转换完成', {
      success: result.success,
      transformTime: `${transformTime.toFixed(2)}ms`
    });

    return {
      ...result,
      metadata: {
        ...result.metadata,
        transformTime,
        workerTime: transformTime,
        engine: 'browser-web-speedy'
      }
    };
  }

  /**
   * 处理配置更新
   */
  private async handleConfigure(newConfig: Partial<WebSpeedyConfig>) {
    if (this.engine) {
      this.engine.updateConfig(newConfig);
    }

    return { configured: true, config: newConfig };
  }

  /**
   * 向主线程发送消息
   */
  private postMessage(response: any) {
    // @ts-expect-error - Worker 环境
    self.postMessage(response);
  }
}

// 初始化 Worker
const worker = new LynxTransformWorker();

// 监听主线程消息
// @ts-expect-error - Worker 环境
self.addEventListener('message', (event: MessageEvent<WorkerMessage>) => {
  worker.handleMessage(event);
});

// 错误处理
// @ts-expect-error - Worker 环境
self.addEventListener('error', (error: ErrorEvent) => {
  console.error('🚨 [LynxTransformWorker] Worker 错误:', error);
  // @ts-expect-error - Worker 环境
  self.postMessage({
    success: false,
    error: 'Worker 内部错误',
    message: error.message
  });
});

console.log('🔧 [LynxTransformWorker] Worker 已启动');
```

### 4.2 Worker 管理器 (WorkerManager)

```typescript
// src/routes/lynx_preview/services/WorkerManager.ts
export class WorkerManager {
  private worker: Worker | null = null;
  private messageId = 0;
  private pendingMessages = new Map<string, {
    resolve: (value: any) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }>();

  constructor() {
    this.initializeWorker();
  }

  /**
   * 初始化 Worker
   */
  private initializeWorker() {
    try {
      // 创建 Worker
      this.worker = new Worker(
        new URL('../workers/lynx-transform-worker.ts', import.meta.url),
        { type: 'module' }
      );

      // 监听 Worker 消息
      this.worker.onmessage = (event) => {
        this.handleWorkerMessage(event.data);
      };

      // 监听 Worker 错误
      this.worker.onerror = (error) => {
        console.error('🚨 [WorkerManager] Worker 错误:', error);
        this.rejectAllPendingMessages(new Error('Worker 错误'));
      };

      console.log('🔧 [WorkerManager] Worker 初始化完成');
    } catch (error) {
      console.error('❌ [WorkerManager] Worker 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行转换
   */
  async transform(files: FileStructure, options?: Partial<WebSpeedyConfig>): Promise<TransformResult> {
    if (!this.worker) {
      throw new Error('Worker 未初始化');
    }

    const messageId = this.generateMessageId();

    return new Promise((resolve, reject) => {
      // 设置超时
      const timeout = setTimeout(() => {
        this.pendingMessages.delete(messageId);
        reject(new Error('转换超时'));
      }, 30000); // 30秒超时

      // 存储 Promise 回调
      this.pendingMessages.set(messageId, {
        resolve,
        reject,
        timeout
      });

      // 发送转换请求
      this.worker!.postMessage({
        id: messageId,
        type: 'TRANSFORM',
        payload: { files, options }
      });
    });
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    if (!this.worker) return false;

    try {
      const result = await this.sendMessage('HEALTH_CHECK', {});
      return result.status === 'healthy';
    } catch {
      return false;
    }
  }

  /**
   * 处理 Worker 消息
   */
  private handleWorkerMessage(data: any) {
    const { id, success, result, error } = data;

    const pending = this.pendingMessages.get(id);
    if (!pending) return;

    // 清理
    clearTimeout(pending.timeout);
    this.pendingMessages.delete(id);

    if (success) {
      pending.resolve(result);
    } else {
      pending.reject(new Error(error));
    }
  }

  /**
   * 发送消息到 Worker
   */
  private sendMessage(type: string, payload: any): Promise<any> {
    if (!this.worker) {
      throw new Error('Worker 未初始化');
    }

    const messageId = this.generateMessageId();

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.pendingMessages.delete(messageId);
        reject(new Error('消息超时'));
      }, 10000);

      this.pendingMessages.set(messageId, {
        resolve,
        reject,
        timeout
      });

      this.worker!.postMessage({
        id: messageId,
        type,
        payload
      });
    });
  }

  /**
   * 生成消息 ID
   */
  private generateMessageId(): string {
    return `msg_${++this.messageId}_${Date.now()}`;
  }

  /**
   * 拒绝所有待处理的消息
   */
  private rejectAllPendingMessages(error: Error) {
    this.pendingMessages.forEach(({ reject, timeout }) => {
      clearTimeout(timeout);
      reject(error);
    });
    this.pendingMessages.clear();
  }

  /**
   * 销毁 Worker
   */
  dispose() {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }

    this.rejectAllPendingMessages(new Error('Worker 已销毁'));
  }
}
```

## 5. 前端组件实现

### 5.1 主页面组件

```typescript
// src/routes/lynx_preview/page.tsx
'use client';

import React, { useState, useEffect, useRef } from 'react';
import { WorkerManager } from './services/WorkerManager';
import { URLResolver } from './services/URLResolver';
import { FileDownloader } from './services/FileDownloader';
import { URLInputSection } from './components/URLInputSection';
import { ProcessingStatus } from './components/ProcessingStatus';
import { PreviewSection } from './components/PreviewSection';
import { ErrorBoundary } from './components/ErrorBoundary';

export default function LynxPreviewPage() {
  const [inputUrl, setInputUrl] = useState('');
  const [files, setFiles] = useState<FileStructure | null>(null);
  const [transformResult, setTransformResult] = useState<TransformResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState<string>('idle');

  const workerManagerRef = useRef<WorkerManager | null>(null);
  const urlResolverRef = useRef<URLResolver | null>(null);
  const fileDownloaderRef = useRef<FileDownloader | null>(null);

  useEffect(() => {
    // 初始化服务
    workerManagerRef.current = new WorkerManager();
    urlResolverRef.current = new URLResolver();
    fileDownloaderRef.current = new FileDownloader();

    return () => {
      // 清理资源
      workerManagerRef.current?.dispose();
    };
  }, []);

  const handleUrlSubmit = async () => {
    if (!inputUrl.trim()) return;

    setLoading(true);
    setError(null);
    setProgress(0);
    setCurrentStep('parsing');

    try {
      // 1. 解析 URL
      console.log('🔍 开始解析 URL...');
      const urlInfo = await urlResolverRef.current!.parseUrl(inputUrl);
      setProgress(20);
      setCurrentStep('downloading');

      // 2. 下载文件
      console.log('📥 开始下载文件...');
      const downloadedFiles = await fileDownloaderRef.current!.downloadAndExtract(
        urlInfo.downloadUrl,
        (downloadProgress) => {
          setProgress(20 + (downloadProgress * 0.4)); // 20% - 60%
        }
      );

      setFiles(downloadedFiles);
      setProgress(60);
      setCurrentStep('transforming');

      // 3. 验证文件结构
      const validation = fileDownloaderRef.current!.validateFileStructure(downloadedFiles);
      if (!validation.isValid) {
        throw new Error(`文件结构验证失败: ${validation.errors.join(', ')}`);
      }

      // 4. 执行转换
      console.log('🔄 开始转换...');
      const result = await workerManagerRef.current!.transform(downloadedFiles, {
        runtimeDslMode: 'ttml',
        rpx: {
          rpxMode: 'vw',
          designWidth: 750
        }
      });

      setTransformResult(result);
      setProgress(100);
      setCurrentStep('completed');

      console.log('✅ 转换完成!', result);

    } catch (err) {
      console.error('❌ 处理失败:', err);
      setError(err instanceof Error ? err.message : String(err));
      setCurrentStep('error');
    } finally {
      setLoading(false);
    }
  };

  const handleClearInput = () => {
    setInputUrl('');
    setFiles(null);
    setTransformResult(null);
    setError(null);
    setProgress(0);
    setCurrentStep('idle');
  };

  return (
    <ErrorBoundary>
      <div className="lynx-preview-container">
        <header className="lynx-preview-header">
          <h1>🚀 Lynx Preview - 在线转换工具</h1>
          <p>完全基于浏览器的 @byted-lynx/web-speedy-plugin 实现</p>
        </header>

        <URLInputSection
          value={inputUrl}
          onChange={setInputUrl}
          onSubmit={handleUrlSubmit}
          onClear={handleClearInput}
          loading={loading}
        />

        <ProcessingStatus
          loading={loading}
          error={error}
          progress={progress}
          currentStep={currentStep}
          files={files}
          result={transformResult}
        />

        {(files || transformResult) && (
          <PreviewSection
            files={files}
            result={transformResult}
          />
        )}
      </div>
    </ErrorBoundary>
  );
}
```

## 6. 性能优化策略

### 6.1 缓存机制
- **URL 缓存**：相同 URL 的文件缓存
- **转换缓存**：相同内容的转换结果缓存
- **预览缓存**：渲染结果缓存

### 6.2 异步处理
- **流式下载**：大文件分块下载
- **并行转换**：多文件并行处理
- **懒加载**：按需加载预览内容

## 7. 下一步实施计划

### 阶段一：基础架构 (1-2 周)
1. 创建 `lynx_preview` 路由和基础页面
2. 实现 URL 解析器和文件下载器
3. 搭建基础 UI 框架

### 阶段二：转换引擎 (2-3 周)
1. 集成 Web-Speedy-Plugin API
2. 增强 Parse5 引擎
3. 实现智能路由选择

### 阶段三：用户体验 (1-2 周)
1. 完善预览界面
2. 添加错误处理
3. 性能优化和测试

这个架构设计充分考虑了现有实现的优缺点，采用混合方案确保既有完整的转换能力，又有良好的用户体验。

## 8. 技术细节补充

### 8.1 Web-Speedy-Plugin 集成方案

#### 8.1.1 服务端 API 设计
```typescript
// src/routes/api/lynx-transform/route.ts
import { webSpeedyPlugin } from '@byted-lynx/web-speedy-plugin';

export async function POST(request: Request) {
  try {
    const { files, options } = await request.json();

    // 配置转换选项
    const transformOptions = {
      runtimeDslMode: 'ttml',
      spa: false,
      rpx: {
        rpxMode: 'vw',
        designWidth: 750
      },
      pageConfig: {
        useLepusNG: true,
        lepusStrict: false,
        webBumpAllSelectorSpecificity: true,
        enableCSSInheritance: true,
        defaultDisplayLinear: true,
        enableTextOverflow: true
      },
      ttmlOptions: {
        enableInferComponent: true,
        plugins: [],
        usingComponents: {}
      },
      ...options
    };

    // 执行转换
    const result = await webSpeedyPlugin.transformFiles(files, transformOptions);

    return Response.json({
      success: true,
      result,
      metadata: {
        engine: 'web-speedy-plugin',
        version: '5.1.1',
        transformTime: Date.now()
      }
    });

  } catch (error) {
    return Response.json({
      success: false,
      error: error.message,
      fallback: true // 指示前端使用备用引擎
    }, { status: 500 });
  }
}
```

#### 8.1.2 文件结构处理
```typescript
// src/routes/lynx_preview/services/FileStructureProcessor.ts
export class FileStructureProcessor {

  /**
   * 将下载的文件转换为 web-speedy-plugin 期望的格式
   */
  processForWebSpeedy(files: Record<string, string>): WebSpeedyInput {
    const processed = {
      entry: this.findEntryFile(files),
      files: {},
      dependencies: this.analyzeDependencies(files),
      config: this.extractConfig(files)
    };

    // 处理每个文件
    Object.entries(files).forEach(([path, content]) => {
      const fileType = this.getFileType(path);

      processed.files[path] = {
        content,
        type: fileType,
        dependencies: this.extractFileDependencies(content, fileType)
      };
    });

    return processed;
  }

  /**
   * 查找入口文件
   */
  private findEntryFile(files: Record<string, string>): string {
    // 优先级：index.ttml > main.ttml > 第一个 .ttml 文件
    const candidates = ['index.ttml', 'main.ttml'];

    for (const candidate of candidates) {
      if (files[candidate]) return candidate;
    }

    // 查找第一个 .ttml 文件
    const tttmlFiles = Object.keys(files).filter(path => path.endsWith('.ttml'));
    return tttmlFiles[0] || Object.keys(files)[0];
  }

  /**
   * 分析文件依赖关系
   */
  private analyzeDependencies(files: Record<string, string>): DependencyGraph {
    const graph: DependencyGraph = {};

    Object.entries(files).forEach(([path, content]) => {
      graph[path] = this.extractFileDependencies(content, this.getFileType(path));
    });

    return graph;
  }
}
```

### 8.2 Enhanced Parse5 引擎改进

#### 8.2.1 映射规则增强
```typescript
// src/routes/lynx_preview/engines/EnhancedParse5Engine.ts
export class EnhancedParse5Engine extends Parse5TransformEngine {

  constructor(config: EnhancedParse5Config) {
    super(config);

    // 加载从 web-speedy-plugin 提取的完整映射规则
    this.loadComprehensiveMappings();

    // 初始化增强的作用域系统
    this.scopeSystem = new EnhancedScopeSystem(config.scopeConfig);

    // 初始化模块解析器
    this.moduleResolver = new ModuleResolver(config.moduleConfig);
  }

  /**
   * 加载完整的映射规则
   */
  private loadComprehensiveMappings(): void {
    // 从 web-speedy-plugin 文档中提取的完整映射
    this.elementMappings = {
      ...BASIC_ELEMENT_MAPPING,
      ...ADVANCED_ELEMENT_MAPPING,
      ...COMPONENT_MAPPING
    };

    this.directiveMappings = {
      ...CONDITIONAL_DIRECTIVES,
      ...ITERATION_DIRECTIVES,
      ...EVENT_DIRECTIVES
    };

    this.styleMappings = {
      ...RPX_CONVERSION_RULES,
      ...SCOPE_RULES,
      ...OPTIMIZATION_RULES
    };
  }

  /**
   * 增强的转换方法
   */
  async convertWithEnhancements(files: FileStructure): Promise<TransformResult> {
    // 1. 预处理：分析文件依赖关系
    const dependencyGraph = this.moduleResolver.analyze(files);

    // 2. 按依赖顺序转换文件
    const sortedFiles = this.topologicalSort(dependencyGraph);
    const results = {};

    for (const filePath of sortedFiles) {
      const fileContent = files[filePath];
      const fileType = this.getFileType(filePath);

      switch (fileType) {
        case 'ttml':
          results[filePath] = await this.convertTTMLWithDependencies(
            fileContent,
            filePath,
            results
          );
          break;

        case 'ttss':
          results[filePath] = await this.convertTTSSWithScope(
            fileContent,
            filePath,
            results
          );
          break;

        case 'js':
          results[filePath] = await this.convertJSWithModules(
            fileContent,
            filePath,
            results
          );
          break;
      }
    }

    // 3. 生成最终的 HTML
    return this.generateFinalHTML(results, dependencyGraph);
  }
}
```

#### 8.2.2 作用域系统增强
```typescript
// src/routes/lynx_preview/engines/EnhancedScopeSystem.ts
export class EnhancedScopeSystem {

  /**
   * 生成与 web-speedy-plugin 兼容的 tagV
   */
  generateTagV(filePath: string, content: string): string {
    // 模拟 web-speedy-plugin 的 hash 算法
    const combined = filePath + content;
    const hash = this.webSpeedyCompatibleHash(combined);
    return hash.substring(0, 5);
  }

  /**
   * 应用样式作用域
   */
  applyScopeToCSS(css: string, tagV: string, options: ScopeOptions): string {
    let scopedCSS = css;

    // 1. 基础选择器作用域化
    scopedCSS = this.scopeSelectors(scopedCSS, tagV);

    // 2. 选择器优先级提升（webBumpAllSelectorSpecificity）
    if (options.bumpSpecificity) {
      scopedCSS = this.bumpSelectorSpecificity(scopedCSS);
    }

    // 3. 处理全局样式（globalModulePaths）
    if (options.globalPaths?.includes(filePath)) {
      scopedCSS = this.preserveGlobalStyles(scopedCSS);
    }

    // 4. CSS 继承处理（enableCSSInheritance）
    if (options.enableInheritance) {
      scopedCSS = this.processCSSInheritance(scopedCSS);
    }

    return scopedCSS;
  }

  /**
   * 兼容 web-speedy-plugin 的哈希算法
   */
  private webSpeedyCompatibleHash(input: string): string {
    // 实现与 web-speedy-plugin 相同的哈希逻辑
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为 32 位整数
    }
    return Math.abs(hash).toString(36);
  }
}
```

### 8.3 智能引擎选择算法

#### 8.3.1 复杂度分析器
```typescript
// src/routes/lynx_preview/services/ComplexityAnalyzer.ts
export class ComplexityAnalyzer {

  analyze(files: FileStructure): ComplexityAnalysis {
    const analysis = {
      fileCount: Object.keys(files).length,
      componentCount: this.countComponents(files),
      dependencyDepth: this.analyzeDependencyDepth(files),
      advancedFeatures: this.detectAdvancedFeatures(files),
      totalSize: this.calculateTotalSize(files),
      complexityScore: 0
    };

    // 计算复杂度评分
    analysis.complexityScore = this.calculateComplexityScore(analysis);

    return analysis;
  }

  /**
   * 检测高级特性
   */
  private detectAdvancedFeatures(files: FileStructure): AdvancedFeature[] {
    const features: AdvancedFeature[] = [];

    Object.entries(files).forEach(([path, content]) => {
      // 检测组件导入
      if (content.includes('import') || content.includes('require')) {
        features.push('module_imports');
      }

      // 检测复杂指令
      if (content.match(/lx:(if|for|elif|else)/g)) {
        features.push('complex_directives');
      }

      // 检测 Lepus 语法
      if (content.includes('Card(') || content.includes('setData')) {
        features.push('lepus_syntax');
      }

      // 检测高级 CSS 特性
      if (content.includes('@media') || content.includes('@keyframes')) {
        features.push('advanced_css');
      }

      // 检测组件化特性
      if (content.includes('usingComponents') || content.includes('component:')) {
        features.push('component_system');
      }
    });

    return [...new Set(features)];
  }

  /**
   * 计算复杂度评分
   */
  private calculateComplexityScore(analysis: ComplexityAnalysis): number {
    let score = 0;

    // 文件数量权重
    score += Math.min(analysis.fileCount * 2, 20);

    // 组件数量权重
    score += Math.min(analysis.componentCount * 5, 30);

    // 依赖深度权重
    score += Math.min(analysis.dependencyDepth * 10, 40);

    // 高级特性权重
    score += analysis.advancedFeatures.length * 8;

    // 文件大小权重
    score += Math.min(analysis.totalSize / 1024, 10); // KB

    return Math.min(score, 100);
  }

  /**
   * 选择最适合的引擎
   */
  selectEngine(analysis: ComplexityAnalysis): EngineChoice {
    if (analysis.complexityScore > 60) {
      return {
        primary: 'web-speedy-plugin',
        fallback: 'enhanced-parse5',
        reason: `高复杂度项目 (评分: ${analysis.complexityScore})，需要完整的转换能力`
      };
    } else if (analysis.complexityScore > 30) {
      return {
        primary: 'enhanced-parse5',
        fallback: 'web-speedy-plugin',
        reason: `中等复杂度项目 (评分: ${analysis.complexityScore})，优先使用浏览器端引擎`
      };
    } else {
      return {
        primary: 'enhanced-parse5',
        fallback: 'basic-parse5',
        reason: `简单项目 (评分: ${analysis.complexityScore})，使用轻量级引擎即可`
      };
    }
  }
}
```

### 8.4 URL 解析和文件处理

#### 8.4.1 URL 解析器实现
```typescript
// src/routes/lynx_preview/services/URLResolver.ts
export class URLResolver {

  /**
   * 解析 CDN URL
   */
  async parseCDNUrl(url: string): Promise<CDNUrlInfo> {
    // 解码 URL
    const decodedUrl = decodeURIComponent(url);

    // 验证 URL 格式
    if (!this.isValidCDNUrl(decodedUrl)) {
      throw new Error('无效的 CDN URL 格式');
    }

    // 提取文件信息
    const urlParts = new URL(decodedUrl);
    const filename = urlParts.pathname.split('/').pop() || 'unknown';

    // 获取文件元信息
    const metadata = await this.fetchFileMetadata(decodedUrl);

    return {
      downloadUrl: decodedUrl,
      filename,
      size: metadata.size,
      type: filename.endsWith('.zip') ? 'zip' : 'single',
      lastModified: metadata.lastModified
    };
  }

  /**
   * 解析 Playground URL
   */
  async parsePlaygroundUrl(url: string): Promise<PlaygroundUrlInfo> {
    const urlObj = new URL(url);
    const params = new URLSearchParams(urlObj.search);

    // 提取配置参数
    const config = {
      useSpeedy: params.get('useSpeedy') === 'true',
      exampleType: params.get('exampleType') || 'ttml-nodiff',
      layout: params.get('layout') || 'preview',
      sdkVersion: params.get('sdkVersion') || 'latest'
    };

    // 提取项目 URL
    const projectUrl = params.get('project');
    if (!projectUrl) {
      throw new Error('Playground URL 中缺少 project 参数');
    }

    // 解析项目 URL（通常也是 CDN URL）
    const projectInfo = await this.parseCDNUrl(projectUrl);

    return {
      projectUrl: projectInfo.downloadUrl,
      config,
      metadata: projectInfo
    };
  }

  /**
   * 验证 CDN URL 格式
   */
  private isValidCDNUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);

      // 检查域名白名单
      const allowedDomains = [
        'lf3-static.bytednsdoc.com',
        'lf6-static.bytednsdoc.com',
        'lf9-static.bytednsdoc.com'
      ];

      return allowedDomains.some(domain => urlObj.hostname.includes(domain));
    } catch {
      return false;
    }
  }
}
```

#### 8.4.2 文件下载和解压
```typescript
// src/routes/lynx_preview/services/FileDownloader.ts
export class FileDownloader {

  /**
   * 下载并解压 ZIP 文件
   */
  async downloadAndExtract(url: string, onProgress?: (progress: number) => void): Promise<FileStructure> {
    try {
      // 下载文件
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`下载失败: ${response.status} ${response.statusText}`);
      }

      // 获取文件大小用于进度计算
      const contentLength = response.headers.get('content-length');
      const totalSize = contentLength ? parseInt(contentLength, 10) : 0;

      // 流式下载
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法创建文件读取器');
      }

      const chunks: Uint8Array[] = [];
      let downloadedSize = 0;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        chunks.push(value);
        downloadedSize += value.length;

        // 报告下载进度
        if (onProgress && totalSize > 0) {
          onProgress((downloadedSize / totalSize) * 100);
        }
      }

      // 合并数据块
      const fileData = new Uint8Array(downloadedSize);
      let offset = 0;
      for (const chunk of chunks) {
        fileData.set(chunk, offset);
        offset += chunk.length;
      }

      // 解压 ZIP 文件
      return this.extractZipFile(fileData);

    } catch (error) {
      throw new Error(`文件下载失败: ${error.message}`);
    }
  }

  /**
   * 解压 ZIP 文件
   */
  private async extractZipFile(data: Uint8Array): Promise<FileStructure> {
    // 使用 JSZip 库解压
    const JSZip = (await import('jszip')).default;
    const zip = new JSZip();

    const zipContent = await zip.loadAsync(data);
    const files: FileStructure = {};

    // 遍历 ZIP 文件中的所有文件
    for (const [relativePath, file] of Object.entries(zipContent.files)) {
      if (!file.dir) {
        // 只处理文件，跳过目录
        const content = await file.async('string');
        files[relativePath] = content;
      }
    }

    return files;
  }

  /**
   * 验证文件结构
   */
  validateFileStructure(files: FileStructure): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查是否有 TTML 文件
    const tttmlFiles = Object.keys(files).filter(path => path.endsWith('.ttml'));
    if (tttmlFiles.length === 0) {
      errors.push('未找到 .ttml 文件');
    }

    // 检查入口文件
    const hasIndex = files['index.ttml'] || files['main.ttml'];
    if (!hasIndex && tttmlFiles.length > 1) {
      warnings.push('未找到明确的入口文件 (index.ttml 或 main.ttml)');
    }

    // 检查文件大小
    const totalSize = Object.values(files).reduce((sum, content) => sum + content.length, 0);
    if (totalSize > 10 * 1024 * 1024) { // 10MB
      warnings.push('项目文件总大小超过 10MB，可能影响转换性能');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      fileCount: Object.keys(files).length,
      totalSize
    };
  }
}

### 8.5 前端组件架构

#### 8.5.1 主页面组件

```typescript
// src/routes/lynx_preview/page.tsx
export default function LynxPreviewPage() {
  const [inputUrl, setInputUrl] = useState('');
  const [files, setFiles] = useState<FileStructure | null>(null);
  const [transformResult, setTransformResult] = useState<TransformResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleUrlSubmit = async () => {
    setLoading(true);
    setError(null);

    try {
      // 1. 解析 URL
      const urlInfo = await urlResolver.parseUrl(inputUrl);

      // 2. 下载文件
      const downloadedFiles = await fileDownloader.downloadAndExtract(
        urlInfo.downloadUrl,
        (progress) => setProgress(progress)
      );

      setFiles(downloadedFiles);

      // 3. 分析复杂度
      const complexity = complexityAnalyzer.analyze(downloadedFiles);
      const engineChoice = complexityAnalyzer.selectEngine(complexity);

      // 4. 执行转换
      const result = await transformRouter.transform(downloadedFiles, engineChoice);
      setTransformResult(result);

    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="lynx-preview-container">
      <URLInputSection
        value={inputUrl}
        onChange={setInputUrl}
        onSubmit={handleUrlSubmit}
        loading={loading}
      />

      <ProcessingStatus
        loading={loading}
        error={error}
        files={files}
        result={transformResult}
      />

      <PreviewSection
        files={files}
        result={transformResult}
      />
    </div>
  );
}
```

#### 8.5.2 URL 输入组件

```typescript
// src/routes/lynx_preview/components/URLInputSection.tsx
export function URLInputSection({ value, onChange, onSubmit, loading }: URLInputProps) {
  const [urlType, setUrlType] = useState<'cdn' | 'playground' | 'upload'>('cdn');

  return (
    <div className="url-input-section">
      <div className="input-type-tabs">
        <button
          className={urlType === 'cdn' ? 'active' : ''}
          onClick={() => setUrlType('cdn')}
        >
          CDN URL
        </button>
        <button
          className={urlType === 'playground' ? 'active' : ''}
          onClick={() => setUrlType('playground')}
        >
          Playground URL
        </button>
        <button
          className={urlType === 'upload' ? 'active' : ''}
          onClick={() => setUrlType('upload')}
        >
          本地上传
        </button>
      </div>

      {urlType === 'upload' ? (
        <FileUploadArea onFilesSelected={handleFileUpload} />
      ) : (
        <div className="url-input-area">
          <input
            type="text"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={getPlaceholder(urlType)}
            className="url-input"
          />
          <button
            onClick={onSubmit}
            disabled={loading || !value.trim()}
            className="submit-button"
          >
            {loading ? '解析中...' : '解析'}
          </button>
        </div>
      )}
    </div>
  );
}
```

#### 8.5.3 预览组件

```typescript
// src/routes/lynx_preview/components/PreviewSection.tsx
export function PreviewSection({ files, result }: PreviewSectionProps) {
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [previewMode, setPreviewMode] = useState<'mobile' | 'desktop'>('mobile');

  if (!files || !result) {
    return <div className="preview-placeholder">请先输入 URL 并解析</div>;
  }

  return (
    <div className="preview-section">
      <div className="file-explorer">
        <FileTree
          files={files}
          selectedFile={selectedFile}
          onFileSelect={setSelectedFile}
        />
      </div>

      <div className="preview-area">
        <div className="preview-controls">
          <button
            className={previewMode === 'mobile' ? 'active' : ''}
            onClick={() => setPreviewMode('mobile')}
          >
            📱 移动端
          </button>
          <button
            className={previewMode === 'desktop' ? 'active' : ''}
            onClick={() => setPreviewMode('desktop')}
          >
            💻 桌面端
          </button>
        </div>

        <div className={`preview-frame ${previewMode}`}>
          {result.success ? (
            <iframe
              srcDoc={result.html}
              className="preview-iframe"
              sandbox="allow-scripts allow-same-origin"
            />
          ) : (
            <ErrorDisplay error={result.error} />
          )}
        </div>
      </div>

      {selectedFile && (
        <div className="code-viewer">
          <CodeEditor
            file={selectedFile}
            content={files[selectedFile]}
            readOnly={true}
          />
        </div>
      )}
    </div>
  );
}
```

### 8.6 样式设计

#### 8.6.1 主要样式文件

```scss
// src/routes/lynx_preview/styles/main.scss
.lynx-preview-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;

  .url-input-section {
    background: white;
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;

    .input-type-tabs {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;

      button {
        padding: 8px 16px;
        border: 1px solid #d0d0d0;
        background: #f8f8f8;
        border-radius: 4px;
        cursor: pointer;

        &.active {
          background: #007bff;
          color: white;
          border-color: #007bff;
        }
      }
    }

    .url-input-area {
      display: flex;
      gap: 10px;

      .url-input {
        flex: 1;
        padding: 10px;
        border: 1px solid #d0d0d0;
        border-radius: 4px;
        font-size: 14px;
      }

      .submit-button {
        padding: 10px 20px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;

        &:disabled {
          background: #ccc;
          cursor: not-allowed;
        }
      }
    }
  }

  .preview-section {
    flex: 1;
    display: flex;
    gap: 10px;
    padding: 10px;

    .file-explorer {
      width: 250px;
      background: white;
      border-radius: 4px;
      padding: 10px;
      overflow-y: auto;
    }

    .preview-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: white;
      border-radius: 4px;

      .preview-controls {
        padding: 10px;
        border-bottom: 1px solid #e0e0e0;
        display: flex;
        gap: 10px;
      }

      .preview-frame {
        flex: 1;
        padding: 20px;
        display: flex;
        justify-content: center;
        align-items: center;

        &.mobile .preview-iframe {
          width: 375px;
          height: 667px;
          border: 1px solid #ccc;
          border-radius: 8px;
        }

        &.desktop .preview-iframe {
          width: 100%;
          height: 100%;
          border: 1px solid #ccc;
        }
      }
    }

    .code-viewer {
      width: 400px;
      background: white;
      border-radius: 4px;
    }
  }
}
```

### 8.7 错误处理和用户体验

#### 8.7.1 错误边界组件

```typescript
// src/routes/lynx_preview/components/ErrorBoundary.tsx
export class LynxPreviewErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Lynx Preview Error:', error, errorInfo);

    // 发送错误报告
    this.reportError(error, errorInfo);
  }

  private reportError(error: Error, errorInfo: ErrorInfo) {
    // 错误上报逻辑
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // 发送到错误监控服务
    fetch('/api/error-report', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(errorReport)
    }).catch(console.error);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>🚨 预览出现错误</h2>
          <p>很抱歉，预览过程中出现了错误。</p>
          <details>
            <summary>错误详情</summary>
            <pre>{this.state.error?.stack}</pre>
          </details>
          <button onClick={() => window.location.reload()}>
            刷新页面
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```
