# React Infinite Loop Fix Summary - Updated

## Problem Description

The application was experiencing a React infinite rendering loop with the following error:
```
logger.ts:492 [Logger] 检测到可能的无限渲染循环，抑制debug日志输出
    at Preview (http://localhost:8080/static/js/async/code_generate/page.js:18689:133)
    at PreviewErrorBoundary (http://localhost:8080/static/js/async/code_generate/page.js:18515:74)
```

This was occurring in the `Preview` component in `src/routes/code_generate/preview.tsx`.

## Root Cause Analysis

The infinite loop was caused by circular dependencies between `useMemo` and `useCallback` hooks:

```typescript
// PROBLEMATIC CODE - useMemo with function dependencies
const renderContentArea = useMemo(() => {
  // Component JSX
}, [
  // 包含大量函数依赖 - 这是问题所在！
  handleWebCodeChange,
  handleLynxCodeChange,
  handleLynxConvert,
  handleRegenerateWebCode,
  getViewStyle,
  setLynxViewMode,
]);
```

**The Problem Chain:**
1. `useMemo` depends on `useCallback` functions
2. `useCallback` functions have changing dependencies
3. Dependencies change → `useCallback` recreates → `useMemo` recalculates → Component re-renders → Dependencies change again
4. **Infinite loop!**

## Solution Applied

### 1. Use `useRef` for Stable Event Handlers

**Before (Problematic)**:
```typescript
const renderContentArea = useMemo(() => {
  return (
    <WebTabContent
      onCodeChange={handleWebCodeChange}
      onConvert={handleLynxConvert}
      onRefresh={handleRegenerateWebCode}
    />
  );
}, [
  // 函数依赖导致无限循环
  handleWebCodeChange,
  handleLynxCodeChange,
  handleLynxConvert,
  handleRegenerateWebCode,
]);
```

**After (Fixed)**:
```typescript
// 创建稳定的事件处理函数引用
const stableHandlers = useRef({
  handleWebCodeChange: (newCode: string) => setEditableWebCode(newCode),
  handleLynxCodeChange: (newCode: string) => setEditableLynxCode(newCode),
  // ... 其他处理函数
});

const renderContentArea = useMemo(() => {
  return (
    <WebTabContent
      onCodeChange={stableHandlers.current.handleWebCodeChange}
      onConvert={stableHandlers.current.handleLynxConvertManual}
      onRefresh={stableHandlers.current.handleRegenerateWebCode}
    />
  );
}, [
  // 只包含真正影响渲染的状态值，移除所有函数依赖
  effectiveActiveTab,
  webState.cleanWebCode,
  webState.webCode,
  webState.isWebRPCLoading,
  lynxState.lynxCode,
  webRPCStatusUnified.error,
  isWebEditMode,
  isLynxEditMode,
  lynxViewMode,
  getViewStyle, // 这个函数依赖contentStyle，保留
]);
```

## Key Principles Applied

### ✅ DO:
- Only include values in dependency arrays that the effect **reads** but doesn't **modify**
- Use refs to avoid unnecessary dependencies
- Consolidate related state changes into a single useEffect
- Use proper cleanup functions for timers

### ❌ DON'T:
- Include state variables in dependency arrays that the effect modifies
- Have multiple useEffects modifying the same state variable
- Create circular dependencies between state and effects

## Verification

The fix ensures:
1. ✅ No infinite loops in React rendering
2. ✅ Sidebar state is properly managed
3. ✅ Tab switching works correctly
4. ✅ No redundant state updates
5. ✅ Proper cleanup of resources

## Files Modified

- `src/routes/code_generate/preview.tsx`: Fixed infinite loop by removing problematic useEffects

## Testing

To verify the fix:
1. Open the application
2. Switch between Web, Lynx, and Mobile tabs
3. Check browser console for React warnings
4. Ensure sidebar shows/hides correctly
5. Verify no "Maximum update depth exceeded" warnings

The fix maintains all existing functionality while eliminating the infinite loop issue.
