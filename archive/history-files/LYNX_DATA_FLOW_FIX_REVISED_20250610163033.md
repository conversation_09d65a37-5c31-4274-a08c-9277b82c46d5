# Lynx数据流修复方案（修订版）

## 问题分析

根据对Lynx代码生成流程的诊断，我们发现了导致Lynx切换到Playground失败的关键问题：

1. Lynx代码生成处理逻辑与Web代码不一致，导致解析问题
2. 统一Context到组件的通信存在问题
3. 视图模式切换无法正确传递

## 修复原则

**核心原则：确保Lynx代码处理与Web代码处理完全一致，禁止使用特殊正则表达式或自定义解析逻辑**

## 具体修复方案

### 1. 修复WebCodeJsonProcessor中的代码识别

确保Lynx代码使用与Web代码完全相同的处理逻辑，移除任何特殊处理：

```ts
// 错误做法（已移除）：为Lynx添加特殊正则表达式
// 正确做法：完全复用Web代码的检测逻辑
private isCodeContent(chunk: string): boolean {
  const trimmedChunk = chunk.trim();

  // 检查是否为JSON元数据
  if (
    (trimmedChunk.includes('"model"') &&
      trimmedChunk.includes('"choices"')) ||
    (trimmedChunk.includes('"id"') && trimmedChunk.includes('"created"')) ||
    trimmedChunk.includes('"aws_sdk_claude')
  ) {
    return false; // 这是JSON元数据，不是代码
  }

  // 与Web代码使用完全相同的检测逻辑
  return (
    CODE_PATTERNS.some(
      pattern =>
        trimmedChunk.startsWith(pattern) || trimmedChunk.includes(pattern),
    ) ||
    (trimmedChunk.includes('{') &&
      trimmedChunk.includes('}') &&
      (trimmedChunk.includes(':') || trimmedChunk.includes(';')) &&
      !trimmedChunk.includes('"choices"') && // 排除JSON API响应
      !trimmedChunk.includes('"delta"')) // 排除JSON API响应
  );
}
```

### 2. 确保LynxRPCCore使用与WebRPCCore相同的流处理方式

```ts
// 采用和Web代码完全一致的处理流程
while (!isStreamComplete) {
  const { value, done } = await reader.read();

  if (done) {
    console.log(
      '%c[LynxRPCCore] 流数据接收完成',
      'color: #52c41a; font-weight: bold',
    );
    isStreamComplete = true;
    break;
  }

  const chunk = new TextDecoder().decode(value);
  
  // 使用与Web代码完全相同的处理器
  const result = webCodeJsonProcessor.processStreamChunk(chunk);

  if (result.success && result.content) {
    const contentChunk = result.content;
    
    // 添加到累积代码 - 与Web代码完全相同的逻辑
    accumulatedCode += contentChunk;
    
    // 更新Context状态
    lynxRPCContext.updateCode(accumulatedCode);
  }
}
```

### 3. 修复统一Context到组件通信

在`LynxTabContent.tsx`中，用户无法从playground模式切换到code模式的主要原因是内部internalViewMode状态与Context不同步：

```tsx
// 修改前 - 错误做法：创建独立的内部状态
const [internalViewMode, setInternalViewMode] = useState<LynxViewMode>(viewMode || 'code');

// 监听viewMode变化，更新内部状态
useEffect(() => {
  setInternalViewMode(viewMode);
}, [viewMode]);

// 修改后 - 正确做法：直接使用Context提供的状态
const internalViewMode = viewMode || 'code';
```

### 4. 确保统一视图模式切换处理

确保使用统一Context提供的方法切换视图模式：

```tsx
// 正确的视图模式切换处理
const handleViewModeChange = (mode: LynxViewMode) => {
  // 直接调用统一Context的API
  setViewMode(mode);
  
  // 如果需要额外逻辑，可以在这里添加，但不要使用内部状态覆盖Context
  if (onViewModeChange) {
    onViewModeChange(mode);
  }
};
```

## 验证修复效果

1. 生成新的Lynx代码并确认可以正常显示
2. 点击切换到Playground模式，确认可以正常显示Playground
3. 从Playground切换回代码模式，确认可以正常切换回代码显示
4. 检查代码传输过程的进度显示是否正常

## 注意事项

1. **绝对禁止**为Lynx代码添加特殊的正则或处理逻辑
2. 所有JSON解析和代码处理必须复用与Web代码相同的逻辑
3. 视图状态切换必须使用统一Context，避免使用组件内部状态
4. 确保所有代码修改符合最小修改原则，不要改动其他功能 