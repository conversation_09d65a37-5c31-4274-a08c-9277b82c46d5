# AI流式数据处理架构分析与实现

## 核心架构概述

### 完整数据流向图

#### Web代码流向：
```
API Response → WebRPCService.fetchWebCodeStream()
→ WebCodeJsonProcessor.processStreamChunk()
→ WebRPCContext.updateWebCode()
→ WebCodeHighlight组件
→ SemiCodeHighlight组件
```

#### Lynx代码流向：
```
API Response → LynxRPCService.fetchLynxCodeStream()
→ WebCodeJsonProcessor.processStreamChunk()
→ LynxRPCContext.updateLynxCode()
→ LynxCodeHighlight组件
→ SemiCodeHighlight组件
```

## 核心模块与职责

### 1. 数据流处理层

```text
RPC/
├── Web/                 # Web代码生成相关模块
│   ├── WebRPCManager.ts # Web代码生成管理器
│   └── WebRPCCore.ts    # Web代码生成核心处理
├── Lynx/                # Lynx代码生成相关模块
│   ├── LynxRPCManager.ts # Lynx代码生成管理器
│   └── LynxRPCCore.ts    # Lynx代码生成核心处理
├── PromptComposer.ts    # 提示词组合器
└── prompts.ts           # 提示词模板
```

### 2. 状态管理层

```text
contexts/
├── WebRPCContext.tsx    # Web代码状态管理
├── WebCodeContext.tsx   # Web代码内容管理
├── UIStateContext.tsx   # UI状态管理
├── LynxRPCContext.tsx   # Lynx代码状态管理
└── LynxContext.tsx      # Lynx代码内容管理
```

### 3. 数据处理层

```text
utils/
├── webCodeJsonProcessor.ts   # Web代码JSON处理器(核心)
├── claudeStreamParser.ts     # Claude流解析器
├── contentProcessors.ts      # 内容处理器集合
├── jsonProcessor.ts          # JSON数据处理
├── rpcSharedUtils.ts         # RPC共享工具函数
└── regexPatterns.ts          # 正则表达式模式
```

### 4. 服务层

```text
services/
├── WebRPCService.ts     # Web代码生成服务
└── LynxRPCService.ts    # Lynx代码生成服务
```

## 数据处理全流程

### 1. 请求发起流程

```
用户输入 → ChatComponent
    ↓
WebRPCManager.sendMessage() / LynxRPCManager.sendMessage()
    ↓
WebRPCCore.sendMessage() / LynxRPCCore.sendMessage()
    ↓
fetchWebRPCStream() / fetchLynxRPCStream()
```

### 2. 流式数据接收与处理

```
processWebRPCStream() / processLynxRPCStream()
    ↓
reader.read() → 循环读取数据块
    ↓
WebCodeJsonProcessor.processStreamChunk() → 从数据块中提取内容
    ↓
提取思考内容(reasoning_content)与实际代码内容(content)
    ↓
添加到 contentFragments 数组并定期更新UI
```

### 3. 内容提取处理

```
WebCodeJsonProcessor.processStreamChunk(chunk)
    ↓
1. SSE格式过滤
   - 检查是否以'data:'开头，直接丢弃这些数据包
    ↓
2. 代码片段快速检测
   - 检查是否为明显的代码片段(HTML/CSS/JS)，直接返回内容
    ↓
3. JSON解析和提取
   - 尝试解析为JSON并提取delta.content或delta.reasoning_content
   - 处理Claude各种格式
    ↓
4. 错误恢复机制
   - 对于解析失败的JSON，返回空字符串防止元数据泄露
   - 处理连接的多个JSON对象
```

### 4. 思考内容处理流程

```
提取思考内容(reasoning_content)
    ↓
存储到对应的思考内容存储中(web/lynx)
    ↓
生成完成后，将思考内容格式化为代码注释
    ↓
将注释添加到生成代码的开头
    ↓
最终显示在代码编辑器中
```

### 5. 数据更新流程

```
updateUI() / addContentFragment()
    ↓
state.code = getCurrentContent()
    ↓
onUpdate({ ...state })
    ↓
context.actions.updateCode(code)
```

## 统一JSON处理器架构

系统使用统一的`WebCodeJsonProcessor`类处理所有格式的流式数据：

- **简化解析规则**: 对于流式数据，只需区分是思考内容(reasoning_content)还是实际代码内容(content)
- **统一处理入口**: 所有JSON解析通过`WebCodeJsonProcessor.processStreamChunk`处理
- **防止元数据泄露**: 解析失败时返回空字符串，而非原始JSON
- **性能优化**: 包含快速路径检测明显的代码片段，批量处理多个JSON对象

### JSON元数据过滤机制

为防止JSON元数据泄露到生成的代码中，实现了多层过滤：

1. **SSE格式过滤**: 检测并丢弃以`data:`开头的数据包
2. **代码片段快速路径**: 直接返回明显的代码片段，跳过JSON解析
3. **格式检测**: 识别Claude API特征字段（`"model"`, `"choices"`）
4. **内容提取**: 只提取`delta.content`和`delta.reasoning_content`字段
5. **安全回退**: 解析失败时返回空字符串而非原始JSON

### 支持的Claude API格式

1. **AWS SDK Claude格式**
   ```json
   {
     "id": "2025060521412426CBD6FC6FD1EF990C8A",
     "created": 1749159729,
     "model": "aws_sdk_claude4_sonnet",
     "choices": [{
       "delta": {
         "content": "<代码内容>"
       },
       "index": 0,
       "stop_reason": ""
     }],
     "usage": {}
   }
   ```

2. **思考内容格式**
   ```json
   {
     "id": "20250521191447476A4F3A3F6B41E142D5",
     "created": 1747854889,
     "model": "aws_sdk_claude37_sonnet",
     "choices": [{
       "delta": {
         "reasoning_content": "<思考内容>"
       },
       "index": 0,
       "stop_reason": ""
     }],
     "usage": {}
   }
   ```

3. **标准Claude格式**
   ```json
   {
     "object": "chat.completion.chunk",
     "choices": [{
       "delta": {
         "content": "<代码内容>"
       }
     }]
   }
   ```

4. **SSE格式**
   ```
   data: {"choices":[{"delta":{"content":"代码内容"}}]}
   ```

### WebCodeJsonProcessor 核心功能

`WebCodeJsonProcessor`类是流式数据处理的核心，提供以下关键功能：

1. **processStreamChunk**: 处理流式响应数据块，提取代码内容和思考内容
2. **parseJsonChunk**: 解析JSON格式数据，支持各种Claude API格式
3. **processBatchedJsonStream**: 处理批量JSON流数据，处理连接的多个JSON对象
4. **isCodeContent**: 快速检测明显的代码片段，优化处理流程

主要处理流程：

```typescript
public processStreamChunk(chunk: string): JsonParseResult {
  try {
    // 1. 检查是否为 SSE 格式(data:前缀) - 直接丢弃
    if (chunk.startsWith('data:')) {
      return {
        content: '',
        success: false,
        format: 'sse',
        hasMetadata: false,
      };
    }

    // 2. 检查是否为明显的非 JSON 内容（代码片段等）
    if (this.isCodeContent(chunk)) {
      return {
        content: chunk,
        success: true,
        format: 'plain',
        hasMetadata: false,
      };
    }

    // 3. 尝试解析为 JSON
    try {
      const parsedData = JSON.parse(chunk);

      // 检查是否是 AWS SDK Claude格式
      if (parsedData.choices && Array.isArray(parsedData.choices)) {
        const choice = parsedData.choices[0];
        
        // 提取代码内容
        if (choice.delta && choice.delta.content !== undefined) {
          return {
            content: String(choice.delta.content || ''),
            success: true,
            format: 'claude',
            hasMetadata: true,
          };
        }
        
        // 提取思考内容
        if (choice.delta && choice.delta.reasoning_content !== undefined) {
          return {
            reasoningContent: String(choice.delta.reasoning_content || ''),
            success: true,
            format: 'claude',
            hasMetadata: true,
          };
        }
      }
      
      // 使用通用解析
      return this.parseJsonChunk(chunk);
    } catch (jsonError) {
      // 处理错误...
      
      // 所有解析尝试都失败，返回空 content 避免 JSON 泄露
      return {
        content: '',
        success: false,
        format: 'unknown',
        hasMetadata: false,
      };
    }
  } catch (error) {
    // 异常情况下也要返回空字符串，防止 JSON 元数据泄露
    return {
      content: '',
      success: false,
      format: 'unknown',
      hasMetadata: false,
    };
  }
}
```

## 并行设计原则

WebRPC和LynxRPC遵循以下设计原则：

1. **完全并行** - WebRPC和LynxRPC可以同时被触发和执行，互不干扰
2. **数据隔离** - 两个子系统使用各自的Context管理状态，避免数据混淆
3. **独立流程** - 每个子系统有自己的生命周期和错误处理机制
4. **UI分离** - 界面上通过Tab切换展示不同类型的代码
5. **手动转换** - "转换成Lynx"按钮允许用户手动将Web代码转换为Lynx代码

## 通信流程图

```
┌───────────────┐     ┌───────────────┐
│  WebRPC       │     │  LynxRPC      │
│  Context      │     │  Context      │
└───────┬───────┘     └───────┬───────┘
        │                     │
        ▼                     ▼
┌───────────────┐     ┌───────────────┐
│  WebRPC       │     │  LynxRPC      │
│  Manager      │     │  Manager      │
└───────┬───────┘     └───────┬───────┘
        │                     │
        ▼                     ▼
┌───────────────┐     ┌───────────────┐
│  WebRPCCore   │     │  LynxRPCCore  │
└───────┬───────┘     └───────┬───────┘
        │                     │
        ▼                     ▼
┌───────────────┐     ┌───────────────┐
│ WebRPCService │     │ LynxRPCService│
└───────┬───────┘     └───────┬───────┘
        │                     │
        ▼                     ▼
┌───────────────┐     ┌───────────────┐
│ WebCode       │     │ LynxCode      │
│ Component     │     │ Component     │
└───────────────┘     └───────────────┘
```

## 自动续传机制

自动续传是确保大型代码完整性的关键机制，工作流程如下：

1. **检测续传标记**：系统会检测代码中的`<CONTINUE_TOKEN>`标记
2. **触发续传**：发现标记后自动调用续传API
3. **构建续传提示**：创建包含原始上下文和已生成代码的提示
4. **递归支持**：支持多轮续传，直到代码完整
5. **透明处理**：整个续传过程对用户完全透明

```
WebRPCCore/LynxRPCCore检测续传标记
    ↓
调用handleContinuation方法
    ↓
构建续传提示，包含上下文和已生成代码
    ↓
发起新的续传API请求
    ↓
解析并合并结果到当前代码
```

## 思考内容处理

系统能够从Claude API响应中提取AI的思考过程并按照以下方式处理：

1. **检测思考内容**：在流式数据中识别`reasoning_content`字段
2. **独立存储**：将思考内容存储在单独的Web/Lynx思考内容仓库中
3. **注释转换**：在代码生成完成后，将思考内容格式化为代码注释
4. **添加到代码**：将注释添加到生成的代码开头，提供算法思路和实现逻辑解释

思考内容提取流程：
```
WebCodeJsonProcessor.processStreamChunk()
    ↓
检测choices[0].delta.reasoning_content
    ↓
提取思考内容并返回reasoningContent字段
    ↓
存储到webReasoningStore/lynxReasoningStore
    ↓
生成完成后格式化为注释并添加到代码
```

## 数据保护机制

### 1. JSON元数据保护

在`WebCodeJsonProcessor`中实现了严格的JSON元数据保护机制:

```typescript
// SSE格式过滤
if (chunk.startsWith('data:')) {
  logger.debug(
    `🚫 [Web数据过滤] 检测到data:开头的数据包，直接丢弃: ${chunk.substring(0, 100)}...`,
  );
  return {
    content: '',
    success: false,
    format: 'sse',
    hasMetadata: false,
    originalLength: chunk.length,
    extractedLength: 0,
    error: 'data:开头的数据包已被丢弃（按设计要求）',
  };
}

// 检查是否为明显的非 JSON 内容（代码片段等）
if (this.isCodeContent(chunk)) {
  logger.debug('检测到代码/HTML/CSS 内容，直接返回');
  return {
    content: chunk,
    success: true,
    format: 'plain',
    hasMetadata: false,
    originalLength: chunk.length,
    extractedLength: chunk.length,
  };
}

// 所有解析尝试都失败，返回空 content 避免 JSON 泄露
logger.warn('所有解析尝试失败，返回空字符串防止 JSON 元数据泄露', {
  chunkLength: chunk.length,
  chunkPreview: chunk.substring(0, 100),
  errorType: (jsonError as any)?.constructor?.name || 'Unknown',
  errorMessage:
    jsonError instanceof Error ? jsonError.message : String(jsonError),
});
return {
  content: '',
  success: false,
  format: 'unknown',
  hasMetadata: false,
  originalLength: chunk.length,
  extractedLength: 0,
  error:
    jsonError instanceof Error ? jsonError.message : String(jsonError),
};
```

### 2. Web代码保护

在`WebRPCContext`中实现了多层保护机制:

```typescript
// 批量更新优化 - 减少频繁的小更新
const shouldEnableBatchMode = updateSize < 10 && !isInitialUpdate;

// 检查新代码是否与当前代码相同，避免不必要的更新
if (newCode === lastCodeRef.current) {
  return;
}

// 防止代码被意外清空
if (!newCode && state.webCode) {
  logger.warn('🛡️ [STATE_PROTECTION][Web] 阻止代码被清空');
  return;
}

// 防止代码大幅缩短（可能的数据丢失）
if (state.webCode && newCode.length < state.webCode.length * 0.5) {
  logger.warn('🛡️ [STATE_PROTECTION][Web] 检测到代码大幅缩短');
  if (process.env.NODE_ENV === 'development') {
    return; // 开发环境阻止
  }
}
```

### 3. 批处理优化

为减少频繁更新导致的性能问题，实现了批处理机制:

```typescript
// 累积内容片段
contentFragments.push(extractedContent);
updateCount++;

// 批处理更新策略：累积一定数量或等待一定时间再更新UI
const shouldUpdate = 
  updateCount >= MAX_UPDATES_BEFORE_FORCE || 
  Date.now() - lastUpdateTime > UPDATE_INTERVAL_MS;

if (shouldUpdate) {
  const accumulatedContent = contentFragments.join('');
  onUpdate(accumulatedContent);
  lastUpdateTime = Date.now();
  updateCount = 0;
}
```

## Prompt组合器架构

系统现在使用新的`PromptComposer`组件来动态生成和组合提示词：

```
PromptComposer/
├── generateWebSystemPrompt()     # 生成Web系统提示词
├── generateLynxSystemPrompt()    # 生成Lynx系统提示词
├── generateAnimationEnhancedPrompt() # 生成动画增强提示词
├── generateTextLayoutOptimizedPrompt() # 生成文字布局优化提示词
├── generateWebContinuationPrompt() # 生成Web续传提示词
├── generateLynxContinuationPrompt() # 生成Lynx续传提示词
└── generateLynxConversionPrompt() # 生成Lynx转换提示词
```

### Prompt生成策略

`PromptComposer`采用基于组件的提示词组合逻辑：

1. **基础系统角色定义** - 定义AI的基本角色和专业领域
2. **Canvas策略指导** - 根据选项动态切换Canvas优先策略
3. **渲染核心逻辑** - 添加Canvas渲染相关的技术指导
4. **平台优化指导** - 针对Web或Lynx平台的特定优化
5. **文本布局优化** - 避免文字重叠和排版问题
6. **输出格式规范** - 确保生成内容符合解析要求
7. **错误预防指导** - 添加防止常见错误的检查指导

这种组合策略允许我们根据不同场景和需求动态调整提示词，同时保持整体一致性。

### 续传提示词机制

续传提示词使用专门的模板设计，确保内容连贯性：

1. **上下文恢复** - 提供之前生成的完整代码作为上下文
2. **原始需求提醒** - 包含原始用户提示，确保一致性
3. **格式要求强化** - 强调输出格式规范，特别是对Lynx代码
4. **连贯性指导** - 明确指示AI继续之前的生成，而非重新开始
5. **标记指导** - 指导AI在内容不完整时添加续传标记
