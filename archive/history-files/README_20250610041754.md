# AI流式数据处理架构分析与实现

## 核心架构概述

### 完整数据流向图

#### Web代码流向：
```
API Response → WebRPCCore.processWebRPCStream()
→ WebCodeJsonProcessor.processStreamChunk()
→ CodeGenerationUnifiedContextV2.dispatch(WEB_UPDATE_CODE)
→ WebCodeHighlight组件
→ SemiCodeHighlight组件
```

#### Lynx代码流向：
```
API Response → LynxRPCCore.processLynxRPCStream()
→ WebCodeJsonProcessor.processStreamChunk()
→ CodeGenerationUnifiedContextV2.dispatch(LYNX_UPDATE_CODE)
→ LynxCodeHighlight组件
→ SemiCodeHighlight组件
```

## 核心模块与职责

### 1. 数据流处理层

```text
RPC/
├── Web/                 # Web代码生成相关模块
│   ├── WebRPCManager.ts # Web代码生成管理器
│   └── WebRPCCore.ts    # Web代码生成核心处理
├── Lynx/                # Lynx代码生成相关模块
│   ├── LynxRPCManager.ts # Lynx代码生成管理器
│   └── LynxRPCCore.ts    # Lynx代码生成核心处理
├── PromptComposer.ts    # 提示词组合器
├── PromptVariables.ts   # 提示词变量和模板
├── 各种PE文件            # 提示词工程化文件集合
└── prompts.ts           # 提示词模板
```

### 2. 状态管理层

```text
contexts/
├── CodeGenerationUnifiedContextV2.tsx   # 统一状态管理Context
├── LynxViewModeContext.tsx              # Lynx视图模式管理
├── ToastContext.tsx                     # 通知提示管理
└── 其他辅助Context文件                   # 辅助功能的状态管理
```

### 3. 数据处理层

```text
utils/
├── webCodeJsonProcessor.ts   # Web代码JSON处理器(核心)
├── claudeStreamParser.ts     # Claude流解析器
├── contentProcessors.ts      # 内容处理器集合
├── jsonProcessor.ts          # JSON数据处理
├── rpcSharedUtils.ts         # RPC共享工具函数
├── storageUtils.ts           # 存储工具函数
└── regexPatterns.ts          # 正则表达式模式
```

## 数据处理全流程

### 1. 请求发起流程

```
用户输入 → ChatComponent
    ↓
使用统一API触发
useWebRPCUnified().sendMessage() / useLynxRPCUnified().convertFromWeb()
    ↓
WebRPCCore.sendMessage() / LynxRPCCore.sendMessage()
    ↓
API请求发送 → API_ENDPOINT
```

### 2. 流式数据接收与处理

```
processWebRPCStream() / processLynxRPCStream()
    ↓
reader.read() → 循环读取数据块
    ↓
WebCodeJsonProcessor.processStreamChunk() → 从数据块中提取内容
    ↓
提取思考内容(reasoning_content)与实际代码内容(content)
    ↓
添加到 contentFragments 数组并通过批量更新机制更新UI
```

### 3. 内容提取处理

```
WebCodeJsonProcessor.processStreamChunk(chunk)
    ↓
1. SSE格式过滤
   - 检查是否以'data:'开头，直接丢弃这些数据包
    ↓
2. 代码片段快速检测
   - 检查是否为明显的代码片段(HTML/CSS/JS)，直接返回内容
    ↓
3. JSON解析和提取
   - 尝试解析为JSON并提取delta.content或delta.reasoning_content
   - 处理Claude各种格式
    ↓
4. 错误恢复机制
   - 对于解析失败的JSON，返回空字符串防止元数据泄露
   - 处理连接的多个JSON对象，批量更新减少渲染
```

### 4. 思考内容处理流程

```
提取思考内容(reasoning_content)
    ↓
存储到对应的思考内容存储中(web/lynx)
    ↓
生成完成后，将思考内容格式化为代码注释
    ↓
将注释添加到生成代码的开头
    ↓
最终显示在代码编辑器中
```

### 5. 数据更新流程

```
批量更新机制
    ↓
累积内容片段直到达到更新阈值
    ↓
CodeGenerationUnifiedContextV2.batchUpdate([
    { type: 'WEB_UPDATE_CODE', code: accumulatedContent },
    { type: 'WEB_SET_PROGRESS', progress: newProgress }
])
    ↓
更新状态版本号state.version++
    ↓
通知订阅组件更新
```

## 统一状态管理架构

系统使用`CodeGenerationUnifiedContextV2`统一管理所有状态，实现以下关键特性：

- **状态隔离但统一管理**: Web和Lynx状态保持逻辑隔离，但通过统一Context管理，减少代码重复
- **批量更新优化**: 通过`batchUpdate`机制累积多个状态更新，一次性提交，减少渲染次数
- **版本控制**: 内置版本号管理，确保状态更新的一致性
- **选择器模式**: 使用`useSelector`进行细粒度的状态订阅，组件只订阅真正需要的状态部分
- **生命周期管理**: 完善的回调注册机制替代了自定义事件，更可靠的组件通信

### 统一状态结构

```typescript
interface UnifiedCodeGenerationState {
  web: WebCodeState;       // Web代码相关状态
  lynx: LynxCodeState;     // Lynx代码相关状态
  ui: UIState;             // UI相关状态
  version: number;         // 状态版本号，用于批量更新
  callbacks: EventCallbacks; // 回调函数注册
}
```

### 批量更新机制

```typescript
// 批处理更新示例
batchUpdate([
  { type: 'WEB_UPDATE_CODE', code: newCode },
  { type: 'WEB_SET_PROGRESS', progress: 0.75 },
  { type: 'SET_ACTIVE_TAB', tab: 'web' }
]);

// 内部实现
function batchUpdate(updates: Array<UnifiedAction>): void {
  unstable_batchedUpdates(() => {
    dispatch({ type: 'BATCH_UPDATE', updates });
  });
}
```

### 选择器模式

```typescript
// 组件中使用选择器订阅特定状态
const webCode = useSelector(state => state.web.webCode);
const isLoading = useSelector(state => state.web.isWebRPCLoading);
```

## JSON元数据处理

为防止JSON元数据泄露到生成的代码中，实现了多层过滤：

1. **SSE格式过滤**: 检测并丢弃以`data:`开头的数据包
2. **代码片段快速路径**: 直接返回明显的代码片段，跳过JSON解析
3. **格式检测**: 识别Claude API特征字段（`"model"`, `"choices"`）
4. **内容提取**: 只提取`delta.content`和`delta.reasoning_content`字段
5. **批量处理**: 累积小增量更新后批量处理，减少重渲染
6. **安全回退**: 解析失败时返回空字符串而非原始JSON

### 支持的Claude API格式

1. **AWS SDK Claude格式**
   ```json
   {
     "id": "2025060521412426CBD6FC6FD1EF990C8A",
     "model": "aws_sdk_claude4_sonnet",
     "choices": [{
       "delta": {
         "content": "<代码内容>"
       },
       "index": 0,
       "stop_reason": ""
     }]
   }
   ```

2. **思考内容格式**
   ```json
   {
     "id": "20250521191447476A4F3A3F6B41E142D5",
     "model": "aws_sdk_claude37_sonnet",
     "choices": [{
       "delta": {
         "reasoning_content": "<思考内容>"
       },
       "index": 0,
       "stop_reason": ""
     }]
   }
   ```

3. **标准Claude格式**
   ```json
   {
     "object": "chat.completion.chunk",
     "choices": [{
       "delta": {
         "content": "<代码内容>"
       }
     }]
   }
   ```

4. **SSE格式** (过滤掉)
   ```
   data: {"choices":[{"delta":{"content":"代码内容"}}]}
   ```

## PromptComposer智能提示系统

新的`PromptComposer`提供动态生成和组合提示词的能力，是提高代码生成质量的关键：

### 组件化提示词架构

```
PromptComposer/
├── 基础系统角色定义 - 定义AI的基本角色和专业领域
├── Canvas策略指导 - 根据选项动态切换Canvas优先策略
├── 平台优化指导 - 针对Web或Lynx平台的特定优化
├── 文本布局优化 - 避免文字重叠和排版问题
├── 输出格式规范 - 确保生成内容符合解析要求
└── 错误预防指导 - 添加防止常见错误的检查指导
```

### 用户意图分析

`analyzeUserIntent`函数分析用户输入，确定最佳生成策略：

```typescript
function analyzeUserIntent(userInput: string): {
  type: 'educational' | 'query' | 'showcase' | 'interactive' | 'data' | 'general';
  shouldUseCanvas: boolean;
  visualComplexity: 'simple' | 'medium' | 'complex';
  designFocus: string[];
}
```

意图分析使得系统能够针对不同类型的需求优化生成策略：
- 教育类内容: 强调清晰的可视化和交互式学习体验
- 查询类内容: 注重数据可视化和信息架构
- 展示类内容: 优先考虑视觉美学和动画效果
- 交互类内容: 关注用户交互和反馈机制

### 智能Canvas触发

根据用户输入和意图分析，系统可以智能决定是否启用Canvas优先策略：

```typescript
const intent = analyzeUserIntent(userInput);
const useCanvasGeneration = intent.shouldUseCanvas || 
                           intent.type === 'educational' ||
                           intent.visualComplexity === 'complex';
```

教育类和复杂可视化需求会自动启用Canvas优先策略，确保最佳视觉表现。

## 自动续传机制

自动续传是确保大型代码完整性的关键机制，通过以下流程工作：

1. **检测续传标记**: WebRPCCore通过`needsContinuation`方法检测代码中的`<CONTINUE_TOKEN>`标记
2. **触发续传**: 发现标记后自动调用`handleContinuation`方法
3. **构建续传提示**: 使用`PromptComposer.generateWebContinuationPrompt`创建包含上下文和已生成代码的提示
4. **递归支持**: 续传后的结果可能仍需继续，支持递归多轮续传
5. **合并结果**: 将续传生成的代码合并到原始代码中，确保内容连贯性

续传提示词特别设计来确保内容连贯性：
- 包含原始请求的完整上下文
- 提供之前生成的代码作为参考
- 明确指示AI继续之前的生成，而非重新开始
- 强调输出格式规范和代码完整性要求

## 存储策略优化

### 严格写入原则

系统遵循严格的存储策略，只在特定情况下写入localStorage：

```typescript
/**
 * ⚠️ 存储核心原则：严格遵循"只在传输完成时写入"
 *
 * 重要说明：
 * 1. 严禁在流式传输过程中写入localStorage
 * 2. 严禁使用防抖或定时器机制定期保存代码
 * 3. 只能在传输完全结束或用户手动保存时写入
 */
```

### 写入时机控制

```typescript
// 只在以下情况保存:
// 1. 代码传输完全结束(isComplete=true)
if (isComplete && code.length > 0) {
  saveCodeToStorage(code);
}

// 2. 用户手动编辑保存
const handleSaveEdit = () => {
  if (editableCode.length > 0) {
    saveCodeToStorage(editableCode);
    setEditMode(false);
  }
};

// 3. 组件卸载且代码已完成
useEffect(() => {
  return () => {
    if (isComplete && webCode.length > 0) {
      saveCodeToStorage(webCode);
    }
  };
}, [isComplete, webCode]);
```

### 代码清理与保护

保存前清理代码，确保只保存HTML标签内的内容：

```typescript
const cleanedCode = cleanWebCodeForDisplay(code);
```

## 数据保护机制

### 1. 代码状态保护

在统一Context中实现了多层保护机制:

```typescript
// 🛡️ 防止代码被意外清空
if (!newCode && state.web.webCode) {
  logger.warn('[STATE_PROTECTION][Web] 阻止代码被清空');
  return;
}

// 🛡️ 防止代码大幅缩短（可能的数据丢失）
if (state.web.webCode && newCode.length < state.web.webCode.length * 0.5) {
  logger.warn('[STATE_PROTECTION][Web] 检测到代码大幅缩短');
  if (process.env.NODE_ENV === 'development') {
    return; // 开发环境阻止
  }
}
```

### 2. 批处理优化

为减少频繁更新导致的性能问题，实现了批处理机制:

```typescript
// 累积内容片段
contentFragments.push(extractedContent);
updateCount++;

// 批处理更新策略：累积一定数量或等待一定时间再更新UI
const shouldUpdate = 
  updateCount >= MAX_UPDATES_BEFORE_FORCE || 
  Date.now() - lastUpdateTime > UPDATE_INTERVAL_MS;

if (shouldUpdate) {
  const accumulatedContent = contentFragments.join('');
  // 使用批量更新减少重渲染
  batchUpdate([
    { type: 'WEB_UPDATE_CODE', code: accumulatedContent },
    { type: 'WEB_SET_PROGRESS', progress: calculateProgress() }
  ]);
  lastUpdateTime = Date.now();
  updateCount = 0;
}
```

## 主要性能优化

1. **状态粒度优化**:
   - 使用`useSelector`进行细粒度的状态订阅
   - 组件只订阅真正需要的状态部分
   - 避免不相关状态变化引起的重渲染

2. **批量更新机制**:
   - 累积小增量更新后批量处理
   - 使用`unstable_batchedUpdates`批量处理React状态更新
   - 状态版本号确保一致性

3. **数据流优化**:
   - 小更新使用批量模式减少渲染
   - 优化JSON解析路径，快速识别代码片段
   - 防止无用数据触发状态更新

4. **渲染优化**:
   - 组件级缓存避免不必要的重新渲染
   - 编辑器惰性加载减少初始加载时间
   - 预览组件按需渲染
