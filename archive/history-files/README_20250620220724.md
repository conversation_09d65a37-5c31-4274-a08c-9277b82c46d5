# Batch Processor 归档文件

本目录包含从 Batch Processor 主代码库归档的文件，这些文件在当前架构中未被使用，但保留以供参考。

## 归档目录结构

- **components/** - 未使用的UI组件
- **services/** - 未使用或已被替代的服务
- **hooks/** - 未使用的React Hooks
- **compatibility/** - 兼容性相关组件和工具

## 归档时间

- 初始归档: 2025-06-19
- 最新归档: 2025-06-20

## 📅 最新归档文件 (2025-06-20)

### 🧪 测试和演示文件
- `tests/DrawerThemeTest.tsx` - 抽屉主题测试组件
- `tests/PromptDrawerTest.tsx` - 提示词抽屉测试组件
- `test-themes/page.tsx` - 主题测试页面
- `demos/IndexedDBDemo.tsx` - IndexedDB演示组件
- `demo/enhanced-settings-demo.html` - 增强设置演示页面

### 🔧 调试和开发工具
- `debug/scroll-test.js` - 滚动测试脚本
- `debug/test-history.js` - 历史记录测试脚本
- `utils/debug-logger.ts` - 调试日志工具
- `utils/test-pe-prompt.ts` - PE提示词测试工具
- `utils/test-query-parser.ts` - 查询解析器测试工具
- `utils/archiveUnusedFiles.js` - 文件归档脚本
- `run-archive.sh` - 归档执行脚本

### 🎨 未使用的UI组件
- `components/EnhancedMonitoringPanel.tsx` - 增强监控面板（未被引用）
- `components/StorageStatusPanel.tsx` - 存储状态面板（未被引用）

### 🚀 功能特性组件
- `features/TourGuide.tsx` - 用户导览组件

### 📚 文档和总结文件
- `docs/DRAWER_ANIMATION_FIX.md` - 抽屉动画修复文档
- `docs/DRAWER_PADDING_FIX_SUMMARY.md` - 抽屉内边距修复总结
- `docs/DRAWER_REFACTOR_SUMMARY.md` - 抽屉重构总结
- `docs/DRAWER_THEME_FIX_SUMMARY.md` - 抽屉主题修复总结
- `docs/PROGRESS_BAR_IMPLEMENTATION_GUIDE.md` - 进度条实现指南
- `docs/PROGRESS_BAR_REFACTOR.md` - 进度条重构文档
- `docs/REFACTOR_PROGRESS.md` - 重构进度文档

### 🎨 样式文档
- `docs/styles/14INCH_OPTIMIZATION_SUMMARY.md` - 14寸屏幕优化总结
- `docs/styles/BUTTON_LAYOUT_IMPROVEMENTS.md` - 按钮布局改进
- `docs/styles/FINAL_FIX_SUMMARY.md` - 最终修复总结
- `docs/styles/FINAL_VERIFICATION_CHECKLIST.md` - 最终验证清单
- `docs/styles/HOTFIX_SUMMARY.md` - 热修复总结
- `docs/styles/LAYOUT_FIX_README.md` - 布局修复说明
- `docs/styles/README_NEW_STRUCTURE.md` - 新结构说明
- `docs/styles/REFACTOR_SUMMARY.md` - 重构总结
- `docs/styles/RESTRUCTURE_SUMMARY.md` - 重构总结
- `docs/styles/SCROLL_FIX_SUMMARY.md` - 滚动修复总结
- `docs/styles/style.md` - 样式文档

## 📋 历史归档文件 (2025-06-19)

### 组件 (Components)
- `components/ProgressDisplay-backup.tsx` - 进度显示组件备份
- `components/EnhancedControlPanel.tsx` - 增强控制面板

### 服务 (Services)
- `services/StreamProcessorService.ts` - 流处理服务

### Hooks
- `hooks/useTourGuide.ts` - 导览Hook

### 兼容性组件
- `compatibility/StyleCompatLayer.tsx` - 样式兼容层

## 归档说明

这些文件已从主代码流程中移除，但保留供将来参考或可能重用。每个文件顶部都添加了归档说明注释，包括原始位置和归档原因。

如需恢复某个文件到主代码库，请确认其与当前架构的兼容性，并更新相关引用。
