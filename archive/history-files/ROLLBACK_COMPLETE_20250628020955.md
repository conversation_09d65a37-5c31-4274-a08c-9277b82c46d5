<iframe srcdoc="<!DOCTYPE html>
<html lang=&quot;zh-CN&quot;>
<head>
  <meta charset=&quot;UTF-8&quot;>
  <meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;>
  <title>TTML预览 - 简化模式</title>
  <style>
    /* 基础样式重置 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
      background: #f8fafc;
      overflow-x: hidden;
      font-size: 14px;
      line-height: 1.6;
    }

    /* 作用域化CSS */
    .container[data-v-4wgeo] {
  max-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 0 5.333333vw 0;
}
.header-section[data-v-4wgeo] {
  padding: 8.000000vw 5.333333vw 5.333333vw 5.333333vw;
  text-align: center;
}
.title-container[data-v-4wgeo] {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 2.666667vw;
}
.icon-globe[data-v-4wgeo] {
  color: #ffffff;
  margin-right: 2.666667vw;
  opacity: 0.9;
}
.main-title[data-v-4wgeo] {
  font-size: 6.400000vw;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 0.266667vw 1.066667vw rgba(0,0,0,0.2);
}
.subtitle[data-v-4wgeo] {
  font-size: 3.733333vw;
  color: rgba(255,255,255,0.8);
  margin-top: 1.600000vw;
}
.stats-overview[data-v-4wgeo] {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  margin: 0 5.333333vw 5.333333vw 5.333333vw;
}
.stat-card[data-v-4wgeo] {
  background: rgba(255,255,255,0.15);
  border-radius: 3.200000vw;
  padding: 4.266667vw 5.333333vw;
  text-align: center;
  border: 0.133333vw solid rgba(255,255,255,0.2);
  min-width: 37.333333vw;
}
.stat-number[data-v-4wgeo] {
  font-size: 5.866667vw;
  font-weight: 800;
  color: #ffffff;
  display: block;
  margin-bottom: 1.066667vw;
}
.stat-label[data-v-4wgeo] {
  font-size: 3.200000vw;
  color: rgba(255,255,255,0.8);
}
.ranking-section[data-v-4wgeo] {
  margin: 0 5.333333vw;
}
.section-header[data-v-4wgeo] {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 4.266667vw;
  padding: 3.200000vw 4.266667vw;
  background: rgba(255,255,255,0.1);
  border-radius: 2.666667vw;
  border: 0.133333vw solid rgba(255,255,255,0.15);
}
.icon-ranking[data-v-4wgeo], .icon-insight[data-v-4wgeo] {
  color: #ffffff;
  margin-right: 2.133333vw;
  opacity: 0.9;
}
.section-title[data-v-4wgeo] {
  font-size: 4.266667vw;
  font-weight: 600;
  color: #ffffff;
}
.country-item[data-v-4wgeo] {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  background: rgba(255,255,255,0.95);
  border-radius: 3.200000vw;
  margin-bottom: 3.200000vw;
  padding: 4.266667vw;
  border: 0.133333vw solid rgba(255,255,255,0.8);
}
.rank-badge[data-v-4wgeo] {
  width: 10.666667vw;
  height: 10.666667vw;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4.266667vw;
  flex-shrink: 0;
}
.rank-top[data-v-4wgeo] {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}
.rank-normal[data-v-4wgeo] {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
}
.rank-number[data-v-4wgeo] {
  font-size: 4.266667vw;
  font-weight: 800;
  color: #333333;
}
.country-info[data-v-4wgeo] {
  flex: 1;
}
.country-header[data-v-4wgeo] {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 2.666667vw;
}
.country-flag[data-v-4wgeo] {
  font-size: 5.333333vw;
  margin-right: 2.133333vw;
}
.country-name[data-v-4wgeo] {
  font-size: 4.800000vw;
  font-weight: 700;
  color: #1a1a1a;
}
.population-display[data-v-4wgeo] {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  margin-bottom: 3.200000vw;
}
.population-number[data-v-4wgeo] {
  font-size: 6.400000vw;
  font-weight: 800;
  color: #2196f3;
  margin-right: 1.066667vw;
}
.population-unit[data-v-4wgeo] {
  font-size: 3.200000vw;
  color: #666666;
  font-weight: 500;
}
.progress-container[data-v-4wgeo] {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 3.200000vw;
}
.progress-bar[data-v-4wgeo] {
  flex: 1;
  height: 1.600000vw;
  background: #e3f2fd;
  border-radius: 0.800000vw;
  overflow: hidden;
  margin-right: 2.133333vw;
}
.progress-fill[data-v-4wgeo] {
  height: 100%;
  background: linear-gradient(90deg, #2196f3, #21cbf3);
  border-radius: 0.800000vw;
  transition: width 0.8s ease;
}
.percentage-text[data-v-4wgeo] {
  font-size: 3.200000vw;
  color: #2196f3;
  font-weight: 600;
  min-width: 10.666667vw;
}
.country-details[data-v-4wgeo] {
  display: flex;
  flex-direction: column;
  gap: 1.600000vw;
}
.detail-item[data-v-4wgeo] {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.detail-icon[data-v-4wgeo] {
  font-size: 3.200000vw;
  margin-right: 1.600000vw;
  width: 4.266667vw;
}
.detail-text[data-v-4wgeo] {
  font-size: 3.200000vw;
  color: #666666;
  line-height: 1.4;
}
.insights-section[data-v-4wgeo] {
  margin: 8.000000vw 5.333333vw 5.333333vw 5.333333vw;
}
.insight-cards[data-v-4wgeo] {
  display: flex;
  flex-direction: column;
  gap: 2.666667vw;
}
.insight-card[data-v-4wgeo] {
  background: rgba(255,255,255,0.15);
  border-radius: 2.666667vw;
  padding: 4.266667vw;
  border: 0.133333vw solid rgba(255,255,255,0.2);
}
.insight-title[data-v-4wgeo] {
  font-size: 3.733333vw;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1.600000vw;
  display: block;
}
.insight-content[data-v-4wgeo] {
  font-size: 3.466667vw;
  color: rgba(255,255,255,0.85);
  line-height: 1.5;
}
.footer-info[data-v-4wgeo] {
  text-align: center;
  padding: 5.333333vw;
}
.data-source[data-v-4wgeo] {
  font-size: 3.200000vw;
  color: rgba(255,255,255,0.7);
  margin-bottom: 1.066667vw;
  display: block;
}
.update-time[data-v-4wgeo] {
  font-size: 2.933333vw;
  color: rgba(255,255,255,0.6);
}
  </style>
</head>
<body>
  <div id=&quot;lynx-app-container&quot; data-component-id=&quot;cjnxcjwc&quot;>
    <scroll-view scroll-y=&quot;{{true}}&quot; class=&quot;container&quot;><div class=&quot;header-section&quot;><div class=&quot;title-container&quot;><span class=&quot;icon-globe&quot;><svg viewBox=&quot;0 0 24 24&quot; width=&quot;28&quot; height=&quot;28&quot;><path d=&quot;M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z&quot; fill=&quot;currentColor&quot;/></svg></span><span class=&quot;main-title&quot;>世界人口最多的十个国家</span></div><span class=&quot;subtitle&quot;>全球人口分布概览 · 2024年数据</span></div><div class=&quot;stats-overview&quot;><div class=&quot;stat-card&quot;><span class=&quot;stat-number&quot;>80.5亿</span><span class=&quot;stat-label&quot;>全球总人口</span></div><div class=&quot;stat-card&quot;><span class=&quot;stat-number&quot;>59.7%</span><span class=&quot;stat-label&quot;>前十国家占比</span></div></div><div class=&quot;ranking-section&quot;><div class=&quot;section-header&quot;><span class=&quot;icon-ranking&quot;><svg viewBox=&quot;0 0 24 24&quot; width=&quot;20&quot; height=&quot;20&quot;><path d=&quot;M7 14l5-5 5 5z&quot; fill=&quot;currentColor&quot;/><path d=&quot;M3 17h4v-4H3v4zm6-8h4v8H9V9zm6-4h4v12h-4V5z&quot; fill=&quot;currentColor&quot;/></svg></span><span class=&quot;section-title&quot;>人口排名</span></div><div class=&quot;country-item&quot;><div class=&quot;rank-badge rank-normal&quot;><span class=&quot;rank-number&quot;>{{item.rank}}</span></div><div class=&quot;country-info&quot;><div class=&quot;country-header&quot;><span class=&quot;country-flag&quot;>{{item.flag}}</span><span class=&quot;country-name&quot;>{{item.name}}</span></div><div class=&quot;population-display&quot;><span class=&quot;population-number&quot;>{{item.population}}</span><span class=&quot;population-unit&quot;>亿人</span></div><div class=&quot;progress-container&quot;><div class=&quot;progress-bar&quot;><div class=&quot;progress-fill&quot; style=&quot;width: {{item.percentage}}%&quot;></div></div><span class=&quot;percentage-text&quot;>{{item.percentage}}%</span></div><div class=&quot;country-details&quot;><div class=&quot;detail-item&quot;><span class=&quot;detail-icon&quot;>🏛️</span><span class=&quot;detail-text&quot;>首都：{{item.capital}}</span></div><div class=&quot;detail-item&quot;><span class=&quot;detail-icon&quot;>🌍</span><span class=&quot;detail-text&quot;>大洲：{{item.continent}}</span></div><div class=&quot;detail-item&quot;><span class=&quot;detail-icon&quot;>📊</span><span class=&quot;detail-text&quot;>密度：{{item.density}} 人/km²</span></div></div></div></div></div><div class=&quot;insights-section&quot;><div class=&quot;section-header&quot;><span class=&quot;icon-insight&quot;><svg viewBox=&quot;0 0 24 24&quot; width=&quot;20&quot; height=&quot;20&quot;><path d=&quot;M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z&quot; fill=&quot;currentColor&quot;/></svg></span><span class=&quot;section-title&quot;>关键洞察</span></div><div class=&quot;insight-cards&quot;><div class=&quot;insight-card&quot;><span class=&quot;insight-title&quot;>亚洲主导</span><span class=&quot;insight-content&quot;>前十名中有六个亚洲国家，体现了亚洲人口的集中分布</span></div><div class=&quot;insight-card&quot;><span class=&quot;insight-title&quot;>双巨头</span><span class=&quot;insight-content&quot;>中国和印度人口均超过14亿，远超其他国家</span></div><div class=&quot;insight-card&quot;><span class=&quot;insight-title&quot;>增长趋势</span><span class=&quot;insight-content&quot;>印度人口增长率高于中国，预计将成为第一人口大国</span></div></div></div><div class=&quot;footer-info&quot;><span class=&quot;data-source&quot;>数据来源：联合国人口司 · 2024年统计</span><span class=&quot;update-time&quot;>更新时间：2024年12月</span></div></scroll-view>
  </div>
</body>
</html>" sandbox="allow-scripts allow-same-origin allow-forms allow-pointer-lock allow-popups allow-modals allow-top-navigation-by-user-activation" title="世界人口最多的十个国家 预览" style="width: 100%; height: 100%; border: none; pointer-events: auto; display: block; background: white; touch-action: pan-y pinch-zoom;"></iframe>