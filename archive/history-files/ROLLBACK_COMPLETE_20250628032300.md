✅ [HTMLGenerator] HTML文档构建完成，总长度: 20367
index.ts:537 ✅ [Parse5TransformEngine] HTML生成完成，耗时: 0.10ms
index.ts:541 📊 [Parse5TransformEngine] HTML生成结果: ObjecthasComponent: truehasDoctype: truehasReact: truehtmlLength: 20367[[Prototype]]: Object
index.ts:567 
✅ [Parse5TransformEngine] ==> 转换引擎完成总结 <==
index.ts:568 ⏰ [Parse5TransformEngine] 总转换耗时: 3.20ms
index.ts:572 📊 [Parse5TransformEngine] 最终转换统计: Object
index.ts:1077 🔍 [Parse5TransformEngine] 转换结果验证: Object
index.ts:1098 ✅ [Parse5TransformEngine] 转换结果验证通过 (质量: high, 完整性: 100.0%)
index.ts:188 📋 [Parse5TransformEngine] 将结果存入缓存
InteractiveIframe.tsx:924 ✅ [InteractiveIframe] Parse5转换成功
InteractiveIframe.tsx:995 ✅ [InteractiveIframe] 转换成功
about:srcdoc:1 An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.
InteractiveIframe.tsx:1010 🎯 [InteractiveIframe] 内容已稳定更新
about:srcdoc:1 An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.
about:srcdoc:591 Uncaught SyntaxError: Unexpected token ':'
useBatchProcessor.ts:177 [useBatchProcessor] 批处理任务已完成，更新状态
LocalStorageService.ts:88 [LocalStorageService] 保存历史记录 (1项) - LRU策略，最大50条
LocalStorageService.ts:133 [LocalStorageService] 加载历史记录
LocalStorageService.ts:118 [LocalStorageService] LRU处理完成：保留50条记录，丢弃1条旧记录
10cssExtractHmr.js:214 [HMR] Reload all CSS