# 发朋友圈的文案 - Lynx代码示例 (已优化)

## 使用最新的lynx direct PE生成

```
发朋友圈的文案
```

<FILES>
<FILE path="/src/index.ttml">
<view class="container">
  <text class="title">发朋友圈的文案</text>
  <view class="content">
    <text>使用最新的lynx direct PE生成的内容</text>
    <view class="demo-box">
      <text class="demo-text">这是关于"发朋友圈的文案"的示例页面</text>
    </view>
  </view>
</view>
</FILE>

<FILE path="/src/index.ttss">
.container {
  display: flex;
  flexDirection: column;
  alignItems: center;
  justifyContent: center;
  padding: 20dp;
}

.title {
  fontSize: 18dp;
  fontWeight: bold;
  marginBottom: 16dp;
  color: #333;
}

.content {
  width: 100%;
  padding: 16dp;
  backgroundColor: #f8f8f8;
  borderRadius: 8dp;
}

.demo-box {
  marginTop: 16dp;
  padding: 12dp;
  borderRadius: 6dp;
  backgroundColor: #e6e6e6;
}

.demo-text {
  fontSize: 15dp;
  color: #666;
}
</FILE>

<FILE path="/src/index.js">
Card({
  data: {
    title: "发朋友圈的文案"
  },
  
  onLoad() {
    console.log("页面加载完成");
  }
});
</FILE>

<FILE path="/src/index.json">
{
  "component": false,
  "usingComponents": {}
}
</FILE>
</FILES>
