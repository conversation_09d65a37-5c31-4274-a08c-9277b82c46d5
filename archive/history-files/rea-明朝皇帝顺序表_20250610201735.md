# 明朝皇帝顺序表 - Lynx代码示例 (已优化)

## 使用最新的lynx direct PE生成

```
明朝皇帝顺序表
```

<FILES>
<FILE path="/src/index.ttml">
<view class="container">
  <text class="title">明朝皇帝顺序表</text>
  <view class="content">
    <text>使用最新的lynx direct PE生成的内容</text>
    <view class="demo-box">
      <text class="demo-text">这是关于"明朝皇帝顺序表"的示例页面</text>
    </view>
  </view>
</view>
</FILE>

<FILE path="/src/index.ttss">
.container {
  display: flex;
  flexDirection: column;
  alignItems: center;
  justifyContent: center;
  padding: 20px;
}

.title {
  fontSize: 18px;
  fontWeight: bold;
  marginBottom: 16px;
  color: #333;
}

.content {
  width: 100%;
  padding: 16px;
  backgroundColor: #f8f8f8;
  borderRadius: 8px;
}

.demo-box {
  marginTop: 16px;
  padding: 12px;
  borderRadius: 6px;
  backgroundColor: #e6e6e6;
}

.demo-text {
  fontSize: 15px;
  color: #666;
}
</FILE>

<FILE path="/src/index.js">
Card({
  data: {
    title: "明朝皇帝顺序表"
  },
  
  onLoad() {
    console.log("页面加载完成");
  }
});
</FILE>

<FILE path="/src/index.json">
{
  "component": false,
  "usingComponents": {}
}
</FILE>
</FILES>
