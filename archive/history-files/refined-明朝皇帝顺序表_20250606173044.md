# 明朝皇帝顺序表 (Lynx版本)

## 使用最新的lynx direct PE生成

API调用失败，这是备用内容。

### 查询: 明朝皇帝顺序表

<FILES>
<FILE path="/src/index.ttml">
<view class="container">
  <text class="title">Lynx示例: 明朝皇帝顺序表</text>
  <view class="content">
    <text>这是备用内容，API调用失败</text>
  </view>
</view>
</FILE>

<FILE path="/src/index.ttss">
.container {
  display: flex;
  flexDirection: column;
  alignItems: center;
  justifyContent: center;
  padding: 20dp;
}

.title {
  fontSize: 18dp;
  fontWeight: bold;
  marginBottom: 16dp;
  color: #333;
}

.content {
  width: 100%;
  padding: 16dp;
  backgroundColor: #f8f8f8;
  borderRadius: 8dp;
}
</FILE>

<FILE path="/src/index.js">
Page({
  data: {
    title: "明朝皇帝顺序表"
  },
  
  onLoad() {
    console.log("页面加载完成");
  }
});
</FILE>
</FILES>

## 说明

本文档是API调用失败时生成的备用内容。
