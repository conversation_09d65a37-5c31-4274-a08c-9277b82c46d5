# `runtime_convert_parse5` vs. `@byted-lynx/web-speedy-plugin` 功能对比分析

## 1. 核心定位与设计哲学的差异

| 特性 | `runtime_convert_parse5` | `@byted-lynx/web-speedy-plugin` |
| :--- | :--- | :--- |
| **定位** | **浏览器端、轻量级、实时转换引擎** | **编译时、重量级、企业级构建插件** |
| **设计哲学** | 模拟与兼容，追求在浏览器中快速实现核心功能 | 完整生态、工程化，追求性能、稳定性和可维护性 |
| **运行环境** | 纯浏览器环境，无Node.js依赖 | Node.js构建环境 (如 Webpack, Rspack) |
| **核心依赖** | `parse5` (HTML解析器) | `Rspack`/`Webpack`, `PostCSS`, `Babel` 等完整构建工具链 |

## 2. 功能实现的深度与广度对比

### 2.1. 模板转换 (TTML -> HTML/JSX)

- **`@byted-lynx/web-speedy-plugin`**: 
  - **完整AST转换**: 使用专业的编译器技术，将TTML完整解析为抽象语法树(AST)，再转换为优化后的JavaScript (JSX)。
  - **企业级特性**: 支持完整的Lynx组件化体系、`lx:if`/`lx:for`等全部指令、复杂数据绑定、计算属性、事件系统等。
  - **编译时优化**: 在构建阶段完成大量优化，如静态节点提升、代码分割、Tree Shaking，生成高效的运行时代码。

- **`runtime_convert_parse5`**:
  - **基于HTML解析的模拟**: 使用 `parse5` 将TTML作为一种“类HTML”进行解析。这本质上是一种“模拟”而非“编译”。
  - **核心功能覆盖**: 实现了对基础标签、核心指令 (`lx:if`, `lx:for`) 和事件 (`bindtap`) 的映射转换。功能覆盖面有限，主要依赖从 `web-speedy-plugin` 提取的**映射规则**，而非其**转换逻辑**。
  - **运行时转换**: 所有转换都在浏览器中实时进行，无法进行编译时的深度优化，性能开销较大。

### 2.2. 样式处理 (TTSS -> CSS)

- **`@byted-lynx/web-speedy-plugin`**:
  - **工程化CSS处理**: 集成 `PostCSS` 等工具，支持完整的TTSS语法，包括变量、混合、嵌套等，并能处理 `@import`。
  - **精细的作用域控制**: 通过 `tagV` 机制，在编译时为每个组件生成唯一的作用域标识，并精确地将该标识应用到CSS选择器和HTML元素上，保证样式的隔离。
  - **选择器权重提升**: 具备 `webBumpAllSelectorSpecificity` 等高级功能，能通过特定技巧提升样式优先级，解决样式覆盖问题。

- **`runtime_convert_parse5`**:
  - **字符串替换与正则处理**: TTSS的处理更偏向于字符串操作和正则表达式匹配，例如，使用正则完成 `rpx` 到 `vw` 的转换。
  - **模拟作用域**: 同样实现了 `tagV` 机制的模拟，在运行时为组件生成哈希值并添加到元素和选择器上。但这种方式不如编译时处理得精确和高效。
  - **功能局限**: 不支持复杂的TTSS语法，如CSS变量、`@import` 等。

### 2.3. 脚本与逻辑 (Lepus Script)

- **`@byted-lynx/web-speedy-plugin`**:
  - **完整的脚本编译**: 拥有专门的 `Lepus` 脚本解析器和编译器，能处理Lynx中的业务逻辑、数据状态管理和生命周期。

- **`runtime_convert_parse5`**:
  - **完全不支持**: `runtime_convert_parse5` 的设计目标是UI渲染，完全没有处理 `Lepus` 脚本的能力。这是两者最根本的区别之一。

## 3. 为什么 `runtime_convert_parse5` 无法在浏览器实现 `web-speedy-plugin` 的功能？

1.  **环境限制 (浏览器 vs. Node.js)**
    -   `@byted-lynx/web-speedy-plugin` 依赖于Node.js环境进行文件读写、模块解析和大量的计算密集型编译任务。这些能力在浏览器沙箱环境中是受限或不存在的。
    -   浏览器无法承载一个完整的构建工具链（如Rspack、Babel、PostCSS）。

2.  **编译时 vs. 运行时**
    -   `web-speedy-plugin` 的核心价值在于**编译时优化**。它在代码上线前，就已经将TTML/TTSS转换和优化为最高效的Web代码。这是实现高性能的关键。
    -   `runtime_convert_parse5` 在浏览器中**实时进行转换**，这意味着每次加载都需要消耗用户的CPU和内存来执行解析和转换，对于复杂页面，性能瓶颈会非常明显。

3.  **生态系统的缺失**
    -   `web-speedy-plugin` 是庞大的 `lina-mono` 和 `Lynx` 生态的一部分，它能无缝地与其他工具（如lynx-client, lynx-shared）协同工作。
    -   `runtime_convert_parse5` 是一个独立的、轻量级的库，它脱离了这个生态，无法利用生态中提供的组件、API和状态管理等能力。

4.  **功能的深度差异**
    -   `runtime_convert_parse5` 的实现是一种“尽力而为”的**模拟**。它通过提取 `web-speedy-plugin` 的**最终映射规则**，试图在浏览器中重现其效果。
    -   然而，它缺失了达到这些效果的**完整过程**，包括复杂的AST分析、作用域注入、代码优化、错误处理和降级策略等。这就像是只抄了答案，却没有掌握解题方法。

## 4. 总结

| 对比维度 | `runtime_convert_parse5` | `@byted-lynx/web-speedy-plugin` |
| :--- | :--- | :--- |
| **本质** | 一个**运行时**的、基于规则映射的**TTML/TTSS渲染器** | 一个**编译时**的、完整的**Lynx到Web的构建方案** |
| **目标** | 在浏览器中快速预览Lynx UI片段 | 构建生产级别的、高性能、全功能的Web应用 |
| **差距** | 无法处理业务逻辑、缺少编译时优化、功能覆盖有限 | 功能完整、性能卓越、生态集成度高 |

`runtime_convert_parse5` 是一个非常出色的**概念验证(PoC)**和**轻量级预览工具**。它巧妙地利用 `parse5` 和提取的映射规则，在浏览器这个受限的环境中，最大程度地模拟了 `web-speedy-plugin` 的UI渲染能力，极大地简化了代码并提升了特定场景下的开发效率。 

然而，由于其**运行时**的本质和对**Node.js构建生态**的缺失，它永远无法替代 `@byted-lynx/web-speedy-plugin` 成为一个企业级的、用于生产环境的完整解决方案。两者并非替代关系，而是分别适用于**快速预览**和**正式构建**两个不同场景的工具。

## 5. CSS 转换问题实例分析：为什么转换后的 iframe 没有显示正确的 UI

### 5.1 问题描述

以下是一个实际的 TTSS 转换案例，展示了 `runtime_convert_parse5` 在处理复杂样式时遇到的问题：

**原始 TTSS 样式**（第一段）：
```css
.container {
  max-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.main-content {
  padding: 32px 24px;
  min-height: 100vh;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 32px;
}

.title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.globe-icon {
  font-size: 32px;
  margin-right: 12px;
}

.main-title {
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.subtitle {
  font-size: 16px;
  color: rgba(255,255,255,0.8);
  font-weight: 400;
}

/* 统计概览卡片 */
.stats-overview {
  display: flex;
  gap: 16px;
  margin-bottom: 32px;
}

.stat-card {
  flex: 1;
  background: rgba(255,255,255,0.15);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 20px;
  text-align: center;
  border: 1px solid rgba(255,255,255,0.2);
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: rgba(255,255,255,0.7);
  font-weight: 500;
}

/* 排行榜列表 */
.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 32px;
}

.list-item {
  background: rgba(255,255,255,0.95);
  border-radius: 20px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  border: 1px solid rgba(255,255,255,0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.list-item:active {
  transform: translateY(2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.rank-1 {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  box-shadow: 0 12px 40px rgba(255,215,0,0.3);
}

.rank-2 {
  background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
  box-shadow: 0 12px 40px rgba(192,192,192,0.3);
}

/* 排名徽章 */
.rank-badge {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: linear-gradient(135deg, #6c7ce7 0%, #a855f7 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 4px 12px rgba(108,124,231,0.3);
}

.rank-gold {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  box-shadow: 0 4px 12px rgba(255,107,53,0.4);
}

.rank-silver {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  box-shadow: 0 4px 12px rgba(116,185,255,0.4);
}

.rank-bronze {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
  box-shadow: 0 4px 12px rgba(253,121,168,0.4);
}

.rank-number {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
}

/* 国家信息 */
.country-info {
  flex: 1;
  margin-right: 16px;
}

.country-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.country-name {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin-right: 8px;
}

.country-flag {
  font-size: 24px;
}

.population-main {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  display: block;
  margin-bottom: 4px;
}

.population-detail {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

/* 进度条 */
.progress-container {
  width: 80px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(113,128,150,0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 数据说明 */
.data-note {
  background: rgba(255,255,255,0.1);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: flex-start;
  border: 1px solid rgba(255,255,255,0.2);
}

.note-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.info-symbol {
  font-size: 20px;
}

.note-content {
  flex: 1;
}

.note-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  display: block;
  margin-bottom: 8px;
}

.note-text {
  font-size: 14px;
  color: rgba(255,255,255,0.8);
  line-height: 1.6;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .main-content {
    padding: 24px 16px;
  }

  .main-title {
    font-size: 24px;
  }

  .stats-overview {
    flex-direction: column;
    gap: 12px;
  }

  .list-item {
    padding: 16px;
  }

  .country-name {
    font-size: 18px;
  }

  .population-main {
    font-size: 20px;
  }
}
```

### 5.2 转换后的问题分析

**转换后的 HTML 结构问题**：

通过分析转换后的 iframe 内容，发现了以下关键问题：

#### 问题1：重复的 class 属性
```html
<!-- 转换后的HTML中存在重复的class属性 -->
<div class="lynx-scroll-view" data-v-76844e6c-0b0e-4bb5-94a1-92ae418365a7
     data-scroll-y="{{true}}" class="container">
```
- 同一个元素有两个 `class` 属性
- 浏览器只会识别第一个，导致 `container` 样式丢失

#### 问题2：CSS 作用域化过度
```css
/* 所有样式都被强制添加了作用域前缀 */
[data-v-76844e6c-0b0e-4bb5-94a1-92ae418365a7] .container {
  max-width: 100%;
  margin: 0 auto;
  background: white;
  /* ... */
}
```

#### 问题3：原始样式被覆盖
转换后的 CSS 中，原始的渐变背景等样式被简化或丢失：

**原始样式**：
```css
.container {
  max-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
```

**转换后**：
```css
[data-v-76844e6c-0b0e-4bb5-94a1-92ae418365a7] .container {
  max-width: 100%;
  margin: 0 auto;
  background: white;  /* 丢失了渐变背景 */
}
```

### 5.3 根本原因分析

#### A. HTML 属性合并问题
在 `parse5-ttml-adapter.ts` 中，属性转换逻辑存在问题：
```typescript
// 问题：没有正确合并相同属性
private transformAttributes(attrs: Array<{name: string, value: string}>): string {
  // 如果同时有 class 和其他映射到 class 的属性，会产生重复
}
```

#### B. CSS 映射不完整
在 `ttss-processor.ts` 中：
```typescript
// 问题：复杂的CSS特性没有被正确保留
private parseCSS(css: string): CSSRule[] {
  // 正则解析可能无法处理复杂的CSS语法
  const ruleRegex = /([^{}]+)\s*\{([^{}]*)\}/g;
}
```

#### C. 作用域化策略问题
```typescript
// 问题：所有样式都被强制作用域化，包括全局样式
private addSelectorScope(rules: CSSRule[], componentId: string): CSSRule[] {
  const scopeAttribute = `[data-v-${componentId}]`;
  // 没有区分全局样式和组件样式
}
```

### 5.4 解决方案

#### 方案1：修复 HTML 属性合并
```typescript
private mergeAttributes(attrs: Record<string, string>): Record<string, string> {
  const merged: Record<string, string> = {};

  for (const [key, value] of Object.entries(attrs)) {
    if (key === 'class' || key === 'className') {
      // 合并所有 class 相关属性
      const existingClass = merged.className || merged.class || '';
      merged.className = existingClass ? `${existingClass} ${value}` : value;
      delete merged.class; // 确保只有一个 class 属性
    } else {
      merged[key] = value;
    }
  }

  return merged;
}
```

#### 方案2：改进 CSS 解析
```typescript
// 使用更强大的 CSS 解析器，而不是简单正则
import * as postcss from 'postcss';

private parseCSS(css: string): CSSRule[] {
  try {
    const root = postcss.parse(css);
    return this.convertPostCSSToRules(root);
  } catch (error) {
    // 降级到正则解析
    return this.parseWithRegex(css);
  }
}
```

#### 方案3：智能作用域化
```typescript
private shouldScopeSelector(selector: string): boolean {
  // 不对以下选择器进行作用域化
  const globalSelectors = [
    /^body\b/,
    /^html\b/,
    /^@media/,
    /^@keyframes/,
    /^\*/,
    /:root/
  ];

  return !globalSelectors.some(pattern => pattern.test(selector));
}
```

### 5.5 临时解决方案

如果需要立即解决显示问题，可以：

1. **禁用作用域化**：
```typescript
const engine = new Parse5TransformEngine({
  enableScope: false  // 临时禁用作用域化
});
```

2. **使用内联样式**：
```typescript
// 将关键样式转换为内联样式
const criticalStyles = {
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  minHeight: '100vh'
};
```

3. **手动修复 HTML**：
```typescript
// 后处理修复重复属性
html = html.replace(/class="([^"]*)"([^>]*?)class="([^"]*)"/g,
                   'class="$1 $3"$2');
```

### 5.6 问题总结

UI 没有成功应用的主要原因是：
1. **HTML 属性重复**导致样式类名丢失
2. **CSS 解析不完整**导致复杂样式丢失
3. **过度作用域化**破坏了原始样式结构
4. **映射规则不完善**无法处理现代 CSS 特性

这些问题反映了 `runtime_convert_parse5` 在处理复杂 CSS 时的局限性，需要在 HTML 属性合并、CSS 解析和作用域化策略方面进行改进。

---

## 6. 技术差距的深层原因

### 6.1 架构设计理念差异

**`runtime_convert_parse5` 的设计理念**：
- **快速模拟**：通过提取映射规则，在浏览器中快速重现效果
- **轻量级实现**：最小化依赖，专注核心转换功能
- **实时处理**：所有转换在运行时完成，适合预览场景

**`@byted-lynx/web-speedy-plugin` 的设计理念**：
- **完整编译**：基于完整的 AST 分析和编译器技术
- **企业级架构**：深度集成构建工具链，支持复杂项目需求
- **编译时优化**：在构建阶段完成所有优化，运行时性能最佳

### 6.2 技术实现的本质差异

| 技术层面 | `runtime_convert_parse5` | `@byted-lynx/web-speedy-plugin` |
|---------|-------------------------|--------------------------------|
| **解析方式** | 基于 Parse5 的 HTML 解析 + 正则表达式 | 基于 Lynx 官方 AST 解析器 |
| **转换策略** | 字符串替换 + 映射规则 | 完整的编译器转换管道 |
| **优化程度** | 基础缓存和性能优化 | 编译时深度优化（Tree Shaking、代码分割） |
| **错误处理** | 简单的错误捕获和降级 | 完整的错误诊断和恢复机制 |
| **扩展性** | 有限的插件化支持 | 完整的插件生态系统 |

这种差异导致了在处理复杂场景时的显著性能和功能差距，特别是在 CSS 转换、组件系统和构建集成方面。

---

## 7. 最终结论与建议

### 7.1 核心差异总结

| 维度 | `runtime_convert_parse5` | `@byted-lynx/web-speedy-plugin` |
|------|-------------------------|--------------------------------|
| **本质定位** | 浏览器端预览工具 | 企业级构建解决方案 |
| **技术架构** | Parse5 + 正则 + 映射规则 | 完整编译器 + AST + 构建工具链 |
| **功能完整性** | 70% 基础功能覆盖 | 100% 企业级功能支持 |
| **CSS 处理能力** | 基础转换，存在兼容性问题 | 完整的现代 CSS 支持 |
| **性能特点** | 实时转换，适合小规模预览 | 编译时优化，适合生产环境 |
| **维护成本** | 需要手动同步 Lynx 更新 | 官方维护，自动跟随更新 |

### 7.2 使用场景建议

**选择 `runtime_convert_parse5` 的场景**：
- ✅ **在线代码编辑器**：实时预览 TTML/TTSS 效果
- ✅ **快速原型验证**：验证设计思路和布局效果
- ✅ **教学演示工具**：展示 Lynx 语法和转换原理
- ✅ **轻量级集成**：需要在现有 Web 应用中嵌入 Lynx 预览功能

**选择 `@byted-lynx/web-speedy-plugin` 的场景**：
- ✅ **生产环境应用**：正式的 Lynx 到 Web 项目
- ✅ **复杂业务逻辑**：需要完整的组件系统和状态管理
- ✅ **团队协作开发**：需要稳定的构建工具链支持
- ✅ **性能要求严格**：需要编译时优化和代码分割

### 7.3 技术改进建议

**对于 `runtime_convert_parse5` 的改进方向**：

1. **修复 CSS 转换问题**：
   - 解决 HTML 属性重复问题
   - 改进 CSS 解析器，支持更多现代 CSS 特性
   - 优化作用域化策略，避免破坏原始样式

2. **增强错误处理**：
   - 提供更详细的错误诊断信息
   - 实现优雅的降级策略
   - 添加调试模式和日志输出

3. **扩展功能覆盖**：
   - 支持更多 Lynx 组件和指令
   - 改进事件系统处理
   - 增加对复杂布局的支持

### 7.4 最终建议

`runtime_convert_parse5` 和 `@byted-lynx/web-speedy-plugin` 应该被视为**互补而非竞争**的工具：

- **`runtime_convert_parse5`** 在浏览器端预览和快速验证场景下具有独特价值，是优秀的概念验证工具
- **`@byted-lynx/web-speedy-plugin`** 是生产环境的必选方案，提供完整的企业级支持

对于开发团队而言，理想的工作流程是：
1. 使用 `runtime_convert_parse5` 进行快速原型设计和效果验证
2. 在确认设计方案后，迁移到 `@byted-lynx/web-speedy-plugin` 进行正式开发
3. 利用两者的优势，在不同阶段使用合适的工具

这种组合使用的方式能够最大化开发效率，既保证了快速迭代的灵活性，又确保了最终产品的质量和性能。



被转换的白屏;<iframe srcdoc="<!DOCTYPE html>
<html lang=&quot;zh-CN&quot;>
<head>
  <meta charset=&quot;UTF-8&quot;>
  <meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;>
  <title>Lynx Preview - bhh2fil5</title>
  <style>

    /* Parse5 Lynx Preview Base Styles */
    * {
      box-sizing: border-box;
    }
    
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      background: #f5f5f5;
    }
    
    #lynx-preview-root {
      width: 100%;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      padding: 20px;
    }
    
    #lynx-app-container {
      width: 100%;
      max-width: 375px;
      min-height: 667px;
      background: #ffffff;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      position: relative;
    }
    
    /* Lynx组件基础样式 */
    .lynx-view {
      display: block;
    }
    
    .lynx-scroll-view {
      overflow: auto;
    }
    
    .lynx-text {
      display: inline;
    }
    
    .lynx-image {
      display: block;
      max-width: 100%;
      height: auto;
    }
    
    .lynx-button {
      display: inline-block;
      padding: 8px 16px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: #fff;
      cursor: pointer;
      font-size: 14px;
    }
    
    .lynx-button:hover {
      background: #f0f0f0;
    }
    
    .lynx-input {
      display: block;
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .lynx-input:focus {
      outline: none;
      border-color: #007aff;
    }
    
    /* 表单元素样式 */
    .lynx-switch {
      width: 40px;
      height: 20px;
    }
    
    .lynx-slider {
      width: 100%;
    }
    
    .lynx-checkbox,
    .lynx-radio {
      margin-right: 8px;
    }
    
    /* 布局辅助类 */
    .lynx-flex {
      display: flex;
    }
    
    .lynx-flex-column {
      flex-direction: column;
    }
    
    .lynx-flex-center {
      justify-content: center;
      align-items: center;
    }
    
    /* 响应式辅助 */
    @media (max-width: 480px) {
      #lynx-preview-root {
        padding: 10px;
      }
      
      #lynx-app-container {
        max-width: 100%;
        border-radius: 0;
      }
    }
    
    /* 错误显示样式 */
    .ttml-error {
      color: #ff3333;
      padding: 16px;
      border: 1px solid #ff3333;
      border-radius: 4px;
      background: #fff5f5;
      font-family: monospace;
      white-space: pre-wrap;
    }
    
    /* 可移动组件样式 */
    .lynx-movable-area {
      position: relative;
      overflow: hidden;
    }
    
    .lynx-movable-view {
      position: absolute;
      top: 0;
      left: 0;
      cursor: move;
      user-select: none;
      touch-action: none;
    }
    
    .lynx-movable-view[data-disabled=&quot;true&quot;] {
      cursor: default;
      pointer-events: none;
    }
    
    .lynx-movable-view:active {
      z-index: 999;
    }
    
    /* 高级组件增强样式 */
    .lynx-swiper {
      position: relative;
      overflow: hidden;
    }
    
    .lynx-swiper[data-vertical=&quot;true&quot;] {
      height: 200px;
    }
    
    .lynx-swiper-item {
      flex-shrink: 0;
      width: 100%;
      height: 100%;
    }
    
    .lynx-progress {
      width: 100%;
      height: 6px;
      appearance: none;
    }
    
    .lynx-progress::-webkit-progress-bar {
      background-color: #f0f0f0;
      border-radius: 3px;
    }
    
    .lynx-progress::-webkit-progress-value {
      background-color: #007aff;
      border-radius: 3px;
    }
    
    /* 覆盖组件样式 */
    .lynx-cover-view,
    .lynx-cover-image {
      position: absolute;
      z-index: 1000;
    }


  </style>
  
  <!-- React 核心库 (确保加载顺序) -->
  <script crossorigin src=&quot;https://unpkg.com/react@18/umd/react.development.js&quot;></script>
  <script crossorigin src=&quot;https://unpkg.com/react-dom@18/umd/react-dom.development.js&quot;></script>

</head>
<body>
  <div id=&quot;lynx-preview-root&quot; data-component-id=&quot;bhh2fil5&quot;>
    <div id=&quot;lynx-app-container&quot;>
      <!-- 组件将在这里渲染 -->
    </div>
  </div>
  
  <script>

    // React环境设置
    const { useState, useEffect, useCallback, useMemo } = React;
    const { createRoot } = ReactDOM;
    
    // 全局错误处理
    window.addEventListener('error', function(e) {
      console.error('Preview错误:', e.error);
    });
    
    // 组件通用工具函数
    const utils = {
      // 模拟小程序API
      setData: function(data, callback) {
        console.log('setData called:', data);
        if (callback) callback();
      },
      
      // 事件处理辅助
      preventDefault: function(e) {
        if (e &amp;&amp; e.preventDefault) e.preventDefault();
      },
      
      stopPropagation: function(e) {
        if (e &amp;&amp; e.stopPropagation) e.stopPropagation();
      },
      
      // 生命周期模拟
      triggerLifecycle: function(name, ...args) {
        console.log(`生命周期: ${name}`, args);
      }
    };
    
    // 全局组件状态管理
    const globalState = {
      data: {},
      methods: {},
      lifecycle: {}
    };


    // 组件定义 - bhh2fil5
    const Component_bhh2fil5 = function(props) {
      const [data, setData] = useState({});
      const [loading, setLoading] = useState(false);
      
      // 组件方法
      const componentMethods = {
        setData: useCallback((newData, callback) => {
          setData(prevData => ({ ...prevData, ...newData }));
          if (callback) callback();
        }, []),
        
        getData: useCallback(() => data, [data]),
        
        // 事件处理器
        handleTap: useCallback((e) => {
          utils.preventDefault(e);
          console.log('tap event:', e);
        }, []),
        
        handleInput: useCallback((e) => {
          const value = e.target.value;
          console.log('input event:', value);
        }, []),
        
        handleChange: useCallback((e) => {
          const value = e.target.value;
          console.log('change event:', value);
        }, [])
      };
      
      // 生命周期模拟
      useEffect(() => {
        utils.triggerLifecycle('onLoad');
        
        return () => {
          utils.triggerLifecycle('onUnload');
        };
      }, []);
      
      useEffect(() => {
        utils.triggerLifecycle('onShow');
      });
      
      // 用户JavaScript代码
      // 无用户JavaScript代码
      
      // 渲染组件
      try {
        return (
          <div className=&quot;component-wrapper&quot; data-v-bhh2fil5>
            React.Fragment, null, React.createElement('div', {className: &quot;lynx-view&quot;, className: &quot;container lynx-view view-container&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, React.createElement('div', {className: &quot;lynx-view&quot;, className: &quot;header lynx-view view-container&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, React.createElement('span', {className: &quot;lynx-text&quot;, className: &quot;title lynx-text text-content&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, &quot;百数表&quot;), React.createElement('span', {className: &quot;lynx-text&quot;, className: &quot;subtitle lynx-text text-content&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, &quot;1-100数字学习表&quot;)), React.createElement('div', {className: &quot;lynx-view&quot;, className: &quot;grid-container lynx-view view-container&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, React.createElement('div', {className: &quot;lynx-view&quot;, className: &quot;grid lynx-view view-container&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, numbers.map((item, index) => null))), React.createElement('div', {className: &quot;lynx-view&quot;, className: &quot;info-panel lynx-view view-container&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, {{selectedNumber}} &amp;&amp; null, React.createElement('div', {className: &quot;lynx-view&quot;, className: &quot;stats lynx-view view-container&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, React.createElement('div', {className: &quot;lynx-view&quot;, className: &quot;stat-item lynx-view view-container&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, React.createElement('span', {className: &quot;lynx-text&quot;, className: &quot;stat-number lynx-text text-content&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, &quot;100&quot;), React.createElement('span', {className: &quot;lynx-text&quot;, className: &quot;stat-label lynx-text text-content&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, &quot;总数&quot;)), React.createElement('div', {className: &quot;lynx-view&quot;, className: &quot;stat-item lynx-view view-container&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, React.createElement('span', {className: &quot;lynx-text&quot;, className: &quot;stat-number lynx-text text-content&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, &quot;10&quot;), React.createElement('span', {className: &quot;lynx-text&quot;, className: &quot;stat-label lynx-text text-content&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, &quot;十的倍数&quot;)), React.createElement('div', {className: &quot;lynx-view&quot;, className: &quot;stat-item lynx-view view-container&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, React.createElement('span', {className: &quot;lynx-text&quot;, className: &quot;stat-number lynx-text text-content&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, &quot;20&quot;), React.createElement('span', {className: &quot;lynx-text&quot;, className: &quot;stat-label lynx-text text-content&quot;, &quot;data-v-bhh2fil5&quot;: &quot;&quot;}, &quot;五的倍数&quot;)))))
          </div>
        );
      } catch (error) {
        console.error('组件渲染错误:', error);
        return (
          <div className=&quot;ttml-error&quot;>
            组件渲染失败: {error.message}
          </div>
        );
      }
    };


    // 等待React加载完成
    function waitForReact() {
      return new Promise((resolve) => {
        if (window.React &amp;&amp; window.ReactDOM) {
          resolve();
        } else {
          setTimeout(() => waitForReact().then(resolve), 50);
        }
      });
    }
    
    // 渲染应用
    waitForReact().then(() => {
      try {
        const container = document.getElementById('lynx-app-container');
        if (!container) {
          console.error('找不到容器元素: lynx-app-container');
          return;
        }
        
        if (!window.React || !window.ReactDOM) {
          console.error('React或ReactDOM未加载');
          container.innerHTML = '<div class=&quot;ttml-error&quot;>React依赖加载失败</div>';
          return;
        }
        
        const { createRoot } = ReactDOM;
        const root = createRoot(container);
        
        // 错误边界组件
        const ErrorBoundary = function({ children }) {
          const [hasError, setHasError] = useState(false);
          const [error, setError] = useState(null);
          
          useEffect(() => {
            const handleError = (event) => {
              setHasError(true);
              setError(event.error);
            };
            
            window.addEventListener('error', handleError);
            return () => window.removeEventListener('error', handleError);
          }, []);
          
          if (hasError) {
            return React.createElement('div', {
              className: 'ttml-error'
            }, `组件运行时错误: ${error?.message || '未知错误'}`);
          }
          
          return children;
        };
        
        // 渲染组件
        root.render(
          React.createElement(ErrorBoundary, null,
            React.createElement(Component_bhh2fil5, null)
          )
        );
        
        console.log('Lynx Preview渲染完成 - bhh2fil5');
      } catch (error) {
        console.error('渲染失败:', error);
        const container = document.getElementById('lynx-app-container');
        if (container) {
          container.innerHTML = '<div class=&quot;ttml-error&quot;>渲染失败: ' + error.message + '</div>';
        }
      }
    }).catch(error => {
      console.error('React加载超时:', error);
      const container = document.getElementById('lynx-app-container');
      if (container) {
        container.innerHTML = '<div class=&quot;ttml-error&quot;>React加载超时</div>';
      }
    });

  </script>
</body>
</html>" sandbox="allow-scripts allow-same-origin allow-forms allow-pointer-lock allow-popups allow-modals allow-top-navigation-by-user-activation" title="百数表 预览" style="width: 100%; height: 100%; border: none; pointer-events: auto; display: block; background: white; touch-action: pan-y pinch-zoom;"></iframe>