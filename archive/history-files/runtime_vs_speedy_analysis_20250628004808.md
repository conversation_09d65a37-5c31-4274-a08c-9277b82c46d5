<iframe srcdoc="<!DOCTYPE html><html lang=&quot;zh-CN&quot;><head><meta charset=&quot;UTF-8&quot;><meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;><title>企业级TTML预览 - Lynx转Web</title><style>
        /* 全局重置样式 */
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
          background: #f8fafc;
          overflow-x: hidden;
          font-size: 3.733333vw; /* 14px in 100.000000vw design */
          line-height: 1.6;
        }
        
        /* Lynx组件基础样式（作用域化） */
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-view {
          display: flex;
          flex-direction: column;
          box-sizing: border-box;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-scroll-view {
          width: 100%;
          overflow: auto;
          -webkit-overflow-scrolling: touch;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-text {
          display: inline-block;
          line-height: 1.6;
          word-wrap: break-word;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-image {
          max-width: 100%;
          height: auto;
          display: block;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-image[data-mode=&quot;aspectFit&quot;] {
          object-fit: contain;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-image[data-mode=&quot;aspectFill&quot;] {
          object-fit: cover;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-image[data-mode=&quot;widthFix&quot;] {
          width: 100%;
          height: auto;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-image[loading=&quot;lazy&quot;] {
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-image[loading=&quot;lazy&quot;]:loaded {
          opacity: 1;
        }
        
        /* 表单组件样式 */
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-input {
          width: 100%;
          padding: 2.666667vw 2.666667vw; /* 10px in 100.000000vw */
          border: 0.266667vw solid #e5e7eb; /* 1px */
          border-radius: 1.066667vw; /* 4px */
          font-size: 3.733333vw; /* 14px */
          line-height: 1.5;
          background: white;
          transition: border-color 0.2s ease;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-input:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 0.8vw rgba(59, 130, 246, 0.1);
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-button {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          padding: 2.666667vw 4vw; /* 10px 15px */
          border: none;
          border-radius: 1.066667vw; /* 4px */
          font-size: 3.733333vw; /* 14px */
          font-weight: 500;
          text-align: center;
          cursor: pointer;
          transition: all 0.2s ease;
          background: #3b82f6;
          color: white;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-button:hover {
          background: #2563eb;
          transform: translateY(-0.266667vw); /* -1px */
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-button:active {
          transform: translateY(0);
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-switch {
          appearance: none;
          width: 13.333333vw; /* 50px */
          height: 6.666667vw; /* 25px */
          background: #d1d5db;
          border-radius: 3.333333vw; /* 12.5px */
          position: relative;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-switch:checked {
          background: #3b82f6;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-switch::before {
          content: '';
          position: absolute;
          width: 5.333333vw; /* 20px */
          height: 5.333333vw; /* 20px */
          background: white;
          border-radius: 50%;
          top: 0.666667vw; /* 2.5px */
          left: 0.666667vw; /* 2.5px */
          transition: transform 0.2s ease;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-switch:checked::before {
          transform: translateX(6.666667vw); /* 25px */
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-slider {
          width: 100%;
          height: 1.066667vw; /* 4px */
          background: #d1d5db;
          border-radius: 0.533333vw; /* 2px */
          outline: none;
          appearance: none;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-slider::-webkit-slider-thumb {
          appearance: none;
          width: 5.333333vw; /* 20px */
          height: 5.333333vw; /* 20px */
          background: #3b82f6;
          border-radius: 50%;
          cursor: pointer;
        }
        
        /* 高级组件样式 */
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-swiper {
          position: relative;
          overflow: hidden;
          width: 100%;
          height: 50vw; /* 默认高度 */
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-swiper-item {
          width: 100%;
          height: 100%;
          flex-shrink: 0;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-progress {
          width: 100%;
          height: 1.333333vw; /* 5px */
          background: #e5e7eb;
          border-radius: 0.666667vw; /* 2.5px */
          overflow: hidden;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-progress::-webkit-progress-bar {
          background: #e5e7eb;
          border-radius: 0.666667vw;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-progress::-webkit-progress-value {
          background: #3b82f6;
          border-radius: 0.666667vw;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-list {
          width: 100%;
          overflow-y: auto;
          -webkit-overflow-scrolling: touch;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-list-item {
          width: 100%;
          border-bottom: 0.266667vw solid #f3f4f6; /* 1px */
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-list-item:last-child {
          border-bottom: none;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-navigator {
          display: inline-block;
          text-decoration: none;
          color: inherit;
          cursor: pointer;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-web-view {
          width: 100%;
          height: 66.666667vw; /* 250px */
          border: none;
        }
        
        /* 自定义业务样式（兼容现有类名） */
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .container {
          max-width: 100%;
          margin: 0 auto;
          background: white;
          border-radius: 3.2vw; /* 12px */
          box-shadow: 0 0.533333vw 2.133333vw rgba(0,0,0,0.1); /* 0 2px 8px */
          padding: 6.4vw; /* 24px */
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .header-section {
          text-align: center;
          margin-bottom: 8.533333vw; /* 32px */
          padding-bottom: 6.4vw; /* 24px */
          border-bottom: 0.533333vw solid #e2e8f0; /* 2px */
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .title-container {
          margin-bottom: 4.266667vw; /* 16px */
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .main-title {
          font-size: 8.533333vw; /* 32px */
          font-weight: 700;
          color: #1a202c;
          margin-bottom: 2.133333vw; /* 8px */
          display: block;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .subtitle {
          font-size: 4.8vw; /* 18px */
          color: #64748b;
          font-weight: 500;
          display: block;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .stats-overview {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 5.333333vw; /* 20px */
          border-radius: 2.133333vw; /* 8px */
          margin: 5.333333vw 0; /* 20px */
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .content-section {
          margin-top: 6.4vw; /* 24px */
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .country-item {
          display: flex;
          align-items: center;
          padding: 4.266667vw; /* 16px */
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          border-radius: 3.2vw; /* 12px */
          margin-bottom: 3.2vw; /* 12px */
          box-shadow: 0 0.533333vw 2.133333vw rgba(0,0,0,0.08);
          transition: all 0.3s ease;
          border-left: 1.066667vw solid transparent; /* 4px */
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .country-item:hover {
          transform: translateY(-0.533333vw); /* -2px */
          box-shadow: 0 1.066667vw 4.266667vw rgba(0,0,0,0.12);
          border-left-color: #667eea;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .rank {
          width: 12.8vw; /* 48px */
          height: 12.8vw; /* 48px */
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 700;
          font-size: 4.8vw; /* 18px */
          margin-right: 5.333333vw; /* 20px */
          box-shadow: 0 1.066667vw 3.2vw rgba(59, 130, 246, 0.3);
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .country-name {
          flex: 1;
          font-size: 5.333333vw; /* 20px */
          font-weight: 600;
          color: #1a202c;
          margin-right: 4.266667vw; /* 16px */
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .population {
          font-size: 4.8vw; /* 18px */
          color: #667eea;
          font-weight: 600;
          background: rgba(102, 126, 234, 0.1);
          padding: 2.133333vw 4.266667vw; /* 8px 16px */
          border-radius: 2.133333vw; /* 8px */
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .footer-section {
          margin-top: 10.666667vw; /* 40px */
          padding-top: 6.4vw; /* 24px */
          border-top: 0.533333vw solid #e2e8f0; /* 2px */
          text-align: center;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .footer-note {
          color: #64748b;
          font-size: 3.733333vw; /* 14px */
          font-style: italic;
          display: block;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
          [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .lynx-scroll-view {
            padding: 3.2vw; /* 12px */
          }
          
          [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .container {
            padding: 5.333333vw; /* 20px */
          }
          
          [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .main-title {
            font-size: 6.4vw; /* 24px */
          }
          
          [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .subtitle {
            font-size: 4.266667vw; /* 16px */
          }
          
          [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .country-item {
            flex-direction: column;
            text-align: center;
            padding: 4.266667vw; /* 16px */
          }
          
          [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .rank {
            margin-bottom: 3.2vw; /* 12px */
            margin-right: 0;
          }
          
          [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .country-name {
            margin-right: 0;
            margin-bottom: 2.133333vw; /* 8px */
          }
        }
        
        /* 动画效果 */
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(5.333333vw); /* 20px */
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .country-item {
          animation: fadeInUp 0.6s ease forwards;
        }
        
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .country-item:nth-child(1) { animation-delay: 0.1s; }
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .country-item:nth-child(2) { animation-delay: 0.2s; }
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .country-item:nth-child(3) { animation-delay: 0.3s; }
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .country-item:nth-child(4) { animation-delay: 0.4s; }
        [data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc] .country-item:nth-child(5) { animation-delay: 0.5s; }
      </style></head><body><div data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc>根据最新数据，世界人口最多的十个国家如下：

<FILES>
<FILE name=&quot;population-ranking.ttml&quot;>
<div class=&quot;lynx-view&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;container&quot;>
  <div class=&quot;lynx-view&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;header&quot;>
    <span class=&quot;lynx-text&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;title&quot;>世界人口最多的十个国家</span>
    <span class=&quot;lynx-text&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;subtitle&quot;>2024年最新数据统计</span>
  </div>
  
  <div class=&quot;lynx-view&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;ranking-list&quot;>
    <div class=&quot;lynx-view&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc 
      class=&quot;country-item&quot; 
      tt:for=&quot;{{countries}}&quot; 
      tt:key=&quot;rank&quot;
      data-bind-tap=&quot;showDetails&quot;
      data-country=&quot;{{item}}&quot;
    >
      <div class=&quot;lynx-view&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;rank-badge&quot;>
        <span class=&quot;lynx-text&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;rank-number&quot;>{{item.rank}}</span>
      </div>
      
      <div class=&quot;lynx-view&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;country-info&quot;>
        <div class=&quot;lynx-view&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;country-header&quot;>
          <span class=&quot;lynx-text&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;country-flag&quot;>{{item.flag}}</span>
          <span class=&quot;lynx-text&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;country-name&quot;>{{item.name}}</span>
        </div>
        <span class=&quot;lynx-text&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;population&quot;>{{item.population}}</span>
        <span class=&quot;lynx-text&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;percentage&quot;>占世界人口 {{item.percentage}}</span>
      </div>
      
      <div class=&quot;lynx-view&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;population-bar&quot;>
        <div class=&quot;lynx-view&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc 
          class=&quot;bar-fill&quot; 
          style=&quot;width: {{item.barWidth}}%&quot;
        ></div>
      </div>
    </div>
  </div>
  
  <div class=&quot;lynx-view&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;summary&quot;>
    <span class=&quot;lynx-text&quot; data-v-84068a36-ffd6-47dc-ac8f-cf2f64f552bc class=&quot;summary-text&quot;>前十国家人口总计约占世界人口的67%</span>
  </div>
</div>
</FILE>

<FILE name=&quot;population-ranking.ttss&quot;>
.container {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 800px;
}

.header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 28px;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  background-clip: text;
  color: transparent;
}

.subtitle {
  font-size: 16px;
  color: #718096;
}

.ranking-list {
  flex-direction: column;
  gap: 16px;
}

.country-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  flex-direction: row;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.country-item:active {
  transform: scale(0.98);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.rank-badge {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  justify-content: center;
  align-items: center;
  margin-right: 16px;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.rank-number {
  font-size: 20px;
  font-weight: bold;
  color: white;
}

.country-info {
  flex: 1;
  flex-direction: column;
}

.country-header {
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
}

.country-flag {
  font-size: 24px;
  margin-right: 12px;
}

.country-name {
  font-size: 20px;
  font-weight: bold;
  color: #2d3748;
  flex: 1;
}

.population {
  font-size: 18px;
  color: #4a5568;
  font-weight: 600;
  margin-bottom: 4px;
}

.percentage {
  font-size: 14px;
  color: #718096;
}

.population-bar {
  width: 80px;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  margin-left: 16px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #48bb78, #38a169);
  border-radius: 4px;
  transition: width 0.8s ease;
}

.summary {
  margin-top: 32px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  text-align: center;
}

.summary-text {
  font-size: 16px;
  color: #4a5568;
  font-weight: 500;
}

/* 排名前三特殊样式 */
.country-item:nth-child(1) .rank-badge {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}

.country-item:nth-child(2) .rank-badge {
  background: linear-gradient(135deg, #c0c0c0, #a8a8a8);
}

.country-item:nth-child(3) .rank-badge {
  background: linear-gradient(135deg, #cd7f32, #b8860b);
}
</FILE>

<FILE name=&quot;population-ranking.js&quot;>
Card({
  data: {
    countries: [
      {
        rank: 1,
        name: '中国',
        flag: '🇨🇳',
        population: '14.12亿',
        percentage: '17.9%',
        barWidth: 100,
        details: '世界第一人口大国，拥有悠久历史和灿烂文化'
      },
      {
        rank: 2,
        name: '印度',
        flag: '🇮🇳',
        population: '14.17亿',
        percentage: '17.7%',
        barWidth: 98,
        details: '人口增长迅速，预计将超越中国成为第一'
      },
      {
        rank: 3,
        name: '美国',
        flag: '🇺🇸',
        population: '3.31亿',
        percentage: '4.2%',
        barWidth: 23,
        details: '世界第三大人口国，经济发达国家'
      },
      {
        rank: 4,
        name: '印度尼西亚',
        flag: '🇮🇩',
        population: '2.73亿',
        percentage: '3.5%',
        barWidth: 19,
        details: '东南亚最大国家，群岛国家'
      },
      {
        rank: 5,
        name: '巴基斯坦',
        flag: '🇵🇰',
        population: '2.20亿',
        percentage: '2.8%',
        barWidth: 16,
        details: '南亚国家，人口增长较快'
      },
      {
        rank: 6,
        name: '巴西',
        flag: '🇧🇷',
        population: '2.15亿',
        percentage: '2.7%',
        barWidth: 15,
        details: '南美洲最大国家，足球王国'
      },
      {
        rank: 7,
        name: '尼日利亚',
        flag: '🇳🇬',
        population: '2.06亿',
        percentage: '2.6%',
        barWidth: 15,
        details: '非洲人口最多的国家'
      },
      {
        rank: 8,
        name: '孟加拉国',
        flag: '🇧🇩',
        population: '1.64亿',
        percentage: '2.1%',
        barWidth: 12,
        details: '南亚国家，人口密度极高'
      },
      {
        rank: 9,
        name: '俄罗斯',
        flag: '🇷🇺',
        population: '1.46亿',
        percentage: '1.8%',
        barWidth: 10,
        details: '世界面积最大国家'
      },
      {
        rank: 10,
        name: '墨西哥',
        flag: '🇲🇽',
        population: '1.28亿',
        percentage: '1.6%',
        barWidth: 9,
        details: '北美洲国家，文化丰富多彩'
      }
    ]
  },

  onLoad() {
    console.log('人口排名页面加载完成');
    this.animateCountUp();
  },

  onReady() {
    console.log('页面渲染完成，开始数据动画');
    // 延迟启动条形图动画
    setTimeout(() => {
      this.animateBars();
    }, 500);
  },

  // 数字递增动画
  animateCountUp() {
    console.log('开始数字递增动画');
    // 这里可以添加数字递增效果的逻辑
  },

  // 条形图动画
  animateBars() {
    console.log('开始条形图动画');
    const countries = this.data.countries.map((country, index) => {
      return {
        ...country,
        barWidth: country.barWidth
      };
    });
    
    this.setData({
      countries: countries
    }, () => {
      console.log('条形图动画数据更新完成');
    });
  },

  // 点击国家详情
  showDetails(e) {
    const country = e.currentTarget.dataset.country;
    console.log('点击查看国家详情:', country);
    
    // 显示详情弹窗
    x.showModal({
      title: country.name + ' 详细信息',
      content: `人口: ${country.population}\n占比: ${country.percentage}\n${country.details}`,
      showCancel: false,
      confirmText: '知道了',
      success: (res) => {
        console.log('详情弹窗确认');
      }
    });
  },

  onShow() {
    console.log('人口排名页面显示');
  },

  onHide() {
    console.log('人口排名页面隐藏');
  },

  onDestroy() {
    console.log('人口排名页面销毁');
  }
});
</FILE>

<FILE name=&quot;population-ranking.json&quot;>
{
  &quot;component&quot;: true,
  &quot;usingComponents&quot;: {},
  &quot;styleIsolation&quot;: &quot;isolated&quot;
}
</FILE>
</FILES>

这个Lynx应用展示了世界人口最多的十个国家，具有以下特色功能：

🌟 **核心特性**：
- **视觉排名展示**：使用渐变色彩和排名徽章突出显示
- **数据可视化**：条形图直观展示人口比例关系
- **交互体验**：点击任意国家查看详细信息
- **动画效果**：条形图加载动画和点击反馈

📊 **数据亮点**：
1. **中国** - 14.12亿人口，占世界17.9%
2. **印度** - 14.17亿人口，占世界17.7%
3. **美国** - 3.31亿人口，占世界4.2%
4. **印度尼西亚** - 2.73亿人口，占世界3.5%
5. **巴基斯坦** - 2.20亿人口，占世界2.8%

🎨 **设计特色**：
- 渐变背景营造现代科技感
- 前三名使用金银铜色特殊标识
- 卡片式布局便于移动端浏览
- 条形图可视化人口比例差异

💡 **交互功能**：
- 点击任意国家卡片查看详细介绍
- 流畅的触摸反馈和动画效果
- 清晰的信息层次和视觉引导

这些数据反映了全球人口分布的重要趋势，前十国家总人口约占世界总人口的67%。</div></body></html>" sandbox="allow-scripts allow-same-origin allow-forms allow-pointer-lock allow-popups allow-modals allow-top-navigation-by-user-activation" title="世界人口最多的十个国家 预览" style="width: 100%; height: 100%; border: none; pointer-events: auto; display: block; background: white; touch-action: pan-y pinch-zoom;"></iframe>