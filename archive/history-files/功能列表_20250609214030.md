# 功能列表

本文档详细列出了 code_generate 模块的所有功能，包括每个组件的功能、按钮和交互方式。

## 核心功能概述

### 1. 并行代码生成
- Web代码和Lynx代码可以同时并行生成，互不影响
- 两种代码生成使用完全独立的Context系统和状态管理
- 用户可以在生成过程中自由切换查看不同类型的代码

### 2. 自动续传机制
- 智能检测代码是否完整，自动触发续传
- 支持多轮递归续传，直到代码完整
- 保持原始提示上下文，确保续传代码的连贯性
- 完全在后台执行，对用户透明

### 3. 流式数据解析
- 使用统一的`WebCodeJsonProcessor`处理所有格式的流式数据
- 仅区分思考内容(reasoning_content)和实际代码内容(content)
- 支持AWS SDK Claude格式、标准Claude格式和SSE格式数据
- 快速路径检测代码片段，直接返回非JSON内容
- 解析失败时返回空字符串，防止JSON元数据泄露到生成的代码中
- 批量处理连接的多个JSON对象
- 思考内容转换为代码注释并添加到生成代码前

### 4. 多视图模式
- Web视图：查看和编辑生成的网页代码
- Lynx视图：查看和编辑生成的Lynx代码，支持在内嵌Playground中预览
- 移动端预览：在模拟手机界面中查看Web代码效果

## 组件功能详解

### Preview 组件（主容器）
- **功能**：整体页面布局和状态管理
- **按钮/交互**：
  - 可调整分隔线：调整代码区域和聊天区域的宽度比例
  - 视图切换：在Web、Lynx和Mobile视图之间切换

### CodeHeader 组件
- **功能**：提供视图切换和代码操作功能
- **按钮/交互**：
  - **视图按钮**：
    - Web视图：切换到Web代码视图，显示Web代码大小和生成进度
    - Lynx视图：切换到Lynx代码视图，显示Lynx代码大小和生成进度
    - Mobile视图：切换到移动端预览视图，显示移动预览大小和加载进度
  
  - **Web代码操作按钮**（在Web视图显示）：
    - 复制按钮：将Web代码复制到剪贴板
    - 编辑/保存按钮：切换编辑模式或保存编辑内容
    - 分享按钮：上传代码到CDN并生成分享链接
    - 重新生成按钮：重新生成Web代码
    - 转换成Lynx按钮：将Web代码转换为Lynx代码
  
  - **Lynx代码操作按钮**（在Lynx视图显示）：
    - 代码/预览切换按钮：在代码和预览模式之间切换
    - 重新解构上传按钮：重新解构和上传Lynx代码
    - 复制按钮：将Lynx代码复制到剪贴板
    - 编辑/保存按钮：切换编辑模式或保存编辑内容
    - Playground按钮：在新窗口打开Lynx Playground
  
  - **移动端操作按钮**（在Mobile视图显示）：
    - 刷新预览按钮：刷新移动端预览

### WebCodeHighlight 组件
- **功能**：显示和编辑Web代码，提供语法高亮
- **按钮/交互**：
  - 代码编辑区域：支持直接编辑代码
  - 错误显示：当代码生成出错时显示错误信息
  - 重试按钮：当生成失败时重新尝试生成
  - 加载指示器：显示代码生成的加载状态和进度

### LynxTabContent 组件
- **功能**：管理Lynx代码视图和预览模式的切换
- **按钮/交互**：
  - 视图切换：在代码视图和Playground预览之间切换
  - 重试加载按钮：重新加载Playground预览
  - 加载指示器：显示Playground加载状态
  - 错误显示：当Playground加载失败时显示错误信息

### LynxCodeHighlight 组件
- **功能**：显示和编辑Lynx代码，提供语法高亮
- **按钮/交互**：
  - 代码编辑区域：支持直接编辑代码
  - 错误显示：当代码生成出错时显示错误信息
  - 重试按钮：当生成失败时重新尝试生成
  - 预览按钮：切换到Playground预览模式

### MobilePreview 组件
- **功能**：在模拟手机界面中预览Web代码效果
- **按钮/交互**：
  - 设备选择器：切换不同的移动设备尺寸
  - 方向切换：在横屏和竖屏之间切换
  - 刷新按钮：重新加载预览内容
  - 截图按钮：捕获当前预览内容的截图
  - 控制台：显示预览中的console输出和错误信息

### Chat 组件
- **功能**：提供与AI交互的界面，发送提示生成代码
- **按钮/交互**：
  - 输入框：输入提示文本
  - 发送按钮：发送提示生成代码
  - 重新生成按钮：使用相同提示重新生成代码
  - 历史消息显示：显示之前的对话历史
  - 建议提示：显示预设的提示建议

### ShareLinkButton 组件
- **功能**：生成和分享代码链接
- **按钮/交互**：
  - 分享按钮：上传代码到CDN并生成分享链接
  - 复制链接：自动复制生成的链接到剪贴板
  - 加载状态：显示分享过程的加载状态

## 数据流和状态管理

### Context架构
1. **UIContext**
   - `setViewMode`：设置当前视图模式
   - `setEditMode`：设置编辑模式
   - `setWebPanelWidth`：设置Web面板宽度
   - `setSidebarWidth`：设置侧边栏宽度

2. **WebRPCContext**
   - `updateWebCode`：更新Web代码内容
   - `resetWebCode`：重置Web代码
   - `setWebCodeComplete`：设置Web代码生成完成状态
   - `setWebRPCLoading`：设置Web代码加载状态
   - `regenerateWebCode`：重新生成Web代码

3. **LynxRPCContext**
   - `updateLynxCode`：更新Lynx代码内容
   - `setLynxLoading`：设置Lynx代码加载状态
   - `setLynxComplete`：设置Lynx代码生成完成状态
   - `setPlaygroundUrl`：设置Lynx Playground URL
   - `convertWebCodeToLynx`：将Web代码转换为Lynx代码
   - `reextractAndUploadFiles`：重新解构和上传Lynx代码

## 数据处理功能

### WebCodeJsonProcessor
- `processStreamChunk`：处理流式响应数据块，提取代码内容和思考内容
- `parseJsonChunk`：解析JSON格式数据，支持各种Claude API格式
- `processBatchedJsonStream`：处理批量JSON流数据，处理连接的多个JSON对象
- `isCodeContent`：快速检测明显的代码片段，优化处理流程

### ClaudeStreamParser
- `parseClaudeStream`：使用WebCodeJsonProcessor解析Claude流式响应
- `hasContinueToken`：检测是否存在续传标记
- `needsContinuation`：检查是否需要继续生成代码
- `processStreamData`：处理流数据，支持批量更新和错误恢复

### ContentProcessors
- `JsonProc.extractReasoning`：从JSON提取思考内容
- `JsonProc.extractClaudeContent`：使用WebCodeJsonProcessor提取Claude内容
- `HTML.formatReasoningAsComment`：将思考内容格式化为HTML代码注释

## RPC服务功能

### WebRPCService
- `generateWebCode`：生成Web代码
- `continueWebCodeGeneration`：继续生成Web代码（自动续传）
- `uploadWebCodeToCDN`：将Web代码上传到CDN生成分享链接
- `fetchWebCodeStream`：处理Web代码流式响应，使用WebCodeJsonProcessor解析数据

### LynxRPCService
- `convertWebCodeToLynx`：将Web代码转换为Lynx代码
- `extractAndUploadLynxFiles`：解构和上传Lynx代码文件
- `reextractAndUploadLynxFiles`：重新解构和上传Lynx代码文件
- `fetchLynxCodeStream`：处理Lynx代码流式响应，使用WebCodeJsonProcessor解析数据

## 存储机制

### localStorage存储项
- `WEB_CODE`：存储生成的Web代码
- `WEB_SESSION_ID`：存储Web会话ID
- `WEB_CODE_TIMESTAMP`：存储Web代码更新时间戳
- `LYNX_CODE`：存储生成的Lynx代码
- `LYNX_SESSION_ID`：存储Lynx会话ID
- `LYNX_PLAYGROUND_URL`：存储Lynx预览URL
- `LYNX_CODE_TIMESTAMP`：存储Lynx代码更新时间戳

### 存储原则
- 只在代码传输完成后写入localStorage
- 在手动编辑保存时写入localStorage
- 在组件卸载时保存当前状态
- 支持会话恢复和数据持久化

## 错误处理和用户反馈

### 错误处理
- 使用try-catch捕获并记录错误
- 在UI中显示友好的错误提示
- 提供重试机制恢复失败的操作
- 支持数据保护和回滚机制

### 用户反馈
- 使用Toast通知显示操作结果
- 提供加载进度指示器
- 在状态变化时更新UI元素
- 支持多种状态的可视化指示

## 数据保护机制

### JSON元数据保护
- SSE格式过滤：丢弃以`data:`开头的数据包
- 非JSON快速路径：直接返回明显的代码片段
- 解析失败安全处理：返回空字符串而非原始JSON
- 防止JSON元数据泄露到生成的代码中
- 批量处理连接的多个JSON对象