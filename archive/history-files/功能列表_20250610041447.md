# 功能列表

本文档详细列出了 code_generate 模块的所有功能，包括每个组件的功能、按钮和交互方式。

## 核心功能概述

### 1. 统一Context并行架构
- Web代码和Lynx代码通过统一的Context系统并行生成，互不影响
- 使用`CodeGenerationUnifiedContextV2`实现Web和Lynx状态的统一管理
- 支持批量更新和版本控制，减少不必要的重渲染
- 完善的生命周期管理和回调注册机制替代自定义事件
- 用户可以在生成过程中自由切换查看不同类型的代码

### 2. 自动续传机制
- 智能检测代码是否完整，自动触发续传
- 支持多轮递归续传，直到代码完整
- PromptComposer构建特殊的续传提示，包含上下文和先前生成的代码
- 完全在后台执行，对用户透明

### 3. 流式数据解析
- 使用统一的`WebCodeJsonProcessor`处理所有格式的流式数据
- 仅区分思考内容(reasoning_content)和实际代码内容(content)
- 支持AWS SDK Claude格式、标准Claude格式和SSE格式数据
- 快速路径检测代码片段，直接返回非JSON内容
- 解析失败时返回空字符串，防止JSON元数据泄露到生成的代码中
- 批量处理连接的多个JSON对象，减少渲染次数
- 思考内容转换为代码注释并添加到生成代码前

### 4. 智能提示词系统
- 使用`PromptComposer`组合器动态生成优化的提示词
- 基于用户意图分析（教育类、查询类、展示类）智能调整生成策略
- 支持Canvas优先策略动态切换，针对不同场景优化视觉表现
- 专门优化的文本布局处理，避免文字重叠和排版问题
- 平台特定的提示词优化（Web/Lynx）确保最佳效果

### 5. 多视图模式
- Web视图：查看和编辑生成的网页代码
- Lynx视图：查看和编辑生成的Lynx代码，支持在内嵌Playground中预览
- 移动端预览：在模拟手机界面中查看Web代码效果

## 组件功能详解

### Preview 组件（主容器）
- **功能**：整体页面布局和状态管理
- **按钮/交互**：
  - 可调整分隔线：调整代码区域和聊天区域的宽度比例
  - 标签切换：在Web、Lynx和Mobile标签之间切换
  - 批量更新UI：通过统一状态优化渲染性能

### CodeHeader 组件
- **功能**：提供视图切换和代码操作功能
- **按钮/交互**：
  - **标签按钮**：
    - Web标签：切换到Web代码视图，显示Web代码大小和生成进度
    - Lynx标签：切换到Lynx代码视图，显示Lynx代码大小和生成进度
    - Mobile标签：切换到移动端预览视图，显示移动预览状态
  
  - **Web代码操作按钮**（在Web视图显示）：
    - 复制按钮：将Web代码复制到剪贴板
    - 编辑/保存按钮：切换编辑模式或保存编辑内容
    - 分享按钮：上传代码到CDN并生成分享链接
    - 重新生成按钮：重新生成Web代码
    - 转换成Lynx按钮：将Web代码转换为Lynx代码
  
  - **Lynx代码操作按钮**（在Lynx视图显示）：
    - 代码/预览切换按钮：在代码和预览模式之间切换
    - 重新解构上传按钮：重新解构和上传Lynx代码
    - 复制按钮：将Lynx代码复制到剪贴板
    - 编辑/保存按钮：切换编辑模式或保存编辑内容
    - Playground按钮：在新窗口打开Lynx Playground
  
  - **移动端操作按钮**（在Mobile视图显示）：
    - 刷新预览按钮：刷新移动端预览
    - 截图按钮：捕获当前预览内容的截图

### WebCodeHighlight 组件
- **功能**：显示和编辑Web代码，提供语法高亮
- **按钮/交互**：
  - 代码编辑区域：支持直接编辑代码
  - 错误显示：当代码生成出错时显示错误信息
  - 重试按钮：当生成失败时重新尝试生成
  - 加载指示器：显示代码生成的加载状态和进度条
  - 安全的存储策略：只在传输完成时写入localStorage
  - 智能代码预处理：清理代码并移除元数据

### LynxTabContent 组件
- **功能**：管理Lynx代码视图和预览模式的切换
- **按钮/交互**：
  - 视图切换：在代码视图和Playground预览之间切换
  - 重试加载按钮：重新加载Playground预览
  - 加载指示器：显示Playground加载状态
  - 错误显示：当Playground加载失败时显示错误信息
  - 预览URL管理：更新和管理预览链接

### LynxCodeHighlight 组件
- **功能**：显示和编辑Lynx代码，提供语法高亮
- **按钮/交互**：
  - 代码编辑区域：支持直接编辑代码
  - 错误显示：当代码生成出错时显示错误信息
  - 重试按钮：当生成失败时重新尝试生成
  - 预览按钮：切换到Playground预览模式
  - 批量更新实现：减少代码更新频率，优化性能

### MobilePreview 组件
- **功能**：在模拟手机界面中预览Web代码效果
- **按钮/交互**：
  - 设备选择器：切换不同的移动设备尺寸
  - 方向切换：在横屏和竖屏之间切换
  - 刷新按钮：重新加载预览内容
  - 截图按钮：捕获当前预览内容的截图
  - 控制台：显示预览中的console输出和错误信息

### Chat 组件
- **功能**：提供与AI交互的界面，发送提示生成代码
- **按钮/交互**：
  - 输入框：输入提示文本
  - 发送按钮：发送提示生成代码
  - 重新生成按钮：使用相同提示重新生成代码
  - 历史消息显示：显示之前的对话历史
  - 建议提示：显示预设的提示建议
  - 智能提示分析：使用PromptComposer进行意图分析和提示优化

### ShareLinkButton 组件
- **功能**：生成和分享代码链接
- **按钮/交互**：
  - 分享按钮：上传代码到CDN并生成分享链接
  - 复制链接：自动复制生成的链接到剪贴板
  - 加载状态：显示分享过程的加载状态

## 数据流和状态管理

### 统一Context架构
1. **CodeGenerationUnifiedContextV2**
   - `state.web`: Web代码状态集合
   - `state.lynx`: Lynx代码状态集合
   - `state.ui`: UI状态集合
   - `state.version`: 状态版本号，用于批量更新
   - `batchUpdate`: 批量更新多个状态，减少重渲染
   - `dispatch`: 分发状态更新动作

2. **特定功能Hooks**
   - `useWebStateOptimized`: 获取Web代码状态，性能优化版本
   - `useLynxStateOptimized`: 获取Lynx代码状态，性能优化版本
   - `useUIStateOptimized`: 获取UI状态，性能优化版本
   - `useWebRPCUnified`: 获取Web RPC功能集合
   - `useLynxRPCUnified`: 获取Lynx RPC功能集合
   - `useWebCodeUnified`: 获取Web代码内容和操作
   - `useLynxViewModeUnified`: 获取和控制Lynx视图模式

## 数据处理功能

### WebCodeJsonProcessor
- `processStreamChunk`: 处理流式响应数据块，提取代码内容和思考内容
- `parseJsonChunk`: 解析JSON格式数据，支持各种Claude API格式
- `processBatchedJsonStream`: 处理批量JSON流数据，处理连接的多个JSON对象
- `isCodeContent`: 快速检测明显的代码片段，优化处理流程

### ClaudeStreamParser
- `parseClaudeStream`: 使用WebCodeJsonProcessor解析Claude流式响应
- `removeContinueTokensForDisplay`: 移除代码中的续传标记，美化显示
- `needsContinuation`: 检查是否需要继续生成代码
- `processStreamData`: 处理流数据，支持批量更新和错误恢复

### ContentProcessors
- `extractReasoning`: 从JSON提取思考内容
- `extractClaudeContent`: 使用WebCodeJsonProcessor提取Claude内容
- `formatReasoningAsComment`: 将思考内容格式化为代码注释

### PromptComposer
- `generateWebSystemPrompt`: 生成Web系统提示词
- `generateLynxSystemPrompt`: 生成Lynx系统提示词
- `analyzeUserIntent`: 分析用户输入意图，确定最佳生成策略
- `generateWebContinuationPrompt`: 生成Web续传提示词
- `generateLynxContinuationPrompt`: 生成Lynx续传提示词
- `generateLynxConversionPrompt`: 生成Lynx转换提示词
- `generatePremiumVisualPrompt`: 生成高级视觉效果提示词
- `generateIntelligentPrompt`: 综合分析用户意图生成智能提示词

## RPC服务功能

### WebRPCCore
- `sendMessage`: 发送Web代码生成请求
- `abortRequest`: 取消正在进行的代码生成请求
- `processWebRPCStream`: 处理Web代码流式响应
- `needsContinuation`: 检测代码是否需要续传
- `handleContinuation`: 处理代码续传请求
- `createMessages`: 创建消息数组，用于API请求

### LynxRPCCore
- `sendMessage`: 发送Lynx代码生成请求
- `convertFromWeb`: 将Web代码转换为Lynx代码
- `processLynxRPCStream`: 处理Lynx代码流式响应
- `extractAndUploadFiles`: 解构和上传Lynx代码文件
- `reextractAndUploadFiles`: 重新解构和上传Lynx代码文件
- `createLynxConversionMessages`: 创建Lynx转换消息

## 存储机制

### localStorage存储项
- `WEB_CODE`：存储生成的Web代码
- `WEB_SESSION_ID`：存储Web会话ID
- `WEB_CODE_TIMESTAMP`：存储Web代码更新时间戳
- `LYNX_CODE`：存储生成的Lynx代码
- `LYNX_SESSION_ID`：存储Lynx会话ID
- `LYNX_PLAYGROUND_URL`：存储Lynx预览URL
- `LYNX_CODE_TIMESTAMP`：存储Lynx代码更新时间戳

### 存储原则
- **严格写入原则**：只在以下情况写入localStorage:
  - 代码传输完全结束(isComplete=true)
  - 用户手动编辑代码并保存
  - 组件卸载且有未保存的完整代码
- **禁止流式写入**：严禁在流式传输过程中写入localStorage
- **清理存储**：保存前清理代码，确保只保存HTML标签内的内容
- **多级恢复策略**：支持数据恢复和回滚机制

## 错误处理和用户反馈

### 错误处理
- 使用try-catch捕获并记录错误
- 在UI中显示友好的错误提示
- 提供重试机制恢复失败的操作
- 支持数据保护和回滚机制
- 使用版本号控制确保状态一致性

### 用户反馈
- 使用Toast通知显示操作结果
- 提供加载进度指示器和实时进度条
- 在状态变化时更新UI元素
- 支持多种状态的可视化指示
- 批量更新时显示平滑的UI过渡

## 数据保护机制

### JSON元数据保护
- SSE格式过滤：丢弃以`data:`开头的数据包
- 非JSON快速路径：直接返回明显的代码片段
- 解析失败安全处理：返回空字符串而非原始JSON
- 防止JSON元数据泄露到生成的代码中
- 批量处理连接的多个JSON对象，减少渲染次数

### 代码状态保护
- 防止代码被意外清空：检测并阻止空代码覆盖现有代码
- 防止代码大幅缩短：检测并警告可能的数据丢失
- 版本控制：使用版本号确保状态更新的一致性
- 状态隔离：Web和Lynx状态保持逻辑隔离但统一管理
- 细粒度订阅：组件只订阅真正需要的状态部分
- 批量更新优化：累积小增量更新后批量处理，减少渲染次数