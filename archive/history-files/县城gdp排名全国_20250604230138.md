# 县城gdp排名全国 - Lynx代码示例

## 查询内容
```
县城gdp排名全国
```

## 生成的Lynx代码

```
<FILES>
<FILE path="/src/index.js">
Card({
  data: {
    yearOptions: ['2023年', '2022年', '2021年', '2020年', '2019年'],
    regionOptions: ['全国', '东部地区', '中部地区', '西部地区', '东北地区'],
    sortOptions: ['GDP降序', 'GDP升序', '增速降序', '增速升序'],
    selectedYear: '2023年',
    selectedRegion: '全国',
    selectedSort: 'GDP降序',
    searchText: '',
    currentPage: 1,
    totalPages: 5,
    gdpData: [
      { rank: 1, name: '昆山市', province: '江苏省', gdp: 4651, growth: 5.2, industry: '电子信息、装备制造' },
      { rank: 2, name: '江阴市', province: '江苏省', gdp: 4308, growth: 4.8, industry: '钢铁、纺织、装备制造' },
      { rank: 3, name: '张家港市', province: '江苏省', gdp: 3350, growth: 4.5, industry: '钢铁、装备制造、港口物流' },
      { rank: 4, name: '常熟市', province: '江苏省', gdp: 3102, growth: 5.0, industry: '电子信息、汽车零部件' },
      { rank: 5, name: '义乌市', province: '浙江省', gdp: 1923, growth: 6.2, industry: '小商品贸易、电子商务' },
      { rank: 6, name: '慈溪市', province: '浙江省', gdp: 1856, growth: 5.5, industry: '家电制造、电子信息' },
      { rank: 7, name: '宜兴市', province: '江苏省', gdp: 1762, growth: 4.3, industry: '环保产业、陶瓷制造' },
      { rank: 8, name: '晋江市', province: '福建省', gdp: 1705, growth: 5.8, industry: '鞋服制造、体育用品' },
      { rank: 9, name: '余姚市', province: '浙江省', gdp: 1580, growth: 4.9, industry: '塑料制品、模具制造' },
      { rank: 10, name: '诸暨市', province: '浙江省', gdp: 1445, growth: 5.3, industry: '袜业、珍珠、轴承' }
    ],
    canvas: null,
    context: null
  },
  
  onLoad() {
    this.setupCanvas();
  },
  
  // 设置Canvas
  setupCanvas() {
    try {
      // 创建Canvas元素
      const canvas = lynx.krypton.createCanvasNG();
      
      // 监听resize事件
      canvas.addEventListener('resize', ({width, height}) => {
        canvas.width = width;
        canvas.height = height;
        
        // 获取绘图上下文
        const context = canvas.getContext('2d');
        
        this.setData({
          canvas,
          context
        });
        
        // 绘制图表
        this.renderChart();
      });
      
      // 绑定到Canvas视图
      canvas.attachToCanvasView('gdp-chart');
    } catch (error) {
      console.error('Canvas创建失败:', error);
    }
  },
  
  // 年份选择变化
  onYearChange(event) {
    this.setData({
      selectedYear: event.detail.value
    });
    this.searchData();
  },
  
  // 地区选择变化
  onRegionChange(event) {
    this.setData({
      selectedRegion: event.detail.value
    });
    this.searchData();
  },
  
  // 排序选择变化
  onSortChange(event) {
    this.setData({
      selectedSort: event.detail.value
    });
    this.searchData();
  },
  
  // 搜索文本变化
  onSearchInput(event) {
    this.setData({
      searchText: event.detail.value
    });
  },
  
  // 点击搜索按钮
  onSearch() {
    this.searchData();
    // 触发轻微振动反馈
    lynx.vibrateShort();
  },
  
  // 模拟搜索数据
  searchData() {
    // 实际应用中这里应该发送请求到服务器获取数据
    console.log('搜索条件:', {
      year: this.data.selectedYear,
      region: this.data.selectedRegion,
      sort: this.data.selectedSort,
      search: this.data.searchText
    });
    
    // 模拟请求成功后的数据更新
    setTimeout(() => {
      // 这里只是模拟，实际应用中应该处理返回的数据
      this.renderChart();
    }, 300);
  },
  
  // 页码切换
  onPageChange(event) {
    const page = parseInt(event.currentTarget.dataset.page);
    
    if (page < 1 || page > this.data.totalPages) {
      return;
    }
    
    this.setData({
      currentPage: page
    });
    
    // 模拟加载数据
    this.searchData();
    
    // 触发轻微振动反馈
    lynx.vibrateShort();
  },
  
  // 绘制图表
  renderChart() {
    const { context, canvas } = this.data;
    if (!context || !canvas) return;
    
    const width = canvas.width;
    const height = canvas.height;
    const padding = { top: 40, right: 20, bottom: 60, left: 50 };
    const chartWidth = width - padding.left - padding.right;
    const chartHeight = height - padding.top - padding.bottom;
    
    // 清空画布
    context.clearRect(0, 0, width, height);
    
    // 绘制标题
    context.font = '16px PingFang SC';
    context.fillStyle = '#333333';
    context.textAlign = 'center';
    context.fillText('GDP前十县市对比图 (单位: 亿元)', width / 2, 25);
    
    // 获取最大GDP值用于比例计算
    const maxGdp = Math.max(...this.data.gdpData.map(item => item.gdp));
    const scale = chartHeight / maxGdp;
    
    // 绘制Y轴
    context.beginPath();
    context.moveTo(padding.left, padding.top);
    context.lineTo(padding.left, height - padding.bottom);
    context.strokeStyle = '#d9d9d9';
    context.stroke();
    
    // 绘制Y轴刻度
    const yTickCount = 5;
    for (let i = 0; i <= yTickCount; i++) {
      const y = padding.top + chartHeight - (i / yTickCount) * chartHeight;
      const value = Math.round((i / yTickCount) * maxGdp);
      
      context.beginPath();
      context.moveTo(padding.left - 5, y);
      context.lineTo(padding.left, y);
      context.strokeStyle = '#d9d9d9';
      context.stroke();
      
      context.font = '12px PingFang SC';
      context.fillStyle = '#666666';
      context.textAlign = 'right';
      context.fillText(value.toString(), padding.left - 10, y + 4);
    }
    
    // 绘制X轴
    context.beginPath();
    context.moveTo(padding.left, height - padding.bottom);
    context.lineTo(width - padding.right, height - padding.bottom);
    context.strokeStyle = '#d9d9d9';
    context.stroke();
    
    // 计算柱状图宽度和间距
    const barCount = this.data.gdpData.length;
    const barWidth = chartWidth / barCount / 2;
    const barSpacing = chartWidth / barCount - barWidth;
    
    // 绘制柱状图和X轴标签
    this.data.gdpData.forEach((item, index) => {
      const x = padding.left + index * (barWidth + barSpacing) + barSpacing / 2;
      const barHeight = item.gdp * scale;
      const y = height - padding.bottom - barHeight;
      
      // 绘制柱状图
      const gradient = context.createLinearGradient(x, y, x, height - padding.bottom);
      gradient.addColorStop(0, '#69c0ff');
      gradient.addColorStop(1, '#1890ff');
      
      context.fillStyle = gradient;
      context.fillRect(x, y, barWidth, barHeight);
      
      // 绘制数值
      context.font = '10px PingFang SC';
      context.fillStyle = '#333333';
      context.textAlign = 'center';
      context.fillText(item.gdp.toString(), x + barWidth / 2, y - 5);
      
      // 绘制X轴标签
      context.font = '10px PingFang SC';
      context.fillStyle = '#666666';
      context.textAlign = 'center';
      context.fillText(item.name, x + barWidth / 2, height - padding.bottom + 15);
      
      // 绘制排名
      context.font = '10px PingFang SC';
      context.fillStyle = '#999999';
      context.textAlign = 'center';
      context.fillText(`#${item.rank}`, x + barWidth / 2, height - padding.bottom + 30);
    });
  },
  
  onUnload() {
    // 解绑Canvas
    if (this.data.canvas) {
      this.data.canvas.detachFromCanvasView();
    }
  }
})
</FILE>

<FILE path="/src/index.ttml">
<view class="container">
  <view class="header">
    <text class="title">县城GDP排名全国</text>
    <text class="subtitle">数据来源：国家统计局 | 最新更新：2023年</text>
  </view>
  
  <view class="filter-section">
    <view class="filter-row">
      <view class="filter-item">
        <text class="filter-label">年份：</text>
        <picker mode="selector" range="{{yearOptions}}" value="0" bindchange="onYearChange">
          <view class="picker-value">{{selectedYear}}</view>
        </picker>
      </view>
      
      <view class="filter-item">
        <text class="filter-label">地区：</text>
        <picker mode="selector" range="{{regionOptions}}" value="0" bindchange="onRegionChange">
          <view class="picker-value">{{selectedRegion}}</view>
        </picker>
      </view>
    </view>
    
    <view class="filter-row">
      <view class="filter-item">
        <text class="filter-label">排序：</text>
        <picker mode="selector" range="{{sortOptions}}" value="0" bindchange="onSortChange">
          <view class="picker-value">{{selectedSort}}</view>
        </picker>
      </view>
      
      <view class="filter-item search-box">
        <input type="text" placeholder="县名/省份" bindinput="onSearchInput" value="{{searchText}}" class="search-input" />
        <view class="search-btn" bindtap="onSearch">查询</view>
      </view>
    </view>
  </view>
  
  <view class="data-summary">
    <text class="summary-title">数据概览</text>
    <text class="summary-content">2023年全国县域经济总量前10强县市GDP总和达到15,682亿元，占全国GDP的比重约为12.3%。其中，江苏省昆山市以4,651亿元的GDP位列全国县级城市首位，同比增长5.2%。</text>
  </view>
  
  <view class="table-container">
    <view class="table-header">
      <view class="th rank">排名</view>
      <view class="th name">县市名称</view>
      <view class="th province">所属省份</view>
      <view class="th gdp">GDP (亿元)</view>
      <view class="th growth">同比增长</view>
    </view>
    
    <scroll-view scroll-y="true" class="table-body">
      <block tt:for="{{gdpData}}" tt:key="rank">
        <view class="table-row">
          <view class="td rank {{item.rank <= 3 ? 'top-rank' : ''}}">{{item.rank}}</view>
          <view class="td name">{{item.name}}</view>
          <view class="td province">{{item.province}}</view>
          <view class="td gdp">{{item.gdp}}</view>
          <view class="td growth {{item.growth >= 0 ? 'positive' : 'negative'}}">+{{item.growth}}%</view>
        </view>
      </block>
    </scroll-view>
  </view>
  
  <view class="pagination">
    <view class="page-btn {{currentPage === 1 ? 'disabled' : ''}}" bindtap="onPageChange" data-page="{{currentPage - 1}}">上一页</view>
    <block tt:for="{{totalPages}}" tt:key="*this">
      <view class="page-btn {{currentPage === index + 1 ? 'active' : ''}}" bindtap="onPageChange" data-page="{{index + 1}}">{{index + 1}}</view>
    </block>
    <view class="page-btn {{currentPage === totalPages ? 'disabled' : ''}}" bindtap="onPageChange" data-page="{{currentPage + 1}}">下一页</view>
  </view>
  
  <view class="chart-section">
    <text class="chart-title">GDP前十县市对比图</text>
    <view class="chart-container">
      <canvas name="gdp-chart" style="width: 100%; height: 400px;" block-native-event="{{true}}"></canvas>
    </view>
    <text class="data-source">数据来源：国家统计局、各地统计局官方网站</text>
  </view>
</view>
</FILE>

<FILE path="/src/index.ttss">
.container {
  background-color: #f5f7fa;
  flex-direction: column;
  padding: 0 0 30px 0;
}

.header {
  background: linear-gradient(135deg, #24C6DC, #514A9D);
  padding: 25px 20px;
  flex-direction: column;
  align-items: center;
}

.title {
  color: #ffffff;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
  text-align: center;
}

.subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  text-align: center;
}

.filter-section {
  background-color: #ffffff;
  padding: 15px;
  flex-direction: column;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.filter-row {
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 10px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-item {
  flex-direction: row;
  align-items: center;
  flex: 1;
}

.filter-label {
  color: #666666;
  font-size: 14px;
  margin-right: 8px;
}

.picker-value {
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  color: #333333;
  border: 1px solid #e8e8e8;
  min-width: 80px;
}

.search-box {
  flex-direction: row;
  align-items: center;
}

.search-input {
  flex: 1;
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  color: #333333;
  border: 1px solid #e8e8e8;
  margin-right: 10px;
}

.search-btn {
  background-color: #4a6cf7;
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
}

.data-summary {
  background-color: #f0f7ff;
  padding: 15px;
  margin: 0 15px 15px;
  border-radius: 6px;
  flex-direction: column;
  border-left: 4px solid #4a6cf7;
}

.summary-title {
  font-weight: bold;
  font-size: 14px;
  color: #333333;
  margin-bottom: 5px;
}

.summary-content {
  font-size: 12px;
  color: #666666;
  line-height: 1.5;
}

.table-container {
  margin: 0 15px;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  flex-direction: column;
  margin-bottom: 15px;
}

.table-header {
  flex-direction: row;
  background-color: #f9f9f9;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.th {
  font-weight: bold;
  color: #333333;
  font-size: 14px;
  text-align: center;
}

.table-body {
  height: 350px;
}

.table-row {
  flex-direction: row;
  border-bottom: 1px solid #eee;
  padding: 12px 0;
}

.td {
  font-size: 14px;
  color: #333333;
  text-align: center;
}

.rank {
  width: 60px;
}

.name {
  flex: 1.5;
}

.province {
  flex: 1.5;
}

.gdp {
  flex: 1;
}

.growth {
  flex: 1;
}

.top-rank {
  color: #ff4d4f;
  font-weight: bold;
}

.positive {
  color: #52c41a;
}

.negative {
  color: #ff4d4f;
}

.pagination {
  flex-direction: row;
  justify-content: center;
  margin: 15px 0;
}

.page-btn {
  padding: 6px 12px;
  margin: 0 3px;
  border: 1px solid #d9d9d9;
  background-color: #ffffff;
  border-radius: 4px;
  font-size: 14px;
  color: #333333;
}

.page-btn.active {
  background-color: #4a6cf7;
  color: #ffffff;
  border-color: #4a6cf7;
}

.page-btn.disabled {
  color: #d9d9d9;
  cursor: not-allowed;
}

.chart-section {
  margin: 0 15px;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 15px;
  flex-direction: column;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15px;
}

.chart-container {
  height: 400px;
  border: 1px solid #eee;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 10px;
}

.data-source {
  font-size: 12px;
  color: #999999;
  text-align: right;
}
</FILE>

<FILE path="/src/index.json">
{
  "component": true,
  "usingComponents": {}
}
</FILE>

<FILE path="/src/package.json">
{
  "name": "county-gdp-ranking",
  "version": "1.0.0",
  "description": "全国县城GDP排名数据展示",
  "main": "index.js",
  "dependencies": {}
}
</FILE>
</FILES>