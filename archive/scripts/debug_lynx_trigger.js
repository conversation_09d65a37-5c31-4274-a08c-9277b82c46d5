// 通过控制台在实际的批处理器中触发一个 Lynx 转换任务的脚本

// 模拟提交一个简单的 Lynx 任务
const triggerLynxConversion = () => {
  console.log('🚀 [DEBUG] 准备触发 Lynx 转换任务');

  // 查找批处理器的输入元素
  const queryInput =
    document.querySelector('textarea[placeholder*="请输入"]') ||
    document.querySelector('textarea') ||
    document.querySelector('input[type="text"]');

  if (queryInput) {
    console.log('📝 [DEBUG] 找到输入框，准备输入测试查询');

    // 输入一个简单的 Lynx 代码生成请求
    const testQuery = `创建一个简单的移动端卡片布局，包含：
1. 一个标题文字
2. 一张图片
3. 一个描述文本
4. 一个按钮

使用 Lynx 语法实现，包含基本的样式`;

    // 模拟用户输入
    queryInput.value = testQuery;
    queryInput.dispatchEvent(new Event('input', { bubbles: true }));
    queryInput.dispatchEvent(new Event('change', { bubbles: true }));

    console.log('✅ [DEBUG] 已输入测试查询');

    // 查找提交按钮
    const submitButton =
      document.querySelector('button[type="submit"]') ||
      document.querySelector('button:contains("提交")') ||
      document.querySelector('button:contains("开始")') ||
      document.querySelector('.ant-btn-primary') ||
      document
        .querySelectorAll('button')
        .find(
          btn =>
            btn.textContent.includes('提交') ||
            btn.textContent.includes('开始') ||
            btn.textContent.includes('执行'),
        );

    if (submitButton) {
      console.log('🎯 [DEBUG] 找到提交按钮，准备提交任务');

      // 模拟点击提交
      setTimeout(() => {
        submitButton.click();
        console.log('✅ [DEBUG] 已提交任务，开始监控转换日志');

        // 开始监控控制台日志
        monitorConversionLogs();
      }, 1000);
    } else {
      console.log('❌ [DEBUG] 未找到提交按钮，列出所有可用按钮:');
      document.querySelectorAll('button').forEach((btn, index) => {
        console.log(
          `  ${index + 1}. "${btn.textContent}" (class: ${btn.className})`,
        );
      });
    }
  } else {
    console.log('❌ [DEBUG] 未找到输入框，检查页面元素:');
    console.log(
      '- textarea 数量:',
      document.querySelectorAll('textarea').length,
    );
    console.log('- input 数量:', document.querySelectorAll('input').length);
    console.log(
      '- 可能的输入元素:',
      Array.from(document.querySelectorAll('textarea, input')).map(el => ({
        tag: el.tagName,
        type: el.type,
        placeholder: el.placeholder,
        id: el.id,
        className: el.className,
      })),
    );
  }
};

// 监控转换日志
const monitorConversionLogs = () => {
  console.log('👀 [DEBUG] 开始监控转换相关的控制台日志...');

  // 覆盖 console.log 来捕获转换日志
  const originalLog = console.log;
  const originalError = console.error;

  console.log = function (...args) {
    const message = args.join(' ');

    // 检查是否是转换相关的日志
    if (
      message.includes('InteractiveIframe') ||
      message.includes('Parse5') ||
      message.includes('转换') ||
      message.includes('TTML') ||
      message.includes('DEBUG')
    ) {
      originalLog('🔍 [MONITOR]', ...args);
    } else {
      originalLog(...args);
    }
  };

  console.error = function (...args) {
    const message = args.join(' ');

    // 总是显示错误日志
    originalError('🚨 [MONITOR ERROR]', ...args);
  };

  // 5分钟后恢复原始日志函数
  setTimeout(
    () => {
      console.log = originalLog;
      console.error = originalError;
      console.log('📋 [DEBUG] 日志监控已停止');
    },
    5 * 60 * 1000,
  );
};

// 检查当前页面是否是批处理器
const checkCurrentPage = () => {
  const url = window.location.href;
  const isBatchProcessor = url.includes('batch_processor');

  console.log('🔍 [DEBUG] 当前页面检查:');
  console.log('  - URL:', url);
  console.log('  - 是批处理器页面:', isBatchProcessor);

  if (!isBatchProcessor) {
    console.log('⚠️ [DEBUG] 当前不在批处理器页面，请导航到 /batch_processor');
    return false;
  }

  return true;
};

// 主执行函数
const main = () => {
  console.log('🎯 [DEBUG] Lynx 转换调试脚本启动');

  if (!checkCurrentPage()) {
    return;
  }

  // 等待页面完全加载
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', triggerLynxConversion);
  } else {
    triggerLynxConversion();
  }
};

// 启动调试脚本
main();

// 导出函数供手动调用
window.debugLynxConversion = {
  trigger: triggerLynxConversion,
  monitor: monitorConversionLogs,
  check: checkCurrentPage,
};
