<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进度条调试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: #fafbfc;
        }
        
        .debug-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #1f2937;
        }
        
        .progress-test {
            margin-bottom: 20px;
        }
        
        .progress-label {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 8px;
            color: #374151;
        }
        
        /* 基础进度条样式 - 确保没有冲突 */
        .pastel-progress-container {
            width: 100%;
            height: 6px;
            background: rgba(229, 231, 235, 0.4);
            border-radius: 3px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
            border: 1px solid #ccc; /* 调试边框 */
        }

        .pastel-progress-bar {
            height: 100%;
            border-radius: 3px;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            min-width: 2px;
            border: 1px solid #000; /* 调试边框 */
        }

        /* 强制样式 - 使用最高优先级 */
        .debug-container .pastel-progress-container .pastel-progress-bar.progress-default {
            background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%) !important;
            box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3) !important;
        }

        .debug-container .pastel-progress-container .pastel-progress-bar.progress-mixed {
            background: linear-gradient(90deg, #10b981 0%, #f59e0b 50%, #ef4444 100%) !important;
            box-shadow: 0 1px 3px rgba(245, 158, 11, 0.3) !important;
        }

        .debug-container .pastel-progress-container .pastel-progress-bar.progress-success {
            background: linear-gradient(90deg, #10b981 0%, #059669 100%) !important;
            box-shadow: 0 1px 3px rgba(16, 185, 129, 0.3) !important;
        }

        .debug-container .pastel-progress-container .pastel-progress-bar.progress-error {
            background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%) !important;
            box-shadow: 0 1px 3px rgba(239, 68, 68, 0.3) !important;
        }
        
        .debug-info {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 5px;
            font-family: monospace;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 style="text-align: center; color: #1f2937; margin-bottom: 30px;">进度条调试页面</h1>
        
        <div class="debug-section">
            <div class="debug-title">1. 基础测试 - 静态进度条</div>
            <div class="progress-test">
                <div class="progress-label">默认进度条 (蓝色)</div>
                <div class="pastel-progress-container">
                    <div class="pastel-progress-bar progress-default" style="width: 75%;"></div>
                </div>
                <div class="debug-info">类名: pastel-progress-bar progress-default | 宽度: 75%</div>
            </div>
            
            <div class="progress-test">
                <div class="progress-label">混合进度条 (多色)</div>
                <div class="pastel-progress-container">
                    <div class="pastel-progress-bar progress-mixed" style="width: 58%;"></div>
                </div>
                <div class="debug-info">类名: pastel-progress-bar progress-mixed | 宽度: 58%</div>
            </div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">2. 动态测试 - 可调节进度条</div>
            <div class="progress-test">
                <div class="progress-label">成功率进度条</div>
                <div>
                    <button class="test-button" onclick="setProgress('success', 25)">25%</button>
                    <button class="test-button" onclick="setProgress('success', 50)">50%</button>
                    <button class="test-button" onclick="setProgress('success', 75)">75%</button>
                    <button class="test-button" onclick="setProgress('success', 100)">100%</button>
                </div>
                <div class="pastel-progress-container">
                    <div id="success-bar" class="pastel-progress-bar progress-success" style="width: 50%;"></div>
                </div>
                <div class="debug-info">当前宽度: <span id="success-width">50%</span></div>
            </div>
            
            <div class="progress-test">
                <div class="progress-label">总进度条</div>
                <div>
                    <button class="test-button" onclick="setProgress('default', 25)">25%</button>
                    <button class="test-button" onclick="setProgress('default', 50)">50%</button>
                    <button class="test-button" onclick="setProgress('default', 75)">75%</button>
                    <button class="test-button" onclick="setProgress('default', 100)">100%</button>
                </div>
                <div class="pastel-progress-container">
                    <div id="default-bar" class="pastel-progress-bar progress-default" style="width: 75%;"></div>
                </div>
                <div class="debug-info">当前宽度: <span id="default-width">75%</span></div>
            </div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">3. 样式检查</div>
            <div class="progress-test">
                <div class="progress-label">样式检查工具</div>
                <button class="test-button" onclick="checkStyles()">检查计算样式</button>
                <button class="test-button" onclick="logElements()">输出元素信息</button>
                <div id="style-output" style="margin-top: 10px; padding: 10px; background: #f3f4f6; border-radius: 4px; font-family: monospace; font-size: 12px; white-space: pre-wrap;"></div>
            </div>
        </div>
    </div>

    <script>
        function setProgress(type, percentage) {
            const bar = document.getElementById(type + '-bar');
            const widthSpan = document.getElementById(type + '-width');
            if (bar && widthSpan) {
                bar.style.width = percentage + '%';
                widthSpan.textContent = percentage + '%';
            }
        }
        
        function checkStyles() {
            const output = document.getElementById('style-output');
            const bars = document.querySelectorAll('.pastel-progress-bar');
            let result = '样式检查结果:\n\n';
            
            bars.forEach((bar, index) => {
                const computedStyle = window.getComputedStyle(bar);
                result += `进度条 ${index + 1}:\n`;
                result += `  类名: ${bar.className}\n`;
                result += `  背景: ${computedStyle.background}\n`;
                result += `  背景图片: ${computedStyle.backgroundImage}\n`;
                result += `  宽度: ${computedStyle.width}\n`;
                result += `  高度: ${computedStyle.height}\n`;
                result += `  阴影: ${computedStyle.boxShadow}\n`;
                result += '\n';
            });
            
            output.textContent = result;
        }
        
        function logElements() {
            const output = document.getElementById('style-output');
            const containers = document.querySelectorAll('.pastel-progress-container');
            let result = '元素结构信息:\n\n';
            
            containers.forEach((container, index) => {
                result += `容器 ${index + 1}:\n`;
                result += `  HTML: ${container.outerHTML.substring(0, 200)}...\n`;
                const bar = container.querySelector('.pastel-progress-bar');
                if (bar) {
                    result += `  进度条类名: ${bar.className}\n`;
                    result += `  进度条样式: ${bar.style.cssText}\n`;
                }
                result += '\n';
            });
            
            output.textContent = result;
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', function() {
            setTimeout(checkStyles, 500);
        });
    </script>
</body>
</html>
