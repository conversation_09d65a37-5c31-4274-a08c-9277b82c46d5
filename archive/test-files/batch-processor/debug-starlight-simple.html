<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星光效果诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: #f0f0f0;
        }
        
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        /* 直接复制主按钮的样式 */
        .test-btn-primary {
            /* 🎨 浅金色渐变 - 明亮专业 */
            background: linear-gradient(135deg,
                #fefce8 0%,     /* 极浅奶黄 */
                #fef3c7 20%,    /* 浅黄色 */
                #fde68a 45%,    /* 金黄色 */
                #fcd34d 70%,    /* 中等金色 */
                #f59e0b 100%    /* 温和橙金 */
            ) !important;
            color: #92400e !important;

            /* 📏 尺寸和间距 */
            padding: 14px 24px !important;
            min-height: 48px !important;
            border-radius: 10px !important;
            font-size: 15px !important;
            font-weight: 600 !important;

            /* 🌟 优化的阴影效果 */
            box-shadow: 
                0 4px 16px rgba(251, 191, 36, 0.2),
                0 2px 8px rgba(253, 230, 138, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;

            /* ✨ 文字阴影优化 */
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5) !important;

            /* 🎯 布局和对齐 */
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 8px !important;

            /* 🎭 过渡动画 */
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

            /* 🔧 层级和溢出 */
            position: relative !important;
            z-index: 10 !important;
            border: none !important;
            overflow: hidden !important;
            
            margin: 20px;
            cursor: pointer;
        }
        
        /* 星光效果 */
        .test-btn-primary::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background:
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.9) 1px, rgba(255, 248, 220, 0.6) 3px, transparent 5px),
                radial-gradient(circle at 70% 20%, rgba(255, 248, 220, 0.8) 1px, rgba(255, 255, 255, 0.5) 3px, transparent 5px),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.7) 1px, rgba(255, 248, 220, 0.4) 3px, transparent 5px),
                radial-gradient(circle at 30% 80%, rgba(255, 248, 220, 0.6) 1px, rgba(255, 255, 255, 0.3) 3px, transparent 5px) !important;
            background-size: 100% 100% !important;
            animation: gentleStarTwinkle 4s ease-in-out infinite !important;
            pointer-events: none !important;
            z-index: 1 !important;
            border-radius: inherit !important;
        }
        
        /* 高光扫过效果 */
        .test-btn-primary::after {
            content: '' !important;
            position: absolute !important;
            top: -50% !important;
            left: -50% !important;
            width: 200% !important;
            height: 200% !important;
            background: linear-gradient(45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.4) 50%,
                transparent 70%) !important;
            transform: translateX(-100%) translateY(-100%) rotate(45deg) !important;
            transition: transform 0.8s ease !important;
            pointer-events: none !important;
            z-index: 2 !important;
        }
        
        /* 悬停时的高光扫过 */
        .test-btn-primary:hover::after {
            transform: translateX(100%) translateY(100%) rotate(45deg) !important;
        }
        
        /* 确保按钮内容在特效之上 */
        .test-btn-primary > * {
            position: relative !important;
            z-index: 3 !important;
        }
        
        /* 星光闪烁动画 */
        @keyframes gentleStarTwinkle {
            0%, 100% {
                opacity: 0.6;
                transform: scale(1);
            }
            50% {
                opacity: 0.9;
                transform: scale(1.05);
            }
        }
        
        /* 使用原始CSS选择器测试 */
        .batch-processor-layout .btn-primary {
            background: linear-gradient(135deg,
                #fefce8 0%,
                #fef3c7 20%,
                #fde68a 45%,
                #fcd34d 70%,
                #f59e0b 100%
            ) !important;
            color: #92400e !important;
            padding: 14px 24px !important;
            min-height: 48px !important;
            border-radius: 10px !important;
            font-size: 15px !important;
            font-weight: 600 !important;
            box-shadow: 
                0 4px 16px rgba(251, 191, 36, 0.2),
                0 2px 8px rgba(253, 230, 138, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5) !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 8px !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative !important;
            z-index: 10 !important;
            border: none !important;
            overflow: hidden !important;
            margin: 20px;
            cursor: pointer;
        }
        
        .batch-processor-layout .btn-primary::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background:
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.9) 1px, rgba(255, 248, 220, 0.6) 3px, transparent 5px),
                radial-gradient(circle at 70% 20%, rgba(255, 248, 220, 0.8) 1px, rgba(255, 255, 255, 0.5) 3px, transparent 5px),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.7) 1px, rgba(255, 248, 220, 0.4) 3px, transparent 5px),
                radial-gradient(circle at 30% 80%, rgba(255, 248, 220, 0.6) 1px, rgba(255, 255, 255, 0.3) 3px, transparent 5px) !important;
            background-size: 100% 100% !important;
            animation: gentleStarTwinkle 4s ease-in-out infinite !important;
            pointer-events: none !important;
            z-index: 1 !important;
            border-radius: inherit !important;
        }
        
        .batch-processor-layout .btn-primary::after {
            content: '' !important;
            position: absolute !important;
            top: -50% !important;
            left: -50% !important;
            width: 200% !important;
            height: 200% !important;
            background: linear-gradient(45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.4) 50%,
                transparent 70%) !important;
            transform: translateX(-100%) translateY(-100%) rotate(45deg) !important;
            transition: transform 0.8s ease !important;
            pointer-events: none !important;
            z-index: 2 !important;
        }
        
        .batch-processor-layout .btn-primary:hover::after {
            transform: translateX(100%) translateY(100%) rotate(45deg) !important;
        }
        
        .batch-processor-layout .btn-primary > * {
            position: relative !important;
            z-index: 3 !important;
        }
        
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 style="text-align: center; color: #333; margin-bottom: 30px;">
            🔍 星光效果诊断工具
        </h1>
        
        <div class="test-section">
            <div class="test-title">🧪 测试1: 独立CSS类</div>
            <p>使用独立的CSS类，应该显示星光效果：</p>
            
            <div style="text-align: center;">
                <button class="test-btn-primary">
                    <span>⚡ 独立测试按钮</span>
                </button>
            </div>
            
            <div class="debug-info">
                <strong>预期：</strong>应该看到星光闪烁效果和悬停时的高光扫过
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🧪 测试2: 原始选择器</div>
            <p>使用原始的 .batch-processor-layout .btn-primary 选择器：</p>
            
            <div class="batch-processor-layout" style="text-align: center;">
                <button class="btn-primary">
                    <span>⚡ 原始选择器测试</span>
                </button>
            </div>
            
            <div class="debug-info">
                <strong>预期：</strong>应该看到与独立测试相同的效果
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🔧 诊断信息</div>
            
            <div class="debug-info">
                <strong>检查项目：</strong><br>
                1. 星光效果 ::before 伪元素是否生效<br>
                2. 高光扫过 ::after 伪元素是否工作<br>
                3. 动画 gentleStarTwinkle 是否运行<br>
                4. z-index 层级是否正确<br>
                5. overflow: hidden 是否设置<br><br>
                
                <strong>如果测试1有效果但测试2没有：</strong><br>
                说明原始CSS文件中有其他规则覆盖了星光效果<br><br>
                
                <strong>如果两个测试都没有效果：</strong><br>
                说明浏览器不支持或CSS语法有问题
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px; padding: 20px; background: #e9ecef; border-radius: 8px;">
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
                请观察两个按钮的效果差异，这将帮助我们定位问题所在。
            </p>
        </div>
    </div>
</body>
</html>
