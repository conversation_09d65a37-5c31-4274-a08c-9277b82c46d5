<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星光效果调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: #f0f0f0;
        }
        
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .test-button {
            position: relative;
            overflow: hidden;
            padding: 14px 24px;
            min-height: 48px;
            border-radius: 10px;
            font-size: 15px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            margin: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            
            /* 金色渐变背景 */
            background: linear-gradient(135deg, 
                #fef3c7 0%,
                #fde68a 25%,
                #fcd34d 50%,
                #f59e0b 75%,
                #d97706 100%
            );
            color: #92400e;
            
            /* 阴影效果 */
            box-shadow: 0 4px 16px rgba(251, 191, 36, 0.25), 
                        0 2px 8px rgba(253, 230, 138, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.6);
            
            /* 过渡动画 */
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* 星光效果 */
        .test-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.9) 1px, rgba(255, 248, 220, 0.6) 3px, transparent 5px),
                radial-gradient(circle at 70% 20%, rgba(255, 248, 220, 0.8) 1px, rgba(255, 255, 255, 0.5) 3px, transparent 5px),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.7) 1px, rgba(255, 248, 220, 0.4) 3px, transparent 5px),
                radial-gradient(circle at 30% 80%, rgba(255, 248, 220, 0.6) 1px, rgba(255, 255, 255, 0.3) 3px, transparent 5px);
            background-size: 100% 100%;
            animation: gentleStarTwinkle 4s ease-in-out infinite;
            pointer-events: none;
            z-index: 1;
            border-radius: inherit;
        }
        
        /* 高光扫过效果 */
        .test-button::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.4) 50%,
                transparent 70%);
            transform: translateX(-100%) translateY(-100%) rotate(45deg);
            transition: transform 0.8s ease;
            pointer-events: none;
            z-index: 2;
        }
        
        /* 悬停时的高光扫过 */
        .test-button:hover::after {
            transform: translateX(100%) translateY(100%) rotate(45deg);
        }
        
        /* 确保按钮内容在特效之上 */
        .test-button > * {
            position: relative;
            z-index: 3;
        }
        
        /* 星光闪烁动画 */
        @keyframes gentleStarTwinkle {
            0%, 100% {
                opacity: 0.6;
                transform: scale(1);
            }
            50% {
                opacity: 0.9;
                transform: scale(1.05);
            }
        }
        
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-good { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 style="text-align: center; color: #333; margin-bottom: 30px;">
            🔍 星光效果调试页面
        </h1>
        
        <div class="test-section">
            <div class="test-title">🧪 基础测试按钮</div>
            <p>这个按钮使用内联CSS，应该显示完整的星光和高光效果：</p>
            
            <div style="text-align: center;">
                <button class="test-button">
                    <span>⚡ 测试按钮</span>
                </button>
            </div>
            
            <div class="debug-info">
                <strong>预期效果：</strong><br>
                1. 金色渐变背景 ✓<br>
                2. 微妙的星光点闪烁 (4秒循环)<br>
                3. 悬停时白色高光从左上扫向右下<br>
                4. 平滑的过渡动画
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">📊 效果检查清单</div>
            
            <div style="margin: 15px 0;">
                <span class="status-indicator status-good"></span>
                <strong>背景渐变：</strong> 金色渐变应该从浅到深
            </div>
            
            <div style="margin: 15px 0;">
                <span class="status-indicator status-warning"></span>
                <strong>星光效果：</strong> 按钮表面应该有微妙的白色/金色光点闪烁
            </div>
            
            <div style="margin: 15px 0;">
                <span class="status-indicator status-warning"></span>
                <strong>高光扫过：</strong> 悬停时应该有白色高光从左上角扫过
            </div>
            
            <div style="margin: 15px 0;">
                <span class="status-indicator status-good"></span>
                <strong>文字可读性：</strong> 按钮文字应该清晰可见
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🔧 技术细节</div>
            
            <div class="debug-info">
                <strong>星光实现 (::before):</strong><br>
                • 使用 4 个径向渐变创建光点<br>
                • 动画：gentleStarTwinkle 4s ease-in-out infinite<br>
                • z-index: 1 (在背景之上，内容之下)<br><br>
                
                <strong>高光扫过 (::after):</strong><br>
                • 45度线性渐变，中间半透明白色<br>
                • 初始位置：左上角外侧<br>
                • 悬停时移动到右下角外侧<br>
                • z-index: 2 (在星光之上，内容之下)
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🚨 故障排除</div>
            
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 12px; margin: 10px 0;">
                <strong>如果看不到星光效果：</strong><br>
                • 检查浏览器是否支持 CSS 动画<br>
                • 确认 ::before 伪元素是否生效<br>
                • 检查 z-index 层级关系<br>
                • 验证动画关键帧定义
            </div>
            
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 6px; padding: 12px; margin: 10px 0;">
                <strong>如果高光扫过不工作：</strong><br>
                • 检查 overflow: hidden 是否设置<br>
                • 确认 ::after 伪元素样式<br>
                • 验证 transform 动画<br>
                • 检查悬停触发器
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">📱 浏览器兼容性</div>
            
            <div style="font-size: 14px; line-height: 1.6;">
                <strong>支持的浏览器：</strong><br>
                • Chrome 26+ ✓<br>
                • Firefox 16+ ✓<br>
                • Safari 9+ ✓<br>
                • Edge 12+ ✓<br><br>
                
                <strong>注意事项：</strong><br>
                • 某些移动浏览器可能不显示动画以节省电量<br>
                • 高对比度模式下效果可能被禁用<br>
                • 减少动画设置会影响效果显示
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px; padding: 20px; background: #e9ecef; border-radius: 8px;">
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
                如果上面的测试按钮显示正常，说明星光和高光效果的CSS代码是正确的。<br>
                如果主页面的按钮没有效果，可能是CSS优先级或选择器的问题。
            </p>
        </div>
    </div>
</body>
</html>
