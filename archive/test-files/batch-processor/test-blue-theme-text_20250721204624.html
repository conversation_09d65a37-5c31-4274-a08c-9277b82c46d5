<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蓝色主题文字颜色测试</title>
    <style>
        :root {
            /* 蓝色主题文字颜色变量 */
            --text-blue-50: #f0f9ff;
            --text-blue-100: #e0f2fe;
            --text-blue-200: #bae6fd;
            --text-blue-300: #7dd3fc;
            --text-blue-400: #38bdf8;
            --text-blue-500: #0ea5e9;
            --text-blue-600: #0284c7;
            --text-blue-700: #0369a1;
            --text-blue-800: #075985;
            --text-blue-900: #0c4a6e;
            
            /* 语义化文字颜色 */
            --text-primary: var(--text-blue-800);
            --text-secondary: var(--text-blue-600);
            --text-tertiary: var(--text-blue-500);
            --text-disabled: var(--text-blue-400);
        }

        body {
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 35%, #f9fafb 65%, #f0f9ff 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            color: var(--text-primary);
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .title {
            text-align: center;
            color: var(--text-primary);
            margin-bottom: 40px;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .demo-section {
            padding: 30px;
            background: rgba(248, 250, 252, 0.8);
            border-radius: 16px;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .demo-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }

        /* 原始灰色文字样式 */
        .gray-theme {
            color: #1f2937; /* gray-800 */
        }

        .gray-theme h1 { color: #111827; } /* gray-900 */
        .gray-theme h2 { color: #1f2937; } /* gray-800 */
        .gray-theme h3 { color: #374151; } /* gray-700 */
        .gray-theme p { color: #4b5563; } /* gray-600 */
        .gray-theme .text-sm { color: #6b7280; } /* gray-500 */
        .gray-theme .text-xs { color: #9ca3af; } /* gray-400 */

        /* 蓝色主题文字样式 */
        .blue-theme {
            color: var(--text-primary);
        }

        .blue-theme h1 { color: var(--text-blue-900); }
        .blue-theme h2 { color: var(--text-blue-800); }
        .blue-theme h3 { color: var(--text-blue-700); }
        .blue-theme p { color: var(--text-blue-600); }
        .blue-theme .text-sm { color: var(--text-blue-500); }
        .blue-theme .text-xs { color: var(--text-blue-400); }

        .content-sample {
            margin-bottom: 20px;
        }

        .content-sample h1 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .content-sample h2 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .content-sample h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 6px;
        }

        .content-sample p {
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 8px;
        }

        .content-sample .text-sm {
            font-size: 0.875rem;
            margin-bottom: 4px;
        }

        .content-sample .text-xs {
            font-size: 0.75rem;
            margin-bottom: 4px;
        }

        .highlight {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border: 2px solid #3b82f6;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }

        .color-item {
            text-align: center;
            padding: 15px 10px;
            border-radius: 8px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .color-blue-50 { background: var(--text-blue-50); color: var(--text-blue-900); }
        .color-blue-100 { background: var(--text-blue-100); color: var(--text-blue-900); }
        .color-blue-200 { background: var(--text-blue-200); color: var(--text-blue-900); }
        .color-blue-300 { background: var(--text-blue-300); color: var(--text-blue-900); }
        .color-blue-400 { background: var(--text-blue-400); color: white; }
        .color-blue-500 { background: var(--text-blue-500); color: white; }
        .color-blue-600 { background: var(--text-blue-600); color: white; }
        .color-blue-700 { background: var(--text-blue-700); color: white; }
        .color-blue-800 { background: var(--text-blue-800); color: white; }
        .color-blue-900 { background: var(--text-blue-900); color: white; }

        .features {
            background: rgba(255, 255, 255, 0.7);
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
        }

        .features h4 {
            color: var(--text-primary);
            margin-bottom: 10px;
            font-weight: 600;
        }

        .features ul {
            margin: 0;
            padding-left: 20px;
            color: var(--text-secondary);
        }

        .features li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="title">🎨 蓝色主题文字颜色对比</h1>
        
        <div class="comparison-grid">
            <div class="demo-section">
                <h3 class="demo-title gray-theme">原始灰色主题</h3>
                <div class="content-sample gray-theme">
                    <h1>主标题文字</h1>
                    <h2>二级标题文字</h2>
                    <h3>三级标题文字</h3>
                    <p>这是段落文字，用于显示主要内容信息。</p>
                    <p class="text-sm">这是小号文字，用于显示次要信息。</p>
                    <p class="text-xs">这是超小号文字，用于显示辅助信息。</p>
                </div>
                <div class="features">
                    <h4>特点：</h4>
                    <ul>
                        <li>传统的灰色文字系统</li>
                        <li>对比度较低</li>
                        <li>缺乏品牌特色</li>
                        <li>视觉层次不够明显</li>
                    </ul>
                </div>
            </div>

            <div class="demo-section highlight">
                <h3 class="demo-title blue-theme">✨ 蓝色主题</h3>
                <div class="content-sample blue-theme">
                    <h1>主标题文字</h1>
                    <h2>二级标题文字</h2>
                    <h3>三级标题文字</h3>
                    <p>这是段落文字，用于显示主要内容信息。</p>
                    <p class="text-sm">这是小号文字，用于显示次要信息。</p>
                    <p class="text-xs">这是超小号文字，用于显示辅助信息。</p>
                </div>
                <div class="features">
                    <h4>优化特点：</h4>
                    <ul>
                        <li>统一的蓝色主题色调</li>
                        <li>更好的品牌一致性</li>
                        <li>清晰的视觉层次</li>
                        <li>专业的商务感</li>
                        <li>与UI主题完美融合</li>
                    </ul>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-bottom: 40px;">
            <h3 style="color: var(--text-primary); margin-bottom: 20px;">🎨 蓝色文字色彩调色板</h3>
            <div class="color-palette">
                <div class="color-item color-blue-50">blue-50<br>#f0f9ff</div>
                <div class="color-item color-blue-100">blue-100<br>#e0f2fe</div>
                <div class="color-item color-blue-200">blue-200<br>#bae6fd</div>
                <div class="color-item color-blue-300">blue-300<br>#7dd3fc</div>
                <div class="color-item color-blue-400">blue-400<br>#38bdf8</div>
                <div class="color-item color-blue-500">blue-500<br>#0ea5e9</div>
                <div class="color-item color-blue-600">blue-600<br>#0284c7</div>
                <div class="color-item color-blue-700">blue-700<br>#0369a1</div>
                <div class="color-item color-blue-800">blue-800<br>#075985</div>
                <div class="color-item color-blue-900">blue-900<br>#0c4a6e</div>
            </div>
        </div>

        <div style="text-align: center; color: var(--text-secondary); margin-top: 40px;">
            <p><strong>💡 说明：</strong> 蓝色主题文字系统提供了更好的品牌一致性和视觉层次</p>
            <p>所有文字颜色都基于蓝色色阶，与整体UI设计完美融合</p>
        </div>
    </div>
</body>
</html>
