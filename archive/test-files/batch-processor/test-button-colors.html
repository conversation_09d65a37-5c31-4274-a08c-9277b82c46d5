<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮颜色修改测试</title>
    <link href="../styles/index.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #1e293b;
            margin-bottom: 40px;
            font-size: 2.5rem;
            font-weight: 700;
        }
        
        .section {
            margin-bottom: 60px;
        }
        
        .section h2 {
            color: #334155;
            margin-bottom: 20px;
            font-size: 1.5rem;
            font-weight: 600;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .button-demo {
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            background: #f8fafc;
            text-align: center;
        }
        
        .button-demo h3 {
            margin: 0 0 15px 0;
            color: #475569;
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .demo-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 40px;
        }
        
        .before, .after {
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }
        
        .before {
            background: #fef2f2;
            border: 2px solid #fecaca;
        }
        
        .after {
            background: #f0fdf4;
            border: 2px solid #bbf7d0;
        }
        
        .before h3 {
            color: #dc2626;
        }
        
        .after h3 {
            color: #16a34a;
        }
        
        .color-info {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            font-size: 0.85rem;
            color: #64748b;
            line-height: 1.5;
        }
        
        .color-swatch {
            display: inline-block;
            width: 16px;
            height: 16px;
            border-radius: 4px;
            margin-right: 8px;
            vertical-align: middle;
            border: 1px solid #e2e8f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 按钮颜色优化测试页面</h1>
        
        <div class="section">
            <h2>✨ 新版本按钮样式（浅色渐变）</h2>
            <div class="button-grid">
                <div class="button-demo">
                    <h3>金色浅黄色渐变按钮</h3>
                    <div class="demo-buttons">
                        <button class="btn btn--primary-gold">
                            主要操作按钮
                        </button>
                        <button class="btn-authority btn-primary-gold">
                            权限按钮
                        </button>
                        <button class="btn btn--primary-gold btn--lg">
                            大尺寸按钮
                        </button>
                    </div>
                    <div class="color-info">
                        <div><span class="color-swatch" style="background: #fef3c7;"></span>浅黄色：#fef3c7</div>
                        <div><span class="color-swatch" style="background: #fde68a;"></span>中黄色：#fde68a</div>
                        <div><span class="color-swatch" style="background: #fcd34d;"></span>金黄色：#fcd34d</div>
                        <div style="margin-top: 8px;">文本颜色：深棕色 #92400e</div>
                    </div>
                </div>
                
                <div class="button-demo">
                    <h3>浅蓝色渐变按钮</h3>
                    <div class="demo-buttons">
                        <button class="btn btn--secondary-glass">
                            次要操作按钮
                        </button>
                        <button class="btn-authority btn-secondary-glass">
                            玻璃效果按钮
                        </button>
                        <button class="btn btn--secondary-glass" disabled>
                            禁用状态
                        </button>
                    </div>
                    <div class="color-info">
                        <div><span class="color-swatch" style="background: #87ceeb;"></span>天蓝色：#87ceeb</div>
                        <div><span class="color-swatch" style="background: #a3d5f0;"></span>浅蓝色：#a3d5f0</div>
                        <div><span class="color-swatch" style="background: #bde0f7;"></span>淡蓝色：#bde0f7</div>
                        <div style="margin-top: 8px;">文本颜色：深蓝色 #1e40af</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>🔄 交互效果演示</h2>
            <div class="button-grid">
                <div class="button-demo">
                    <h3>悬停效果</h3>
                    <div class="demo-buttons">
                        <button class="btn-authority btn-primary-gold sparkle-hover">
                            <span class="glow-text">悬停我试试</span>
                        </button>
                        <button class="btn-authority btn-secondary-glass">
                            悬停看渐变
                        </button>
                    </div>
                </div>
                
                <div class="button-demo">
                    <h3>动画效果</h3>
                    <div class="demo-buttons">
                        <button class="btn-authority btn-primary-gold btn-micro-bounce">
                            微弹跳效果
                        </button>
                        <button class="btn-authority btn-secondary-glass btn-breathing">
                            呼吸灯效果
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="comparison">
            <div class="before">
                <h3>🔴 修改前（深色重型）</h3>
                <div style="margin: 20px 0;">
                    <div style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 25%, #f59e0b 50%, #fbbf24 75%, #fcd34d 100%); 
                                padding: 12px 24px; border-radius: 8px; color: white; font-weight: 600; margin-bottom: 10px;">
                        深金色按钮（过重）
                    </div>
                    <div style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 25%, #1d4ed8 50%, #1e40af 75%, #1e3a8a 100%); 
                                padding: 12px 24px; border-radius: 8px; color: white; font-weight: 600;">
                        深蓝色按钮（过重）
                    </div>
                </div>
                <div class="color-info">
                    问题：颜色过深过重，视觉压迫感强，缺乏现代感
                </div>
            </div>
            
            <div class="after">
                <h3>🟢 修改后（浅色现代）</h3>
                <div style="margin: 20px 0;">
                    <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 25%, #fcd34d 50%, #fbbf24 75%, #f9e71e 100%); 
                                padding: 12px 24px; border-radius: 8px; color: #92400e; font-weight: 600; margin-bottom: 10px;">
                        浅金色按钮（清新）
                    </div>
                    <div style="background: linear-gradient(135deg, #87ceeb 0%, #a3d5f0 25%, #bde0f7 50%, #d4edfc 75%, #e6f4fd 100%); 
                                padding: 12px 24px; border-radius: 8px; color: #1e40af; font-weight: 600;">
                        浅蓝色按钮（清新）
                    </div>
                </div>
                <div class="color-info">
                    优势：颜色轻盈现代，视觉舒适，符合当前UI设计趋势
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📊 修改总结</h2>
            <div style="background: #f8fafc; padding: 30px; border-radius: 12px; border-left: 4px solid #3b82f6;">
                <h3 style="color: #1e40af; margin-top: 0;">✅ 完成的优化</h3>
                <ul style="color: #475569; line-height: 1.8;">
                    <li><strong>金色按钮</strong>：从深金色 #f59e0b → 浅黄色 #fef3c7 渐变</li>
                    <li><strong>蓝色按钮</strong>：从深蓝色 #3b82f6 → 天蓝色 #87ceeb 渐变</li>
                    <li><strong>文本颜色</strong>：调整为深色文本，提升可读性</li>
                    <li><strong>阴影效果</strong>：减轻阴影强度，更加柔和</li>
                    <li><strong>边框样式</strong>：使用浅色边框，减少视觉重量</li>
                    <li><strong>交互效果</strong>：保持原有动画，优化颜色过渡</li>
                </ul>
                <p style="margin-bottom: 0; color: #64748b; font-style: italic;">
                    所有修改已应用到 buttons.css 文件中，涵盖所有按钮变体和状态。
                </p>
            </div>
        </div>
    </div>
    
    <script>
        // 添加交互效果演示
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button:not([disabled])');
            
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 添加点击效果
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                    
                    // 显示点击反馈
                    const ripple = document.createElement('div');
                    ripple.style.cssText = `
                        position: absolute;
                        border-radius: 50%;
                        background: rgba(255, 255, 255, 0.6);
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;
                    
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
                    ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';
                    
                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });
        
        // 添加涟漪动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>