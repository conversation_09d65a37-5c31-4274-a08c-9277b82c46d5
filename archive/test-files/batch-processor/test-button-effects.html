<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮星光和高光效果测试</title>
    <style>
        /* 导入完整样式系统 */
        @import url('./styles/index.css');
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            background: #fafbfc;
        }
        
        .test-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .button-demo {
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .demo-label {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 12px;
            color: #6b7280;
        }
        
        .status-info {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 8px;
            padding: 8px 12px;
            background: #f9fafb;
            border-radius: 6px;
            border-left: 3px solid #3b82f6;
        }
        
        .effects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .effect-item {
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            background: white;
            border: 1px solid #e5e7eb;
        }
        
        .effect-label {
            font-size: 0.75rem;
            font-weight: 600;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: #374151;
        }
        
        /* 确保应用批处理器布局类 */
        .batch-processor-layout {
            width: 100%;
        }
        
        /* 测试按钮样式 */
        .test-button {
            margin: 10px;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
            margin: 16px 0;
        }
        
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checklist li:last-child {
            border-bottom: none;
        }
        
        .checklist li::before {
            content: "🔍";
            flex-shrink: 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        
        .highlight-box h4 {
            margin: 0 0 8px 0;
            color: #92400e;
        }
        
        .highlight-box p {
            margin: 0;
            color: #78350f;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container batch-processor-layout">
        <h1 style="text-align: center; color: #1f2937; margin-bottom: 40px; font-size: 2rem;">
            ✨ 按钮星光和高光效果测试
        </h1>
        
        <!-- 主按钮效果测试 -->
        <div class="test-section">
            <div class="test-title">
                <span>🌟</span>
                主按钮星光效果测试
            </div>
            
            <div class="button-demo">
                <div class="demo-label">主按钮 - 完整效果 (星光 + 高光扫过)</div>
                <button class="btn-primary sparkle-hover">
                    <span>⚡ 开始批量处理</span>
                </button>
                <div class="status-info">
                    ✨ 应该看到：金色渐变背景 + 星光闪烁 + 悬停时高光扫过
                </div>
            </div>
            
            <div class="button-demo">
                <div class="demo-label">处理中状态 - 动画效果</div>
                <button class="btn-primary sparkle-hover processing">
                    <span>🔄 处理中...</span>
                </button>
                <div class="status-info">
                    🔄 应该看到：处理中的脉冲动画 + 持续的星光效果
                </div>
            </div>
        </div>
        
        <!-- 效果分解测试 -->
        <div class="test-section">
            <div class="test-title">
                <span>🔬</span>
                效果分解测试
            </div>
            
            <div class="effects-grid">
                <div class="effect-item">
                    <div class="effect-label">基础按钮 (无特效)</div>
                    <button class="test-button" style="background: linear-gradient(135deg, #fef3c7 0%, #f59e0b 100%); color: #92400e;">
                        基础按钮
                    </button>
                    <div style="margin-top: 8px; font-size: 12px; color: #6b7280;">
                        只有基础的金色渐变
                    </div>
                </div>
                
                <div class="effect-item">
                    <div class="effect-label">仅星光效果</div>
                    <button class="btn-primary">
                        星光按钮
                    </button>
                    <div style="margin-top: 8px; font-size: 12px; color: #6b7280;">
                        金色渐变 + 星光闪烁
                    </div>
                </div>
                
                <div class="effect-item">
                    <div class="effect-label">完整效果</div>
                    <button class="btn-primary sparkle-hover">
                        完整效果
                    </button>
                    <div style="margin-top: 8px; font-size: 12px; color: #6b7280;">
                        星光 + 高光扫过 + 增强悬停
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 效果检查清单 -->
        <div class="test-section">
            <div class="test-title">
                <span>📋</span>
                效果检查清单
            </div>
            
            <div class="highlight-box">
                <h4>🎯 预期效果</h4>
                <p>主按钮应该具有金色渐变背景、微妙的星光闪烁动画，悬停时有高光扫过效果和增强的阴影。</p>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4 style="color: #374151; margin-bottom: 12px;">🌟 星光效果检查</h4>
                <ul class="checklist">
                    <li>按钮表面有微妙的星光点闪烁</li>
                    <li>星光点位置分布在按钮的不同区域</li>
                    <li>闪烁动画平滑且不刺眼</li>
                    <li>星光颜色为白色和金色调</li>
                </ul>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4 style="color: #374151; margin-bottom: 12px;">✨ 高光扫过检查</h4>
                <ul class="checklist">
                    <li>悬停时有白色高光从左上角扫向右下角</li>
                    <li>高光扫过动画流畅自然</li>
                    <li>扫过效果不会遮挡按钮文字</li>
                    <li>扫过完成后按钮恢复正常状态</li>
                </ul>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4 style="color: #374151; margin-bottom: 12px;">🎨 视觉效果检查</h4>
                <ul class="checklist">
                    <li>金色渐变背景显示正确</li>
                    <li>悬停时阴影增强明显</li>
                    <li>按钮文字清晰可读</li>
                    <li>整体效果协调统一</li>
                </ul>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4 style="color: #374151; margin-bottom: 12px;">⚡ 性能检查</h4>
                <ul class="checklist">
                    <li>动画流畅，无卡顿现象</li>
                    <li>CPU使用率正常</li>
                    <li>悬停响应及时</li>
                    <li>多次悬停无异常</li>
                </ul>
            </div>
        </div>
        
        <!-- 故障排除 -->
        <div class="test-section">
            <div class="test-title">
                <span>🔧</span>
                故障排除
            </div>
            
            <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 16px; margin-bottom: 16px;">
                <h4 style="margin: 0 0 8px 0; color: #dc2626;">如果星光效果不显示：</h4>
                <ul style="margin: 0; color: #7f1d1d; font-size: 14px;">
                    <li>检查CSS文件是否正确加载</li>
                    <li>确认 ::before 伪元素样式生效</li>
                    <li>检查 z-index 层级设置</li>
                    <li>验证动画关键帧定义</li>
                </ul>
            </div>
            
            <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 16px;">
                <h4 style="margin: 0 0 8px 0; color: #dc2626;">如果高光扫过不工作：</h4>
                <ul style="margin: 0; color: #7f1d1d; font-size: 14px;">
                    <li>检查 ::after 伪元素样式</li>
                    <li>确认 overflow: hidden 设置</li>
                    <li>验证 transform 动画</li>
                    <li>检查悬停触发器</li>
                </ul>
            </div>
        </div>
        
        <!-- 技术实现 -->
        <div class="test-section">
            <div class="test-title">
                <span>💻</span>
                技术实现详情
            </div>
            
            <div style="background: #f3f4f6; border-radius: 8px; padding: 16px; font-family: 'Courier New', monospace; font-size: 12px;">
                <h4 style="margin: 0 0 8px 0; color: #1f2937; font-family: inherit;">星光效果 (::before):</h4>
                <pre style="margin: 0; color: #374151;">background: radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.9) 1px, ...);
animation: gentleStarTwinkle 4s ease-in-out infinite;</pre>
            </div>
            
            <div style="background: #f3f4f6; border-radius: 8px; padding: 16px; font-family: 'Courier New', monospace; font-size: 12px; margin-top: 12px;">
                <h4 style="margin: 0 0 8px 0; color: #1f2937; font-family: inherit;">高光扫过 (::after):</h4>
                <pre style="margin: 0; color: #374151;">background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.4) 50%, transparent 70%);
transform: translateX(-100%) translateY(-100%) rotate(45deg);
transition: transform 0.8s ease;</pre>
            </div>
        </div>
    </div>
</body>
</html>
