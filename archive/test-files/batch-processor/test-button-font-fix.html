<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 按钮字体修复测试</title>
    <link rel="stylesheet" href="./styles/index.css">
    <style>
        body {
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: #f9fafb;
        }

        .test-title {
            font-size: 20px;
            font-weight: 700;
            color: #374151;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-description {
            color: #6b7280;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .button-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .button-demo {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .button-label {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 15px;
        }

        .status-indicator {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-top: 10px;
        }

        .status-fixed {
            background: #dcfce7;
            color: #166534;
        }

        .status-issue {
            background: #fef2f2;
            color: #dc2626;
        }

        .comparison-note {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
            color: #1e40af;
        }

        /* 模拟批量处理器布局 */
        .batch-processor-layout {
            /* 确保样式正确应用 */
        }

        /* 测试按钮样式 */
        .test-button {
            width: 100%;
            max-width: 280px;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .highlight-box {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }

        .highlight-title {
            font-size: 18px;
            font-weight: 700;
            color: #92400e;
            margin-bottom: 15px;
        }

        .highlight-text {
            color: #92400e;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container batch-processor-layout">
        <div class="test-section">
            <h1 class="test-title">🔧 按钮字体颜色修复验证</h1>
            <p class="test-description">
                测试 force-white-text 类是否正确排除金色按钮，确保金色按钮显示深色文字而不是白色文字。
            </p>

            <div class="button-grid">
                <div class="button-demo">
                    <div class="button-label">✅ 修复后：金色按钮 (应该显示深色文字)</div>
                    <button class="btn-authority btn-primary-gold force-white-text test-button">
                        <span>⚡ 处理 5 条查询</span>
                        <span class="bg-white/20 px-2 py-1 rounded text-xs">100%</span>
                    </button>
                    <div class="status-indicator status-fixed">✓ 深色文字正常</div>
                </div>

                <div class="button-demo">
                    <div class="button-label">✅ 修复后：双横线金色按钮</div>
                    <button class="btn-authority btn--primary-gold force-white-text test-button">
                        <span>🔄 重试失败项</span>
                        <span class="bg-white/20 px-2 py-1 rounded text-xs">3</span>
                    </button>
                    <div class="status-indicator status-fixed">✓ 深色文字正常</div>
                </div>

                <div class="button-demo">
                    <div class="button-label">🔍 对比：普通按钮 (仍然是白色文字)</div>
                    <button class="btn force-white-text test-button" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white;">
                        <span>普通按钮</span>
                        <span class="bg-white/20 px-2 py-1 rounded text-xs">示例</span>
                    </button>
                    <div class="status-indicator status-fixed">✓ 白色文字正常</div>
                </div>

                <div class="button-demo">
                    <div class="button-label">🔍 对比：无 force-white-text 的金色按钮</div>
                    <button class="btn-authority btn-primary-gold test-button">
                        <span>⭐ 金色按钮</span>
                        <span class="bg-white/20 px-2 py-1 rounded text-xs">OK</span>
                    </button>
                    <div class="status-indicator status-fixed">✓ 深色文字正常</div>
                </div>
            </div>

            <div class="comparison-note">
                <strong>🎯 修复要点：</strong><br>
                • force-white-text 类现在排除 .btn-primary-gold 和 .btn--primary-gold<br>
                • 金色按钮始终显示深色文字 (#92400e)<br>
                • 其他按钮的白色文字功能保持正常<br>
                • 悬停状态也正确处理
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">🧪 实际页面按钮测试</h2>
            <p class="test-description">
                模拟实际页面中的按钮结构和样式，验证修复效果。
            </p>

            <div style="max-width: 400px; margin: 0 auto;">
                <!-- 模拟主要操作按钮 -->
                <button
                  className="btn-authority btn-primary-gold relative w-full overflow-hidden bg-gradient-to-br from-yellow-50 via-yellow-200 to-yellow-500 font-semibold text-base px-6 py-3.5 rounded-xl shadow-lg shadow-yellow-200/50 transition-all duration-300 ease-out hover:shadow-xl hover:shadow-yellow-300/60 hover:-translate-y-0.5 hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed force-white-text"
                  style="width: 100%; padding: 16px 24px; background: linear-gradient(135deg, #fef3c7 0%, #fde68a 25%, #fcd34d 50%, #f59e0b 75%, #d97706 100%); border-radius: 12px; border: none; font-size: 16px; font-weight: 600; cursor: pointer;"
                >
                  <div style="position: relative; z-index: 10; display: flex; align-items: center; justify-content: center; gap: 12px;">
                    <span>⚡</span>
                    <span>处理 5 条查询</span>
                    <span style="background: rgba(146, 64, 14, 0.2); color: #92400e; padding: 4px 8px; border-radius: 12px; font-size: 12px;">→</span>
                  </div>
                </button>
            </div>

            <div class="comparison-note" style="margin-top: 20px;">
                <strong>✅ 预期结果：</strong> 按钮文字应该显示为深橙色 (#92400e)，而不是白色。
            </div>
        </div>

        <div class="highlight-box">
            <div class="highlight-title">🎯 修复总结</div>
            <div class="highlight-text">
                ✅ <strong>问题解决</strong>：force-white-text 不再影响金色按钮<br>
                ✅ <strong>文字颜色</strong>：金色按钮显示正确的深色文字 (#92400e)<br>
                ✅ <strong>选择器优化</strong>：使用 :not() 伪类排除特定按钮<br>
                ✅ <strong>兼容性保持</strong>：其他按钮的白色文字功能正常<br>
                ✅ <strong>无副作用</strong>：不影响现有的样式系统
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelectorAll('button').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // 检查按钮文字颜色
        window.addEventListener('load', function() {
            const goldButtons = document.querySelectorAll('.btn-primary-gold, .btn--primary-gold');
            goldButtons.forEach(btn => {
                const computedStyle = window.getComputedStyle(btn);
                const color = computedStyle.color;
                console.log('金色按钮文字颜色:', color);
                
                // 检查是否为深色（非白色）
                if (color !== 'rgb(255, 255, 255)' && color !== 'white') {
                    console.log('✅ 金色按钮文字颜色正确');
                } else {
                    console.log('❌ 金色按钮文字颜色仍为白色');
                }
            });
        });
    </script>
</body>
</html>
