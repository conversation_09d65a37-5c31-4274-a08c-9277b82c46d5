<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金色按钮字体定位修复测试</title>
    <link rel="stylesheet" href="../styles/foundation/variables.css">
    <link rel="stylesheet" href="../styles/components/buttons.css">
    <link rel="stylesheet" href="../styles/index.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(12px);
        }

        .demo-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 8px;
            color: #1f2937;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-description {
            color: #6b7280;
            margin-bottom: 20px;
            font-size: 0.95rem;
        }

        .button-demo {
            margin: 20px 0;
            padding: 20px;
            background: rgba(248, 250, 252, 0.8);
            border-radius: 12px;
            border: 1px solid rgba(226, 232, 240, 0.6);
        }

        .button-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
            font-size: 1rem;
        }

        .highlight-box {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.15) 100%);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 12px;
            padding: 16px;
            margin-top: 20px;
        }

        .highlight-title {
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 8px;
        }

        .highlight-text {
            color: #3730a3;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* 模拟批处理器布局 */
        .batch-processor-layout {
            /* 应用批处理器的样式上下文 */
        }

        /* 模拟主按钮容器 */
        .main-button-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            width: 100%;
            max-width: 300px;
            margin: 0 auto;
        }

        /* 确保按钮全宽显示 */
        .test-button {
            width: 100%;
        }

        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 8px;
            font-family: 'SF Mono', Consolas, monospace;
            font-size: 0.875rem;
            margin: 12px 0;
            overflow-x: auto;
        }

        .selector-path {
            background: rgba(59, 130, 246, 0.1);
            color: #1e40af;
            padding: 8px 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.8rem;
            word-break: break-all;
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-section">
            <h1 class="demo-title">🔧 金色按钮字体定位修复</h1>
            <p class="demo-description">
                修复 force-white-text 类与金色按钮的冲突，确保金色按钮显示正确的深色文字
            </p>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">问题定位</h2>
            <p class="demo-description">
                您提到的按钮选择器路径：
            </p>
            <div class="selector-path">
                document.querySelector("#main-content > div > aside.layout-sidebar > div > div > div.mt-4.flex-shrink-0.space-y-4.relative.z-50.main-button-container > button")
            </div>
            <p class="demo-description">
                这个按钮同时具有 <code>btn-authority</code> 和 <code>btn-primary-gold</code> 类，
                但被 <code>force-white-text</code> 类强制覆盖为白色文字，导致字体定位错误。
            </p>
        </div>

        <div class="demo-section batch-processor-layout">
            <h2 class="demo-title">修复效果演示</h2>
            
            <div class="button-demo">
                <div class="button-title">✅ 修复后的金色按钮 (完整结构)</div>
                <div class="main-button-container">
                    <button class="btn-authority btn-primary-gold relative w-full overflow-hidden bg-gradient-to-br from-yellow-50 via-yellow-200 to-yellow-500 font-semibold text-base px-6 py-3.5 rounded-xl shadow-lg shadow-yellow-200/50 transition-all duration-300 ease-out hover:shadow-xl hover:shadow-yellow-300/60 hover:-translate-y-0.5 hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed force-white-text test-button">
                        <!-- 星光效果层 -->
                        <div class="absolute inset-0 pointer-events-none"></div>
                        <!-- 高光扫过效果层 -->
                        <div class="absolute pointer-events-none transition-transform duration-800 ease-out highlight-sweep-layer" style="top: -50%; left: -50%; width: 200%; height: 200%; background: linear-gradient(45deg, transparent 20%, rgba(255, 255, 255, 0.8) 50%, transparent 80%); transform: translateX(-100%) translateY(-100%) rotate(45deg); z-index: 2;"></div>
                        <!-- 额外的闪光效果 -->
                        <div class="absolute inset-0 pointer-events-none" style="background: radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.2) 0%, transparent 50%); animation: sparkleGlow 3s ease-in-out infinite; z-index: 3; border-radius: inherit;"></div>
                        <!-- 内容层 -->
                        <div class="relative z-10 flex items-center justify-center w-full h-full">
                            <div class="flex items-center justify-center gap-3">
                                <div class="button-icon flex items-center justify-center flex-shrink-0">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>
                                    </svg>
                                </div>
                                <div class="button-text flex items-center gap-2 font-semibold">
                                    <span>处理 3 条查询</span>
                                    <span class="opacity-75 text-lg">→</span>
                                </div>
                            </div>
                        </div>
                        <!-- 波光效果 -->
                        <div class="shimmer-effect"></div>
                    </button>
                </div>
                <p style="color: #6b7280; font-size: 0.875rem; margin-top: 12px;">
                    完整的按钮结构，包含所有装饰层和复杂的嵌套布局
                </p>
            </div>

            <div class="button-demo">
                <div class="button-title">🔍 对比：普通按钮 (仍然是白色文字)</div>
                <div class="main-button-container">
                    <button class="btn force-white-text test-button" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 12px 24px; border-radius: 8px;">
                        <span>普通按钮</span>
                        <span class="bg-white bg-opacity-20 rounded-full px-2 py-0.5 text-xs font-mono">示例</span>
                    </button>
                </div>
                <p style="color: #6b7280; font-size: 0.875rem; margin-top: 12px;">
                    普通按钮仍然受 force-white-text 影响，显示白色文字
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">修复方案</h2>
            <p class="demo-description">
                通过修改 CSS 选择器，使 force-white-text 类排除金色按钮：
            </p>
            
            <div class="code-block">
/* 修复前 */
.force-white-text,
.force-white-text * {
  color: white !important;
}

/* 修复后 */
.force-white-text:not(.btn-authority):not(.btn-primary-gold),
.force-white-text:not(.btn-authority):not(.btn-primary-gold) * {
  color: white !important;
}

/* 金色按钮专用文字颜色 */
button.btn-authority.btn-primary-gold,
button.btn-authority.btn-primary-gold * {
  color: #92400e !important;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.2) !important;
}
            </div>
        </div>

        <div class="demo-section">
            <div class="highlight-box">
                <div class="highlight-title">🎯 修复总结</div>
                <div class="highlight-text">
                    ✅ <strong>问题解决</strong>：force-white-text 不再影响金色按钮<br>
                    ✅ <strong>文字颜色</strong>：金色按钮显示正确的深色文字 (#92400e)<br>
                    ✅ <strong>选择器优化</strong>：使用 :not() 伪类排除特定按钮<br>
                    ✅ <strong>兼容性保持</strong>：其他按钮的白色文字功能正常<br>
                    ✅ <strong>无副作用</strong>：不影响现有的样式系统
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟按钮点击效果
        document.querySelectorAll('.test-button').forEach(btn => {
            btn.addEventListener('click', function() {
                const originalText = this.innerHTML;
                this.innerHTML = this.innerHTML.replace(/开始批量处理|普通按钮/, '点击成功 ✓');
                setTimeout(() => {
                    this.innerHTML = originalText;
                }, 1500);
            });
        });

        // 显示当前按钮的计算样式
        document.addEventListener('DOMContentLoaded', function() {
            const goldButton = document.querySelector('.btn-authority.btn-primary-gold');
            if (goldButton) {
                const computedStyle = window.getComputedStyle(goldButton);
                console.log('金色按钮文字颜色:', computedStyle.color);
                console.log('金色按钮文字阴影:', computedStyle.textShadow);
            }
        });
    </script>
</body>
</html>
