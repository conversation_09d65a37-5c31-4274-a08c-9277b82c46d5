<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮优化效果测试</title>
    <link rel="stylesheet" href="../styles/foundation/variables.css">
    <link rel="stylesheet" href="../styles/components/buttons.css">
    <link rel="stylesheet" href="../styles/components/panels.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(12px);
        }

        .demo-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 8px;
            color: #1f2937;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-description {
            color: #6b7280;
            margin-bottom: 20px;
            font-size: 0.95rem;
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 20px;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .comparison-item {
            padding: 16px;
            background: rgba(248, 250, 252, 0.8);
            border-radius: 12px;
            border: 1px solid rgba(226, 232, 240, 0.6);
        }

        .comparison-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
            font-size: 1rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-list li {
            padding: 4px 0;
            color: #6b7280;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feature-list li::before {
            content: "✨";
            font-size: 0.75rem;
        }

        .highlight-box {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.15) 100%);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 12px;
            padding: 16px;
            margin-top: 20px;
        }

        .highlight-title {
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 8px;
        }

        .highlight-text {
            color: #3730a3;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* 确保按钮在演示中正确显示 */
        .status-filter-btn {
            margin-right: 8px;
            margin-bottom: 8px;
        }

        .btn--secondary-glass {
            margin-right: 8px;
            margin-bottom: 8px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-section">
            <h1 class="demo-title">🎨 按钮优化效果演示</h1>
            <p class="demo-description">
                根据主题风格优化的按钮设计，更精准的配色、更强的交互性、更现代的视觉效果
            </p>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">视图切换器 (浅蓝色渐变)</h2>
            <p class="demo-description">
                修复为浅蓝色渐变的Switch切换器，颜色不再过深，使用深蓝色文字和图标
            </p>

            <div class="view-mode-switcher" style="margin-bottom: 20px;">
                <button class="view-mode-btn active">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <rect x="3" y="3" width="7" height="7" />
                        <rect x="14" y="3" width="7" height="7" />
                        <rect x="14" y="14" width="7" height="7" />
                        <rect x="3" y="14" width="7" height="7" />
                    </svg>
                    卡片
                </button>
                <button class="view-mode-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <line x1="8" y1="6" x2="21" y2="6" />
                        <line x1="8" y1="12" x2="21" y2="12" />
                        <line x1="8" y1="18" x2="21" y2="18" />
                        <line x1="3" y1="6" x2="3.01" y2="6" />
                        <line x1="3" y1="12" x2="3.01" y2="12" />
                        <line x1="3" y1="18" x2="3.01" y2="18" />
                    </svg>
                    列表
                </button>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">状态筛选按钮</h2>
            <p class="demo-description">
                修复"全部"按钮的UI风格统一性，采用一致的主题蓝色设计
            </p>

            <div class="button-group">
                <button class="status-filter-btn selected all">
                    全部 <span class="count-badge">4</span>
                </button>
                <button class="status-filter-btn unselected success">
                    成功 <span class="count-badge">4</span>
                </button>
                <button class="status-filter-btn unselected error">
                    失败 <span class="count-badge">0</span>
                </button>
                <button class="status-filter-btn unselected processing">
                    处理中 <span class="count-badge">0</span>
                </button>
            </div>

            <div class="comparison-grid">
                <div class="comparison-item">
                    <div class="comparison-title">🔧 修复内容</div>
                    <ul class="feature-list">
                        <li>视图切换改为Switch样式</li>
                        <li>"全部"按钮UI风格统一</li>
                        <li>次要按钮删除星光特效</li>
                        <li>主次按钮层次分明</li>
                        <li>柔和的浅蓝色渐变</li>
                    </ul>
                </div>
                <div class="comparison-item">
                    <div class="comparison-title">✅ 优化效果</div>
                    <ul class="feature-list">
                        <li>Switch切换器更直观</li>
                        <li>主题配色更加统一</li>
                        <li>按钮层次更加清晰</li>
                        <li>交互反馈更加精准</li>
                        <li>视觉噪音显著减少</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">操作按钮 (主次区分)</h2>
            <p class="demo-description">
                次要按钮使用柔和的浅蓝色渐变，删除星光特效，主次分明的设计层次
            </p>
            
            <div class="button-group">
                <button class="btn btn-sm btn--secondary-glass">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    打开所有成功结果
                    <span class="bg-white/30 rounded-full px-2 py-0.5 text-xs font-mono">4</span>
                </button>
                <button class="btn btn-sm btn--secondary-glass">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                    </svg>
                    复制成功结果列表
                    <span class="bg-white/30 rounded-full px-2 py-0.5 text-xs font-mono">4</span>
                </button>
            </div>

            <div class="comparison-grid">
                <div class="comparison-item">
                    <div class="comparison-title">🎯 次要按钮优化</div>
                    <ul class="feature-list">
                        <li>删除星光特效减少干扰</li>
                        <li>柔和的浅蓝色渐变</li>
                        <li>适度的悬停反馈</li>
                        <li>保持玻璃拟态质感</li>
                        <li>主次层次更加分明</li>
                    </ul>
                </div>
                <div class="comparison-item">
                    <div class="comparison-title">⚖️ 主次平衡</div>
                    <ul class="feature-list">
                        <li>主要按钮保留星光特效</li>
                        <li>次要按钮降低视觉权重</li>
                        <li>统一的主题配色系统</li>
                        <li>清晰的信息层次结构</li>
                        <li>减少视觉噪音干扰</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="highlight-box">
                <div class="highlight-title">🎯 修复总结</div>
                <div class="highlight-text">
                    ✅ <strong>视图切换器</strong>：修复为真正的Switch样式，而不是两个独立按钮<br>
                    ✅ <strong>按钮主次</strong>：次要按钮使用柔和浅蓝色渐变，删除星光特效<br>
                    ✅ <strong>"全部"按钮</strong>：修复UI风格统一性，采用一致的主题蓝色<br>
                    ✅ <strong>层次分明</strong>：主要按钮保留特效，次要按钮降低视觉权重<br>
                    ✅ <strong>代码质量</strong>：在现有文件中微调，无!important，高可维护性
                </div>
            </div>
        </div>
    </div>

    <script>
        // 视图切换器交互
        document.querySelectorAll('.view-mode-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除所有active状态
                document.querySelectorAll('.view-mode-btn').forEach(b => {
                    b.classList.remove('active');
                });
                // 添加当前按钮的active状态
                this.classList.add('active');
            });
        });

        // 状态筛选按钮交互
        document.querySelectorAll('.status-filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除所有选中状态
                document.querySelectorAll('.status-filter-btn').forEach(b => {
                    b.className = b.className.replace(/selected \w+/, 'unselected');
                });

                // 添加选中状态
                if (this.textContent.includes('全部')) {
                    this.className = this.className.replace('unselected', 'selected all');
                } else if (this.textContent.includes('成功')) {
                    this.className = this.className.replace('unselected', 'selected success');
                } else if (this.textContent.includes('失败')) {
                    this.className = this.className.replace('unselected', 'selected error');
                } else if (this.textContent.includes('处理中')) {
                    this.className = this.className.replace('unselected', 'selected processing');
                }
            });
        });

        // 操作按钮点击反馈
        document.querySelectorAll('.btn--secondary-glass').forEach(btn => {
            btn.addEventListener('click', function() {
                const originalText = this.innerHTML;
                this.innerHTML = this.innerHTML.replace(/打开所有成功结果|复制成功结果列表/, '操作完成 ✓');
                setTimeout(() => {
                    this.innerHTML = originalText;
                }, 1500);
            });
        });
    </script>
</body>
</html>
