<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抽屉动画测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }

        .test-container {
            position: relative;
            width: 100%;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .trigger-btn {
            padding: 12px 24px;
            background: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .trigger-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }

        /* 抽屉容器 */
        .drawer-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .drawer-overlay.open {
            opacity: 1;
            visibility: visible;
        }

        .drawer-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 400px;
            height: 100vh;
            background: white;
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
            transform: translateX(-100%);
            transition: transform 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            z-index: 1001;
        }

        .drawer-container.open {
            transform: translateX(0);
        }

        .drawer-header {
            padding: 20px;
            border-bottom: 1px solid #e5e5e5;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .drawer-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .close-btn:hover {
            color: #333;
        }

        .drawer-content {
            padding: 20px;
            height: calc(100vh - 80px);
            overflow-y: auto;
        }

        .slide-indicator {
            position: absolute;
            right: -6px;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 60px;
            background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%);
            border-radius: 0 6px 6px 0;
            box-shadow: 2px 0 12px rgba(59, 130, 246, 0.4);
        }

        .debug-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 2000;
            min-width: 300px;
        }

        .debug-info h3 {
            margin-bottom: 10px;
            color: #4ade80;
        }

        .debug-info p {
            margin: 5px 0;
        }

        .status-ok {
            color: #4ade80;
        }

        .status-error {
            color: #ef4444;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <button class="trigger-btn" onclick="toggleDrawer()">
            测试左侧滑入抽屉
        </button>
    </div>

    <!-- 抽屉 -->
    <div id="drawerOverlay" class="drawer-overlay" onclick="closeDrawer()">
        <div id="drawerContainer" class="drawer-container" onclick="event.stopPropagation()">
            <div class="slide-indicator"></div>
            <div class="drawer-header">
                <div class="drawer-title">测试抽屉</div>
                <button class="close-btn" onclick="closeDrawer()">&times;</button>
            </div>
            <div class="drawer-content">
                <h3>抽屉内容</h3>
                <p>这是一个测试抽屉，用于验证左侧滑入动画效果。</p>
                <p>如果你看到这个抽屉是从左侧滑入的，说明动画正确。</p>
                <p>如果是从右侧滑入的，说明还有问题需要修复。</p>
                
                <h4>测试步骤：</h4>
                <ol>
                    <li>点击"测试左侧滑入抽屉"按钮</li>
                    <li>观察抽屉的滑入方向</li>
                    <li>应该从左侧滑入到屏幕中央</li>
                    <li>滑动指示器应该在抽屉右边缘</li>
                </ol>

                <h4>预期行为：</h4>
                <ul>
                    <li>✅ 抽屉从左侧（-100%）滑入</li>
                    <li>✅ 最终位置在屏幕左侧（left: 0）</li>
                    <li>✅ 滑动指示器在右边缘</li>
                    <li>✅ 关闭时滑出到左侧</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 调试信息 -->
    <div class="debug-info">
        <h3>🔍 抽屉动画调试信息</h3>
        <p>抽屉状态: <span id="drawerStatus">关闭</span></p>
        <p>Transform: <span id="transformInfo">translateX(-100%)</span></p>
        <p>Position: <span id="positionInfo">left: 0</span></p>
        <p>动画方向: <span id="animationDirection">左侧滑入</span></p>
        <p>指示器位置: <span id="indicatorPosition">right: -6px</span></p>
        
        <h4>问题诊断：</h4>
        <p>初始位置: <span id="initialPosition" class="status-ok">✅ 正确</span></p>
        <p>滑入方向: <span id="slideDirection" class="status-ok">✅ 正确</span></p>
        <p>最终位置: <span id="finalPosition" class="status-ok">✅ 正确</span></p>
    </div>

    <script>
        let isDrawerOpen = false;

        function toggleDrawer() {
            if (isDrawerOpen) {
                closeDrawer();
            } else {
                openDrawer();
            }
        }

        function openDrawer() {
            const overlay = document.getElementById('drawerOverlay');
            const container = document.getElementById('drawerContainer');
            
            // 更新调试信息
            updateDebugInfo('打开中', 'translateX(0)', 'left: 0', '从左侧滑入');
            
            overlay.classList.add('open');
            container.classList.add('open');
            isDrawerOpen = true;
            
            // 延迟更新状态以反映动画完成
            setTimeout(() => {
                updateDebugInfo('已打开', 'translateX(0)', 'left: 0', '完全显示');
            }, 400);
        }

        function closeDrawer() {
            const overlay = document.getElementById('drawerOverlay');
            const container = document.getElementById('drawerContainer');
            
            // 更新调试信息
            updateDebugInfo('关闭中', 'translateX(-100%)', 'left: 0', '滑出到左侧');
            
            overlay.classList.remove('open');
            container.classList.remove('open');
            isDrawerOpen = false;
            
            // 延迟更新状态以反映动画完成
            setTimeout(() => {
                updateDebugInfo('已关闭', 'translateX(-100%)', 'left: 0', '隐藏在左侧');
            }, 400);
        }

        function updateDebugInfo(status, transform, position, direction) {
            document.getElementById('drawerStatus').textContent = status;
            document.getElementById('transformInfo').textContent = transform;
            document.getElementById('positionInfo').textContent = position;
            document.getElementById('animationDirection').textContent = direction;
        }

        // 键盘事件
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && isDrawerOpen) {
                closeDrawer();
            }
        });

        // 初始化调试信息
        updateDebugInfo('已关闭', 'translateX(-100%)', 'left: 0', '隐藏在左侧');
    </script>
</body>
</html>