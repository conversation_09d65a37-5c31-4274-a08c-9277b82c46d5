<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强对比度蓝色文字系统测试</title>
    <style>
        :root {
            /* 增强对比度的蓝色文字色阶 */
            --text-blue-50: #f0f9ff;
            --text-blue-100: #e0f2fe;
            --text-blue-200: #bae6fd;
            --text-blue-300: #7dd3fc;
            --text-blue-400: #38bdf8;
            --text-blue-500: #0ea5e9;
            --text-blue-600: #0284c7;
            --text-blue-700: #0369a1;
            --text-blue-800: #075985;
            --text-blue-900: #0c4a6e;
            --text-blue-950: #082f49;
            --text-blue-975: #051e34;
            
            /* 白色文字系列 */
            --text-white: #ffffff;
            --text-white-90: rgba(255, 255, 255, 0.9);
            --text-white-80: rgba(255, 255, 255, 0.8);
            --text-white-70: rgba(255, 255, 255, 0.7);
        }

        body {
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 35%, #f9fafb 65%, #f0f9ff 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            color: var(--text-blue-950);
        }

        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .title {
            text-align: center;
            color: var(--text-blue-950);
            margin-bottom: 40px;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .demo-section {
            padding: 25px;
            border-radius: 16px;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .light-bg {
            background: rgba(248, 250, 252, 0.8);
        }

        .medium-bg {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: var(--text-white);
        }

        .dark-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #0c4a6e 100%);
            color: var(--text-white);
        }

        .demo-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }

        .content-sample {
            margin-bottom: 15px;
        }

        .content-sample h1 {
            font-size: 1.6rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .content-sample h2 {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 6px;
        }

        .content-sample h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .content-sample p {
            font-size: 1rem;
            line-height: 1.5;
            margin-bottom: 6px;
        }

        .content-sample .text-sm {
            font-size: 0.875rem;
            margin-bottom: 4px;
        }

        .content-sample .text-xs {
            font-size: 0.75rem;
            margin-bottom: 4px;
        }

        /* 浅色背景文字样式 */
        .light-text h1 { color: var(--text-blue-950); }
        .light-text h2 { color: var(--text-blue-900); }
        .light-text h3 { color: var(--text-blue-800); }
        .light-text p { color: var(--text-blue-800); }
        .light-text .text-sm { color: var(--text-blue-700); }
        .light-text .text-xs { color: var(--text-blue-600); }

        /* 中等背景文字样式 */
        .medium-text h1 { color: var(--text-white); }
        .medium-text h2 { color: var(--text-white-90); }
        .medium-text h3 { color: var(--text-white-80); }
        .medium-text p { color: var(--text-white-90); }
        .medium-text .text-sm { color: var(--text-white-80); }
        .medium-text .text-xs { color: var(--text-white-70); }

        /* 深色背景文字样式 */
        .dark-text h1 { color: var(--text-white); }
        .dark-text h2 { color: var(--text-blue-50); }
        .dark-text h3 { color: var(--text-blue-100); }
        .dark-text p { color: var(--text-blue-100); }
        .dark-text .text-sm { color: var(--text-blue-200); }
        .dark-text .text-xs { color: var(--text-blue-300); }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 8px;
            margin-top: 20px;
        }

        .color-item {
            text-align: center;
            padding: 12px 8px;
            border-radius: 6px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .color-blue-50 { background: var(--text-blue-50); color: var(--text-blue-950); }
        .color-blue-100 { background: var(--text-blue-100); color: var(--text-blue-950); }
        .color-blue-200 { background: var(--text-blue-200); color: var(--text-blue-950); }
        .color-blue-300 { background: var(--text-blue-300); color: var(--text-blue-950); }
        .color-blue-400 { background: var(--text-blue-400); color: white; }
        .color-blue-500 { background: var(--text-blue-500); color: white; }
        .color-blue-600 { background: var(--text-blue-600); color: white; }
        .color-blue-700 { background: var(--text-blue-700); color: white; }
        .color-blue-800 { background: var(--text-blue-800); color: white; }
        .color-blue-900 { background: var(--text-blue-900); color: white; }
        .color-blue-950 { background: var(--text-blue-950); color: white; }
        .color-blue-975 { background: var(--text-blue-975); color: white; }

        .features {
            background: rgba(255, 255, 255, 0.7);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }

        .features h4 {
            color: var(--text-blue-950);
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .features ul {
            margin: 0;
            padding-left: 16px;
            color: var(--text-blue-800);
            font-size: 0.8rem;
        }

        .features li {
            margin-bottom: 3px;
        }

        .highlight {
            border: 2px solid #3b82f6;
        }

        .contrast-info {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            padding: 20px;
            border-radius: 12px;
            margin-top: 30px;
            text-align: center;
        }

        .contrast-info h3 {
            color: var(--text-blue-950);
            margin-bottom: 15px;
        }

        .contrast-info p {
            color: var(--text-blue-800);
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="title">🎨 增强对比度蓝色文字系统</h1>
        
        <div class="comparison-grid">
            <div class="demo-section light-bg highlight">
                <h3 class="demo-title light-text">✨ 浅色背景 - 深蓝文字</h3>
                <div class="content-sample light-text">
                    <h1>主标题 (blue-950)</h1>
                    <h2>二级标题 (blue-900)</h2>
                    <h3>三级标题 (blue-800)</h3>
                    <p>段落文字内容展示 (blue-800)</p>
                    <p class="text-sm">小号文字信息 (blue-700)</p>
                    <p class="text-xs">超小号辅助信息 (blue-600)</p>
                </div>
                <div class="features">
                    <h4>浅色背景特点：</h4>
                    <ul>
                        <li>使用最深蓝色确保对比度</li>
                        <li>清晰的视觉层次</li>
                        <li>符合无障碍标准</li>
                        <li>专业商务感</li>
                    </ul>
                </div>
            </div>

            <div class="demo-section medium-bg">
                <h3 class="demo-title medium-text">🌟 中等背景 - 白色文字</h3>
                <div class="content-sample medium-text">
                    <h1>主标题 (white)</h1>
                    <h2>二级标题 (white-90)</h2>
                    <h3>三级标题 (white-80)</h3>
                    <p>段落文字内容展示 (white-90)</p>
                    <p class="text-sm">小号文字信息 (white-80)</p>
                    <p class="text-xs">超小号辅助信息 (white-70)</p>
                </div>
                <div class="features">
                    <h4>中等背景特点：</h4>
                    <ul>
                        <li>白色文字系统</li>
                        <li>透明度层次分明</li>
                        <li>良好的可读性</li>
                        <li>现代设计感</li>
                    </ul>
                </div>
            </div>

            <div class="demo-section dark-bg">
                <h3 class="demo-title dark-text">🌙 深色背景 - 浅蓝文字</h3>
                <div class="content-sample dark-text">
                    <h1>主标题 (white)</h1>
                    <h2>二级标题 (blue-50)</h2>
                    <h3>三级标题 (blue-100)</h3>
                    <p>段落文字内容展示 (blue-100)</p>
                    <p class="text-sm">小号文字信息 (blue-200)</p>
                    <p class="text-xs">超小号辅助信息 (blue-300)</p>
                </div>
                <div class="features">
                    <h4>深色背景特点：</h4>
                    <ul>
                        <li>浅蓝色文字系统</li>
                        <li>渐进式亮度层次</li>
                        <li>护眼深色模式</li>
                        <li>高端科技感</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="contrast-info">
            <h3>🎨 完整蓝色文字色彩调色板</h3>
            <p><strong>新增深度层次：</strong> blue-950, blue-975 和完整白色文字系统</p>
            <div class="color-palette">
                <div class="color-item color-blue-50">blue-50</div>
                <div class="color-item color-blue-100">blue-100</div>
                <div class="color-item color-blue-200">blue-200</div>
                <div class="color-item color-blue-300">blue-300</div>
                <div class="color-item color-blue-400">blue-400</div>
                <div class="color-item color-blue-500">blue-500</div>
                <div class="color-item color-blue-600">blue-600</div>
                <div class="color-item color-blue-700">blue-700</div>
                <div class="color-item color-blue-800">blue-800</div>
                <div class="color-item color-blue-900">blue-900</div>
                <div class="color-item color-blue-950">blue-950</div>
                <div class="color-item color-blue-975">blue-975</div>
            </div>
        </div>

        <div style="text-align: center; color: var(--text-blue-800); margin-top: 30px;">
            <p><strong>💡 优化说明：</strong></p>
            <p>• 增加了 blue-950 和 blue-975 两个超深蓝色层次</p>
            <p>• 添加了完整的白色文字透明度系统</p>
            <p>• 根据背景色自动选择最佳对比度的文字颜色</p>
            <p>• 确保所有文字都符合 WCAG 无障碍对比度标准</p>
        </div>
    </div>
</body>
</html>
