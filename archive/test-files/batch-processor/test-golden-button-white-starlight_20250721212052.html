<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金色按钮白色星光测试</title>
    <link rel="stylesheet" href="../styles/index.css">
    <style>
        body {
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 35%, #f9fafb 65%, #e0f2fe 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            color: #1e3a8a;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .title {
            text-align: center;
            color: #1e3a8a;
            margin-bottom: 40px;
            font-size: 2rem;
            font-weight: 700;
        }

        .button-demo {
            display: flex;
            flex-direction: column;
            gap: 30px;
            align-items: center;
        }

        .demo-section {
            width: 100%;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            text-align: center;
        }

        .demo-section h3 {
            margin-bottom: 20px;
            color: #1e40af;
        }

        .highlight {
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.05);
        }

        .debug-info {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            text-align: left;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .icon-placeholder {
            width: 16px;
            height: 16px;
            background: #ffffff;
            border-radius: 2px;
            margin-right: 8px;
        }

        .features {
            background: rgba(255, 255, 255, 0.7);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            text-align: left;
        }

        .features h4 {
            color: #1e40af;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .features ul {
            margin: 0;
            padding-left: 16px;
            color: #374151;
        }

        .features li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="title">✨ 金色按钮白色星光测试</h1>
        
        <div class="button-demo">
            <div class="demo-section highlight">
                <h3>🌟 金色按钮 - 白色星光效果</h3>
                <div class="batch-processor-layout">
                    <button class="btn-primary sparkle-hover">
                        <div class="icon-placeholder"></div>
                        处理 35 条查询 →
                    </button>
                </div>
                <div class="features">
                    <h4>预期效果：</h4>
                    <ul>
                        <li>15个白色星点随机闪烁</li>
                        <li>3层独立动画叠加</li>
                        <li>白色光芒扫过动画</li>
                        <li>悬停时效果增强</li>
                        <li>点击时惊喜爆发</li>
                        <li>与金色背景形成美丽对比</li>
                    </ul>
                </div>
                <div class="debug-info">
                    类名: btn-primary sparkle-hover<br>
                    容器: batch-processor-layout<br>
                    应该显示: 白色星点闪烁 + 白色光芒扫过效果
                </div>
            </div>

            <div class="demo-section">
                <h3>🔍 调试信息</h3>
                <div class="debug-info">
                    <strong>CSS选择器优先级:</strong><br>
                    html body .batch-processor-layout .btn-primary::before<br>
                    html body .batch-processor-layout button.btn-primary::before<br>
                    body .batch-processor-layout .btn-primary.sparkle-hover::before<br><br>
                    
                    <strong>星光效果:</strong><br>
                    • 15个白色星点，不同大小和透明度<br>
                    • 使用 randomTwinkle1, randomTwinkle2, randomTwinkle3 动画<br>
                    • z-index: 1<br><br>
                    
                    <strong>光芒扫过:</strong><br>
                    • 白色渐变光芒从左上到右下<br>
                    • 使用 whiteStarSweep 动画<br>
                    • z-index: 2<br><br>
                    
                    <strong>如果没有效果，检查:</strong><br>
                    1. 浏览器控制台是否有CSS错误<br>
                    2. index.css 是否正确加载<br>
                    3. animations.css 中的动画定义<br>
                    4. CSS选择器是否被其他样式覆盖
                </div>
            </div>

            <div class="demo-section">
                <h3>🎨 效果对比</h3>
                <p>将鼠标悬停在按钮上，观察白色星光效果的变化：</p>
                <ul style="text-align: left; max-width: 400px; margin: 0 auto;">
                    <li><strong>正常状态:</strong> 柔和的白色星点闪烁</li>
                    <li><strong>悬停状态:</strong> 星点变大变亮，动画加速</li>
                    <li><strong>点击状态:</strong> 惊喜爆发效果</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 调试脚本
        console.log('=== 金色按钮白色星光调试 ===');
        
        // 检查CSS规则是否存在
        function checkCSSRule(selector) {
            const sheets = document.styleSheets;
            for (let i = 0; i < sheets.length; i++) {
                try {
                    const rules = sheets[i].cssRules || sheets[i].rules;
                    for (let j = 0; j < rules.length; j++) {
                        if (rules[j].selectorText && rules[j].selectorText.includes(selector)) {
                            console.log(`✅ 找到CSS规则: ${rules[j].selectorText}`);
                            return true;
                        }
                    }
                } catch (e) {
                    console.log(`⚠️ 无法访问样式表 ${i}: ${e.message}`);
                }
            }
            console.log(`❌ 未找到CSS规则: ${selector}`);
            return false;
        }

        // 检查关键CSS规则
        setTimeout(() => {
            console.log('检查金色按钮白色星光CSS规则...');
            checkCSSRule('btn-primary::before');
            checkCSSRule('btn-primary::after');
            
            // 检查动画定义
            checkCSSRule('randomTwinkle1');
            checkCSSRule('randomTwinkle2');
            checkCSSRule('randomTwinkle3');
            checkCSSRule('whiteStarSweep');
            
            // 检查按钮元素
            const button = document.querySelector('.btn-primary');
            if (button) {
                console.log('✅ 找到按钮元素');
                console.log('按钮类名:', button.className);
                console.log('按钮样式:', window.getComputedStyle(button));
            } else {
                console.log('❌ 未找到按钮元素');
            }
        }, 1000);
    </script>
</body>
</html>
