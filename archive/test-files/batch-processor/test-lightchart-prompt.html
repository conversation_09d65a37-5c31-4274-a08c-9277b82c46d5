<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LightChart Prompt 完整指南测试</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { margin: 30px 0; padding: 20px; border-left: 4px solid #007acc; background: #f8f9fa; }
        .critical { border-left-color: #dc3545; background: #fff5f5; }
        .success { border-left-color: #28a745; background: #f8fff8; }
        .warning { border-left-color: #ffc107; background: #fffdf5; }
        .code { background: #f1f3f4; padding: 15px; border-radius: 5px; font-family: 'Courier New', monospace; margin: 10px 0; }
        .wrong { background: #ffebee; color: #c62828; }
        .correct { background: #e8f5e8; color: #2e7d32; }
        h1, h2, h3 { color: #333; }
        .checklist { list-style: none; padding: 0; }
        .checklist li { padding: 5px 0; }
        .checklist li:before { content: "✓ "; color: #28a745; font-weight: bold; }
        .error-pattern { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 LightChart TTML 完整实战指南</h1>
        <p><strong>目标：</strong>让AI完全掌握LightChart在TTML中的正确使用方法，避免所有常见陷阱</p>

        <div class="section critical">
            <h2>🚨 关键区别：LightChart vs ECharts</h2>
            <div class="code wrong">
                <strong>❌ ECharts风格（AI经常错误使用）</strong><br>
                // 构造函数<br>
                const chart = echarts.init(document.getElementById('container'));<br><br>
                // 数据模型<br>
                option = {<br>
                &nbsp;&nbsp;xAxis: { data: ['Jan', 'Feb'] },<br>
                &nbsp;&nbsp;series: [{ data: [100, 200] }]<br>
                }
            </div>
            <div class="code correct">
                <strong>✅ LightChart正确方式</strong><br>
                // 构造函数 - 必须三个参数<br>
                const chart = new LynxChart({ canvasName: 'uniqueId', width: 400, height: 300 });<br><br>
                // 数据模型 - 统一data + encode<br>
                option = {<br>
                &nbsp;&nbsp;data: [{ month: 'Jan', value: 100 }, { month: 'Feb', value: 200 }],<br>
                &nbsp;&nbsp;series: [{ type: 'line', encode: { x: 'month', y: 'value' } }]<br>
                }
            </div>
        </div>

        <div class="section critical">
            <h2>🔥 实例存储致命错误</h2>
            <div class="error-pattern">
                <strong>常见错误：</strong>将图表实例存储在 this.data 中<br>
                <strong>后果：</strong>setData() 会清空图表实例，导致图表失效<br>
                <strong>症状：</strong>图表闪现后消失，或者更新时无响应
            </div>
            <div class="code wrong">
                ❌ 错误存储方式<br>
                this.setData({ chart: new LynxChart(...) });
            </div>
            <div class="code correct">
                ✅ 正确存储方式<br>
                this.chart = new LynxChart(...);
            </div>
        </div>

        <div class="section warning">
            <h2>⚠️ 多图表冲突问题</h2>
            <p>每个图表都需要：</p>
            <ul>
                <li><strong>唯一的canvasName</strong> - 全局唯一标识符</li>
                <li><strong>独立的实例变量</strong> - 避免实例覆盖</li>
                <li><strong>独立的初始化方法</strong> - 避免事件绑定冲突</li>
            </ul>
            <div class="code">
                // TTML模板<br>
                &lt;lightcharts-canvas canvasName="trajectory" bindinitchart="initTrajectory"/&gt;<br>
                &lt;lightcharts-canvas canvasName="velocity" bindinitchart="initVelocity"/&gt;<br><br>
                // JavaScript<br>
                Card({<br>
                &nbsp;&nbsp;trajectoryChart: null,<br>
                &nbsp;&nbsp;velocityChart: null,<br>
                &nbsp;&nbsp;initTrajectory(e) { this.trajectoryChart = new LynxChart(e.detail); },<br>
                &nbsp;&nbsp;initVelocity(e) { this.velocityChart = new LynxChart(e.detail); }<br>
                });
            </div>
        </div>

        <div class="section">
            <h2>📊 数据模型详解</h2>
            <h3>坐标系图表（Line, Bar, Scatter）</h3>
            <div class="code">
                option = {<br>
                &nbsp;&nbsp;data: [<br>
                &nbsp;&nbsp;&nbsp;&nbsp;{ x: 0, y: 10, type: 'trajectory' },<br>
                &nbsp;&nbsp;&nbsp;&nbsp;{ x: 5, y: 15, type: 'trajectory' },<br>
                &nbsp;&nbsp;&nbsp;&nbsp;{ x: 10, y: 0, type: 'key' }<br>
                &nbsp;&nbsp;],<br>
                &nbsp;&nbsp;series: [<br>
                &nbsp;&nbsp;&nbsp;&nbsp;{ type: 'line', encode: { x: 'x', y: 'y' } },<br>
                &nbsp;&nbsp;&nbsp;&nbsp;{ type: 'scatter', encode: { x: 'x', y: 'y' } }<br>
                &nbsp;&nbsp;]<br>
                };
            </div>
            <h3>饼图（Pie）</h3>
            <div class="code">
                option = {<br>
                &nbsp;&nbsp;series: [{<br>
                &nbsp;&nbsp;&nbsp;&nbsp;type: 'pie',<br>
                &nbsp;&nbsp;&nbsp;&nbsp;data: [<br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{ name: 'A', value: 30 },<br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{ name: 'B', value: 70 }<br>
                &nbsp;&nbsp;&nbsp;&nbsp;]<br>
                &nbsp;&nbsp;}]<br>
                };
            </div>
        </div>

        <div class="section critical">
            <h2>🚫 JSON序列化约束</h2>
            <p>所有图表配置都会经过 JSON.stringify()，因此：</p>
            <div class="code wrong">
                ❌ 禁止使用函数<br>
                tooltip: {<br>
                &nbsp;&nbsp;formatter: function(params) { return params.value; }<br>
                }
            </div>
            <div class="code correct">
                ✅ 使用字符串模板<br>
                tooltip: {<br>
                &nbsp;&nbsp;formatter: '{b}: {c}'<br>
                }
            </div>
        </div>

        <div class="section success">
            <h2>✅ 完整生命周期管理</h2>
            <div class="code">
                Card({<br>
                &nbsp;&nbsp;chart: null, // 存储在组件实例<br><br>
                &nbsp;&nbsp;initChart(e) {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;const { canvasName, width, height } = e.detail;<br>
                &nbsp;&nbsp;&nbsp;&nbsp;this.chart = new LynxChart({ canvasName, width, height });<br>
                &nbsp;&nbsp;&nbsp;&nbsp;setTimeout(() => this.updateChart(), 100);<br>
                &nbsp;&nbsp;},<br><br>
                &nbsp;&nbsp;updateChart() {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;if (!this.chart) return;<br>
                &nbsp;&nbsp;&nbsp;&nbsp;const option = { /* 配置 */ };<br>
                &nbsp;&nbsp;&nbsp;&nbsp;this.chart.setOption(option);<br>
                &nbsp;&nbsp;},<br><br>
                &nbsp;&nbsp;onUnload() {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;if (this.chart) {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;this.chart.destroy();<br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;this.chart = null;<br>
                &nbsp;&nbsp;&nbsp;&nbsp;}<br>
                &nbsp;&nbsp;}<br>
                });
            </div>
        </div>

        <div class="section warning">
            <h2>🐛 常见错误症状及解决方案</h2>
            <div class="error-pattern">
                <strong>症状：</strong>图表空白，无错误提示<br>
                <strong>原因：</strong>数据模型错误（饼图使用了坐标系格式）<br>
                <strong>解决：</strong>检查数据是在 option.data 还是 series.data 中
            </div>
            <div class="error-pattern">
                <strong>症状：</strong>图表闪现后消失<br>
                <strong>原因：</strong>实例存储在 this.data 中被 setData() 清空<br>
                <strong>解决：</strong>改为 this.chart = new LynxChart(...)
            </div>
            <div class="error-pattern">
                <strong>症状：</strong>多图表相互干扰<br>
                <strong>原因：</strong>canvasName 重复或实例变量冲突<br>
                <strong>解决：</strong>确保 canvasName 唯一，使用独立实例变量
            </div>
            <div class="error-pattern">
                <strong>症状：</strong>配置无效或报错<br>
                <strong>原因：</strong>配置中包含函数或不可序列化对象<br>
                <strong>解决：</strong>用 JSON.stringify(option) 验证配置
            </div>
        </div>

        <div class="section">
            <h2>🔧 调试检查列表</h2>
            <ol class="checklist">
                <li>验证 canvasName 唯一性</li>
                <li>检查实例存储位置（component instance vs this.data）</li>
                <li>确认数据模型匹配图表类型</li>
                <li>验证配置可序列化（JSON.stringify 测试）</li>
                <li>检查初始化时序（使用 setTimeout）</li>
                <li>确认生命周期管理（onUnload 清理）</li>
                <li>验证 TTML 组件注册</li>
                <li>检查事件绑定方法名匹配</li>
            </ol>
        </div>

        <div class="section success">
            <h2>🎯 AI生成代码终极检查清单</h2>
            <div class="checklist">
                <li>Import: <code>import LynxChart from "@byted/lynx-lightcharts/src/chart";</code></li>
                <li>Component: 在 index.json 中注册 lightcharts-canvas</li>
                <li>Template: 使用唯一 canvasName 和正确的 bindinitchart</li>
                <li>Instance: 存储在组件实例，不在 this.data 中</li>
                <li>Data: 数据模型匹配图表类型（coordinate vs series）</li>
                <li>Config: 配置对象中无函数，使用字符串模板</li>
                <li>Timing: 使用 setTimeout 延迟初始化</li>
                <li>Cleanup: 实现 onUnload 方法调用 destroy()</li>
                <li>Debug: 添加 console.log 便于调试</li>
                <li>Validation: 用 JSON.stringify 验证配置</li>
            </div>
        </div>

        <div class="section">
            <h2>📝 AI生成代码模板</h2>
            <div class="code">
                <strong>标准单图表模板：</strong><br><br>
                // index.json<br>
                {<br>
                &nbsp;&nbsp;"usingComponents": {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;"lightcharts-canvas": "@byted/lynx-lightcharts/lightcharts-canvas/lightcharts-canvas"<br>
                &nbsp;&nbsp;}<br>
                }<br><br>
                // index.ttml<br>
                &lt;lightcharts-canvas<br>
                &nbsp;&nbsp;canvasName="myChart"<br>
                &nbsp;&nbsp;bindinitchart="initChart"<br>
                &nbsp;&nbsp;style="width: 100%; height: 400px;"/&gt;<br><br>
                // index.js<br>
                import LynxChart from "@byted/lynx-lightcharts/src/chart";<br><br>
                Card({<br>
                &nbsp;&nbsp;chart: null,<br><br>
                &nbsp;&nbsp;initChart(e) {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;const { canvasName, width, height } = e.detail;<br>
                &nbsp;&nbsp;&nbsp;&nbsp;this.chart = new LynxChart({ canvasName, width, height });<br>
                &nbsp;&nbsp;&nbsp;&nbsp;setTimeout(() => this.updateChart(), 100);<br>
                &nbsp;&nbsp;},<br><br>
                &nbsp;&nbsp;updateChart() {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;if (!this.chart) return;<br>
                &nbsp;&nbsp;&nbsp;&nbsp;const option = { /* 图表配置 */ };<br>
                &nbsp;&nbsp;&nbsp;&nbsp;this.chart.setOption(option);<br>
                &nbsp;&nbsp;},<br><br>
                &nbsp;&nbsp;onUnload() {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;if (this.chart) {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;this.chart.destroy();<br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;this.chart = null;<br>
                &nbsp;&nbsp;&nbsp;&nbsp;}<br>
                &nbsp;&nbsp;}<br>
                });
            </div>
        </div>

        <div class="section critical">
            <h2>⚡ 抛物线运动Demo失败分析</h2>
            <p>基于源码分析，图表渲染失败的根本原因：</p>
            <ol>
                <li><strong>数据模型混用：</strong>坐标系图表错误使用了 option.data + series.encode，但 series 中数据格式不统一</li>
                <li><strong>实例管理错误：</strong>可能存在实例存储位置或生命周期管理问题</li>
                <li><strong>Canvas初始化：</strong>canvasName 冲突或参数验证不足</li>
                <li><strong>配置序列化：</strong>模板字符串中可能包含不可序列化的内容</li>
            </ol>
            <p><strong>建议修复步骤：</strong></p>
            <ol>
                <li>确保所有图表数据使用统一的数组格式：<code>[[x, y], [x, y]]</code></li>
                <li>验证实例存储在组件属性而非 this.data</li>
                <li>检查 canvasName 唯一性</li>
                <li>用 JSON.stringify 验证所有配置</li>
            </ol>
        </div>
    </div>
</body>
</html>