<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lynx Preview Debug Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .iframe-container {
            border: 1px solid #ccc;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        iframe {
            width: 100%;
            height: 500px;
            border: none;
        }
    </style>
</head>
<body>
    <h1>🔍 Lynx Preview Debug Analysis</h1>
    
    <div class="debug-section">
        <h2>🧪 Debug Tests</h2>
        <button onclick="testDirectAccess()">Test Direct Access</button>
        <button onclick="testJavaScriptErrors()">Check JS Errors</button>
        <button onclick="testNetworkRequests()">Test Network</button>
        <button onclick="reloadIframe()">Reload Preview</button>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>

    <div class="debug-section">
        <h2>📊 Test Results</h2>
        <div id="testResults"></div>
    </div>

    <div class="debug-section">
        <h2>🖼️ Lynx Preview iframe Test</h2>
        <div class="iframe-container">
            <iframe 
                id="previewFrame" 
                src="http://localhost:8082/lynx_preview"
                title="Lynx Preview Test"
                onload="handleIframeLoad()"
                onerror="handleIframeError()">
            </iframe>
        </div>
    </div>

    <div class="debug-section">
        <h2>📝 Debug Logs</h2>
        <div id="debugLogs"></div>
    </div>

    <script>
        let logContainer = document.getElementById('debugLogs');
        let resultsContainer = document.getElementById('testResults');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `status ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function addResult(test, status, details) {
            const result = document.createElement('div');
            result.className = `status ${status}`;
            result.innerHTML = `
                <strong>${test}:</strong> ${status.toUpperCase()}
                ${details ? `<br><small>${details}</small>` : ''}
            `;
            resultsContainer.appendChild(result);
        }

        async function testDirectAccess() {
            log('🔍 Testing direct access to /lynx_preview', 'info');
            
            try {
                const response = await fetch('http://localhost:8082/lynx_preview');
                const text = await response.text();
                
                if (response.ok) {
                    log(`✅ HTTP ${response.status}: Response received`, 'success');
                    addResult('Direct Access', 'success', `HTTP ${response.status}, Content length: ${text.length}`);
                    
                    // Check for specific patterns that might indicate issues
                    if (text.includes('SyntaxError')) {
                        log('⚠️ Found SyntaxError in response', 'warning');
                        addResult('Response Analysis', 'warning', 'SyntaxError found in response');
                    }
                    
                    if (text.includes('lynx_preview/page.js')) {
                        log('✅ Found lynx_preview JS reference', 'success');
                        addResult('JS Reference', 'success', 'lynx_preview/page.js found in HTML');
                    } else {
                        log('⚠️ No lynx_preview JS reference found', 'warning');
                        addResult('JS Reference', 'warning', 'lynx_preview/page.js not found');
                    }
                } else {
                    log(`❌ HTTP ${response.status}: ${response.statusText}`, 'error');
                    addResult('Direct Access', 'error', `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                log(`❌ Network error: ${error.message}`, 'error');
                addResult('Direct Access', 'error', error.message);
            }
        }

        async function testJavaScriptErrors() {
            log('🔍 Checking for JavaScript errors', 'info');
            
            // Listen for global errors
            window.addEventListener('error', (event) => {
                log(`❌ JS Error: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
                addResult('JS Error Detection', 'error', `${event.message} at line ${event.lineno}`);
            });

            // Test if specific JS files are loading
            try {
                const jsResponse = await fetch('http://localhost:8082/static/js/async/lynx_preview/page.js');
                if (jsResponse.ok) {
                    log('✅ lynx_preview/page.js is accessible', 'success');
                    addResult('JS File Access', 'success', 'lynx_preview/page.js loads successfully');
                } else {
                    log(`⚠️ lynx_preview/page.js returned ${jsResponse.status}`, 'warning');
                    addResult('JS File Access', 'warning', `HTTP ${jsResponse.status}`);
                }
            } catch (error) {
                log(`❌ Failed to load lynx_preview/page.js: ${error.message}`, 'error');
                addResult('JS File Access', 'error', error.message);
            }
        }

        async function testNetworkRequests() {
            log('🔍 Testing network connectivity', 'info');
            
            const testUrls = [
                'http://localhost:8082/',
                'http://localhost:8082/batch_processor',
                'http://localhost:8082/lynx_preview',
                'http://localhost:8082/static/js/main.js'
            ];

            for (const url of testUrls) {
                try {
                    const response = await fetch(url, { method: 'HEAD' });
                    if (response.ok) {
                        log(`✅ ${url} - HTTP ${response.status}`, 'success');
                    } else {
                        log(`⚠️ ${url} - HTTP ${response.status}`, 'warning');
                    }
                } catch (error) {
                    log(`❌ ${url} - ${error.message}`, 'error');
                }
            }
        }

        function handleIframeLoad() {
            log('✅ iframe loaded successfully', 'success');
            addResult('iframe Load', 'success', 'iframe content loaded');
            
            // Try to inspect iframe content
            try {
                const iframe = document.getElementById('previewFrame');
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                if (iframeDoc && iframeDoc.body) {
                    const bodyContent = iframeDoc.body.innerHTML;
                    if (bodyContent.trim()) {
                        log(`✅ iframe has content (${bodyContent.length} chars)`, 'success');
                        addResult('iframe Content', 'success', `${bodyContent.length} characters`);
                    } else {
                        log('⚠️ iframe body is empty', 'warning');
                        addResult('iframe Content', 'warning', 'Empty body content');
                    }
                } else {
                    log('⚠️ Cannot access iframe content (CORS or loading)', 'warning');
                    addResult('iframe Content', 'warning', 'Content not accessible');
                }
            } catch (error) {
                log(`⚠️ iframe content inspection failed: ${error.message}`, 'warning');
                addResult('iframe Content', 'warning', error.message);
            }
        }

        function handleIframeError() {
            log('❌ iframe failed to load', 'error');
            addResult('iframe Load', 'error', 'Failed to load iframe');
        }

        function reloadIframe() {
            log('🔄 Reloading iframe...', 'info');
            const iframe = document.getElementById('previewFrame');
            iframe.src = iframe.src;
        }

        function clearLogs() {
            logContainer.innerHTML = '';
            resultsContainer.innerHTML = '';
            log('🧹 Logs cleared', 'info');
        }

        // Auto-run initial tests
        window.addEventListener('load', () => {
            log('🚀 Debug page loaded, starting automatic tests...', 'info');
            setTimeout(testDirectAccess, 1000);
            setTimeout(testJavaScriptErrors, 2000);
        });

        // Monitor for console errors
        const originalConsoleError = console.error;
        console.error = function(...args) {
            log(`🐛 Console Error: ${args.join(' ')}`, 'error');
            originalConsoleError.apply(console, args);
        };
    </script>
</body>
</html>