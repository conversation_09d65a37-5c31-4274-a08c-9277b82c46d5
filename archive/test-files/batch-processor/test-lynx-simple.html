<!DOCTYPE html>
<html>
<head>
    <title>Simple Lynx Test</title>
    <style>
        body { font-family: sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; border-radius: 4px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Lynx Preview Test</h1>
        <div id="status" class="status info">正在加载 Lynx Preview...</div>
        
        <h2>测试步骤：</h2>
        <ol>
            <li>检查下方 iframe 是否显示内容</li>
            <li>如果看到空白页面，打开浏览器开发者工具查看错误</li>
            <li>检查网络请求是否正常</li>
        </ol>
        
        <iframe 
            src="http://localhost:8082/lynx_preview" 
            onload="handleLoad()" 
            onerror="handleError()">
        </iframe>
    </div>

    <script>
        function handleLoad() {
            document.getElementById('status').innerHTML = '✅ iframe 加载完成';
            document.getElementById('status').className = 'status success';
        }
        
        function handleError() {
            document.getElementById('status').innerHTML = '❌ iframe 加载失败';
            document.getElementById('status').className = 'status error';
        }
        
        // 测试直接访问
        fetch('http://localhost:8082/lynx_preview')
            .then(response => {
                if (response.ok) {
                    console.log('✅ Direct fetch successful:', response.status);
                    return response.text();
                } else {
                    console.error('❌ Direct fetch failed:', response.status);
                    throw new Error(`HTTP ${response.status}`);
                }
            })
            .then(html => {
                console.log('📄 Response length:', html.length);
                if (html.includes('lynx-preview-container')) {
                    console.log('✅ Found lynx-preview-container in response');
                } else {
                    console.warn('⚠️ lynx-preview-container not found');
                }
            })
            .catch(error => {
                console.error('❌ Fetch error:', error);
            });
    </script>
</body>
</html>