<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主按钮样式测试</title>
    <style>
        /* 导入完整样式系统 */
        @import url('./styles/index.css');
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            background: #fafbfc;
        }
        
        .test-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #1f2937;
        }
        
        .button-demo {
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .demo-label {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 12px;
            color: #6b7280;
        }
        
        .status-info {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 8px;
            padding: 8px 12px;
            background: #f9fafb;
            border-radius: 6px;
            border-left: 3px solid #3b82f6;
        }
        
        /* 确保应用批处理器布局类 */
        .batch-processor-layout {
            width: 100%;
        }
        
        /* 图标样式 */
        .icon {
            width: 16px;
            height: 16px;
            display: inline-block;
            flex-shrink: 0;
        }
        
        .icon-lightning::before {
            content: "⚡";
        }
        
        /* 调试信息样式 */
        .debug-info {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 16px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #374151;
        }
        
        .debug-info h4 {
            margin: 0 0 8px 0;
            color: #1f2937;
            font-size: 14px;
        }
        
        .debug-info pre {
            margin: 0;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="test-container batch-processor-layout">
        <h1 style="text-align: center; color: #1f2937; margin-bottom: 40px; font-size: 2rem;">
            🎯 主按钮样式测试
        </h1>
        
        <!-- 主按钮测试 -->
        <div class="test-section">
            <div class="test-title">
                主按钮样式测试 (.btn-primary)
            </div>
            
            <div class="button-demo">
                <div class="demo-label">正常状态</div>
                <button class="btn-primary">
                    <div class="button-content">
                        <div class="button-icon">
                            <span class="icon icon-lightning"></span>
                        </div>
                        <div class="button-text">
                            处理 35 条查询
                        </div>
                    </div>
                </button>
                <div class="status-info">
                    应该显示金色渐变背景，深棕色文字
                </div>
            </div>
            
            <div class="button-demo">
                <div class="demo-label">带进度徽章</div>
                <button class="btn-primary">
                    <div class="button-content">
                        <div class="button-icon">
                            <span class="icon icon-lightning"></span>
                        </div>
                        <div class="button-text">
                            处理中 25/35
                            <span class="progress-badge">71%</span>
                        </div>
                    </div>
                </button>
                <div class="status-info">
                    应该显示金色渐变背景，带有进度徽章
                </div>
            </div>
            
            <div class="button-demo">
                <div class="demo-label">处理中状态 (带脉冲动画)</div>
                <button class="btn-primary processing">
                    <div class="button-content">
                        <div class="button-icon">
                            <span class="icon">⚡</span>
                        </div>
                        <div class="button-text">
                            处理中...
                            <span class="progress-badge">85%</span>
                        </div>
                    </div>
                </button>
                <div class="status-info">
                    应该显示脉冲动画效果
                </div>
            </div>
            
            <div class="button-demo">
                <div class="demo-label">禁用状态</div>
                <button class="btn-primary" disabled>
                    <div class="button-content">
                        <div class="button-icon">
                            <span class="icon icon-lightning"></span>
                        </div>
                        <div class="button-text">
                            无查询可处理
                        </div>
                    </div>
                </button>
                <div class="status-info">
                    应该显示灰色背景，禁用状态
                </div>
            </div>
        </div>
        
        <!-- 样式调试信息 -->
        <div class="test-section">
            <div class="test-title">
                样式调试信息
            </div>
            
            <div class="debug-info">
                <h4>CSS 选择器优先级:</h4>
                <pre>body .batch-processor-layout .btn-primary (权重: 0,0,3,1)
body .batch-processor-layout button.btn-primary (权重: 0,0,3,2)</pre>
            </div>
            
            <div class="debug-info">
                <h4>预期样式属性:</h4>
                <pre>background: linear-gradient(135deg, #fef3c7 0%, #fde68a 25%, #fcd34d 50%, #f59e0b 75%, #d97706 100%) !important;
color: #92400e !important;
padding: 14px 24px !important;
min-height: 48px !important;
border-radius: 10px !important;</pre>
            </div>
            
            <div class="debug-info">
                <h4>检查步骤:</h4>
                <pre>1. 打开浏览器开发者工具 (F12)
2. 选择主按钮元素
3. 查看 Computed 样式
4. 确认 background 和 color 属性值
5. 检查是否有其他样式覆盖</pre>
            </div>
        </div>
        
        <!-- 原始按钮对比 -->
        <div class="test-section">
            <div class="test-title">
                原始样式对比
            </div>
            
            <div class="button-demo">
                <div class="demo-label">原始蓝色按钮 (未优化)</div>
                <button style="
                    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                    color: white;
                    padding: 14px 24px;
                    border: none;
                    border-radius: 10px;
                    font-size: 15px;
                    font-weight: 600;
                    display: inline-flex;
                    align-items: center;
                    gap: 8px;
                    cursor: pointer;
                ">
                    <span class="icon icon-lightning"></span>
                    处理 35 条查询
                </button>
                <div class="status-info">
                    原始蓝色样式，用于对比
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 添加一些调试信息
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.btn-primary');
            buttons.forEach((button, index) => {
                console.log(`Button ${index + 1}:`, {
                    element: button,
                    computedStyle: window.getComputedStyle(button),
                    background: window.getComputedStyle(button).background,
                    color: window.getComputedStyle(button).color
                });
            });
        });
    </script>
</body>
</html>
