<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化后的图标动画测试</title>
    <style>
        body {
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 35%, #f9fafb 65%, #f0f9ff 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .title {
            text-align: center;
            color: #1e40af;
            margin-bottom: 40px;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .demo-section {
            text-align: center;
            padding: 30px;
            background: rgba(248, 250, 252, 0.8);
            border-radius: 16px;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .demo-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #374151;
        }

        /* 原始动画样式 */
        .icon-original {
            position: relative;
            overflow: visible;
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 25%, #60a5fa 75%, #93c5fd 100%);
            border-radius: 50%;
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), 0 0 40px rgba(147, 197, 253, 0.2);
            animation: originalBreathe 4s ease-in-out infinite;
            transition: all 0.3s ease;
            width: 96px;
            height: 96px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }

        .icon-original:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.6), 0 0 40px rgba(255, 255, 255, 0.5);
        }

        /* 优化后的动画样式 */
        .icon-optimized {
            position: relative;
            overflow: visible;
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 25%, #60a5fa 75%, #93c5fd 100%);
            border-radius: 50%;
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.2), 0 0 30px rgba(147, 197, 253, 0.15);
            animation: ultraGentleIconBreathe 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            transition: all 0.7s cubic-bezier(0.4, 0, 0.6, 1);
            width: 96px;
            height: 96px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            will-change: transform, box-shadow;
            backface-visibility: hidden;
        }

        .icon-optimized:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.45), 0 0 35px rgba(255, 255, 255, 0.4), 0 0 50px rgba(59, 130, 246, 0.3);
            animation: ultraGentleIconBreathe 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .icon-optimized:hover::before {
            animation: hoverStarTwinkle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            background:
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.9) 2px, rgba(219, 234, 254, 0.5) 4px, transparent 6px),
                radial-gradient(circle at 70% 20%, rgba(147, 197, 253, 0.8) 2px, rgba(96, 165, 250, 0.4) 4px, transparent 6px),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.8) 2px, rgba(219, 234, 254, 0.4) 4px, transparent 6px);
        }

        /* 星光效果 */
        .icon-original::before,
        .icon-optimized::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            pointer-events: none;
            z-index: 1;
        }

        .icon-original::before {
            background:
                radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.9) 2px, rgba(219, 234, 254, 0.5) 4px, transparent 6px),
                radial-gradient(circle at 75% 25%, rgba(147, 197, 253, 0.8) 2px, rgba(96, 165, 250, 0.4) 4px, transparent 6px),
                radial-gradient(circle at 85% 75%, rgba(255, 255, 255, 0.8) 2px, rgba(219, 234, 254, 0.4) 4px, transparent 6px);
            animation: originalStarTwinkle 3s ease-in-out infinite;
        }

        .icon-optimized::before {
            background:
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.6) 1px, rgba(219, 234, 254, 0.3) 3px, transparent 5px),
                radial-gradient(circle at 70% 20%, rgba(147, 197, 253, 0.5) 1px, rgba(96, 165, 250, 0.25) 3px, transparent 5px),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.5) 1px, rgba(219, 234, 254, 0.25) 3px, transparent 5px);
            animation: ultraGentleStarTwinkle 5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        /* 图标样式 */
        .demo-icon {
            font-size: 4rem;
            color: #FBCD2C;
            position: relative;
            z-index: 2;
        }

        /* 原始动画关键帧 */
        @keyframes originalBreathe {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), 0 0 40px rgba(147, 197, 253, 0.2);
            }
            50% {
                transform: scale(1.02);
                box-shadow: 0 0 30px rgba(59, 130, 246, 0.4), 0 0 60px rgba(147, 197, 253, 0.3);
            }
        }

        @keyframes originalStarTwinkle {
            0%, 100% {
                opacity: 0.8;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
        }

        /* 优化后的动画关键帧 - 增强版本 */
        @keyframes ultraGentleIconBreathe {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.25), 0 0 40px rgba(147, 197, 253, 0.2);
            }
            25% {
                transform: scale(1.015);
                box-shadow: 0 0 25px rgba(59, 130, 246, 0.35), 0 0 50px rgba(147, 197, 253, 0.25);
            }
            50% {
                transform: scale(1.03);
                box-shadow: 0 0 30px rgba(59, 130, 246, 0.4), 0 0 60px rgba(147, 197, 253, 0.3);
            }
            75% {
                transform: scale(1.015);
                box-shadow: 0 0 25px rgba(59, 130, 246, 0.35), 0 0 50px rgba(147, 197, 253, 0.25);
            }
        }

        @keyframes ultraGentleStarTwinkle {
            0%, 100% {
                opacity: 0.5;
                transform: scale(0.9);
            }
            20% {
                opacity: 0.7;
                transform: scale(0.95);
            }
            40% {
                opacity: 0.8;
                transform: scale(1);
            }
            60% {
                opacity: 0.9;
                transform: scale(1.05);
            }
            80% {
                opacity: 0.8;
                transform: scale(1.02);
            }
        }

        @keyframes hoverStarTwinkle {
            0%, 100% {
                opacity: 0.7;
                transform: scale(0.95);
                filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.6));
            }
            25% {
                opacity: 0.9;
                transform: scale(1);
                filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
            }
            50% {
                opacity: 1;
                transform: scale(1.08);
                filter: drop-shadow(0 0 12px rgba(255, 255, 255, 1));
            }
            75% {
                opacity: 0.9;
                transform: scale(1.03);
                filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.9));
            }
        }

        .description {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .features {
            text-align: left;
            background: rgba(255, 255, 255, 0.7);
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
        }

        .features h4 {
            color: #374151;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .features ul {
            margin: 0;
            padding-left: 20px;
            color: #6b7280;
        }

        .features li {
            margin-bottom: 5px;
        }

        .highlight {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border: 2px solid #3b82f6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="title">🎭 图标动画优化对比</h1>
        
        <div class="comparison-grid">
            <div class="demo-section">
                <h3 class="demo-title">原始动画</h3>
                <div class="icon-original">
                    <div class="demo-icon">⭐</div>
                </div>
                <p class="description">
                    较为激进的动画效果，缩放幅度大，过渡较快
                </p>
                <div class="features">
                    <h4>特点：</h4>
                    <ul>
                        <li>4秒呼吸周期</li>
                        <li>1.02倍缩放</li>
                        <li>hover时1.1倍缩放</li>
                        <li>3秒星光闪烁</li>
                        <li>ease-in-out缓动</li>
                    </ul>
                </div>
            </div>

            <div class="demo-section highlight">
                <h3 class="demo-title">✨ 优化后动画</h3>
                <div class="icon-optimized">
                    <div class="demo-icon">⭐</div>
                </div>
                <p class="description">
                    更加柔和自然的动画效果，细腻的过渡和缓动
                </p>
                <div class="features">
                    <h4>增强优化特点：</h4>
                    <ul>
                        <li>4秒呼吸周期（适中节奏）</li>
                        <li>1.03倍最大缩放（更明显）</li>
                        <li>hover时1.05倍缩放 + 强化星光</li>
                        <li>5秒星光闪烁（更活跃）</li>
                        <li>hover时2秒快速星光闪烁</li>
                        <li>cubic-bezier缓动函数</li>
                        <li>4阶段渐进式动画</li>
                        <li>增强的透明度和阴影变化</li>
                        <li>0.7秒过渡时间</li>
                        <li>hover时额外的滤镜效果</li>
                    </ul>
                </div>
            </div>
        </div>

        <div style="text-align: center; color: #6b7280; margin-top: 40px;">
            <p><strong>💡 提示：</strong> 将鼠标悬停在图标上体验不同的hover效果</p>
            <p>优化后的动画更加符合现代UI设计的微交互原则，提供更舒适的用户体验</p>
        </div>
    </div>
</body>
</html>
