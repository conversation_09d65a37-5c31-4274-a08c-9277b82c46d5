<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进度条样式测试</title>
    <style>
        /* 直接内联样式，避免导入问题 */

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #fafbfc;
        }
        
        .test-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #1f2937;
        }
        
        .progress-test {
            margin-bottom: 20px;
        }
        
        .progress-label {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 8px;
            color: #374151;
        }
        
        /* 复制indicators.css中的样式 */
        .pastel-progress-container {
            width: 100%;
            height: 6px;
            background: rgba(229, 231, 235, 0.4);
            border-radius: 3px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .pastel-progress-bar {
            height: 100%;
            border-radius: 3px;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            min-width: 2px;
        }

        /* 默认进度条 - 蓝色渐变 */
        .pastel-progress-container .pastel-progress-bar.progress-default {
            background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%) !important;
            box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3) !important;
        }

        /* 成功进度条 - 绿色渐变 */
        .pastel-progress-container .pastel-progress-bar.progress-success {
            background: linear-gradient(90deg, #10b981 0%, #059669 100%) !important;
            box-shadow: 0 1px 3px rgba(16, 185, 129, 0.3) !important;
        }

        /* 错误进度条 - 红色渐变 */
        .pastel-progress-container .pastel-progress-bar.progress-error {
            background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%) !important;
            box-shadow: 0 1px 3px rgba(239, 68, 68, 0.3) !important;
        }

        /* 混合进度条 - 多色渐变 */
        .pastel-progress-container .pastel-progress-bar.progress-mixed {
            background: linear-gradient(90deg, #10b981 0%, #f59e0b 50%, #ef4444 100%) !important;
            box-shadow: 0 1px 3px rgba(245, 158, 11, 0.3) !important;
        }

        /* 进度条光泽效果 */
        .pastel-progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, 
                transparent 0%, 
                rgba(255, 255, 255, 0.4) 50%, 
                transparent 100%);
            animation: progressShine 2s infinite;
            border-radius: 3px;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        .progress-info {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #1f2937; margin-bottom: 30px;">进度条样式测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 默认进度条 (蓝色)</div>
            <div class="progress-test">
                <div class="progress-label">总进度</div>
                <div class="pastel-progress-container">
                    <div class="pastel-progress-bar progress-default" style="width: 75%;"></div>
                </div>
                <div class="progress-info">应该显示蓝色渐变 (#3b82f6 → #1d4ed8)</div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 混合进度条 (多色)</div>
            <div class="progress-test">
                <div class="progress-label">成功率</div>
                <div class="pastel-progress-container">
                    <div class="pastel-progress-bar progress-mixed" style="width: 58%;"></div>
                </div>
                <div class="progress-info">应该显示绿-黄-红渐变 (#10b981 → #f59e0b → #ef4444)</div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 成功进度条 (绿色)</div>
            <div class="progress-test">
                <div class="progress-label">成功任务</div>
                <div class="pastel-progress-container">
                    <div class="pastel-progress-bar progress-success" style="width: 90%;"></div>
                </div>
                <div class="progress-info">应该显示绿色渐变 (#10b981 → #059669)</div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. 错误进度条 (红色)</div>
            <div class="progress-test">
                <div class="progress-label">失败任务</div>
                <div class="pastel-progress-container">
                    <div class="pastel-progress-bar progress-error" style="width: 25%;"></div>
                </div>
                <div class="progress-info">应该显示红色渐变 (#ef4444 → #dc2626)</div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">5. 模拟实际使用场景</div>
            <div class="progress-test">
                <div style="display: flex; justify-content: space-between; font-size: 0.75rem; margin-bottom: 4px; color: #2563eb;">
                    <span>成功率</span>
                    <span style="font-weight: 700; color: #6b7280;">58%</span>
                </div>
                <div class="pastel-progress-container">
                    <div class="pastel-progress-bar progress-mixed" style="width: 58%;"></div>
                </div>
            </div>
            
            <div class="progress-test" style="margin-top: 16px;">
                <div style="display: flex; justify-content: space-between; font-size: 0.75rem; margin-bottom: 4px; color: #2563eb;">
                    <span>总进度</span>
                    <span style="font-weight: 700; color: #6b7280;">100%</span>
                </div>
                <div class="pastel-progress-container">
                    <div class="pastel-progress-bar progress-default" style="width: 100%;"></div>
                </div>
            </div>
            <div class="progress-info">这是页面中实际使用的样式</div>
        </div>
    </div>
</body>
</html>
