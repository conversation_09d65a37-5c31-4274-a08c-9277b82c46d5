<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查询预览滚动测试</title>
    <link rel="stylesheet" href="./styles/index.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #1f2937;
        }
        
        /* 模拟查询预览区域 */
        .mock-query-preview {
            background: linear-gradient(135deg, 
                rgba(239, 246, 255, 0.8) 0%, 
                rgba(248, 250, 252, 0.9) 100%);
            border: 1px solid rgba(59, 130, 246, 0.15);
            border-radius: 8px;
            padding: 12px;
            max-height: 160px;
            overflow-y: auto;
            overflow-x: hidden;
            backdrop-filter: blur(4px);
            box-shadow: inset 0 1px 3px rgba(59, 130, 246, 0.08);
            position: relative;
            z-index: 1;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: thin;
            scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
        }
        
        .mock-query-preview::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        
        .mock-query-preview::-webkit-scrollbar-track {
            background: rgba(59, 130, 246, 0.05);
            border-radius: 3px;
        }
        
        .mock-query-preview::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.3);
            border-radius: 3px;
            transition: background 0.2s ease;
        }
        
        .mock-query-preview::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.5);
        }
        
        .mock-query-item {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(59, 130, 246, 0.08);
            border-radius: 6px;
            padding: 8px 10px;
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(59, 130, 246, 0.04);
            min-height: 36px;
            flex-shrink: 0;
        }
        
        .mock-query-item:hover {
            background: rgba(255, 255, 255, 1);
            border-color: rgba(59, 130, 246, 0.15);
            transform: translateX(2px);
            box-shadow: 0 2px 6px rgba(59, 130, 246, 0.08);
        }
        
        .mock-query-item:last-child {
            margin-bottom: 0;
        }
        
        .mock-query-number {
            background: linear-gradient(135deg, 
                #3b82f6 0%, 
                #2563eb 100%);
            color: white;
            font-size: 0.7rem;
            font-weight: 600;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            flex-shrink: 0;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }
        
        .mock-query-text {
            font-size: 0.75rem;
            color: #374151;
            word-break: break-words;
            flex: 1;
            line-height: 1.4;
        }
        
        .test-info {
            margin-top: 20px;
            padding: 12px;
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            font-size: 14px;
            color: #0c4a6e;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">查询预览滚动测试</h1>
        
        <div class="mock-query-preview">
            <div class="mock-query-item">
                <div class="mock-query-number">1</div>
                <div class="mock-query-text">什么是人工智能？</div>
            </div>
            <div class="mock-query-item">
                <div class="mock-query-number">2</div>
                <div class="mock-query-text">机器学习的基本原理</div>
            </div>
            <div class="mock-query-item">
                <div class="mock-query-number">3</div>
                <div class="mock-query-text">深度学习与神经网络的关系</div>
            </div>
            <div class="mock-query-item">
                <div class="mock-query-number">4</div>
                <div class="mock-query-text">自然语言处理技术的应用场景</div>
            </div>
            <div class="mock-query-item">
                <div class="mock-query-number">5</div>
                <div class="mock-query-text">计算机视觉在现实生活中的应用</div>
            </div>
            <div class="mock-query-item">
                <div class="mock-query-number">6</div>
                <div class="mock-query-text">强化学习的基本概念和算法</div>
            </div>
            <div class="mock-query-item">
                <div class="mock-query-number">7</div>
                <div class="mock-query-text">大语言模型的训练过程</div>
            </div>
            <div class="mock-query-item">
                <div class="mock-query-number">8</div>
                <div class="mock-query-text">AI伦理和安全性考虑</div>
            </div>
            <div class="mock-query-item">
                <div class="mock-query-number">9</div>
                <div class="mock-query-text">量子计算与AI的结合前景</div>
            </div>
            <div class="mock-query-item">
                <div class="mock-query-number">10</div>
                <div class="mock-query-text">边缘计算在AI部署中的作用</div>
            </div>
        </div>
        
        <div class="test-info">
            <strong>测试说明：</strong><br>
            1. 上方区域应该可以垂直滚动<br>
            2. 滚动条应该显示在右侧<br>
            3. 鼠标悬停时项目应该有交互效果<br>
            4. 最大高度限制为160px
        </div>
    </div>
</body>
</html>
