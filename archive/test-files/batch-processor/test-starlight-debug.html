<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>白色星光按钮调试测试</title>
    <link rel="stylesheet" href="../styles/index.css">
    <style>
        body {
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 35%, #f9fafb 65%, #e0f2fe 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            color: #1e3a8a;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .title {
            text-align: center;
            color: #1e3a8a;
            margin-bottom: 40px;
            font-size: 2rem;
            font-weight: 700;
        }

        .button-test {
            display: flex;
            flex-direction: column;
            gap: 30px;
            align-items: center;
        }

        .test-section {
            width: 100%;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            text-align: center;
        }

        .test-section h3 {
            margin-bottom: 20px;
            color: #1e40af;
        }

        .debug-info {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            text-align: left;
            font-family: monospace;
            font-size: 0.9rem;
        }

        /* 强制显示星光效果的调试样式 */
        .debug-starlight {
            position: relative;
            overflow: hidden;
            isolation: isolate;
        }

        .debug-starlight::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 1;
            
            background-image: 
                radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.9) 2px, transparent 4px),
                radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.8) 3px, transparent 5px),
                radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.9) 2px, transparent 4px),
                radial-gradient(circle at 25% 75%, rgba(255, 255, 255, 0.7) 4px, transparent 6px),
                radial-gradient(circle at 45% 35%, rgba(255, 255, 255, 0.6) 2px, transparent 3px),
                radial-gradient(circle at 90% 60%, rgba(255, 255, 255, 0.8) 2px, transparent 4px);
            
            animation: debugTwinkle 2s ease-in-out infinite;
            opacity: 1;
        }

        .debug-starlight::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            pointer-events: none;
            z-index: 2;
            
            background: linear-gradient(
                45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.3) 45%,
                rgba(255, 255, 255, 0.8) 50%,
                rgba(255, 255, 255, 0.3) 55%,
                transparent 70%
            );
            
            animation: debugSweep 3s ease-in-out infinite;
            opacity: 0.8;
        }

        .debug-starlight > * {
            position: relative;
            z-index: 3;
        }

        @keyframes debugTwinkle {
          0%, 100% {
            opacity: 0.5;
            transform: scale(0.8);
          }
          50% {
            opacity: 1;
            transform: scale(1.2);
          }
        }

        @keyframes debugSweep {
          0% {
            transform: translateX(-200%) translateY(-200%) rotate(-45deg);
            opacity: 0;
          }
          50% {
            opacity: 1;
            transform: translateX(0%) translateY(0%) rotate(-45deg);
          }
          100% {
            transform: translateX(200%) translateY(200%) rotate(-45deg);
            opacity: 0;
          }
        }

        .icon-placeholder {
            width: 16px;
            height: 16px;
            background: #3b82f6;
            border-radius: 2px;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="title">🔍 白色星光按钮调试测试</h1>
        
        <div class="button-test">
            <div class="test-section">
                <h3>1. 原始按钮（无星光）</h3>
                <button class="btn btn--secondary-glass btn--lg px-6 py-3 text-base font-medium">
                    <div class="icon-placeholder"></div>
                    使用示例数据
                </button>
                <div class="debug-info">
                    类名: btn btn--secondary-glass btn--lg px-6 py-3 text-base font-medium
                </div>
            </div>

            <div class="test-section">
                <h3>2. 添加白色星光类的按钮</h3>
                <button class="btn btn--secondary-glass btn--lg btn-white-starlight px-6 py-3 text-base font-medium">
                    <div class="icon-placeholder"></div>
                    使用示例数据
                </button>
                <div class="debug-info">
                    类名: btn btn--secondary-glass btn--lg btn-white-starlight px-6 py-3 text-base font-medium<br>
                    应该显示: 白色星点闪烁 + 光芒扫过效果
                </div>
            </div>

            <div class="test-section">
                <h3>3. 强制调试星光效果</h3>
                <button class="btn btn--secondary-glass btn--lg debug-starlight px-6 py-3 text-base font-medium">
                    <div class="icon-placeholder"></div>
                    使用示例数据
                </button>
                <div class="debug-info">
                    类名: btn btn--secondary-glass btn--lg debug-starlight px-6 py-3 text-base font-medium<br>
                    强制显示: 简化版星光效果（用于对比）
                </div>
            </div>

            <div class="test-section">
                <h3>4. 检查CSS加载状态</h3>
                <div class="debug-info">
                    <strong>检查步骤:</strong><br>
                    1. 打开浏览器开发者工具 (F12)<br>
                    2. 查看 Network 标签，确认 index.css 已加载<br>
                    3. 查看 Elements 标签，检查按钮元素的计算样式<br>
                    4. 查看 Console 标签，检查是否有CSS错误<br>
                    5. 在 Elements 中搜索 "btn-white-starlight" 样式规则<br><br>
                    
                    <strong>预期效果:</strong><br>
                    • 按钮2应该显示白色星点闪烁<br>
                    • 按钮2应该有光芒扫过效果<br>
                    • 悬停时效果应该增强<br>
                    • 点击时应该有爆发效果
                </div>
            </div>
        </div>
    </div>

    <script>
        // 调试脚本
        console.log('=== 白色星光按钮调试 ===');
        
        // 检查CSS规则是否存在
        function checkCSSRule(selector) {
            const sheets = document.styleSheets;
            for (let i = 0; i < sheets.length; i++) {
                try {
                    const rules = sheets[i].cssRules || sheets[i].rules;
                    for (let j = 0; j < rules.length; j++) {
                        if (rules[j].selectorText && rules[j].selectorText.includes(selector)) {
                            console.log(`✅ 找到CSS规则: ${rules[j].selectorText}`);
                            return true;
                        }
                    }
                } catch (e) {
                    console.log(`⚠️ 无法访问样式表 ${i}: ${e.message}`);
                }
            }
            console.log(`❌ 未找到CSS规则: ${selector}`);
            return false;
        }

        // 检查关键CSS规则
        setTimeout(() => {
            console.log('检查白色星光CSS规则...');
            checkCSSRule('btn-white-starlight');
            checkCSSRule('btn-white-starlight::before');
            checkCSSRule('btn-white-starlight::after');
            
            // 检查动画定义
            checkCSSRule('randomTwinkle1');
            checkCSSRule('randomTwinkle2');
            checkCSSRule('randomTwinkle3');
        }, 1000);
    </script>
</body>
</html>
