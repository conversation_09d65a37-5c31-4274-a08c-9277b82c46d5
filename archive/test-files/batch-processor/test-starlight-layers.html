<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星光层测试</title>
    <style>
        /* 导入完整样式系统 */
        @import url('./styles/index.css');
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            background: #fafbfc;
            text-align: center;
        }
        
        .test-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #1f2937;
        }
        
        .status-info {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 16px;
            padding: 12px 16px;
            background: #f9fafb;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        
        /* 确保应用批处理器布局类 */
        .batch-processor-layout {
            width: 100%;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
            margin: 16px 0;
            text-align: left;
        }
        
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checklist li:last-child {
            border-bottom: none;
        }
        
        .checklist li::before {
            content: "🔍";
            flex-shrink: 0;
        }
        
        .success-indicator {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        
        .success-indicator h4 {
            margin: 0 0 8px 0;
            color: #065f46;
        }
        
        .success-indicator p {
            margin: 0;
            color: #047857;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container batch-processor-layout">
        <h1 style="text-align: center; color: #1f2937; margin-bottom: 40px; font-size: 2rem;">
            🌟 星光层测试页面
        </h1>
        
        <!-- 主要测试区域 -->}
        <div class="test-section">
            <div class="test-title">
                ✨ 星光层实现测试
            </div>
            
            <div style="margin: 30px 0;">
                <button class="btn-primary sparkle-hover">
                    <!-- 星光效果层 -->
                    <div class="starlight-layer"></div>
                    <!-- 高光扫过效果层 -->
                    <div class="highlight-sweep"></div>
                    <div class="button-content">
                        <span>⚡ 处理 35 条查询 →</span>
                    </div>
                </button>
            </div>
            
            <div class="status-info">
                ✨ 预期效果：应该看到按钮表面有微妙的星光点闪烁，悬停时有白色高光从左上扫向右下
            </div>
        </div>
        
        <!-- 对比测试 -->
        <div class="test-section">
            <div class="test-title">
                🔄 对比测试
            </div>
            
            <div style="display: flex; gap: 20px; justify-content: center; align-items: center; flex-wrap: wrap;">
                <div style="text-align: center;">
                    <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">无星光层</div>
                    <button class="btn-primary">
                        <div class="button-content">
                            <span>普通按钮</span>
                        </div>
                    </button>
                </div>
                
                <div style="text-align: center;">
                    <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">有星光层</div>
                    <button class="btn-primary sparkle-hover">
                        <div class="starlight-layer"></div>
                        <div class="highlight-sweep"></div>
                        <div class="button-content">
                            <span>星光按钮</span>
                        </div>
                    </button>
                </div>
            </div>
            
            <div class="status-info">
                🔍 对比观察：右侧按钮应该有明显的星光闪烁效果，左侧按钮没有
            </div>
        </div>
        
        <!-- 效果检查清单 -->
        <div class="test-section">
            <div class="test-title">
                📋 效果检查清单
            </div>
            
            <div class="success-indicator">
                <h4>🎯 星光层实现方案</h4>
                <p>使用独立的 div 元素作为星光层，避免 CSS 伪元素冲突问题。</p>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4 style="color: #374151; margin-bottom: 12px;">🌟 星光效果检查</h4>
                <ul class="checklist">
                    <li>按钮表面有5个星光点分布在不同位置</li>
                    <li>星光点以2.5秒为周期进行闪烁</li>
                    <li>闪烁动画平滑自然，不刺眼</li>
                    <li>星光颜色为白色和金色调</li>
                </ul>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4 style="color: #374151; margin-bottom: 12px;">✨ 高光扫过检查</h4>
                <ul class="checklist">
                    <li>悬停时有白色高光从左上角扫向右下角</li>
                    <li>高光扫过动画持续0.8秒</li>
                    <li>扫过效果不遮挡按钮文字</li>
                    <li>扫过完成后按钮恢复正常状态</li>
                </ul>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4 style="color: #374151; margin-bottom: 12px;">🎨 视觉层次检查</h4>
                <ul class="checklist">
                    <li>星光层在背景之上，内容之下 (z-index: 1)</li>
                    <li>高光层在星光层之上，内容之下 (z-index: 2)</li>
                    <li>按钮内容在所有特效之上 (z-index: 3)</li>
                    <li>所有层级关系正确，无遮挡问题</li>
                </ul>
            </div>
        </div>
        
        <!-- 技术实现 -->
        <div class="test-section">
            <div class="test-title">
                🔧 技术实现详情
            </div>
            
            <div style="background: #f3f4f6; border-radius: 8px; padding: 16px; font-family: 'Courier New', monospace; font-size: 12px;">
                <h4 style="margin: 0 0 8px 0; color: #1f2937; font-family: inherit;">HTML结构:</h4>
                <pre style="margin: 0; color: #374151;">&lt;button class="btn-primary sparkle-hover"&gt;
  &lt;div class="starlight-layer"&gt;&lt;/div&gt;
  &lt;div class="highlight-sweep"&gt;&lt;/div&gt;
  &lt;div class="button-content"&gt;
    &lt;span&gt;按钮文字&lt;/span&gt;
  &lt;/div&gt;
&lt;/button&gt;</pre>
            </div>
            
            <div style="background: #f3f4f6; border-radius: 8px; padding: 16px; font-family: 'Courier New', monospace; font-size: 12px; margin-top: 12px;">
                <h4 style="margin: 0 0 8px 0; color: #1f2937; font-family: inherit;">CSS关键样式:</h4>
                <pre style="margin: 0; color: #374151;">.starlight-layer {
  position: absolute;
  background: radial-gradient(...);
  animation: starlightTwinkle 2.5s infinite;
  z-index: 1;
}</pre>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px; padding: 20px; background: #e9ecef; border-radius: 8px;">
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
                🎉 如果看到星光效果，说明星光层实现成功！<br>
                这种方法避免了CSS伪元素的冲突问题，确保效果稳定显示。
            </p>
        </div>
    </div>
</body>
</html>
