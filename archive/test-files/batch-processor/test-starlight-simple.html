<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单白色星光测试</title>
    <style>
        body {
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 35%, #f9fafb 65%, #e0f2fe 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            color: #1e3a8a;
        }

        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .title {
            color: #1e3a8a;
            margin-bottom: 40px;
            font-size: 2rem;
            font-weight: 700;
        }

        /* 基础按钮样式 */
        .btn {
            border: none;
            background: none;
            padding: 12px 24px;
            margin: 10px;
            font: inherit;
            color: inherit;
            cursor: pointer;
            outline: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            border-radius: 8px;
            font-weight: 500;
            line-height: 1;
            white-space: nowrap;
            user-select: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-height: 48px;
        }

        .btn--secondary-glass {
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.08) 0%,
                rgba(37, 99, 235, 0.12) 50%,
                rgba(29, 78, 216, 0.15) 100%);
            color: #1e40af;
            border: 1px solid rgba(59, 130, 246, 0.2);
            backdrop-filter: blur(8px);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        /* 强制白色星光效果 - 最高优先级 */
        .btn.btn--secondary-glass.btn-white-starlight {
            position: relative;
            overflow: hidden;
            isolation: isolate;
        }

        .btn.btn--secondary-glass.btn-white-starlight::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 10;
            
            /* 15个白色星点 - 随机分布，不同大小 */
            background-image: 
                /* 大星点 */
                radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
                radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.8) 1.5px, transparent 2.5px),
                radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
                radial-gradient(circle at 25% 75%, rgba(255, 255, 255, 0.7) 2px, transparent 3px),
                radial-gradient(circle at 92% 85%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),
                
                /* 中等星点 */
                radial-gradient(circle at 45% 35%, rgba(255, 255, 255, 0.6) 1px, transparent 1.5px),
                radial-gradient(circle at 90% 60%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),
                radial-gradient(circle at 10% 85%, rgba(255, 255, 255, 0.7) 1px, transparent 1.5px),
                radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
                radial-gradient(circle at 75% 45%, rgba(255, 255, 255, 0.6) 1px, transparent 1.5px),
                
                /* 小星点 */
                radial-gradient(circle at 35% 60%, rgba(255, 255, 255, 0.5) 0.5px, transparent 1px),
                radial-gradient(circle at 80% 40%, rgba(255, 255, 255, 0.6) 0.5px, transparent 1px),
                radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.4) 0.5px, transparent 1px),
                radial-gradient(circle at 55% 85%, rgba(255, 255, 255, 0.7) 0.5px, transparent 1px),
                radial-gradient(circle at 5% 30%, rgba(255, 255, 255, 0.5) 0.5px, transparent 1px);
            
            /* 多层随机闪烁动画 */
            animation: 
                randomTwinkle1 3.2s ease-in-out infinite,
                randomTwinkle2 2.8s ease-in-out infinite 0.5s,
                randomTwinkle3 3.5s ease-in-out infinite 1s;
            opacity: 0.8;
        }

        .btn.btn--secondary-glass.btn-white-starlight::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            pointer-events: none;
            z-index: 11;
            
            /* 白色光芒渐变 */
            background: linear-gradient(
                45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.1) 40%,
                rgba(255, 255, 255, 0.3) 45%,
                rgba(255, 255, 255, 0.6) 50%,
                rgba(255, 255, 255, 0.3) 55%,
                rgba(255, 255, 255, 0.1) 60%,
                transparent 70%
            );
            
            /* 光芒扫过动画 */
            animation: whiteStarSweep 3.5s ease-in-out infinite;
            opacity: 0.8;
            filter: blur(0.5px);
        }

        .btn.btn--secondary-glass.btn-white-starlight > * {
            position: relative;
            z-index: 12;
        }

        /* 动画定义 */
        @keyframes randomTwinkle1 {
          0%, 100% { opacity: 0.3; transform: scale(0.8); }
          15% { opacity: 1; transform: scale(1.4); }
          30% { opacity: 0.2; transform: scale(0.6); }
          45% { opacity: 0.9; transform: scale(1.2); }
          60% { opacity: 0.4; transform: scale(0.9); }
          75% { opacity: 0.8; transform: scale(1.1); }
          90% { opacity: 0.5; transform: scale(0.7); }
        }

        @keyframes randomTwinkle2 {
          0%, 100% { opacity: 0.4; transform: scale(0.9); }
          20% { opacity: 0.8; transform: scale(1.3); }
          40% { opacity: 0.3; transform: scale(0.7); }
          60% { opacity: 1; transform: scale(1.5); }
          80% { opacity: 0.6; transform: scale(1.0); }
        }

        @keyframes randomTwinkle3 {
          0%, 100% { opacity: 0.2; transform: scale(0.6); }
          25% { opacity: 0.9; transform: scale(1.2); }
          50% { opacity: 0.5; transform: scale(0.8); }
          75% { opacity: 1; transform: scale(1.4); }
        }

        @keyframes whiteStarSweep {
          0% {
            transform: translateX(-200%) translateY(-200%) rotate(-45deg);
            opacity: 0;
          }
          20% {
            opacity: 0.6;
          }
          50% {
            opacity: 1;
            transform: translateX(0%) translateY(0%) rotate(-45deg);
          }
          80% {
            opacity: 0.6;
          }
          100% {
            transform: translateX(200%) translateY(200%) rotate(-45deg);
            opacity: 0;
          }
        }

        .icon-placeholder {
            width: 16px;
            height: 16px;
            background: #3b82f6;
            border-radius: 2px;
            margin-right: 8px;
        }

        .test-info {
            background: #f3f4f6;
            padding: 20px;
            border-radius: 12px;
            margin-top: 30px;
            text-align: left;
        }

        .test-info h3 {
            color: #1e40af;
            margin-bottom: 15px;
        }

        .test-info p {
            margin-bottom: 10px;
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="title">✨ 简单白色星光测试</h1>
        
        <div>
            <h3>原始按钮（无星光）</h3>
            <button class="btn btn--secondary-glass">
                <div class="icon-placeholder"></div>
                使用示例数据
            </button>
        </div>

        <div>
            <h3>白色星光按钮</h3>
            <button class="btn btn--secondary-glass btn-white-starlight">
                <div class="icon-placeholder"></div>
                使用示例数据
            </button>
        </div>

        <div class="test-info">
            <h3>🔍 测试说明</h3>
            <p><strong>预期效果：</strong></p>
            <p>• 第二个按钮应该显示白色星点闪烁效果</p>
            <p>• 应该有光芒从左上角扫向右下角</p>
            <p>• 星点应该以不同的速度和节奏闪烁</p>
            <p>• 悬停时效果应该更明显</p>
            
            <p><strong>如果没有效果：</strong></p>
            <p>• 检查浏览器控制台是否有CSS错误</p>
            <p>• 确认CSS文件正确加载</p>
            <p>• 检查CSS选择器优先级</p>
        </div>
    </div>
</body>
</html>
