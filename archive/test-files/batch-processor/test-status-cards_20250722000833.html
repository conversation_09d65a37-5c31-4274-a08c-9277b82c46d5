<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态卡片样式测试</title>
    <style>
        /* 引入相关的CSS变量 */
        :root {
            --color-blue-50: #eff6ff;
            --color-blue-100: #dbeafe;
            --color-blue-200: #bfdbfe;
            --color-blue-300: #93c5fd;
            --color-blue-600: #2563eb;
            --color-success: #10b981;
            --color-error: #ef4444;
            --color-warning: #f59e0b;
            --color-warning-light: rgba(245, 158, 11, 0.1);
            --color-gray-600: #6b7280;
            --color-gray-700: #374151;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        /* 引入状态卡片优化CSS */
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .unified-title-secondary {
            display: flex;
            align-items: center;
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 12px;
            color: #374151;
        }

        .unified-title-icon-secondary {
            margin-right: 8px;
        }

        .icon {
            width: 20px;
            height: 20px;
        }

        .grid {
            display: grid;
        }

        .grid-cols-2 {
            grid-template-columns: repeat(2, 1fr);
        }

        .gap-2 {
            gap: 8px;
        }

        .mb-3 {
            margin-bottom: 12px;
        }

        .text-center {
            text-align: center;
        }

        .p-2 {
            padding: 8px;
        }

        .rounded-lg {
            border-radius: 8px;
        }

        .text-lg {
            font-size: 1.125rem;
        }

        .font-bold {
            font-weight: 700;
        }

        .text-xs {
            font-size: 0.75rem;
        }

        /* 状态卡片样式 */
        .batch-processor-layout .glass-card .grid.grid-cols-2 > div {
            background: transparent;
            border-radius: 8px;
            padding: 12px 8px;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            border: 1px solid rgba(0, 0, 0, 0.08);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
            min-height: 64px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .batch-processor-layout .glass-card .grid.grid-cols-2 > div:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
            border-color: rgba(0, 0, 0, 0.12);
        }

        /* 总数卡片 - 主题蓝色渐变 */
        .batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(1) {
            background: linear-gradient(135deg, 
                var(--color-blue-50) 0%, 
                var(--color-blue-100) 100%);
            border-color: var(--color-blue-200);
        }

        .batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(1):hover {
            background: linear-gradient(135deg, 
                var(--color-blue-100) 0%, 
                var(--color-blue-200) 100%);
            border-color: var(--color-blue-300);
        }

        /* 成功卡片 - 主题蓝色渐变 */
        .batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(2) {
            background: linear-gradient(135deg, 
                var(--color-blue-50) 0%, 
                rgba(59, 130, 246, 0.1) 100%);
            border-color: var(--color-blue-200);
        }

        .batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(2):hover {
            background: linear-gradient(135deg, 
                var(--color-blue-100) 0%, 
                rgba(59, 130, 246, 0.15) 100%);
            border-color: var(--color-blue-300);
        }

        /* 失败卡片 - 主题蓝色渐变 */
        .batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(3) {
            background: linear-gradient(135deg, 
                var(--color-blue-50) 0%, 
                rgba(239, 68, 68, 0.08) 100%);
            border-color: var(--color-blue-200);
        }

        .batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(3):hover {
            background: linear-gradient(135deg, 
                var(--color-blue-100) 0%, 
                rgba(239, 68, 68, 0.12) 100%);
            border-color: var(--color-blue-300);
        }

        /* 等待卡片 - 主题蓝色渐变 */
        .batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(4) {
            background: linear-gradient(135deg, 
                var(--color-blue-50) 0%, 
                rgba(245, 158, 11, 0.08) 100%);
            border-color: var(--color-blue-200);
        }

        .batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(4):hover {
            background: linear-gradient(135deg, 
                var(--color-blue-100) 0%, 
                rgba(245, 158, 11, 0.12) 100%);
            border-color: var(--color-blue-300);
        }

        /* 数值颜色 - 成功卡片也使用主题蓝色 */
        .batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(1) .text-lg {
            color: var(--color-blue-600);
        }

        .batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(2) .text-lg {
            color: var(--color-blue-600);
        }

        .batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(3) .text-lg {
            color: var(--color-error);
        }

        .batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(4) .text-lg {
            color: var(--color-warning);
        }

        /* 标签颜色 */
        .batch-processor-layout .glass-card .grid.grid-cols-2 > div .text-xs {
            color: var(--color-gray-600);
        }

        .batch-processor-layout .glass-card .grid.grid-cols-2 > div:hover .text-xs {
            color: var(--color-gray-700);
        }

        /* 进度条样式 */
        .progress-display {
            margin-top: 20px;
            padding: 16px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .progress-label-success {
            color: var(--color-blue-600);
        }

        .progress-label-primary {
            color: var(--color-blue-600);
        }

        .progress-percentage {
            font-weight: 700;
            color: var(--color-gray-600);
        }

        .pastel-progress-container {
            width: 100%;
            height: 8px;
            background: rgba(229, 231, 235, 0.6);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 4px;
        }

        .pastel-progress-bar {
            height: 100%;
            border-radius: 4px;
            transition: width 0.4s ease;
        }

        .progress-mixed {
            background: linear-gradient(90deg, #10b981 0%, #f59e0b 50%, #ef4444 100%);
        }

        .progress-default {
            background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
        }

        .test-section {
            margin-bottom: 40px;
        }

        .test-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: #1f2937;
        }

        .test-description {
            color: #6b7280;
            margin-bottom: 20px;
        }

        /* 优化后的状态筛选按钮样式 */
        .status-filter-btn {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            gap: 6px;
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            margin-right: 8px;
            margin-bottom: 8px;
        }

        /* 全部按钮 - 选中状态 */
        .status-filter-btn.selected.all {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .status-filter-btn.selected.all:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4), 0 3px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        /* 成功按钮 - 选中状态 */
        .status-filter-btn.selected.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .status-filter-btn.selected.success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4), 0 3px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        /* 失败按钮 - 选中状态 */
        .status-filter-btn.selected.error {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .status-filter-btn.selected.error:hover {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4), 0 3px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        /* 处理中按钮 - 选中状态 */
        .status-filter-btn.selected.processing {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .status-filter-btn.selected.processing:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4), 0 3px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        /* 未选中状态 - 统一样式 */
        .status-filter-btn.unselected {
            background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.95) 100%);
            color: #64748b;
            border: 1px solid rgba(226, 232, 240, 0.6);
            box-shadow: 0 2px 8px rgba(100, 116, 139, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04);
        }

        .status-filter-btn.unselected:hover {
            background: linear-gradient(135deg, rgba(241, 245, 249, 0.95) 0%, rgba(226, 232, 240, 1) 100%);
            color: #475569;
            border-color: rgba(59, 130, 246, 0.2);
            box-shadow: 0 4px 12px rgba(100, 116, 139, 0.12), 0 2px 6px rgba(0, 0, 0, 0.06);
            transform: translateY(-1px);
        }

        /* 数字徽章优化 */
        .status-filter-btn .count-badge {
            background: rgba(255, 255, 255, 0.25);
            color: inherit;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 700;
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            min-width: 20px;
            text-align: center;
        }

        .status-filter-btn.unselected .count-badge {
            background: rgba(100, 116, 139, 0.1);
            color: #64748b;
            border-color: rgba(100, 116, 139, 0.2);
        }

        /* 操作按钮优化 */
        .action-btn-enhanced {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 10px 18px;
            border-radius: 12px;
            font-size: 13px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            margin-right: 8px;
            margin-bottom: 8px;
        }

        .action-btn-enhanced.primary {
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.1) 0%,
                rgba(37, 99, 235, 0.15) 50%,
                rgba(29, 78, 216, 0.18) 100%);
            color: #1e40af;
            border: 1px solid rgba(59, 130, 246, 0.25);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.12),
                0 2px 6px rgba(0, 0, 0, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.7);
        }

        .action-btn-enhanced.primary:hover {
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.9) 0%,
                rgba(37, 99, 235, 0.95) 50%,
                rgba(29, 78, 216, 1) 100%);
            color: white;
            border-color: rgba(59, 130, 246, 0.4);
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 24px rgba(59, 130, 246, 0.25),
                0 4px 12px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .action-btn-enhanced .action-badge {
            background: rgba(255, 255, 255, 0.25);
            color: inherit;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 700;
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            min-width: 20px;
            text-align: center;
        }

        .action-btn-enhanced.primary:hover .action-badge {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .button-demo-section {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #1f2937; margin-bottom: 40px;">状态卡片样式测试</h1>
        
        <div class="test-section">
            <h2 class="test-title">1. 状态概览卡片</h2>
            <p class="test-description">测试四个状态小卡的样式，使用主题蓝色渐变效果。已移除顶部装饰条。</p>
            
            <div class="batch-processor-layout">
                <div class="glass-card">
                    <h3 class="unified-title-secondary">
                        <div class="unified-title-icon-secondary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" fill="none" viewBox="0 0 24 24" stroke="#374151">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        状态概览
                    </h3>
                    <div class="grid grid-cols-2 gap-2 mb-3">
                        <div class="text-center p-2 rounded-lg">
                            <div class="text-lg font-bold">31</div>
                            <div class="text-xs">总数</div>
                        </div>
                        <div class="text-center p-2 rounded-lg">
                            <div class="text-lg font-bold">18</div>
                            <div class="text-xs">完成</div>
                        </div>
                        <div class="text-center p-2 rounded-lg">
                            <div class="text-lg font-bold">12</div>
                            <div class="text-xs">失败</div>
                        </div>
                        <div class="text-center p-2 rounded-lg">
                            <div class="text-lg font-bold">1</div>
                            <div class="text-xs">等待</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">2. 进度条标签</h2>
            <p class="test-description">测试"成功率"和"总进度"标签的样式优化，使用主题蓝色。</p>
            
            <div class="progress-display">
                <div style="margin-bottom: 16px;">
                    <div class="flex justify-between text-xs mb-1 progress-label-success">
                        <span>成功率</span>
                        <span class="progress-percentage">58%</span>
                    </div>
                    <div class="pastel-progress-container">
                        <div class="pastel-progress-bar progress-mixed" style="width: 58%;"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between text-xs mb-1 progress-label-primary">
                        <span>总进度</span>
                        <span class="progress-percentage">100%</span>
                    </div>
                    <div class="pastel-progress-container">
                        <div class="pastel-progress-bar progress-default" style="width: 100%;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">3. 交互测试</h2>
            <p class="test-description">将鼠标悬停在卡片上查看悬停效果，包括背景色变化、边框变化和向上位移。</p>
            
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                <div style="padding: 16px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                    <h4 style="margin-top: 0; color: #374151;">原始样式问题</h4>
                    <ul style="color: #6b7280; font-size: 0.875rem;">
                        <li>色块与主题差距过大</li>
                        <li>标签颜色不统一</li>
                        <li>进度条标签样式不够突出</li>
                        <li>顶部装饰条过于突出</li>
                        <li>缺少渐变效果</li>
                    </ul>
                </div>
                
                <div style="padding: 16px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                    <h4 style="margin-top: 0; color: #374151;">优化后效果</h4>
                    <ul style="color: #6b7280; font-size: 0.875rem;">
                        <li>使用主题蓝色渐变效果</li>
                        <li>所有卡片统一使用主题色系</li>
                        <li>成功率和总进度标签使用主题蓝色</li>
                        <li>柔和的渐变背景增强视觉层次</li>
                        <li>移除顶部装饰条，界面更简洁</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">4. 优化后的按钮样式</h2>
            <p class="test-description">测试状态筛选按钮和操作按钮的优化效果，包括渐变背景、微妙动画和交互反馈。</p>

            <div class="button-demo-section">
                <h3 style="margin-top: 0; color: #374151; margin-bottom: 16px;">状态筛选按钮</h3>
                <div style="margin-bottom: 20px;">
                    <button class="status-filter-btn selected all">
                        全部 <span class="count-badge">4</span>
                    </button>
                    <button class="status-filter-btn unselected success">
                        成功 <span class="count-badge">4</span>
                    </button>
                    <button class="status-filter-btn unselected error">
                        失败 <span class="count-badge">0</span>
                    </button>
                    <button class="status-filter-btn unselected processing">
                        处理中 <span class="count-badge">0</span>
                    </button>
                </div>

                <h3 style="color: #374151; margin-bottom: 16px;">操作按钮</h3>
                <div>
                    <button class="action-btn-enhanced primary">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        打开所有成功结果 <span class="action-badge">4</span>
                    </button>
                    <button class="action-btn-enhanced primary">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                        </svg>
                        复制成功结果列表 <span class="action-badge">4</span>
                    </button>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px; margin-bottom: 20px;">
                <div style="padding: 16px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                    <h4 style="margin-top: 0; color: #374151;">状态按钮优化</h4>
                    <ul style="color: #6b7280; font-size: 0.875rem;">
                        <li>✨ 现代化胶囊状设计</li>
                        <li>🎨 精准的主题配色渐变</li>
                        <li>💫 微妙的星光特效</li>
                        <li>🔄 流畅的悬停动画</li>
                        <li>📱 完美的响应式适配</li>
                    </ul>
                </div>

                <div style="padding: 16px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                    <h4 style="margin-top: 0; color: #374151;">操作按钮优化</h4>
                    <ul style="color: #6b7280; font-size: 0.875rem;">
                        <li>🌟 玻璃拟态设计语言</li>
                        <li>🎯 增强的交互反馈</li>
                        <li>✨ 悬停时的变色效果</li>
                        <li>🏷️ 优化的徽章样式</li>
                        <li>⚡ 性能优化的动画</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">5. 主内容区域字体样式</h2>
            <p class="test-description">演示次要不显眼的字体样式效果 - 字体灰蒙蒙，但div背景保持清晰。</p>
            
            <div id="main-content">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
                    <div style="padding: 16px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <h3>示例标题</h3>
                        <p>这是一个示例段落，展示次要不显眼的字体样式。文字颜色较浅，字体大小适中，但背景保持清晰。</p>
                        <a href="#">这是一个链接</a>
                        <button>这是一个按钮</button>
                    </div>
                    <div style="padding: 16px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <h4>次要内容</h4>
                        <span>这里是一些次要信息，不会吸引太多注意力。</span>
                        <div>这是一个div元素中的文本。</div>
                    </div>
                    <div style="padding: 16px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <h5>低调显示</h5>
                        <p>文本采用低调的灰色和较小的字体，但容器背景保持正常的白色，不会整体灰蒙蒙。</p>
                    </div>
                </div>
            </div>
            
            <div style="margin-top: 20px; padding: 16px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #3b82f6;">
                <h4 style="margin-top: 0; color: #1f2937; font-size: 1rem;">优化说明</h4>
                <ul style="color: #6b7280; font-size: 0.875rem; margin-bottom: 0;">
                    <li>✅ 字体颜色使用灰色系，营造次要不显眼的视觉效果</li>
                    <li>✅ 移除了opacity设置，避免整个div变灰蒙蒙</li>
                    <li>✅ 移除了filter滤镜，保持背景清晰</li>
                    <li>✅ 使用CSS变量确保颜色与主题一致</li>
                    <li>✅ 悬停时颜色稍微加深，增强交互反馈</li>
                </ul>
            </div>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: #f3f4f6; border-radius: 8px; text-align: center;">
            <p style="color: #6b7280; margin: 0;">
                <strong>测试完成</strong> - 状态卡片样式已优化，使用主题蓝色渐变效果，主内容区域字体采用次要不显眼的样式
            </p>
        </div>
    </div>

    <script>
        // 添加一些交互效果测试
        document.querySelectorAll('.batch-processor-layout .glass-card .grid.grid-cols-2 > div').forEach(card => {
            card.addEventListener('click', function() {
                console.log('卡片被点击:', this.querySelector('.text-xs').textContent);
            });
        });
    </script>
</body>
</html>