<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎛️ Switch按钮浅蓝色主题测试</title>
    <link rel="stylesheet" href="./styles/index.css">
    <style>
        body {
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: #f9fafb;
        }

        .test-title {
            font-size: 20px;
            font-weight: 700;
            color: #374151;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-description {
            color: #6b7280;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .demo-container {
            display: flex;
            flex-direction: column;
            gap: 30px;
            align-items: flex-start;
        }

        .demo-row {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            width: 100%;
        }

        .demo-label {
            font-weight: 600;
            color: #374151;
            min-width: 120px;
        }

        .status-indicator {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 20px;
        }

        .status-fixed {
            background: #dcfce7;
            color: #166534;
        }

        .comparison-note {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
            color: #1e40af;
        }

        .highlight-box {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }

        .highlight-title {
            font-size: 18px;
            font-weight: 700;
            color: #92400e;
            margin-bottom: 15px;
        }

        .highlight-text {
            color: #92400e;
            line-height: 1.6;
        }

        /* 模拟原始问题的样式 */
        .old-switch {
            display: flex;
            background: #f3f4f6;
            border-radius: 8px;
            padding: 4px;
        }

        .old-switch button {
            padding: 8px 12px;
            border: none;
            background: transparent;
            color: #6b7280;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
        }

        .old-switch button.active {
            background: #3b82f6;
            color: white;
        }

        /* 模拟状态筛选按钮组 */
        .filter-buttons {
            display: flex;
            gap: 12px;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-section">
            <h1 class="test-title">🎛️ Switch按钮和"全部"按钮浅蓝色主题</h1>
            <p class="test-description">
                将按钮主题从橙色改为清新的浅蓝色，提供更加舒适的视觉体验。
            </p>

            <div class="demo-container">
                <!-- 视图切换器测试 -->
                <div class="demo-row">
                    <div class="demo-label">✅ 修复后Switch:</div>
                    <div class="view-mode-switch">
                        <div class="switch-container">
                            <button class="switch-option active">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <rect x="3" y="3" width="7" height="7" />
                                    <rect x="14" y="3" width="7" height="7" />
                                    <rect x="14" y="14" width="7" height="7" />
                                    <rect x="3" y="14" width="7" height="7" />
                                </svg>
                                <span>卡片</span>
                            </button>
                            <button class="switch-option">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <line x1="8" y1="6" x2="21" y2="6" />
                                    <line x1="8" y1="12" x2="21" y2="12" />
                                    <line x1="8" y1="18" x2="21" y2="18" />
                                    <line x1="3" y1="6" x2="3.01" y2="6" />
                                    <line x1="3" y1="12" x2="3.01" y2="12" />
                                    <line x1="3" y1="18" x2="3.01" y2="18" />
                                </svg>
                                <span>列表</span>
                            </button>
                        </div>
                    </div>
                    <div class="status-indicator status-fixed">✓ 现代蓝色主题</div>
                </div>

                <!-- 对比：旧的Switch样式 -->
                <div class="demo-row">
                    <div class="demo-label">🔍 修复前对比:</div>
                    <div class="old-switch">
                        <button class="active">卡片</button>
                        <button>列表</button>
                    </div>
                    <div class="status-indicator" style="background: #fef2f2; color: #dc2626;">❌ 普通蓝色</div>
                </div>

                <!-- "全部"按钮测试 */
                <div class="demo-row">
                    <div class="demo-label">✅ 修复后"全部":</div>
                    <div class="filter-buttons">
                        <button class="status-filter-btn-all active px-3 py-1.5 rounded-lg text-sm font-medium flex items-center space-x-2">
                            <span>全部</span>
                            <span class="count-badge">15</span>
                        </button>
                        <button class="status-filter-btn-all px-3 py-1.5 rounded-lg text-sm font-medium flex items-center space-x-2">
                            <span>成功</span>
                            <span class="count-badge">12</span>
                        </button>
                        <button class="status-filter-btn-all px-3 py-1.5 rounded-lg text-sm font-medium flex items-center space-x-2">
                            <span>失败</span>
                            <span class="count-badge">3</span>
                        </button>
                    </div>
                    <div class="status-indicator status-fixed">✓ 蓝色主题</div>
                </div>

                <!-- 交互演示 -->
                <div class="demo-row">
                    <div class="demo-label">🎮 交互演示:</div>
                    <div style="display: flex; gap: 20px; align-items: center;">
                        <div class="view-mode-switch">
                            <div class="switch-container">
                                <button class="switch-option" id="card-btn">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <rect x="3" y="3" width="7" height="7" />
                                        <rect x="14" y="3" width="7" height="7" />
                                        <rect x="14" y="14" width="7" height="7" />
                                        <rect x="3" y="14" width="7" height="7" />
                                    </svg>
                                    <span>卡片</span>
                                </button>
                                <button class="switch-option active" id="list-btn">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <line x1="8" y1="6" x2="21" y2="6" />
                                        <line x1="8" y1="12" x2="21" y2="12" />
                                        <line x1="8" y1="18" x2="21" y2="18" />
                                        <line x1="3" y1="6" x2="3.01" y2="6" />
                                        <line x1="3" y1="12" x2="3.01" y2="12" />
                                        <line x1="3" y1="18" x2="3.01" y2="18" />
                                    </svg>
                                    <span>列表</span>
                                </button>
                            </div>
                        </div>
                        <span style="color: #6b7280;">← 点击切换</span>
                    </div>
                    <div class="status-indicator status-fixed">✓ 流畅动画</div>
                </div>
            </div>

            <div class="comparison-note">
                <strong>🎯 修复要点：</strong><br>
                • Switch使用现代圆角设计 + 金色主题<br>
                • "全部"按钮激活时显示金色渐变背景<br>
                • 统一的视觉语言和交互反馈<br>
                • 保持无障碍访问性和键盘导航
            </div>
        </div>

        <div class="highlight-box">
            <div class="highlight-title">🎯 修复总结</div>
            <div class="highlight-text">
                ✅ <strong>Switch样式</strong>：现代圆角设计 + 金色主题色<br>
                ✅ <strong>"全部"按钮</strong>：激活状态使用金色渐变背景<br>
                ✅ <strong>视觉一致性</strong>：与整体设计语言保持统一<br>
                ✅ <strong>交互体验</strong>：流畅的悬停和激活动画<br>
                ✅ <strong>无障碍性</strong>：保持良好的对比度和可访问性
            </div>
        </div>
    </div>

    <script>
        // 添加交互功能
        document.getElementById('card-btn').addEventListener('click', function() {
            document.getElementById('list-btn').classList.remove('active');
            this.classList.add('active');
        });

        document.getElementById('list-btn').addEventListener('click', function() {
            document.getElementById('card-btn').classList.remove('active');
            this.classList.add('active');
        });

        // 状态筛选按钮交互
        document.querySelectorAll('.status-filter-btn-all').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除其他按钮的active类
                document.querySelectorAll('.status-filter-btn-all').forEach(b => b.classList.remove('active'));
                // 添加当前按钮的active类
                this.classList.add('active');
            });
        });

        // 添加点击反馈
        document.querySelectorAll('button').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
