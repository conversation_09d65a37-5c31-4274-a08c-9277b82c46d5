<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>目标选择器旋转测试</title>
    <link rel="stylesheet" href="./styles/index.css">
    <style>
        body {
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .status {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
        }
        .status.success { background: #d1fae5; color: #065f46; border-left: 4px solid #10b981; }
        .status.error { background: #fee2e2; color: #991b1b; border-left: 4px solid #ef4444; }
        .status.info { background: #dbeafe; color: #1e40af; border-left: 4px solid #3b82f6; }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 目标选择器旋转修复</h1>
        <p>针对特定元素选择器应用CSS旋转动画</p>

        <div class="status info">
            <strong>🔍 目标选择器：</strong><br>
            <code>#main-content > div > main > div > div > div > div.flex.items-center.justify-between.mb-4 > div > div.icon-container.icon-container--md</code>
        </div>

        <div class="status success">
            <strong>✅ 修复已应用：</strong><br>
            • 删除了所有之前无效的修复<br>
            • 针对您指定的具体元素添加了CSS旋转动画<br>
            • 使用了最高优先级的CSS规则 (!important)<br>
            • 包含了通用的 .icon-container.icon-container--md 选择器作为备用
        </div>

        <h2>📋 应用的CSS规则</h2>
        <div class="code-block">
/* 针对特定元素的旋转动画 - 基于用户提供的选择器 */
#main-content > div > main > div > div > div > div.flex.items-center.justify-between.mb-4 > div > div.icon-container.icon-container--md,
#main-content > div > main > div > div > div > div.flex.items-center.justify-between.mb-4 > div > div.icon-container.icon-container--md *,
.icon-container.icon-container--md,
.icon-container.icon-container--md * {
  animation: targetIconSpin 1.2s linear infinite !important;
  transform-origin: center center !important;
  will-change: transform !important;
}

@keyframes targetIconSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
        </div>

        <h2>🧪 测试步骤</h2>
        <div class="status info">
            <strong>请按以下步骤测试：</strong><br>
            1. 刷新批量处理页面<br>
            2. 点击"处理 X 条查询"按钮开始批量处理<br>
            3. 观察指定元素是否开始旋转<br>
            4. 如果仍然不旋转，请在浏览器控制台运行下面的调试代码
        </div>

        <h2>🔧 调试代码</h2>
        <div class="code-block">
// 在批量处理页面的浏览器控制台中运行此代码
console.log('=== 目标元素检查 ===');

// 1. 检查目标元素是否存在
const targetElement = document.querySelector('#main-content > div > main > div > div > div > div.flex.items-center.justify-between.mb-4 > div > div.icon-container.icon-container--md');

if (targetElement) {
    console.log('✅ 找到目标元素:', targetElement);
    console.log('元素类名:', targetElement.className);
    console.log('元素样式:', targetElement.style.cssText);
    
    // 检查计算后的样式
    const computedStyle = window.getComputedStyle(targetElement);
    console.log('计算后的动画:', computedStyle.animation);
    console.log('计算后的变换:', computedStyle.transform);
    
    // 强制应用旋转动画
    targetElement.style.animation = 'targetIconSpin 1.2s linear infinite';
    targetElement.style.transformOrigin = 'center center';
    console.log('✅ 已强制应用旋转动画');
} else {
    console.log('❌ 未找到目标元素');
    
    // 查找相似的元素
    const iconContainers = document.querySelectorAll('.icon-container');
    console.log('找到的图标容器数量:', iconContainers.length);
    
    iconContainers.forEach((container, index) => {
        console.log(`图标容器 ${index + 1}:`, container);
        console.log('类名:', container.className);
        console.log('父元素:', container.parentElement);
    });
}

// 2. 检查所有可能的图标容器
const allIconContainers = document.querySelectorAll('.icon-container--md');
console.log('所有 icon-container--md 元素数量:', allIconContainers.length);

allIconContainers.forEach((container, index) => {
    console.log(`容器 ${index + 1}:`, container);
    const computedStyle = window.getComputedStyle(container);
    console.log('动画状态:', computedStyle.animation);
    
    // 强制应用旋转
    container.style.animation = 'targetIconSpin 1.2s linear infinite';
    container.style.transformOrigin = 'center center';
});

console.log('=== 检查完成 ===');
        </div>

        <h2>🎯 预期结果</h2>
        <div class="status success">
            如果修复成功，您应该看到：<br>
            • 批量处理进行中时，指定的图标容器开始旋转<br>
            • 旋转动画流畅，周期为1.2秒<br>
            • 动画在所有现代浏览器中正常工作
        </div>

        <div style="margin-top: 30px; text-align: center; color: #64748b;">
            <p>🎉 针对性修复已完成！请测试实际的批量处理页面。</p>
        </div>
    </div>

    <script>
        console.log('目标选择器旋转修复已加载:', new Date().toLocaleString());
        
        // 提供快速测试函数
        window.testTargetSelector = function() {
            const selector = '#main-content > div > main > div > div > div > div.flex.items-center.justify-between.mb-4 > div > div.icon-container.icon-container--md';
            const element = document.querySelector(selector);
            
            if (element) {
                console.log('✅ 目标元素存在');
                element.style.animation = 'targetIconSpin 1.2s linear infinite';
                element.style.transformOrigin = 'center center';
                return true;
            } else {
                console.log('❌ 目标元素不存在');
                return false;
            }
        };
    </script>
</body>
</html>
