<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三栏布局测试</title>
    <link rel="stylesheet" href="./styles/index.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
        }

        /* 测试用的简化样式 */
        .test-layout {
            display: grid !important;
            grid-template-columns: 320px 1fr 280px !important;
            grid-template-rows: 1fr;
            gap: 16px;
            height: 100vh;
            padding: 20px;
            box-sizing: border-box;
            min-width: 1200px;
        }

        .test-sidebar,
        .test-main,
        .test-console {
            border-radius: 12px;
            padding: 20px;
            box-sizing: border-box;
            display: flex !important;
            flex-direction: column;
            opacity: 1 !important;
            visibility: visible !important;
            border: 2px solid #3b82f6;
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .test-sidebar {
            background: linear-gradient(135deg,
                    rgba(248, 250, 252, 0.98) 0%,
                    rgba(241, 245, 249, 0.95) 100%);
            border-color: #3b82f6;
        }

        .test-main {
            background: rgba(255, 255, 255, 0.98);
            border-color: #0ea5e9;
        }

        .test-console {
            background: linear-gradient(135deg,
                    rgba(248, 250, 252, 0.98) 0%,
                    rgba(241, 245, 249, 0.95) 100%);
            border-color: #f59e0b;
        }

        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            text-align: center;
        }

        .test-content {
            flex: 1;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            padding: 16px;
            border: 1px dashed #94a3b8;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #64748b;
        }

        .layout-info {
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: #1f2937;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 1000;
            font-family: monospace;
        }
    </style>
</head>

<body>
    <div class="layout-info">
        三栏布局测试 - 左栏(320px) | 中栏(自适应) | 右栏(280px)
    </div>

    <div class="test-layout">
        <!-- 左栏 -->
        <div class="test-sidebar">
            <h2 class="test-title" style="color: #3b82f6;">左栏 (Sidebar)</h2>
            <div class="test-content">
                <div>
                    <p>查询输入面板</p>
                    <p>查询预览区域</p>
                    <p>操作按钮</p>
                </div>
            </div>
        </div>

        <!-- 中栏 -->
        <div class="test-main">
            <h2 class="test-title" style="color: #0ea5e9;">中栏 (Main)</h2>
            <div class="test-content">
                <div>
                    <p>主要内容区域</p>
                    <p>结果展示</p>
                    <p>欢迎信息</p>
                </div>
            </div>
        </div>

        <!-- 右栏 -->
        <div class="test-console">
            <h2 class="test-title" style="color: #f59e0b;">右栏 (Console)</h2>
            <div class="test-content">
                <div>
                    <p>状态概览</p>
                    <p>进度显示</p>
                    <p>日志信息</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示当前窗口尺寸
        function updateWindowSize() {
            const info = document.querySelector('.layout-info');
            const width = window.innerWidth;
            const height = window.innerHeight;
            info.textContent = `三栏布局测试 - 窗口尺寸: ${width}x${height}px - 左栏(320px) | 中栏(自适应) | 右栏(280px)`;
        }

        window.addEventListener('resize', updateWindowSize);
        updateWindowSize();

        // 检查布局是否正确
        setTimeout(() => {
            const sidebar = document.querySelector('.test-sidebar');
            const main = document.querySelector('.test-main');
            const console = document.querySelector('.test-console');

            console.log('布局检查:');
            console.log('左栏宽度:', sidebar.offsetWidth);
            console.log('中栏宽度:', main.offsetWidth);
            console.log('右栏宽度:', console.offsetWidth);
            console.log('左栏显示:', window.getComputedStyle(sidebar).display);
            console.log('中栏显示:', window.getComputedStyle(main).display);
            console.log('右栏显示:', window.getComputedStyle(console).display);
        }, 100);
    </script>
</body>

</html>