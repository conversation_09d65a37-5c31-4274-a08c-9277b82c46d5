<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 TTSS内容提取失败调试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
            line-height: 1.6;
        }

        .debug-section {
            background: white;
            padding: 24px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #3b82f6;
        }

        .urgent {
            border-left-color: #ef4444;
            background: #fef2f2;
        }

        .success {
            border-left-color: #0ea5e9;
            background: #f0fdf4;
        }

        .warning {
            border-left-color: #f59e0b;
            background: #fffbeb;
        }

        .status {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            font-size: 14px;
        }

        .status.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .status.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }

        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }

        pre {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 13px;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 300px;
            overflow-y: auto;
        }

        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
            transition: all 0.2s;
        }

        button:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        button.urgent {
            background: #ef4444;
        }

        button.urgent:hover {
            background: #dc2626;
        }

        .test-input {
            width: 100%;
            height: 200px;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            resize: vertical;
        }

        .test-input:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .results-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .result-panel {
            background: #f8fafc;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .result-panel h4 {
            margin: 0 0 12px 0;
            color: #374151;
            font-size: 14px;
            font-weight: 600;
        }

        .extraction-result {
            background: #1f2937;
            color: #f9fafb;
            padding: 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            min-height: 100px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .highlight {
            background: #fbbf24;
            color: #92400e;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 600;
        }

        .tab-container {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }

        .tab-header {
            display: flex;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
        }

        .tab-button {
            padding: 12px 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-weight: 500;
            color: #6b7280;
        }

        .tab-button.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
            background: white;
        }

        .tab-content {
            display: none;
            padding: 20px;
            background: white;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>

<body>
    <h1>🚨 TTSS内容提取失败紧急调试</h1>
    <p class="highlight">
        <strong>问题现象：</strong>Parse5EnhancedConverter只接收到空的TTSS内容，只有默认CSS被应用，原始样式完全丢失
    </p>

    <div class="debug-section urgent">
        <h2>🔥 紧急测试</h2>
        <button class="urgent" onclick="runUrgentDiagnostic()">立即运行完整诊断</button>
        <button onclick="testCurrentExtractTTSS()">测试当前extractTTSS函数</button>
        <button onclick="testImprovedExtractTTSS()">测试改进的extractTTSS函数</button>
        <button onclick="analyzeAIGeneratedFormat()">分析AI生成内容格式</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div class="tab-container">
        <div class="tab-header">
            <button class="tab-button active" onclick="switchTab('test-samples')">测试样本</button>
            <button class="tab-button" onclick="switchTab('real-content')">真实AI内容</button>
            <button class="tab-button" onclick="switchTab('extraction-results')">提取结果</button>
            <button class="tab-button" onclick="switchTab('fixes')">修复方案</button>
        </div>

        <div id="test-samples" class="tab-content active">
            <h3>📝 AI生成内容测试样本</h3>
            <p>请粘贴实际的AI生成内容来测试TTSS提取：</p>
            <textarea id="testContent" class="test-input" placeholder="粘贴包含TTSS样式的AI生成内容...">
<!-- 示例1: FILE格式 -->
<FILE path="index.ttml">
<view class="container">
  <text class="title">世界人口排名</text>
  <view class="country-list">
    <view class="country-item">
      <text class="rank">1</text>
      <text class="name">中国</text>
      <text class="population">14.2亿</text>
    </view>
  </view>
</view>
</FILE>

<FILE path="index.ttss">
.container {
  padding: 20rpx;
  background: #f8fafc;
}
.title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 24rpx;
}
.country-item {
  display: flex;
  padding: 16rpx;
  background: white;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
}
.rank {
  width: 60rpx;
  font-weight: bold;
  color: #3b82f6;
}
</FILE>

<!-- 示例2: 内嵌格式 -->
<ttss>
.app-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
  min-height: 100vh;
}
.content-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}
</ttss>

<view class="app-container">
  <view class="content-card">
    <text>带样式的内容</text>
  </view>
</view>
            </textarea>
            <button onclick="testCurrentSample()">测试当前样本</button>
        </div>

        <div id="real-content" class="tab-content">
            <h3>🎯 真实AI内容分析</h3>
            <p>这里将显示从实际InteractiveIframe中捕获的AI生成内容：</p>
            <div id="realContentAnalysis"></div>
            <button onclick="captureRealContent()">捕获真实内容</button>
        </div>

        <div id="extraction-results" class="tab-content">
            <h3>📊 TTSS提取结果对比</h3>
            <div class="results-grid">
                <div class="result-panel">
                    <h4>🚫 当前extractTTSS函数结果</h4>
                    <div id="currentExtractionResult" class="extraction-result">
                        等待测试...
                    </div>
                </div>
                <div class="result-panel">
                    <h4>✅ 改进的extractTTSS函数结果</h4>
                    <div id="improvedExtractionResult" class="extraction-result">
                        等待测试...
                    </div>
                </div>
            </div>
            <div class="result-panel">
                <h4>🔍 提取过程详细日志</h4>
                <div id="extractionLogs" class="extraction-result">
                    等待测试...
                </div>
            </div>
        </div>

        <div id="fixes" class="tab-content">
            <h3>🛠️ 紧急修复方案</h3>
            <div class="debug-section success">
                <h4>✅ 推荐修复方案</h4>
                <ol>
                    <li><strong>增强extractTTSS函数</strong> - 支持多种TTSS格式检测</li>
                    <li><strong>改进FILE格式解析</strong> - 正确提取FILE标签中的TTSS内容</li>
                    <li><strong>添加调试日志</strong> - 记录TTSS提取过程</li>
                    <li><strong>增加降级策略</strong> - 当TTSS提取失败时的处理</li>
                </ol>
            </div>
            <button onclick="generateFixCode()">生成修复代码</button>
            <div id="fixCodeOutput"></div>
        </div>
    </div>

    <div class="debug-section">
        <h2>📝 诊断日志</h2>
        <div id="diagnosticLogs"></div>
    </div>

    <script>
        let logContainer = document.getElementById('diagnosticLogs');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `status ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function switchTab(tabId) {
            // 隐藏所有tab内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // 显示选中的tab
            document.getElementById(tabId).classList.add('active');
            document.querySelector(`[onclick="switchTab('${tabId}')"]`).classList.add('active');
        }

        // 🚫 当前的extractTTSS函数（有问题的版本）
        function currentExtractTTSS(content) {
            // 提取TTSS样式内容
            const ttssMatch = content.match(/<ttss[^>]*>([\s\S]*?)<\/ttss>/i);
            if (ttssMatch) {
                return ttssMatch[1].trim();
            }

            // 提取style标签内容作为TTSS
            const styleMatch = content.match(/<style[^>]*>([\s\S]*?)<\/style>/i);
            if (styleMatch) {
                return styleMatch[1].trim();
            }

            return '';
        }

        // ✅ 改进的extractTTSS函数
        function improvedExtractTTSS(content) {
            log('🔍 开始改进的TTSS提取...', 'info');

            const results = [];

            // 1. 提取 <ttss> 标签内容
            const ttssMatch = content.match(/<ttss[^>]*>([\s\S]*?)<\/ttss>/i);
            if (ttssMatch) {
                results.push({
                    source: '<ttss> 标签',
                    content: ttssMatch[1].trim()
                });
                log('✅ 找到 <ttss> 标签内容', 'success');
            }

            // 2. 提取 <style> 标签内容
            const styleMatch = content.match(/<style[^>]*>([\s\S]*?)<\/style>/i);
            if (styleMatch) {
                results.push({
                    source: '<style> 标签',
                    content: styleMatch[1].trim()
                });
                log('✅ 找到 <style> 标签内容', 'success');
            }

            // 3. 🔥 关键修复：提取FILE格式中的TTSS内容
            const fileMatches = content.match(/<FILE\s+path="[^"]*\.ttss"[^>]*>([\s\S]*?)<\/FILE>/gi);
            if (fileMatches) {
                fileMatches.forEach((match, index) => {
                    const fileContent = match.match(/<FILE\s+path="([^"]*)"[^>]*>([\s\S]*?)<\/FILE>/i);
                    if (fileContent) {
                        results.push({
                            source: `FILE: ${fileContent[1]}`,
                            content: fileContent[2].trim()
                        });
                        log(`✅ 找到FILE格式TTSS: ${fileContent[1]}`, 'success');
                    }
                });
            }

            // 4. 提取内联CSS样式（.css块）
            const cssBlockMatch = content.match(/```css\s*\n([\s\S]*?)\n```/i);
            if (cssBlockMatch) {
                results.push({
                    source: 'CSS代码块',
                    content: cssBlockMatch[1].trim()
                });
                log('✅ 找到CSS代码块', 'success');
            }

            // 5. 提取STYLE注释块格式
            const styleCommentMatch = content.match(/\/\*\s*STYLE\s*\*\/([\s\S]*?)\/\*\s*END\s*STYLE\s*\*\//i);
            if (styleCommentMatch) {
                results.push({
                    source: '/* STYLE */ 注释块',
                    content: styleCommentMatch[1].trim()
                });
                log('✅ 找到STYLE注释块', 'success');
            }

            // 6. 合并所有找到的样式内容
            if (results.length > 0) {
                const combinedCSS = results.map(r => `/* 来源: ${r.source} */\n${r.content}`).join('\n\n');
                log(`✅ 总共找到 ${results.length} 个样式源，合并后长度: ${combinedCSS.length}`, 'success');
                return combinedCSS;
            }

            log('⚠️ 未找到任何TTSS内容', 'warning');
            return '';
        }

        function testCurrentExtractTTSS() {
            const content = document.getElementById('testContent').value;
            log('🧪 测试当前extractTTSS函数...', 'info');

            const result = currentExtractTTSS(content);
            const resultContainer = document.getElementById('currentExtractionResult');

            if (result) {
                resultContainer.textContent = result;
                log(`当前函数提取结果长度: ${result.length}`, result.length > 0 ? 'success' : 'warning');
            } else {
                resultContainer.textContent = '❌ 未提取到任何TTSS内容';
                log('当前函数未提取到TTSS内容', 'error');
            }
        }

        function testImprovedExtractTTSS() {
            const content = document.getElementById('testContent').value;
            log('🧪 测试改进的extractTTSS函数...', 'info');

            const result = improvedExtractTTSS(content);
            const resultContainer = document.getElementById('improvedExtractionResult');

            if (result) {
                resultContainer.textContent = result;
                log(`改进函数提取结果长度: ${result.length}`, 'success');
            } else {
                resultContainer.textContent = '❌ 未提取到任何TTSS内容';
                log('改进函数也未提取到TTSS内容', 'warning');
            }
        }

        function testCurrentSample() {
            log('🧪 测试当前样本内容...', 'info');
            testCurrentExtractTTSS();
            testImprovedExtractTTSS();
            analyzeContentStructure();
        }

        function analyzeContentStructure() {
            const content = document.getElementById('testContent').value;
            log('🔍 分析内容结构...', 'info');

            const structures = [
                { name: '<ttss> 标签', pattern: /<ttss[^>]*>/i },
                { name: '<style> 标签', pattern: /<style[^>]*>/i },
                { name: 'FILE格式', pattern: /<FILE\s+path="[^"]*\.ttss"/i },
                { name: 'CSS代码块', pattern: /```css/i },
                { name: 'STYLE注释', pattern: /\/\*\s*STYLE\s*\*\//i },
                { name: 'TTML内容', pattern: /<view|<text|<scroll-view/i },
                { name: 'RPX单位', pattern: /\d+rpx/i }
            ];

            const logsContainer = document.getElementById('extractionLogs');
            let analysisLog = '内容结构分析:\n\n';

            structures.forEach(structure => {
                const found = structure.pattern.test(content);
                analysisLog += `${found ? '✅' : '❌'} ${structure.name}: ${found ? '发现' : '未发现'}\n`;
                log(`${structure.name}: ${found ? '✅ 发现' : '❌ 未发现'}`, found ? 'success' : 'info');
            });

            analysisLog += `\n内容总长度: ${content.length} 字符\n`;
            analysisLog += `行数: ${content.split('\n').length}\n`;

            logsContainer.textContent = analysisLog;
        }

        function analyzeAIGeneratedFormat() {
            log('🤖 分析AI生成内容格式...', 'info');

            // 这里模拟分析真实的AI生成内容格式
            const commonFormats = [
                {
                    name: 'Lynx FILE格式',
                    example: `<FILE path="index.ttml">...</FILE>\n<FILE path="index.ttss">...</FILE>`,
                    prevalence: '85%'
                },
                {
                    name: '内嵌TTSS标签',
                    example: `<ttss>...</ttss>\n<view>...</view>`,
                    prevalence: '10%'
                },
                {
                    name: '分离的CSS块',
                    example: `\`\`\`css\n...\n\`\`\`\n\n<view>...</view>`,
                    prevalence: '5%'
                }
            ];

            let formatAnalysis = 'AI生成内容格式分析:\n\n';
            commonFormats.forEach(format => {
                formatAnalysis += `📊 ${format.name} (${format.prevalence})\n`;
                formatAnalysis += `   示例: ${format.example.substring(0, 50)}...\n\n`;
            });

            const logsContainer = document.getElementById('extractionLogs');
            logsContainer.textContent = formatAnalysis;

            log('完成AI内容格式分析', 'success');
        }

        function runUrgentDiagnostic() {
            log('🚨 开始紧急诊断...', 'error');

            clearResults();
            setTimeout(() => {
                testCurrentSample();
            }, 500);

            setTimeout(() => {
                analyzeAIGeneratedFormat();
            }, 1000);

            setTimeout(() => {
                generateFixCode();
            }, 1500);

            log('🚨 紧急诊断完成！请查看各个标签页的结果', 'success');
        }

        function generateFixCode() {
            log('🛠️ 生成修复代码...', 'info');

            const fixCode = `
// 🔧 修复的extractTTSS函数
const extractTTSS = (content: string): string => {
  console.log('🔍 [TTSS] 开始增强的TTSS提取，内容长度:', content.length);
  
  const results: Array<{source: string, content: string}> = [];
  
  // 1. 提取 <ttss> 标签内容
  const ttssMatch = content.match(/<ttss[^>]*>([\\s\\S]*?)<\\/ttss>/i);
  if (ttssMatch) {
    results.push({ source: '<ttss> 标签', content: ttssMatch[1].trim() });
    console.log('✅ [TTSS] 找到 <ttss> 标签内容');
  }

  // 2. 提取 <style> 标签内容  
  const styleMatch = content.match(/<style[^>]*>([\\s\\S]*?)<\\/style>/i);
  if (styleMatch) {
    results.push({ source: '<style> 标签', content: styleMatch[1].trim() });
    console.log('✅ [TTSS] 找到 <style> 标签内容');
  }

  // 🔥 3. 关键修复：提取FILE格式中的TTSS内容
  const fileMatches = content.match(/<FILE\\s+path="[^"]*\\.ttss"[^>]*>([\\s\\S]*?)<\\/FILE>/gi);
  if (fileMatches) {
    fileMatches.forEach((match, index) => {
      const fileContent = match.match(/<FILE\\s+path="([^"]*)"[^>]*>([\\s\\S]*?)<\\/FILE>/i);
      if (fileContent) {
        results.push({ source: \`FILE: \${fileContent[1]}\`, content: fileContent[2].trim() });
        console.log(\`✅ [TTSS] 找到FILE格式TTSS: \${fileContent[1]}\`);
      }
    });
  }

  // 4. 提取CSS代码块
  const cssBlockMatch = content.match(/\`\`\`css\\s*\\n([\\s\\S]*?)\\n\`\`\`/i);
  if (cssBlockMatch) {
    results.push({ source: 'CSS代码块', content: cssBlockMatch[1].trim() });
    console.log('✅ [TTSS] 找到CSS代码块');
  }

  // 5. 合并所有找到的样式内容
  if (results.length > 0) {
    const combinedCSS = results.map(r => \`/* 来源: \${r.source} */\\n\${r.content}\`).join('\\n\\n');
    console.log(\`✅ [TTSS] 总共找到 \${results.length} 个样式源，合并后长度: \${combinedCSS.length}\`);
    return combinedCSS;
  }

  console.log('⚠️ [TTSS] 未找到任何TTSS内容');
  return '';
};`;

            const fixContainer = document.getElementById('fixCodeOutput');
            fixContainer.innerHTML = `
                <div class="debug-section success">
                    <h4>🛠️ 修复代码</h4>
                    <pre>${fixCode}</pre>
                    <p><strong>使用说明：</strong></p>
                    <ol>
                        <li>将此代码替换InteractiveIframe.tsx中第971-985行的extractTTSS函数</li>
                        <li>确保Parse5EnhancedConverter能接收到非空的TTSS内容</li>
                        <li>添加更多调试日志来跟踪TTSS提取过程</li>
                    </ol>
                </div>
            `;

            log('✅ 修复代码已生成', 'success');
        }

        function captureRealContent() {
            log('🎯 尝试捕获真实AI内容...', 'info');
            // 这里实际需要从InteractiveIframe或其他地方获取真实内容
            // 目前模拟显示
            const realAnalysis = document.getElementById('realContentAnalysis');
            realAnalysis.innerHTML = `
                <div class="status info">
                    <strong>💡 提示：</strong>要捕获真实内容，需要：
                    <ol>
                        <li>在InteractiveIframe.tsx中添加调试日志</li>
                        <li>记录传入extractTTSS函数的原始content参数</li>
                        <li>检查result.metadata?.extractedContent的实际格式</li>
                        <li>验证AI是否使用FILE格式或其他格式</li>
                    </ol>
                </div>
            `;
        }

        function clearResults() {
            document.getElementById('currentExtractionResult').textContent = '等待测试...';
            document.getElementById('improvedExtractionResult').textContent = '等待测试...';
            document.getElementById('extractionLogs').textContent = '等待测试...';
            logContainer.innerHTML = '';
            log('🧹 结果已清空', 'info');
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', () => {
            log('🚀 TTSS提取调试工具已加载', 'info');
            log('💡 点击"立即运行完整诊断"开始紧急调试', 'warning');
        });
    </script>
</body>

</html>