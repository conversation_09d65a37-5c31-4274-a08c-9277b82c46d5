<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>✅ TTSS修复验证测试</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      max-width: 900px;
      margin: 0 auto;
      padding: 20px;
      background: #f8fafc;
      line-height: 1.6;
    }

    .test-section {
      background: white;
      padding: 24px;
      margin: 20px 0;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #0ea5e9;
    }

    .test-case {
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 16px;
      margin: 16px 0;
      background: #f9fafb;
    }

    .test-case h4 {
      margin: 0 0 12px 0;
      color: #374151;
    }

    .test-input {
      width: 100%;
      height: 120px;
      padding: 12px;
      border: 2px solid #e5e7eb;
      border-radius: 6px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 11px;
      background: #1f2937;
      color: #f9fafb;
      resize: vertical;
    }

    .test-result {
      margin-top: 12px;
      padding: 12px;
      border-radius: 6px;
      font-family: monospace;
      font-size: 12px;
      white-space: pre-wrap;
      word-wrap: break-word;
      max-height: 200px;
      overflow-y: auto;
    }

    .success-result {
      background: #dcfce7;
      border: 1px solid #bbf7d0;
      color: #166534;
    }

    .error-result {
      background: #fee2e2;
      border: 1px solid #fecaca;
      color: #991b1b;
    }

    .warning-result {
      background: #fef3c7;
      border: 1px solid #fde68a;
      color: #92400e;
    }

    button {
      background: #0ea5e9;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      margin: 5px;
      font-weight: 500;
    }

    button:hover {
      background: #059669;
    }

    .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 600;
      margin-left: 8px;
    }

    .pass {
      background: #dcfce7;
      color: #166534;
    }

    .fail {
      background: #fee2e2;
      color: #991b1b;
    }

    .pending {
      background: #fef3c7;
      color: #92400e;
    }
  </style>
</head>

<body>
  <h1>✅ TTSS提取修复验证测试</h1>
  <p><strong>目标：</strong>验证修复后的extractTTSS函数能正确提取各种格式的TTSS内容</p>

  <div class="test-section">
    <h2>🧪 修复后的extractTTSS函数</h2>
    <button onclick="runAllTests()">运行所有测试</button>
    <button onclick="clearResults()">清空结果</button>
    <div id="overallResult"></div>
  </div>

  <div class="test-case">
    <h4>测试案例 1: FILE格式 (最常见) <span id="test1-status" class="status-badge pending">等待测试</span></h4>
    <textarea id="test1-input" class="test-input" readonly><FILE path="index.ttml">
<view class="container">
  <text class="title">测试标题</text>
</view>
</FILE>

<FILE path="index.ttss">
.container {
  padding: 20rpx;
  background: #f8fafc;
}
.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
</FILE></textarea>
    <button onclick="runTest(1)">测试</button>
    <div id="test1-result" class="test-result"></div>
  </div>

  <div class="test-case">
    <h4>测试案例 2: 内嵌TTSS标签 <span id="test2-status" class="status-badge pending">等待测试</span></h4>
    <textarea id="test2-input" class="test-input" readonly><ttss>
.app-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
  min-height: 100vh;
}
.content-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
}
</ttss>

<view class="app-container">
  <view class="content-card">
    <text>内容</text>
  </view>
</view></textarea>
    <button onclick="runTest(2)">测试</button>
    <div id="test2-result" class="test-result"></div>
  </div>

  <div class="test-case">
    <h4>测试案例 3: CSS代码块格式 <span id="test3-status" class="status-badge pending">等待测试</span></h4>
    <textarea id="test3-input" class="test-input" readonly">```css
.header {
  background: #fff;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}
.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
}
```

<view class="header">
  <text class="title">CSS块格式测试</text>
</view></textarea>
    <button onclick="runTest(3)">测试</button>
    <div id="test3-result" class="test-result"></div>
  </div>

  <div class="test-case">
    <h4>测试案例 4: Style标签格式 <span id="test4-status" class="status-badge pending">等待测试</span></h4>
    <textarea id="test4-input" class="test-input" readonly><style>
.main-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}
.header-bar {
  height: 88rpx;
  background: #007aff;
  color: white;
}
</style>

<view class="main-view">
  <view class="header-bar">
    <text>Style标签测试</text>
  </view>
</view></textarea>
    <button onclick="runTest(4)">测试</button>
    <div id="test4-result" class="test-result"></div>
  </div>

  <div class="test-case">
    <h4>测试案例 5: 混合格式 <span id="test5-status" class="status-badge pending">等待测试</span></h4>
    <textarea id="test5-input" class="test-input" readonly><FILE path="index.ttss">
.file-style {
  color: #333;
}
</FILE>

<ttss>
.ttss-style {
  background: #f0f0f0;
}
</ttss>

<style>
.style-tag {
  padding: 10rpx;
}
</style>

<view class="container">
  <text>混合格式测试</text>
</view></textarea>
    <button onclick="runTest(5)">测试</button>
    <div id="test5-result" class="test-result"></div>
  </div>

  <div class="test-case">
    <h4>测试案例 6: 空内容 (应该返回空字符串) <span id="test6-status" class="status-badge pending">等待测试</span></h4>
    <textarea id="test6-input" class="test-input" readonly><view class="container">
  <text>只有TTML，没有样式</text>
</view></textarea>
    <button onclick="runTest(6)">测试</button>
    <div id="test6-result" class="test-result"></div>
  </div>

  <script>
    // 🔧 修复后的extractTTSS函数 (从InteractiveIframe.tsx复制)
    function extractTTSS(content) {
      console.log('🔍 [TTSS] 开始增强的TTSS提取，内容长度:', content.length);
      console.log('📄 [TTSS] 内容预览:', content.substring(0, 300) + '...');

      const results = [];

      // 1. 提取 <ttss> 标签内容
      const ttssMatch = content.match(/<ttss[^>]*>([\s\S]*?)<\/ttss>/i);
      if (ttssMatch) {
        results.push({ source: '<ttss> 标签', content: ttssMatch[1].trim() });
        console.log('✅ [TTSS] 找到 <ttss> 标签内容, 长度:', ttssMatch[1].trim().length);
      }

      // 2. 提取 <style> 标签内容  
      const styleMatch = content.match(/<style[^>]*>([\s\S]*?)<\/style>/i);
      if (styleMatch) {
        results.push({ source: '<style> 标签', content: styleMatch[1].trim() });
        console.log('✅ [TTSS] 找到 <style> 标签内容, 长度:', styleMatch[1].trim().length);
      }

      // 🔥 3. 关键修复：提取FILE格式中的TTSS内容
      const fileMatches = content.match(/<FILE\s+path="[^"]*\.ttss"[^>]*>([\s\S]*?)<\/FILE>/gi);
      if (fileMatches) {
        console.log('✅ [TTSS] 发现', fileMatches.length, '个FILE格式的TTSS文件');
        fileMatches.forEach((match, index) => {
          const fileContent = match.match(/<FILE\s+path="([^"]*)"[^>]*>([\s\S]*?)<\/FILE>/i);
          if (fileContent) {
            results.push({ source: `FILE: ${fileContent[1]}`, content: fileContent[2].trim() });
            console.log(`✅ [TTSS] FILE ${index + 1}: ${fileContent[1]}, 长度:`, fileContent[2].trim().length);
          }
        });
      }

      // 4. 提取CSS代码块 (```css ... ```)
      const cssBlockMatch = content.match(/```css\s*\n([\s\S]*?)\n```/i);
      if (cssBlockMatch) {
        results.push({ source: 'CSS代码块', content: cssBlockMatch[1].trim() });
        console.log('✅ [TTSS] 找到CSS代码块, 长度:', cssBlockMatch[1].trim().length);
      }

      // 5. 提取STYLE注释块格式
      const styleCommentMatch = content.match(/\/\*\s*STYLE\s*\*\/([\s\S]*?)\/\*\s*END\s*STYLE\s*\*\//i);
      if (styleCommentMatch) {
        results.push({ source: '/* STYLE */ 注释块', content: styleCommentMatch[1].trim() });
        console.log('✅ [TTSS] 找到STYLE注释块, 长度:', styleCommentMatch[1].trim().length);
      }

      // 6. 合并所有找到的样式内容
      if (results.length > 0) {
        const combinedCSS = results.map(r => `/* 来源: ${r.source} */\n${r.content}`).join('\n\n');
        console.log(`✅ [TTSS] 总共找到 ${results.length} 个样式源，合并后长度: ${combinedCSS.length}`);
        console.log('📝 [TTSS] 合并后的CSS预览:', combinedCSS.substring(0, 200) + '...');
        return combinedCSS;
      }

      console.log('⚠️ [TTSS] 未找到任何TTSS内容');
      console.log('🔍 [TTSS] 内容格式分析:');
      console.log('  - 包含<ttss>标签:', /<ttss[^>]*>/i.test(content));
      console.log('  - 包含<style>标签:', /<style[^>]*>/i.test(content));
      console.log('  - 包含FILE格式:', /<FILE\s+path="[^"]*\.ttss"/i.test(content));
      console.log('  - 包含CSS代码块:', /```css/i.test(content));
      console.log('  - 包含TTML内容:', /<view|<text|<scroll-view/i.test(content));

      return '';
    }

    function runTest(testNumber) {
      const input = document.getElementById(`test${testNumber}-input`).value;
      const resultDiv = document.getElementById(`test${testNumber}-result`);
      const statusBadge = document.getElementById(`test${testNumber}-status`);

      console.log(`\n🧪 开始测试案例 ${testNumber}`);
      console.log('输入内容:', input.substring(0, 100) + '...');

      try {
        const result = extractTTSS(input);

        let resultText = `测试案例 ${testNumber} 结果:\n\n`;

        if (result && result.trim() !== '') {
          resultText += `✅ 成功提取TTSS内容\n`;
          resultText += `📏 提取长度: ${result.length} 字符\n\n`;
          resultText += `📝 提取内容:\n${result}`;

          resultDiv.className = 'test-result success-result';
          statusBadge.className = 'status-badge pass';
          statusBadge.textContent = '通过';

          console.log(`✅ 测试案例 ${testNumber} 通过`);
        } else {
          if (testNumber === 6) {
            // 测试案例6期望返回空值
            resultText += `✅ 正确返回空值 (符合预期)\n`;
            resultText += `📏 结果长度: ${result.length} 字符`;

            resultDiv.className = 'test-result success-result';
            statusBadge.className = 'status-badge pass';
            statusBadge.textContent = '通过';

            console.log(`✅ 测试案例 ${testNumber} 通过 (正确返回空值)`);
          } else {
            resultText += `❌ 未能提取到TTSS内容\n`;
            resultText += `📏 结果长度: ${result.length} 字符\n`;
            resultText += `🔍 这可能表示存在问题`;

            resultDiv.className = 'test-result error-result';
            statusBadge.className = 'status-badge fail';
            statusBadge.textContent = '失败';

            console.log(`❌ 测试案例 ${testNumber} 失败`);
          }
        }

        resultDiv.textContent = resultText;

      } catch (error) {
        const errorText = `❌ 测试执行出错:\n${error.message}\n\n${error.stack}`;
        resultDiv.textContent = errorText;
        resultDiv.className = 'test-result error-result';
        statusBadge.className = 'status-badge fail';
        statusBadge.textContent = '错误';

        console.error(`💥 测试案例 ${testNumber} 执行出错:`, error);
      }
    }

    function runAllTests() {
      console.log('🚀 开始运行所有测试案例...');

      for (let i = 1; i <= 6; i++) {
        setTimeout(() => {
          runTest(i);
          if (i === 6) {
            setTimeout(showOverallResult, 500);
          }
        }, i * 200);
      }
    }

    function showOverallResult() {
      const totalTests = 6;
      let passedTests = 0;

      for (let i = 1; i <= totalTests; i++) {
        const badge = document.getElementById(`test${i}-status`);
        if (badge.textContent === '通过') {
          passedTests++;
        }
      }

      const overallDiv = document.getElementById('overallResult');
      const passRate = (passedTests / totalTests * 100).toFixed(1);

      let overallText = `\n📊 测试结果总结:\n`;
      overallText += `✅ 通过: ${passedTests}/${totalTests} (${passRate}%)\n`;

      if (passedTests === totalTests) {
        overallText += `🎉 所有测试通过！TTSS提取功能修复成功！`;
        overallDiv.className = 'test-result success-result';
      } else if (passedTests >= totalTests * 0.8) {
        overallText += `⚠️ 大部分测试通过，还有小问题需要修复`;
        overallDiv.className = 'test-result warning-result';
      } else {
        overallText += `❌ 多个测试失败，需要进一步修复`;
        overallDiv.className = 'test-result error-result';
      }

      overallDiv.textContent = overallText;
      console.log('📊 测试总结完成');
    }

    function clearResults() {
      for (let i = 1; i <= 6; i++) {
        document.getElementById(`test${i}-result`).textContent = '';
        document.getElementById(`test${i}-result`).className = 'test-result';
        const badge = document.getElementById(`test${i}-status`);
        badge.className = 'status-badge pending';
        badge.textContent = '等待测试';
      }
      document.getElementById('overallResult').textContent = '';
      document.getElementById('overallResult').className = '';
      console.log('🧹 测试结果已清空');
    }

    // 页面加载时的说明
    window.addEventListener('load', () => {
      console.log('🔧 TTSS修复验证测试工具已加载');
      console.log('💡 点击"运行所有测试"开始验证修复效果');
    });
  </script>
</body>

</html>