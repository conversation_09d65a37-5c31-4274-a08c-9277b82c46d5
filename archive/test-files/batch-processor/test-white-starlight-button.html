<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>白色星光按钮效果测试</title>
    <style>
        /* 白色星光动画 */
        @keyframes whiteStarTwinkle {
          0%, 100% {
            opacity: 0.3;
            transform: scale(0.8) rotate(0deg);
            filter: brightness(1);
          }

          25% {
            opacity: 0.7;
            transform: scale(1.2) rotate(90deg);
            filter: brightness(1.3);
          }

          50% {
            opacity: 1;
            transform: scale(1.5) rotate(180deg);
            filter: brightness(1.6);
          }

          75% {
            opacity: 0.8;
            transform: scale(1.1) rotate(270deg);
            filter: brightness(1.2);
          }
        }

        /* 随机闪烁动画组 */
        @keyframes randomTwinkle1 {
          0%, 100% { opacity: 0.3; transform: scale(0.8); }
          15% { opacity: 1; transform: scale(1.4); }
          30% { opacity: 0.2; transform: scale(0.6); }
          45% { opacity: 0.9; transform: scale(1.2); }
          60% { opacity: 0.4; transform: scale(0.9); }
          75% { opacity: 0.8; transform: scale(1.1); }
          90% { opacity: 0.5; transform: scale(0.7); }
        }

        @keyframes randomTwinkle2 {
          0%, 100% { opacity: 0.4; transform: scale(0.9); }
          20% { opacity: 0.8; transform: scale(1.3); }
          40% { opacity: 0.3; transform: scale(0.7); }
          60% { opacity: 1; transform: scale(1.5); }
          80% { opacity: 0.6; transform: scale(1.0); }
        }

        @keyframes randomTwinkle3 {
          0%, 100% { opacity: 0.2; transform: scale(0.6); }
          25% { opacity: 0.9; transform: scale(1.2); }
          50% { opacity: 0.5; transform: scale(0.8); }
          75% { opacity: 1; transform: scale(1.4); }
        }

        @keyframes surpriseStarBurst {
          0% {
            opacity: 0;
            transform: scale(0) rotate(0deg);
          }
          20% {
            opacity: 0.8;
            transform: scale(1.5) rotate(72deg);
          }
          40% {
            opacity: 1;
            transform: scale(2.2) rotate(144deg);
          }
          60% {
            opacity: 0.9;
            transform: scale(1.8) rotate(216deg);
          }
          80% {
            opacity: 0.6;
            transform: scale(1.2) rotate(288deg);
          }
          100% {
            opacity: 0;
            transform: scale(0.5) rotate(360deg);
          }
        }

        @keyframes whiteStarSweep {
          0% {
            transform: translateX(-200%) translateY(-200%) rotate(-45deg);
            opacity: 0;
          }

          20% {
            opacity: 0.6;
          }

          50% {
            opacity: 1;
            transform: translateX(0%) translateY(0%) rotate(-45deg);
          }

          80% {
            opacity: 0.6;
          }

          100% {
            transform: translateX(200%) translateY(200%) rotate(-45deg);
            opacity: 0;
          }
        }

        body {
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 35%, #f9fafb 65%, #e0f2fe 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            color: #1e3a8a;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .title {
            text-align: center;
            color: #1e3a8a;
            margin-bottom: 40px;
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .demo-section {
            padding: 30px;
            border-radius: 16px;
            background: rgba(248, 250, 252, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .demo-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 25px;
            text-align: center;
            color: #1e40af;
        }

        /* 基础按钮样式 */
        .btn {
            border: none;
            background: none;
            padding: 0;
            margin: 0;
            font: inherit;
            color: inherit;
            cursor: pointer;
            outline: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            border-radius: 8px;
            font-weight: 500;
            line-height: 1;
            white-space: nowrap;
            user-select: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .btn--secondary-glass {
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.9) 0%, 
                rgba(255, 255, 255, 0.7) 100%);
            color: #1e40af;
            border: 1px solid rgba(59, 130, 246, 0.2);
            backdrop-filter: blur(8px);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        .btn--lg {
            padding: 12px 24px;
            font-size: 1rem;
            min-height: 48px;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        /* 白色星光按钮效果 */
        .btn-white-starlight {
            position: relative;
            overflow: hidden;
            isolation: isolate;
        }

        .btn-white-starlight::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 1;

            /* 15个白色星点 - 随机分布，不同大小 */
            background-image:
                /* 大星点 */
                radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
                radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.8) 1.5px, transparent 2.5px),
                radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
                radial-gradient(circle at 25% 75%, rgba(255, 255, 255, 0.7) 2px, transparent 3px),
                radial-gradient(circle at 92% 85%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),

                /* 中等星点 */
                radial-gradient(circle at 45% 35%, rgba(255, 255, 255, 0.6) 1px, transparent 1.5px),
                radial-gradient(circle at 90% 60%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),
                radial-gradient(circle at 10% 85%, rgba(255, 255, 255, 0.7) 1px, transparent 1.5px),
                radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
                radial-gradient(circle at 75% 45%, rgba(255, 255, 255, 0.6) 1px, transparent 1.5px),

                /* 小星点 */
                radial-gradient(circle at 35% 60%, rgba(255, 255, 255, 0.5) 0.5px, transparent 1px),
                radial-gradient(circle at 80% 40%, rgba(255, 255, 255, 0.6) 0.5px, transparent 1px),
                radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.4) 0.5px, transparent 1px),
                radial-gradient(circle at 55% 85%, rgba(255, 255, 255, 0.7) 0.5px, transparent 1px),
                radial-gradient(circle at 5% 30%, rgba(255, 255, 255, 0.5) 0.5px, transparent 1px);

            /* 多层随机闪烁动画 */
            animation:
                randomTwinkle1 3.2s ease-in-out infinite,
                randomTwinkle2 2.8s ease-in-out infinite 0.5s,
                randomTwinkle3 3.5s ease-in-out infinite 1s;
            opacity: 0.8;
        }

        .btn-white-starlight::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            pointer-events: none;
            z-index: 2;
            
            background: linear-gradient(
                45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.1) 40%,
                rgba(255, 255, 255, 0.3) 45%,
                rgba(255, 255, 255, 0.6) 50%,
                rgba(255, 255, 255, 0.3) 55%,
                rgba(255, 255, 255, 0.1) 60%,
                transparent 70%
            );
            
            animation: whiteStarSweep 3.5s ease-in-out infinite;
            opacity: 0.8;
            filter: blur(0.5px);
        }

        .btn-white-starlight:hover::before {
            /* 悬停时动画加速，星点变大 */
            animation:
                randomTwinkle1 1.5s ease-in-out infinite,
                randomTwinkle2 1.2s ease-in-out infinite 0.3s,
                randomTwinkle3 1.8s ease-in-out infinite 0.6s;
            opacity: 1;

            /* 悬停时星点变大变亮 */
            background-image:
                radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 1) 2px, transparent 3px),
                radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.9) 2.5px, transparent 3.5px),
                radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 1) 2px, transparent 3px),
                radial-gradient(circle at 25% 75%, rgba(255, 255, 255, 0.8) 3px, transparent 4px),
                radial-gradient(circle at 92% 85%, rgba(255, 255, 255, 0.9) 2px, transparent 3px),
                radial-gradient(circle at 45% 35%, rgba(255, 255, 255, 0.7) 1.5px, transparent 2.5px),
                radial-gradient(circle at 90% 60%, rgba(255, 255, 255, 0.9) 2px, transparent 3px),
                radial-gradient(circle at 10% 85%, rgba(255, 255, 255, 0.8) 1.5px, transparent 2.5px),
                radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 1) 2px, transparent 3px),
                radial-gradient(circle at 75% 45%, rgba(255, 255, 255, 0.7) 1.5px, transparent 2.5px),
                radial-gradient(circle at 35% 60%, rgba(255, 255, 255, 0.6) 1px, transparent 1.5px),
                radial-gradient(circle at 80% 40%, rgba(255, 255, 255, 0.7) 1px, transparent 1.5px),
                radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.5) 1px, transparent 1.5px),
                radial-gradient(circle at 55% 85%, rgba(255, 255, 255, 0.8) 1px, transparent 1.5px),
                radial-gradient(circle at 5% 30%, rgba(255, 255, 255, 0.6) 1px, transparent 1.5px);
        }

        .btn-white-starlight:hover::after {
            animation-duration: 1.5s;
            opacity: 1;
        }

        /* 点击时的惊喜爆发效果 */
        .btn-white-starlight:active::before {
            animation: surpriseStarBurst 0.6s ease-out;
            opacity: 1;
            transform: scale(1.1);
        }

        .btn-white-starlight:active::after {
            animation: whiteStarSweep 0.4s ease-out;
            opacity: 1;
            transform: scale(1.05);
        }

        .btn-white-starlight > * {
            position: relative;
            z-index: 3;
        }

        .button-demo {
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
        }

        .icon {
            width: 16px;
            height: 16px;
            background: #3b82f6;
            border-radius: 2px;
            flex-shrink: 0;
        }

        .features {
            background: rgba(255, 255, 255, 0.7);
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
        }

        .features h4 {
            color: #1e40af;
            margin-bottom: 12px;
            font-weight: 600;
        }

        .features ul {
            margin: 0;
            padding-left: 20px;
            color: #1e3a8a;
        }

        .features li {
            margin-bottom: 6px;
        }

        .highlight {
            border: 2px solid #3b82f6;
            background: rgba(59, 130, 246, 0.05);
        }

        .comparison-info {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            padding: 25px;
            border-radius: 12px;
            margin-top: 30px;
            text-align: center;
        }

        .comparison-info h3 {
            color: #1e40af;
            margin-bottom: 15px;
        }

        .comparison-info p {
            color: #1e3a8a;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="title">✨ 白色星光按钮效果测试</h1>
        
        <div class="demo-grid">
            <div class="demo-section">
                <h3 class="demo-title">🌟 原始按钮</h3>
                <div class="button-demo">
                    <button class="btn btn--secondary-glass btn--lg">
                        <div class="icon"></div>
                        使用示例数据
                    </button>
                </div>
                <div class="features">
                    <h4>原始效果：</h4>
                    <ul>
                        <li>基础玻璃质感</li>
                        <li>简单悬停效果</li>
                        <li>标准交互反馈</li>
                        <li>无特殊动画</li>
                    </ul>
                </div>
            </div>

            <div class="demo-section highlight">
                <h3 class="demo-title">✨ 白色星光按钮</h3>
                <div class="button-demo">
                    <button class="btn btn--secondary-glass btn--lg btn-white-starlight">
                        <div class="icon"></div>
                        使用示例数据
                    </button>
                </div>
                <div class="features">
                    <h4>星光效果：</h4>
                    <ul>
                        <li>15个白色星点随机闪烁</li>
                        <li>3层独立动画叠加</li>
                        <li>光芒扫过动画</li>
                        <li>悬停时效果增强</li>
                        <li>点击时惊喜爆发</li>
                        <li>增加强烈惊喜感</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="comparison-info">
            <h3>🎨 白色星光效果特点</h3>
            <p><strong>星点闪烁：</strong> 12个不同大小的白色星点，随机分布，持续闪烁</p>
            <p><strong>光芒扫过：</strong> 白色光芒从左上角扫向右下角，营造动态感</p>
            <p><strong>悬停增强：</strong> 鼠标悬停时动画加速，星点变大，效果更明显</p>
            <p><strong>惊喜感：</strong> 白色星光与蓝色主题形成对比，增加视觉吸引力</p>
        </div>

        <div style="text-align: center; color: #1e3a8a; margin-top: 30px;">
            <p><strong>💡 实现说明：</strong></p>
            <p>• 使用 ::before 伪元素创建星点闪烁效果</p>
            <p>• 使用 ::after 伪元素创建光芒扫过效果</p>
            <p>• 悬停时动画加速，增强交互反馈</p>
            <p>• 白色星光与蓝色按钮形成美丽对比</p>
        </div>
    </div>
</body>
</html>
