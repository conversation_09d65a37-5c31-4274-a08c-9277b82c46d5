/**
 * FILES标签保护工具测试
 * 
 * 测试修复后的代码能够正确处理<FILES><FILE path="index.ttml">格式
 * 确保不会再出现尖括号丢失的问题
 */

import {
  extractTextContentSafely,
  processContentWithProtection,
  containsFilesTags,
  validateFilesTagFormat,
  repairFilesTagFormat
} from '../filesTagProtector';

describe('filesTagProtector', () => {
  describe('extractTextContentSafely', () => {
    it('应该保护FILES和FILE标签，移除其他HTML标签', () => {
      const input = `<FILES>
<FILE path="index.ttml">
<view class="container">
  <text>Hello World</text>
</view>
</FILE>
</FILES>`;

      const result = extractTextContentSafely(input);
      
      // 应该保留FILES和FILE标签
      expect(result).toContain('<FILES>');
      expect(result).toContain('<FILE path="index.ttml">');
      expect(result).toContain('</FILE>');
      expect(result).toContain('</FILES>');
      
      // 应该移除其他HTML标签
      expect(result).not.toContain('<view');
      expect(result).not.toContain('<text>');
      
      // 应该保留文本内容
      expect(result).toContain('Hello World');
    });

    it('应该处理空内容', () => {
      expect(extractTextContentSafely('')).toBe('');
      expect(extractTextContentSafely(null as any)).toBe('');
      expect(extractTextContentSafely(undefined as any)).toBe('');
    });

    it('应该处理没有FILES标签的内容', () => {
      const input = '<div>Hello <span>World</span></div>';
      const result = extractTextContentSafely(input);
      expect(result).toBe('Hello World');
    });

    it('应该处理复杂的FILE标签属性', () => {
      const input = `<FILES>
<FILE path="index.ttml" type="template">
<div>Content</div>
</FILE>
<FILE path="style.ttss" type="style">
.class { color: red; }
</FILE>
</FILES>`;

      const result = extractTextContentSafely(input);
      
      expect(result).toContain('<FILE path="index.ttml" type="template">');
      expect(result).toContain('<FILE path="style.ttss" type="style">');
      expect(result).not.toContain('<div>');
      expect(result).toContain('Content');
      expect(result).toContain('.class { color: red; }');
    });
  });

  describe('processContentWithProtection', () => {
    it('应该在处理过程中保护FILES和FILE标签', () => {
      const input = `<FILES>
<FILE path="test.ttml">
<div>Test</div>
</FILE>
</FILES>`;

      const processor = (content: string) => {
        // 模拟移除所有HTML标签的处理器
        return content.replace(/<[^>]*>/g, '');
      };

      const result = processContentWithProtection(input, processor);
      
      // FILES和FILE标签应该被保护
      expect(result).toContain('<FILES>');
      expect(result).toContain('<FILE path="test.ttml">');
      expect(result).toContain('</FILE>');
      expect(result).toContain('</FILES>');
      
      // 其他HTML标签应该被移除
      expect(result).not.toContain('<div>');
      expect(result).toContain('Test');
    });
  });

  describe('containsFilesTags', () => {
    it('应该正确检测FILES和FILE标签', () => {
      expect(containsFilesTags('<FILES></FILES>')).toBe(true);
      expect(containsFilesTags('<FILE path="test"></FILE>')).toBe(true);
      expect(containsFilesTags('Some <FILES> content')).toBe(true);
      expect(containsFilesTags('Some <FILE> content')).toBe(true);
      expect(containsFilesTags('<div>No files tags</div>')).toBe(false);
      expect(containsFilesTags('')).toBe(false);
    });
  });

  describe('validateFilesTagFormat', () => {
    it('应该验证正确的格式', () => {
      const validContent = `<FILES>
<FILE path="index.ttml">
Content
</FILE>
</FILES>`;

      const result = validateFilesTagFormat(validContent);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('应该检测到格式错误', () => {
      const invalidContent = `FILES
FILE index.ttml
Content
/FILE
/FILES`;

      const result = validateFilesTagFormat(invalidContent);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('应该检测到标签不配对', () => {
      const unbalancedContent = `<FILES>
<FILE path="test.ttml">
Content
</FILES>`;

      const result = validateFilesTagFormat(unbalancedContent);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('不配对'))).toBe(true);
    });

    it('应该警告缺少path属性', () => {
      const nopathContent = `<FILES>
<FILE>
Content
</FILE>
</FILES>`;

      const result = validateFilesTagFormat(nopathContent);
      expect(result.warnings.some(warning => warning.includes('缺少path属性'))).toBe(true);
    });
  });

  describe('repairFilesTagFormat', () => {
    it('应该修复缺少尖括号的FILES标签', () => {
      const brokenContent = `FILES
FILE path="index.ttml"
Content
/FILE
/FILES`;

      const result = repairFilesTagFormat(brokenContent);
      
      expect(result).toContain('<FILES>');
      expect(result).toContain('<FILE path="index.ttml">');
      expect(result).toContain('</FILE>');
      expect(result).toContain('</FILES>');
    });

    it('应该处理混合格式', () => {
      const mixedContent = `<FILES>
FILE path="test1.ttml"
Content1
</FILE>
<FILE path="test2.ttml">
Content2
/FILE
</FILES>`;

      const result = repairFilesTagFormat(mixedContent);
      
      expect(result).toContain('<FILE path="test1.ttml">');
      expect(result).toContain('<FILE path="test2.ttml">');
      expect(result).toContain('</FILE>');
    });
  });

  describe('真实场景测试', () => {
    it('应该修复AI输出中常见的格式问题', () => {
      // 模拟AI输出的问题格式
      const aiOutput = `FILES
FILE index.ttml
view class="container"
  text Hello World /text
/view
/FILE
FILE style.ttss
.container {
  padding: 20px;
}
/FILE
/FILES`;

      // 修复格式
      const repairedOutput = repairFilesTagFormat(aiOutput);
      
      // 验证修复结果
      const validation = validateFilesTagFormat(repairedOutput);
      expect(validation.isValid).toBe(true);
      
      // 验证可以安全提取文本
      const extractedText = extractTextContentSafely(repairedOutput);
      expect(extractedText).toContain('<FILES>');
      expect(extractedText).toContain('<FILE path="index.ttml">');
      expect(extractedText).toContain('<FILE path="style.ttss">');
    });
  });
});
