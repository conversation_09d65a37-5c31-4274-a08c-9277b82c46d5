<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 FILES Format Parsing Fix Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2196F3;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-case {
            background-color: #f8f9fa;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-input {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .test-output {
            background-color: #e8f5e8;
            border: 1px solid #c8e6c9;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .test-button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 0;
        }
        .test-button:hover {
            background-color: #1976D2;
        }
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        .error {
            color: #f44336;
            font-weight: bold;
        }
        .warning {
            color: #ff9800;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 FILES Format Parsing Fix Test</h1>
        
        <div class="test-case">
            <h3>Test Case 1: 🔧 BEFORE FIX - 用户报告的格式（应该能正确解析）</h3>
            <div class="test-input" id="testInput1">
&lt;FILES&gt;
&lt;FILE path="index.ttml"&gt;
&lt;view class="container"&gt;
    &lt;text class="title"&gt;Hello World&lt;/text&gt;
    &lt;view class="content"&gt;
        &lt;text&gt;This is a test with FILES wrapper&lt;/text&gt;
    &lt;/view&gt;
&lt;/view&gt;
&lt;/FILE&gt;
&lt;FILE path="index.ttss"&gt;
.container {
    padding: 20px;
    background-color: #f5f5f5;
}
.title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}
&lt;/FILE&gt;
&lt;/FILES&gt;
            </div>
            <button class="test-button" onclick="testCurrentParsing('testInput1', 'output1')">测试当前解析逻辑</button>
            <div class="test-output" id="output1"></div>
        </div>

        <div class="test-case">
            <h3>Test Case 2: 🚀 ENHANCED - 改进后的UnifiedClaudeStreamParser</h3>
            <div class="test-input" id="testInput2">
&lt;FILES&gt;
&lt;FILE path="components/App.ttml"&gt;
&lt;view class="app"&gt;
    &lt;text class="header"&gt;App Component&lt;/text&gt;
    &lt;view class="body"&gt;
        &lt;text&gt;App content here&lt;/text&gt;
    &lt;/view&gt;
&lt;/view&gt;
&lt;/FILE&gt;
&lt;FILE path="components/Header.ttml"&gt;
&lt;view class="header-component"&gt;
    &lt;text class="logo"&gt;Logo&lt;/text&gt;
    &lt;text class="nav"&gt;Navigation&lt;/text&gt;
&lt;/view&gt;
&lt;/FILE&gt;
&lt;FILE path="app.js"&gt;
import { App } from './components/App';
import { Header } from './components/Header';

export default function MainApp() {
    return (
        &lt;div&gt;
            &lt;Header /&gt;
            &lt;App /&gt;
        &lt;/div&gt;
    );
}
&lt;/FILE&gt;
&lt;/FILES&gt;
            </div>
            <button class="test-button" onclick="testEnhancedParsing('testInput2', 'output2')">测试增强解析逻辑</button>
            <div class="test-output" id="output2"></div>
        </div>

        <div class="test-case">
            <h3>Test Case 3: 🔍 REGEX VALIDATION - 验证正则表达式</h3>
            <div class="test-input" id="testInput3">
&lt;FILES&gt;
&lt;FILE path="index.ttml"&gt;
&lt;view class="container"&gt;
    &lt;text&gt;Test content&lt;/text&gt;
&lt;/view&gt;
&lt;/FILE&gt;
&lt;/FILES&gt;
            </div>
            <button class="test-button" onclick="testRegexValidation('testInput3', 'output3')">测试正则表达式</button>
            <div class="test-output" id="output3"></div>
        </div>

        <div class="test-case">
            <h3>Test Case 4: 🧪 COMPARISON - 对比batch_processor的解析</h3>
            <div class="test-input" id="testInput4">
&lt;FILES&gt;
&lt;FILE path="index.ttml"&gt;
&lt;view class="container"&gt;
    &lt;text&gt;Batch processor compatible format&lt;/text&gt;
&lt;/view&gt;
&lt;/FILE&gt;
&lt;FILE path="styles.ttss"&gt;
.container {
    padding: 20px;
}
&lt;/FILE&gt;
&lt;/FILES&gt;
            </div>
            <button class="test-button" onclick="testBatchProcessorComparison('testInput4', 'output4')">对比batch_processor</button>
            <div class="test-output" id="output4"></div>
        </div>
    </div>

    <script>
        // 模拟当前的parseLynxCodeToFileStructure函数
        function testCurrentParsing(inputId, outputId) {
            const input = document.getElementById(inputId);
            const output = document.getElementById(outputId);
            
            const testCode = input.textContent.trim();
            
            console.log('🔧 测试当前解析逻辑:', testCode.substring(0, 100));
            
            // 模拟当前的解析逻辑
            const files = {};
            
            // 1. 检查是否有 <FILES> 包装器
            const filesWrapperRegex = /<FILES>([\s\S]*?)<\/FILES>/g;
            let filesWrapperMatch = filesWrapperRegex.exec(testCode);
            
            let outputHtml = '';
            if (filesWrapperMatch) {
                outputHtml += '<div class="success">✅ 检测到 &lt;FILES&gt; 包装器</div>';
                
                const filesContent = filesWrapperMatch[1];
                outputHtml += `<p>提取的内容长度: ${filesContent.length}</p>`;
                
                // 2. 在 <FILES> 内容中查找 <FILE> 标签
                const fileArtifactRegex = /<FILE\s+path=['"]([^'"]+)['"]\s*>([\s\S]*?)<\/FILE>/g;
                let match;
                let fileCount = 0;
                
                while ((match = fileArtifactRegex.exec(filesContent)) !== null) {
                    const filePath = match[1].trim();
                    const fileContent = match[2];
                    
                    files[filePath] = fileContent;
                    fileCount++;
                    
                    outputHtml += `<div><strong>文件 ${fileCount}:</strong> ${filePath} (${fileContent.length} 字符)</div>`;
                    outputHtml += `<div class="test-input" style="margin-top: 5px; font-size: 12px;">${fileContent}</div>`;
                }
                
                if (fileCount === 0) {
                    outputHtml += '<div class="error">❌ 未找到任何 &lt;FILE&gt; 标签</div>';
                } else {
                    outputHtml += `<div class="success">✅ 成功解析 ${fileCount} 个文件</div>`;
                }
            } else {
                outputHtml += '<div class="error">❌ 未检测到 &lt;FILES&gt; 包装器</div>';
            }
            
            output.innerHTML = outputHtml;
        }

        // 模拟增强的解析逻辑
        function testEnhancedParsing(inputId, outputId) {
            const input = document.getElementById(inputId);
            const output = document.getElementById(outputId);
            
            const testCode = input.textContent.trim();
            
            console.log('🚀 测试增强解析逻辑:', testCode.substring(0, 100));
            
            // 模拟增强的UnifiedClaudeStreamParser逻辑
            const result = simulateUnifiedClaudeStreamParser(testCode);
            
            let outputHtml = '';
            if (result.success) {
                outputHtml += '<div class="success">✅ 增强解析成功</div>';
                outputHtml += `<p>解析策略: ${result.strategy}</p>`;
                outputHtml += `<p>文件数量: ${Object.keys(result.files).length}</p>`;
                
                Object.entries(result.files).forEach(([path, content]) => {
                    outputHtml += `<div><strong>文件:</strong> ${path} (${content.length} 字符)</div>`;
                    outputHtml += `<div class="test-input" style="margin-top: 5px; font-size: 12px;">${content}</div>`;
                });
            } else {
                outputHtml += '<div class="error">❌ 增强解析失败</div>';
                outputHtml += `<p>错误: ${result.error}</p>`;
            }
            
            output.innerHTML = outputHtml;
        }

        // 模拟增强的UnifiedClaudeStreamParser
        function simulateUnifiedClaudeStreamParser(code) {
            try {
                const files = {};
                
                // 检查是否有 <FILES> 包装器
                let parseableCode = code;
                if (code.includes('<FILES>')) {
                    console.log('🔍 检测到<FILES>包装器，提取内部内容');
                    
                    const filesWrapperMatch = code.match(/<FILES[^>]*>([\s\S]*?)<\/FILES>/);
                    if (filesWrapperMatch) {
                        parseableCode = filesWrapperMatch[1];
                        console.log('✅ 成功提取<FILES>包装器内容', parseableCode.length);
                    }
                }

                // 使用与batch_processor完全相同的正则表达式
                const fileMatches = parseableCode.match(
                    /<FILE[^>]*(?:path|name)="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/g,
                );

                if (fileMatches) {
                    for (const match of fileMatches) {
                        // 优先匹配 path 属性，如果没有则匹配 name 属性
                        let pathMatch = match.match(/path="([^"]+)"/);
                        if (!pathMatch) {
                            pathMatch = match.match(/name="([^"]+)"/);
                        }

                        const contentMatch = match.match(/<FILE[^>]*>([\s\S]*?)<\/FILE>/);

                        if (pathMatch && contentMatch) {
                            const filePath = pathMatch[1];
                            const fileContent = contentMatch[1];
                            
                            files[filePath] = fileContent;
                        }
                    }
                }

                return {
                    success: Object.keys(files).length > 0,
                    files: files,
                    strategy: 'enhanced_files_parsing'
                };
            } catch (error) {
                return {
                    success: false,
                    files: {},
                    error: error.message
                };
            }
        }

        // 测试正则表达式验证
        function testRegexValidation(inputId, outputId) {
            const input = document.getElementById(inputId);
            const output = document.getElementById(outputId);
            
            const testCode = input.textContent.trim();
            
            console.log('🔍 测试正则表达式验证:', testCode.substring(0, 100));
            
            let outputHtml = '<h4>正则表达式验证结果:</h4>';
            
            // 测试 FILES_WRAPPER 正则
            const filesWrapperRegex = /<FILES>([\s\S]*?)<\/FILES>/g;
            const filesMatch = filesWrapperRegex.exec(testCode);
            
            if (filesMatch) {
                outputHtml += '<div class="success">✅ FILES_WRAPPER 正则表达式匹配成功</div>';
                outputHtml += `<p>提取内容长度: ${filesMatch[1].length}</p>`;
                
                // 测试 FILE_ARTIFACT 正则
                const fileArtifactRegex = /<FILE\s+path=['"]([^'"]+)['"]\s*>([\s\S]*?)<\/FILE>/g;
                let match;
                let fileCount = 0;
                
                while ((match = fileArtifactRegex.exec(filesMatch[1])) !== null) {
                    fileCount++;
                    outputHtml += `<div class="success">✅ FILE_ARTIFACT 正则表达式匹配 ${fileCount}: ${match[1]}</div>`;
                }
                
                if (fileCount === 0) {
                    outputHtml += '<div class="error">❌ FILE_ARTIFACT 正则表达式未匹配任何文件</div>';
                    
                    // 测试更宽松的正则
                    const relaxedRegex = /<FILE[^>]*(?:path|name)="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/g;
                    let relaxedMatch;
                    let relaxedCount = 0;
                    
                    while ((relaxedMatch = relaxedRegex.exec(filesMatch[1])) !== null) {
                        relaxedCount++;
                        outputHtml += `<div class="warning">⚠️ 宽松正则匹配 ${relaxedCount}: ${relaxedMatch[1]}</div>`;
                    }
                    
                    if (relaxedCount > 0) {
                        outputHtml += '<div class="warning">⚠️ 建议使用更宽松的正则表达式</div>';
                    }
                }
            } else {
                outputHtml += '<div class="error">❌ FILES_WRAPPER 正则表达式未匹配</div>';
            }
            
            output.innerHTML = outputHtml;
        }

        // 测试与batch_processor的对比
        function testBatchProcessorComparison(inputId, outputId) {
            const input = document.getElementById(inputId);
            const output = document.getElementById(outputId);
            
            const testCode = input.textContent.trim();
            
            console.log('🧪 测试batch_processor对比:', testCode.substring(0, 100));
            
            // 模拟batch_processor的解析逻辑
            const batchResult = simulateBatchProcessorParsing(testCode);
            
            // 模拟code_generate的解析逻辑
            const codeGenerateResult = simulateUnifiedClaudeStreamParser(testCode);
            
            let outputHtml = '<h4>解析结果对比:</h4>';
            
            outputHtml += '<div style="display: flex; gap: 20px;">';
            
            // batch_processor结果
            outputHtml += '<div style="flex: 1; border: 1px solid #ccc; padding: 10px; border-radius: 4px;">';
            outputHtml += '<h5>batch_processor 结果:</h5>';
            if (batchResult.success) {
                outputHtml += `<div class="success">✅ 解析成功 (${Object.keys(batchResult.files).length} 个文件)</div>`;
                Object.keys(batchResult.files).forEach(path => {
                    outputHtml += `<div>📄 ${path}</div>`;
                });
            } else {
                outputHtml += '<div class="error">❌ 解析失败</div>';
            }
            outputHtml += '</div>';
            
            // code_generate结果
            outputHtml += '<div style="flex: 1; border: 1px solid #ccc; padding: 10px; border-radius: 4px;">';
            outputHtml += '<h5>code_generate 结果:</h5>';
            if (codeGenerateResult.success) {
                outputHtml += `<div class="success">✅ 解析成功 (${Object.keys(codeGenerateResult.files).length} 个文件)</div>`;
                Object.keys(codeGenerateResult.files).forEach(path => {
                    outputHtml += `<div>📄 ${path}</div>`;
                });
            } else {
                outputHtml += '<div class="error">❌ 解析失败</div>';
            }
            outputHtml += '</div>';
            
            outputHtml += '</div>';
            
            // 一致性检查
            if (batchResult.success && codeGenerateResult.success) {
                const batchFiles = Object.keys(batchResult.files).sort();
                const codeGenerateFiles = Object.keys(codeGenerateResult.files).sort();
                
                if (JSON.stringify(batchFiles) === JSON.stringify(codeGenerateFiles)) {
                    outputHtml += '<div class="success">✅ 解析结果一致</div>';
                } else {
                    outputHtml += '<div class="error">❌ 解析结果不一致</div>';
                }
            }
            
            output.innerHTML = outputHtml;
        }

        // 模拟batch_processor的解析逻辑
        function simulateBatchProcessorParsing(code) {
            try {
                const files = {};
                
                // 策略1: 解析 <FILE> 格式 (支持 path 和 name 属性)
                if (code.indexOf('<FILE') !== -1) {
                    const fileMatches = code.match(
                        /<FILE[^>]*(?:path|name)="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/g,
                    );
                    
                    if (fileMatches) {
                        for (const match of fileMatches) {
                            let pathMatch = match.match(/path="([^"]+)"/);
                            if (!pathMatch) {
                                pathMatch = match.match(/name="([^"]+)"/);
                            }
                            
                            const contentMatch = match.match(/<FILE[^>]*>([\s\S]*?)<\/FILE>/);
                            
                            if (pathMatch && contentMatch) {
                                const filePath = pathMatch[1];
                                const fileContent = contentMatch[1];
                                files[filePath] = fileContent;
                            }
                        }
                    }
                }
                
                return {
                    success: Object.keys(files).length > 0,
                    files: files
                };
            } catch (error) {
                return {
                    success: false,
                    files: {}
                };
            }
        }

        // 设置当前时间
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 FILES Format Parsing Fix Test 页面加载完成');
            console.log('测试目标:');
            console.log('1. 验证当前解析逻辑是否正确');
            console.log('2. 测试增强的UnifiedClaudeStreamParser');
            console.log('3. 正则表达式验证');
            console.log('4. 与batch_processor的对比');
        });
    </script>
</body>
</html>