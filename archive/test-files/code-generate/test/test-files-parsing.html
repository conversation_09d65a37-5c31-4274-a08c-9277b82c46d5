<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FILES Format Parsing Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2196F3;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-case {
            background-color: #f8f9fa;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-input {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .test-output {
            background-color: #e8f5e8;
            border: 1px solid #c8e6c9;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .test-button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 0;
        }
        .test-button:hover {
            background-color: #1976D2;
        }
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        .error {
            color: #f44336;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 FILES Format Parsing Test</h1>
        
        <div class="test-case">
            <h3>Test Case 1: 原始 &lt;FILE&gt; 格式 (Batch Processor Style)</h3>
            <div class="test-input" id="testInput1">
&lt;FILE path="index.ttml"&gt;
&lt;view class="container"&gt;
    &lt;text class="title"&gt;Hello World&lt;/text&gt;
    &lt;view class="content"&gt;
        &lt;text&gt;This is a test&lt;/text&gt;
    &lt;/view&gt;
&lt;/view&gt;
&lt;/FILE&gt;
            </div>
            <button class="test-button" onclick="testFileParsing('testInput1', 'output1')">测试解析</button>
            <div class="test-output" id="output1"></div>
        </div>

        <div class="test-case">
            <h3>Test Case 2: 🚀 NEW - &lt;FILES&gt; 包装器格式 (Fixed Format)</h3>
            <div class="test-input" id="testInput2">
&lt;FILES&gt;
&lt;FILE path="index.ttml"&gt;
&lt;view class="container"&gt;
    &lt;text class="title"&gt;Hello World&lt;/text&gt;
    &lt;view class="content"&gt;
        &lt;text&gt;This is a test with FILES wrapper&lt;/text&gt;
    &lt;/view&gt;
&lt;/view&gt;
&lt;/FILE&gt;
&lt;FILE path="styles.ttss"&gt;
.container {
    padding: 20px;
    background-color: #f5f5f5;
}
.title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}
&lt;/FILE&gt;
&lt;/FILES&gt;
            </div>
            <button class="test-button" onclick="testFileParsing('testInput2', 'output2')">测试解析</button>
            <div class="test-output" id="output2"></div>
        </div>

        <div class="test-case">
            <h3>Test Case 3: 多个文件 - &lt;FILES&gt; 包装器</h3>
            <div class="test-input" id="testInput3">
&lt;FILES&gt;
&lt;FILE path="components/App.ttml"&gt;
&lt;view class="app"&gt;
    &lt;text class="header"&gt;App Component&lt;/text&gt;
    &lt;view class="body"&gt;
        &lt;text&gt;App content here&lt;/text&gt;
    &lt;/view&gt;
&lt;/view&gt;
&lt;/FILE&gt;
&lt;FILE path="components/Header.ttml"&gt;
&lt;view class="header-component"&gt;
    &lt;text class="logo"&gt;Logo&lt;/text&gt;
    &lt;text class="nav"&gt;Navigation&lt;/text&gt;
&lt;/view&gt;
&lt;/FILE&gt;
&lt;FILE path="app.js"&gt;
import { App } from './components/App';
import { Header } from './components/Header';

export default function MainApp() {
    return (
        &lt;div&gt;
            &lt;Header /&gt;
            &lt;App /&gt;
        &lt;/div&gt;
    );
}
&lt;/FILE&gt;
&lt;/FILES&gt;
            </div>
            <button class="test-button" onclick="testFileParsing('testInput3', 'output3')">测试解析</button>
            <div class="test-output" id="output3"></div>
        </div>

        <div class="test-case">
            <h3>Test Case 4: 兼容 name 属性格式</h3>
            <div class="test-input" id="testInput4">
&lt;FILES&gt;
&lt;FILE name="index.ttml"&gt;
&lt;view class="container"&gt;
    &lt;text&gt;Using name attribute instead of path&lt;/text&gt;
&lt;/view&gt;
&lt;/FILE&gt;
&lt;/FILES&gt;
            </div>
            <button class="test-button" onclick="testFileParsing('testInput4', 'output4')">测试解析</button>
            <div class="test-output" id="output4"></div>
        </div>
    </div>

    <script>
        // 模拟 UnifiedClaudeStreamParser 的 parseFileTagFormat 方法
        function parseFileTagFormat(code) {
            const files = {};
            
            try {
                console.log('🔍 开始解析FILE标签格式', {
                    codeLength: code.length,
                    hasFilesWrapper: code.includes('<FILES>'),
                    hasFileTag: code.includes('<FILE'),
                    preview: code.substring(0, 300),
                });

                // 🚀 CRITICAL FIX: 支持 <FILES><FILE path="index.ttml">格式
                let parseableCode = code;
                if (code.includes('<FILES>')) {
                    console.log('🔍 检测到<FILES>包装器，提取内部内容');
                    
                    // 提取 <FILES> 标签内的内容
                    const filesWrapperMatch = code.match(/<FILES[^>]*>([\\s\\S]*?)<\\/FILES>/);
                    if (filesWrapperMatch) {
                        parseableCode = filesWrapperMatch[1];
                        console.log('✅ 成功提取<FILES>包装器内容', {
                            extractedLength: parseableCode.length,
                            preview: parseableCode.substring(0, 200),
                        });
                    }
                }

                // 🚀 ENHANCED: 使用与batch_processor完全相同的正则表达式
                const fileMatches = parseableCode.match(
                    /<FILE[^>]*(?:path|name)="([^"]+)"[^>]*>([\\s\\S]*?)<\\/FILE>/g,
                );

                console.log('📊 FILE标签匹配结果', {
                    matchCount: fileMatches?.length || 0,
                    matches: fileMatches?.map(match => match.substring(0, 100) + '...') || [],
                });

                if (fileMatches) {
                    for (const match of fileMatches) {
                        console.log('🔍 处理FILE标签', {
                            matchPreview: match.substring(0, 100) + '...',
                        });

                        // 优先匹配 path 属性，如果没有则匹配 name 属性
                        let pathMatch = match.match(/path="([^"]+)"/);
                        if (!pathMatch) {
                            pathMatch = match.match(/name="([^"]+)"/);
                        }

                        const contentMatch = match.match(/<FILE[^>]*>([\\s\\S]*?)<\\/FILE>/);

                        if (pathMatch && contentMatch) {
                            const filePath = pathMatch[1];
                            const fileContent = contentMatch[1];
                            
                            // 保持原始格式
                            files[filePath] = fileContent;
                            
                            console.log('✅ 成功提取FILE标签内容', {
                                filePath,
                                contentLength: fileContent.length,
                                hasNewlines: fileContent.includes('\\n'),
                                preview: fileContent.substring(0, 100),
                            });
                        } else {
                            console.warn('❌ FILE标签格式不正确', {
                                pathMatch: !!pathMatch,
                                contentMatch: !!contentMatch,
                                matchPreview: match.substring(0, 200),
                            });
                        }
                    }
                }

                return { success: Object.keys(files).length > 0, files };
            } catch (error) {
                console.error('❌ FILE标签解析失败', error);
                return { success: false, files: {} };
            }
        }

        function testFileParsing(inputId, outputId) {
            const input = document.getElementById(inputId);
            const output = document.getElementById(outputId);
            
            const testCode = input.textContent.trim();
            
            console.log('🚀 开始测试解析:', { inputId, testCode: testCode.substring(0, 100) });
            
            const result = parseFileTagFormat(testCode);
            
            let outputHtml = '';
            if (result.success) {
                outputHtml = '<div class="success">✅ 解析成功!</div>';
                outputHtml += '<h4>提取的文件:</h4>';
                outputHtml += '<ul>';
                for (const [path, content] of Object.entries(result.files)) {
                    outputHtml += `<li><strong>${path}</strong> (${content.length} 字符)<br>`;
                    outputHtml += `<div class="test-input" style="margin-top: 5px;">${content}</div></li>`;
                }
                outputHtml += '</ul>';
            } else {
                outputHtml = '<div class="error">❌ 解析失败</div>';
            }
            
            output.innerHTML = outputHtml;
        }

        // 设置当前时间
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 FILES Format Parsing Test 页面加载完成');
            console.log('测试支持的格式:');
            console.log('1. 原始 <FILE path="..."> 格式');
            console.log('2. 🆕 <FILES><FILE path="..."> 包装器格式');
            console.log('3. 多文件支持');
            console.log('4. path 和 name 属性兼容');
        });
    </script>
</body>
</html>