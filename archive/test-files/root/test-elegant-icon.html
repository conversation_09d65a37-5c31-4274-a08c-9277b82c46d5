<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优雅图标容器 - 简洁设计</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .demo-section {
            margin: 40px 0;
            padding: 30px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            background: #f8fafc;
        }

        .icon-demo {
            display: flex;
            gap: 50px;
            flex-wrap: wrap;
            margin: 30px 0;
            justify-content: center;
        }

        /* 优化前 - 复杂的星光容器 */
        .starlight-container-old {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            animation: complexAnimation 4s ease-in-out infinite;
        }

        .starlight-effect-old {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.8) 1px, rgba(147, 197, 253, 0.4) 3px, transparent 5px),
                radial-gradient(circle at 70% 20%, rgba(147, 197, 253, 0.6) 1px, rgba(255, 255, 255, 0.3) 3px, transparent 5px),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.7) 1px, rgba(147, 197, 253, 0.3) 3px, transparent 5px),
                radial-gradient(circle at 30% 80%, rgba(147, 197, 253, 0.5) 1px, rgba(255, 255, 255, 0.2) 3px, transparent 5px);
            background-size: 100% 100%;
            animation: starTwinkle 3s ease-in-out infinite;
            pointer-events: none;
            z-index: 1;
            border-radius: inherit;
        }

        @keyframes complexAnimation {

            0%,
            100% {
                transform: scale(1);
                box-shadow: 0 4px 16px rgba(59, 130, 246, 0.25);
            }

            50% {
                transform: scale(1.05);
                box-shadow: 0 6px 24px rgba(59, 130, 246, 0.35);
            }
        }

        @keyframes starTwinkle {

            0%,
            100% {
                opacity: 0.6;
                transform: scale(1);
            }

            50% {
                opacity: 0.9;
                transform: scale(1.05);
            }
        }

        /* 优化后 - 优雅的图标容器 */
        .elegant-icon-container {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px auto;
            border-radius: 50%;
            background: linear-gradient(135deg,
                    rgba(59, 130, 246, 0.9) 0%,
                    rgba(37, 99, 235, 0.95) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;

            /* 优雅的阴影 */
            box-shadow:
                0 4px 20px rgba(59, 130, 246, 0.25),
                0 1px 3px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);

            /* 温和的动画 */
            animation: elegantFloat 6s ease-in-out infinite;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .elegant-icon-container:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow:
                0 8px 30px rgba(59, 130, 246, 0.35),
                0 2px 6px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        /* ✨ 精致的星光闪烁效果 */
        .elegant-icon-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: inherit;
            background:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),
                radial-gradient(circle at 75% 30%, rgba(255, 255, 255, 0.6) 0.5px, transparent 1.5px),
                radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.7) 1px, transparent 2px),
                radial-gradient(circle at 20% 70%, rgba(255, 255, 255, 0.5) 0.5px, transparent 1.5px);
            background-size: 100% 100%;
            animation: gentleSparkle 8s ease-in-out infinite;
            pointer-events: none;
            z-index: 1;
        }

        /* 🌟 随机闪烁的星光点 */
        .elegant-icon-container::after {
            content: '✨';
            position: absolute;
            top: -8px;
            right: -8px;
            font-size: 12px;
            opacity: 0;
            transform: scale(0.5) rotate(0deg);
            animation: surpriseSparkle 12s ease-in-out infinite;
            pointer-events: none;
            z-index: 3;
            filter: hue-rotate(0deg) brightness(1.2);
        }

        /* 🌟 悬停时增强星光效果 */
        .elegant-icon-container:hover::before {
            animation: gentleSparkle 2s ease-in-out infinite, hoverSparkleBoost 0.6s ease-out;
        }

        .elegant-icon-container:hover::after {
            animation: surpriseSparkle 3s ease-in-out infinite, hoverSurpriseBoost 0.8s ease-out;
        }

        .elegant-icon-container .icon-main {
            position: relative;
            z-index: 2;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
            color: white;
            font-size: 24px;
        }

        @keyframes elegantFloat {

            0%,
            100% {
                transform: translateY(0) scale(1);
                box-shadow:
                    0 4px 20px rgba(59, 130, 246, 0.25),
                    0 1px 3px rgba(0, 0, 0, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
            }

            50% {
                transform: translateY(-3px) scale(1.02);
                box-shadow:
                    0 6px 25px rgba(59, 130, 246, 0.3),
                    0 2px 5px rgba(0, 0, 0, 0.12),
                    inset 0 1px 0 rgba(255, 255, 255, 0.25);
            }
        }

        @keyframes gentleSparkle {

            0%,
            100% {
                opacity: 0.3;
                transform: scale(1) rotate(0deg);
            }

            25% {
                opacity: 0.6;
                transform: scale(1.05) rotate(90deg);
            }

            50% {
                opacity: 0.4;
                transform: scale(1.02) rotate(180deg);
            }

            75% {
                opacity: 0.7;
                transform: scale(1.03) rotate(270deg);
            }
        }

        @keyframes surpriseSparkle {

            0%,
            85%,
            100% {
                opacity: 0;
                transform: scale(0.5) rotate(0deg);
            }

            87% {
                opacity: 0.8;
                transform: scale(1.2) rotate(180deg);
            }

            92% {
                opacity: 1;
                transform: scale(1.4) rotate(360deg);
            }

            97% {
                opacity: 0.6;
                transform: scale(1.1) rotate(540deg);
            }
        }

        @keyframes hoverSparkleBoost {
            0% {
                opacity: 0.3;
                transform: scale(1) rotate(0deg);
            }

            50% {
                opacity: 0.9;
                transform: scale(1.1) rotate(180deg);
            }

            100% {
                opacity: 0.6;
                transform: scale(1.05) rotate(360deg);
            }
        }

        @keyframes hoverSurpriseBoost {
            0% {
                opacity: 0;
                transform: scale(0.5) rotate(0deg);
            }

            30% {
                opacity: 1;
                transform: scale(1.6) rotate(180deg);
            }

            60% {
                opacity: 0.8;
                transform: scale(1.3) rotate(360deg);
            }

            100% {
                opacity: 0.6;
                transform: scale(1.1) rotate(540deg);
            }
        }

        .demo-item {
            text-align: center;
            margin: 20px;
        }

        .demo-label {
            margin-top: 20px;
            font-weight: 600;
            color: #1e40af;
            font-size: 16px;
        }

        .demo-description {
            margin-top: 8px;
            font-size: 14px;
            color: #64748b;
            max-width: 200px;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }

        .before,
        .after {
            padding: 25px;
            border-radius: 12px;
            text-align: center;
        }

        .before {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 2px solid #ef4444;
        }

        .after {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border: 2px solid #22c55e;
        }

        .status {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .status.old {
            color: #dc2626;
        }

        .status.new {
            color: #16a34a;
        }

        .feature-list {
            text-align: left;
            margin: 20px 0;
        }

        .feature-list li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }

        .feature-list li::before {
            content: '✨';
            position: absolute;
            left: 0;
            top: 0;
        }

        .icon-old {
            position: relative;
            z-index: 2;
            color: white;
            font-size: 24px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>✨ 优雅图标容器 - 简洁设计</h1>

        <div class="demo-section">
            <h2>🎨 设计对比</h2>
            <div class="icon-demo">
                <div class="demo-item">
                    <div class="starlight-container-old">
                        <div class="starlight-effect-old"></div>
                        <div class="icon-old">⚡</div>
                    </div>
                    <div class="demo-label">优化前</div>
                    <div class="demo-description">复杂的星光特效，过于花哨，视觉噪音多</div>
                </div>

                <div class="demo-item">
                    <div class="elegant-icon-container">
                        <div class="icon-main">⚡</div>
                    </div>
                    <div class="demo-label">优化后</div>
                    <div class="demo-description">简洁优雅，温和的浮动动画，视觉舒适</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔄 详细对比</h2>
            <div class="comparison-grid">
                <div class="before">
                    <div class="status old">❌ 优化前 - 过于复杂</div>
                    <ul class="feature-list">
                        <li>多层径向渐变星光点</li>
                        <li>复杂的闪烁动画</li>
                        <li>视觉噪音过多</li>
                        <li>分散用户注意力</li>
                        <li>动画过于频繁</li>
                        <li>设计过度装饰</li>
                    </ul>
                </div>

                <div class="after">
                    <div class="status new">✅ 优化后 - 简洁优雅 + 星光惊喜</div>
                    <ul class="feature-list">
                        <li>清晰的渐变背景</li>
                        <li>温和的浮动动画</li>
                        <li>精致的星光闪烁点</li>
                        <li>随机出现的✨emoji惊喜</li>
                        <li>悬停时增强的星光效果</li>
                        <li>优雅的阴影层次</li>
                        <li>舒适的交互反馈</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎯 设计原则</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #3b82f6;">
                    <h3>🎨 简洁至上</h3>
                    <p>移除不必要的视觉装饰，专注于核心功能和用户体验</p>
                </div>
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #60a5fa;">
                    <h3>🌊 温和动画</h3>
                    <p>使用缓慢、温和的浮动动画，避免过于频繁的视觉变化</p>
                </div>
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #93c5fd;">
                    <h3>💎 质感提升</h3>
                    <p>通过精心设计的阴影和渐变，提升视觉质感和层次感</p>
                </div>
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #dbeafe;">
                    <h3>🎯 用户友好</h3>
                    <p>减少视觉干扰，让用户专注于内容和操作</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🚀 技术实现</h2>
            <h3>CSS关键特性：</h3>
            <ul style="margin: 20px 0; padding-left: 20px;">
                <li><strong>渐变背景</strong>：使用半透明的蓝色渐变，保持清晰度</li>
                <li><strong>多层阴影</strong>：外阴影 + 内阴影，创造立体感</li>
                <li><strong>温和动画</strong>：6秒循环的浮动动画，不会分散注意力</li>
                <li><strong>悬停反馈</strong>：微妙的位移和缩放，提供交互反馈</li>
                <li><strong>图标优化</strong>：添加阴影滤镜，增强视觉层次</li>
            </ul>

            <h3>动画时长优化：</h3>
            <p>从3-4秒的快速动画改为6秒的缓慢动画，减少视觉疲劳</p>
        </div>
    </div>

    <script>
        console.log('✨ 优雅图标容器演示页面已加载');
        console.log('🎯 设计理念：简洁、优雅、用户友好');
        console.log('🚀 优化重点：移除过度装饰，专注核心体验');
    </script>
</body>

</html>