/**
 * 验证FILES标签保护修复效果的测试脚本
 */

// 模拟修复前的问题函数
function problematicTextExtraction(content) {
    // 这是修复前的代码，会移除所有HTML标签
    return content.replace(/<[^>]*>/g, '').trim();
}

// 修复后的安全函数
function extractTextContentSafely(content) {
    if (!content) {
        return '';
    }

    // 先保护FILES和FILE标签
    let protectedContent = content
        .replace(/<FILES>/g, '___PROTECTED_FILES_START___')
        .replace(/<\/FILES>/g, '___PROTECTED_FILES_END___')
        .replace(/<FILE([^>]*)>/g, '___PROTECTED_FILE_START___$1___PROTECTED_FILE_ATTR_END___')
        .replace(/<\/FILE>/g, '___PROTECTED_FILE_END___');

    // 移除其他HTML标签
    protectedContent = protectedContent.replace(/<[^>]*>/g, '');

    // 恢复保护的标签
    const restoredContent = protectedContent
        .replace(/___PROTECTED_FILES_START___/g, '<FILES>')
        .replace(/___PROTECTED_FILES_END___/g, '</FILES>')
        .replace(/___PROTECTED_FILE_START___([^_]*)___PROTECTED_FILE_ATTR_END___/g, '<FILE$1>')
        .replace(/___PROTECTED_FILE_END___/g, '</FILE>');

    return restoredContent.trim();
}

// 测试用例
const testCases = [
    {
        name: '基本FILES格式',
        input: `<FILES>
<FILE path="index.ttml">
<view class="container">
  <text>Hello World</text>
</view>
</FILE>
</FILES>`
    },
    {
        name: '多文件格式',
        input: `<FILES>
<FILE path="index.ttml">
<view class="container">
  <text>Hello World</text>
</view>
</FILE>
<FILE path="style.ttss">
.container {
  padding: 20px;
}
</FILE>
</FILES>`
    },
    {
        name: '复杂属性格式',
        input: `<FILES>
<FILE path="index.ttml" type="template">
<view class="container">
  <text>Hello World</text>
</view>
</FILE>
</FILES>`
    }
];

console.log('🔍 FILES标签保护修复效果验证\n');

testCases.forEach((testCase, index) => {
    console.log(`\n📋 测试用例 ${index + 1}: ${testCase.name}`);
    console.log('=' .repeat(50));
    
    console.log('\n📥 原始输入:');
    console.log(testCase.input);
    
    console.log('\n❌ 修复前结果 (问题版本):');
    const problematicResult = problematicTextExtraction(testCase.input);
    console.log(problematicResult);
    
    console.log('\n✅ 修复后结果 (安全版本):');
    const safeResult = extractTextContentSafely(testCase.input);
    console.log(safeResult);
    
    // 验证修复效果
    const hasFilesTag = safeResult.includes('<FILES>');
    const hasFileTag = safeResult.includes('<FILE');
    const hasClosingTags = safeResult.includes('</FILE>') && safeResult.includes('</FILES>');
    
    console.log('\n🔍 验证结果:');
    console.log(`  - 保留<FILES>标签: ${hasFilesTag ? '✅' : '❌'}`);
    console.log(`  - 保留<FILE>标签: ${hasFileTag ? '✅' : '❌'}`);
    console.log(`  - 保留闭合标签: ${hasClosingTags ? '✅' : '❌'}`);
    
    const isFixed = hasFilesTag && hasFileTag && hasClosingTags;
    console.log(`  - 整体修复状态: ${isFixed ? '✅ 修复成功' : '❌ 仍有问题'}`);
});

console.log('\n🎯 总结:');
console.log('修复前: <FILES><FILE path="index.ttml"> → FILES FILE index.ttml (尖括号丢失)');
console.log('修复后: <FILES><FILE path="index.ttml"> → <FILES><FILE path="index.ttml"> (格式保持)');
console.log('\n✅ 修复方案已实施到以下文件:');
console.log('  - src/routes/code_generate/utils/htmlModifier.ts');
console.log('  - src/routes/code_generate/utils/fastHtmlEditor.ts');
console.log('  - src/routes/code_generate/utils/filesTagProtector.ts (新增工具)');
