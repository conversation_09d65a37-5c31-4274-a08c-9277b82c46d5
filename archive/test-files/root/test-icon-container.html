<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标容器优化 - Light Blue到白色渐变</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .demo-section {
            margin: 40px 0;
            padding: 30px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            background: #f8fafc;
        }
        
        .icon-demo {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
            margin: 20px 0;
            justify-content: center;
        }
        
        /* 优化前的图标容器 - 棕色/金色 */
        .icon-container-old {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg,
                #f59e0b 0%,     /* 温和橙金 */
                #d97706 50%,    /* 深橙金色 */
                #b45309 100%    /* 更深橙金 */
            );
            color: white;
            box-shadow: 
                0 4px 16px rgba(251, 191, 36, 0.3),
                0 2px 8px rgba(253, 230, 138, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }
        
        .icon-container-old:hover {
            background: linear-gradient(135deg,
                #fbbf24 0%,     /* 亮金色 */
                #f59e0b 50%,    /* 温和橙金 */
                #d97706 100%    /* 深橙金色 */
            );
            box-shadow: 0 6px 20px rgba(251, 191, 36, 0.4),
                0 3px 10px rgba(217, 119, 6, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.3);
            transform: translateY(-1px) scale(1.02);
        }
        
        /* 优化后的图标容器 - Light Blue到白色渐变 */
        .icon-container-new {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg,
                #87ceeb 0%,     /* Light Blue */
                #b3d9f2 30%,    /* 浅蓝色 */
                #ddeef8 70%,    /* 极浅蓝色 */
                #ffffff 100%    /* 白色 */
            );
            color: #1e40af;
            border: 1px solid rgba(135, 206, 235, 0.3);
            box-shadow: 
                0 4px 16px rgba(135, 206, 235, 0.25),
                0 2px 8px rgba(179, 217, 242, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }
        
        .icon-container-new:hover {
            background: linear-gradient(135deg,
                #60a5fa 0%,     /* 亮蓝色 */
                #93c5fd 30%,    /* 中等蓝色 */
                #dbeafe 70%,    /* 浅蓝色 */
                #ffffff 100%    /* 白色 */
            );
            box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3),
                0 3px 10px rgba(147, 197, 253, 0.2),
                inset 0 2px 0 rgba(255, 255, 255, 0.9);
            transform: translateY(-1px) scale(1.02);
        }
        
        .icon-symbol {
            font-size: 24px;
            font-weight: bold;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .before, .after {
            padding: 25px;
            border-radius: 12px;
            text-align: center;
        }
        
        .before {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 2px solid #ef4444;
        }
        
        .after {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border: 2px solid #22c55e;
        }
        
        .status {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .status.old {
            color: #dc2626;
        }
        
        .status.new {
            color: #16a34a;
        }
        
        .feature-list {
            text-align: left;
            margin: 20px 0;
        }
        
        .feature-list li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .feature-list li::before {
            content: '✨';
            position: absolute;
            left: 0;
            top: 0;
        }
        
        .color-palette {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            justify-content: center;
        }
        
        .color-swatch {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .color-label {
            font-size: 12px;
            margin-top: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 图标容器优化 - Light Blue到白色渐变</h1>
        
        <div class="demo-section">
            <h2>✨ 优化效果对比</h2>
            <div class="icon-demo">
                <div style="text-align: center;">
                    <div class="icon-container-old">
                        <span class="icon-symbol">▶</span>
                    </div>
                    <p style="margin-top: 10px; color: #dc2626; font-weight: bold;">优化前 - 棕色/金色</p>
                </div>
                
                <div style="text-align: center;">
                    <div class="icon-container-new">
                        <span class="icon-symbol">▶</span>
                    </div>
                    <p style="margin-top: 10px; color: #16a34a; font-weight: bold;">优化后 - Light Blue到白色</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔄 详细对比</h2>
            <div class="comparison-grid">
                <div class="before">
                    <div class="status old">❌ 优化前 - 棕色主题</div>
                    <div class="color-palette">
                        <div>
                            <div class="color-swatch" style="background: #f59e0b;"></div>
                            <div class="color-label">橙金</div>
                        </div>
                        <div>
                            <div class="color-swatch" style="background: #d97706;"></div>
                            <div class="color-label">深橙金</div>
                        </div>
                        <div>
                            <div class="color-swatch" style="background: #b45309;"></div>
                            <div class="color-label">更深橙金</div>
                        </div>
                    </div>
                    <ul class="feature-list">
                        <li>颜色偏棕色，视觉不够清新</li>
                        <li>与整体蓝色主题不协调</li>
                        <li>显得过于厚重</li>
                        <li>白色图标在金色背景上对比度一般</li>
                    </ul>
                </div>
                
                <div class="after">
                    <div class="status new">✅ 优化后 - Light Blue主题</div>
                    <div class="color-palette">
                        <div>
                            <div class="color-swatch" style="background: #87ceeb;"></div>
                            <div class="color-label">Light Blue</div>
                        </div>
                        <div>
                            <div class="color-swatch" style="background: #b3d9f2;"></div>
                            <div class="color-label">浅蓝色</div>
                        </div>
                        <div>
                            <div class="color-swatch" style="background: #ddeef8;"></div>
                            <div class="color-label">极浅蓝</div>
                        </div>
                        <div>
                            <div class="color-swatch" style="background: #ffffff;"></div>
                            <div class="color-label">白色</div>
                        </div>
                    </div>
                    <ul class="feature-list">
                        <li>清新的蓝白渐变，视觉舒适</li>
                        <li>与整体界面主题完美协调</li>
                        <li>现代简洁的设计风格</li>
                        <li>深蓝色图标在浅色背景上对比度极佳</li>
                        <li>渐变从蓝色到白色，层次丰富</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎯 设计理念</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #87ceeb;">
                    <h3>🌊 Light Blue基调</h3>
                    <p>使用经典的Light Blue (#87ceeb)作为起始色，营造清新自然的视觉感受</p>
                </div>
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #b3d9f2;">
                    <h3>🎨 渐变过渡</h3>
                    <p>通过多层次的蓝色渐变到白色，创造丰富的视觉层次</p>
                </div>
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #1e40af;">
                    <h3>🎯 对比度优化</h3>
                    <p>深蓝色图标在浅色背景上具有极佳的可读性和视觉对比</p>
                </div>
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #ddeef8;">
                    <h3>🌟 主题一致性</h3>
                    <p>与整体界面的蓝色主题保持完美一致，提升整体设计协调性</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🚀 技术实现</h2>
            <h3>CSS渐变代码：</h3>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; font-family: monospace; margin: 15px 0;">
                <div style="color: #059669; margin-bottom: 10px;">/* 基础渐变 */</div>
                <div>background: linear-gradient(135deg,</div>
                <div style="margin-left: 20px;">#87ceeb 0%,     /* Light Blue */</div>
                <div style="margin-left: 20px;">#b3d9f2 30%,    /* 浅蓝色 */</div>
                <div style="margin-left: 20px;">#ddeef8 70%,    /* 极浅蓝色 */</div>
                <div style="margin-left: 20px;">#ffffff 100%    /* 白色 */</div>
                <div>);</div>
                
                <div style="color: #059669; margin: 15px 0 10px 0;">/* 悬停效果 */</div>
                <div>background: linear-gradient(135deg,</div>
                <div style="margin-left: 20px;">#60a5fa 0%,     /* 亮蓝色 */</div>
                <div style="margin-left: 20px;">#93c5fd 30%,    /* 中等蓝色 */</div>
                <div style="margin-left: 20px;">#dbeafe 70%,    /* 浅蓝色 */</div>
                <div style="margin-left: 20px;">#ffffff 100%    /* 白色 */</div>
                <div>);</div>
            </div>
        </div>
    </div>

    <script>
        console.log('🎨 图标容器优化演示页面已加载');
        console.log('✨ 主题：从棕色/金色改为Light Blue到白色渐变');
        console.log('🎯 目标：提升视觉协调性和现代感');
    </script>
</body>
</html>
