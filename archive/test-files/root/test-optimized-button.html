<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化按钮UI - 浅金色星光效果</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .demo-section {
            margin: 40px 0;
            padding: 30px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            background: #f8fafc;
        }

        .button-demo {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin: 20px 0;
            justify-content: center;
        }

        /* 优化后的浅金色星光按钮 */
        .btn-primary-optimized {
            /* 基础布局 */
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;

            /* 尺寸和间距 */
            padding: 16px 32px;
            min-height: 56px;
            border-radius: 12px;
            border: 1px solid rgba(251, 191, 36, 0.3);

            /* 浅金色渐变背景 */
            background: linear-gradient(135deg,
                    #fefce8 0%,
                    /* 极浅的奶黄色 */
                    #fef3c7 20%,
                    /* 浅黄色 */
                    #fde68a 40%,
                    /* 柔和金色 */
                    #fcd34d 60%,
                    /* 中等金色 */
                    #f59e0b 100%
                    /* 温和的橙金色 */
                );
            background-size: 200% 200%;
            color: #92400e;

            /* 优化的阴影和光效 */
            box-shadow:
                0 4px 16px rgba(251, 191, 36, 0.25),
                0 2px 8px rgba(253, 230, 138, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.8),
                inset 0 -1px 0 rgba(251, 191, 36, 0.1);

            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 浅金色星光特效 */
        .btn-primary-optimized::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.9) 1px, rgba(255, 248, 220, 0.6) 3px, transparent 5px),
                radial-gradient(circle at 70% 20%, rgba(255, 248, 220, 0.8) 1px, rgba(255, 255, 255, 0.5) 3px, transparent 5px),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.7) 1px, rgba(255, 248, 220, 0.4) 3px, transparent 5px),
                radial-gradient(circle at 30% 80%, rgba(255, 248, 220, 0.6) 1px, rgba(255, 255, 255, 0.3) 3px, transparent 5px);
            background-size: 100% 100%;
            animation: gentleStarTwinkle 4s ease-in-out infinite;
            pointer-events: none;
            z-index: 1;
            border-radius: inherit;
        }

        @keyframes gentleStarTwinkle {

            0%,
            100% {
                opacity: 0.6;
                transform: scale(1);
            }

            50% {
                opacity: 0.9;
                transform: scale(1.05);
            }
        }

        /* 渐变高光扫过效果 */
        .btn-primary-optimized::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg,
                    transparent 30%,
                    rgba(255, 255, 255, 0.4) 50%,
                    transparent 70%);
            transform: translateX(-100%) translateY(-100%) rotate(45deg);
            transition: transform 0.8s ease;
            pointer-events: none;
            z-index: 2;
        }

        .btn-primary-optimized:hover::after {
            transform: translateX(100%) translateY(100%) rotate(45deg);
        }

        /* 悬停效果 */
        .btn-primary-optimized:hover {
            background: linear-gradient(135deg,
                    #fde68a 0%,
                    #fcd34d 25%,
                    #fbbf24 50%,
                    #f59e0b 75%,
                    #d97706 100%);
            background-position: 100% 100%;

            box-shadow:
                0 8px 32px rgba(251, 191, 36, 0.4),
                0 4px 16px rgba(253, 230, 138, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.9),
                inset 0 -2px 0 rgba(251, 191, 36, 0.2),
                0 0 40px rgba(251, 191, 36, 0.3);

            transform: translateY(-2px) scale(1.02);
        }

        /* 星光emoji效果 */
        .btn-primary-optimized.sparkle::after {
            content: '✨';
            position: absolute;
            top: 8px;
            right: 8px;
            font-size: 14px;
            opacity: 0;
            transform: scale(0.5) rotate(0deg);
            transition: all 0.4s ease;
            pointer-events: none;
            z-index: 4;
            filter: hue-rotate(30deg) brightness(1.2);
        }

        .btn-primary-optimized.sparkle:hover::after {
            opacity: 0.9;
            transform: scale(1.1) rotate(180deg);
            animation: sparkleFloat 2.5s ease-in-out infinite;
        }

        @keyframes sparkleFloat {

            0%,
            100% {
                transform: scale(1.1) rotate(180deg) translateY(0);
                filter: hue-rotate(30deg) brightness(1.2);
            }

            50% {
                transform: scale(1.3) rotate(180deg) translateY(-3px);
                filter: hue-rotate(45deg) brightness(1.4);
            }
        }

        /* 确保按钮内容在特效之上 */
        .btn-primary-optimized>* {
            position: relative;
            z-index: 3;
        }

        /* 处理中状态 */
        .btn-primary-optimized.processing {
            background: linear-gradient(135deg,
                    #fef3c7 0%,
                    #fde68a 50%,
                    #fcd34d 100%);
            animation: processing-pulse 2.5s ease-in-out infinite;
        }

        @keyframes processing-pulse {

            0%,
            100% {
                box-shadow:
                    0 4px 16px rgba(251, 191, 36, 0.3),
                    0 2px 8px rgba(253, 230, 138, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.8);
                transform: scale(1);
            }

            50% {
                box-shadow:
                    0 6px 24px rgba(251, 191, 36, 0.45),
                    0 3px 12px rgba(253, 230, 138, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.9),
                    0 0 30px rgba(251, 191, 36, 0.2);
                transform: scale(1.01);
            }
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }

        .before,
        .after {
            padding: 25px;
            border-radius: 12px;
            text-align: center;
        }

        .before {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 2px solid #ef4444;
        }

        .after {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border: 2px solid #22c55e;
        }

        .status {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .status.old {
            color: #dc2626;
        }

        .status.new {
            color: #16a34a;
        }

        .feature-list {
            text-align: left;
            margin: 20px 0;
        }

        .feature-list li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }

        .feature-list li::before {
            content: '✨';
            position: absolute;
            left: 0;
            top: 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🎨 优化按钮UI - 浅金色星光效果</h1>

        <div class="demo-section">
            <h2>✨ 优化后的按钮效果</h2>
            <div class="button-demo">
                <button class="btn-primary-optimized sparkle">
                    <div class="shimmer-effect"></div>
                    <span>⚡</span>
                    <span>处理 5 条查询</span>
                    <span style="opacity: 0.75;">→</span>
                </button>

                <button class="btn-primary-optimized sparkle processing">
                    <div class="shimmer-effect"></div>
                    <span>🔄</span>
                    <span>处理中 3/5</span>
                    <span
                        style="background: rgba(146, 64, 14, 0.15); padding: 2px 6px; border-radius: 10px; font-size: 11px; margin-left: 6px;">60%</span>
                </button>
            </div>

            <div
                style="margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #f59e0b;">
                <h3>🎯 实际应用中的按钮</h3>
                <p>在实际的batch processor页面中，按钮使用以下类名：</p>
                <code style="background: #f8f9fa; padding: 4px 8px; border-radius: 4px; font-family: monospace;">
                    className="btn-primary sparkle-hover"
                </code>
                <p style="margin-top: 10px;">现在已经通过高优先级选择器 <code>body .batch-processor-layout .btn-primary</code>
                    来确保样式生效。</p>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔄 优化对比</h2>
            <div class="comparison-grid">
                <div class="before">
                    <div class="status old">❌ 优化前</div>
                    <p>基础蓝色按钮，缺乏视觉吸引力</p>
                    <ul class="feature-list">
                        <li>单调的蓝色渐变</li>
                        <li>缺少特效和动画</li>
                        <li>视觉层次感不足</li>
                        <li>交互反馈平淡</li>
                    </ul>
                </div>

                <div class="after">
                    <div class="status new">✅ 优化后</div>
                    <p>浅金色星光设计，视觉效果出众</p>
                    <ul class="feature-list">
                        <li>优雅的浅金色渐变</li>
                        <li>动态星光特效</li>
                        <li>渐变高光扫过</li>
                        <li>星光emoji动画</li>
                        <li>处理中脉冲效果</li>
                        <li>丰富的交互反馈</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎯 设计特点</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #f59e0b;">
                    <h3>🌟 浅金色星光</h3>
                    <p>使用径向渐变创建的星光点，营造梦幻的视觉效果</p>
                </div>
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #fcd34d;">
                    <h3>✨ 渐变高光</h3>
                    <p>悬停时的高光扫过效果，增强交互反馈</p>
                </div>
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #fde68a;">
                    <h3>🎭 Emoji动画</h3>
                    <p>星光emoji的旋转和浮动动画，增加趣味性</p>
                </div>
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #fef3c7;">
                    <h3>💫 处理状态</h3>
                    <p>处理中的温和脉冲效果，提供清晰的状态反馈</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🚀 技术实现</h2>
            <h3>CSS关键技术：</h3>
            <ul>
                <li><strong>多层渐变背景</strong>：创建丰富的金色层次</li>
                <li><strong>径向渐变星光</strong>：模拟真实的星光效果</li>
                <li><strong>CSS动画</strong>：流畅的过渡和动态效果</li>
                <li><strong>伪元素特效</strong>：::before和::after创建特效层</li>
                <li><strong>Z-index层级</strong>：确保特效和内容的正确层叠</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('🎨 优化按钮UI演示页面已加载');
        console.log('✨ 特效包括：浅金色星光、渐变高光、emoji动画');
        console.log('🎯 设计目标：提升视觉吸引力和用户体验');
    </script>
</body>

</html>