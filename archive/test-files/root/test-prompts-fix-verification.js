/**
 * 验证Prompts修复效果的测试脚本
 * 检查提示词文件中是否正确使用了<FILES>和<FILE>标签格式
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证Prompts文件中的FILES格式修复效果\n');

// 要检查的文件列表
const filesToCheck = [
    'src/routes/batch_processor/prompts/LynxFrameworkCore.ts',
    'src/routes/batch_processor/prompts/ModularPromptLoader.ts',
    'src/routes/batch_processor/prompts/LynxOutputFormat.ts'
];

let totalIssues = 0;
let fixedIssues = 0;

filesToCheck.forEach((filePath, index) => {
    console.log(`\n📋 检查文件 ${index + 1}: ${filePath}`);
    console.log('=' .repeat(60));
    
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 检查问题模式
        const problemPatterns = [
            {
                name: '缺少尖括号的FILES标签',
                pattern: /使用FILES和FILE标签/g,
                shouldBe: '使用<FILES>和<FILE>标签'
            },
            {
                name: '缺少尖括号的FILES作为根容器',
                pattern: /FILES作为根容器/g,
                shouldBe: '<FILES>作为根容器'
            },
            {
                name: '缺少尖括号的FILE path',
                pattern: /FILE path包裹/g,
                shouldBe: '<FILE path="文件路径">包裹'
            }
        ];
        
        let fileHasIssues = false;
        
        problemPatterns.forEach(pattern => {
            const matches = content.match(pattern.pattern);
            if (matches) {
                console.log(`❌ 发现问题: ${pattern.name}`);
                console.log(`   匹配次数: ${matches.length}`);
                console.log(`   应该改为: ${pattern.shouldBe}`);
                totalIssues += matches.length;
                fileHasIssues = true;
            }
        });
        
        // 检查正确格式
        const correctPatterns = [
            {
                name: '正确的<FILES>和<FILE>标签',
                pattern: /使用<FILES>和<FILE>标签/g
            },
            {
                name: '正确的<FILES>作为根容器',
                pattern: /<FILES>作为根容器/g
            },
            {
                name: '正确的<FILE path>格式',
                pattern: /<FILE path="[^"]*">包裹/g
            }
        ];
        
        let hasCorrectFormat = false;
        correctPatterns.forEach(pattern => {
            const matches = content.match(pattern.pattern);
            if (matches) {
                console.log(`✅ 发现正确格式: ${pattern.name}`);
                console.log(`   匹配次数: ${matches.length}`);
                fixedIssues += matches.length;
                hasCorrectFormat = true;
            }
        });
        
        if (!fileHasIssues && hasCorrectFormat) {
            console.log('✅ 文件格式正确，无需修复');
        } else if (!fileHasIssues && !hasCorrectFormat) {
            console.log('ℹ️  文件中未找到相关格式说明');
        }
        
    } catch (error) {
        console.log(`❌ 读取文件失败: ${error.message}`);
    }
});

console.log('\n🎯 总结报告');
console.log('=' .repeat(60));
console.log(`📊 检查文件数量: ${filesToCheck.length}`);
console.log(`❌ 发现问题数量: ${totalIssues}`);
console.log(`✅ 修复格式数量: ${fixedIssues}`);

if (totalIssues === 0 && fixedIssues > 0) {
    console.log('\n🎉 修复完成！所有prompts文件中的格式问题已解决');
    console.log('✅ AI现在会看到正确的<FILES>和<FILE>标签格式要求');
    console.log('✅ 这将大大降低输出时尖括号丢失的概率');
} else if (totalIssues > 0) {
    console.log('\n⚠️  仍有问题需要修复');
    console.log('🔧 请检查上述文件中标记的问题');
} else {
    console.log('\n📝 未在检查的文件中找到相关格式说明');
}

console.log('\n🔍 修复前后对比:');
console.log('修复前: "使用FILES和FILE标签包裹" ❌');
console.log('修复后: "使用<FILES>和<FILE>标签包裹" ✅');
console.log('\n这个修复解决了AI输出格式问题的根本原因！');
