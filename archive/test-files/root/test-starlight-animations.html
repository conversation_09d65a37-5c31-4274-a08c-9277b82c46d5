<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星光主题动画优化</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .demo-section {
            margin: 40px 0;
            padding: 30px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            background: #f8fafc;
        }
        
        .animation-demo {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
            margin: 20px 0;
            justify-content: center;
        }
        
        /* 星光容器动画 */
        .starlight-container {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            animation: gentleIconBreathe 4s ease-in-out infinite;
        }
        
        @keyframes gentleIconBreathe {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 4px 16px rgba(59, 130, 246, 0.25);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 6px 24px rgba(59, 130, 246, 0.35);
            }
        }
        
        .starlight-effect {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.8) 1px, rgba(147, 197, 253, 0.4) 3px, transparent 5px),
                radial-gradient(circle at 70% 20%, rgba(147, 197, 253, 0.6) 1px, rgba(255, 255, 255, 0.3) 3px, transparent 5px),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.7) 1px, rgba(147, 197, 253, 0.3) 3px, transparent 5px),
                radial-gradient(circle at 30% 80%, rgba(147, 197, 253, 0.5) 1px, rgba(255, 255, 255, 0.2) 3px, transparent 5px);
            background-size: 100% 100%;
            animation: gentleStarTwinkle 3s ease-in-out infinite;
            pointer-events: none;
            z-index: 1;
            border-radius: inherit;
        }
        
        @keyframes gentleStarTwinkle {
            0%, 100% {
                opacity: 0.6;
                transform: scale(1);
            }
            50% {
                opacity: 0.9;
                transform: scale(1.05);
            }
        }
        
        .icon-symbol {
            color: white;
            font-size: 24px;
            position: relative;
            z-index: 2;
        }
        
        /* 星光脉冲文本框 */
        .starlight-pulse-demo {
            width: 300px;
            height: 100px;
            border: 2px solid #93c5fd;
            border-radius: 8px;
            padding: 12px;
            background: white;
            position: relative;
            animation: starlightPulse 1.2s ease-in-out;
        }
        
        .starlight-pulse-demo::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background:
                radial-gradient(circle at 25% 25%, rgba(147, 197, 253, 0.6) 1px, transparent 3px),
                radial-gradient(circle at 75% 25%, rgba(255, 255, 255, 0.8) 1px, transparent 3px),
                radial-gradient(circle at 75% 75%, rgba(147, 197, 253, 0.6) 1px, transparent 3px),
                radial-gradient(circle at 25% 75%, rgba(255, 255, 255, 0.8) 1px, transparent 3px);
            background-size: 50px 50px;
            border-radius: inherit;
            animation: gentleStarTwinkle 0.8s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }
        
        @keyframes starlightPulse {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(147, 197, 253, 0.4);
                border-color: #93c5fd;
            }
            50% {
                box-shadow: 0 0 0 8px rgba(147, 197, 253, 0.1);
                border-color: #3b82f6;
            }
        }
        
        /* 星光清空动画 */
        .starlight-clear-demo {
            width: 200px;
            height: 60px;
            border: 2px solid #93c5fd;
            border-radius: 8px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            animation: starlightClear 0.6s ease-in-out;
        }
        
        @keyframes starlightClear {
            0% {
                transform: scale(1);
                opacity: 1;
                box-shadow: 0 0 0 0 rgba(147, 197, 253, 0.4);
            }
            25% {
                transform: scale(0.98);
                opacity: 0.8;
                box-shadow: 0 0 0 4px rgba(147, 197, 253, 0.3);
            }
            50% {
                transform: scale(1.02);
                opacity: 0.6;
                box-shadow: 0 0 0 8px rgba(147, 197, 253, 0.2);
            }
            75% {
                transform: scale(0.99);
                opacity: 0.8;
                box-shadow: 0 0 0 4px rgba(147, 197, 253, 0.3);
            }
            100% {
                transform: scale(1);
                opacity: 1;
                box-shadow: 0 0 0 0 rgba(147, 197, 253, 0.4);
            }
        }
        
        .demo-item {
            text-align: center;
            margin: 20px;
        }
        
        .demo-label {
            margin-top: 15px;
            font-weight: 600;
            color: #1e40af;
        }
        
        .demo-description {
            margin-top: 5px;
            font-size: 14px;
            color: #64748b;
        }
        
        .trigger-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 10px;
            transition: all 0.2s ease;
        }
        
        .trigger-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .before, .after {
            padding: 25px;
            border-radius: 12px;
            text-align: center;
        }
        
        .before {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 2px solid #ef4444;
        }
        
        .after {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border: 2px solid #22c55e;
        }
        
        .status {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .status.old {
            color: #dc2626;
        }
        
        .status.new {
            color: #16a34a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 星光主题动画优化</h1>
        
        <div class="demo-section">
            <h2>✨ 优化后的动画效果</h2>
            <div class="animation-demo">
                <div class="demo-item">
                    <div class="starlight-container">
                        <div class="starlight-effect"></div>
                        <div class="icon-symbol">⚡</div>
                    </div>
                    <div class="demo-label">星光容器</div>
                    <div class="demo-description">温和的呼吸动画 + 星光闪烁</div>
                </div>
                
                <div class="demo-item">
                    <div class="starlight-pulse-demo">
                        <div style="color: #64748b;">文本框脉冲效果</div>
                    </div>
                    <div class="demo-label">星光脉冲</div>
                    <div class="demo-description">边框脉冲 + 星光边框</div>
                    <button class="trigger-btn" onclick="triggerPulse()">触发脉冲</button>
                </div>
                
                <div class="demo-item">
                    <div class="starlight-clear-demo" onclick="triggerClear(this)">
                        <span>点击清空</span>
                    </div>
                    <div class="demo-label">星光清空</div>
                    <div class="demo-description">缩放 + 透明度 + 光环扩散</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔄 动画对比</h2>
            <div class="comparison-grid">
                <div class="before">
                    <div class="status old">❌ 优化前</div>
                    <ul style="text-align: left; margin: 20px 0;">
                        <li>简单的 animate-pulse 类</li>
                        <li>基础的 animate-shake 效果</li>
                        <li>单调的闪烁动画</li>
                        <li>缺乏主题一致性</li>
                        <li>视觉效果平淡</li>
                    </ul>
                </div>
                
                <div class="after">
                    <div class="status new">✅ 优化后</div>
                    <ul style="text-align: left; margin: 20px 0;">
                        <li>复用星光icon的动画效果</li>
                        <li>统一的蓝色星光主题</li>
                        <li>多层次的视觉特效</li>
                        <li>温和的呼吸和脉冲动画</li>
                        <li>丰富的交互反馈</li>
                        <li>与整体设计风格一致</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎯 技术实现</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #3b82f6;">
                    <h3>🌟 星光特效</h3>
                    <p>使用径向渐变创建星光点，配合 gentleStarTwinkle 动画</p>
                </div>
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #93c5fd;">
                    <h3>💫 呼吸动画</h3>
                    <p>gentleIconBreathe 动画，温和的缩放和阴影变化</p>
                </div>
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #dbeafe;">
                    <h3>🎭 脉冲效果</h3>
                    <p>starlightPulse 动画，边框颜色和光环扩散</p>
                </div>
                <div style="padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #eff6ff;">
                    <h3>✨ 清空动画</h3>
                    <p>starlightClear 动画，缩放、透明度和光环的组合</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🚀 应用场景</h2>
            <ul style="margin: 20px 0; padding-left: 20px;">
                <li><strong>QueryInputPanel 图标容器</strong>：使用 starlight-container 类</li>
                <li><strong>文本框脉冲提示</strong>：使用 starlight-pulse 类</li>
                <li><strong>清空操作反馈</strong>：使用 starlight-clear 类</li>
                <li><strong>统一主题风格</strong>：所有动画都基于蓝色星光主题</li>
            </ul>
        </div>
    </div>

    <script>
        function triggerPulse() {
            const element = document.querySelector('.starlight-pulse-demo');
            element.style.animation = 'none';
            setTimeout(() => {
                element.style.animation = 'starlightPulse 1.2s ease-in-out';
            }, 10);
        }
        
        function triggerClear(element) {
            element.style.animation = 'none';
            setTimeout(() => {
                element.style.animation = 'starlightClear 0.6s ease-in-out';
            }, 10);
        }
        
        console.log('🌟 星光主题动画优化演示页面已加载');
        console.log('✨ 特效：复用星光icon动画，统一主题风格');
        console.log('🎯 目标：提升动画一致性和视觉体验');
    </script>
</body>
</html>
