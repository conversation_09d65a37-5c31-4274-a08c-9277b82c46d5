<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星光效果修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-container {
            text-align: center;
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        /* 复制实际的CSS样式 */
        .batch-processor-layout .elegant-icon-container {
            width: 64px !important;
            height: 64px !important;
            margin: 0 auto 16px auto !important;
            border-radius: 50% !important;
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.9) 0%,
                rgba(37, 99, 235, 0.95) 100%) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            position: relative !important;
            box-shadow:
                0 4px 20px rgba(59, 130, 246, 0.25),
                0 1px 3px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
            animation: elegantFloat 6s ease-in-out infinite !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }
        
        .batch-processor-layout .elegant-icon-container::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            border-radius: inherit !important;
            background:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 1) 2px, transparent 4px),
                radial-gradient(circle at 75% 30%, rgba(255, 255, 255, 0.8) 1.5px, transparent 3px),
                radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 1) 2px, transparent 4px),
                radial-gradient(circle at 20% 70%, rgba(255, 255, 255, 0.8) 1.5px, transparent 3px) !important;
            background-size: 100% 100% !important;
            animation: gentleSparkle 4s ease-in-out infinite !important;
            pointer-events: none !important;
            z-index: 1 !important;
        }
        
        .batch-processor-layout .elegant-icon-container::after {
            content: '✨' !important;
            position: absolute !important;
            top: -10px !important;
            right: -10px !important;
            font-size: 16px !important;
            opacity: 0 !important;
            transform: scale(0.5) rotate(0deg) !important;
            animation: surpriseSparkle 6s ease-in-out infinite !important;
            pointer-events: none !important;
            z-index: 3 !important;
            filter: hue-rotate(0deg) brightness(1.5) drop-shadow(0 0 2px rgba(255, 255, 255, 0.8)) !important;
        }
        
        .batch-processor-layout .elegant-icon-container .icon-main {
            position: relative !important;
            z-index: 2 !important;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) !important;
            color: white;
            font-size: 24px;
        }
        
        @keyframes elegantFloat {
            0%, 100% {
                transform: translateY(0) scale(1);
                box-shadow:
                    0 4px 20px rgba(59, 130, 246, 0.25),
                    0 1px 3px rgba(0, 0, 0, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
            }
            50% {
                transform: translateY(-3px) scale(1.02);
                box-shadow:
                    0 6px 25px rgba(59, 130, 246, 0.3),
                    0 2px 5px rgba(0, 0, 0, 0.12),
                    inset 0 1px 0 rgba(255, 255, 255, 0.25);
            }
        }
        
        @keyframes gentleSparkle {
            0%, 100% {
                opacity: 0.8;
                transform: scale(1) rotate(0deg);
            }
            25% {
                opacity: 1;
                transform: scale(1.05) rotate(90deg);
            }
            50% {
                opacity: 0.9;
                transform: scale(1.02) rotate(180deg);
            }
            75% {
                opacity: 1;
                transform: scale(1.03) rotate(270deg);
            }
        }
        
        @keyframes surpriseSparkle {
            0%, 70%, 100% {
                opacity: 0;
                transform: scale(0.5) rotate(0deg);
            }
            75% {
                opacity: 1;
                transform: scale(1.3) rotate(180deg);
            }
            85% {
                opacity: 1;
                transform: scale(1.5) rotate(360deg);
            }
            95% {
                opacity: 0.8;
                transform: scale(1.2) rotate(540deg);
            }
        }
        
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 8px;
            font-weight: bold;
        }
        
        .working {
            background: #dcfce7;
            color: #16a34a;
            border: 2px solid #22c55e;
        }
        
        .broken {
            background: #fee2e2;
            color: #dc2626;
            border: 2px solid #ef4444;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🌟 星光效果修复测试</h1>
        
        <div class="batch-processor-layout">
            <div class="elegant-icon-container">
                <div class="icon-main">⚡</div>
            </div>
        </div>
        
        <div id="status" class="status working">
            ✅ 星光效果应该正常工作
        </div>
        
        <div style="margin-top: 30px;">
            <h3>应该看到的效果：</h3>
            <ul style="text-align: left; max-width: 400px; margin: 0 auto;">
                <li>🔵 蓝色渐变圆形容器</li>
                <li>✨ 4个白色星光点在容器内闪烁</li>
                <li>🌟 右上角偶尔出现✨emoji</li>
                <li>🌊 容器轻微上下浮动</li>
                <li>💫 星光点会旋转和缩放</li>
            </ul>
        </div>
        
        <div style="margin-top: 20px; font-size: 14px; color: #64748b;">
            如果看不到星光效果，请检查CSS选择器优先级
        </div>
    </div>

    <script>
        console.log('🌟 星光效果测试页面已加载');
        
        // 检查动画是否在运行
        setTimeout(() => {
            const container = document.querySelector('.elegant-icon-container');
            const beforeElement = window.getComputedStyle(container, '::before');
            const afterElement = window.getComputedStyle(container, '::after');
            
            console.log('容器样式:', window.getComputedStyle(container));
            console.log('::before 内容:', beforeElement.content);
            console.log('::after 内容:', afterElement.content);
            
            if (beforeElement.content === '""' && afterElement.content.includes('✨')) {
                document.getElementById('status').textContent = '✅ 星光效果正常工作！';
                document.getElementById('status').className = 'status working';
            } else {
                document.getElementById('status').textContent = '❌ 星光效果未生效';
                document.getElementById('status').className = 'status broken';
            }
        }, 1000);
    </script>
</body>
</html>
