// VSCode 测试文件
// 如果您能看到这个文件，说明 VSCode 基本功能正常

console.log('VSCode 测试成功！');

// 测试功能:
// 1. 文件内容是否正常显示 ✅
// 2. 语法高亮是否正常 ✅ 
// 3. 智能提示是否正常 (输入 console. 试试)
// 4. 错误提示是否正常 (故意写错语法试试)

const testObj = {
  name: 'VSCode测试',
  status: 'running',
  files: ['test-vscode.js', 'package.json', 'README.md']
};

// 如果能看到这些文件，请尝试打开它们:
// - package.json
// - src/routes/batch_processor/page.tsx
// - src/routes/page.tsx

function testVSCode() {
  return 'VSCode 工作正常！';
}