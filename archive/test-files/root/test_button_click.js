// 在浏览器控制台中运行这个脚本来测试按钮
const buttons = document.querySelectorAll('button');
console.log('找到的按钮数量:', buttons.length);

for (let i = 0; i < buttons.length; i++) {
  const btn = buttons[i];
  if (btn.textContent && btn.textContent.includes('打开所有成功结果')) {
    console.log('找到目标按钮:', btn.textContent);
    console.log('按钮可见:', btn.offsetWidth > 0 && btn.offsetHeight > 0);
    console.log('按钮可点击:', \!btn.disabled);
    
    // 模拟点击
    console.log('点击按钮...');
    btn.click();
    break;
  }
}
EOF < /dev/null
