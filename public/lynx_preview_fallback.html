<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lynx Preview - 在线转换工具</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .header {
            background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 50%, #81d4fa 100%);
            color: #0277bd;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 6px rgba(2, 119, 189, 0.1);
        }
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #0277bd, #29b6f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .content {
            flex: 1;
            padding: 2rem;
            max-width: 800px;
            margin: 0 auto;
        }
        .status-card {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
        }
        .status-working {
            border-left: 4px solid #0ea5e9;
        }
        .status-info {
            border-left: 4px solid #3b82f6;
        }
        .status-warning {
            border-left: 4px solid #f59e0b;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        .feature-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
            margin: 0.5rem;
        }
        .btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        .btn-secondary {
            background: #6b7280;
        }
        .btn-secondary:hover {
            background: #4b5563;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Lynx Preview</h1>
        <p>在线转换工具 - 完全基于浏览器的 @byted-lynx/web-speedy-plugin 实现</p>
    </div>

    <div class="content">
        <div class="status-card status-working">
            <h2>✅ 服务状态正常</h2>
            <p>Lynx Preview 系统正在运行，所有核心服务已就绪。</p>
        </div>

        <div class="status-card status-info">
            <h2>📋 使用说明</h2>
            <p>Lynx Preview 是一个交互式工具页面，需要您提供输入才会显示转换内容：</p>
            <ol style="margin-top: 1rem; padding-left: 2rem;">
                <li><strong>CDN URL 输入</strong> - 输入包含TTML/TTSS文件的ZIP链接</li>
                <li><strong>本地文件上传</strong> - 拖拽或选择本地ZIP文件</li>
                <li><strong>Playground URL</strong> - 输入Playground分享链接</li>
            </ol>
        </div>

        <div class="status-card status-warning">
            <h2>🔧 技术状态</h2>
            <p><strong>当前状态：</strong> React应用正在加载中，如果您看到这个页面，可能是以下原因：</p>
            <ul style="margin-top: 1rem; padding-left: 2rem;">
                <li>客户端JavaScript正在加载</li>
                <li>HMR热更新正在处理</li>
                <li>网络连接较慢</li>
            </ul>
            <p style="margin-top: 1rem;"><em>请等待几秒钟，页面应该会自动切换到完整版本。</em></p>
        </div>

        <div style="text-align: center; margin: 2rem 0;">
            <button class="btn" onclick="location.reload()">🔄 刷新页面</button>
            <button class="btn btn-secondary" onclick="window.open('http://localhost:8082/', '_blank')">🏠 返回首页</button>
            <button class="btn btn-secondary" onclick="window.open('http://localhost:8082/batch_processor', '_blank')">⚡ 批处理工具</button>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">📦</div>
                <h3>CDN URL 解析</h3>
                <p>支持从CDN链接直接下载和解析Lynx项目文件，自动识别TTML、TTSS和配置文件。</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🎮</div>
                <h3>Playground 集成</h3>
                <p>兼容Lynx Playground的分享链接，可以直接从Playground导入项目进行预览。</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📁</div>
                <h3>本地文件上传</h3>
                <p>支持拖拽上传本地ZIP文件，无需上传到服务器即可完成转换和预览。</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔄</div>
                <h3>实时转换</h3>
                <p>基于@byted-lynx/web-speedy-plugin的浏览器端转换引擎，TTML/TTSS转HTML/CSS。</p>
            </div>
        </div>
    </div>

    <script>
        console.log('📱 [Fallback] Lynx Preview fallback页面已加载');
        
        // 定期检查主应用是否加载
        let checkCount = 0;
        const maxChecks = 20; // 最多检查20次（20秒）
        
        const checkMainApp = () => {
            checkCount++;
            
            // 检查是否有React根节点内容
            const root = document.getElementById('root');
            if (root && root.children.length > 0) {
                console.log('✅ [Fallback] 检测到主应用已加载，隐藏fallback页面');
                document.body.style.display = 'none';
                return;
            }
            
            if (checkCount < maxChecks) {
                setTimeout(checkMainApp, 1000);
            } else {
                console.warn('⚠️ [Fallback] 主应用加载超时，继续显示fallback页面');
            }
        };
        
        // 延迟5秒开始检查，给主应用足够的加载时间
        setTimeout(checkMainApp, 5000);
        
        // 显示当前时间
        const updateTime = () => {
            const now = new Date().toLocaleTimeString();
            console.log(`🕐 [Fallback] 当前时间: ${now}`);
        };
        updateTime();
        setInterval(updateTime, 30000); // 每30秒更新一次
    </script>
</body>
</html>