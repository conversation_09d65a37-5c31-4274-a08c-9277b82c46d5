<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>窗口检测测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
    </style>
</head>
<body>
    <h1>🔧 窗口检测修复测试</h1>
    
    <div class="test-card">
        <h2>问题说明</h2>
        <p>原始问题：即使窗口成功打开，仍然弹出"所有链接都被浏览器阻止了！"的alert。</p>
        <p><strong>根本原因：</strong>window.open() 在跨域情况下即使成功也可能返回null，原有的检测逻辑不准确。</p>
    </div>

    <div class="test-card">
        <h2>修复方案</h2>
        <p>1. 使用 Promise + setTimeout 异步检测窗口状态</p>
        <p>2. 检测条件：<code>newWindow === null || (newWindow && newWindow.closed)</code></p>
        <p>3. 50ms延迟确保窗口状态稳定</p>
        <p>4. 准确统计成功/失败数量</p>
    </div>

    <div class="test-card">
        <h2>测试区域</h2>
        <button onclick="testOldMethod()">🚫 旧方法测试 (有问题)</button>
        <button onclick="testNewMethod()">✅ 新方法测试 (修复后)</button>
        <button onclick="testMultipleWindows()">🔄 批量打开测试</button>
        <button onclick="clearLog()">🧹 清空日志</button>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            logElement.innerHTML = '';
        }
        
        // 旧方法 (有问题的检测逻辑)
        function testOldMethod() {
            log('=== 旧方法测试开始 ===', 'info');
            const testUrls = [
                'https://www.google.com',
                'https://www.baidu.com',
                'https://github.com'
            ];
            
            let openedCount = 0;
            let failedCount = 0;
            
            testUrls.forEach((url, i) => {
                log(`尝试打开第 ${i + 1} 个链接: ${url}`, 'info');
                
                try {
                    const newWindow = window.open(url, '_blank', 'noopener,noreferrer');
                    
                    // 旧的检测逻辑 (有问题)
                    if (newWindow) {
                        openedCount++;
                        log(`✅ 旧方法认为第 ${i + 1} 个链接成功打开`, 'success');
                    } else {
                        failedCount++;
                        log(`❌ 旧方法认为第 ${i + 1} 个链接被阻止`, 'error');
                    }
                } catch (error) {
                    failedCount++;
                    log(`❌ 打开第 ${i + 1} 个链接时出错: ${error.message}`, 'error');
                }
            });
            
            setTimeout(() => {
                log(`旧方法统计结果: 成功 ${openedCount} 个，失败 ${failedCount} 个`, 'info');
                if (failedCount > 0) {
                    log('⚠️ 旧方法可能会误报"所有链接都被浏览器阻止了！"', 'error');
                }
            }, 100);
        }
        
        // 新方法 (修复后的检测逻辑)
        async function testNewMethod() {
            log('=== 新方法测试开始 ===', 'info');
            const testUrls = [
                'https://www.google.com',
                'https://www.baidu.com', 
                'https://github.com'
            ];
            
            let openedCount = 0;
            let failedCount = 0;
            
            // 使用修复后的逻辑
            const openPromises = testUrls.map(async (url, i) => {
                log(`尝试打开第 ${i + 1} 个链接: ${url}`, 'info');
                
                try {
                    const newWindow = window.open(url, '_blank', 'noopener,noreferrer');
                    
                    // 新的检测逻辑 (修复后)
                    return new Promise((resolve) => {
                        setTimeout(() => {
                            if (newWindow === null || (newWindow && newWindow.closed)) {
                                log(`❌ 新方法检测第 ${i + 1} 个链接被阻止`, 'error');
                                resolve({ success: false, url });
                            } else {
                                log(`✅ 新方法检测第 ${i + 1} 个链接成功打开`, 'success');
                                resolve({ success: true, url });
                            }
                        }, 50);
                    });
                } catch (error) {
                    log(`❌ 打开第 ${i + 1} 个链接时出错: ${error.message}`, 'error');
                    return Promise.resolve({ success: false, url });
                }
            });
            
            // 等待所有检测完成
            const results = await Promise.all(openPromises);
            openedCount = results.filter(r => r.success).length;
            failedCount = results.filter(r => !r.success).length;
            
            log(`新方法最终统计: 成功 ${openedCount} 个，失败 ${failedCount} 个`, 'success');
            
            // 模拟用户反馈
            if (failedCount === 0) {
                if (openedCount > 0) {
                    log('✅ 新方法反馈: 成功打开所有链接！', 'success');
                }
            } else if (openedCount > 0) {
                log(`⚠️ 新方法反馈: 部分成功 - 打开了 ${openedCount} 个，${failedCount} 个被阻止`, 'info');
            } else {
                log('❌ 新方法反馈: 所有链接都被浏览器阻止了！', 'error');
            }
        }
        
        // 批量打开测试
        async function testMultipleWindows() {
            log('=== 批量打开测试 ===', 'info');
            log('这个测试可能会被浏览器的弹窗阻止器拦截', 'info');
            
            const testUrls = [
                'https://www.example.com',
                'https://httpbin.org/get',
                'https://jsonplaceholder.typicode.com/posts/1',
                'https://api.github.com',
                'https://www.w3.org'
            ];
            
            const openPromises = testUrls.map(async (url, i) => {
                try {
                    const newWindow = window.open(url, '_blank', 'noopener,noreferrer');
                    
                    return new Promise((resolve) => {
                        setTimeout(() => {
                            if (newWindow === null || (newWindow && newWindow.closed)) {
                                resolve({ success: false, url });
                            } else {
                                resolve({ success: true, url });
                            }
                        }, 50);
                    });
                } catch (error) {
                    return Promise.resolve({ success: false, url });
                }
            });
            
            const results = await Promise.all(openPromises);
            const openedCount = results.filter(r => r.success).length;
            const failedCount = results.filter(r => !r.success).length;
            
            log(`批量测试结果: ${testUrls.length} 个链接，成功 ${openedCount} 个，失败 ${failedCount} 个`, 'success');
            
            // 显示详细结果
            results.forEach((result, i) => {
                const status = result.success ? '✅ 成功' : '❌ 失败';
                log(`  ${i + 1}. ${status} - ${result.url}`, result.success ? 'success' : 'error');
            });
        }
        
        // 页面加载完成后的说明
        window.addEventListener('load', () => {
            log('窗口检测修复测试页面已加载', 'success');
            log('点击上方按钮测试不同的窗口检测方法', 'info');
            log('注意：某些链接可能会被浏览器的弹窗阻止器拦截', 'info');
        });
    </script>
</body>
</html>