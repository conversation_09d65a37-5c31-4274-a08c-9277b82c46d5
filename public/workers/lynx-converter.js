/**
 * Lynx到HTML转换Web Worker
 * 轻量级转换器，只做必要的基础转换
 */

class LynxConverter {
  constructor() {
    // 基础标签映射
    this.tagMap = {
      view: 'div',
      text: 'span',
      image: 'img',
      'scroll-view': 'div',
      list: 'div',
      'list-item': 'div',
    };

    // 默认配置
    this.config = {
      rpxRatio: 0.5, // 1rpx = 0.5px (基于375px宽度)
      timeout: 10000, // 10秒超时
      preserveWhitespace: true,
    };
  }

  /**
   * 主转换方法
   */
  convert(content, options = {}) {
    const startTime = performance.now();

    try {
      // 1. 预处理内容
      const preprocessed = this.preprocessContent(content);

      // 2. 解析文件结构
      const files = this.parseFileStructure(preprocessed);

      // 3. 验证必要文件
      this.validateFiles(files);

      // 4. 转换各部分
      const html = this.convertTTML(files);
      const css = this.convertTTSS(files);
      const js = this.convertJS(files);

      // 5. 生成完整HTML
      const fullHTML = this.generateHTML(html, css, js);

      const endTime = performance.now();

      return {
        success: true,
        html: fullHTML,
        processingTime: endTime - startTime,
      };
    } catch (error) {
      return {
        success: false,
        error: error.name || 'CONVERSION_ERROR',
        message: error.message,
        processingTime: performance.now() - startTime,
      };
    }
  }

  /**
   * 预处理内容
   */
  preprocessContent(content) {
    if (!content || typeof content !== 'string') {
      throw new Error('内容为空或格式无效');
    }

    let processed = content;

    // 清理转义字符
    processed = processed
      .replace(/\\n/g, '\n')
      .replace(/\\"/g, '"')
      .replace(/\\</g, '<')
      .replace(/\\>/g, '>');

    // 移除外层<FILES>包装（如果存在）
    if (processed.includes('<FILES>')) {
      const filesMatch = processed.match(/<FILES>([\s\S]*?)<\/FILES>/);
      if (filesMatch) {
        processed = filesMatch[1].trim();
      }
    }

    return processed;
  }

  /**
   * 解析文件结构
   */
  parseFileStructure(content) {
    const files = {};

    // 匹配<FILE>标签
    const filePattern = /<FILE\s+path="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/g;
    let match;

    while ((match = filePattern.exec(content)) !== null) {
      const [, path, fileContent] = match;
      files[path] = fileContent.trim();
    }

    // 如果没有找到FILE标签，尝试直接解析内容
    if (Object.keys(files).length === 0) {
      if (this.looksLikeTTML(content)) {
        files['index.ttml'] = content;
      } else {
        throw new Error('无法识别文件格式，请确保内容包含有效的Lynx代码');
      }
    }

    return files;
  }

  /**
   * 验证文件
   */
  validateFiles(files) {
    const tttmlFiles = Object.keys(files).filter(
      path => path.endsWith('.ttml') || this.looksLikeTTML(files[path]),
    );

    if (tttmlFiles.length === 0) {
      throw new Error('未找到TTML文件，无法进行转换');
    }
  }

  /**
   * 转换TTML到HTML
   */
  convertTTML(files) {
    // 找到TTML文件
    const tttmlFile =
      files['index.ttml'] ||
      Object.entries(files).find(
        ([path, content]) =>
          path.endsWith('.ttml') || this.looksLikeTTML(content),
      )?.[1];

    if (!tttmlFile) {
      throw new Error('未找到TTML内容');
    }

    let html = tttmlFile;

    // 基础标签转换
    Object.entries(this.tagMap).forEach(([lynxTag, htmlTag]) => {
      // 转换开始标签
      const openRegex = new RegExp(`<${lynxTag}(\\s[^>]*?)?>`, 'g');
      html = html.replace(openRegex, `<${htmlTag}$1>`);

      // 转换结束标签
      const closeRegex = new RegExp(`</${lynxTag}>`, 'g');
      html = html.replace(closeRegex, `</${htmlTag}>`);
    });

    // 处理事件绑定
    html = html.replace(/bindtap="([^"]*)"/g, 'onclick="handleTap(\'$1\')"');
    html = html.replace(
      /bindlongpress="([^"]*)"/g,
      'oncontextmenu="handleLongPress(\'$1\'); return false;"',
    );
    html = html.replace(
      /catchtap="([^"]*)"/g,
      'onclick="handleTap(\'$1\'); event.stopPropagation();"',
    );

    // 简单处理模板语法
    html = html.replace(
      /\{\{([^}]+)\}\}/g,
      '<span class="data-placeholder">[$1]</span>',
    );

    // 移除Lynx特有属性
    html = html.replace(/tt:for="[^"]*"/g, '');
    html = html.replace(/tt:if="[^"]*"/g, '');
    html = html.replace(/tt:key="[^"]*"/g, '');
    html = html.replace(/tt:elif="[^"]*"/g, '');
    html = html.replace(/tt:else/g, '');

    return html;
  }

  /**
   * 转换TTSS到CSS
   */
  convertTTSS(files) {
    // 找到TTSS文件
    const ttssFile =
      files['index.ttss'] ||
      Object.entries(files).find(
        ([path, content]) =>
          path.endsWith('.ttss') || this.looksLikeTTSS(content),
      )?.[1];

    if (!ttssFile) {
      return this.getDefaultCSS();
    }

    let css = ttssFile;

    // rpx单位转换
    css = css.replace(/(\d+(?:\.\d+)?)rpx/g, (match, value) => {
      const pxValue = parseFloat(value) * this.config.rpxRatio;
      return `${pxValue}px`;
    });

    // 移除Lynx特有属性
    css = css.replace(/enable-scroll\s*:\s*[^;]+;?/g, '');
    css = css.replace(/scroll-[xy]\s*:\s*[^;]+;?/g, '');
    css = css.replace(/clip-radius\s*:\s*[^;]+;?/g, '');
    css = css.replace(/block-native-event\s*:\s*[^;]+;?/g, '');

    return css;
  }

  /**
   * 转换JavaScript
   */
  convertJS(files) {
    // 找到JS文件
    const jsFile =
      files['index.js'] ||
      Object.entries(files).find(
        ([path, content]) => path.endsWith('.js') || this.looksLikeJS(content),
      )?.[1];

    if (!jsFile) {
      return this.getDefaultJS();
    }

    let js = jsFile;

    // 简单的Card语法转换
    js = js.replace(/Card\s*\(\s*\{/, 'const component = {');
    js = js.replace(/this\.setData/g, 'component.setData');
    js = js.replace(/this\.data/g, 'component.data');

    return js;
  }

  /**
   * 生成完整HTML
   */
  generateHTML(html, css, js) {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lynx Web Preview</title>
    <style>
        /* 基础重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            padding: 16px;
        }
        
        /* Lynx默认布局样式 */
        div {
            display: flex;
            flex-direction: column;
        }
        
        span {
            display: inline-block;
        }
        
        /* 数据占位符样式 */
        .data-placeholder {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            font-family: monospace;
        }
        
        /* 用户自定义样式 */
        ${css}
    </style>
</head>
<body>
    <!-- 转换后的HTML内容 -->
    ${html}
    
    <script>
        // 基础功能实现
        const component = {
            data: {},
            setData: function(newData, callback) {
                Object.assign(this.data, newData);
                console.log('setData called:', newData);
                if (callback) callback();
            }
        };
        
        // 事件处理函数
        function handleTap(handler) {
            console.log('Tap event:', handler);
            if (typeof window[handler] === 'function') {
                window[handler]();
            }
        }
        
        function handleLongPress(handler) {
            console.log('Long press event:', handler);
            if (typeof window[handler] === 'function') {
                window[handler]();
            }
        }
        
        // 用户脚本
        try {
            ${js}
        } catch (error) {
            console.warn('User script error:', error);
        }
        
        // 全局错误处理
        window.onerror = function(msg, url, line, col, error) {
            console.error('Preview runtime error:', { msg, line, col });
        };
        
        // 页面加载完成提示
        console.log('Lynx Web Preview loaded successfully');
    </script>
</body>
</html>`;
  }

  /**
   * 内容类型检测方法
   */
  looksLikeTTML(content) {
    const tttmlIndicators = [
      '<view',
      '<text',
      '<image',
      '<scroll-view',
      'tt:',
      'bind',
    ];
    return tttmlIndicators.some(indicator => content.includes(indicator));
  }

  looksLikeTTSS(content) {
    const ttssIndicators = ['rpx', 'flex-direction', 'enable-scroll', '{', '}'];
    return ttssIndicators.some(indicator => content.includes(indicator));
  }

  looksLikeJS(content) {
    const jsIndicators = ['Card(', 'setData', 'function', 'onLoad', 'onShow'];
    return jsIndicators.some(indicator => content.includes(indicator));
  }

  /**
   * 默认样式
   */
  getDefaultCSS() {
    return `
      .container {
        padding: 20px;
        max-width: 375px;
        margin: 0 auto;
      }
      
      .text {
        color: #333;
        font-size: 14px;
        margin: 4px 0;
      }
      
      .view {
        margin: 8px 0;
      }
    `;
  }

  /**
   * 默认脚本
   */
  getDefaultJS() {
    return `
      console.log('Lynx preview initialized');
      
      // 模拟常用生命周期
      if (typeof onLoad === 'function') onLoad();
      if (typeof onShow === 'function') onShow();
    `;
  }
}

// Worker消息处理
const converter = new LynxConverter();

self.onmessage = function (event) {
  const { type, content, resultId, options } = event.data;

  switch (type) {
    case 'CONVERT':
      const result = converter.convert(content, options);
      self.postMessage({
        ...result,
        resultId,
      });
      break;

    case 'HEALTH_CHECK':
      self.postMessage({
        success: true,
        message: 'Worker is healthy',
        timestamp: Date.now(),
      });
      break;

    default:
      self.postMessage({
        success: false,
        error: 'UNKNOWN_MESSAGE_TYPE',
        message: `Unknown message type: ${type}`,
      });
  }
};

// 错误处理
self.onerror = function (error) {
  self.postMessage({
    success: false,
    error: 'WORKER_ERROR',
    message: error.message,
  });
};

console.log('Lynx Converter Worker initialized');
