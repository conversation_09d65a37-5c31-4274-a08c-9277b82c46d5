#!/usr/bin/env node
/**
 * 🔍 Prompt Syntax Checker - 检查prompt文件语法状态
 * 
 * 简化版检查脚本，用于快速验证prompt文件的语法状态
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class PromptSyntaxChecker {
  constructor() {
    this.issues = [];
    this.checkedFiles = [];
  }

  /**
   * 检查单个文件的语法问题
   */
  checkFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const issues = [];
      
      // 检查未转义的代码块
      const unescapedCodeBlocks = content.match(/(?<!\\\\)```/g);
      if (unescapedCodeBlocks) {
        issues.push(`未转义的代码块: ${unescapedCodeBlocks.length}个`);
      }
      
      // 检查未转义的内联代码
      const unescapedInlineCode = content.match(/(?<!\\\\)`[^`\n]+`(?!\\\\)/g);
      if (unescapedInlineCode) {
        issues.push(`未转义的内联代码: ${unescapedInlineCode.length}个`);
      }
      
      // 检查装饰性符号
      const decorativeSymbols = content.match(/^(-{3,}|={3,}|\*{3,}|~{3,})$/gm);
      if (decorativeSymbols) {
        issues.push(`装饰性符号: ${decorativeSymbols.length}个`);
      }
      
      // 检查多级标题
      const multiLevelHeaders = content.match(/^#{2,6}\\s+/gm);
      if (multiLevelHeaders) {
        issues.push(`多级标题: ${multiLevelHeaders.length}个`);
      }
      
      this.checkedFiles.push(filePath);
      
      if (issues.length > 0) {
        this.issues.push({ file: filePath, issues });
        return false;
      }
      
      return true;
    } catch (error) {
      this.issues.push({ file: filePath, issues: [`读取错误: ${error.message}`] });
      return false;
    }
  }

  /**
   * 检查所有prompt文件
   */
  checkAllFiles() {
    const promptsDir = path.join(process.cwd(), 'src/routes/batch_processor/prompts');
    
    if (!fs.existsSync(promptsDir)) {
      console.error(`❌ 目录不存在: ${promptsDir}`);
      return false;
    }
    
    this.scanDirectory(promptsDir);
    return true;
  }

  /**
   * 递归扫描目录
   */
  scanDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    items.forEach(item => {
      const fullPath = path.join(dirPath, item);
      const stats = fs.statSync(fullPath);
      
      if (stats.isDirectory()) {
        this.scanDirectory(fullPath);
      } else if (item.endsWith('.ts') && !item.includes('.d.ts')) {
        this.checkFile(fullPath);
      }
    });
  }

  /**
   * 验证TypeScript编译状态
   */
  checkCompilation() {
    try {
      execSync('pnpm type-check', { 
        stdio: 'pipe',
        cwd: process.cwd(),
        timeout: 30000
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 生成检查报告
   */
  generateReport() {
    console.log('🔍 Prompt语法检查报告');
    console.log('===================');
    
    console.log(`📁 检查的文件数: ${this.checkedFiles.length}`);
    console.log(`⚠️  发现问题的文件数: ${this.issues.length}`);
    
    if (this.issues.length > 0) {
      console.log('\\n🚨 发现的问题:');
      this.issues.forEach(({ file, issues }) => {
        console.log(`\\n📄 ${file}:`);
        issues.forEach(issue => {
          console.log(`  - ${issue}`);
        });
      });
    }
    
    // 检查编译状态
    console.log('\\n🔧 TypeScript编译检查:');
    const compilationOK = this.checkCompilation();
    if (compilationOK) {
      console.log('✅ 编译通过');
    } else {
      console.log('❌ 编译失败');
    }
    
    // 总结
    console.log('\\n📊 检查结果:');
    if (this.issues.length === 0 && compilationOK) {
      console.log('✅ 所有prompt文件语法正常');
      return true;
    } else {
      console.log('⚠️  发现语法问题，建议运行: pnpm fix-prompts');
      return false;
    }
  }

  /**
   * 执行检查
   */
  run() {
    if (!this.checkAllFiles()) {
      return false;
    }
    
    return this.generateReport();
  }
}

// 主执行逻辑
if (require.main === module) {
  const checker = new PromptSyntaxChecker();
  const success = checker.run();
  process.exit(success ? 0 : 1);
}

module.exports = PromptSyntaxChecker;