#!/usr/bin/env node
/**
 * 🚨 Prompt TypeScript Syntax Fixer - 自动修复提示词文件语法问题
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class PromptSyntaxFixer {
  constructor() {
    this.fixedFiles = [];
    this.errors = [];
    this.stats = {
      codeBlocksFixed: 0,
      inlineCodeFixed: 0,
      templateStringsFixes: 0,
      markdownSimplified: 0,
      decorativeSymbolsRemoved: 0
    };
  }

  /**
   * 修复单个文件的语法问题
   */
  fixFile(filePath) {
    try {
      console.log(`🔧 修复文件: ${filePath}`);
      
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;
      
      // 1. 修复代码块反引号
      content = this.fixCodeBlocks(content);
      
      // 2. 修复内联代码反引号  
      content = this.fixInlineCode(content);
      
      // 3. 修复模板字符串中的反引号
      content = this.fixTemplateStrings(content);
      
      // 4. 简化Markdown符号
      content = this.simplifyMarkdown(content);
      
      // 5. 移除装饰性符号
      content = this.removeDecorativeSymbols(content);
      
      // 6. 写入修复后的内容
      if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        this.fixedFiles.push(filePath);
        console.log(`✅ 已修复: ${filePath}`);
      } else {
        console.log(`⚪ 无需修复: ${filePath}`);
      }
      
    } catch (error) {
      this.errors.push({ file: filePath, error: error.message });
      console.error(`❌ 修复失败: ${filePath} - ${error.message}`);
    }
  }

  /**
   * 修复代码块反引号
   */
  fixCodeBlocks(content) {
    // 匹配三个反引号的代码块：```language ... ```
    const codeBlockRegex = /```([\\s\\S]*?)```/g;
    let fixCount = 0;
    
    content = content.replace(codeBlockRegex, (match, codeContent) => {
      fixCount++;
      const escapedBackticks = '\\\\\\`\\\\\\`\\\\\\`';
      return `${escapedBackticks}${codeContent}${escapedBackticks}`;
    });
    
    this.stats.codeBlocksFixed += fixCount;
    if (fixCount > 0) {
      console.log(`  📝 修复了 ${fixCount} 个代码块`);
    }
    
    return content;
  }

  /**
   * 修复内联代码反引号
   */
  fixInlineCode(content) {
    // 匹配单个反引号的内联代码：`text`
    // 但要避免已经转义的：\\`text\\`
    const inlineCodeRegex = /(?<!\\\\)`([^`\\n]+)`(?!\\\\)/g;
    let fixCount = 0;
    
    content = content.replace(inlineCodeRegex, (match, codeText) => {
      fixCount++;
      return '\\\\\\\\`' + codeText + '\\\\\\\\`';
    });
    
    this.stats.inlineCodeFixed += fixCount;
    if (fixCount > 0) {
      console.log(`  💡 修复了 ${fixCount} 个内联代码`);
    }
    
    return content;
  }

  /**
   * 修复模板字符串中的反引号和变量占位符
   */
  fixTemplateStrings(content) {
    // 修复未转义的模板字符串变量
    let fixCount = 0;
    
    content = content.replace(/(?<!\\\\)\\$\\{([^}]+)\\}/g, (match, variable) => {
      fixCount++;
      return `\\\\\\$\\\\\\{${variable}\\\\\\}`;
    });
    
    this.stats.templateStringsFixes += fixCount;
    if (fixCount > 0) {
      console.log(`  🔤 修复了 ${fixCount} 个模板字符串变量`);
    }
    
    return content;
  }

  /**
   * 简化Markdown符号
   */
  simplifyMarkdown(content) {
    let fixCount = 0;
    
    // 将多级标题简化为单级
    content = content.replace(/^#{2,6}\\s+(.+)$/gm, (match, title) => {
      fixCount++;
      return `# ${title}`;
    });
    
    // 简化粗体和斜体标记
    content = content.replace(/\\*\\*([^*]+)\\*\\*/g, (match, text) => {
      fixCount++;
      return text;
    });
    
    content = content.replace(/\\*([^*]+)\\*/g, (match, text) => {
      fixCount++;
      return text;
    });
    
    this.stats.markdownSimplified += fixCount;
    if (fixCount > 0) {
      console.log(`  📝 简化了 ${fixCount} 个Markdown标记`);
    }
    
    return content;
  }

  /**
   * 移除装饰性符号
   */
  removeDecorativeSymbols(content) {
    let fixCount = 0;
    
    // 移除水平分割线
    const horizontalRules = [
      /^-{3,}$/gm,
      /^={3,}$/gm,
      /^\*{3,}$/gm,
      /^~{3,}$/gm
    ];
    
    horizontalRules.forEach(regex => {
      content = content.replace(regex, () => {
        fixCount++;
        return '';
      });
    });
    
    // 移除多余的空行（保持最多2个连续空行）
    content = content.replace(/\\n{4,}/g, '\\n\\n\\n');
    
    this.stats.decorativeSymbolsRemoved += fixCount;
    if (fixCount > 0) {
      console.log(`  🧹 移除了 ${fixCount} 个装饰符号`);
    }
    
    return content;
  }

  /**
   * 扫描并修复所有prompt文件
   */
  fixAllPromptFiles() {
    console.log('🚀 开始扫描并修复prompt文件...');
    
    const promptsDir = path.join(process.cwd(), 'src/routes/batch_processor/prompts');
    
    if (!fs.existsSync(promptsDir)) {
      console.error(`❌ 目录不存在: ${promptsDir}`);
      return false;
    }
    
    this.scanDirectory(promptsDir);
    return true;
  }

  /**
   * 递归扫描目录
   */
  scanDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    items.forEach(item => {
      const fullPath = path.join(dirPath, item);
      const stats = fs.statSync(fullPath);
      
      if (stats.isDirectory()) {
        this.scanDirectory(fullPath);
      } else if (item.endsWith('.ts') && !item.includes('.d.ts')) {
        this.fixFile(fullPath);
      }
    });
  }

  /**
   * 验证修复后的TypeScript编译状态
   */
  validateCompilation() {
    console.log('\\n🔍 验证TypeScript编译状态...');
    
    try {
      execSync('pnpm type-check', { 
        stdio: 'pipe',
        cwd: process.cwd(),
        timeout: 60000
      });
      console.log('✅ TypeScript编译检查通过');
      return true;
    } catch (error) {
      console.log('⚠️ TypeScript编译仍有错误:');
      
      // 只显示prompt相关的错误
      const output = error.stdout ? error.stdout.toString() : error.stderr.toString();
      const promptErrors = output.split('\\n')
        .filter(line => line.includes('prompts'))
        .slice(0, 10); // 只显示前10个错误
      
      if (promptErrors.length > 0) {
        promptErrors.forEach(error => console.log(`  ${error}`));
      } else {
        console.log('  没有发现prompt相关的编译错误');
      }
      
      return false;
    }
  }

  /**
   * 生成修复报告
   */
  generateReport() {
    console.log('\\n📊 修复报告:');
    console.log('================');
    console.log(`✅ 修复的文件数: ${this.fixedFiles.length}`);
    console.log(`❌ 失败的文件数: ${this.errors.length}`);
    console.log('\\n📈 修复统计:');
    console.log(`  代码块修复: ${this.stats.codeBlocksFixed}`);
    console.log(`  内联代码修复: ${this.stats.inlineCodeFixed}`);
    console.log(`  模板字符串修复: ${this.stats.templateStringsFixes}`);
    console.log(`  Markdown简化: ${this.stats.markdownSimplified}`);
    console.log(`  装饰符号移除: ${this.stats.decorativeSymbolsRemoved}`);
    
    if (this.fixedFiles.length > 0) {
      console.log('\\n🔧 修复的文件:');
      this.fixedFiles.forEach(file => {
        console.log(`  ${file}`);
      });
    }
    
    if (this.errors.length > 0) {
      console.log('\\n❌ 修复失败的文件:');
      this.errors.forEach(({ file, error }) => {
        console.log(`  ${file}: ${error}`);
      });
    }
  }

  /**
   * 执行完整的修复流程
   */
  run() {
    console.log('🚨 Prompt TypeScript Syntax Fixer');
    console.log('==================================');
    
    // 1. 修复所有文件
    if (!this.fixAllPromptFiles()) {
      return false;
    }
    
    // 2. 验证编译状态
    const compilationPassed = this.validateCompilation();
    
    // 3. 生成报告
    this.generateReport();
    
    // 4. 总结
    console.log('\\n🎯 修复完成!');
    if (compilationPassed) {
      console.log('✅ 所有prompt文件已通过TypeScript编译检查');
    } else {
      console.log('⚠️ 部分编译错误可能需要手动处理');
    }
    
    return true;
  }
}

// 主执行逻辑
if (require.main === module) {
  const fixer = new PromptSyntaxFixer();
  const success = fixer.run();
  process.exit(success ? 0 : 1);
}

module.exports = PromptSyntaxFixer;