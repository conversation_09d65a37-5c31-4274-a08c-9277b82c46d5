# 批量处理器页面 (`page.tsx`) - 功能与设计文档

## 1. 概述

`page.tsx` 是批量处理工具的核心前端页面。它为用户提供了一个完整的、自包含的工作区，用于输入批量查询、配置AI处理任务、执行任务、监控进度、查看结果以及管理历史记录。

该页面的核心设计思想是**效率**、**可观测性**和**灵活性**。它旨在将一个原本需要手动、重复执行的任务（例如，为100个主题生成摘要）转变为一个自动化的、一键式的流程，同时为用户提供对整个过程的清晰洞察和控制。

## 2. 核心功能

- **批量查询输入**: 用户可以在一个文本区域内输入多个查询，每个查询占一行。系统会自动解析、去重（可配置）并将其作为独立的任务项。
- **AI 驱动的处理**: 页面集成了一个强大的批处理服务 (`useBatchProcessor`)，该服务调用后端AI工作流（通过 `workflowId` 指定）来处理每一个查询。
- **可定制的系统提示词 (System Prompt)**: 用户可以通过一个专用的抽屉面板 (`PromptDrawer`) 来编辑和保存指导AI行为的系统级提示词，实现了对AI输出风格和内容的宏观控制。
- **实时进度与结果展示**:
    - **总体进度**: 一个动态的进度条 (`ProgressDisplay`) 显示了整个批处理任务的完成百分比、已完成数量和总数。
    - **独立任务状态**: 结果面板 (`ResultsPanel`) 实时更新每个查询的状态（排队中、处理中、成功、失败），并即时展示成功的结果。
    - **状态日志**: 一个实时滚动的日志窗口 (`StatusLogger`) 提供了详细的操作反馈，包括任务开始、停止、成功、失败、重试和配置变更等。
- **历史记录管理**:
    - **自动保存**: 每个完成的批处理任务（包括所有查询和结果）都会被自动保存到浏览器的 Local Storage 中。
    - **历史追溯**: 用户可以通过历史抽屉 (`HistoryDrawer`) 查看过去所有的批处理记录。
    - **查询重用**: 用户可以方便地从历史记录中选择一个或多个查询，并将其加载到当前输入框中，以便重新运行或修改。
- **高级配置与控制**:
    - **并发控制**: 用户可以配置并发请求数 (`concurrent`)，以在速度和服务器负载之间找到平衡。
    - **错误处理**: 支持配置自动重试次数 (`maxRetries`)，并提供手动重试失败任务的按钮。
    - **任务控制**: 用户可以随时**启动**或**停止**整个批处理任务。
- **关键设置保护**: 系统内置了保护机制，确保用户通过UI开关（如“底稿数据模式”）所做的关键设置不会在页面重新加载或配置更新时被意外覆盖。

## 3. 设计思想与架构

页面的架构遵循了现代React的最佳实践，强调模块化、可维护性和清晰的数据流。

### 3.1. 基于组件的UI (Component-Driven UI)

UI被拆分为多个高内聚、低耦合的React组件，每个组件负责一块独立的功能。这种方式使得代码更易于理解、测试和复用。

- **`BatchProcessorPage`**: 顶层容器组件，负责整体布局和状态的“编排”。
- **`QueryInputPanel`**: 左侧的输入面板，管理用户输入和查询解析。
- **`ResultsPanel`**: 中间的核心区域，负责展示所有任务的详细结果列表。
- **`ProgressDisplay`**: 顶部的进度展示组件。
- **`*Drawer` 系列组件**:
    - `HistoryDrawer`, `PromptDrawer`, `SettingsDrawer`：将次级功能（历史、提示词、设置）收纳在抽屉中，保持主界面的整洁，用户需要时可随时滑出。
- **`StatusLogger`**: 右下角的状态日志组件。

### 3.2. 基于Hooks的逻辑封装

应用的核心业务逻辑被封装在自定义Hooks中，实现了UI与逻辑的彻底分离。

- **`useBatchProcessor` (核心引擎)**: 这是整个应用的心脏。它封装了所有与批处理相关的复杂逻辑，包括：
    - 状态管理（`progress`, `results`, `isRunning`）。
    - 与后端API的交互（发送请求、处理响应）。
    - 并发队列和任务调度。
    - 重试逻辑。
    - 状态更新和回调。
    `page.tsx` 只需调用此Hook暴露出的简单接口（`start`, `stop`, `retryFailed`），而无需关心内部的实现细节。

- **`useStatusLogger`**: 一个独立的Hook，用于管理和展示日志消息，使日志功能可以轻松地被集成到任何组件中。

### 3.3. 清晰的单向数据流

数据流遵循React的单向原则，使得状态变化可预测且易于调试。

1.  **输入**: 用户在 `QueryInputPanel` 中输入文本。
2.  **状态提升**: 文本内容 (`inputText`) 和解析后的查询列表 (`queries`) 被保存在 `BatchProcessorPage` 的 state 中。
3.  **触发**: 用户点击“开始”按钮，调用 `handleStartBatch` 函数。
4.  **执行**: `handleStartBatch` 调用 `useBatchProcessor` Hook 返回的 `start` 方法，并将 `queries` 和 `systemPrompt` 作为参数传入。
5.  **后台处理**: `useBatchProcessor` 开始在后台处理任务，并持续更新其内部的 `results` 和 `progress` 状态。
6.  **响应**: `page.tsx` 组件由于 `useBatchProcessor` 的状态变化而重新渲染。
7.  **下传**: 最新的 `results` 和 `progress` 作为 props 传递给 `ResultsPanel` 和 `ProgressDisplay`。
8.  **渲染**: 子组件根据新的 props 更新UI，向用户展示最新的进度和结果。
9.  **持久化**: 任务完成后，`useEffect` Hook 会触发 `LocalStorageService` 将结果保存。

### 3.4. 服务层抽象

通过 `LocalStorageService` 将与浏览器 Local Storage 的直接交互进行抽象。这使得：
-   代码更整洁，业务逻辑中不会散落 `localStorage.getItem/setItem` 的调用。
-   未来如果需要更换存储机制（例如，换成 IndexedDB 或服务器存储），只需修改 `LocalStorageService` 内部实现，而无需改动上层业务代码。

### 3.5. 现代化的CSS架构

代码注释中明确提到，项目采用了一套重构后的模块化CSS架构 (`index.css`)，并用CSS类替代了行内样式。这带来了更好的性能、可维护性和主题化能力。响应式布局（如 `processing-mode` 切换）也通过CSS类来控制，而非JS逻辑。

## 4. 总结

`batch_processor/page.tsx` 是一个功能强大且设计精良的前端应用。它通过清晰的组件划分、逻辑与视图分离的Hooks架构以及可预测的单向数据流，成功地将一个复杂的业务场景实现为一个健壮、易用且可扩展的工具。该页面不仅是功能的集合，更是优秀前端工程实践的体现。