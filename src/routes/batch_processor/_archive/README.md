# Batch Processor 归档目录

## 概述
此目录包含batch_processor模块中归档的过时文件和冗余文件。这些文件已被移动到此处以保持主要代码库的整洁，但仍保留以供参考。

## 归档日期
2025-07-04

## 目录结构

### `/md-reports/` - 临时修复报告文档
以下是临时的修复和分析报告，大部分已完成其使命：

**根目录报告：**
- `CLOUD_ANIMATIONS_REMOVED.md` - 云动画移除报告
- `COMPLETE_FALLBACK_REMOVAL.md` - 完整fallback移除报告  
- `COMPREHENSIVE_FALLBACK_AUDIT.md` - 综合fallback审计报告
- `FINAL_UI_FIXES_COMPLETE.md` - 最终UI修复完成报告
- `GOLDEN_BUTTON_ICON_FIXED.md` - 金色按钮图标修复报告
- `LYNX_MVP_IMPLEMENTATION.md` - Lynx MVP实现报告
- `PARSE5_ARCHITECTURE_ANALYSIS_AND_P0_FIXES.md` - Parse5架构分析和P0修复
- `PARSE5_SPECIFIC_ERRORS_AND_FIXES.md` - Parse5特定错误和修复
- `PRODUCTION_FIXES_IMPLEMENTED.md` - 生产环境修复实施报告
- `RUNTIME_ERROR_FIXES.md` - 运行时错误修复报告
- `TTSS_EXTRACTION_FIX_SUMMARY.md` - TTSS提取修复总结
- `UI_FIXES_COMPLETED.md` - UI修复完成报告

**分析文档：**
- `runtime_vs_speedy_analysis.md` - Runtime vs Speedy分析
- `batch_processor_architecture.md` - 批处理器架构文档

**样式报告：**
- `CSS_DEDUPLICATION_REPORT.md` - CSS去重报告
- `CSS_PRIORITY_MANAGEMENT.md` - CSS优先级管理报告
- `SCROLLBAR_FIX_REPORT.md` - 滚动条修复报告
- `SPARKLE_GLOW_RECOVERY_REPORT.md` - 火花发光恢复报告
- `TITLE_COLOR_OPTIMIZATION.md` - 标题颜色优化报告

### `/css-deprecated/` - 已弃用CSS文件
*此目录预留给需要归档的CSS文件*

### `/test-files/` - 测试文件
已移动的临时测试HTML文件：
- `test-lynx-preview-debug.html` - Lynx预览调试测试
- `test-lynx-simple.html` - Lynx简单测试
- `test-query-preview-scroll.html` - 查询预览滚动测试
- `test-three-column-layout.html` - 三栏布局测试
- `test-ttss-extraction-debug.html` - TTSS提取调试测试
- `test-ttss-fix-validation.html` - TTSS修复验证测试

### `/utils-deprecated/` - 已弃用工具文件
冗余的prompt loader文件：
- `BalancedPEPromptLoader.ts` - 平衡PE提示加载器
- `OptimizedPEPromptLoader.ts` - 优化PE提示加载器
- `UpdatedPEPromptLoader.ts` - 更新PE提示加载器
- `pePromptLoader.ts.bak2` - PE提示加载器备份文件

**注意**: `LightChartPromptLoader.ts` 和 `MasterLevelUIPromptLoader.ts` 已恢复到主utils目录，因为它们仍被 `promptTemplateManager.ts` 引用。

### `/temp-fixes/` - 临时修复
*此目录预留给临时修复文件*

## 注意事项

1. **不要删除这些文件** - 它们包含重要的历史信息和调试数据
2. **CSS文件处理** - 部分CSS文件仍在被引用，需要逐步重构合并
3. **测试文件** - 测试文件已移动但测试逻辑可能仍然有用
4. **Prompt Loader** - 保留了主要的prompt loader，移除了冗余变体

## 当前状态的CSS文件

以下CSS文件仍在主代码中使用，需要谨慎处理：
- `layout-fix.css` - 全局布局修复 (32号引用)
- `layout-fixes.css` - 侧边栏布局修复 (129号引用)  
- `unified-button-patch.css` - 统一按钮补丁 (97号引用)

这些文件需要在后续重构中逐步合并到设计系统中。

## 效果

经过整理后，batch_processor目录更加整洁，主要文件结构更清晰：
- 移除了17个临时MD报告文档
- 移除了6个测试HTML文件  
- 移除了6个冗余的prompt loader文件
- 保持了核心功能文件的完整性