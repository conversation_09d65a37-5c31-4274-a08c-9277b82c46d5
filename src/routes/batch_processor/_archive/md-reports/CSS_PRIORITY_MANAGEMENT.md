# CSS 优先级管理指南

## 概述
本文档规范了 batch_processor 样式系统中的 CSS 优先级管理，避免滥用 `!important`，建立可维护的样式层次结构。

## 🚫 禁止滥用 `!important`

### 问题分析
滥用 `!important` 会导致：
- 样式优先级混乱，难以调试
- 后续修改困难，需要更多 `!important` 覆盖
- 代码可维护性差
- 团队协作困难

### 解决方案
使用合理的选择器优先级替代 `!important`：

```css
/* ❌ 错误：滥用 !important */
.glass-card {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(148, 163, 184, 0.2) !important;
  border-radius: 12px !important;
}

/* ✅ 正确：使用合理的选择器优先级 */
.batch-processor-layout .glass-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
}
```

## 📊 CSS 优先级层次

### 1. 基础层 (Specificity: 0,0,1,0)
```css
/* 元素选择器 */
.glass-card { }
.btn-authority { }
.icon { }
```

### 2. 布局层 (Specificity: 0,0,2,0)
```css
/* 布局容器 + 组件 */
.batch-processor-layout .glass-card { }
.batch-processor-layout .btn-authority { }
.batch-processor-layout .icon { }
```

### 3. 状态层 (Specificity: 0,0,3,0)
```css
/* 布局容器 + 组件 + 状态 */
.batch-processor-layout .glass-card:hover { }
.batch-processor-layout .btn-authority:disabled { }
.batch-processor-layout .icon.processing { }
```

### 4. 变体层 (Specificity: 0,0,3,0)
```css
/* 布局容器 + 组件 + 变体 */
.batch-processor-layout .glass-card.glass-card-blue { }
.batch-processor-layout .btn-authority.btn-sm { }
.batch-processor-layout .icon.size-lg { }
```

### 5. 覆盖层 (Specificity: 0,0,4,0)
```css
/* 特殊情况下的覆盖 */
.batch-processor-layout .glass-card.override-styles { }
.batch-processor-layout .btn-authority.force-disabled { }
```

### 6. 紧急层 (使用 !important)
```css
/* 仅在绝对必要时使用 */
.glass-card.critical-override {
  display: none !important; /* 紧急隐藏 */
}
```

## 🎯 选择器命名策略

### 布局容器
所有样式都应该在 `.batch-processor-layout` 容器内：
```css
.batch-processor-layout .component-name { }
```

### 组件变体
使用 BEM 命名约定：
```css
.batch-processor-layout .component__element { }
.batch-processor-layout .component--modifier { }
.batch-processor-layout .component__element--modifier { }
```

### 状态类
使用伪类和状态类：
```css
.batch-processor-layout .component:hover { }
.batch-processor-layout .component:focus { }
.batch-processor-layout .component.is-active { }
.batch-processor-layout .component.is-disabled { }
```

## 📋 重构清单

### 已完成的重构
- [x] `.glass-card` - 移除所有 `!important`，使用 `.batch-processor-layout .glass-card`
- [x] `.query-input-textarea-container` - 移除 `!important`，使用合理选择器
- [x] `.btn-authority` - 移除大部分 `!important`，保留必要的布局属性
- [x] `.icon` - 移除所有 `!important`，使用选择器优先级

### 待重构的组件
- [ ] `.status-indicator` - 清理 `unified-button-patch.css` 中的重复定义
- [ ] 输入框和表单元素 - 统一样式，移除 `!important`
- [ ] 响应式样式 - 清理 `responsive-14inch.css` 中的 `!important`
- [ ] 动画相关样式 - 检查动画工具类的优先级

## 🔧 实施规则

### 1. 新增样式规则
- 所有新样式必须在 `.batch-processor-layout` 容器内
- 禁止使用 `!important`，除非有明确的技术原因
- 使用合理的选择器优先级

### 2. 修改现有样式
- 逐步移除现有的 `!important`
- 使用更具体的选择器替代
- 保持向后兼容性

### 3. 代码审查要点
- 检查是否有不必要的 `!important`
- 验证选择器优先级是否合理
- 确保样式不会意外覆盖其他组件

## 🎨 最佳实践

### 1. 选择器优先级管理
```css
/* 基础样式 */
.batch-processor-layout .component {
  /* 基础属性 */
}

/* 状态样式 */
.batch-processor-layout .component:hover,
.batch-processor-layout .component.is-active {
  /* 状态变化 */
}

/* 变体样式 */
.batch-processor-layout .component.component--variant {
  /* 变体特有属性 */
}
```

### 2. 避免过度嵌套
```css
/* ❌ 避免：过度嵌套 */
.batch-processor-layout .sidebar .card .header .title .text { }

/* ✅ 推荐：合理的嵌套深度 */
.batch-processor-layout .card-title { }
```

### 3. 使用 CSS 自定义属性
```css
/* 定义变量 */
:root {
  --card-bg: rgba(255, 255, 255, 0.95);
  --card-border: rgba(148, 163, 184, 0.2);
}

/* 使用变量 */
.batch-processor-layout .glass-card {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
}
```

## 🚀 迁移计划

### 阶段 1: 核心组件 (已完成)
- 玻璃卡片系统
- 按钮系统
- 图标系统
- 输入框系统

### 阶段 2: 状态和交互 (进行中)
- 状态指示器
- 悬停效果
- 动画系统

### 阶段 3: 响应式和特殊情况 (计划中)
- 响应式样式优化
- 特殊覆盖情况处理
- 遗留代码清理

## 📝 注意事项

### 何时可以使用 `!important`
1. **第三方库覆盖**: 需要覆盖第三方CSS库的样式
2. **紧急修复**: 生产环境的紧急样式修复
3. **工具类**: 原子化的工具类（如 `.hidden { display: none !important; }`）
4. **可访问性**: 确保可访问性样式不被覆盖

### 代码示例
```css
/* ✅ 合理使用 !important 的情况 */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* 第三方库覆盖 */
.arco-button.custom-override {
  background: var(--custom-bg) !important;
}
```

---

**文档版本**: 1.0  
**最后更新**: 2025-06-30  
**维护者**: Augment Agent  
**状态**: ✅ 活跃维护
