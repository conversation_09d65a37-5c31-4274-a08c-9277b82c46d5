/**
 * 数据源标签组件
 * 
 * 🎯 核心功能：
 * - 显示结果的数据源类型（底稿数据/AI生成/回退模式）
 * - 提供清晰的视觉标识和颜色区分
 * - 支持悬停显示详细信息
 * - 响应式设计，适配不同屏幕尺寸
 * 
 * 📊 数据源类型：
 * - internal: 使用抖音内部底稿数据生成
 * - ai: AI直接生成
 * - fallback: 底稿数据获取失败后回退到AI生成
 * 
 * 🎨 设计原则：
 * - 颜色编码：绿色(底稿数据)、蓝色(AI生成)、橙色(回退模式)
 * - 图标标识：不同数据源使用不同的图标
 * - 信息透明：用户可以清楚知道内容的来源
 * 
 * <AUTHOR>
 * @since 2025-07-28
 * @version 1.0.0
 */

import React from 'react';
import { Tooltip } from '@douyinfe/semi-ui';
import Icon from './Icon';
import '../styles/components/data-source-label.css';

/**
 * 数据源标签属性接口
 */
export interface DataSourceLabelProps {
  /** 数据源类型 */
  dataSource: 'internal' | 'ai' | 'fallback';
  /** 底稿数据摘要（仅当dataSource为internal时显示） */
  internalDataSummary?: string;
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 紧凑模式（更小的尺寸） */
  compact?: boolean;
}

/**
 * 数据源配置映射
 */
const DATA_SOURCE_CONFIG = {
  internal: {
    label: '底稿数据',
    icon: 'database',
    color: 'green',
    bgClass: 'bg-green-100',
    textClass: 'text-green-800',
    borderClass: 'border-green-200',
    description: '使用抖音内部底稿数据生成，内容更准确可靠'
  },
  ai: {
    label: 'AI生成',
    icon: 'brain',
    color: 'blue',
    bgClass: 'bg-blue-100',
    textClass: 'text-blue-800',
    borderClass: 'border-blue-200',
    description: 'AI直接生成内容，创意性更强'
  },
  fallback: {
    label: '回退模式',
    icon: 'refresh',
    color: 'orange',
    bgClass: 'bg-orange-100',
    textClass: 'text-orange-800',
    borderClass: 'border-orange-200',
    description: '底稿数据获取失败，回退到AI生成'
  }
} as const;

/**
 * 数据源标签组件
 */
export const DataSourceLabel: React.FC<DataSourceLabelProps> = ({
  dataSource,
  internalDataSummary,
  showDetails = true,
  className = '',
  compact = false
}) => {
  const config = DATA_SOURCE_CONFIG[dataSource];
  
  if (!config) {
    console.warn(`Unknown data source: ${dataSource}`);
    return null;
  }

  const labelContent = (
    <div 
      className={`
        inline-flex items-center gap-1.5 px-2 py-1 rounded-md border
        ${config.bgClass} ${config.textClass} ${config.borderClass}
        ${compact ? 'text-xs' : 'text-sm'}
        ${className}
        transition-all duration-200 hover:shadow-sm
      `}
    >
      <Icon 
        type={config.icon as any}
        color={config.color as any}
        size={compact ? 'xs' : 'sm'}
      />
      <span className="font-medium">
        {config.label}
      </span>
      {dataSource === 'internal' && internalDataSummary && showDetails && (
        <span className="text-xs opacity-75 ml-1">
          ({internalDataSummary})
        </span>
      )}
    </div>
  );

  if (!showDetails) {
    return labelContent;
  }

  return (
    <Tooltip
      content={
        <div className="max-w-xs">
          <div className="font-medium mb-1">{config.label}</div>
          <div className="text-sm opacity-90">{config.description}</div>
          {dataSource === 'internal' && internalDataSummary && (
            <div className="text-xs mt-2 p-2 bg-gray-50 rounded">
              <strong>底稿摘要：</strong>{internalDataSummary}
            </div>
          )}
        </div>
      }
      position="top"
    >
      {labelContent}
    </Tooltip>
  );
};

/**
 * 数据源标签列表组件 - 用于显示多个数据源统计
 */
export interface DataSourceStatsProps {
  /** 数据源统计 */
  stats: {
    internal: number;
    ai: number;
    fallback: number;
  };
  /** 总数 */
  total: number;
  /** 紧凑模式 */
  compact?: boolean;
}

export const DataSourceStats: React.FC<DataSourceStatsProps> = ({
  stats,
  total,
  compact = false,
}) => {
  if (total === 0) return null;

  const hasInternal = stats.internal > 0;
  const hasAi = stats.ai > 0;
  const hasFallback = stats.fallback > 0;

  return (
    <div className={`flex items-center gap-2 ${compact ? 'text-xs' : 'text-sm'}`}>
      <span className="text-gray-600 font-medium">数据源：</span>
      
      {hasInternal && (
        <div className="flex items-center gap-1">
          <DataSourceLabel 
            dataSource="internal" 
            compact={compact}
            showDetails={false}
          />
          <span className="text-gray-500">({stats.internal})</span>
        </div>
      )}
      
      {hasAi && (
        <div className="flex items-center gap-1">
          <DataSourceLabel 
            dataSource="ai" 
            compact={compact}
            showDetails={false}
          />
          <span className="text-gray-500">({stats.ai})</span>
        </div>
      )}
      
      {hasFallback && (
        <div className="flex items-center gap-1">
          <DataSourceLabel 
            dataSource="fallback" 
            compact={compact}
            showDetails={false}
          />
          <span className="text-gray-500">({stats.fallback})</span>
        </div>
      )}
    </div>
  );
};

export default DataSourceLabel;
