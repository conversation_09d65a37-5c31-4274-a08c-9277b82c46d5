/**
 * 模式切换组件 - 底稿数据模式的用户界面控制器
 *
 * 🎯 核心功能：
 * 1. 提供"使用抖音内部的底稿数据"开关界面
 * 2. 实时显示当前数据源模式状态
 * 3. 提供用户友好的交互体验和视觉反馈
 * 4. 支持禁用状态、加载状态和错误处理
 * 5. 显示数据源指示器和功能说明
 *
 * 🎨 界面设计：
 * - 清晰的开关标识和状态指示
 * - 实时的加载进度和状态反馈
 * - 响应式设计，适配移动端和桌面端
 * - 无障碍访问支持，包含aria-label
 *
 * 📍 使用位置：
 * - 位于QueryInputPanel的输入区域上方
 * - 作为用户选择数据源模式的主要入口
 * - 与其他输入组件形成统一的用户界面
 *
 * 🔄 交互逻辑：
 * - 开启：使用抖音内部底稿数据，确保信息权威性
 * - 关闭：使用直接AI生成，响应更快但可能缺乏特定数据
 * - 加载中：显示底稿数据获取进度，提供用户反馈
 * - 错误状态：显示错误信息，提供重试或回退选项
 *
 * 💡 设计原则：
 * - 用户控制：让用户明确知道当前使用的数据源
 * - 状态透明：清晰显示系统当前的工作状态
 * - 体验优先：确保切换过程流畅，错误处理友好
 * - 信息完整：提供足够的上下文帮助用户理解功能
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @version 1.0.0
 */

import React, { useCallback } from 'react';
import { Switch } from '@douyinfe/semi-ui';
import Icon from './Icon';

/**
 * 模式切换组件属性接口
 *
 * 📋 属性说明：
 * - useInternalData: 当前是否启用底稿数据模式
 * - onToggle: 模式切换时的回调函数，参数为新的模式状态
 * - disabled: 是否禁用切换功能，通常在处理中时禁用
 * - loading: 是否显示加载状态，底稿数据获取时为true
 * - className: 自定义CSS类名，用于样式扩展
 * - showDescription: 是否显示详细的功能描述文本
 *
 * 💡 使用建议：
 * - disabled通常在查询处理中设为true，避免频繁切换
 * - loading在底稿数据获取过程中设为true，提供用户反馈
 * - showDescription在空间充足时设为true，帮助用户理解功能
 */
export interface ModeToggleProps {
  /** 当前是否使用抖音内部底稿数据模式 */
  useInternalData: boolean;
  /** 模式切换回调函数，参数为新的启用状态 */
  onToggle: (enabled: boolean) => void;
  /** 是否禁用切换功能，通常在处理中时禁用 */
  disabled?: boolean;
  /** 是否正在加载底稿数据，显示加载状态 */
  loading?: boolean;
  /** 自定义CSS类名，用于样式定制 */
  className?: string;
  /** 是否显示详细的功能描述文本 */
  showDescription?: boolean;
}

/**
 * 数据源指示器组件
 */
const DataSourceIndicator: React.FC<{
  useInternalData: boolean;
  loading: boolean;
}> = ({ useInternalData, loading }) => {
  if (loading) {
    return (
      <div className="flex items-center text-xs text-blue-600">
        <div className="mr-1">
          <Icon type="processing" size="xs" animate={true} />
        </div>
        <span>正在获取底稿数据...</span>
      </div>
    );
  }

  if (useInternalData) {
    return (
      <div className="flex items-center text-xs text-blue-600">
        <Icon type="database" size="xs" className="mr-1" />
        <span>底稿数据模式</span>
      </div>
    );
  }

  return (
    <div className="flex items-center text-xs text-gray-500">
      <Icon type="lightning" size="xs" className="mr-1" />
      <span>直接AI生成模式</span>
    </div>
  );
};

/**
 * 模式切换组件
 */
export const ModeToggle: React.FC<ModeToggleProps> = ({
  useInternalData,
  onToggle,
  disabled = false,
  loading = false,
  className = '',
  showDescription = true,
}) => {
  // 处理切换事件
  const handleToggle = useCallback((checked: boolean) => {
    if (disabled || loading) {
      return;
    }
    
    console.log(`[ModeToggle] 数据源模式切换: ${checked ? '内部数据' : '直接模式'}`);
    onToggle(checked);
  }, [onToggle, disabled, loading]);

  // 获取描述文本
  const getDescriptionText = () => {
    if (loading) {
      return "正在获取底稿数据，请稍候...";
    }
    
    if (useInternalData) {
      return "已启用：将使用抖音内部底稿数据作为数据源，确保信息准确性";
    }
    
    return "已关闭：直接使用AI生成内容，响应更快但可能缺乏特定数据";
  };

  return (
    <div className={`mode-toggle-container ${className}`}>
      {/* 主要切换区域 */}
      <div className="mode-toggle-content">
        <div className="mode-toggle-left">
          <div className="mode-toggle-label-wrapper">
            <span className="mode-toggle-label">
              使用抖音内部的底稿数据
            </span>
            {/* 数据源指示器 */}
            <DataSourceIndicator 
              useInternalData={useInternalData} 
              loading={loading} 
            />
          </div>
        </div>
        
        <div className="mode-toggle-right">
          <Switch
            checked={useInternalData}
            onChange={handleToggle}
            size="small"
            disabled={disabled || loading}
            className="mode-toggle-switch"
            aria-label="切换数据源模式"
          />
        </div>
      </div>

      {/* 描述文本 */}
      {showDescription && (
        <div className="mode-toggle-description">
          {getDescriptionText()}
        </div>
      )}

      {/* 功能说明 */}
      {useInternalData && !loading && (
        <div className="mode-toggle-features">
          <div className="mode-toggle-feature-item">
            <Icon type="check" size="xs" className="text-green-500" />
            <span>数据准确性更高</span>
          </div>
          <div className="mode-toggle-feature-item">
            <Icon type="check" size="xs" className="text-green-500" />
            <span>内容更加权威</span>
          </div>
          <div className="mode-toggle-feature-item">
            <Icon type="info" size="xs" className="text-blue-500" />
            <span>响应时间稍长</span>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * 简化版模式切换组件（用于空间受限的场景）
 */
export const CompactModeToggle: React.FC<Omit<ModeToggleProps, 'showDescription'>> = ({
  useInternalData,
  onToggle,
  disabled = false,
  loading = false,
  className = '',
}) => {
  const handleToggle = useCallback((checked: boolean) => {
    if (disabled || loading) {
      return;
    }
    onToggle(checked);
  }, [onToggle, disabled, loading]);

  return (
    <div className={`compact-mode-toggle ${className}`}>
      <div className="compact-mode-toggle-content">
        <span className="compact-mode-toggle-label">
          底稿数据
        </span>
        <Switch
          checked={useInternalData}
          onChange={handleToggle}
          size="small"
          disabled={disabled || loading}
          className="compact-mode-toggle-switch"
          aria-label="切换数据源模式"
        />
      </div>
      
      {loading && (
        <div className="compact-mode-toggle-loading">
          <Icon type="processing" size="xs" animate={true} />
        </div>
      )}
    </div>
  );
};

export default ModeToggle;
