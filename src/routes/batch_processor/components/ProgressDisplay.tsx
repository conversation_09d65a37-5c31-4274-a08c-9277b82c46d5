import React, { useState, useEffect, useMemo } from 'react';
import Tooltip from './Tooltip';

// -----------------------------------------------------------------------------
// ProgressDisplay
// -----------------------------------------------------------------------------
// 该组件展示批量处理的整体进度。它包含以下信息：
//   - 总体完成百分比
//   - 已处理/总数计数
//   - 成功/失败/处理中的详细计数
// 同时，该组件使用动画效果平滑过渡进度变化，增强用户体验
//
// 2025-06-05: 增加变体支持，将page.tsx中的内联进度条实现统一集中到该组件
// -----------------------------------------------------------------------------

interface ProgressDisplayProps {
  /** 进度状态 */
  progress: {
    total: number;
    completed: number; // 成功完成的任务数量
    failed: number; // 失败的任务数量
    percentage: number;
    successPercentage?: number; // 成功率百分比
  };
  onRetry?: () => Promise<void> | void;
  isRetrying?: boolean;
  variant?: 'full' | 'compact' | 'minimal'; // 显示变体：完整/精简/最小化
  showStats?: boolean; // 是否显示统计卡片
  showTitle?: boolean; // 是否显示标题
  className?: string; // 允许外部自定义样式
}

export const ProgressDisplay: React.FC<ProgressDisplayProps> = ({
  progress,
  onRetry,
  isRetrying = false,
  variant = 'full',
  showStats = true,
  showTitle = true,
  className = '',
}) => {
  // 使用动画进度值，平滑过渡
  const [animatedPercent, setAnimatedPercent] = useState(0);
  const [isFinishAnimating, setIsFinishAnimating] = useState(false);
  const [isHoveringStats, setIsHoveringStats] = useState<string | null>(null);

  const { total, completed, failed, percentage } = progress;
  const processed = completed + failed; // 已处理任务总数（成功+失败）
  const successPercentage =
    progress.successPercentage || (total > 0 ? (completed / total) * 100 : 0);

  // 当任务完成时的动画效果
  useEffect(() => {
    if (processed === total && total > 0) {
      // 设置弹跳动画
      const timer = setTimeout(() => {
        setIsFinishAnimating(true);
      }, 300);

      return () => clearTimeout(timer);
    } else {
      setIsFinishAnimating(false);
    }
  }, [processed, total]);

  // 当实际进度变化时，以动画方式更新显示的进度
  useEffect(() => {
    // 如果差距很小，直接更新到目标值
    if (Math.abs(percentage - animatedPercent) < 0.5) {
      setAnimatedPercent(percentage);
      return;
    }

    // 否则，使用动画过渡 - 采用更平滑的缓动效果
    const timeout = setTimeout(() => {
      // 动画更新逻辑：向目标值逐步靠近，使用非线性渐进
      const step = (percentage - animatedPercent) * 0.25;
      setAnimatedPercent(prev => Math.round((prev + step) * 10) / 10);
    }, 30);

    return () => clearTimeout(timeout);
  }, [percentage, animatedPercent]);

  // 处理状态变化
  const progressStatus = useMemo(() => {
    if (total === 0) {
      return 'idle';
    }
    if (processed === total) {
      return 'completed';
    }
    return 'processing';
  }, [total, processed]);

  // 根据状态确定进度条颜色
  const progressBarColor = useMemo(() => {
    if (failed > 0 && failed > completed) {
      return 'progress-error';
    } else if (failed > 0) {
      return 'progress-mixed';
    }
    return 'progress-default';
  }, [failed, completed]);

  // 进度条动画类
  const progressAnimClass = useMemo(() => {
    if (isFinishAnimating) {
      return 'animate-pulse';
    }
    if (animatedPercent > 0 && animatedPercent < 100) {
      return 'progress-active';
    }
    return '';
  }, [isFinishAnimating, animatedPercent]);

  // 统计卡片动画类
  const getStatCardClass = (statType: string) => {
    // 基础样式
    const baseClasses =
      'rounded-xl p-4 transition-all duration-300 text-center relative overflow-hidden elastic-click';

    // 悬停状态样式
    if (isHoveringStats === statType) {
      return `${baseClasses} transform -translate-y-2 scale-105 shadow-lg`;
    }

    // 根据统计类型确定样式
    if (statType === 'total') {
      return `${baseClasses} status-neutral-gradient shadow-md hover:shadow-lg liquid-ripple`;
    } else if (statType === 'succeeded') {
      return `${baseClasses} status-success-gradient shadow-md hover:shadow-lg light-scan`;
    } else if (statType === 'failed') {
      return `${baseClasses} status-error-gradient shadow-md hover:shadow-lg magnetic-hover`;
    }

    return `${baseClasses} hover:shadow-lg`;
  };

  // 处理重试失败任务
  const handleRetry = async () => {
    console.log('🔄 [ProgressDisplay] ===== RETRY BUTTON CLICKED =====');
    console.log('🔄 [ProgressDisplay] 当前状态:', {
      failed,
      onRetry: !!onRetry,
      isRetrying,
      total,
      completed,
    });

    if (!onRetry) {
      console.warn('❌ [ProgressDisplay] 无法重试：重试函数未提供');
      return;
    }

    if (failed === 0) {
      console.warn('❌ [ProgressDisplay] 无法重试：没有失败任务');
      return;
    }

    if (isRetrying) {
      console.warn('❌ [ProgressDisplay] 无法重试：重试操作正在进行中');
      return;
    }

    try {
      console.log(`🚀 [ProgressDisplay] 开始重试 ${failed} 个失败任务`);
      await onRetry();
      console.log('✅ [ProgressDisplay] 重试请求已发送成功');
    } catch (error) {
      console.error('💥 [ProgressDisplay] 重试失败:', error);
    }
  };

  // 最小化变体 - 仅显示进度条
  if (variant === 'minimal') {
    return (
      <div className={`relative ${className}`}>
        <div className="pastel-progress-container">
          <div
            className={`pastel-progress-bar ${progressBarColor} ${progressAnimClass}`}
            style={{ width: `${animatedPercent}%` }}
          />
        </div>
      </div>
    );
  }

  // 精简变体 - 显示两个进度条和百分比
  if (variant === 'compact') {
    return (
      <div className={`space-y-2 progress-display ${className}`}>
        {/* 成功率进度条 */}
        <div>
          <div className="flex justify-between text-xs mb-1 progress-label-success">
            <span>成功率</span>
            <span className="progress-percentage">{successPercentage.toFixed(0)}%</span>
          </div>
          <div className="pastel-progress-container">
            <div
              className="pastel-progress-bar progress-mixed"
              style={{
                width: `${successPercentage}%`,
              }}
            />
          </div>
        </div>

        {/* 总进度条 */}
        <div>
          <div className="flex justify-between text-xs mb-1 progress-label-primary">
            <span>总进度</span>
            <span className="progress-percentage">{animatedPercent.toFixed(0)}%</span>
          </div>
          <div className="pastel-progress-container">
            <div
              className={`pastel-progress-bar progress-default ${progressAnimClass}`}
              style={{
                width: `${animatedPercent}%`,
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  // 完整版 - 包含标题、进度条和统计卡片
  return (
    <div className={`relative overflow-hidden progress-display ${className}`}>
      {/* Debug Info - Remove in production */}
      {process.env.NODE_ENV === 'development' && (
        <div
          className="mb-2 p-2 rounded text-xs"
          style={{
            backgroundColor: 'var(--color-warning-light)',
            border: '1px solid var(--color-warning)',
          }}
        >
          <strong>Debug Info:</strong> Failed: {failed}, OnRetry:{' '}
          {onRetry ? 'Yes' : 'No'}, IsRetrying: {isRetrying ? 'Yes' : 'No'}
        </div>
      )}

      {/* 头部：标题和状态标签 */}
      {showTitle && (
        <div className="flex justify-between items-center mb-4">
          <Tooltip content="显示当前批处理任务的整体进度信息" position="right">
            <h3
              className="text-lg font-medium flex items-center cursor-help"
              style={{ color: 'var(--color-primary-700)' }}
            >
              <span
                className="w-10 h-10 text-white flex items-center justify-center mr-2 rounded-lg shadow-md transition-all duration-300 hover:scale-110 breathing-glow"
                style={{
                  background:
                    'linear-gradient(45deg, var(--color-primary-500), var(--color-blue-500), var(--color-primary-600))',
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                    clipRule="evenodd"
                  />
                </svg>
              </span>
              <span
                className="bg-clip-text text-transparent"
                style={{
                  background:
                    'linear-gradient(45deg, var(--color-primary-600), var(--color-blue-600))',
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                }}
              >
                处理进度
              </span>
            </h3>
          </Tooltip>

          <Tooltip
            content={
              progressStatus === 'idle'
                ? '尚未开始处理任务'
                : progressStatus === 'completed'
                  ? '所有查询处理已完成'
                  : '正在执行查询处理'
            }
            position="left"
          >
            <div
              className={`px-3 py-1.5 rounded-full text-sm font-medium flex items-center transition-all duration-500 shadow-lg relative overflow-hidden ${
                progressStatus === 'idle'
                  ? ''
                  : progressStatus === 'completed'
                    ? 'status-success-button breathing-glow'
                    : 'text-white shimmer-highlight'
              }`}
              style={{
                background:
                  progressStatus === 'processing'
                    ? 'var(--color-primary-300)'
                    : progressStatus === 'idle'
                      ? 'linear-gradient(45deg, var(--color-primary-100), var(--color-primary-200))'
                      : undefined,
                color:
                  progressStatus === 'idle'
                    ? 'var(--color-primary-600)'
                    : undefined,
              }}
            >
              {progressStatus === 'idle' ? (
                <span>未开始</span>
              ) : progressStatus === 'completed' ? (
                <span className="flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-1"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                  完成
                </span>
              ) : (
                <span className="flex items-center">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  处理中
                </span>
              )}
            </div>
          </Tooltip>
        </div>
      )}

      {/* 主进度条 */}
      <div
        className={`relative mb-6 rounded-full overflow-hidden transition-all duration-500 ${
          isFinishAnimating ? 'breathing-glow' : ''
        }`}
      >
        <div className="pastel-progress-container">
          <div
            className={`pastel-progress-bar ${progressBarColor} ${progressAnimClass} shimmer-highlight`}
            style={{ width: `${animatedPercent}%` }}
          />
        </div>

        {/* 进度百分比和数量指示器 */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div
            className="text-xs font-semibold px-3 py-1 rounded-full shadow-lg border backdrop-blur-sm"
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              borderColor: 'var(--color-primary-100)',
              color: 'var(--color-primary-700)',
            }}
          >
            {animatedPercent.toFixed(1)}% ({processed}/{total})
          </div>
        </div>
      </div>

      {/* 统计卡片组 */}
      {showStats && (
        <div className="grid grid-cols-3 gap-2">
          {/* 总任务数 */}
          <div
            className={getStatCardClass('total')}
            onMouseEnter={() => setIsHoveringStats('total')}
            onMouseLeave={() => setIsHoveringStats(null)}
          >
            <Tooltip content="批处理任务的总查询数量" position="top">
              <div
                className="text-xs mb-1"
                style={{ color: 'var(--color-gray-500)' }}
              >
                总任务
              </div>
            </Tooltip>
            <div
              className="text-xl font-bold"
              style={{ color: 'var(--color-gray-700)' }}
            >
              {total}
            </div>
          </div>

          {/* 成功数量 */}
          <div
            className={getStatCardClass('succeeded')}
            onMouseEnter={() => setIsHoveringStats('succeeded')}
            onMouseLeave={() => setIsHoveringStats(null)}
          >
            <Tooltip content="成功处理的查询数量" position="top">
              <div
                className="text-xs mb-1"
                style={{ color: 'var(--color-success)' }}
              >
                成功
              </div>
            </Tooltip>
            <div
              className="text-xl font-bold"
              style={{ color: 'var(--color-blue-700)' }}
            >
              {completed}
            </div>
          </div>

          {/* 失败数量 */}
          <div
            className={getStatCardClass('failed')}
            onMouseEnter={() => setIsHoveringStats('failed')}
            onMouseLeave={() => setIsHoveringStats(null)}
          >
            <Tooltip content="处理失败的查询数量" position="top">
              <div className="flex justify-between items-center">
                <span
                  className="text-xs"
                  style={{ color: 'var(--color-error)' }}
                >
                  失败
                </span>
                {failed > 0 && (
                  <button
                    className={`text-xs p-1 rounded transition-all duration-200 cursor-pointer ${
                      isRetrying ? 'cursor-not-allowed' : ''
                    }`}
                    style={{
                      color: isRetrying
                        ? 'var(--color-gray-400)'
                        : 'var(--color-success)',
                      backgroundColor: isRetrying
                        ? 'var(--color-gray-100)'
                        : 'transparent',
                    }}
                    onMouseEnter={e => {
                      if (!isRetrying) {
                        e.currentTarget.style.color = 'var(--color-blue-700)';
                        e.currentTarget.style.backgroundColor =
                          'var(--color-success-light)';
                      }
                    }}
                    onMouseLeave={e => {
                      if (!isRetrying) {
                        e.currentTarget.style.color = 'var(--color-success)';
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                    }}
                    onClick={e => {
                      console.log('🖱️ [ProgressDisplay] 重试按钮被点击', {
                        event: e,
                        failed,
                        isRetrying,
                        onRetry: !!onRetry,
                      });
                      handleRetry();
                    }}
                    disabled={isRetrying}
                    title={isRetrying ? '正在重试中...' : '重试失败的任务'}
                  >
                    {isRetrying ? (
                      <span className="flex items-center">
                        <svg
                          className="animate-spin h-3 w-3 mr-1"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          />
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          />
                        </svg>
                        重试中
                      </span>
                    ) : (
                      '重试'
                    )}
                  </button>
                )}
              </div>
            </Tooltip>
            <div
              className="text-xl font-bold"
              style={{ color: 'var(--color-error)' }}
            >
              {failed}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProgressDisplay;
