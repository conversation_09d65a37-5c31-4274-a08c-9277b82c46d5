import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
// import { parseQueryList } from '../utils/queryParser'; // Unused
import Tooltip from './Tooltip';
import Icon from './Icon';
import { ModeToggle } from './ModeToggle';
import { Switch } from '@douyinfe/semi-ui';
import { DataProcessingService, QueryProcessingResult } from '../services/DataProcessingService';
import { EnhancedBatchProcessorService } from '../services/EnhancedBatchProcessorService';

// -----------------------------------------------------------------------------
// QueryInputPanel
// -----------------------------------------------------------------------------
// 该组件提供批处理查询的输入界面，包括：
//   - 多行文本输入框，支持粘贴多行内容
//   - 输入计数器和视觉反馈
//   - 样本数据快速插入
//   - 查询列表分割与显示预览
// 组件支持通过多种分隔符（换行符、逗号、空格）拆分查询
// -----------------------------------------------------------------------------

interface QueryInputPanelProps {
  /** 输入文本 */
  inputText: string;
  /** 查询列表更改回调 */
  onInputChange: (text: string) => void;
  /** 查询列表 */
  queries: string[];
  /** 是否正在处理中 */
  isProcessing?: boolean;
  /** 处理进度信息 */
  progress?: {
    total: number;
    completed: number;
    failed: number;
    processing: number;
    percentage: number;
  };
  /** 开始处理回调 */
  onStartProcess?: () => void;
  /** 查询处理结果回调 */
  onQueryProcessed?: (result: QueryProcessingResult) => void;
  /** 批处理服务实例 - 用于设置底稿数据模式 */
  batchProcessorService?: EnhancedBatchProcessorService;
  /** 是否启用查询去重 */
  enableDeduplication?: boolean;
  /** 去重设置更改回调 */
  onDeduplicationChange?: (enabled: boolean) => void;
}

export const QueryInputPanel: React.FC<QueryInputPanelProps> = ({
  inputText,
  onInputChange,
  queries,
  isProcessing = false,
  progress,
  onQueryProcessed,
  batchProcessorService,
  enableDeduplication = true,
  onDeduplicationChange,
  // onStartProcess, // Unused
}) => {
  // 文本输入焦点状态
  const [isFocused, setIsFocused] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 新增：模式切换状态
  const [useInternalData, setUseInternalData] = useState(false);
  const [internalDataProgress, setInternalDataProgress] = useState(0);
  const [isLoadingInternalData, setIsLoadingInternalData] = useState(false);

  // 计算文字计数显示
  const characterCount = useMemo(() => inputText.length, [inputText]);

  // 提示动画
  useEffect(() => {
    if (!inputText && !isProcessing) {
      const timer = setTimeout(() => {
        setShowHint(true);
        setTimeout(() => {
          setShowHint(false);
        }, 3000);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [inputText, isProcessing]);

  // 计算进度条比例 - 最多500个字符的进度
  // const progressWidth = useMemo(() => {
  //   const maxChars = 500;
  //   const percentage = Math.min((characterCount / maxChars) * 100, 100);
  //   return `${percentage}%`;
  // }, [characterCount]); // Unused

  // 计算查询数量的分级颜色
  // const queryCountColor = useMemo(() => {
  //   if (queries.length === 0) {
  //     return 'var(--color-gray-400)';
  //   }
  //   if (queries.length < 5) {
  //     return 'var(--color-blue-500)';
  //   }
  //   if (queries.length < 15) {
  //     return 'var(--color-primary-500)';
  //   }
  //   if (queries.length < 30) {
  //     return 'var(--color-processing)';
  //   }
  //   return 'var(--color-primary-600)';
  // }, [queries.length]); // Unused

  // 输入文本变化处理
  const handleInputChange = (value: string) => {
    onInputChange(value);
  };

  // 🎯 模式切换处理 - 关键功能：同步UI状态和批处理服务配置
  const handleModeToggle = useCallback((enabled: boolean) => {
    console.log(`[QueryInputPanel] 🎛️ 底稿数据开关切换:`);
    console.log(`   📊 新状态: ${enabled ? '启用' : '关闭'}`);

    // 1️⃣ 更新本地UI状态
    setUseInternalData(enabled);
    console.log(`   ✅ UI状态已更新: useInternalData = ${enabled}`);

    // 2️⃣ 🎯 关键修复：同时更新批处理服务的底稿数据模式
    if (batchProcessorService) {
      console.log(`   📞 调用 batchProcessorService.setInternalDataMode(${enabled})`);
      batchProcessorService.setInternalDataMode(enabled);

      // 3️⃣ 验证配置同步是否成功
      const actualMode = batchProcessorService.getInternalDataMode();
      if (actualMode === enabled) {
        console.log(`   ✅ 批处理服务配置同步成功: ${actualMode}`);
      } else {
        console.error(`   ❌ 批处理服务配置同步失败！`);
        console.error(`      预期: ${enabled}, 实际: ${actualMode}`);
      }
    } else {
      console.error(`   ❌ [QueryInputPanel] 批处理服务未提供，无法设置底稿数据模式`);
      console.error(`      这将导致开关状态与实际行为不一致！`);
    }

    // 4️⃣ 用户友好的状态提示
    if (enabled) {
      console.log(`   🗂️ 用户已启用底稿数据模式`);
      console.log(`   📋 下次查询将先请求底稿数据接口，然后调用AI接口`);
    } else {
      console.log(`   🤖 用户已切换到直接AI模式`);
      console.log(`   📋 下次查询将直接调用AI接口`);
    }
  }, [batchProcessorService]);

  // 查询处理增强（当查询发生变化时自动处理）
  const handleEnhancedQuery = useCallback(async (query: string) => {
    if (!query.trim() || !useInternalData) {
      return query;
    }

    try {
      setIsLoadingInternalData(true);
      setInternalDataProgress(0);

      const result = await DataProcessingService.processQuery(
        query,
        useInternalData,
        setInternalDataProgress
      );

      // 通知父组件查询处理结果
      if (onQueryProcessed) {
        onQueryProcessed(result);
      }

      return result.processedQuery;
    } catch (error) {
      console.error('[QueryInputPanel] 查询增强失败:', error);
      return query; // 回退到原始查询
    } finally {
      setIsLoadingInternalData(false);
      setInternalDataProgress(0);
    }
  }, [useInternalData, onQueryProcessed]);

  // 粘贴样本数据
  const handlePasteSample = () => {
    // 扩展的热门搜索词库
    const allSampleQueries = [
      // --- 数学与教育 ---
      '九九乘法表',
      '绘制 y = x^2 的函数图像',
      '鸡兔同笼问题详解',
      '100以内的质数有哪些',
      '共振',
      '波粒二象性',
      '抛物线',
      '三角函数',
      '拼音教学',
      '笔画教学',
      '勾股定理证明',
      '圆周率的历史',
      '黄金分割比例',
      '斐波那契数列',
      '欧拉公式',
      '微积分入门',
      '线性代数基础',
      '概率论与统计',

      // --- 旅游与热门话题 ---
      '特种兵式旅游热门城市推荐',
      '淄博烧烤攻略',
      '暑期去哪儿旅游最划算',
      '国内热门旅游景点 Top 10',
      '2024年高考作文题目分析',
      '高考专业填报',
      '高中作文题目分析',
      '热门专业解析',
      '三分钟复习高中物理',
      '西藏自驾游路线',
      '新疆旅游攻略',
      '云南大理丽江旅游',
      '四川九寨沟旅游',
      '海南三亚旅游',
      '北京故宫游览攻略',
      '上海迪士尼攻略',
      '杭州西湖一日游',
      '成都美食攻略',

      // --- 图表与数据可视化 ---
      '近十年中国新能源汽车销量变化趋势图',
      '2024年全球智能手机市场份额分布饼图',
      '世界各国人口金字塔对比',
      '绘制一个爱心函数图像',
      '县城gdp排名全国',
      '中国真实人口有多少',
      '全球气温变化趋势图',
      '股市走势分析图表',
      '房价涨跌趋势图',
      '疫情数据可视化',

      // --- 历史文化 ---
      '唐朝灭亡后是哪个朝代',
      '元朝巅峰时期版图',
      '明朝皇帝顺序表',
      '清朝历史大事件',
      '中国古代四大发明',
      '丝绸之路历史',
      '秦始皇统一六国',
      '汉武帝的功绩',
      '唐诗宋词名篇',
      '红楼梦人物关系',
      '水浒传108将',
      '三国演义主要人物',
      '西游记师徒四人',

      // --- 科技与互联网 ---
      '人工智能发展历程',
      'ChatGPT使用技巧','美联储的动向',
      '区块链技术原理',
      '5G网络优势',
      '量子计算机原理',
      '元宇宙概念解析',
      'NFT是什么',
      '比特币价格走势',
      '新能源汽车技术',
      '自动驾驶技术',
      '虚拟现实VR技术',
      '增强现实AR应用',
      '机器学习算法详解',
      '深度学习神经网络',
      '云计算服务对比',
      '物联网技术应用',
      '大数据分析方法',
      '网络安全防护',
      '编程语言对比',
      'Python入门教程',
      'JavaScript框架选择',
      '数据库设计原理',
      '软件工程方法论',
      '敏捷开发流程',
      '区块链挖矿原理',
      '加密货币投资',
      '智能合约开发',
      'Web3.0概念',
      '去中心化应用DApp',

      // --- 生活实用 ---
      '英语语法','为什么要戒糖','代糖评测','个税退税指南','心理学流派','国家一级证书有哪些推荐考','单词速记',
      '商务英语速记',
      '地球仪怎么看',
      '北欧地图',
      '养花小技巧',
      '夜空星座图',
      '拉格朗日定理','美股为什么长牛',
      '金融理财入门',
      '口红试色推荐',
      '粉底液对比指南',
      '高考录取时间线',
      '叶酸含量高的食物',
      '火锅的底料配方',
      '川渝美食',
      '两块大白兔奶糖的热量',
      '生日快乐祝福语',
      '朋友圈文案高级感',
      '减肥食谱推荐',
      '健身计划制定',
      '护肤品使用顺序',
      '家常菜做法大全',
      '装修风格选择',
      '理财产品对比',
      '保险购买指南',
'弗洛姆生平简介',
      // --- 娱乐文化 ---
      '2024年热门电影推荐',
      '经典电视剧排行榜',
      '流行音乐排行榜',
      '网红打卡地推荐',
      '抖音热门话题',
      '小红书种草清单',
      '游戏攻略大全',
      '手机摄影技巧',
      '短视频制作教程',
      '直播带货技巧',

      // --- 健康养生 ---
      '中医养生知识',
      '食疗养生方法',
      '运动健身指南',
      '心理健康调节',
      '睡眠质量改善',
      '营养搭配原则',
      '常见疾病预防',
      '体检报告解读',
      '药物使用注意事项',
      '急救常识大全',

      // --- 职场发展 ---
      '简历制作技巧',
      '面试技巧分享',
      '职场沟通技巧',
      '时间管理方法',
      '团队协作技巧',
      '领导力培养',
      '创业项目选择',
      '副业赚钱方法',
      '技能提升路径',
      '行业发展趋势',

      // --- 欧洲相关 ---
      '欧洲经济走势分析','瑞典的养老制度','北欧的发展历程',
      '欧洲小镇旅游预算和规划',
      '瑞士景区大赏',
      '法国巴黎旅游攻略',
      '意大利罗马景点',
      '德国啤酒文化',
      '英国伦敦必去景点',
      '西班牙弗拉明戈',
      '荷兰郁金香花季',
      '北欧极光观赏',

      // --- 自然科学知识 ---
      '太阳系八大行星',
      '地球的形成过程',
      '恐龙灭绝的原因',
      '进化论基本原理',
      'DNA遗传机制',
      '细胞分裂过程',
      '光合作用原理',
      '生态系统平衡',
      '全球变暖成因',
      '臭氧层破坏',
      '海洋酸化现象',
      '生物多样性保护',
      '基因工程技术',
      '克隆技术原理',
      '干细胞研究',
      '免疫系统机制',
      '病毒传播机制',
      '抗生素耐药性',
      '疫苗工作原理',
      '癌症发病机理',

      // --- 物理化学知识 ---
      '相对论基本概念',
      '量子力学原理',
      '原子结构模型',
      '化学元素周期表',
      '化学反应类型',
      '酸碱中和反应',
      '氧化还原反应',
      '有机化学基础',
      '无机化学应用',
      '物理定律大全',
      '牛顿三大定律',
      '热力学定律',
      '电磁学原理',
      '光学现象解释',
      '声学基础知识',
      '材料科学发展',
      '纳米技术应用',
      '超导体原理',
      '核能发电原理',
      '可再生能源技术',

      // --- 人文社科知识 ---
      '哲学思想流派',
      '古希腊哲学家',
      '中国古代哲学',
      '马克思主义理论',
      '存在主义哲学',
      '认知心理学',
      '社会心理学',
      '发展心理学',
      '教育心理学',
      '行为经济学',
      '宏观经济学',
      '微观经济学',
      '国际贸易理论',
      '货币银行学',
      '财政学基础',
      '统计学方法',
      '社会学理论',
      '人类学研究',
      '政治学概论',
      '国际关系学',

      // --- 艺术文学知识 ---
      '世界名画赏析',
      '音乐理论基础',
      '古典音乐作品',
      '现代艺术流派',
      '建筑风格演变',
      '雕塑艺术历史',
      '摄影技术发展',
      '电影艺术理论',
      '戏剧表演艺术',
      '舞蹈艺术形式',
      '世界文学名著',
      '诗歌创作技巧',
      '小说写作方法',
      '散文艺术特点',
      '修辞手法运用',
      '文学批评理论',
      '比较文学研究',
      '翻译理论实践',
      '语言学基础',
      '汉语言文字学',

      // --- 地理环境知识 ---
      '世界地理概况',
      '中国地理特征',
      '气候类型分布',
      '地质构造运动',
      '火山地震成因',
      '河流湖泊形成',
      '山脉高原特点',
      '海洋洋流分布',
      '极地环境特征',
      '沙漠形成原因',
      '森林生态系统',
      '草原生态特点',
      '湿地保护意义',
      '城市规划原理',
      '交通运输发展',
      '人口分布规律',
      '资源开发利用',
      '环境污染治理',
      '可持续发展',
      '生态文明建设',
    ];

    // 随机选择15个查询并打乱顺序
    const shuffleArray = (array: string[]) => {
      const shuffled = [...array];
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
      }
      return shuffled;
    };

    const randomQueries = shuffleArray(allSampleQueries).slice(0, 15);
    const sampleData = randomQueries.join('\n');

    // 直接填充数据，移除所有云朵和火箭动画
    handleInputChange(sampleData);

    // 星光主题的文本框脉冲效果
    if (textareaRef.current) {
      textareaRef.current.classList.add('starlight-pulse');
      setTimeout(() => {
        textareaRef.current?.classList.remove('starlight-pulse');
      }, 1200);
    }
  };

  // 清空输入
  const handleClearInput = () => {
    if (inputText) {
      // 添加星光主题清空动画
      if (textareaRef.current) {
        textareaRef.current.classList.add('starlight-clear');
        setTimeout(() => {
          handleInputChange('');
          textareaRef.current?.classList.remove('starlight-clear');
          textareaRef.current?.focus();
        }, 600);
      } else {
        handleInputChange('');
      }
    } else {
      textareaRef.current?.focus();
    }
  };

  // 移除自动调整高度，使用固定高度 + overflow-y: auto

  return (
    <div className="relative overflow-hidden flex flex-col bg-transparent">

      <div className="space-y-6 flex flex-col">
        {/* 新增：模式切换区域 */}
        <div className="relative flex-shrink-0 z-20">
          <ModeToggle
            useInternalData={useInternalData}
            onToggle={handleModeToggle}
            disabled={isProcessing}
            loading={isLoadingInternalData}
            showDescription={true}
          />

          {/* 内部数据加载进度 */}
          {useInternalData && internalDataProgress > 0 && internalDataProgress < 1 && (
            <div className="mt-2 px-4 py-2 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="text-xs text-blue-700 mb-1 flex items-center">
                <Icon type="loading" size="xs" className="animate-spin mr-2" />
                正在获取底稿数据... {Math.round(internalDataProgress * 100)}%
              </div>
              <div className="w-full bg-blue-200 rounded-full h-1.5">
                <div
                  className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                  style={{ width: `${internalDataProgress * 100}%` }}
                />
              </div>
            </div>
          )}
        </div>

        {/* 统一的输入区域 - 使用统一的玻璃卡片样式 */}
        <div className="relative flex-shrink-0 z-20">
          <div className="glass-card p-0 overflow-hidden">
            {/* 标题区域 */}
            <div
              className="px-4 py-3 border-b"
              style={{
                backgroundColor: 'var(--color-primary-50)',
                borderColor: 'var(--color-primary-200)',
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="unified-title-tertiary mb-0">
                  <div className="unified-title-icon-tertiary">
                    <Icon type="edit" color="white" size="sm" />
                  </div>
                  输入区域
                </h3>

                {/* 去重开关 - 与底稿数据开关保持一致的样式 */}
                <Tooltip
                  content={
                    enableDeduplication
                      ? "已启用去重：相同查询只会处理一次"
                      : "已禁用去重：相同查询会被多次处理"
                  }
                  placement="bottom"
                >
                  <div className="flex items-center gap-3">
                    <span className="text-xs font-medium text-gray-700">
                      去重
                    </span>
                    <Switch
                      checked={enableDeduplication}
                      onChange={(checked) => onDeduplicationChange?.(checked)}
                      size="small"
                      disabled={isProcessing}
                      className="deduplication-switch"
                      aria-label="切换查询去重模式"
                    />
                  </div>
                </Tooltip>
              </div>

              {/* 按钮区域 - 优化布局 */}
              <div className="flex gap-3 mb-4">
                <button
                  type="button"
                  onClick={handlePasteSample}
                  className="btn--secondary-glass btn btn-sm flex items-center justify-center relative z-10"
                >
                  <Icon type="database" color="white" size="sm" className="mr-2" />
                  <span className="relative z-10">示例数据</span>
                </button>
                <button
                  type="button"
                  onClick={handleClearInput}
                  disabled={!inputText}
                  className={`btn btn-sm flex items-center justify-center relative z-10 transition-all duration-200 ${
                    inputText ? 'btn--secondary-glass opacity-100' : 'opacity-50 cursor-not-allowed'
                  }`}
                >
                  <Icon
                    type="trash"
                    color="white"
                    size="sm"
                    className="mr-2 flex-shrink-0"
                  />
                  <span className="relative z-10">清除</span>
                </button>
              </div>

              {/* 状态指示器 */}
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center">
                  <div
                    className="w-2 h-2 rounded-full mr-2"
                    style={{
                      backgroundColor:
                        queries.length > 0
                          ? 'var(--color-primary-500)'
                          : 'var(--color-gray-400)',
                    }}
                  />
                  <span style={{ color: 'var(--color-gray-600)' }}>
                    {queries.length > 0
                      ? `已解析 ${queries.length} 个查询${!enableDeduplication ? '（含重复）' : ''}`
                      : '等待输入查询'}
                  </span>
                </div>
                <span style={{ color: 'var(--color-gray-500)' }}>
                  {characterCount} 字符
                </span>
              </div>
            </div>

            {/* 输入框区域 - 直接开始，无需额外标题栏 */}
            <div
              className="px-4 py-2 border-b"
              style={{
                backgroundColor: 'var(--color-gray-50)',
                borderColor: 'var(--color-gray-200)',
              }}
            >
              <div className="flex items-center justify-between">
                <span
                  className="text-xs font-medium"
                  style={{ color: 'var(--color-gray-600)' }}
                >
                  查询输入框
                </span>
                <div
                  className="flex items-center text-xs"
                  style={{ color: 'var(--color-gray-500)' }}
                >
                  <span className="mr-2">支持多行 | 逗号分隔</span>
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{
                      backgroundColor: isFocused
                        ? 'var(--color-primary-500)'
                        : 'var(--color-gray-400)',
                    }}
                  />
                </div>
              </div>
            </div>

            <div
              className="query-input-textarea-container relative"
              style={{ height: '160px' }}
            >
              {/* 输入提示动画 */}
              {showHint && !inputText && !isProcessing && (
                <div className="absolute inset-0 z-10 flex items-center justify-center bg-white bg-opacity-95 rounded-lg animate-fade-in-up">
                  <div className="text-center p-6">
                    <div className="elegant-icon-container">
                      <Icon
                        type="lightning"
                        color="white"
                        size="lg"
                        className="icon-main"
                      />
                    </div>
                    <h3 className="text-base font-semibold text-gray-800 mb-2">
                      开始批量处理
                    </h3>
                    <p className="text-sm text-gray-600 mb-3">
                      输入多个查询，一键生成所有结果
                    </p>
                    <p className="text-xs text-gray-500">
                      点击&quot;示例&quot;按钮快速体验 →
                    </p>
                  </div>
                </div>
              )}

              <textarea
                ref={textareaRef}
                className="w-full h-full p-4 resize-none focus:outline-none border-0 overflow-y-auto optimized-scrollbar query-input-textarea"
                style={{
                  backgroundColor: isProcessing
                    ? 'var(--color-primary-50)'
                    : 'transparent',
                  color: 'var(--color-gray-800)',
                }}
                placeholder="请输入查询列表，每行一个查询或用逗号、分号分隔&#10;&#10;示例：&#10;百数表&#10;三角函数&#10;两块大白兔奶糖的热量"
                value={inputText}
                onChange={e => handleInputChange(e.target.value)}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                disabled={isProcessing}
              />
            </div>

            {/* 处理中状态覆盖层 */}
            {isProcessing && (
              <div
                className="absolute inset-0 flex items-center justify-center"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',zIndex: 99
                }}
              >
                <div
                  className="rounded-lg px-3 py-2 flex items-center"
                  style={{
                    backgroundColor: 'var(--color-blue-100)',
                    border: '1px solid var(--color-blue-300)',
                  }}
                >
                  <div
                    className="w-4 h-4 border-2 rounded-full animate-spin mr-2"
                    style={{
                      borderColor: 'var(--color-blue-500)',
                      borderTopColor: 'transparent',
                    }}
                  />
                  <span
                    className="text-sm font-medium"
                    style={{ color: 'var(--color-blue-700)' }}
                  >
                    {progress && progress.total > 0
                      ? `正在处理 ${progress.processing || 0} 个查询 (${progress.percentage.toFixed(0)}% 完成)`
                      : '正在处理中...'}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 统一的查询预览 - 增强视觉效果 */}
        <div
          className="glass-card p-0 overflow-hidden flex flex-col flex-shrink-0 relative z-5"
          style={{ height: '280px' }}
          data-testid="query-preview-panel"
        >
          {/* 标题区域 - 与输入区域保持一致的结构 */}
          <div
            className="px-4 py-3 border-b flex-shrink-0"
            style={{
              backgroundColor: 'var(--color-primary-50)',
              borderColor: 'var(--color-primary-200)',
            }}
          >
            <h4 className="unified-title-tertiary">
              <div className="unified-title-icon-tertiary">
                <Icon type="list" color="white" size="sm" />
              </div>
              查询预览
              <div
                className="rounded-lg px-2 py-1 ml-auto"
                style={{
                  backgroundColor: 'var(--color-primary-50)',
                  border: '1px solid var(--color-primary-200)',
                }}
              >
                <span
                  className="text-sm font-medium"
                  style={{ color: 'var(--color-primary-700)' }}
                >
                  {queries.length}
                </span>
                <span
                  className="text-xs ml-1"
                  style={{ color: 'var(--color-gray-600)' }}
                >
                  个
                </span>
              </div>
            </h4>
          </div>

          {/* 内容区域 - 添加padding保持一致性 */}
          <div className="query-preview-container flex-1 overflow-y-auto px-4 py-3">
            {queries.length > 0 ? (
              <div className="space-y-1">
                {queries.map((query, index) => (
                  <div key={index} className="query-preview-item">
                    <div className="query-preview-item-number">{index + 1}</div>
                    <span className="query-preview-item-text">{query}</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div
                    className="w-8 h-8 rounded-full mx-auto mb-2 flex items-center justify-center flex-shrink-0"
                    style={{
                      backgroundColor: 'var(--color-gray-200)',
                    }}
                  >
                    <Icon
                      type="list"
                      color="gray"
                      size="sm"
                      className="flex-shrink-0"
                    />
                  </div>
                  <p
                    className="text-sm"
                    style={{ color: 'var(--color-gray-500)' }}
                  >
                    输入查询后将在此显示预览
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default QueryInputPanel;
