import React, { useMemo } from 'react';
import { ProcessResult, BatchProgress } from '../types';
import Icon from './Icon';

/**
 * Query Progress Detail Component
 * 查询进度详细信息组件
 *
 * 显示每个查询词的具体状态和进度
 */
interface QueryProgressDetailProps {
  /** 处理结果数组 */
  results: ProcessResult[];
  /** 批处理进度信息 */
  progress: BatchProgress;
  /** 是否正在运行批处理 */
  isRunning: boolean;
  /** 原始查询列表（用于显示待处理的查询） */
  queries?: string[];
  /** 是否展开显示详细信息 */
  expanded?: boolean;
}

/**
 * Get status display information for a query
 * 获取查询的状态显示信息
 */
const getQueryStatusInfo = (
  result: ProcessResult | undefined,
  query: string,
) => {
  if (!result) {
    return {
      status: 'pending',
      icon: 'clock',
      color: 'text-gray-500',
      bgColor: 'bg-gray-100',
      text: '等待处理',
    };
  }

  switch (result.status) {
    case 'success':
      return {
        status: 'success',
        icon: 'check',
        color: 'text-green-600',
        bgColor: 'bg-green-100',
        text: '处理成功',
      };
    case 'error':
      return {
        status: 'error',
        icon: 'cross',
        color: 'text-red-600',
        bgColor: 'bg-red-100',
        text: result.error || '处理失败',
      };
    case 'processing':
      return {
        status: 'processing',
        icon: 'processing',
        color: 'text-blue-600',
        bgColor: 'bg-blue-100',
        text: '正在处理',
      };
    case 'waiting':
    case 'pending':
    default:
      return {
        status: 'pending',
        icon: 'clock',
        color: 'text-gray-500',
        bgColor: 'bg-gray-100',
        text: '等待处理',
      };
  }
};

export const QueryProgressDetail: React.FC<QueryProgressDetailProps> = ({
  results,
  progress,
  isRunning,
  queries = [],
  expanded = false,
}) => {
  /**
   * Create a map of query status information
   * 创建查询状态信息映射
   */
  const queryStatusMap = useMemo(() => {
    const statusMap = new Map<string, ProcessResult>();
    results.forEach(result => {
      statusMap.set(result.query, result);
    });
    return statusMap;
  }, [results]);

  /**
   * Get all queries including those not yet started
   * 获取所有查询，包括尚未开始的查询
   */
  const allQueries = useMemo(() => {
    // Use original queries list as the source of truth
    // 使用原始查询列表作为数据源
    if (queries.length > 0) {
      return queries;
    }
    // Fallback to queries from results if original list not available
    // 如果原始列表不可用，则回退到结果中的查询
    return results.map(r => r.query);
  }, [queries, results]);

  /**
   * Categorize queries by status
   * 按状态分类查询
   */
  const categorizedQueries = useMemo(() => {
    const processing: string[] = [];
    const completed: string[] = [];
    const failed: string[] = [];
    const pending: string[] = [];

    allQueries.forEach(query => {
      const result = queryStatusMap.get(query);
      if (!result) {
        pending.push(query);
      } else {
        switch (result.status) {
          case 'processing':
            processing.push(query);
            break;
          case 'success':
            completed.push(query);
            break;
          case 'error':
            failed.push(query);
            break;
          default:
            pending.push(query);
        }
      }
    });

    return { processing, completed, failed, pending };
  }, [allQueries, queryStatusMap]);

  // Don't render if not running and no results
  // 如果没有运行且没有结果则不渲染
  if (!isRunning && results.length === 0) {
    return null;
  }

  return (
    <div className="mt-3 space-y-2">
      {/* Compact status overview with query samples */}
      {/* 紧凑状态概览，包含查询样本 */}
      <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
        <div className="flex items-center justify-between text-xs mb-2">
          <div className="flex items-center space-x-3">
            {progress.processing > 0 && (
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-1 animate-pulse" />
                <span className="text-blue-600 font-medium">
                  处理中 {progress.processing}
                </span>
              </div>
            )}
            {progress.completed > 0 && (
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-1" />
                <span className="text-green-600">
                  已完成 {progress.completed}
                </span>
              </div>
            )}
            {progress.failed > 0 && (
              <div className="flex items-center">
                <div className="w-2 h-2 bg-red-500 rounded-full mr-1" />
                <span className="text-red-600">失败 {progress.failed}</span>
              </div>
            )}
            {progress.pending > 0 && (
              <div className="flex items-center">
                <div className="w-2 h-2 bg-gray-400 rounded-full mr-1" />
                <span className="text-gray-500">待处理 {progress.pending}</span>
              </div>
            )}
          </div>

          {/* Progress percentage */}
          {/* 进度百分比 */}
          <div className="text-gray-600 font-medium text-sm">
            {progress.percentage.toFixed(0)}%
          </div>
        </div>

        {/* Quick status indicators with query samples */}
        {/* 快速状态指示器，包含查询样本 */}
        <div className="space-y-1">
          {/* Current processing queries */}
          {categorizedQueries.processing.length > 0 && (
            <div className="flex items-center text-xs">
              <Icon
                type="processing"
                color="blue"
                size="xs"
                animate={true}
                className="mr-1"
              />
              <span className="text-blue-700 font-medium mr-2">处理中</span>
              <div className="flex-1 flex flex-wrap gap-1">
                {categorizedQueries.processing
                  .slice(0, 2)
                  .map((query, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs truncate max-w-32"
                    >
                      {query}
                    </span>
                  ))}
                {categorizedQueries.processing.length > 2 && (
                  <span className="text-blue-600 text-xs">
                    +{categorizedQueries.processing.length - 2} 更多
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Recently completed queries */}
          {categorizedQueries.completed.length > 0 && (
            <div className="flex items-center text-xs">
              <Icon type="check" color="green" size="xs" className="mr-1" />
              <span className="text-green-700 font-medium mr-2">最近完成</span>
              <div className="flex-1 flex flex-wrap gap-1">
                {categorizedQueries.completed.slice(-2).map((query, index) => (
                  <span
                    key={index}
                    className="bg-green-100 text-green-800 px-2 py-0.5 rounded text-xs truncate max-w-32"
                  >
                    {query}
                  </span>
                ))}
                {categorizedQueries.completed.length > 2 && (
                  <span className="text-green-600 text-xs">
                    +{categorizedQueries.completed.length - 2} 更多
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Failed queries */}
          {categorizedQueries.failed.length > 0 && (
            <div className="flex items-center text-xs">
              <Icon type="cross" color="red" size="xs" className="mr-1" />
              <span className="text-red-700 font-medium mr-2">失败</span>
              <div className="flex-1 flex flex-wrap gap-1">
                {categorizedQueries.failed.slice(0, 2).map((query, index) => (
                  <span
                    key={index}
                    className="bg-red-100 text-red-800 px-2 py-0.5 rounded text-xs truncate max-w-32"
                  >
                    {query}
                  </span>
                ))}
                {categorizedQueries.failed.length > 2 && (
                  <span className="text-red-600 text-xs">
                    +{categorizedQueries.failed.length - 2} 更多
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Next pending queries */}
          {categorizedQueries.pending.length > 0 && isRunning && (
            <div className="flex items-center text-xs">
              <Icon type="clock" color="gray" size="xs" className="mr-1" />
              <span className="text-gray-700 font-medium mr-2">即将处理</span>
              <div className="flex-1 flex flex-wrap gap-1">
                {categorizedQueries.pending.slice(0, 3).map((query, index) => (
                  <span
                    key={index}
                    className="bg-gray-100 text-gray-700 px-2 py-0.5 rounded text-xs truncate max-w-32"
                  >
                    {query}
                  </span>
                ))}
                {categorizedQueries.pending.length > 3 && (
                  <span className="text-gray-600 text-xs">
                    +{categorizedQueries.pending.length - 3} 更多
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Detailed query status list (if expanded) */}
      {/* 详细查询状态列表（如果展开） */}
      {expanded && (
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {/* Processing queries */}
            {categorizedQueries.processing.map((query, index) => {
              const statusInfo = getQueryStatusInfo(
                queryStatusMap.get(query),
                query,
              );
              return (
                <div
                  key={`processing-${index}`}
                  className="flex items-center text-xs"
                >
                  <div
                    className={`w-3 h-3 rounded-full flex items-center justify-center mr-2 ${statusInfo.bgColor}`}
                  >
                    <Icon
                      type={statusInfo.icon as any}
                      color="blue"
                      size="xs"
                      animate={true}
                    />
                  </div>
                  <span className={`${statusInfo.color} font-medium mr-2`}>
                    {statusInfo.text}
                  </span>
                  <span className="text-gray-700 truncate flex-1">{query}</span>
                </div>
              );
            })}

            {/* Completed queries */}
            {categorizedQueries.completed.map((query, index) => {
              const statusInfo = getQueryStatusInfo(
                queryStatusMap.get(query),
                query,
              );
              return (
                <div
                  key={`completed-${index}`}
                  className="flex items-center text-xs"
                >
                  <div
                    className={`w-3 h-3 rounded-full flex items-center justify-center mr-2 ${statusInfo.bgColor}`}
                  >
                    <Icon
                      type={statusInfo.icon as any}
                      color="green"
                      size="xs"
                    />
                  </div>
                  <span className={`${statusInfo.color} font-medium mr-2`}>
                    {statusInfo.text}
                  </span>
                  <span className="text-gray-700 truncate flex-1">{query}</span>
                </div>
              );
            })}

            {/* Failed queries */}
            {categorizedQueries.failed.map((query, index) => {
              const statusInfo = getQueryStatusInfo(
                queryStatusMap.get(query),
                query,
              );
              return (
                <div
                  key={`failed-${index}`}
                  className="flex items-center text-xs"
                >
                  <div
                    className={`w-3 h-3 rounded-full flex items-center justify-center mr-2 ${statusInfo.bgColor}`}
                  >
                    <Icon type={statusInfo.icon as any} color="red" size="xs" />
                  </div>
                  <span className={`${statusInfo.color} font-medium mr-2`}>
                    {statusInfo.text}
                  </span>
                  <span className="text-gray-700 truncate flex-1">{query}</span>
                </div>
              );
            })}

            {/* Pending queries */}
            {categorizedQueries.pending.map((query, index) => {
              const statusInfo = getQueryStatusInfo(undefined, query);
              return (
                <div
                  key={`pending-${index}`}
                  className="flex items-center text-xs"
                >
                  <div
                    className={`w-3 h-3 rounded-full flex items-center justify-center mr-2 ${statusInfo.bgColor}`}
                  >
                    <Icon
                      type={statusInfo.icon as any}
                      color="gray"
                      size="xs"
                    />
                  </div>
                  <span className={`${statusInfo.color} font-medium mr-2`}>
                    {statusInfo.text}
                  </span>
                  <span className="text-gray-700 truncate flex-1">{query}</span>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default QueryProgressDetail;
