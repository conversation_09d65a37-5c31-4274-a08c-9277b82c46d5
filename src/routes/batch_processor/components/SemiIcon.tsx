import React from 'react';
import {
  IconLightningStroked,
  IconBulb,
  IconEditStroked,
  IconHistory,
  IconSetting,
  IconStop,
  IconCopy,
  IconSpin,
  IconLoading,
  IconBolt,
  IconRefresh,
  IconCheckboxTick,
  IconCheckList,
  IconCheckCircleStroked,
  IconTick,
} from '@douyinfe/semi-icons';

// 图标类型映射 - 只包含实际使用的图标
export type SemiIconType =
  | 'lightning'      // 闪电
  | 'lightbulb'      // 灯泡
  | 'edit'           // 编辑
  | 'history'        // 历史
  | 'settings'       // 设置
  | 'stop'           // 停止
  | 'copy'           // 复制
  | 'processing'     // 处理中
  | 'loading'        // 加载中
  | 'target'         // 目标 - 圆形图标
  | 'bolt'           // 闪电能量 - 表示充满能量准备行动
  | 'check-circle'   // 检查圆圈 - 表示准备完成
  | 'refresh'        // 刷新/重试
  | 'checkbox-tick'  // 复选框勾选 - 方形外框，更语义化
  | 'check-list'     // 检查列表 - 列表形式的检查
  | 'check-circle-stroked' // 圆形检查线条版
  | 'tick';          // 简单勾选

export type SemiIconSize = 'small' | 'default' | 'large' | 'extra-large';

export type SemiIconColor = 
  | 'auto'           // 自动检测父按钮颜色
  | 'gold'           // 金色按钮颜色 #92400e
  | 'blue'           // 蓝色按钮颜色 #1e40af
  | 'success'        // 成功状态 #16a34a
  | 'error'          // 错误状态 #dc2626
  | 'warning'        // 警告状态 #d97706
  | 'neutral'        // 中性颜色
  | 'white'          // 白色
  | string;          // 自定义颜色

interface SemiIconProps {
  type: SemiIconType;
  size?: SemiIconSize;
  color?: SemiIconColor;
  className?: string;
  spin?: boolean;      // 旋转动画
  style?: React.CSSProperties;
}

// 图标组件映射 - 只包含实际使用的图标
const iconComponents: Record<SemiIconType, React.ComponentType<any>> = {
  lightning: IconLightningStroked,
  lightbulb: IconBulb,
  edit: IconEditStroked,
  history: IconHistory,
  settings: IconSetting,
  stop: IconStop,
  copy: IconCopy,
  processing: IconSpin,
  loading: IconLoading,
  bolt: IconBolt,
  refresh: IconRefresh,
  'checkbox-tick': IconCheckboxTick,
  'check-list': IconCheckList,
  'check-circle-stroked': IconCheckCircleStroked,
  tick: IconTick,
};

// 颜色映射
const colorMap: Record<string, string> = {
  auto: 'currentColor',
  gold: '#92400e',      // 金色按钮文字颜色
  blue: '#1e40af',      // 蓝色按钮文字颜色
  success: '#16a34a',   // 成功绿色
  error: '#dc2626',     // 错误红色
  warning: '#d97706',   // 警告橙色
  neutral: '#64748b',   // 中性灰色
  white: '#ffffff',     // 白色
};

// 尺寸映射
const sizeMap: Record<SemiIconSize, string> = {
  small: '14px',
  default: '16px',
  large: '20px',
  'extra-large': '24px',
};

// 检测父元素按钮类型，自动选择颜色
const useAutoColor = (color: SemiIconColor): string => {
  if (color !== 'auto') {
    return colorMap[color] || color;
  }

  // 在实际应用中，这里可以通过React Context或DOM查询来检测父按钮类型
  // 现在先返回默认颜色
  return 'currentColor';
};

const SemiIcon: React.FC<SemiIconProps> = ({
  type,
  size = 'default',
  color = 'auto',
  className = '',
  spin = false,
  style = {},
}) => {
  const IconComponent = iconComponents[type];
  const resolvedColor = useAutoColor(color);
  const resolvedSize = sizeMap[size];

  if (!IconComponent) {
    console.warn(`SemiIcon: Unknown icon type "${type}"`);
    return null;
  }

  const iconStyle: React.CSSProperties = {
    color: resolvedColor,
    fontSize: resolvedSize,
    width: resolvedSize,
    height: resolvedSize,
    ...style,
  };

  // 添加旋转动画类
  const combinedClassName = `semi-icon ${spin ? 'semi-icon-spin' : ''} ${className}`.trim();

  return (
    <IconComponent
      style={iconStyle}
      className={combinedClassName}
      size={size}
    />
  );
};

export default SemiIcon;

// 导出一些常用的预设配置
export const GoldIcon: React.FC<Omit<SemiIconProps, 'color'>> = (props) => (
  <SemiIcon {...props} color="gold" />
);

export const BlueIcon: React.FC<Omit<SemiIconProps, 'color'>> = (props) => (
  <SemiIcon {...props} color="blue" />
);

export const SuccessIcon: React.FC<Omit<SemiIconProps, 'color'>> = (props) => (
  <SemiIcon {...props} color="success" />
);

export const ErrorIcon: React.FC<Omit<SemiIconProps, 'color'>> = (props) => (
  <SemiIcon {...props} color="error" />
);

export const WarningIcon: React.FC<Omit<SemiIconProps, 'color'>> = (props) => (
  <SemiIcon {...props} color="warning" />
);