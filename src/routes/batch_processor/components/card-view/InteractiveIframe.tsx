import React, { useCallback, useEffect, useRef, useState } from 'react';
import { colors } from '../../config/theme';
import { ProcessResult } from '../../types';
// 使用Parse5-based转换系统
import type {
  Parse5ConversionOptions,
  Parse5PreviewResult,
} from '../../runtime_convert_parse5/adapters/batch-processor-adapter';
import { Parse5BatchProcessorAdapter } from '../../runtime_convert_parse5/adapters/batch-processor-adapter';
// 集成增强的Lynx预览管理器
import type { PreviewResult as LynxPreviewResult } from '../../runtime_convert_parse5/lynx-preview-manager';
import { LynxPreviewManager } from '../../runtime_convert_parse5/lynx-preview-manager';
// 静态导入Parse5增强转换器避免chunk加载问题
import {
  DEFAULT_PARSE5_CONFIG,
  Parse5EnhancedConverter,
} from '../../utils/parse5EnhancedConverter';
// 🚀 新增：导入基于 Prompts 的智能转换器
import { PromptsBasedConverter } from '../../utils/promptsBasedConverter';

// 类型别名以保持兼容性
type PreviewResult = Parse5PreviewResult;
type ConversionOptions = Parse5ConversionOptions;

interface InteractiveIframeProps {
  /** 处理结果数据 */
  result: ProcessResult;
  /** 是否聚焦状态 - 聚焦时启用iframe交互 */
  focused?: boolean;
  /** iframe宽度 */
  width?: number;
  /** iframe高度 */
  height?: number;
  /** 缩略图URL - 如果提供则优先显示缩略图 */
  thumbnailUrl?: string;
  /** 缩略图上传失败时的回调 */
  onThumbnailError?: () => void;
  /** 预览生成完成回调 */
  onPreviewGenerated?: (resultId: string, preview: PreviewResult) => void;
  /** 是否启用自动截图 */
  enableAutoScreenshot?: boolean;
  /** 是否启用增强的Lynx预览模式 */
  enableEnhancedLynxPreview?: boolean;
}

export const InteractiveIframe: React.FC<InteractiveIframeProps> = ({
  result,
  focused = false,
  width = 280,
  height = 500,
  thumbnailUrl,
  onThumbnailError,
  onPreviewGenerated,
  enableAutoScreenshot = false,
  enableEnhancedLynxPreview = false,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [showIframe, setShowIframe] = useState(false);
  const [thumbnailError, setThumbnailError] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [previewResult, setPreviewResult] = useState<PreviewResult | null>(
    null,
  );
  const [conversionStatus, setConversionStatus] = useState<
    'idle' | 'detecting' | 'converting' | 'capturing' | 'success' | 'error'
  >('idle');
  const [conversionError, setConversionError] = useState<string | null>(null);

  // 稳定性状态管理
  const [isContentStable, setIsContentStable] = useState(false);
  const [stableContent, setStableContent] = useState<string>('');
  const [hasFailedPermanently, setHasFailedPermanently] = useState(false);
  const [permanentErrorContent, setPermanentErrorContent] =
    useState<string>('');
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const adapterRef = useRef<Parse5BatchProcessorAdapter | null>(null);
  const lynxPreviewManagerRef = useRef<LynxPreviewManager | null>(null);
  const conversionCacheRef = useRef<Map<string, PreviewResult>>(new Map());
  const lynxPreviewCacheRef = useRef<Map<string, LynxPreviewResult>>(new Map());
  const errorStabilityRef = useRef<{
    lastError: string | null;
    count: number;
    timestamp: number;
  }>({
    lastError: null,
    count: 0,
    timestamp: 0,
  });
  const contentStabilityRef = useRef<{
    lastContent: string;
    lastUpdate: number;
    stabilityTimeout: NodeJS.Timeout | null;
  }>({
    lastContent: '',
    lastUpdate: 0,
    stabilityTimeout: null,
  });

  // 初始化Parse5适配器和LynxPreviewManager (runtime_convert_parse5)
  useEffect(() => {
    console.log('🚀 [InteractiveIframe] 初始化Parse5转换引擎');

    adapterRef.current = new Parse5BatchProcessorAdapter({
      enableCache: true,
      enableScope: true,
      enableOptimization: true,
      strictMode: false, // 宽松模式，提供更好的错误恢复
    });

    // 初始化增强的Lynx预览管理器
    if (enableEnhancedLynxPreview) {
      console.log('🌟 [InteractiveIframe] 初始化增强Lynx预览管理器');
      lynxPreviewManagerRef.current = new LynxPreviewManager({
        enableDebug: true,
        previewMode: 'iframe',
        autoRetry: true,
        maxRetries: 3,
        timeout: 30000,
      });

      // 设置事件监听
      lynxPreviewManagerRef.current.on('progress', event => {
        console.log('📈 [LynxPreview] 转换进度:', event.data);
        setConversionStatus('converting');
      });

      lynxPreviewManagerRef.current.on('success', event => {
        console.log('✅ [LynxPreview] 转换成功:', event.data);
        setConversionStatus('success');
      });

      lynxPreviewManagerRef.current.on('error', event => {
        console.error('❌ [LynxPreview] 转换失败:', event.data);
        setConversionStatus('error');
        setConversionError(event.data?.error || '未知错误');
      });

      lynxPreviewManagerRef.current.on('retry', event => {
        console.log('🔄 [LynxPreview] 重试转换:', event.data);
        setConversionStatus('converting');
      });
    }

    return () => {
      adapterRef.current?.dispose();
      lynxPreviewManagerRef.current?.dispose();

      // 清理稳定性超时
      if (contentStabilityRef.current.stabilityTimeout) {
        clearTimeout(contentStabilityRef.current.stabilityTimeout);
      }
    };
  }, [enableEnhancedLynxPreview]);

  // 检查是否在可视区域
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold: 0.1 },
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // 🚀 新增：基于 Prompts 的智能转换系统
  const convertWithPromptsIntelligence = async (
    ttmlContent: string,
    ttssContent: string,
    jsContent: string,
    queryContext?: string
  ): Promise<string> => {
    console.log('🧠 [InteractiveIframe] 开始基于 Prompts 的智能转换');
    
    try {
      const promptsConverter = new PromptsBasedConverter({
        enableContentAnalysis: true,
        enableIntelligentVisualization: true,
        enforceUIGuidance: true,
        enforceP0Standards: true,
        enableMagazineDesign: true,
        enableDebugMode: true,
      });
      
      const result = await promptsConverter.convertWithPrompts(
        ttmlContent,
        ttssContent,
        jsContent,
        queryContext || ''
      );
      
      if (result.success) {
        console.log('✅ [PromptsConverter] 智能转换成功');
        console.log('📊 [PromptsConverter] 内容类型:', result.insights.contentType);
        console.log('🎯 [PromptsConverter] 可视化类型:', result.insights.visualizationType);
        console.log('⏱️ [PromptsConverter] 处理时间:', result.insights.processingTime + 'ms');
        
        return result.html;
      } else {
        console.error('❌ [PromptsConverter] 转换失败:', result.errors);
        throw new Error(result.errors.join(', '));
      }
    } catch (error) {
      console.error('❌ [PromptsConverter] 智能转换异常:', error);
      throw error;
    }
  };

  // 企业级TTML到HTML转换系统（基于升级方案文档）
  const convertTtmlToHtml = (ttmlContent: string, componentId?: string) => {
    console.log('🔄 [InteractiveIframe] 开始企业级TTML到HTML转换');
    console.log(
      '📄 [InteractiveIframe] TTML内容:',
      ttmlContent.substring(0, 300),
    );

    // 生成作用域标识符（5位哈希，模拟企业级标准）
    const scopeId = componentId || Math.random().toString(36).substr(2, 5);
    const scopeAttribute = `data-v-${scopeId}`;

    // 企业级元素映射表（符合@byted-lynx/web-speedy-plugin标准）
    const ELEMENT_MAPPING = {
      // 基础布局容器
      view: { tag: 'div', className: 'lynx-view' },
      'scroll-view': { tag: 'div', className: 'lynx-scroll-view' },
      'movable-area': { tag: 'div', className: 'lynx-movable-area' },
      'movable-view': { tag: 'div', className: 'lynx-movable-view' },
      'cover-view': { tag: 'div', className: 'lynx-cover-view' },
      'cover-image': {
        tag: 'img',
        className: 'lynx-cover-image',
        selfClosing: true,
      },

      // 文本与富文本元素
      text: { tag: 'span', className: 'lynx-text' },
      'rich-text': { tag: 'div', className: 'lynx-rich-text' },

      // 媒体元素
      image: { tag: 'img', className: 'lynx-image', selfClosing: true },
      video: { tag: 'video', className: 'lynx-video' },
      audio: { tag: 'audio', className: 'lynx-audio' },

      // 表单元素
      input: { tag: 'input', className: 'lynx-input', selfClosing: true },
      textarea: { tag: 'textarea', className: 'lynx-textarea' },
      button: { tag: 'button', className: 'lynx-button' },
      switch: {
        tag: 'input',
        type: 'checkbox',
        className: 'lynx-switch',
        selfClosing: true,
      },
      slider: {
        tag: 'input',
        type: 'range',
        className: 'lynx-slider',
        selfClosing: true,
      },
      picker: { tag: 'select', className: 'lynx-picker' },

      // 高级组件
      swiper: { tag: 'div', className: 'lynx-swiper' },
      'swiper-item': { tag: 'div', className: 'lynx-swiper-item' },
      progress: { tag: 'progress', className: 'lynx-progress' },
      canvas: { tag: 'canvas', className: 'lynx-canvas' },
      'web-view': {
        tag: 'iframe',
        className: 'lynx-web-view',
        selfClosing: true,
      },

      // 列表与导航元素
      list: { tag: 'div', className: 'lynx-list' },
      'list-item': { tag: 'div', className: 'lynx-list-item' },
      cell: { tag: 'div', className: 'lynx-cell' },
      navigator: { tag: 'a', className: 'lynx-navigator' },
      link: { tag: 'a', className: 'lynx-link' },
    };

    // 属性映射表（企业级标准）
    const ATTRIBUTE_MAPPING: Record<string, Record<string, string>> = {
      image: {
        src: 'src',
        mode: 'data-mode',
        'lazy-load': 'loading',
        'fade-in': 'data-fade-in',
        webp: 'data-webp',
        'show-menu-by-longpress': 'data-show-menu',
      },
      'scroll-view': {
        'scroll-x': 'data-scroll-x',
        'scroll-y': 'data-scroll-y',
        'upper-threshold': 'data-upper-threshold',
        'lower-threshold': 'data-lower-threshold',
        'scroll-into-view': 'data-scroll-into-view',
        'scroll-with-animation': 'data-scroll-with-animation',
        'enable-back-to-top': 'data-enable-back-to-top',
      },
      swiper: {
        'indicator-dots': 'data-indicator-dots',
        'indicator-color': 'data-indicator-color',
        'indicator-active-color': 'data-indicator-active-color',
        autoplay: 'data-autoplay',
        interval: 'data-interval',
        duration: 'data-duration',
        circular: 'data-circular',
        vertical: 'data-vertical',
      },
      list: {
        'scroll-direction': 'data-scroll-direction',
        'item-count': 'data-item-count',
        'buffer-size': 'data-buffer-size',
      },
    };

    // RPX转换配置（符合企业级标准）
    const RPX_CONFIG = {
      designWidth: 750,
      mode: 'vw', // 使用vw模式，符合企业级标准
      converter: (rpx: number) => `${((rpx / 750) * 100).toFixed(6)}vw`,
    };

    // RPX转换函数
    const convertRpxUnits = (value: string) =>
      value.replace(/(\d+(?:\.\d+)?)rpx/g, (match, rpxValue) => {
        const rpx = parseFloat(rpxValue);
        return RPX_CONFIG.converter(rpx);
      });

    // 企业级TTML转换逻辑
    let html = ttmlContent;

    // 1. 处理动态模板和列表渲染（必须在元素映射之前进行）
    console.log('🔄 [InteractiveIframe] 处理动态模板语法');

    // 生成模拟数据用于动态模板渲染
    const generateMockData = () => ({
      listItems: [
        {
          id: 1,
          rank: '1',
          title: '中国',
          subtitle: '人口14.2亿',
          description: '世界第一人口大国',
        },
        {
          id: 2,
          rank: '2',
          title: '印度',
          subtitle: '人口14.1亿',
          description: '快速增长的人口',
        },
        {
          id: 3,
          rank: '3',
          title: '美国',
          subtitle: '人口3.3亿',
          description: '发达国家人口',
        },
        {
          id: 4,
          rank: '4',
          title: '印尼',
          subtitle: '人口2.7亿',
          description: '东南亚最大国',
        },
        {
          id: 5,
          rank: '5',
          title: '巴基斯坦',
          subtitle: '人口2.4亿',
          description: '南亚人口大国',
        },
      ],
      switchValue: true,
      sliderValue: 65,
      progressValue: 75,
      richTextNodes: [
        {
          name: 'div',
          attrs: { style: 'color: #667eea; font-weight: bold;' },
          children: [{ type: 'text', text: '这是富文本内容示例' }],
        },
      ],
    });

    const mockData = generateMockData();

    // 处理lx:for循环渲染
    html = html.replace(
      /<list-item[^>]*lx:for="([^"]*)"[^>]*>([\s\S]*?)<\/list-item>/g,
      (match, forExpression, itemTemplate) => {
        console.log('🔄 [InteractiveIframe] 处理列表循环:', forExpression);
        console.log('🔄 [InteractiveIframe] 原始项模板:', itemTemplate);

        // 解析for表达式 (例如: "item in listItems")
        const forMatch = forExpression.match(/(\w+)\s+in\s+(\w+)/);
        if (!forMatch) {
          console.log(
            '❌ [InteractiveIframe] 无法解析for表达式:',
            forExpression,
          );
          return match;
        }

        const [, itemVar, arrayVar] = forMatch;
        const dataArray = mockData[arrayVar as keyof typeof mockData] as any[];

        if (!Array.isArray(dataArray)) {
          console.log('❌ [InteractiveIframe] 数据不是数组:', arrayVar);
          return match;
        }

        console.log('📊 [InteractiveIframe] 数组数据:', dataArray);

        // 为每个数据项生成HTML
        const renderedItems = dataArray
          .map((itemData, index) => {
            console.log(
              `🔄 [InteractiveIframe] 处理第${index + 1}项:`,
              itemData,
            );

            let renderedItem = itemTemplate;

            // 替换模板变量 {{item.property}} - 使用简单字符串替换
            const templateVarRegex = /\{\{item\.(\w+)\}\}/g;
            let varMatch;
            const replacements = [];

            // 收集所有需要替换的变量
            while ((varMatch = templateVarRegex.exec(itemTemplate)) !== null) {
              const property = varMatch[1];
              const fullVarPattern = varMatch[0];
              const value = itemData[property];
              replacements.push({
                pattern: fullVarPattern,
                value: value || '',
              });
              console.log(
                `🔄 [InteractiveIframe] 发现变量 ${fullVarPattern} -> ${value}`,
              );
            }

            // 执行所有替换（使用简单字符串替换，避免正则表达式问题）
            for (const replacement of replacements) {
              const beforeReplace = renderedItem;
              renderedItem = renderedItem
                .split(replacement.pattern)
                .join(replacement.value);
              if (beforeReplace !== renderedItem) {
                console.log(
                  `✅ [InteractiveIframe] 成功替换 ${replacement.pattern} -> ${replacement.value}`,
                );
              } else {
                console.log(
                  `❌ [InteractiveIframe] 替换失败 ${replacement.pattern}`,
                );
              }
            }

            // 处理其他模板语法
            renderedItem = renderedItem
              .replace(/lx:key="[^"]*"/g, `data-key="${itemData.id || index}"`)
              .replace(
                /data-id="\{\{item\.(\w+)\}\}"/g,
                `data-id="${itemData.id || index}"`,
              )
              .replace(/\{\{index\}\}/g, index.toString())
              .replace(/\{\{item\}\}/g, JSON.stringify(itemData));

            console.log('🔄 [InteractiveIframe] 处理后项模板:', renderedItem);

            return `<div class="lynx-list-item" ${scopeAttribute} data-index="${index}">${renderedItem}</div>`;
          })
          .join('');

        console.log(
          '✅ [InteractiveIframe] 列表循环处理完成，生成项数:',
          dataArray.length,
        );
        return renderedItems;
      },
    );

    // 处理其他模板变量
    html = html
      // 处理简单变量绑定
      .replace(/\{\{(\w+)\}\}/g, (match, varName) => {
        const value = mockData[varName as keyof typeof mockData];
        if (value !== undefined) {
          console.log(
            `🔄 [InteractiveIframe] 替换变量 {{${varName}}} -> ${value}`,
          );
          return typeof value === 'object'
            ? JSON.stringify(value)
            : String(value);
        }
        return match;
      })
      // 处理条件渲染指令
      .replace(/lx:if="([^"]*)"/g, 'data-lx-if="$1"')
      .replace(/lx:for="([^"]*)"/g, 'data-lx-for="$1"')
      .replace(/lx:key="([^"]*)"/g, 'data-lx-key="$1"')
      // 处理事件绑定
      .replace(/bind(\w+)="([^"]*)"/g, 'data-bind-$1="$2"')
      .replace(/catch(\w+)="([^"]*)"/g, 'data-catch-$1="$2"');

    // 2. 处理元素映射（在动态模板处理之后）
    console.log('🔄 [InteractiveIframe] 处理元素映射');
    Object.entries(ELEMENT_MAPPING).forEach(([lynxElement, mapping]) => {
      const { tag, className, selfClosing } = mapping;
      const type = 'type' in mapping ? mapping.type : undefined;

      // 开始标签转换
      const openTagRegex = new RegExp(`<${lynxElement}([^>]*)>`, 'g');
      html = html.replace(openTagRegex, (match, attributes) => {
        let processedAttrs = attributes;

        // 处理特定元素的属性映射
        if (ATTRIBUTE_MAPPING[lynxElement]) {
          Object.entries(ATTRIBUTE_MAPPING[lynxElement]).forEach(
            ([lynxAttr, webAttr]) => {
              const attrRegex = new RegExp(`\\s${lynxAttr}="([^"]*)"`, 'g');
              processedAttrs = processedAttrs.replace(
                attrRegex,
                (attrMatch: string, value: string) => {
                  // 处理特殊属性值
                  if (lynxAttr === 'lazy-load' && value === 'true') {
                    return ' loading="lazy"';
                  }
                  return ` ${webAttr}="${value}"`;
                },
              );
            },
          );
        }

        // 添加作用域属性和Lynx类名
        const classAttr = className ? ` class="${className}"` : '';
        const typeAttr = type ? ` type="${type}"` : '';
        const scopeAttr = ` ${scopeAttribute}`;

        return `<${tag}${classAttr}${typeAttr}${scopeAttr}${processedAttrs}>`;
      });

      // 结束标签转换（非自闭合标签）
      if (!selfClosing) {
        const closeTagRegex = new RegExp(`</${lynxElement}>`, 'g');
        html = html.replace(closeTagRegex, `</${tag}>`);
      }
    });

    // 3. 处理CSS中的RPX单位
    html = convertRpxUnits(html);

    // 4. 生成企业级CSS样式（符合作用域化和RPX转换标准）
    const generateEnterpriseCSS = (scopeAttribute: string) => {
      // 先生成CSS内容，然后安全化处理
      const tempCSS = `
        /* 全局重置样式 */
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
          background: #f8fafc;
          overflow-x: hidden;
          font-size: 3.733333vw; /* 14px in 750rpx design */
          line-height: 1.6;
        }
        
        /* Lynx组件基础样式（作用域化） */
        [${scopeAttribute}] .lynx-view {
          display: flex;
          flex-direction: column;
          box-sizing: border-box;
        }
        
        [${scopeAttribute}] .lynx-scroll-view {
          width: 100%;
          overflow: auto;
          -webkit-overflow-scrolling: touch;
        }
        
        [${scopeAttribute}] .lynx-text {
          display: inline-block;
          line-height: 1.6;
          word-wrap: break-word;
        }
        
        [${scopeAttribute}] .lynx-image {
          max-width: 100%;
          height: auto;
          display: block;
        }
        
        [${scopeAttribute}] .lynx-image[data-mode="aspectFit"] {
          object-fit: contain;
        }
        
        [${scopeAttribute}] .lynx-image[data-mode="aspectFill"] {
          object-fit: cover;
        }
        
        [${scopeAttribute}] .lynx-image[data-mode="widthFix"] {
          width: 100%;
          height: auto;
        }
        
        [${scopeAttribute}] .lynx-image[loading="lazy"] {
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        [${scopeAttribute}] .lynx-image[loading="lazy"]:loaded {
          opacity: 1;
        }
        
        /* 表单组件样式 */
        [${scopeAttribute}] .lynx-input {
          width: 100%;
          padding: 2.666667vw 2.666667vw; /* 10px in 750rpx */
          border: 0.266667vw solid #e5e7eb; /* 1px */
          border-radius: 1.066667vw; /* 4px */
          font-size: 3.733333vw; /* 14px */
          line-height: 1.5;
          background: white;
          transition: border-color 0.2s ease;
        }
        
        [${scopeAttribute}] .lynx-input:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 0.8vw rgba(59, 130, 246, 0.1);
        }
        
        [${scopeAttribute}] .lynx-button {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          padding: 2.666667vw 4vw; /* 10px 15px */
          border: none;
          border-radius: 1.066667vw; /* 4px */
          font-size: 3.733333vw; /* 14px */
          font-weight: 500;
          text-align: center;
          cursor: pointer;
          transition: all 0.2s ease;
          background: #3b82f6;
          color: white;
        }
        
        [${scopeAttribute}] .lynx-button:hover {
          background: #2563eb;
          transform: translateY(-0.266667vw); /* -1px */
        }
        
        [${scopeAttribute}] .lynx-button:active {
          transform: translateY(0);
        }
        
        [${scopeAttribute}] .lynx-switch {
          appearance: none;
          width: 13.333333vw; /* 50px */
          height: 6.666667vw; /* 25px */
          background: #d1d5db;
          border-radius: 3.333333vw; /* 12.5px */
          position: relative;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }
        
        [${scopeAttribute}] .lynx-switch:checked {
          background: #3b82f6;
        }
        
        [${scopeAttribute}] .lynx-switch::before {
          content: '';
          position: absolute;
          width: 5.333333vw; /* 20px */
          height: 5.333333vw; /* 20px */
          background: white;
          border-radius: 50%;
          top: 0.666667vw; /* 2.5px */
          left: 0.666667vw; /* 2.5px */
          transition: transform 0.2s ease;
        }
        
        [${scopeAttribute}] .lynx-switch:checked::before {
          transform: translateX(6.666667vw); /* 25px */
        }
        
        [${scopeAttribute}] .lynx-slider {
          width: 100%;
          height: 1.066667vw; /* 4px */
          background: #d1d5db;
          border-radius: 0.533333vw; /* 2px */
          outline: none;
          appearance: none;
        }
        
        [${scopeAttribute}] .lynx-slider::-webkit-slider-thumb {
          appearance: none;
          width: 5.333333vw; /* 20px */
          height: 5.333333vw; /* 20px */
          background: #3b82f6;
          border-radius: 50%;
          cursor: pointer;
        }
        
        /* 高级组件样式 */
        [${scopeAttribute}] .lynx-swiper {
          position: relative;
          overflow: hidden;
          width: 100%;
          height: 50vw; /* 默认高度 */
        }
        
        [${scopeAttribute}] .lynx-swiper-item {
          width: 100%;
          height: 100%;
          flex-shrink: 0;
        }
        
        [${scopeAttribute}] .lynx-progress {
          width: 100%;
          height: 1.333333vw; /* 5px */
          background: #e5e7eb;
          border-radius: 0.666667vw; /* 2.5px */
          overflow: hidden;
        }
        
        [${scopeAttribute}] .lynx-progress::-webkit-progress-bar {
          background: #e5e7eb;
          border-radius: 0.666667vw;
        }
        
        [${scopeAttribute}] .lynx-progress::-webkit-progress-value {
          background: #3b82f6;
          border-radius: 0.666667vw;
        }
        
        [${scopeAttribute}] .lynx-list {
          width: 100%;
          overflow-y: auto;
          -webkit-overflow-scrolling: touch;
        }
        
        [${scopeAttribute}] .lynx-list-item {
          width: 100%;
          border-bottom: 0.266667vw solid #f3f4f6; /* 1px */
        }
        
        [${scopeAttribute}] .lynx-list-item:last-child {
          border-bottom: none;
        }
        
        [${scopeAttribute}] .lynx-navigator {
          display: inline-block;
          text-decoration: none;
          color: inherit;
          cursor: pointer;
        }
        
        [${scopeAttribute}] .lynx-web-view {
          width: 100%;
          height: 66.666667vw; /* 250px */
          border: none;
        }
        
        /* 自定义业务样式（兼容现有类名） */
        [${scopeAttribute}] .container {
          max-width: 100%;
          margin: 0 auto;
          background: white;
          border-radius: 3.2vw; /* 12px */
          box-shadow: 0 0.533333vw 2.133333vw rgba(0,0,0,0.1); /* 0 2px 8px */
          padding: 6.4vw; /* 24px */
        }
        
        [${scopeAttribute}] .header-section {
          text-align: center;
          margin-bottom: 8.533333vw; /* 32px */
          padding-bottom: 6.4vw; /* 24px */
          border-bottom: 0.533333vw solid #e2e8f0; /* 2px */
        }
        
        [${scopeAttribute}] .title-container {
          margin-bottom: 4.266667vw; /* 16px */
        }
        
        [${scopeAttribute}] .main-title {
          font-size: 8.533333vw; /* 32px */
          font-weight: 700;
          color: #1a202c;
          margin-bottom: 2.133333vw; /* 8px */
          display: block;
        }
        
        [${scopeAttribute}] .subtitle {
          font-size: 4.8vw; /* 18px */
          color: #64748b;
          font-weight: 500;
          display: block;
        }
        
        [${scopeAttribute}] .stats-overview {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 5.333333vw; /* 20px */
          border-radius: 2.133333vw; /* 8px */
          margin: 5.333333vw 0; /* 20px */
        }
        
        [${scopeAttribute}] .content-section {
          margin-top: 6.4vw; /* 24px */
        }
        
        [${scopeAttribute}] .country-item {
          display: flex;
          align-items: center;
          padding: 4.266667vw; /* 16px */
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          border-radius: 3.2vw; /* 12px */
          margin-bottom: 3.2vw; /* 12px */
          box-shadow: 0 0.533333vw 2.133333vw rgba(0,0,0,0.08);
          transition: all 0.3s ease;
          border-left: 1.066667vw solid transparent; /* 4px */
        }
        
        [${scopeAttribute}] .country-item:hover {
          transform: translateY(-0.533333vw); /* -2px */
          box-shadow: 0 1.066667vw 4.266667vw rgba(0,0,0,0.12);
          border-left-color: #667eea;
        }
        
        [${scopeAttribute}] .rank {
          width: 12.8vw; /* 48px */
          height: 12.8vw; /* 48px */
          background: linear-gradient(135deg, #3b82f6 0%, rgb(35, 146, 239) 100%);
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 700;
          font-size: 4.8vw; /* 18px */
          margin-right: 5.333333vw; /* 20px */
          box-shadow: 0 1.066667vw 3.2vw rgba(59, 130, 246, 0.3);
        }
        
        [${scopeAttribute}] .country-name {
          flex: 1;
          font-size: 5.333333vw; /* 20px */
          font-weight: 600;
          color: #1a202c;
          margin-right: 4.266667vw; /* 16px */
        }
        
        [${scopeAttribute}] .population {
          font-size: 4.8vw; /* 18px */
          color: #667eea;
          font-weight: 600;
          background: rgba(102, 126, 234, 0.1);
          padding: 2.133333vw 4.266667vw; /* 8px 16px */
          border-radius: 2.133333vw; /* 8px */
        }
        
        [${scopeAttribute}] .footer-section {
          margin-top: 10.666667vw; /* 40px */
          padding-top: 6.4vw; /* 24px */
          border-top: 0.533333vw solid #e2e8f0; /* 2px */
          text-align: center;
        }
        
        [${scopeAttribute}] .footer-note {
          color: #64748b;
          font-size: 3.733333vw; /* 14px */
          font-style: italic;
          display: block;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
          [${scopeAttribute}] .lynx-scroll-view {
            padding: 3.2vw; /* 12px */
          }
          
          [${scopeAttribute}] .container {
            padding: 5.333333vw; /* 20px */
          }
          
          [${scopeAttribute}] .main-title {
            font-size: 6.4vw; /* 24px */
          }
          
          [${scopeAttribute}] .subtitle {
            font-size: 4.266667vw; /* 16px */
          }
          
          [${scopeAttribute}] .country-item {
            flex-direction: column;
            text-align: center;
            padding: 4.266667vw; /* 16px */
          }
          
          [${scopeAttribute}] .rank {
            margin-bottom: 3.2vw; /* 12px */
            margin-right: 0;
          }
          
          [${scopeAttribute}] .country-name {
            margin-right: 0;
            margin-bottom: 2.133333vw; /* 8px */
          }
        }
        
        /* 动画效果 */
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(5.333333vw); /* 20px */
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        [${scopeAttribute}] .country-item {
          animation: fadeInUp 0.6s ease forwards;
        }
        
        [${scopeAttribute}] .country-item:nth-child(1) { animation-delay: 0.1s; }
        [${scopeAttribute}] .country-item:nth-child(2) { animation-delay: 0.2s; }
        [${scopeAttribute}] .country-item:nth-child(3) { animation-delay: 0.3s; }
        [${scopeAttribute}] .country-item:nth-child(4) { animation-delay: 0.4s; }
        [${scopeAttribute}] .country-item:nth-child(5) { animation-delay: 0.5s; }
      `;

      // 转换CSS中的RPX单位
      return convertRpxUnits(tempCSS);
    };

    // 🔧 修复：使用字符串拼接而不是模板字面量，避免srcDoc解析问题
    const generatedCSS = generateEnterpriseCSS(scopeAttribute);
    const fullHtml =
      '<!DOCTYPE html>' +
      '<html lang="zh-CN">' +
      '<head>' +
      '<meta charset="UTF-8">' +
      '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
      '<title>企业级TTML预览 - Lynx转Web</title>' +
      `<style>${generatedCSS}</style>` +
      '</head>' +
      '<body>' +
      `<div ${scopeAttribute}>${html}</div>` +
      '</body>' +
      '</html>';

    console.log('✅ [InteractiveIframe] TTML转换完成');
    return fullHtml;
  };

  // 转换 Lynx 内容的回调函数
  const convertLynxContent = useCallback(async () => {
    console.log('🚀 [InteractiveIframe] 开始内容转换流程');

    // 🔒 关键修复：如果已经永久失败，直接返回不再重试
    if (hasFailedPermanently) {
      console.log('🔒 [InteractiveIframe] 转换已永久失败，使用稳定错误内容');
      return;
    }

    // 检查缓存 (使用Parse5转换引擎)
    const cacheKey = `parse5-${result.id}-${result.metadata?.extractedContent?.length || 0}`;
    const cached = conversionCacheRef.current.get(cacheKey);
    if (cached) {
      console.log('🎯 [InteractiveIframe] 使用缓存结果');
      setPreviewResult(cached);
      setConversionStatus('success');
      onPreviewGenerated?.(result.id, cached);
      return;
    }

    try {
      setConversionError(null);
      setConversionStatus('detecting');

      // 获取内容
      const content = result.metadata?.extractedContent || result.content || '';
      console.log('📄 [InteractiveIframe] 提取到的内容长度:', content.length);
      console.log(
        '📄 [InteractiveIframe] 内容预览:',
        content.substring(0, 200),
      );

      // Simplified content analysis - only when debugging
      // Note: Debug mode is always enabled for troubleshooting
      console.log('🔍 [InteractiveIframe] Content analysis:', {
        source: result.metadata?.extractedContent
          ? 'extractedContent'
          : 'content',
        length: content.length,
        hasTTML: content.includes('<view') || content.includes('<text'),
        hasHTML: content.includes('<div') || content.includes('<html'),
      });

      // 🔧 修复：增强的TTSS提取函数
      const extractTTSS = (content: string): string => {
        console.log('🔍 [TTSS] 开始增强的TTSS提取，内容长度:', content.length);
        console.log('📄 [TTSS] 内容预览:', content.substring(0, 300) + '...');

        const results: Array<{ source: string; content: string }> = [];

        // 1. 提取 <ttss> 标签内容
        const ttssMatch = content.match(/<ttss[^>]*>([\s\S]*?)<\/ttss>/i);
        if (ttssMatch) {
          results.push({ source: '<ttss> 标签', content: ttssMatch[1].trim() });
          console.log(
            '✅ [TTSS] 找到 <ttss> 标签内容, 长度:',
            ttssMatch[1].trim().length,
          );
        }

        // 2. 提取 <style> 标签内容
        const styleMatch = content.match(/<style[^>]*>([\s\S]*?)<\/style>/i);
        if (styleMatch) {
          results.push({
            source: '<style> 标签',
            content: styleMatch[1].trim(),
          });
          console.log(
            '✅ [TTSS] 找到 <style> 标签内容, 长度:',
            styleMatch[1].trim().length,
          );
        }

        // 🔥 3. 关键修复：提取FILE格式中的TTSS内容
        const fileMatches = content.match(
          /<FILE\s+path="[^"]*\.ttss"[^>]*>([\s\S]*?)<\/FILE>/gi,
        );
        if (fileMatches) {
          console.log(
            '✅ [TTSS] 发现',
            fileMatches.length,
            '个FILE格式的TTSS文件',
          );
          fileMatches.forEach((match, index) => {
            const fileContent = match.match(
              /<FILE\s+path="([^"]*)"[^>]*>([\s\S]*?)<\/FILE>/i,
            );
            if (fileContent) {
              results.push({
                source: `FILE: ${fileContent[1]}`,
                content: fileContent[2].trim(),
              });
              console.log(
                `✅ [TTSS] FILE ${index + 1}: ${fileContent[1]}, 长度:`,
                fileContent[2].trim().length,
              );
            }
          });
        }

        // 4. 提取CSS代码块 (```css ... ```)
        const cssBlockMatch = content.match(/```css\s*\n([\s\S]*?)\n```/i);
        if (cssBlockMatch) {
          results.push({
            source: 'CSS代码块',
            content: cssBlockMatch[1].trim(),
          });
          console.log(
            '✅ [TTSS] 找到CSS代码块, 长度:',
            cssBlockMatch[1].trim().length,
          );
        }

        // 5. 提取STYLE注释块格式
        const styleCommentMatch = content.match(
          /\/\*\s*STYLE\s*\*\/([\s\S]*?)\/\*\s*END\s*STYLE\s*\*\//i,
        );
        if (styleCommentMatch) {
          results.push({
            source: '/* STYLE */ 注释块',
            content: styleCommentMatch[1].trim(),
          });
          console.log(
            '✅ [TTSS] 找到STYLE注释块, 长度:',
            styleCommentMatch[1].trim().length,
          );
        }

        // 6. 合并所有找到的样式内容
        if (results.length > 0) {
          const combinedCSS = results
            .map(r => `/* 来源: ${r.source} */\n${r.content}`)
            .join('\n\n');
          console.log(
            `✅ [TTSS] 总共找到 ${results.length} 个样式源，合并后长度: ${combinedCSS.length}`,
          );
          console.log(
            '📝 [TTSS] 合并后的CSS预览:',
            combinedCSS.substring(0, 200) + '...',
          );
          return combinedCSS;
        }

        console.log('⚠️ [TTSS] 未找到任何TTSS内容');
        console.log('🔍 [TTSS] 内容格式分析:');
        console.log('  - 包含<ttss>标签:', /<ttss[^>]*>/i.test(content));
        console.log('  - 包含<style>标签:', /<style[^>]*>/i.test(content));
        console.log(
          '  - 包含FILE格式:',
          /<FILE\s+path="[^"]*\.ttss"/i.test(content),
        );
        console.log('  - 包含CSS代码块:', /```css/i.test(content));
        console.log(
          '  - 包含TTML内容:',
          /<view|<text|<scroll-view/i.test(content),
        );

        return '';
      };

      const extractJS = (content: string): string => {
        // 提取JavaScript内容
        const jsMatch = content.match(/<script[^>]*>([\s\S]*?)<\/script>/i);
        if (jsMatch) {
          return jsMatch[1].trim();
        }

        // 提取PAGE或Card语法
        const pageMatch = content.match(
          /PAGE\([\s\S]*?\);?|Card\([\s\S]*?\);?/i,
        );
        if (pageMatch) {
          return pageMatch[0];
        }

        return '';
      };

      if (!content.trim()) {
        // Try fallback content sources
        const fallbackContent =
          result.response || result.data || result.text || '';
        if (
          fallbackContent &&
          typeof fallbackContent === 'string' &&
          fallbackContent.trim()
        ) {
          content = fallbackContent;
        } else {
          // Generate error placeholder instead of throwing
          content = `
<view class="error-placeholder">
  <text class="error-message">内容加载失败</text>
  <text class="error-detail">Result ID: ${result.id}</text>
  <button bindtap="handleRetry">重试</button>
</view>`;
        }
      }

      setConversionStatus('converting');

      let finalHtml = '';

      // 检查是否包含TTML内容
      if (
        content.includes('<FILE path="index.ttml">') ||
        content.includes('<scroll-view') ||
        content.includes('<view') ||
        content.includes('<text')
      ) {
        console.log('🔍 [InteractiveIframe] 检测到TTML内容');

        // 提取TTML内容
        let ttmlContent = content;

        // 如果是FILE格式，提取TTML部分
        const ttmlMatch = content.match(
          /<FILE path="index\.ttml"[^>]*>([\s\S]*?)<\/FILE>/,
        );
        if (ttmlMatch) {
          ttmlContent = ttmlMatch[1].trim();
          console.log('📦 [InteractiveIframe] 从FILE格式中提取TTML');
        }

        // 转换TTML到HTML（使用Parse5企业级转换引擎）
        console.log('🔧 [InteractiveIframe] 使用Parse5增强转换器进行转换');

        // 🚀 优先使用基于 Prompts 的智能转换器
        try {
          console.log('🧠 [InteractiveIframe] 优先使用基于 Prompts 的智能转换器');

          // 准备转换输入
          const ttssContent = extractTTSS(content);
          const jsContent = extractJS(content);

          console.log('📄 [InteractiveIframe] 转换输入:', {
            ttml: ttmlContent.length,
            ttss: ttssContent.length,
            js: jsContent.length,
          });

          // 🚀 尝试使用基于 Prompts 的智能转换器
          try {
            console.log('🧠 [InteractiveIframe] 开始智能转换...');
            finalHtml = await convertWithPromptsIntelligence(
              ttmlContent,
              ttssContent,
              jsContent,
              result.query || ''
            );
            console.log('✅ [InteractiveIframe] 智能转换成功！');
          } catch (promptsError) {
            console.warn('⚠️ [InteractiveIframe] 智能转换失败，回退到传统转换器:', promptsError);
            
            // 回退到传统的 Parse5 转换器
            console.log('🔄 [InteractiveIframe] 使用传统Parse5增强转换器');
            
            // 创建传统转换器实例
            try {
              const converter = new Parse5EnhancedConverter({
              ...DEFAULT_PARSE5_CONFIG,
              enableTreeShaking: true,
              enableStyleInlining: true,
              enableComponentScoping: true,
              generateSourceMap: false,
              enableDebugMode: true,
            });

            // 🔧 额外调试：如果TTSS为空，记录详细信息
            if (!ttssContent || ttssContent.trim() === '') {
              console.log(
                '🚨 [InteractiveIframe] ⚠️ TTSS内容为空！这是问题根源！',
              );
              console.log('🔍 [InteractiveIframe] 原始内容分析:');
              console.log('  - 内容长度:', content.length);
              console.log('  - 包含<ttss>:', content.includes('<ttss>'));
              console.log('  - 包含<style>:', content.includes('<style>'));
              console.log('  - 包含FILE.ttss:', content.includes('.ttss'));
              console.log('  - 原始内容片段:', content.substring(0, 500));
            } else {
              console.log(
                '✅ [InteractiveIframe] TTSS内容提取成功，长度:',
                ttssContent.length,
              );
              console.log(
                '🎨 [InteractiveIframe] TTSS内容预览:',
                ttssContent.substring(0, 200) + '...',
              );
            }

            // 执行传统转换
            try {
              const parse5Result = await converter.convert(
                ttmlContent,
                ttssContent,
                jsContent,
              );

              if (parse5Result.success && parse5Result.html) {
                console.log('✅ [InteractiveIframe] Parse5增强转换成功');
                finalHtml = parse5Result.html;

                // 调用预览生成回调
                if (onPreviewGenerated) {
                  onPreviewGenerated(result.id, {
                    success: true,
                    html: parse5Result.html,
                    metadata: {
                      transformTime: Date.now(),
                      engine: 'Parse5Enhanced',
                      version: '3.0.0',
                      statistics: parse5Result.statistics,
                    },
                  });
                }
              } else {
                console.error('❌ [InteractiveIframe] Parse5增强转换失败:', parse5Result.errors);
                throw new Error(parse5Result.errors.map(e => e.message).join(', '));
              }
            } catch (parse5Error) {
              console.error('💥 [InteractiveIframe] Parse5转换异常:', parse5Error);
              setConversionError(`Parse5转换失败: ${parse5Error.message}`);

              // 回退到内置转换器
              console.log('🔄 [InteractiveIframe] 回退到内置TTML转换器');
              finalHtml = convertTtmlToHtml(ttmlContent, result.id);
            }
          } catch (converterError) {
            console.log(
              '⚠️ [InteractiveIframe] Parse5转换失败，使用内置转换器',
            );
            console.error('💥 [InteractiveIframe] 转换器创建或使用失败:', converterError);
            throw new Error('Parse5转换失败');
          }
        }
      } catch (outerError) {
        console.error('💥 [InteractiveIframe] 转换过程发生未知错误:', outerError);
        setConversionError(`转换失败: ${outerError.message}`);
        finalHtml = convertTtmlToHtml(ttmlContent, result.id);
      }
    } else if (
        content.includes('<!DOCTYPE html>') ||
        content.includes('<html')
      ) {
        console.log('🔍 [InteractiveIframe] 检测到HTML内容，直接使用');
        finalHtml = content;
      } else {
        console.log('🔍 [InteractiveIframe] 未知内容类型，作为文本处理');
        finalHtml = `
          <!DOCTYPE html>
          <html lang="zh-CN">
            <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>文本预览</title>
              <style>
                body { 
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
                  padding: 20px; 
                  line-height: 1.6; 
                  background: #f8fafc;
                }
                .content {
                  background: white;
                  padding: 24px;
                  border-radius: 12px;
                  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                }
              </style>
            </head>
            <body>
              <div class="content">
                <pre style="white-space: pre-wrap; word-wrap: break-word;">${escapeHtml(content)}</pre>
              </div>
            </body>
          </html>
        `;
      }

      const conversionResult: PreviewResult = {
        success: true,
        html: finalHtml,
        metadata: {
          transformTime: Date.now(),
          componentId: result.id,
          engine: 'DirectTtml',
          version: '1.0.0',
          contentType: 'TTML',
          width,
          height,
          enableAutoScreenshot,
        },
      };

      console.log('✅ [InteractiveIframe] 转换成功');

      // 内容稳定性检查
      const newContent = finalHtml;
      const stability = contentStabilityRef.current;
      const now = Date.now();

      // 如果内容相同且在500ms内，跳过更新避免闪烁
      if (
        stability.lastContent === newContent &&
        now - stability.lastUpdate < 500
      ) {
        console.log('🔒 [InteractiveIframe] 内容稳定，跳过更新避免闪烁');
        return;
      }

      // 清除之前的稳定性超时
      if (stability.stabilityTimeout) {
        clearTimeout(stability.stabilityTimeout);
      }

      // 延迟更新确保内容稳定
      stability.stabilityTimeout = setTimeout(() => {
        setPreviewResult(conversionResult);
        setConversionStatus('success');
        setIsContentStable(true);
        setStableContent(newContent);

        stability.lastContent = newContent;
        stability.lastUpdate = now;
        stability.stabilityTimeout = null;

        console.log('🎯 [InteractiveIframe] 内容已稳定更新');
      }, 200); // 200ms延迟确保稳定

      // 缓存结果
      conversionCacheRef.current.set(cacheKey, conversionResult);

      // 通知父组件
      onPreviewGenerated?.(result.id, conversionResult);
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '转换异常';

      // 错误稳定性检查：避免频繁的状态变化
      const now = Date.now();
      const stability = errorStabilityRef.current;

      if (
        stability.lastError === errorMsg &&
        now - stability.timestamp < 3000
      ) {
        // 3秒内相同错误，增加计数但不更新UI
        stability.count++;
        console.warn(
          `⚠️ [InteractiveIframe] 相同错误重复 ${stability.count} 次，跳过UI更新`,
        );
        return;
      }

      // 更新错误稳定性记录
      stability.lastError = errorMsg;
      stability.count = 1;
      stability.timestamp = now;

      console.error('💥 [InteractiveIframe] 转换异常:', errorMsg);

      // 🔒 关键修复：设置永久失败状态，生成一次性稳定错误内容
      const errorPageContent = generateErrorPage(errorMsg, result);

      setConversionError(errorMsg);
      setConversionStatus('error');
      setHasFailedPermanently(true);
      setPermanentErrorContent(errorPageContent);
      setIsContentStable(true);
      setStableContent(errorPageContent);

      // 创建降级预览结果
      const fallbackResult: PreviewResult = {
        success: true,
        html: errorPageContent,
        message: errorMsg,
      };
      setPreviewResult(fallbackResult);

      console.log('🔒 [InteractiveIframe] 已设置永久失败状态，不再重试转换');
    }
  }, [
    result,
    width,
    height,
    enableAutoScreenshot,
    onPreviewGenerated,
    hasFailedPermanently,
  ]);

  // 当内容发生变化时进行转换 - 但如果已永久失败则不再转换
  useEffect(() => {
    if (isVisible && result.status === 'success' && !hasFailedPermanently) {
      convertLynxContent();
    }
  }, [convertLynxContent, isVisible, result.status, hasFailedPermanently]);

  // 根据聚焦状态和可视性决定是否显示iframe - 增加稳定性检查
  useEffect(() => {
    if (focused || (isVisible && result.status === 'success')) {
      // 延迟显示iframe，避免频繁切换
      const timer = setTimeout(() => {
        setShowIframe(true);
      }, 100);

      return () => clearTimeout(timer);
    } else if (!focused && !isVisible) {
      // 失焦且不可见时，延迟隐藏iframe以减少闪烁
      const timer = setTimeout(() => {
        setShowIframe(false);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [focused, isVisible, result.status]);

  // 处理iframe加载完成 - 增加稳定性延迟
  const handleIframeLoad = () => {
    // 延迟设置加载完成，确保内容真正稳定
    setTimeout(() => {
      setIsLoading(false);
    }, 150);
  };

  // 处理缩略图错误
  const handleThumbnailError = () => {
    setThumbnailError(true);
    onThumbnailError?.();
  };

  // HTML转义函数
  // 安全的HTML转义函数
  const escapeHtml = (text: string) => {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  };

  // 安全的HTML属性转义函数
  const escapeHtmlAttr = (text: string) =>
    text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\$/g, '&#x24;') // 转义 $ 符号避免模板字面量问题
      .replace(/`/g, '&#x60;'); // 转义反引号避免模板字面量问题
  // 生成错误页面
  const generateErrorPage = (errorMessage: string, result: ProcessResult) => {
    const safeErrorMessage = escapeHtml(errorMessage);
    const safeQuery = escapeHtml(result.query || '');
    const safeStatus = escapeHtml(result.status || '');

    return `
      <!DOCTYPE html>
      <html lang="zh-CN">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>转换错误</title>
          <style>
            html, body {
              margin: 0;
              padding: 0;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
              background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
              color: #475569;
              height: 100vh;
              display: flex;
              align-items: center;
              justify-content: center;
              overflow: hidden;
            }
            .error-container {
              text-align: center;
              padding: 40px 20px;
              background: rgba(255, 255, 255, 0.9);
              border-radius: 16px;
              backdrop-filter: blur(20px);
              border: 1px solid rgba(203, 213, 225, 0.5);
              box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
              max-width: 90%;
              animation: fadeIn 0.5s ease-in-out;
            }
            .error-icon {
              font-size: 64px;
              margin-bottom: 20px;
              animation: bounce 2s infinite;
            }
            .error-title {
              font-size: 24px;
              font-weight: 700;
              margin-bottom: 16px;
              color: #334155;
            }
            .error-message {
              font-size: 16px;
              line-height: 1.6;
              margin-bottom: 20px;
              color: #64748b;
              padding: 16px;
              background: rgba(248, 250, 252, 0.8);
              border-radius: 8px;
              border-left: 4px solid #f59e0b;
              word-wrap: break-word;
              overflow-wrap: break-word;
            }
            .query-info {
              font-size: 14px;
              color: #64748b;
              margin-top: 16px;
              padding: 12px;
              background: rgba(248, 250, 252, 0.6);
              border-radius: 6px;
              border: 1px solid rgba(203, 213, 225, 0.5);
            }
            .query-label {
              font-weight: 600;
              margin-bottom: 8px;
              color: #475569;
            }
            .query-text {
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 12px;
              line-height: 1.4;
              word-wrap: break-word;
              overflow-wrap: break-word;
              max-height: 100px;
              overflow-y: auto;
            }
            .error-details {
              margin-top: 20px;
              padding: 12px;
              background: rgba(248, 250, 252, 0.8);
              border-radius: 6px;
              font-size: 12px;
              color: #64748b;
              text-align: left;
            }
            .retry-hint {
              margin-top: 16px;
              font-size: 14px;
              color: #64748b;
              padding: 8px 16px;
              background: rgba(249, 250, 251, 0.9);
              border-radius: 20px;
              display: inline-block;
              border: 1px solid rgba(203, 213, 225, 0.5);
            }
            @keyframes fadeIn {
              from { opacity: 0; transform: translateY(20px); }
              to { opacity: 1; transform: translateY(0); }
            }
            @keyframes bounce {
              0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
              40% { transform: translateY(-10px); }
              60% { transform: translateY(-5px); }
            }
          </style>
        </head>
        <body>
          <div class="error-container">
            <div class="error-icon">🔧</div>
            <div class="error-title">转换处理中</div>
            <div class="error-message">
              ${safeErrorMessage}
            </div>
            <div class="query-info">
              <div class="query-label">原始查询:</div>
              <div class="query-text">${safeQuery}</div>
            </div>
            <div class="error-details">
              <div><strong>处理类型:</strong> 代码转换服务</div>
              <div><strong>当前状态:</strong> ${safeStatus}</div>
              <div><strong>时间:</strong> ${new Date().toLocaleString()}</div>
            </div>
            <div class="retry-hint">
              💡 正在使用备用方案显示内容
            </div>
          </div>
        </body>
      </html>
    `;
  };

  // 生成HTML内容用于iframe - 使用增强的滚动和样式支持，并确保内容稳定
  const getIframeContent = useCallback(() => {
    // 🔒 最高优先级：如果已永久失败，使用稳定的错误内容
    if (hasFailedPermanently && permanentErrorContent) {
      return permanentErrorContent;
    }

    // 优先使用稳定的内容
    if (isContentStable && stableContent) {
      return stableContent;
    }

    // 如果有转换错误，显示错误页面（但这不应该再被调用）
    if (conversionError) {
      return generateErrorPage(conversionError, result);
    }

    // 优先使用转换后的预览结果
    if (previewResult?.html) {
      const { html } = previewResult;
      // 如果已经是完整的HTML文档，直接使用
      if (html.includes('<!DOCTYPE html>') || html.includes('<html')) {
        return html;
      }

      // 否则包装成完整的HTML文档
      return `
        <!DOCTYPE html>
        <html lang="zh-CN">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
            <title>Lynx 预览</title>
            <style>
              /* 增强的滚动样式 */
              html {
                font-size: 16px;
                scroll-behavior: smooth;
              }
              
              html, body {
                margin: 0;
                padding: 12px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
                line-height: 1.6;
                color: #333;
                background-color: #fff;
                
                /* 关键：确保可以滚动 */
                overflow-y: auto !important;
                overflow-x: hidden;
                height: auto;
                min-height: 100vh;
                
                /* 移动端滚动优化 */
                -webkit-overflow-scrolling: touch;
                overscroll-behavior: contain;
              }
              
              * {
                box-sizing: border-box;
              }
              
              /* 自定义滚动条样式 */
              ::-webkit-scrollbar {
                width: 8px;
              }
              
              ::-webkit-scrollbar-track {
                background: rgba(0,0,0,0.05);
                border-radius: 4px;
              }
              
              ::-webkit-scrollbar-thumb {
                background: rgba(0,0,0,0.2);
                border-radius: 4px;
                transition: background 0.2s ease;
              }
              
              ::-webkit-scrollbar-thumb:hover {
                background: rgba(0,0,0,0.4);
              }
              
              /* Lynx 特定样式优化 */
              [class*="view"], [class*="container"] {
                position: relative;
              }
              
              /* 确保所有内容都可见和可滚动 */
              .scroll-view, .list-view {
                overflow-y: auto !important;
                max-height: none !important;
              }
              
              /* 图片适配 */
              img {
                max-width: 100%;
                height: auto;
                display: block;
              }
              
              /* 防止内容溢出 */
              pre, code {
                white-space: pre-wrap;
                word-wrap: break-word;
                overflow-wrap: break-word;
              }
            </style>
          </head>
          <body>
            <div class="lynx-preview-container">
              ${html}
              
              <!-- 添加演示滚动内容确保可以滚动 -->
              <div style="margin-top: 20px; padding: 16px; background: rgba(255,255,255,0.1); border-radius: 8px; border-left: 3px solid rgba(255,255,255,0.5);">
                <h4 style="margin-top: 0; color: rgba(255,255,255,0.9);">📱 触控板滚动测试</h4>
                <p style="margin: 8px 0; opacity: 0.8; line-height: 1.5;">
                  如果您能看到这个测试区域，请使用触控板双指滚动来验证滚动功能是否正常工作。
                </p>
                ${Array.from(
                  { length: 5 },
                  (_, i) => `
                  <div style="margin: 8px 0; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 4px;">
                    <strong style="color: rgba(255,255,255,0.9);">测试块 ${i + 1}</strong><br>
                    <span style="opacity: 0.7; font-size: 14px;">
                      这是额外的滚动测试内容，用于确保iframe内部有足够的高度来产生滚动条。
                    </span>
                  </div>
                `,
                ).join('')}
              </div>
            </div>
            
            <script>
              // 确保 iframe 可以滚动
              document.addEventListener('DOMContentLoaded', function() {
                console.log('📱 [Lynx Preview] DOM 加载完成');
                
                // 移除可能阻止滚动的样式
                document.body.style.overflow = 'auto';
                document.documentElement.style.overflow = 'auto';
                
                // 确保最小高度以产生滚动
                const container = document.querySelector('.lynx-preview-container');
                if (container) {
                  // 强制设置足够的高度来产生滚动（iframe高度是500px）
                  const minHeight = Math.max(800, window.innerHeight * 1.5);
                  container.style.minHeight = minHeight + 'px';
                  console.log('📐 [Lynx Preview] 设置容器最小高度:', minHeight);
                }
                
                // 通知父窗口内容已就绪
                if (window.parent) {
                  window.parent.postMessage({ type: 'LYNX_PREVIEW_READY' }, '*');
                }
              });
              
              // 处理触摸事件以确保滚动
              document.addEventListener('touchstart', function(e) {
                // 允许触摸事件传播
              }, { passive: true });
              
              document.addEventListener('touchmove', function(e) {
                // 允许触摸滚动
              }, { passive: true });
            </script>
          </body>
        </html>
      `;
    }

    // 降级：使用原始提取内容
    if (result.metadata?.extractedContent) {
      const content = result.metadata.extractedContent;
      return content.includes('<html')
        ? content
        : `
        <!DOCTYPE html>
        <html lang="zh-CN">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>原始内容预览</title>
            <style>
              html, body {
                margin: 0;
                padding: 12px;
                font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                overflow-y: auto !important;
                overflow-x: hidden;
                min-height: 100vh;
                height: auto;
                -webkit-overflow-scrolling: touch;
                background: #f9f9f9;
              }
              .content-wrapper {
                background: white;
                padding: 16px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                white-space: pre-wrap;
                word-wrap: break-word;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 12px;
                line-height: 1.5;
              }
            </style>
          </head>
          <body>
            <div class="content-wrapper">${content}</div>
          </body>
        </html>
      `;
    }

    // 最后的占位符
    return `
      <!DOCTYPE html>
      <html lang="zh-CN">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>状态预览</title>
          <style>
            html, body {
              margin: 0;
              padding: 20px;
              font-family: -apple-system, BlinkMacSystemFont, sans-serif;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              min-height: 200vh;
              height: auto;
              overflow-y: auto !important;
              overflow-x: hidden;
              -webkit-overflow-scrolling: touch;
            }
            .status-card {
              text-align: center;
              padding: 24px;
              background: rgba(255,255,255,0.15);
              border-radius: 12px;
              backdrop-filter: blur(20px);
              margin-bottom: 20px;
              border: 1px solid rgba(255,255,255,0.2);
            }
            .status-icon {
              font-size: 48px;
              margin-bottom: 16px;
            }
            .status-text {
              font-size: 20px;
              font-weight: 600;
              margin-bottom: 8px;
            }
            .query-text {
              font-size: 14px;
              opacity: 0.9;
              margin-top: 12px;
            }
            .scroll-demo {
              margin-top: 30px;
              padding: 20px;
              background: rgba(255,255,255,0.1);
              border-radius: 8px;
            }
            .demo-item {
              padding: 12px;
              margin: 12px 0;
              background: rgba(255,255,255,0.1);
              border-radius: 6px;
              border-left: 3px solid rgba(255,255,255,0.5);
            }
          </style>
        </head>
        <body>
          <div class="status-card">
            <div class="status-icon">
              ${
                result.status === 'success'
                  ? '✅'
                  : result.status === 'processing'
                    ? '⏳'
                    : result.status === 'error'
                      ? '❌'
                      : '⏰'
              }
            </div>
            <div class="status-text">
              ${
                result.status === 'success'
                  ? '处理完成'
                  : result.status === 'processing'
                    ? '正在处理查询...'
                    : result.status === 'error'
                      ? '处理失败'
                      : '等待处理'
              }
            </div>
            <div class="query-text">${result.query}</div>
            ${result.error ? `<div style="color: #ffcdd2; margin-top: 12px; font-size: 13px; padding: 8px; background: rgba(255,0,0,0.1); border-radius: 4px;">${result.error}</div>` : ''}
          </div>
          
          <div class="scroll-demo">
            <h3 style="margin-top: 0;">📱 ${focused ? '聚焦模式 - ' : ''}内容预览</h3>
            <p>${focused ? '当前卡片已聚焦，支持完整交互' : '使用触控板双指滚动浏览内容'}</p>
            
            ${Array.from(
              { length: focused ? 15 : 8 },
              (_, i) => `
              <div class="demo-item">
                <strong>内容块 ${i + 1}</strong><br>
                这是可滚动的预览内容，用于验证触控板滚动功能正常工作。
                ${focused ? '聚焦模式下显示更多详细内容。' : ''}
              </div>
            `,
            ).join('')}
            
            ${
              focused
                ? '<div style="margin-top: 24px; padding: 16px; background: rgba(255,255,255,0.2); border-radius: 8px;">' +
                  '<h4 style="margin-top: 0;">🎮 交互说明</h4>' +
                  '<ul style="text-align: left; padding-left: 20px; margin: 0;">' +
                  '<li>使用触控板双指滚动</li>' +
                  '<li>支持鼠标滚轮滚动</li>' +
                  '<li>内容完全可交互</li>' +
                  '<li>点击外部区域失焦</li>' +
                  '</ul>' +
                  '</div>'
                : '<div style="margin-top: 16px; padding: 12px; background: rgba(255,255,255,0.15); border-radius: 6px; text-align: center;">' +
                  '<p style="margin: 0; font-size: 14px; opacity: 0.9;">' +
                  '💡 使用触控板双指滚动查看更多内容<br>' +
                  '点击卡片进入聚焦模式获得完整交互' +
                  '</p>' +
                  '</div>'
            }
          </div>
        </body>
      </html>
    `;
  }, [
    previewResult,
    result,
    focused,
    conversionError,
    isContentStable,
    stableContent,
    hasFailedPermanently,
    permanentErrorContent,
  ]);

  // 渲染缩略图
  const renderThumbnail = () => {
    if (thumbnailError) {
      // 显示缩略图失败标识
      return (
        <div
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: colors.gray[100],
            position: 'relative',
          }}
        >
          <div
            style={{
              width: '24px',
              height: '24px',
              backgroundColor: '#FFC107',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '12px',
            }}
          >
            📷
          </div>
          <div
            style={{
              position: 'absolute',
              bottom: '8px',
              left: '8px',
              right: '8px',
              fontSize: '10px',
              color: colors.gray[600],
              textAlign: 'center',
            }}
          >
            {result.query}
          </div>
        </div>
      );
    }

    if (thumbnailUrl) {
      return (
        <img
          src={thumbnailUrl}
          alt={`${result.query} 预览`}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
          onError={handleThumbnailError}
          onLoad={() => setIsLoading(false)}
        />
      );
    }

    return null;
  };

  // 渲染占位符
  const renderPlaceholder = () => (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.gray[50],
        color: colors.gray[500],
        fontSize: '12px',
        textAlign: 'center',
        padding: '20px',
      }}
    >
      <div
        style={{
          width: '48px',
          height: '48px',
          borderRadius: '50%',
          backgroundColor: colors.gray[200],
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: '12px',
          fontSize: '20px',
        }}
      >
        {result.status === 'success'
          ? '✅'
          : result.status === 'processing'
            ? '⏳'
            : result.status === 'error'
              ? '❌'
              : '⏰'}
      </div>
      <div style={{ fontWeight: 500, marginBottom: '4px' }}>
        {result.status === 'success'
          ? '点击查看预览'
          : result.status === 'processing'
            ? '正在处理查询...'
            : result.status === 'error'
              ? '处理失败'
              : '等待处理'}
      </div>
      <div style={{ fontSize: '10px', opacity: 0.7 }}>{result.query}</div>
    </div>
  );

  return (
    <div
      ref={containerRef}
      style={{
        width,
        height,
        position: 'relative',
        // 始终允许溢出，让iframe内容处理滚动
        overflow: 'visible',
        backgroundColor: colors.gray[50],
        borderRadius: '8px',
      }}
    >
      {/* 加载状态 */}
      {isLoading && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255,255,255,0.8)',
            zIndex: 10,
          }}
        >
          <div
            style={{
              width: '24px',
              height: '24px',
              border: '2px solid #e0e0e0',
              borderTop: '2px solid #1976d2',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
            }}
          />
        </div>
      )}

      {/* 转换状态指示器 */}
      {conversionStatus !== 'idle' && conversionStatus !== 'success' && (
        <div
          style={{
            position: 'absolute',
            top: '4px',
            right: '4px',
            background:
              conversionStatus === 'error'
                ? 'rgba(220, 53, 69, 0.9)'
                : 'rgba(13, 110, 253, 0.9)',
            color: 'white',
            fontSize: '8px',
            padding: '3px 6px',
            borderRadius: '3px',
            zIndex: 20,
            display: 'flex',
            alignItems: 'center',
            gap: '3px',
            maxWidth: '120px',
            overflow: 'hidden',
          }}
          title={`转换状态: ${
            conversionStatus === 'detecting'
              ? '检测中'
              : conversionStatus === 'converting'
                ? '转换中'
                : conversionStatus === 'capturing'
                  ? '截图中'
                  : conversionStatus === 'error'
                    ? '转换失败'
                    : conversionStatus
          }`}
        >
          {conversionStatus === 'detecting' && '🔍'}
          {conversionStatus === 'converting' && '⚙️'}
          {conversionStatus === 'capturing' && '📸'}
          {conversionStatus === 'error' && '❌'}
          <span style={{ fontSize: '7px' }}>
            {conversionStatus === 'detecting'
              ? '检测'
              : conversionStatus === 'converting'
                ? '转换'
                : conversionStatus === 'capturing'
                  ? '截图'
                  : conversionStatus === 'error'
                    ? '失败'
                    : ''}
          </span>
        </div>
      )}

      {/* 转换错误详情 */}
      {conversionError && (
        <div
          style={{
            position: 'absolute',
            top: '4px',
            left: '4px',
            background: 'rgba(255, 193, 7, 0.95)',
            color: '#333',
            fontSize: '7px',
            padding: '2px 4px',
            borderRadius: '2px',
            zIndex: 20,
            maxWidth: '100px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
          title={`错误详情: ${conversionError}`}
        >
          ⚠️ 降级显示
        </div>
      )}

      {/* Lynx 功能指示器 */}
      {previewResult?.success && !conversionError && (
        <div
          style={{
            position: 'absolute',
            top: '4px',
            left: '4px',
            background: 'rgba(34, 197, 94, 0.9)',
            color: 'white',
            fontSize: '7px',
            padding: '2px 4px',
            borderRadius: '2px',
            zIndex: 20,
            display: 'flex',
            alignItems: 'center',
            gap: '2px',
          }}
          title="Lynx 转换成功"
        >
          🎯 Lynx
        </div>
      )}

      {/* 内容区域 */}
      {showIframe && result.status === 'success' ? (
        <iframe
          ref={iframeRef}
          srcDoc={getIframeContent()}
          key={`iframe-${result.id}-${isContentStable ? 'stable' : 'loading'}`} // 稳定内容后不再重新加载
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            pointerEvents: 'auto', // 始终允许指针事件，包括触控板滚动
            display: 'block',
            background: 'white',
            touchAction: 'pan-y pinch-zoom', // 明确支持触控板手势
          }}
          onLoad={() => {
            handleIframeLoad();

            // 设置iframe消息监听
            const handleMessage = (event: MessageEvent) => {
              if (event.source === iframeRef.current?.contentWindow) {
                if (event.data?.type === 'LYNX_PREVIEW_READY') {
                  console.log('📱 [InteractiveIframe] Lynx 预览就绪');
                }
              }
            };

            window.addEventListener('message', handleMessage);

            return () => {
              window.removeEventListener('message', handleMessage);
            };
          }}
          // 增强sandbox权限，确保支持所有交互和滚动
          sandbox="allow-scripts allow-same-origin allow-forms allow-pointer-lock allow-popups allow-modals allow-top-navigation-by-user-activation"
          title={`${result.query} 预览`}
          // 增强的事件处理
          onMouseEnter={() => {
            if (focused && iframeRef.current) {
              // 确保iframe获得焦点以支持滚动
              setTimeout(() => {
                try {
                  iframeRef.current?.focus();
                  // 发送消息通知iframe准备接收滚动事件
                  iframeRef.current?.contentWindow?.postMessage(
                    { type: 'FOCUS_RECEIVED' },
                    '*',
                  );
                } catch (e) {
                  console.warn('iframe focus failed:', e);
                }
              }, 0);
            }
          }}
          // 点击时也要确保聚焦和滚动激活
          onClick={() => {
            if (focused && iframeRef.current) {
              try {
                iframeRef.current.focus();
                // 激活iframe滚动
                iframeRef.current.contentWindow?.postMessage(
                  { type: 'ACTIVATE_SCROLL' },
                  '*',
                );
              } catch (e) {
                console.warn('iframe activation failed:', e);
              }
            }
          }}
        />
      ) : thumbnailUrl ? (
        renderThumbnail()
      ) : (
        renderPlaceholder()
      )}

      {/* 聚焦状态指示器 */}
      {focused && (
        <div
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            padding: '4px 8px',
            backgroundColor: 'rgba(59, 130, 246, 0.9)',
            color: 'white',
            borderRadius: '4px',
            fontSize: '10px',
            fontWeight: 500,
            zIndex: 20,
          }}
        >
          已聚焦
        </div>
      )}

      {/* 点击遮罩 - 未聚焦时显示，但不阻止滚动事件 */}
      {!focused && showIframe && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0,0,0,0)',
            cursor: 'pointer',
            zIndex: 5,
            pointerEvents: 'none', // 不阻止滚动事件，让事件穿透到iframe
          }}
        />
      )}

      {/* 增强的交互提示 */}
      {focused && showIframe && (
        <div
          style={{
            position: 'absolute',
            bottom: '8px',
            left: '8px',
            padding: '3px 8px',
            backgroundColor: 'rgba(59, 130, 246, 0.9)',
            color: 'white',
            borderRadius: '4px',
            fontSize: '8px',
            fontWeight: 600,
            zIndex: 20,
            pointerEvents: 'none',
            display: 'flex',
            alignItems: 'center',
            gap: '3px',
            boxShadow: '0 2px 6px rgba(0,0,0,0.15)',
          }}
        >
          <span>📱</span>
          <span>可滚动交互</span>
        </div>
      )}

      {/* 预览质量指示器 */}
      {showIframe && previewResult && (
        <div
          style={{
            position: 'absolute',
            bottom: '8px',
            right: '8px',
            padding: '2px 6px',
            backgroundColor: previewResult.screenshot
              ? 'rgba(34, 197, 94, 0.9)'
              : 'rgba(168, 85, 247, 0.9)',
            color: 'white',
            borderRadius: '4px',
            fontSize: '7px',
            fontWeight: 500,
            zIndex: 20,
            pointerEvents: 'none',
          }}
          title={previewResult.screenshot ? '包含截图预览' : 'HTML 预览'}
        >
          {previewResult.screenshot ? '📸' : '🌐'}
        </div>
      )}

      {/* CSS动画和样式 */}
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
          }
          
          .converting-indicator {
            animation: pulse 1.5s ease-in-out infinite;
          }
          
          /* 确保iframe在聚焦时具有正确的z-index */
          .interactive-iframe {
            position: relative;
            z-index: ${focused ? '10' : '1'};
          }
        `}
      </style>
    </div>
  );
};

export default InteractiveIframe;
