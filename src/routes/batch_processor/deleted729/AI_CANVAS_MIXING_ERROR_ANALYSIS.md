# 🚨 AI Canvas 混用错误分析报告

## 📋 问题概述

AI 经常在同一个 Card 组件中混用原生 Canvas 和 LightChart 的初始化，这是一个**严重的架构错误**，必须完全禁止。

## 🔍 错误模式分析

### 典型的 AI 混用错误
```javascript
// ❌ 绝对禁止：AI 经常生成这种混用代码
Card({
  // 原生 Canvas 初始化 - 技术栈 A
  setupCanvas() {
    try {
      const canvas = lynx.createCanvasNG();
      
      canvas.addEventListener("resize", ({ width, height }) => {
        canvas.width = width * SystemInfo.pixelRatio;
        canvas.height = height * SystemInfo.pixelRatio;
        const ctx = canvas.getContext('2d');
        ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
        this.canvas = canvas;
        this.ctx = ctx;
        this.canvasWidth = width;
        this.canvasHeight = height;
        this.drawProcessFlow();
      });
      
      canvas.attachToCanvasView("processCanvas");
    } catch (error) {
      console.error("Canvas setup failed:", error);
    }
  },

  // LightChart 初始化 - 技术栈 B (禁止与上面混用!)
  initChart(e) {
    if (typeof lynx === 'undefined' || !lynx.krypton) return;
    if (typeof SystemInfo === 'undefined') return;
    
    const { canvasName, width, height } = e.detail;
    this.chart = new LynxChart({ canvasName, width, height });
    setTimeout(() => this.updateChart(), 100);
  }
});
```

## 🚨 为什么绝对禁止混用

### 1. 技术栈冲突
- **原生 Canvas**: 使用 `lynx.createCanvasNG()` 和 `canvas.getContext('2d')`
- **LightChart**: 使用 `new LynxChart()` 和 `chart.setOption()`
- **冲突**: 两种技术栈有不同的运行时依赖和初始化流程

### 2. 环境依赖不同
- **原生 Canvas**: 依赖 `lynx.createCanvasNG` 和 `SystemInfo.pixelRatio`
- **LightChart**: 依赖 `lynx.krypton.createCanvas` 和 `SystemInfo.pixelRatio`
- **问题**: 不同的 lynx API 可能在不同环境中有不同的可用性

### 3. 生命周期管理混乱
- **原生 Canvas**: 需要手动管理 resize 事件、context 状态
- **LightChart**: 自动管理图表生命周期、响应式更新
- **冲突**: 两种不同的生命周期管理模式会相互干扰

### 4. 内存泄漏风险
- **原生 Canvas**: 需要手动清理事件监听器、context 引用
- **LightChart**: 通过 `chart.destroy()` 自动清理
- **问题**: 混用导致清理逻辑不完整，容易造成内存泄漏

## ✅ 正确的技术栈选择

### 选择 A: 全部使用原生 Canvas
```javascript
Card({
  canvas: null,
  ctx: null,
  canvasWidth: 0,
  canvasHeight: 0,

  created() {
    this.setupCanvas = this.setupCanvas.bind(this);
    this.drawProcessFlow = this.drawProcessFlow.bind(this);
  },

  onLoad() {
    this.setupCanvas();
  },

  setupCanvas() {
    try {
      const canvas = lynx.createCanvasNG();
      
      canvas.addEventListener("resize", ({ width, height }) => {
        canvas.width = width * SystemInfo.pixelRatio;
        canvas.height = height * SystemInfo.pixelRatio;
        const ctx = canvas.getContext('2d');
        ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
        this.canvas = canvas;
        this.ctx = ctx;
        this.canvasWidth = width;
        this.canvasHeight = height;
        this.drawProcessFlow();
      });
      
      canvas.attachToCanvasView("processCanvas");
    } catch (error) {
      console.error("Canvas setup failed:", error);
    }
  },

  drawProcessFlow() {
    if (!this.ctx) return;
    
    // 使用原生 Canvas API 绘制
    this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
    this.ctx.fillStyle = '#3498db';
    this.ctx.fillRect(50, 50, 200, 100);
    // ... 更多原生绘制逻辑
  },

  onUnload() {
    // 清理原生 Canvas 资源
    if (this.canvas) {
      this.canvas.removeEventListener("resize");
      this.canvas = null;
      this.ctx = null;
    }
  }
});
```

### 选择 B: 全部使用 LightChart
```javascript
Card({
  processChart: null,
  flowChart: null,

  created() {
    this.initProcessChart = this.initProcessChart.bind(this);
    this.initFlowChart = this.initFlowChart.bind(this);
    this.updateProcessChart = this.updateProcessChart.bind(this);
    this.updateFlowChart = this.updateFlowChart.bind(this);
  },

  initProcessChart(e) {
    if (typeof lynx === 'undefined' || !lynx.krypton) return;
    if (typeof SystemInfo === 'undefined') return;
    
    const { canvasName, width, height } = e.detail;
    this.processChart = new LynxChart({ canvasName, width, height });
    setTimeout(() => this.updateProcessChart(), 100);
  },

  initFlowChart(e) {
    if (typeof lynx === 'undefined' || !lynx.krypton) return;
    if (typeof SystemInfo === 'undefined') return;
    
    const { canvasName, width, height } = e.detail;
    this.flowChart = new LynxChart({ canvasName, width, height });
    setTimeout(() => this.updateFlowChart(), 100);
  },

  updateProcessChart() {
    if (!this.processChart) return;
    
    const option = {
      // LightChart 配置
      series: [{ type: 'bar', data: [...] }]
    };
    
    try {
      this.processChart.setOption(option);
    } catch (error) {
      console.error('Process chart update failed:', error);
    }
  },

  updateFlowChart() {
    if (!this.flowChart) return;
    
    const option = {
      // LightChart 配置
      series: [{ type: 'line', data: [...] }]
    };
    
    try {
      this.flowChart.setOption(option);
    } catch (error) {
      console.error('Flow chart update failed:', error);
    }
  },

  onUnload() {
    // 清理 LightChart 资源
    if (this.processChart) {
      this.processChart.destroy();
      this.processChart = null;
    }
    if (this.flowChart) {
      this.flowChart.destroy();
      this.flowChart = null;
    }
  }
});
```

## 🔥 AI 混用检测规则

### 关键词检测
如果代码中同时出现以下关键词组合，立即报错并要求重构：

1. **API 混用检测**:
   - `lynx.createCanvasNG` AND `new LynxChart`
   - `canvas.getContext` AND `chart.setOption`

2. **方法名混用检测**:
   - `setupCanvas` AND `initChart`
   - `drawContent` AND `updateChart`

3. **生命周期混用检测**:
   - `attachToCanvasView` AND `LynxChart`
   - `canvas.addEventListener` AND `chart.destroy`

### 自动修复建议
当检测到混用时，AI 应该：

1. **停止生成混用代码**
2. **询问用户技术栈偏好**:
   - "您希望使用原生 Canvas 还是 LightChart？"
   - "检测到混用，请选择统一的技术栈"

3. **提供重构建议**:
   - 如果选择原生 Canvas：删除所有 LightChart 相关代码
   - 如果选择 LightChart：删除所有原生 Canvas 相关代码

## 📊 最佳实践总结

### 技术栈选择原则

1. **简单图表**: 优先选择 LightChart
   - 标准的柱状图、饼图、折线图
   - 需要交互功能（tooltip、legend）
   - 数据驱动的可视化

2. **复杂绘制**: 选择原生 Canvas
   - 自定义图形绘制
   - 复杂的动画效果
   - 特殊的交互需求

3. **性能考虑**: 
   - 大数据量：原生 Canvas
   - 多图表组合：LightChart
   - 实时更新：根据具体需求选择

### 强制隔离规则

1. **一个 Card 一种技术**: 绝不混用
2. **完整的生命周期管理**: 初始化和清理必须配套
3. **统一的错误处理**: 使用对应技术栈的错误处理模式
4. **清晰的代码结构**: 避免技术栈相关的代码混杂

## 🎯 结论

**绝对禁止** AI 在同一个组件中混用原生 Canvas 和 LightChart。这不是建议，而是**强制要求**。

**检测到混用时必须**:
1. 立即停止代码生成
2. 要求选择统一的技术栈
3. 重构代码以消除混用

这个规则已经添加到 `LightChartPromptLoader.ts` 的 R63 规则中，确保 AI 能够避免这种致命的架构错误。
