/**
 * 数据源标签演示组件
 * 
 * 🎯 用于测试和展示数据源标签的各种状态和样式
 */

import React from 'react';
import { DataSourceLabel, DataSourceStats } from './DataSourceLabel';

export const DataSourceLabelDemo: React.FC = () => {
  // 模拟统计数据
  const mockStats = {
    internal: 15,
    ai: 8,
    fallback: 3,
  };

  return (
    <div style={{ padding: '20px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <h1 style={{ marginBottom: '30px', color: '#333' }}>数据源标签组件演示</h1>
      
      {/* 基础标签展示 */}
      <section style={{ marginBottom: '40px', backgroundColor: 'white', padding: '20px', borderRadius: '8px' }}>
        <h2 style={{ marginBottom: '20px', color: '#666' }}>基础标签 - 标准模式</h2>
        <div style={{ display: 'flex', gap: '16px', alignItems: 'center', flexWrap: 'wrap' }}>
          <DataSourceLabel
            dataSource="internal"
            internalDataSummary="基于抖音内部底稿数据生成，包含权威信息和参考资料"
            showDetails={true}
            compact={false}
          />
          <DataSourceLabel
            dataSource="ai"
            showDetails={true}
            compact={false}
          />
          <DataSourceLabel
            dataSource="fallback"
            showDetails={true}
            compact={false}
          />
        </div>
      </section>

      {/* 紧凑模式标签展示 */}
      <section style={{ marginBottom: '40px', backgroundColor: 'white', padding: '20px', borderRadius: '8px' }}>
        <h2 style={{ marginBottom: '20px', color: '#666' }}>紧凑标签 - 列表模式</h2>
        <div style={{ display: 'flex', gap: '12px', alignItems: 'center', flexWrap: 'wrap' }}>
          <DataSourceLabel
            dataSource="internal"
            internalDataSummary="底稿数据摘要信息"
            showDetails={true}
            compact={true}
          />
          <DataSourceLabel
            dataSource="ai"
            showDetails={true}
            compact={true}
          />
          <DataSourceLabel
            dataSource="fallback"
            showDetails={true}
            compact={true}
          />
        </div>
      </section>

      {/* 模拟列表项布局 */}
      <section style={{ marginBottom: '40px', backgroundColor: 'white', padding: '20px', borderRadius: '8px' }}>
        <h2 style={{ marginBottom: '20px', color: '#666' }}>列表项布局测试</h2>
        
        {/* 模拟结果列表项 */}
        <div style={{ border: '1px solid #e5e5e5', borderRadius: '8px', overflow: 'hidden' }}>
          {[
            { query: '九九乘法表', dataSource: 'internal' as const, time: 111.02 },
            { query: '中国历史朝代主要事件', dataSource: 'ai' as const, time: 137.98 },
            { query: '三角函数基础知识', dataSource: 'fallback' as const, time: 89.45 },
          ].map((item, index) => (
            <div
              key={index}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                padding: '12px 16px',
                borderBottom: index < 2 ? '1px solid #e5e5e5' : 'none',
                minHeight: '40px',
              }}
            >
              {/* 左侧内容 */}
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1 }}>
                {/* 状态图标 */}
                <div
                  style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: '6px',
                    backgroundColor: '#52c41a',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '14px',
                  }}
                >
                  ✓
                </div>
                
                {/* 查询标题 - 垂直居中 */}
                <div style={{ flex: 1, display: 'flex', alignItems: 'center' }}>
                  <h4 style={{ 
                    margin: 0, 
                    fontSize: '14px', 
                    fontWeight: 500, 
                    color: '#333',
                    lineHeight: 1,
                  }}>
                    {item.query}
                  </h4>
                </div>
              </div>

              {/* 右侧标签区域 - 垂直居中对齐 */}
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {/* 处理时间 */}
                <span
                  style={{
                    fontSize: '11px',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    backgroundColor: '#f5f5f5',
                    color: '#666',
                    fontFamily: 'monospace',
                    height: '24px',
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  {item.time}s
                </span>
                
                {/* 数据源标签 */}
                <DataSourceLabel
                  dataSource={item.dataSource}
                  internalDataSummary={item.dataSource === 'internal' ? '底稿数据摘要' : undefined}
                  compact={true}
                  showDetails={true}
                />
                
                {/* 预览按钮 */}
                <button
                  style={{
                    padding: '4px 12px',
                    borderRadius: '6px',
                    border: 'none',
                    backgroundColor: '#1890ff',
                    color: 'white',
                    fontSize: '11px',
                    cursor: 'pointer',
                    height: '24px',
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  打开预览
                </button>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* 统计组件展示 */}
      <section style={{ marginBottom: '40px', backgroundColor: 'white', padding: '20px', borderRadius: '8px' }}>
        <h2 style={{ marginBottom: '20px', color: '#666' }}>数据源统计</h2>
        
        <div style={{ marginBottom: '16px' }}>
          <h3 style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>标准模式</h3>
          <DataSourceStats
            stats={mockStats}
            total={26}
            compact={false}
          />
        </div>
        
        <div>
          <h3 style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>紧凑模式</h3>
          <DataSourceStats
            stats={mockStats}
            total={26}
            compact={true}
          />
        </div>
      </section>

      {/* 无交互模式 */}
      <section style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px' }}>
        <h2 style={{ marginBottom: '20px', color: '#666' }}>无交互模式</h2>
        <div style={{ display: 'flex', gap: '12px', alignItems: 'center', flexWrap: 'wrap' }}>
          <DataSourceLabel
            dataSource="internal"
            showDetails={false}
            compact={true}
          />
          <DataSourceLabel
            dataSource="ai"
            showDetails={false}
            compact={true}
          />
          <DataSourceLabel
            dataSource="fallback"
            showDetails={false}
            compact={true}
          />
        </div>
      </section>
    </div>
  );
};
