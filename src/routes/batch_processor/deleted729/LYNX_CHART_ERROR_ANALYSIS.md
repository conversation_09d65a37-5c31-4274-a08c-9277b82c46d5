# LynxChart "lynx.createCanvasContext is not a function" 错误深度分析报告

## 📋 问题概述

即使使用 `new LynxChart()` 构造函数，仍然出现"lynx.createCanvasContext is not a function"错误。本报告深入分析了错误的根本原因和解决方案。

## 🔍 源码分析发现

### 1. LynxChart内部依赖分析

通过分析 `@byted/lynx-lightcharts/src/chart.ts`，发现关键问题：

```typescript
// 第68行：LynxChart构造函数内部调用
super(lynx.krypton.createCanvas(option.canvasName), {
  dpr: SystemInfo.pixelRatio,
  width: option.width,
  height: option.height,
});

// 第15行：模块级别的Canvas创建器设置
setCanvasCreator((width, height) => lynx.createOffscreenCanvas(width, height));
```

**关键发现**：LynxChart构造函数内部使用了 `lynx.krypton.createCanvas()` 而不是 `lynx.createCanvasContext()`

### 2. Lynx运行时API依赖

chart.ts文件声明了完整的lynx对象结构：

```typescript
declare let lynx: {
  createImage: Function;
  createCanvas: Function;
  requestAnimationFrame: Function;
  cancelAnimationFrame: Function;
  krypton: {
    CanvasElement: new (name: string, legacy: boolean) => HTMLCanvasElement;
    createImage: Function;
    createCanvas: Function;  // 这是实际使用的API
  };
  createSelectorQuery: Function;
  createOffscreenCanvas: (width: number, heght: number) => HTMLCanvasElement;
};
```

## 🚨 错误根本原因分析

### 1. 环境兼容性问题

LynxChart依赖的并非 `lynx.createCanvasContext`，而是：
- `lynx.krypton.createCanvas()`（主要Canvas创建）
- `lynx.createOffscreenCanvas()`（离屏Canvas创建）
- `lynx.krypton.createImage()`（图片资源创建）
- `SystemInfo.pixelRatio`（系统信息）

### 2. 版本依赖冲突

项目中的版本信息：
- `@byted/lynx-lightcharts`: v0.9.4
- `@byted/lightcharts`: v2.5.0
- lynx-lightcharts依赖的lightcharts版本: 2.5.0-beta.28

**可能的冲突**：lynx-lightcharts期望的lightcharts版本是beta版，而项目使用的是正式版。

### 3. 运行环境缺失

LynxChart需要在真正的Lynx运行时环境中工作，包括：
- 完整的lynx全局对象
- SystemInfo全局对象
- Krypton渲染引擎支持

## 💡 解决方案

### 方案1: 环境检查和Mock实现

```javascript
// 在使用LynxChart之前检查环境
function checkLynxEnvironment() {
  const requiredAPIs = [
    'lynx.krypton.createCanvas',
    'lynx.createOffscreenCanvas',
    'lynx.krypton.createImage',
    'lynx.requestAnimationFrame',
    'lynx.cancelAnimationFrame'
  ];
  
  const missing = requiredAPIs.filter(api => {
    try {
      return !eval(api);
    } catch (e) {
      return true;
    }
  });
  
  if (missing.length > 0) {
    throw new Error(`Missing Lynx APIs: ${missing.join(', ')}`);
  }
}

// Mock实现（仅用于开发调试）
function mockLynxEnvironment() {
  if (typeof lynx === 'undefined') {
    global.lynx = {
      krypton: {
        createCanvas: (canvasName) => {
          const canvas = document.createElement('canvas');
          canvas.id = canvasName;
          return canvas;
        },
        createImage: (src) => {
          const img = new Image();
          img.src = src;
          return img;
        }
      },
      createOffscreenCanvas: (width, height) => {
        if (typeof OffscreenCanvas !== 'undefined') {
          return new OffscreenCanvas(width, height);
        }
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        return canvas;
      },
      requestAnimationFrame: requestAnimationFrame,
      cancelAnimationFrame: cancelAnimationFrame
    };
  }
  
  if (typeof SystemInfo === 'undefined') {
    global.SystemInfo = {
      pixelRatio: window.devicePixelRatio || 1
    };
  }
}
```

### 方案2: 版本同步修复

```bash
# 确保版本一致性
pnpm install @byted/lightcharts@2.5.0-beta.28
# 或者
pnpm install @byted/lynx-lightcharts@latest
```

### 方案3: 条件性使用

```javascript
import LynxChart from '@byted/lynx-lightcharts/src/chart';

function createChart(config) {
  try {
    // 检查环境
    if (typeof lynx === 'undefined' || !lynx.krypton) {
      throw new Error('Lynx environment not available');
    }
    
    return new LynxChart(config);
  } catch (error) {
    console.warn('LynxChart not available, falling back:', error.message);
    
    // 回退到普通的lightcharts或其他解决方案
    return createFallbackChart(config);
  }
}

function createFallbackChart(config) {
  // 使用原生HTML5 Canvas或其他图表库
  const canvas = document.createElement('canvas');
  canvas.width = config.width;
  canvas.height = config.height;
  return canvas;
}
```

## 🔧 调试步骤

### 1. 环境检测
```javascript
console.log('Lynx Environment Check:');
console.log('lynx defined:', typeof lynx !== 'undefined');
console.log('lynx.krypton defined:', typeof lynx?.krypton !== 'undefined');
console.log('SystemInfo defined:', typeof SystemInfo !== 'undefined');
console.log('lynx.krypton.createCanvas:', typeof lynx?.krypton?.createCanvas);
console.log('lynx.createOffscreenCanvas:', typeof lynx?.createOffscreenCanvas);
```

### 2. 依赖版本检查
```bash
pnpm list @byted/lynx-lightcharts
pnpm list @byted/lightcharts
```

### 3. 运行时检测
```javascript
try {
  const testConfig = { canvasName: 'test', width: 100, height: 100 };
  const chart = new LynxChart(testConfig);
  console.log('LynxChart创建成功');
  chart.destroy();
} catch (error) {
  console.error('LynxChart创建失败:', error.message);
  console.error('Error stack:', error.stack);
}
```

## 📊 兼容性矩阵

| 环境 | lynx.krypton | SystemInfo | createOffscreenCanvas | 兼容性 |
|------|--------------|------------|---------------------|--------|
| 真实Lynx环境 | ✅ | ✅ | ✅ | 完全兼容 |
| Web开发环境 | ❌ | ❌ | Partial | 需要Mock |
| Node.js环境 | ❌ | ❌ | ❌ | 完全不兼容 |
| 浏览器环境 | ❌ | Partial | Partial | 需要适配 |

## 🎯 最佳实践建议

### 1. 渐进式增强

```javascript
class ChartManager {
  constructor() {
    this.chartEngine = this.detectBestEngine();
  }
  
  detectBestEngine() {
    if (this.isLynxEnvironment()) {
      return 'lynx-lightcharts';
    } else if (this.isBrowserEnvironment()) {
      return 'web-lightcharts';
    }
    return 'fallback';
  }
  
  createChart(config) {
    switch (this.chartEngine) {
      case 'lynx-lightcharts':
        return new LynxChart(config);
      case 'web-lightcharts':
        return new WebChart(config);
      default:
        return new FallbackChart(config);
    }
  }
}
```

### 2. 错误处理策略

```javascript
async function initChart(config) {
  const fallbacks = [
    () => new LynxChart(config),
    () => new WebLightChart(config),
    () => new CanvasChart(config),
    () => new SVGChart(config)
  ];
  
  for (const createChart of fallbacks) {
    try {
      const chart = createChart();
      console.log(`Using chart engine: ${chart.constructor.name}`);
      return chart;
    } catch (error) {
      console.warn(`Chart creation failed:`, error.message);
    }
  }
  
  throw new Error('No chart engine available');
}
```

## 🔮 结论

错误的根本原因不是 `lynx.createCanvasContext` 未定义，而是：

1. **运行环境不匹配**：LynxChart需要完整的Lynx运行时环境
2. **依赖版本不一致**：beta版本与正式版本之间的API差异
3. **缺少必要的全局对象**：`lynx.krypton`, `SystemInfo` 等

解决方案应该集中在环境适配、版本同步和渐进式增强三个方面，而不是简单地修复单个API调用。