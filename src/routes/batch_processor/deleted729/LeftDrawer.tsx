import React, { useEffect, useRef, useState } from 'react';
import Icon, { IconType } from './Icon';

/**
 * 抽屉操作按钮配置
 */
interface HeaderAction {
  /** 按钮点击事件处理 */
  onClick: () => void;
  /** 按钮图标类型 */
  icon?: IconType;
  /** 按钮标签文本 */
  label?: string;
  /** 按钮CSS类名 */
  className?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 按钮提示文本 */
  tooltip?: string;
}

interface LeftDrawerProps {
  /** 是否显示抽屉 */
  isOpen: boolean;
  /** 关闭抽屉回调 */
  onClose: () => void;
  /** 抽屉标题 */
  title: string | React.ReactNode;
  /** 抽屉内容 */
  children: React.ReactNode;
  /** 抽屉宽度 */
  width?: string;
  /** 是否显示关闭按钮 */
  showCloseButton?: boolean;
  /** 标题栏额外内容 */
  headerExtra?: React.ReactNode;
  /** 头部操作按钮列表 */
  headerActions?: HeaderAction[];
  /** 抽屉主题 */
  theme?: 'settings' | 'history' | 'prompt' | 'default' | 'gold' | 'blue';
  /** 是否阻止点击遮罩层关闭 */
  preventBackdropClose?: boolean;
  /** 是否自动聚焦第一个可聚焦元素 */
  autoFocus?: boolean;
  /** 底部内容 */
  footer?: React.ReactNode;
  /** 打开时回调 */
  onOpen?: () => void;
  /** 自定义类名 */
  className?: string;
  /** 是否显示帮助按钮 */
  showHelpButton?: boolean;
  /** 帮助按钮点击回调 */
  onHelpClick?: () => void;
}

/**
 * 左滑抽屉组件
 * 从左侧滑入的抽屉，带有遮罩层和动画效果
 * 支持自定义主题、头部操作按钮和灵活的内容布局
 */
const LeftDrawer: React.FC<LeftDrawerProps> = ({
  isOpen,
  onClose,
  title,
  children,
  width = '400px',
  showCloseButton = true,
  headerExtra,
  headerActions = [],
  theme = 'default',
  preventBackdropClose = false,
  autoFocus = true,
  footer,
  onOpen,
  className = '',
  showHelpButton = false,
  onHelpClick,
}) => {
  const drawerRef = useRef<HTMLDivElement>(null);
  const [isClosing, setIsClosing] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  
  const initialFocusRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          setIsClosing(false);
          

          const timer = setTimeout(() => {
            if (onOpen) {
              onOpen();
            }

            if (autoFocus && drawerRef.current) {
              const focusableElements = drawerRef.current.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
              );
              if (focusableElements.length > 0) {
                (focusableElements[0] as HTMLElement).focus();
                initialFocusRef.current = focusableElements[0] as HTMLElement;
              }
            }
          }, 400);

          return () => clearTimeout(timer);
        });
      });
    } else if (shouldRender) {
      setIsClosing(true);
      const timer = setTimeout(() => {
        setShouldRender(false);
        setIsClosing(false);
      }, 300);

      if (initialFocusRef.current) {
        initialFocusRef.current.focus();
        initialFocusRef.current = null;
      }

      return () => clearTimeout(timer);
    }
  }, [isOpen, onOpen, autoFocus]);

  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen && !preventBackdropClose) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose, preventBackdropClose]);

  const handleOverlayClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget && !preventBackdropClose) {
      onClose();
    }
  };

  if (!shouldRender) {
    return null;
  }

  const getDrawerIconType = (): IconType => {
    switch (theme) {
      case 'history':
        return 'history';
      case 'settings':
        return 'settings';
      case 'prompt':
        return 'edit';
      case 'gold':
        return 'grid';
      case 'blue':
        return 'info';
      default:
        return 'grid';
    }
  };

  const getAnimationClass = () => {
    if (isClosing) {
      return 'slide-out-left';
    }
    if (isOpen && !isClosing) {
      return 'slide-in-left';
    }
    return '';
  };

    const drawerContainerClass = [
    'drawer-container-left',
    getAnimationClass(),
    `theme-${theme}`,
    className,
  ].filter(Boolean).join(' ');

  const drawerOverlayClass = [
    'drawer-overlay',
    isOpen && !isClosing ? 'open' : '',
  ].filter(Boolean).join(' ');

  return (
    <div
      className={drawerOverlayClass}
      onClick={handleOverlayClick}
      aria-hidden={!isOpen ? 'true' : 'false'}
    >
      <div
        ref={drawerRef}
        className={drawerContainerClass}
        style={{ width }}
        role="dialog"
        aria-modal="true"
        aria-labelledby="drawer-title"
      >
        <div className={`drawer-header theme-header-${theme}`}>
          <div className="drawer-title-container">
            <Icon type={getDrawerIconType()} className="drawer-title-icon" />
                        <h2 id="drawer-title" className="drawer-title">
              {title}
            </h2>
          </div>
          <div className="drawer-header-actions">
            {headerExtra}
            {headerActions.map((action, index) => (
                            <button
                key={index}
                onClick={action.onClick}
                className={`header-action-btn ${action.className || ''}`}
                disabled={action.disabled}
                title={action.tooltip}
              >
                {action.icon && <Icon type={action.icon} />}
                {action.label && <span>{action.label}</span>}
              </button>
            ))}
            {showHelpButton && onHelpClick && (
                            <button
                onClick={onHelpClick}
                className="header-action-btn help-btn"
                aria-label="Help"
              >
                <Icon type="help" />
              </button>
            )}
            {showCloseButton && (
                            <button
                onClick={onClose}
                className="header-action-btn close-btn"
                aria-label="Close"
              >
                <Icon type="close" />
              </button>
            )}
          </div>
        </div>
        <div className="drawer-content">{children}</div>
        {footer && <div className="drawer-footer">{footer}</div>}
      </div>
    </div>
  );
};

export default LeftDrawer;