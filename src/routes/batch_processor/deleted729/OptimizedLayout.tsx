import React, { useState, useCallback } from 'react';
import Icon from './Icon';

/**
 * 🎯 优化版批处理器布局
 * 
 * 核心改进：
 * 1. 响应式三段式布局：紧凑输入区 + 主结果区 + 侧边工具栏
 * 2. 移除冗余的状态页面和重复信息
 * 3. 智能空间利用，减少固定高度约束
 * 4. 简化交互流程，提升操作效率
 */

interface OptimizedLayoutProps {
  // 输入相关
  inputText: string;
  queries: string[];
  onInputChange: (text: string) => void;
  onStartProcess: () => void;
  
  // 处理状态
  isProcessing: boolean;
  progress: {
    total: number;
    completed: number;
    failed: number;
    percentage: number;
  };
  
  // 结果数据
  results: any[];
  
  // 工具操作
  onRetryFailed?: () => void;
  onOpenSettings?: () => void;
  onOpenHistory?: () => void;
}

export const OptimizedLayout: React.FC<OptimizedLayoutProps> = ({
  inputText,
  queries,
  onInputChange,
  onStartProcess,
  isProcessing,
  progress,
  results,
  onRetryFailed,
  onOpenSettings,
  onOpenHistory,
}) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [activeView, setActiveView] = useState<'list' | 'grid'>('grid');

  // 智能状态检测
  const hasQueries = queries.length > 0;
  const hasResults = results.length > 0;
  const hasErrors = progress.failed > 0;
  const isComplete = !isProcessing && hasResults;

  // 样本数据插入
  const handlePasteSample = useCallback(() => {
    const samples = [
      '9乘法表的规律和记忆方法',
      '中国历史朝代更替的主要原因',
      '世界人口最多的十个国家',
      '如何写出吸引人的社交媒体文案',
      '小学数学教学中的趣味方法',
    ];
    onInputChange(samples.join('\n'));
  }, [onInputChange]);

  return (
    <div className="optimized-layout">
      {/* 顶部紧凑输入区 */}
      <header className="layout-header">
        <div className="input-container">
          {/* 左侧：输入控制 */}
          <div className="input-section">
            <div className="input-meta">
              <h2 className="app-title">
                <Icon type="batch" size="sm" />
                LYNX 批量生成器
              </h2>
              {hasQueries && (
                <div className="query-summary">
                  <span className="query-count">{queries.length} 个查询</span>
                  {isProcessing && (
                    <span className="processing-indicator">
                      <Icon type="processing" size="xs" animate />
                      处理中
                    </span>
                  )}
                </div>
              )}
            </div>

            <div className="input-controls">
              <textarea
                className={`main-input ${isProcessing ? 'disabled' : ''}`}
                placeholder="输入查询列表，每行一个或逗号、分号分隔..."
                value={inputText}
                onChange={(e) => onInputChange(e.target.value)}
                disabled={isProcessing}
                rows={3}
              />
              
              <div className="input-actions">
                <button 
                  className="btn-action btn-sample"
                  onClick={handlePasteSample}
                  disabled={isProcessing}
                >
                  <Icon type="lightning" size="xs" />
                  示例
                </button>
                
                <button 
                  className="btn-action btn-clear"
                  onClick={() => onInputChange('')}
                  disabled={!inputText || isProcessing}
                >
                  <Icon type="trash" size="xs" />
                  清除
                </button>
              </div>
            </div>
          </div>

          {/* 右侧：主操作按钮 */}
          <div className="primary-action">
            <button
              className={`btn-primary-large ${isProcessing ? 'processing' : ''}`}
              onClick={onStartProcess}
              disabled={!hasQueries || isProcessing}
            >
              <Icon 
                type={isProcessing ? 'processing' : 'rocket'} 
                size="md" 
                animate={isProcessing}
              />
              <div className="btn-content">
                <span className="btn-title">
                  {isProcessing ? '处理中' : '开始处理'}
                </span>
                {hasQueries && (
                  <span className="btn-subtitle">
                    {isProcessing 
                      ? `${progress.completed}/${progress.total} (${progress.percentage.toFixed(0)}%)`
                      : `${queries.length} 个查询`
                    }
                  </span>
                )}
              </div>
            </button>
          </div>
        </div>

        {/* 进度指示器 */}
        {isProcessing && (
          <div className="progress-strip">
            <div 
              className="progress-bar"
              style={{ width: `${progress.percentage}%` }}
            />
            <div className="progress-stats">
              <span>完成: {progress.completed}</span>
              {progress.failed > 0 && <span>失败: {progress.failed}</span>}
              <span>剩余: {progress.total - progress.completed - progress.failed}</span>
            </div>
          </div>
        )}
      </header>

      {/* 主内容区域 */}
      <main className="layout-main">
        {!hasQueries && !hasResults && (
          // 极简欢迎提示
          <div className="welcome-simple">
            <div className="welcome-icon">
              <Icon type="batch" size="xl" />
            </div>
            <h3>批量处理中心</h3>
            <p>输入多个查询，一键生成所有结果</p>
          </div>
        )}

        {hasResults && (
          <div className="results-area">
            {/* 结果工具栏 */}
            <div className="results-toolbar">
              <div className="view-controls">
                <button 
                  className={`view-btn ${activeView === 'grid' ? 'active' : ''}`}
                  onClick={() => setActiveView('grid')}
                >
                  <Icon type="grid" size="sm" />
                  卡片
                </button>
                <button 
                  className={`view-btn ${activeView === 'list' ? 'active' : ''}`}
                  onClick={() => setActiveView('list')}
                >
                  <Icon type="list" size="sm" />
                  列表
                </button>
              </div>

              <div className="results-stats">
                <span className="stat stat-success">成功: {progress.completed}</span>
                {progress.failed > 0 && (
                  <span className="stat stat-error">失败: {progress.failed}</span>
                )}
              </div>

              <div className="results-actions">
                {progress.completed > 0 && (
                  <button className="btn-action btn-success">
                    <Icon type="external" size="xs" />
                    打开成功结果
                  </button>
                )}
                {hasErrors && onRetryFailed && (
                  <button className="btn-action btn-warning" onClick={onRetryFailed}>
                    <Icon type="refresh" size="xs" />
                    重试失败项
                  </button>
                )}
              </div>
            </div>

            {/* 结果内容 */}
            <div className={`results-content view-${activeView}`}>
              {/* 这里会渲染实际的结果组件 */}
              <div className="results-placeholder">
                <p>结果展示区域 ({activeView} 视图)</p>
                <p>共 {results.length} 条结果</p>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* 侧边工具栏 */}
      <aside className={`layout-sidebar ${sidebarCollapsed ? 'collapsed' : ''}`}>
        <div className="sidebar-header">
          <h4>工具</h4>
          <button 
            className="collapse-btn"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          >
            <Icon type={sidebarCollapsed ? 'chevron-left' : 'chevron-right'} size="sm" />
          </button>
        </div>

        {!sidebarCollapsed && (
          <div className="sidebar-content">
            {/* 状态概览 */}
            {(hasQueries || hasResults) && (
              <div className="status-overview">
                <h5>状态概览</h5>
                <div className="status-grid">
                  <div className="status-item">
                    <span className="status-value">{queries.length}</span>
                    <span className="status-label">总数</span>
                  </div>
                  <div className="status-item status-success">
                    <span className="status-value">{progress.completed}</span>
                    <span className="status-label">完成</span>
                  </div>
                  {progress.failed > 0 && (
                    <div className="status-item status-error">
                      <span className="status-value">{progress.failed}</span>
                      <span className="status-label">失败</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 快捷工具 */}
            <div className="quick-tools">
              <h5>快捷工具</h5>
              <div className="tool-buttons">
                <button className="tool-btn" onClick={onOpenHistory}>
                  <Icon type="history" size="sm" />
                  历史记录
                </button>
                <button className="tool-btn" onClick={onOpenSettings}>
                  <Icon type="settings" size="sm" />
                  系统设置
                </button>
              </div>
            </div>

            {/* 简化日志 */}
            {isComplete && (
              <div className="completion-summary">
                <h5>处理完成</h5>
                <div className="summary-stats">
                  <p>成功率: {Math.round((progress.completed / progress.total) * 100)}%</p>
                  <p>耗时: 估算中...</p>
                </div>
              </div>
            )}
          </div>
        )}
      </aside>
    </div>
  );
};

export default OptimizedLayout;