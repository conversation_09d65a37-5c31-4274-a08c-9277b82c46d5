# 抖音内部底稿数据模式 - 开发者指南

## 🎯 快速理解

**底稿数据模式**是一个数据源切换功能，允许系统在处理用户查询时选择使用：
- **抖音内部底稿数据**：权威、准确的内部数据源
- **直接AI生成**：传统的AI直接生成模式

## 🔧 核心文件说明

### 1. InternalDataService.ts
```typescript
/**
 * 🗂️ 底稿数据接口服务
 * 
 * 职责：封装抖音内部底稿数据接口调用
 * 接口：http://9gzj7t9k.fn.bytedance.net/api/search/stream
 * 特性：5分钟缓存、30秒超时、完善错误处理
 */
```

**关键方法**：
- `fetchInternalData(query, summaryOnly)` - 获取底稿数据
- `getCacheStats()` - 获取缓存统计
- `clearCache()` - 清空缓存

### 2. DataProcessingService.ts
```typescript
/**
 * ⚙️ 数据处理服务
 * 
 * 职责：处理底稿数据与AI prompt的结合
 * 核心：确保AI严格使用底稿数据
 * 特性：模式切换、数据验证、优雅回退
 */
```

**关键方法**：
- `processQuery(query, useInternalData, onProgress)` - 处理查询
- `formatInternalDataForDisplay(data)` - 格式化显示数据

### 3. ModeToggle.tsx
```typescript
/**
 * 🔄 模式切换组件
 * 
 * 职责：提供用户界面的模式切换功能
 * 位置：输入区域上方
 * 特性：状态指示、加载进度、错误处理
 */
```

## 📊 数据结构详解

### 底稿数据接口返回格式
```json
{
  "answer": "核心答案内容，包含HTML标记和🔶n🔷引用标记",
  "pv": 4351,
  "reasoning": "推理过程（可选）",
  "reference": "详细参考资料，包含来源链接和日期"
}
```

### 字段说明
| 字段 | 类型 | 说明 | 处理方式 |
|------|------|------|----------|
| `answer` | string | 核心答案，包含HTML标记 | 严格保持，不允许大幅修改 |
| `pv` | number | 页面浏览量，表示热度 | 完全保持，用于显示数据权威性 |
| `reasoning` | string | 推理过程，可能为空 | 可选展示，增强透明度 |
| `reference` | string | 参考资料和来源信息 | 保持来源链接，确保可追溯性 |

## 🔄 工作流程图

```
用户输入查询
       ↓
   模式判断开关
       ↓
┌─────────────────┐    ┌─────────────────┐
│  底稿数据模式    │    │   直接AI模式    │
│  (开启状态)     │    │   (关闭状态)    │
└─────────────────┘    └─────────────────┘
       ↓                       ↓
   获取底稿数据              直接AI处理
       ↓                       ↓
   验证数据有效性            返回AI结果
       ↓
   构建严格prompt
       ↓
   AI处理(严格使用底稿数据)
       ↓
   返回权威结果
```

## 💻 代码使用示例

### 1. 基础使用
```typescript
import { DataProcessingService } from './services/DataProcessingService';

// 使用底稿数据模式
const result = await DataProcessingService.processQuery(
  '九九乘法表',
  true, // 启用底稿数据模式
  (progress) => {
    console.log(`处理进度: ${Math.round(progress * 100)}%`);
  }
);

if (result.source === 'internal') {
  console.log('✅ 使用了底稿数据');
  console.log('📊 数据摘要:', result.internalDataSummary);
  console.log('🔗 数据上下文:', result.context);
} else {
  console.log('⚡ 使用了直接AI模式');
}
```

### 2. 在React组件中使用
```typescript
import React, { useState } from 'react';
import { ModeToggle } from './components/ModeToggle';
import { DataProcessingService } from './services/DataProcessingService';

const MyComponent: React.FC = () => {
  const [useInternalData, setUseInternalData] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleQuery = async (query: string) => {
    setIsLoading(true);
    try {
      const result = await DataProcessingService.processQuery(
        query,
        useInternalData,
        (progress) => console.log(`进度: ${progress * 100}%`)
      );
      
      // 处理结果...
      console.log('查询结果:', result);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      {/* 模式切换组件 */}
      <ModeToggle
        useInternalData={useInternalData}
        onToggle={setUseInternalData}
        loading={isLoading}
        showDescription={true}
      />
      
      {/* 其他UI组件... */}
    </div>
  );
};
```

### 3. 批处理服务集成
```typescript
import { EnhancedBatchProcessorService } from './services/EnhancedBatchProcessorService';

const batchService = new EnhancedBatchProcessorService();

// 启用底稿数据模式
batchService.setInternalDataMode(true);

// 检查当前模式
const isEnabled = batchService.getInternalDataMode();
console.log('底稿数据模式状态:', isEnabled ? '已启用' : '已关闭');
```

## 🚨 重要注意事项

### 1. 严格数据使用原则
```typescript
// ✅ 允许的操作
- 调整UI结构和布局
- 优化字体大小和颜色
- 改善移动端显示效果
- 添加适当的图标和分隔线

// ❌ 禁止的操作
- 修改核心答案内容
- 删除参考资料来源
- 更改数据热度(pv值)
- 大幅改动原始信息
```

### 2. 错误处理策略
```typescript
// 网络错误 -> 显示重试选项
// 超时错误 -> 自动回退到直接模式
// 数据无效 -> 记录日志并回退
// 接口异常 -> 显示错误信息并提供回退
```

### 3. 性能优化建议
```typescript
// 缓存策略
- 5分钟缓存有效期
- 基于query和参数生成缓存键
- 自动清理过期缓存

// 请求优化
- 30秒请求超时
- 避免重复请求
- 支持请求取消
```

## 🧪 测试和调试

### 1. 接口测试
```bash
# 使用curl测试底稿数据接口
curl "https://9gzj7t9k.fn.bytedance.net/api/search/stream?query=九九乘法表&summary_only=1"

# 使用提供的测试脚本
./src/routes/batch_processor/scripts/testInternalDataAPI.sh
```

### 2. 功能测试
```typescript
// 使用演示组件测试完整功能
import { InternalDataDemo } from './components/InternalDataDemo';

// 在开发环境中渲染
<InternalDataDemo />
```

### 3. 单元测试
```bash
# 运行相关测试
npm test InternalDataService
npm test DataProcessingService
npm test ModeToggle
```

## 📈 监控和维护

### 1. 关键指标
- **接口成功率**：底稿数据获取成功的比例
- **缓存命中率**：缓存使用效率
- **模式切换频率**：用户使用偏好
- **错误发生率**：系统稳定性指标

### 2. 日志查看
```typescript
// 在浏览器控制台查看相关日志
console.log('[InternalDataService] 底稿数据获取成功');
console.log('[DataProcessingService] 查询处理完成');
console.log('[ModeToggle] 模式切换');
```

### 3. 缓存管理
```typescript
import { InternalDataService } from './services/InternalDataService';

// 查看缓存统计
const stats = InternalDataService.getCacheStats();
console.log('缓存统计:', stats);

// 清空缓存
InternalDataService.clearCache();
```

## 🔮 扩展和定制

### 1. 自定义配置
```typescript
import { DataProcessingService } from './services/DataProcessingService';

// 修改处理配置
DataProcessingService.setConfig({
  strictMode: true,
  allowDataModification: true,
  maxDataSize: 200 * 1024, // 200KB
  timeoutMs: 15000, // 15秒
});
```

### 2. 添加新的数据源
```typescript
// 可以参考InternalDataService的实现
// 创建新的数据源服务类
class NewDataSourceService {
  static async fetchData(query: string) {
    // 实现新的数据获取逻辑
  }
}
```

### 3. 自定义错误处理
```typescript
// 扩展错误处理逻辑
class CustomErrorHandler {
  static handleError(error: InternalDataError, context: any) {
    // 自定义错误处理逻辑
  }
}
```

## 📚 相关文档

- [详细技术指南](./docs/INTERNAL_DATA_MODE_GUIDE.md)
- [实现总结](./docs/IMPLEMENTATION_SUMMARY.md)
- [使用指南](./docs/INTERNAL_DATA_INTEGRATION_GUIDE.md)

## 🆘 常见问题

### Q: 底稿数据获取失败怎么办？
A: 系统会自动回退到直接AI模式，不影响用户使用。

### Q: 如何确保数据的准确性？
A: 通过严格的prompt指令确保AI严格使用底稿数据。

### Q: 缓存多久会过期？
A: 默认5分钟，可以通过配置调整。

### Q: 如何监控系统性能？
A: 查看浏览器控制台日志和缓存统计信息。

---

**维护团队**：前端开发团队  
**最后更新**：2025年7月28日  
**版本**：v1.0.0
