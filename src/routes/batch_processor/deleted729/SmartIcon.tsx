import React, { useContext, useRef, useEffect, useState } from 'react';
import Icon, { IconType } from './Icon';
import SemiIcon, { SemiIconType, GoldIcon, BlueIcon } from './SemiIcon';

// 图标类型映射 - 从原有图标类型映射到Semi图标类型
const iconMapping: Partial<Record<IconType, SemiIconType>> = {
  'lightning': 'lightning',
  'play': 'play',
  'stop': 'stop',
  'check': 'check',
  'error': 'close',
  'warning': 'warning',
  'processing': 'loading',
  'edit': 'edit',
  'settings': 'settings',
  'history': 'history',
  'refresh': 'refresh',
  'copy': 'copy',
  'download': 'download',
  'upload': 'upload',
  'save': 'save',
  'reset': 'refresh',
  'close': 'close',
  'delete': 'delete',
  'info': 'info',
  'help': 'help',
  'grid': 'grid',
  'list': 'list',
  'monitor': 'monitor',
  'web': 'web',
  'mobile': 'mobile',
  'database': 'database',
  'shield': 'shield',
  'package': 'package',
  'timer': 'timer',
  'layers': 'layers',
  'gauge': 'gauge',
  'plus': 'plus',
  'minus': 'minus',
  'arrow-right': 'arrow-right',
  'arrow-left': 'arrow-left',
  'external-link': 'external',
  'fire': 'fire',
  'lightbulb': 'lightbulb',
  'robot': 'robot',
  'batch': 'batch',
  'clock': 'clock',
  'tip': 'lightbulb',
  'trash': 'delete',
  'use': 'arrow-right',
  'guide': 'help',
};

interface SmartIconProps {
  type: IconType;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  color?: string;
  className?: string;
  animate?: boolean;
  useSemi?: boolean;  // 强制使用Semi图标
  useOriginal?: boolean;  // 强制使用原始图标
}

// 检测父按钮类型的Hook
const useButtonContext = () => {
  const [buttonType, setButtonType] = useState<'gold' | 'blue' | 'neutral'>('neutral');
  const iconRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!iconRef.current) return;

    const findButtonParent = (element: HTMLElement): HTMLElement | null => {
      if (!element.parentElement) return null;
      if (element.parentElement.tagName === 'BUTTON' || 
          element.parentElement.classList.contains('btn') ||
          element.parentElement.classList.contains('btn-authority')) {
        return element.parentElement;
      }
      return findButtonParent(element.parentElement);
    };

    const buttonParent = findButtonParent(iconRef.current);
    if (buttonParent) {
      const classList = buttonParent.classList;
      if (classList.contains('btn-primary-gold') || classList.contains('btn--primary-gold')) {
        setButtonType('gold');
      } else if (classList.contains('btn-secondary-glass') || classList.contains('btn--secondary-glass')) {
        setButtonType('blue');
      } else {
        setButtonType('neutral');
      }
    }
  }, []);

  return { buttonType, iconRef };
};

const SmartIcon: React.FC<SmartIconProps> = ({
  type,
  size = 'md',
  color,
  className = '',
  animate = false,
  useSemi = true,  // 默认优先使用Semi图标
  useOriginal = false,
}) => {
  const { buttonType, iconRef } = useButtonContext();

  // 强制使用原始图标
  if (useOriginal) {
    return (
      <div ref={iconRef} style={{ display: 'inline-flex' }}>
        <Icon
          type={type}
          size={size}
          color={color || (buttonType === 'gold' ? '#92400e' : buttonType === 'blue' ? '#1e40af' : 'neutral')}
          className={className}
          animate={animate}
        />
      </div>
    );
  }

  // 检查是否有对应的Semi图标
  const semiIconType = iconMapping[type];
  const hasSemiIcon = semiIconType && useSemi;

  // 尺寸映射
  const sizeMapping: Record<string, any> = {
    'xs': 'small',
    'sm': 'small', 
    'md': 'default',
    'lg': 'large',
    'xl': 'extra-large',
  };

  if (hasSemiIcon) {
    // 使用Semi图标
    const semiSize = sizeMapping[size] || 'default';
    
    // 根据按钮类型自动选择预设组件
    if (!color) {
      if (buttonType === 'gold') {
        return (
          <div ref={iconRef} style={{ display: 'inline-flex' }}>
            <GoldIcon
              type={semiIconType}
              size={semiSize}
              className={className}
              spin={animate && (type === 'processing' || type === 'refresh')}
            />
          </div>
        );
      } else if (buttonType === 'blue') {
        return (
          <div ref={iconRef} style={{ display: 'inline-flex' }}>
            <BlueIcon
              type={semiIconType}
              size={semiSize}
              className={className}
              spin={animate && (type === 'processing' || type === 'refresh')}
            />
          </div>
        );
      }
    }

    // 使用自定义颜色或自动颜色
    return (
      <div ref={iconRef} style={{ display: 'inline-flex' }}>
        <SemiIcon
          type={semiIconType}
          size={semiSize}
          color={color || 'auto'}
          className={className}
          spin={animate && (type === 'processing' || type === 'refresh')}
        />
      </div>
    );
  } else {
    // 回退到原始图标
    return (
      <div ref={iconRef} style={{ display: 'inline-flex' }}>
        <Icon
          type={type}
          size={size}
          color={color || (buttonType === 'gold' ? '#92400e' : buttonType === 'blue' ? '#1e40af' : 'neutral')}
          className={className}
          animate={animate}
        />
      </div>
    );
  }
};

export default SmartIcon;

// 导出便捷组件
export const GoldSmartIcon: React.FC<Omit<SmartIconProps, 'color'>> = (props) => (
  <SmartIcon {...props} color="#92400e" />
);

export const BlueSmartIcon: React.FC<Omit<SmartIconProps, 'color'>> = (props) => (
  <SmartIcon {...props} color="#1e40af" />
);

// 导出图标映射供其他组件使用
export { iconMapping };