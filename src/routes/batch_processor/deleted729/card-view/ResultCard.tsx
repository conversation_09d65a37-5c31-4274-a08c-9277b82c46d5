import React, { useState, useRef } from 'react';
import { ProcessResult } from '../../types';
import { colors } from '../../config/theme';
import Icon from '../Icon';
import Tooltip from '../Tooltip';
import { InteractiveIframe } from './InteractiveIframe';
import { DataSourceLabel } from '../DataSourceLabel';

interface ResultCardProps {
  /** 处理结果数据 */
  result: ProcessResult;
  /** 是否聚焦状态 */
  focused?: boolean;
  /** 是否选中状态 */
  selected?: boolean;
  /** 是否拖拽状态 */
  dragging?: boolean;
  /** 卡片点击回调 */
  onClick?: (result: ProcessResult) => void;
  /** 选择状态变化回调 */
  onSelectionChange?: (selected: boolean) => void;
  /** 拖拽开始回调 */
  onDragStart?: (event: React.DragEvent) => void;
  /** 拖拽结束回调 */
  onDragEnd?: (event: React.DragEvent) => void;
}

export const ResultCard: React.FC<ResultCardProps> = ({
  result,
  focused = false,
  selected = false,
  dragging = false,
  onClick,
  onSelectionChange,
  onDragStart,
  onDragEnd,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  const { id, query, status, playgroundUrl, error, processTime, metadata } =
    result;

  // 根据状态确定卡片样式
  const getCardStyles = () => {
    let baseStyles: React.CSSProperties = {
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      cursor: 'pointer',
      borderRadius: '12px',
      border: '2px solid transparent',
      position: 'relative',
      height: '600px', // 增加到600px以容纳500px的iframe
      width: '280px',
      background: 'white',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    };

    // 状态相关样式
    if (status === 'success') {
      baseStyles = {
        ...baseStyles,
        borderColor: focused ? colors.primary[400] : 'transparent',
        boxShadow: focused
          ? '0 0 0 3px rgba(59,130,246,0.3), 0 8px 25px rgba(0,0,0,0.15)'
          : '0 2px 8px rgba(100, 181, 246, 0.15)',
      };
    } else if (status === 'error') {
      baseStyles = {
        ...baseStyles,
        borderColor: focused ? '#F0E68C' : 'transparent',
        opacity: 0.9,
        boxShadow: focused
          ? '0 0 0 3px rgba(240, 230, 140, 0.3), 0 8px 25px rgba(0,0,0,0.15)'
          : '0 1px 3px rgba(148, 163, 184, 0.08)',
      };
    } else if (status === 'processing') {
      baseStyles = {
        ...baseStyles,
        borderColor: focused ? colors.warning.main : 'transparent',
        boxShadow: focused
          ? '0 0 0 3px rgba(255, 193, 7, 0.3), 0 8px 25px rgba(0,0,0,0.15)'
          : '0 2px 8px rgba(255, 213, 79, 0.15)',
      };
    }

    // 悬停效果
    if (isHovered && !focused) {
      baseStyles.transform = 'translateY(-4px)';
      baseStyles.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
    }

    // 选中效果
    if (selected) {
      baseStyles.borderColor = colors.primary[500];
      baseStyles.boxShadow =
        '0 0 0 3px rgba(59,130,246,0.2), 0 8px 25px rgba(0,0,0,0.15)';
    }

    // 拖拽效果
    if (dragging) {
      baseStyles.opacity = 0.8;
      baseStyles.transform = 'rotate(3deg)';
      baseStyles.boxShadow = '0 12px 30px rgba(0,0,0,0.25)';
    }

    return baseStyles;
  };

  // 状态图标和文本
  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <Icon type="check" color="success" size="sm" />;
      case 'error':
        return <Icon type="error" color="error" size="sm" />;
      case 'processing':
        return <Icon type="processing" color="processing" size="sm" animate />;
      default:
        return <Icon type="clock" color="neutral" size="sm" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'success':
        return '处理成功';
      case 'error':
        return '处理失败';
      case 'processing':
        return '处理中...';
      default:
        return '等待处理';
    }
  };

  // 处理卡片点击
  const handleCardClick = (event: React.MouseEvent) => {
    // 如果点击在拖拽手柄区域，不触发卡片点击
    if ((event.target as HTMLElement).closest('.drag-handle')) {
      return;
    }

    // 如果已经聚焦且点击在iframe区域，不要失焦
    const target = event.target as HTMLElement;
    const isIframeArea =
      target.closest('.iframe-container') || target.tagName === 'IFRAME';

    if (focused && isIframeArea) {
      // 聚焦状态下点击iframe区域，不做任何处理，让iframe处理事件
      return;
    }

    onClick?.(result);
  };

  // 处理选择框变化
  const handleSelectionChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    event.stopPropagation();
    onSelectionChange?.(event.target.checked);
  };

  return (
    <div
      ref={cardRef}
      className="result-card"
      style={getCardStyles()}
      onClick={handleCardClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      draggable
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
    >
      {/* 拖拽手柄区域 */}
      <div
        className="drag-handle"
        style={{
          height: '20px',
          backgroundColor: 'rgba(0,0,0,0.05)',
          borderRadius: '12px 12px 0 0',
          cursor: 'grab',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
        }}
        onMouseDown={e => {
          e.currentTarget.style.cursor = 'grabbing';
        }}
        onMouseUp={e => {
          e.currentTarget.style.cursor = 'grab';
        }}
      >
        {/* 拖拽指示器 */}
        <div
          style={{
            width: '24px',
            height: '4px',
            backgroundColor: 'rgba(0,0,0,0.2)',
            borderRadius: '2px',
          }}
        />

        {/* 选择框 */}
        <input
          type="checkbox"
          checked={selected}
          onChange={handleSelectionChange}
          style={{
            position: 'absolute',
            left: '8px',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '14px',
            height: '14px',
          }}
          onClick={e => e.stopPropagation()}
        />
      </div>

      {/* iframe预览区域 */}
      <div
        className="iframe-container"
        style={{
          height: '500px',
          padding: '0',
          overflow: 'visible', // 始终允许溢出，让iframe处理滚动
          borderRadius: '0 0 8px 8px',
          position: 'relative',
        }}
      >
        <InteractiveIframe
          result={result}
          focused={focused}
          width={280}
          height={500}
          enableEnhancedLynxPreview={true}
        />
      </div>

      {/* 查询名称区域 */}
      <div
        style={{
          height: '30px',
          padding: '4px 12px',
          display: 'flex',
          alignItems: 'center',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <h4
          style={{
            fontSize: '14px',
            fontWeight: 600,
            color: colors.gray[900],
            margin: 0,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
          title={query}
        >
          {query}
        </h4>
      </div>

      {/* 标签和操作区域 */}
      <div
        style={{
          height: '50px',
          padding: '8px 12px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
        }}
      >
        {/* 标签行 */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          {/* 状态图标 */}
          <Tooltip content={getStatusText()} position="top">
            <div
              style={{
                padding: '2px 6px',
                borderRadius: '4px',
                backgroundColor:
                  status === 'success'
                    ? colors.success.light
                    : status === 'error'
                      ? colors.error.light
                      : status === 'processing'
                        ? colors.warning.light
                        : colors.gray[100],
                display: 'flex',
                alignItems: 'center',
                gap: '2px',
              }}
            >
              {getStatusIcon()}
              <span style={{ fontSize: '10px', fontWeight: 500 }}>
                {status === 'success'
                  ? '成功'
                  : status === 'error'
                    ? '失败'
                    : status === 'processing'
                      ? '处理中'
                      : '等待'}
              </span>
            </div>
          </Tooltip>

          {/* 处理时间 */}
          {processTime && (
            <span
              style={{
                fontSize: '10px',
                color: colors.gray[500],
                backgroundColor: colors.gray[100],
                padding: '2px 4px',
                borderRadius: '2px',
                fontFamily: 'mono',
              }}
            >
              {(processTime / 1000).toFixed(2)}s
            </span>
          )}

          {/* 数据源标签 */}
          {result.dataSource && (
            <DataSourceLabel
              dataSource={result.dataSource}
              internalDataSummary={result.internalDataSummary}
              compact={true}
              showDetails={true}
            />
          )}
        </div>

        {/* 操作行 */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          {/* Playground链接 */}
          {status === 'success' && playgroundUrl && (
            <Tooltip content="打开Playground预览" position="top">
              <a
                href={playgroundUrl}
                target="_blank"
                rel="noopener noreferrer"
                onClick={e => e.stopPropagation()}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                  padding: '4px 8px',
                  borderRadius: '6px',
                  backgroundColor: colors.primary[100],
                  color: colors.primary[700],
                  textDecoration: 'none',
                  fontSize: '12px',
                  fontWeight: 500,
                  transition: 'all 0.2s',
                }}
                onMouseEnter={e => {
                  e.currentTarget.style.backgroundColor = colors.primary[200];
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.backgroundColor = colors.primary[100];
                }}
              >
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                  />
                </svg>
                <span>🔗</span>
              </a>
            </Tooltip>
          )}

          {/* 错误提示 */}
          {status === 'error' && error && (
            <Tooltip content={error} position="top">
              <div
                style={{
                  color: colors.error.main,
                  fontSize: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                }}
              >
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                >
                  <circle cx="12" cy="12" r="10" />
                  <line x1="12" y1="8" x2="12" y2="12" />
                  <line x1="12" y1="16" x2="12.01" y2="16" />
                </svg>
                <span>错误</span>
              </div>
            </Tooltip>
          )}

          {/* 时间戳 */}
          <span
            style={{
              fontSize: '10px',
              color: colors.gray[400],
              marginLeft: 'auto',
            }}
          >
            {result.timestamp &&
              new Date(result.timestamp).toLocaleTimeString()}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ResultCard;
