import React, { useState, useRef, useEffect, useCallback } from 'react';
import { ProcessResult } from '../../types';
import { colors } from '../../config/theme';
import { ResultCard } from './ResultCard';

interface ResultCardsGridProps {
  /** 处理结果数组 */
  results: ProcessResult[];
  /** 当前聚焦的卡片ID */
  focusedCardId?: string;
  /** 选中的卡片ID列表 */
  selectedCardIds?: string[];
  /** 卡片聚焦变化回调 */
  onCardFocus?: (cardId: string | null) => void;
  /** 卡片选择变化回调 */
  onCardSelection?: (cardId: string, selected: boolean) => void;
  /** 批量操作回调 */
  onBatchAction?: (action: string, cardIds: string[]) => void;
  /** 拖拽排序回调 */
  onCardReorder?: (fromIndex: number, toIndex: number) => void;
}

export const ResultCardsGrid: React.FC<ResultCardsGridProps> = ({
  results,
  focusedCardId,
  selectedCardIds = [],
  onCardFocus,
  onCardSelection,
  // onBatchAction,
  onCardReorder,
}) => {
  const [draggedCardIndex, setDraggedCardIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const gridRef = useRef<HTMLDivElement>(null);

  // 计算网格列数
  const getColumnCount = useCallback(() => {
    if (!gridRef.current) {
      return 4;
    }

    const containerWidth = gridRef.current.offsetWidth;
    const cardWidth = 280;
    const gap = 16;
    const padding = 32; // 左右padding总和

    const availableWidth = containerWidth - padding;
    const columnsWithGaps = Math.floor(availableWidth / (cardWidth + gap));

    // 最小1列，最大5列
    return Math.max(1, Math.min(5, columnsWithGaps));
  }, []);

  const [columnCount, setColumnCount] = useState(() => getColumnCount());

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setColumnCount(getColumnCount());
    };

    window.addEventListener('resize', handleResize);
    // 初始计算
    setTimeout(handleResize, 100);

    return () => window.removeEventListener('resize', handleResize);
  }, [getColumnCount]);

  // 处理卡片点击
  const handleCardClick = useCallback(
    (result: ProcessResult) => {
      // 切换聚焦状态
      const newFocusedId = focusedCardId === result.id ? null : result.id;
      onCardFocus?.(newFocusedId);
    },
    [focusedCardId, onCardFocus],
  );

  // 处理卡片选择
  const handleCardSelection = useCallback(
    (cardId: string, selected: boolean) => {
      onCardSelection?.(cardId, selected);
    },
    [onCardSelection],
  );

  // 处理页面点击（失焦）
  useEffect(() => {
    const handleDocumentClick = (event: MouseEvent) => {
      if (gridRef.current && !gridRef.current.contains(event.target as Node)) {
        onCardFocus?.(null);
      }
    };

    document.addEventListener('click', handleDocumentClick);
    return () => document.removeEventListener('click', handleDocumentClick);
  }, [onCardFocus]);

  // 处理ESC键失焦
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onCardFocus?.(null);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onCardFocus]);

  // 拖拽相关处理
  const handleDragStart = useCallback(
    (event: React.DragEvent, index: number) => {
      setDraggedCardIndex(index);
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('text/plain', index.toString());

      // 创建拖拽预览
      const dragImage = event.currentTarget.cloneNode(true) as HTMLElement;
      dragImage.style.transform = 'rotate(3deg)';
      dragImage.style.opacity = '0.8';
      event.dataTransfer.setDragImage(dragImage, 140, 200);
    },
    [],
  );

  const handleDragEnd = useCallback(() => {
    setDraggedCardIndex(null);
    setDragOverIndex(null);
  }, []);

  const handleDragOver = useCallback(
    (event: React.DragEvent, index: number) => {
      event.preventDefault();
      event.dataTransfer.dropEffect = 'move';

      if (draggedCardIndex !== null && draggedCardIndex !== index) {
        setDragOverIndex(index);
      }
    },
    [draggedCardIndex],
  );

  const handleDrop = useCallback(
    (event: React.DragEvent, dropIndex: number) => {
      event.preventDefault();

      if (draggedCardIndex !== null && draggedCardIndex !== dropIndex) {
        onCardReorder?.(draggedCardIndex, dropIndex);
      }

      setDraggedCardIndex(null);
      setDragOverIndex(null);
    },
    [draggedCardIndex, onCardReorder],
  );

  // 计算网格样式
  const gridStyles: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: `repeat(${columnCount}, 1fr)`,
    gap: '16px',
    width: '100%',
    maxWidth: '1600px',
    margin: '0 auto',
  };

  // 如果没有结果，显示空状态
  if (results.length === 0) {
    return (
      <div
        style={{
          height: '400px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: colors.gray[500],
          fontSize: '16px',
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <div
            style={{
              width: '80px',
              height: '80px',
              borderRadius: '50%',
              background: colors.gray[100],
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '32px',
              margin: '0 auto 16px',
            }}
          >
            📋
          </div>
          <div style={{ fontWeight: 500, marginBottom: '8px' }}>
            暂无处理结果
          </div>
          <div style={{ fontSize: '14px', opacity: 0.7 }}>
            请在左侧输入查询内容并开始处理
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ position: 'relative', width: '100%', minHeight: '100%' }}>
      {/* 卡片网格 */}
      <div ref={gridRef} style={gridStyles}>
        {results.map((result, index) => (
          <div
            key={result.id}
            style={{
              position: 'relative',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              transform:
                draggedCardIndex === index ? 'scale(0.95)' : 'scale(1)',
            }}
            onDragOver={e => handleDragOver(e, index)}
            onDrop={e => handleDrop(e, index)}
          >
            {/* 拖拽插入指示器 */}
            {dragOverIndex === index && draggedCardIndex !== index && (
              <div
                style={{
                  position: 'absolute',
                  top: '-8px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '80%',
                  height: '4px',
                  background: colors.primary[500],
                  borderRadius: '2px',
                  zIndex: 10,
                }}
              />
            )}

            <ResultCard
              result={result}
              focused={focusedCardId === result.id}
              selected={selectedCardIds.includes(result.id)}
              dragging={draggedCardIndex === index}
              onClick={handleCardClick}
              onSelectionChange={selected =>
                handleCardSelection(result.id, selected)
              }
              onDragStart={event => handleDragStart(event, index)}
              onDragEnd={handleDragEnd}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default ResultCardsGrid;
