export { ResultCard } from './ResultCard';
export { ResultCardsGrid } from './ResultCardsGrid';
export { InteractiveIframe } from './InteractiveIframe';

// 导出相关类型
export interface CardViewState {
  focusedCardId: string | null;
  selectedCardIds: string[];
  dragState: {
    isDragging: boolean;
    draggedIndex: number | null;
    dragOverIndex: number | null;
  };
}

export interface BatchAction {
  type: 'openAll' | 'export' | 'delete' | 'clear';
  cardIds: string[];
}
