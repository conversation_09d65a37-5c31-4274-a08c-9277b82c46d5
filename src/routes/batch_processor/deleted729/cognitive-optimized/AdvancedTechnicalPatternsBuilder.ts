import { QueryContext, BuilderModule } from './types';
import { BEST_PRACTICES } from '../prompts/BestPractices';

export class AdvancedTechnicalPatternsBuilder implements BuilderModule {
  build(context: QueryContext): string {
    if (context.complexity !== 'complex') {
      return '';
    }

    return `
# 🚀 ADVANCED TECHNICAL PATTERNS

## BEST PRACTICES AND CODE PATTERNS
${BEST_PRACTICES}

## PERFORMANCE OPTIMIZATION STRATEGIES

### Memory Management and Resource Cleanup
\\\`\\\`\\\`javascript
// Proper resource cleanup in onUnload
onUnload() {
  // Clear all timers
  if (this.data.timer) {
    clearInterval(this.data.timer);
  }
  
  // Destroy chart instances
  if (this.data.chartInstance) {
    this.data.chartInstance.destroy();
  }
  
  // Clear event listeners
  if (this.data.eventListeners) {
    this.data.eventListeners.forEach(listener => {
      listener.remove();
    });
  }
  
  // Clear memory references
  this.setData({
    timer: null,
    chartInstance: null,
    eventListeners: null,
    largeDataSet: null
  });
}
\\\`\\\`\\\`

### Network Request Optimization
\\\`\\\`\\\`javascript
// Debounced network requests
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Implement in component
searchData: debounce(function(query) {
  lynx.request({
    url: '/api/search',
    data: { q: query },
    success: (res) => {
      this.setData({ searchResults: res.data });
    }
  });
}, 300)
\\\`\\\`\\\`

### Rendering Performance Optimization
\\\`\\\`\\\`javascript
// Efficient list rendering with virtual scrolling
handleScroll(event) {
  const scrollTop = event.detail.scrollTop;
  const itemHeight = 100; // rpx
  const visibleStart = Math.floor(scrollTop / itemHeight);
  const visibleEnd = visibleStart + Math.ceil(event.detail.scrollHeight / itemHeight);
  
  // Only render visible items
  const visibleItems = this.data.allItems.slice(visibleStart, visibleEnd);
  this.setData({
    visibleItems,
    translateY: visibleStart * itemHeight
  });
}
\\\`\\\`\\\`

### Cache Strategy Implementation
\\\`\\\`\\\`javascript
// Implement LRU cache for expensive operations
class LRUCache {
  constructor(capacity) {
    this.capacity = capacity;
    this.cache = new Map();
  }
  
  get(key) {
    if (this.cache.has(key)) {
      const value = this.cache.get(key);
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return null;
  }
  
  set(key, value) {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.capacity) {
      this.cache.delete(this.cache.keys().next().value);
    }
    this.cache.set(key, value);
  }
}
\\\`\\\`\\\`

## ADVANCED ARCHITECTURE PATTERNS

### State Management Pattern
\\\`\\\`\\\`javascript
// Centralized state management
const StateManager = {
  state: {},
  listeners: [],
  
  setState(newState) {
    this.state = { ...this.state, ...newState };
    this.listeners.forEach(listener => listener(this.state));
  },
  
  getState() {
    return this.state;
  },
  
  subscribe(listener) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }
};
\\\`\\\`\\\`

### Component Communication Pattern
\\\`\\\`\\\`javascript
// Event bus for component communication
const EventBus = {
  events: {},
  
  on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  },
  
  emit(event, data) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(data));
    }
  },
  
  off(event, callback) {
    if (this.events[event]) {
      this.events[event] = this.events[event].filter(cb => cb !== callback);
    }
  }
};
\\\`\\\`\\\`

### Data Flow Management
\\\`\\\`\\\`javascript
// Reactive data flow with observers
class DataObserver {
  constructor(data, callback) {
    this.callback = callback;
    this.proxy = new Proxy(data, {
      set: (target, property, value) => {
        target[property] = value;
        this.callback(property, value, target);
        return true;
      }
    });
  }
  
  getData() {
    return this.proxy;
  }
}

// Usage in component
onLoad() {
  const observer = new DataObserver(this.data, (prop, value, data) => {
    this.setData({ [prop]: value });
  });
  this.observableData = observer.getData();
}
\\\`\\\`\\\`
`;
  }
}

export const advancedTechnicalPatternsBuilder = new AdvancedTechnicalPatternsBuilder();