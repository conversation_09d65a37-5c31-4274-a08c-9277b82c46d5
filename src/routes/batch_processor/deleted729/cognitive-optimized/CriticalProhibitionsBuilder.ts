import { QueryContext, BuilderModule } from './types';

export class CriticalProhibitionsBuilder implements BuilderModule {
  build(context: QueryContext): string {
    return `
# 🚨 CRITICAL PROHIBITIONS - ZERO TOLERANCE RULES

## 🚫 ABSOLUTELY FORBIDDEN CONTENT (ZERO TOLERANCE)

### 1. 绝对禁止的视觉元素
- **🚫 EMOJI 完全禁止**: 任何Unicode Emoji字符都严禁使用
  - ❌ 禁止: 😀 😃 😄 😁 😊 🚀 ✨ 💫 🔥 💪 👍 ❤️ 🎉 🌟 等任何emoji
  - ❌ 禁止: 表情符号、手势符号、物品符号、符号emoji等
  - ✅ 唯一图标方案: Font Awesome Unicode字符（如 \\uF015）

### 2. 绝对禁止的HTML标签
- **🚫 HTML标签完全禁止**: 
  - ❌ 容器: \`<div>\`, \`<span>\`, \`<section>\`, \`<article>\`, \`<header>\`, \`<footer>\`
  - ❌ 文本: \`<p>\`, \`<h1>\`-\`<h6>\`, \`<strong>\`, \`<em>\`, \`<b>\`, \`<i>\`
  - ❌ 列表: \`<ul>\`, \`<ol>\`, \`<li>\`, \`<dl>\`, \`<dt>\`, \`<dd>\`
  - ❌ 表格: \`<table>\`, \`<tr>\`, \`<td>\`, \`<th>\`, \`<thead>\`, \`<tbody>\`
  - ❌ 媒体: \`<img>\`, \`<video>\`(HTML版), \`<audio>\`(HTML版)
  - ❌ 表单: \`<form>\`(HTML版), \`<input>\`(HTML版), \`<button>\`(HTML版), \`<select>\`, \`<option>\`, \`<textarea>\`(HTML版)
  - ❌ 其他: \`<a>\`, \`<iframe>\`, \`<embed>\`, \`<object>\`, \`<canvas>\`(HTML版)

### 3. 绝对禁止的CSS属性
- **🚫 Webkit属性全禁**: 
  - ❌ \`-webkit-transform\`, \`-webkit-transition\`, \`-webkit-animation\`
  - ❌ \`-webkit-appearance\`, \`-webkit-user-select\`, \`-webkit-tap-highlight-color\`
  - ❌ \`-webkit-overflow-scrolling\`, \`-webkit-box-shadow\`, \`-webkit-border-radius\`
  - ❌ \`-moz-*\`, \`-ms-*\`, \`-o-*\` 等所有前缀属性

- **🚫 现代CSS特性禁用**:
  - ❌ \`backdrop-filter\`, \`filter\`, \`clip-path\`, \`mask\`, \`mask-image\`
  - ❌ \`object-fit\`, \`object-position\`, \`user-select\`, \`cursor\`, \`pointer-events\`
  - ❌ \`text-shadow\`, \`appearance\`, \`outline\`, \`resize\`, \`content\`
  - ❌ \`counter-increment\`, \`counter-reset\`, \`quotes\`, \`list-style\`

- **🚫 定位和粘性布局完全禁用**:
  - ❌ \`position: sticky\` - 严禁所有sticky定位，特别是标题粘性定位
  - ❌ \`position: fixed\` - 禁止固定定位，影响移动端滚动体验
  - ❌ \`position: absolute\` 搭配 \`top: 0\` - 禁止模拟sticky行为
  - ❌ 标题sticky效果：所有标题必须随内容正常滚动，不得粘在顶部
  - ❌ 导航栏sticky：导航元素不得使用粘性定位
  - ✅ 替代方案：使用正常文档流布局，让内容自然滚动

- **🚫 Grid布局完全禁用**:
  - ❌ \`display: grid\`, \`display: inline-grid\`
  - ❌ \`grid-template-columns\`, \`grid-template-rows\`, \`grid-template-areas\`
  - ❌ \`grid-column\`, \`grid-row\`, \`grid-area\`, \`grid-gap\`
  - ❌ \`justify-items\`, \`align-items\` (Grid context), \`place-items\`

### 4. 绝对禁止的选择器
- **🚫 多类选择器严禁**: 
  - ❌ \`.btn.primary\`, \`.card.active\`, \`.item.selected\`
  - ❌ \`.container.fluid.responsive\` 等连续类名
  - ✅ 正确: \`.btn-primary\`, \`.card-active\`, \`.item-selected\`

- **🚫 不支持的选择器**:
  - ❌ 通配符: \`*\`, \`*:before\`, \`*:after\`
  - ❌ 属性选择器: \`[type="text"]\`, \`[class*="btn"]\`, \`[data-*]\`
  - ❌ 伪类: \`:hover\`, \`:focus\`, \`:active\`, \`:nth-child\`, \`:not()\`
  - ❌ 伪元素: \`::before\`, \`::after\`, \`::first-line\`, \`::first-letter\`

### 5. 绝对禁止的事件语法
- **🚫 错误的事件绑定**:
  - ❌ React语法: \`bindtap="handleClick"\`, \`bindinput="handleInput"\`
  - ❌ HTML语法: \`onclick="handleClick"\`, \`onchange="handleChange"\`
  - ❌ Vue语法: \`@click="handleClick"\`, \`@input="handleInput"\`
  - ❌ JavaScript: \`addEventListener("click", handler)\`
  - ✅ 正确Lynx语法: \`bindtap="handleClick"\`, \`bindinput="handleInput"\`

### 6. 绝对禁止的输出格式
- **🚫 禁止解释性文字**:
  - ❌ "这是一个...", "我将为您...", "以下是..."
  - ❌ "让我来创建...", "现在开始实现...", "代码如下..."
  - ❌ "这个应用包含...", "主要功能有...", "实现步骤是..."

- **🚫 禁止思考过程输出**:
  - ❌ 分步骤说明: "第一步...", "接下来...", "最后..."
  - ❌ 实现思路: "我的想法是...", "设计思路如下..."
  - ❌ 代码解释: "上面的代码实现了...", "这段代码的作用是..."

- **🚫 强制输出格式**:
  - ✅ 必须使用: \`<FILES><FILE path="...">content</FILE></FILES>\`
  - ✅ 直接输出5个完整文件，无任何额外文字

### 7. 绝对禁止的数据处理
- **🚫 错误的数据修改**:
  - ❌ 直接赋值: \`this.data.count = 10\` (不会触发重渲染)
  - ❌ 数组直接修改: \`this.data.items.push(item)\` (不会触发重渲染)
  - ❌ 对象直接修改: \`this.data.user.name = 'John'\` (不会触发重渲染)
  - ✅ 正确: \`this.setData({ count: 10 })\`, \`this.setData({ items: [...this.data.items, item] })\`

- **🚫 危险的数据访问**:
  - ❌ 直接访问: \`this.data.user.profile.name\` (可能报错)
  - ❌ 假设存在: \`this.data.items[0].title\` (可能undefined)
  - ✅ 正确: \`this.data?.user?.profile?.name || 'Default'\`

### 8. 绝对禁止的组件使用
- **🚫 自闭合标签错误**:
  - ❌ \`<image src="..."></image>\`, \`<input type="text"></input>\`
  - ❌ \`<progress percent="50"></progress>\`, \`<slider min="0" max="100"></slider>\`
  - ✅ 正确: \`<image src="..." />\`, \`<input type="text" />\`, \`<progress percent="50" />\`

- **🚫 scroll-view致命错误**:
  - ❌ 无高度: \`<scroll-view scroll-y>content</scroll-view>\` (滚动失效)
  - ✅ 正确: \`<scroll-view scroll-y style="height: 600rpx;">content</scroll-view>\`

- **🚫 文字包裹错误**:
  - ❌ \`<view>用户名: {{name}}</view>\` (文字未包裹)
  - ✅ 正确: \`<view><text>用户名: {{name}}</text></view>\`

### 9. 绝对禁止的图标使用
- **🚫 其他图标库完全禁止**:
  - ❌ SVG图标: \`<svg><path d="..."/></svg>\`
  - ❌ 图片图标: \`<image src="icon.png" />\`
  - ❌ 其他字体图标: Material Icons, Feather Icons等
  - ❌ 自定义图标字体: 除Font Awesome之外的任何字体图标
  - ✅ 唯一正确: Font Awesome Unicode字符在text组件中

### 10. 绝对禁止的设计元素
- **🚫 被禁用的视觉效果**:
  - ❌ 平面单色设计: 纯色背景无渐变
  - ❌ 无高光效果: 缺少box-shadow高光
  - ❌ 静态界面: 无@keyframes动画
  - ✅ 强制要求: 渐变背景 + 高光效果 + keyframe动画

### 11. 绝对禁止的兼容性问题
- **🚫 不支持的单位和属性**:
  - ❌ \`vw\`, \`vh\`, \`vmin\`, \`vmax\` (不支持viewport单位)
  - ❌ \`calc()\` 复杂计算 (支持有限)
  - ❌ \`em\`, \`rem\` (推荐使用rpx)
  - ❌ CSS变量: \`var(--color)\`, \`--custom-property\`

### 12. 绝对禁止的内容类型
- **🚫 禁止的文档内容**:
  - ❌ 版本信息: 构建号、时间戳、版本记录
  - ❌ 实现笔记: 技术讨论、调试信息、TODO注释
  - ❌ 装饰元素: \`---\`, \`***\`, \`===\`, \`~~~\` 等分隔符
  - ❌ 元评论: 对prompt系统本身的评论或说明

## 💀 VIOLATION CONSEQUENCES

违反任何上述禁忌规则将导致：
- **编译失败**: 代码无法正常编译和运行
- **渲染异常**: 界面显示错误或空白
- **性能问题**: 应用卡顿、内存泄漏
- **跨平台兼容性问题**: 在不同设备上表现不一致
- **用户体验问题**: 交互异常、功能失效

## ✅ COMPLIANCE VERIFICATION

每次生成代码后必须验证：
1. **零HTML标签**: 确保没有任何HTML标签
2. **零Emoji**: 确保没有任何emoji字符
3. **零禁用CSS**: 确保没有使用禁用的CSS属性
4. **零Sticky定位**: 确保没有使用position: sticky或类似粘性定位
5. **正确事件语法**: 使用bindtap等正确语法
6. **正确组件使用**: 自闭合标签正确，scroll-view有高度设置
7. **正确数据处理**: 使用setData和可选链
8. **Font Awesome唯一**: 图标只使用Font Awesome Unicode
9. **Query深度分析**: 确保进行了充分的用户意图理解
10. **FILES格式**: 输出格式严格遵循要求

这些禁忌规则是确保Lynx应用质量和兼容性的底线，绝对不能违反！
`;
  }
}

export const criticalProhibitionsBuilder = new CriticalProhibitionsBuilder();