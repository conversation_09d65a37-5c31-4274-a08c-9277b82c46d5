import { QueryContext, BuilderModule } from './types';
import { LIGHTCHART_PROMPT_CONTENT } from '../prompts/LightChartPromptLoader';

export class LightChartIntegrationBuilder implements BuilderModule {
  build(context: QueryContext): string {
    if (!context.isLightChartNeeded) {
      return '';
    }

    return `
# 📊 LIGHTCHART INTEGRATION SPECIFICATION

## CRITICAL ARCHITECTURE REQUIREMENTS
LightChart integration requires complete adherence to this specification:

${LIGHTCHART_PROMPT_CONTENT}

## 🔥 TOP 11 CRITICAL FAILURE MODES

### 1. 🚨 Canvas Component Missing - Three-file setup incomplete
**Solution**: index.json registers lightcharts-canvas + index.ttml has <lightcharts-canvas> + index.js has matching bindinitchart method

### 2. 🚨 JSON Serialization Failure - Functions stripped by JSON.stringify()
**Solution**: Use string templates "{b}: {c}" instead of function(params)
**Detection**: try { JSON.stringify(option); } catch(e) { console.error("Not serializable:", e.message); return false; }
**Templates**: "{a}" = series name, "{b}" = category name, "{c}" = value

### 3. 🚨 Data Format Mismatch - Wrong pattern for chart type
**Solution**: 
- PIE: series.data = [{ name: "A", value: 30 }] (MUST be objects)
- BAR: xAxis.data = ["A", "B"] + series.data = [10, 20] (category pattern)
- LINE: option.data = [{ x: "A", y: 10 }] + series.encode = { x: "x", y: "y" } (coordinate pattern)
**Validation**: validatePieData(data) { return Array.isArray(data) && data.every(item => typeof item.name === "string" && typeof item.value === "number"); }

### 4. 🚨 Canvas Timing Race - setOption() before canvas ready
**Solution**: ALWAYS use setTimeout(100ms) after LynxChart constructor

### 5. 🚨 Memory Leak from ChartMap - Instances not destroyed
**Solution**: Call chart.destroy() in onUnload. Store on component instance, not this.data

### 6. 🚨 Constructor Parameter Missing - Required params absent
**Solution**: ALL THREE mandatory: { canvasName, width, height } from e.detail

### 7. 🚨 Complex Styling Incompatibility - ECharts styling not supported
**Solution**: Replace gradients with simple color strings. Use itemStyle.color = "#hex"

### 8. 🚨 Large Number Precision - Canvas rendering limits
**Solution**: Scale to reasonable ranges. Use Math.round() for large values

### 9. 🚨 Async Data Race - Chart setup before data arrives
**Solution**: Separate initialization from data setup
**Pattern**: initChart(e) { this.chart = new LynxChart(e.detail); } + updateChart() { if (this.validateOption(option)) this.chart.setOption(option); }

### 10. 🚨 Multi-Chart Instance Conflicts - Shared canvasName/variables
**Solution**: Unique canvasName per chart + separate instance properties + separate init methods

### 11. 🚨 Complete Option Replacement Required - Prevents corruption
**Solution**: Always use complete option objects: this.chart.setOption(completeOption) not partial updates

## 🛠️ EMERGENCY DEBUGGING PROTOCOL

### STEP 1: Setup Verification
\`\`\`bash
grep -r "lightcharts-canvas" index.json
grep -r "bindinitchart" index.ttml
grep -r "initChart" index.js
\`\`\`

### STEP 2: Option Validation
\`\`\`javascript
try {
  const serialized = JSON.stringify(option);
  console.log("Option valid:", serialized.length);
} catch(e) {
  console.error("Serialization failed:", e.message);
}
\`\`\`

### STEP 3: Canvas Lifecycle Check
\`\`\`javascript
console.log("Canvas params:", e.detail);
console.log("Chart instance:", !!this.chart);
console.log("Chart ready:", this.chart?.getRenderInstance());
\`\`\`

### STEP 4: Memory Leak Detection
\`\`\`javascript
console.log("Active charts:", Object.keys(chartMap || {}).length);
console.log("Chart destroyed:", this.chart === null);
\`\`\`

## 📋 MANDATORY IMPLEMENTATION CHECKLIST

### ✅ Three-File Setup
- [ ] index.json registers lightcharts-canvas
- [ ] index.ttml has <lightcharts-canvas> + bindinitchart
- [ ] index.js has matching initChart method
- [ ] canvasName globally unique

### ✅ Option Validation
- [ ] JSON.stringify(option) test passes (use detection code)
- [ ] No functions anywhere in option object
- [ ] Data format matches chart type (coordinate vs category vs series)
- [ ] PIE data uses [{ name, value }] objects only
- [ ] Complete option replacement pattern used
- [ ] Numeric values finite and reasonable

### ✅ Lifecycle Management
- [ ] Chart stored on component instance (not this.data)
- [ ] setTimeout(100ms) for initial setOption
- [ ] Separate initChart() and updateChart() methods
- [ ] destroy() call in onUnload
- [ ] Error handling for async operations
- [ ] Data validation functions implemented

### ✅ Performance Optimization
- [ ] Data points <100 for performance
- [ ] Large numbers scaled appropriately
- [ ] Memory usage monitored
- [ ] Canvas dimensions explicitly set

### ✅ Debugging Instrumentation
- [ ] Console logs for lifecycle events
- [ ] Validation functions implemented
- [ ] Error boundaries for failures
- [ ] Diagnostic commands available

## 🎯 GUARANTEED SUCCESS PATTERN

\`\`\`javascript
// index.json
{ "usingComponents": { "lightcharts-canvas": "@byted/lynx-lightcharts/lightcharts-canvas/lightcharts-canvas" } }

// index.ttml
<lightcharts-canvas canvasName="myUniqueChart" bindinitchart="initChart" style="width: 100%; height: 400px;"/>

// index.js
import LynxChart from "@byted/lynx-lightcharts/src/chart";

Card({
  chart: null,
  
  initChart(e) {
    const { canvasName, width, height } = e.detail;
    this.chart = new LynxChart({ canvasName, width, height });
    
    setTimeout(() => {
      if (this.chart) {
        const option = {
          series: [{
            type: "pie",
            data: [{ name: "A", value: 30 }, { name: "B", value: 70 }]
          }]
        };
        
        if (this.validateOption(option)) {
          this.chart.setOption(option);
        }
      }
    }, 100);
  },
  
  validateOption(option) {
    try {
      JSON.stringify(option);
      return true;
    } catch (e) {
      console.error("Invalid option:", e.message);
      return false;
    }
  },
  
  onUnload() {
    if (this.chart) {
      this.chart.destroy();
      this.chart = null;
    }
  }
});
\`\`\`

This pattern eliminates all 10 critical failure modes and ensures reliable chart rendering.
`;
  }
}

export const lightChartIntegrationBuilder = new LightChartIntegrationBuilder();