import { QueryContext, BuilderModule } from './types';

export class LynxComponentsBuilder implements BuilderModule {
  build(context: QueryContext): string {
    return `
# 🧩 LYNX 组件系统完整映射规范

## 基础组件系统详细规范

### 🚨 CRITICAL: 文字强制包裹规则
**所有文字内容必须使用 text 标签包裹，这是 Lynx 的核心约束！**

❌ **错误写法**:
\\\`\\\`\\\`html
<view>这是文字</view>
<view>用户名: {{name}}</view>
\\\`\\\`\\\`

✅ **正确写法**:
\\\`\\\`\\\`html
<view><text>这是文字</text></view>
<view><text>用户名: {{name}}</text></view>
\\\`\\\`\\\`

### 🚨 CRITICAL: TTML转义规则
XML实体转义是必需的：

- **< 必须转义为 &lt;**
- **> 必须转义为 &gt;**
- **& 必须转义为 &amp;**
- **" 必须转义为 &quot;**
- **' 必须转义为 &apos;**

## 容器组件 (Container Components)

### view - 通用容器
- **功能**: 替代所有 HTML 的 div、span 标签
- **用法**: 布局容器、组件包裹、样式容器
- **属性**: class, style, bindtap, bindlongpress
- **特性**: 支持 flexbox 布局、绝对定位、变换动画

\\\`\\\`\\\`html
<view class="container">
  <text>内容区域</text>
</view>
\\\`\\\`\\\`

### scroll-view - 滚动容器
- **功能**: 高性能滚动区域
- **🚨 CRITICAL**: 必须设置 height 或 max-height 属性
- **属性**: scroll-y, scroll-x, scroll-top, bindscroll
- **性能**: 原生滚动优化，支持惯性滚动

\\\`\\\`\\\`html
<scroll-view class="scroll-container" scroll-y style="height: 600rpx;">
  <view tt:for="{{items}}" tt:key="{{item.id}}">
    <text>{{item.name}}</text>
  </view>
</scroll-view>
\\\`\\\`\\\`

### swiper - 轮播容器
- **功能**: 触摸轮播组件
- **属性**: autoplay, interval, circular, indicator-dots
- **子组件**: 必须使用 swiper-item 作为直接子元素

\\\`\\\`\\\`html
<swiper class="swiper" autoplay interval="3000" circular indicator-dots>
  <swiper-item>
    <view class="slide">
      <text>第一页</text>
    </view>
  </swiper-item>
  <swiper-item>
    <view class="slide">
      <text>第二页</text>
    </view>
  </swiper-item>
</swiper>
\\\`\\\`\\\`

### cover-view - 覆盖层容器
- **功能**: 原生组件上方的覆盖层
- **使用场景**: video、map、live-player 等原生组件的覆盖层
- **限制**: 只能覆盖原生组件，不能覆盖其他组件

## 文本和媒体组件

### text - 文本组件
- **功能**: 唯一的文本显示组件
- **🚨 CRITICAL**: 所有文字内容的强制包裹器
- **属性**: selectable, decode, class, style
- **特性**: 支持文本选择、HTML 实体解码

\\\`\\\`\\\`html
<text class="title">{{title}}</text>
<text class="content" selectable>{{content}}</text>
\\\`\\\`\\\`

### image - 图片组件
- **功能**: 图片显示组件（替代 HTML img）
- **🚨 CRITICAL**: 自闭合标签，必须以 /> 结尾
- **属性**: src, mode, lazy-load, binderror, bindload
- **模式**: aspectFit, aspectFill, scaleToFill, center, top, bottom, left, right

\\\`\\\`\\\`html
<image src="{{avatar}}" mode="aspectFit" class="avatar" lazy-load bindload="onImageLoad" />
\\\`\\\`\\\`

### video - 视频组件
- **功能**: 视频播放器
- **属性**: src, controls, autoplay, loop, muted, poster
- **事件**: bindplay, bindpause, bindended, bindtimeupdate

\\\`\\\`\\\`html
<video src="{{videoUrl}}" controls poster="{{poster}}" bindplay="onVideoPlay" />
\\\`\\\`\\\`

### audio - 音频组件
- **功能**: 音频播放器
- **属性**: src, controls, autoplay, loop, name, author, poster
- **事件**: bindplay, bindpause, bindended

### canvas - 画布组件
- **功能**: 2D 绘图和图表渲染
- **🚨 CRITICAL**: 必须设置 canvas-id 属性
- **用法**: 图表绘制、自定义图形、动画效果

\\\`\\\`\\\`html
<canvas canvas-id="chartCanvas" class="chart-canvas" />
\\\`\\\`\\\`

## 表单和输入组件

### form - 表单容器
- **功能**: 表单数据收集和提交
- **属性**: bindsubmit, bindreset
- **用法**: 包裹表单控件，统一数据管理

\\\`\\\`\\\`html
<form bindsubmit="onSubmit">
  <input name="username" placeholder="用户名" />
  <button form-type="submit">提交</button>
</form>
\\\`\\\`\\\`

### input - 输入框
- **功能**: 单行文本输入
- **🚨 CRITICAL**: 自闭合标签
- **属性**: type, placeholder, value, maxlength, bindinput
- **类型**: text, number, password, idcard, digit

\\\`\\\`\\\`html
<input type="text" placeholder="请输入用户名" value="{{username}}" bindinput="onInputChange" />
\\\`\\\`\\\`

### textarea - 多行文本输入
- **功能**: 多行文本输入区域
- **属性**: placeholder, value, maxlength, auto-height
- **事件**: bindinput, bindfocus, bindblur

\\\`\\\`\\\`html
<textarea placeholder="请输入内容" value="{{content}}" maxlength="200" auto-height bindinput="onTextareaInput" />
\\\`\\\`\\\`

### picker - 选择器
- **功能**: 选项选择组件
- **属性**: mode, range, value, bindchange
- **模式**: selector, multiSelector, time, date, region

\\\`\\\`\\\`html
<picker mode="selector" range="{{options}}" value="{{selectedIndex}}" bindchange="onPickerChange">
  <view class="picker-display">
    <text>{{options[selectedIndex]}}</text>
  </view>
</picker>
\\\`\\\`\\\`

### slider - 滑块
- **功能**: 数值范围选择
- **🚨 CRITICAL**: 自闭合标签
- **属性**: min, max, value, step, bindchange

\\\`\\\`\\\`html
<slider min="0" max="100" value="{{sliderValue}}" bindchange="onSliderChange" />
\\\`\\\`\\\`

### switch - 开关
- **功能**: 布尔值切换
- **🚨 CRITICAL**: 自闭合标签
- **属性**: checked, color, bindchange

\\\`\\\`\\\`html
<switch checked="{{isEnabled}}" bindchange="onSwitchChange" />
\\\`\\\`\\\`

### checkbox - 复选框
- **功能**: 多选选项
- **🚨 CRITICAL**: 必须配合 checkbox-group 使用
- **属性**: value, checked, bindchange

\\\`\\\`\\\`html
<checkbox-group bindchange="onCheckboxChange">
  <checkbox value="option1" checked="{{option1Checked}}">选项1</checkbox>
  <checkbox value="option2" checked="{{option2Checked}}">选项2</checkbox>
</checkbox-group>
\\\`\\\`\\\`

### radio - 单选框
- **功能**: 单选选项
- **🚨 CRITICAL**: 必须配合 radio-group 使用
- **属性**: value, checked, bindchange

\\\`\\\`\\\`html
<radio-group bindchange="onRadioChange">
  <radio value="A" checked="{{selectedOption === 'A'}}">选项A</radio>
  <radio value="B" checked="{{selectedOption === 'B'}}">选项B</radio>
</radio-group>
\\\`\\\`\\\`

## 导航和交互组件

### navigator - 导航器
- **功能**: 声明式页面导航
- **属性**: url, open-type, bindSuccess, bindFail
- **类型**: navigate, redirect, switchTab, navigateBack

\\\`\\\`\\\`html
<navigator url="/pages/detail/detail?id={{id}}" open-type="navigate">
  <view class="nav-item">
    <text>查看详情</text>
  </view>
</navigator>
\\\`\\\`\\\`

### button - 按钮组件
- **功能**: 交互按钮
- **属性**: type, size, plain, loading, disabled, bindtap
- **类型**: primary, default, warn

\\\`\\\`\\\`html
<button type="primary" bindtap="onButtonClick" loading="{{isLoading}}">
  确认
</button>
\\\`\\\`\\\`

### progress - 进度条
- **功能**: 进度指示器
- **🚨 CRITICAL**: 自闭合标签
- **属性**: percent, show-info, stroke-width, color

\\\`\\\`\\\`html
<progress percent="{{progress}}" show-info stroke-width="4" color="#1aad19" />
\\\`\\\`\\\`

### rich-text - 富文本
- **功能**: HTML 内容渲染
- **属性**: nodes, selectable
- **限制**: 支持有限的 HTML 标签和属性

\\\`\\\`\\\`html
<rich-text nodes="{{htmlContent}}" selectable />
\\\`\\\`\\\`

## 🚨 自闭合标签规则

以下组件必须使用自闭合标签语法：

✅ **正确写法**:
\\\`\\\`\\\`html
<image src="{{src}}" />
<input placeholder="输入内容" />
<slider min="0" max="100" />
<switch checked="{{isOn}}" />
<progress percent="50" />
<canvas canvas-id="myCanvas" />
\\\`\\\`\\\`

❌ **错误写法**:
\\\`\\\`\\\`html
<image src="{{src}}"></image>
<input placeholder="输入内容"></input>
<slider min="0" max="100"></slider>
\\\`\\\`\\\`

## 高级组件使用模式

### 响应式列表渲染
\\\`\\\`\\\`html
<scroll-view class="list-container" scroll-y style="height: 100vh;">
  <view tt:for="{{items}}" tt:key="{{item.id}}" class="list-item">
    <image src="{{item.avatar}}" class="avatar" />
    <view class="content">
      <text class="title">{{item.title}}</text>
      <text class="description">{{item.description}}</text>
    </view>
  </view>
</scroll-view>
\\\`\\\`\\\`

### 条件渲染和状态管理
\\\`\\\`\\\`html
<view class="status-container">
  <view tt:if="{{loading}}">
    <text>加载中...</text>
  </view>
  <view tt:elif="{{error}}">
    <text>加载失败: {{error}}</text>
  </view>
  <view tt:else>
    <text>内容: {{content}}</text>
  </view>
</view>
\\\`\\\`\\\`

### 复杂交互组件
\\\`\\\`\\\`html
<view class="interactive-card" bindtap="onCardClick">
  <image src="{{imageUrl}}" class="card-image" />
  <view class="card-content">
    <text class="title">{{title}}</text>
    <text class="subtitle">{{subtitle}}</text>
    <view class="actions">
      <button size="mini" type="primary" bindtap="onLike">点赞</button>
      <button size="mini" type="default" bindtap="onShare">分享</button>
    </view>
  </view>
</view>
\\\`\\\`\\\`

这些组件构成了 Lynx 框架的完整组件生态系统，确保了跨平台的一致性和高性能的用户体验。
`;
  }
}

export const lynxComponentsBuilder = new LynxComponentsBuilder();