import { QueryContext, BuilderModule } from './types';
import { TTSS_STRICT_CONSTRAINTS } from '../prompts/TTSSStrictConstraints';
import { TTML_STRICT_CONSTRAINTS } from '../prompts/TTMLStrictConstraints';
import { LYNX_SYNTAX_ENFORCER } from '../prompts/LynxSyntaxEnforcer';

export class LynxConstraintCoreBuilder implements BuilderModule {
  build(context: QueryContext): string {
    return `
# ⚡ 第二阶段：Lynx技术实现规范 (Phase 2: Lynx Technical Implementation)

## 🚨 CRITICAL TECHNICAL CONSTRAINTS (ZERO TOLERANCE)

${TTSS_STRICT_CONSTRAINTS}

${TTML_STRICT_CONSTRAINTS}

${LYNX_SYNTAX_ENFORCER}

## MANDATORY FILE STRUCTURE
Every Lynx project requires exactly 5 files:

### 1. index.ttml (UI Structure)
- Semantic TTML component hierarchy using ONLY Lynx components
- Complete data binding syntax with \${{}\\} expressions
- Proper component nesting rules and accessibility considerations
- Event binding with ONLY correct Lynx syntax (bindtap, bindinput, etc.)

### 2. index.ttss (Style Design)
- Mobile-first RPX unit system (750rpx = screen width)
- Responsive design patterns optimized for multiple device sizes
- Performance-optimized CSS rules avoiding forbidden properties
- Font Awesome integration with mandatory @font-face declarations

### 3. index.js (Interaction Logic)
- Complete lifecycle management (onLoad, onShow, onReady, onHide, onUnload)
- Event handling and state management using this.setData() exclusively
- Network requests and asynchronous data processing with error handling
- Canvas initialization and LightChart integration for data visualization

### 4. index.json (Component Configuration)
- Component property definitions and default value specifications
- External component dependency declarations (lightcharts-canvas, etc.)
- Page configuration options and navigation parameters

### 5. lynx.config.json (Application Configuration)
- Global application settings and behavior configuration
- Navigation bar and window appearance customization

## COMPONENT SYSTEM REFERENCE (CRITICAL)

### Container Components
- **view**: Universal container (replaces ALL HTML div/span), supports flexbox, positioning, animations
- **scroll-view**: High-performance scrolling container, MUST have height/max-height property set
- **swiper**: Touch carousel with indicators, auto-play, loop support, and smooth transitions
- **swiper-item**: Individual carousel pages, must be direct children of swiper component
- **cover-view**: Overlay container for native components like video, map, live-player

### Text and Media Components  
- **text**: ONLY component for text content and icons, mandatory wrapper for ALL text display
- **image**: Self-closing image component (replaces img), supports lazy loading, multiple fit modes
- **video**: Video player with native controls, fullscreen, subtitle, and poster image support
- **audio**: Audio player with playlist, progress control, and background playback capabilities
- **canvas**: 2D drawing canvas for complex graphics, charts, and interactive visualizations

### Form and Input Components
- **form**: Form container for data submission, validation management, and field grouping
- **input**: Self-closing input field with types: text, password, number, idcard, digit
- **textarea**: Multi-line text input with auto-resize and character counting functionality
- **picker**: Single/multi-column selector with customizable options and formatting
- **picker-view**: Embedded picker without popup, for inline selection interfaces
- **slider**: Self-closing range slider for numeric value selection with step controls
- **switch**: Self-closing boolean toggle control with custom styling and animations
- **checkbox**: Self-closing multi-select option, must be used with checkbox-group
- **radio**: Self-closing single-select option, must be used with radio-group for mutual exclusion

### Navigation and Interaction Components
- **navigator**: Declarative page navigation with parameter passing and transition animations
- **button**: Interactive button component (though view+bindtap often preferred for custom styling)
- **progress**: Self-closing progress indicator with customizable styling and animation
- **rich-text**: HTML content renderer for formatted text display with limited HTML support

## EVENT SYSTEM SPECIFICATION (CRITICAL)

### Event Binding Syntax Rules (MANDATORY)
✅ CORRECT LYNX SYNTAX:
- bindtap="handleClick"           // Click/touch events
- bindlongpress="handleLongPress" // Long press events (500ms+)  
- bindinput="handleInput"         // Input field real-time changes
- bindchange="handleChange"       // Value change events (picker, switch, slider)
- bindscroll="handleScroll"       // Scroll position changes with scroll event object
- bindfocus="handleFocus"         // Input focus events
- bindblur="handleBlur"           // Input blur events

❌ FORBIDDEN SYNTAX (Causes Compilation Errors):
- bindtap="handleClick"          // React-style binding (NOT supported)
- onclick="handleClick"           // HTML-style events (NOT supported)
- @click="handleClick"            // Vue-style events (NOT supported)
- addEventListener("click", fn)   // JavaScript DOM events (NOT supported)

### Event Object Structure
- event.detail: Event-specific data and custom information payload
- event.currentTarget: Element that has the event binding (target element)
- event.target: Element that actually triggered the event (may be child element)
- event.touches: Touch point information for multi-touch gesture events
- event.changedTouches: Touch points that changed in this specific event

## DATA MANAGEMENT SYSTEM (CRITICAL)

### Component Architecture Pattern (MANDATORY)
\\\`\\\`\\\`javascript
Card({
  // Component state (reactive data) - ONLY place for UI state
  data() {
    return {
      // All reactive properties that trigger UI updates
      count: 0,
      items: [],
      user: null,
      loading: false,
      formData: {}
    };
  },
  
  // Lifecycle methods (MANDATORY for proper resource management)
  onLoad(options) {
    // Page initialization with URL parameters
    // Network requests and initial data loading
    // Setup component state and external dependencies
  },
  
  onShow() {
    // Page becomes visible (navigation, tab switch)
    // Refresh time-sensitive data if needed
    // Resume paused operations and animations
  },
  
  onReady() {
    // Page rendering complete, DOM queries safe
    // Canvas initialization and chart creation
    // Complex component setup and third-party integrations
  },
  
  onHide() {
    // Page hidden (navigation away, tab switch)
    // Pause resource-intensive operations
    // Save temporary user input and state
  },
  
  onUnload() {
    // Page destruction and cleanup (CRITICAL)
    // Clear timers, intervals, and event listeners
    // Destroy chart instances and canvas contexts
    // Release all external resources and prevent memory leaks
  },
  
  // Event handlers (directly in Card object)
  handleTap(event) {
    // MANDATORY: Use this.setData() for ALL state updates
    this.setData({
      count: this.data.count + 1
    });
  },
  
  handleInput(event) {
    // MANDATORY: Use optional chaining for event data access
    const value = event?.detail?.value || '';
    this.setData({ inputValue: value });
  }
});
\\\`\\\`\\\`

### Data Flow Patterns (MANDATORY)
- **State Updates**: ALWAYS use this.setData({ key: value }) - direct assignment won't trigger re-render
- **Data Access**: ALWAYS use optional chaining this.data?.property?.subProperty for safety
- **Async Operations**: Use proper promise chains or async/await with comprehensive error handling
- **Form Binding**: Implement two-way binding with bindinput events and this.setData patterns
`;
  }
}

export const lynxConstraintCoreBuilder = new LynxConstraintCoreBuilder();