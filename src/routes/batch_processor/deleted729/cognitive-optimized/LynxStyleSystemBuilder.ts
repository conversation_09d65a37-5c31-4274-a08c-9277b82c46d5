import { QueryContext, BuilderModule } from './types';

export class LynxStyleSystemBuilder implements BuilderModule {
  build(context: QueryContext): string {
    return `
# 🎨 LYNX 样式系统核心规范

## TTSS 核心系统

### 基础选择器支持
- **元素选择器**: view, text, image, scroll-view
- **类选择器**: .className
- **ID选择器**: #elementId
- **属性选择器**: [attribute="value"]
- **伪类选择器**: :hover, :active, :focus, :first-child, :last-child

### 高级选择器限制
- **后代选择器**: .parent .child ✅
- **子选择器**: .parent > .child ✅
- **相邻选择器**: .item + .item ✅
- **通用选择器**: * ⚠️ (性能影响，谨慎使用)

## 🚨 CRITICAL: 多类选择器严格禁止

### ❌ 绝对禁止的写法
\\\`\\\`\\\`css
.btn.primary { } /* 多类选择器 - 不支持 */
.card.active.selected { } /* 多类组合 - 不支持 */
.item.highlight { } /* 连续类名 - 不支持 */
\\\`\\\`\\\`

### ✅ 正确的替代方案
\\\`\\\`\\\`css
/* 使用单一类名 */
.btn-primary { }
.card-active-selected { }
.item-highlight { }

/* 或使用层级选择器 */
.btn.primary { } /* 改为 */ .btn .primary { }
.card .active.selected { } /* 改为 */ .card .active-selected { }
\\\`\\\`\\\`

## 禁止CSS属性详细清单

### 🚨 完全禁止的属性
- **Webkit前缀**: -webkit-*, -moz-*, -ms-*, -o-*
- **现代CSS滤镜**: filter, backdrop-filter
- **CSS Grid**: display: grid, grid-template-*, grid-area
- **CSS Mask**: mask, mask-image, mask-position
- **CSS Clip**: clip-path, clip-rule
- **CSS对象**: object-fit, object-position
- **CSS计数器**: counter-*, content
- **CSS变量**: --custom-property, var()

### ⚠️ 受限制的属性
- **Position**: sticky 不支持，使用 fixed 替代
- **Display**: table-* 系列不完全支持
- **Float**: 不推荐使用，使用 flexbox 替代
- **Clear**: 与 float 相关，不推荐

## 必须使用的正确属性

### 布局属性
\\\`\\\`\\\`css
.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
}
\\\`\\\`\\\`

### 尺寸属性
\\\`\\\`\\\`css
.element {
  width: 750rpx;
  height: 200rpx;
  min-width: 100rpx;
  max-width: 100%;
  min-height: 50rpx;
  max-height: 80vh;
}
\\\`\\\`\\\`

### 边距属性
\\\`\\\`\\\`css
.spaced {
  margin: 20rpx;
  margin-top: 10rpx;
  margin-right: 15rpx;
  margin-bottom: 10rpx;
  margin-left: 15rpx;
  padding: 16rpx;
  padding-top: 8rpx;
  padding-right: 12rpx;
  padding-bottom: 8rpx;
  padding-left: 12rpx;
}
\\\`\\\`\\\`

### 文字属性
\\\`\\\`\\\`css
.text {
  font-size: 28rpx;
  font-weight: 500;
  font-family: "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
  line-height: 1.4;
  color: #333333;
  text-align: center;
  text-decoration: none;
  letter-spacing: 0.5rpx;
}
\\\`\\\`\\\`

### 背景属性
\\\`\\\`\\\`css
.background {
  background-color: #ffffff;
  background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
\\\`\\\`\\\`

### Flexbox 布局
\\\`\\\`\\\`css
.flex-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  align-content: center;
}

.flex-item {
  flex: 1;
  flex-grow: 1;
  flex-shrink: 0;
  flex-basis: auto;
  align-self: center;
  order: 1;
}
\\\`\\\`\\\`

## RPX 单位系统

### 基础转换规则
- **设计稿基准**: 750rpx = 屏幕宽度
- **iPhone 6**: 750rpx = 375px (ratio 2:1)
- **iPhone 6 Plus**: 750rpx = 414px (ratio 1.8:1)
- **Android**: 动态适配不同屏幕密度

### 常用尺寸规范
\\\`\\\`\\\`css
/* 字体大小 */
.text-xs { font-size: 20rpx; }
.text-sm { font-size: 24rpx; }
.text-base { font-size: 28rpx; }
.text-lg { font-size: 32rpx; }
.text-xl { font-size: 36rpx; }
.text-2xl { font-size: 42rpx; }

/* 间距系统 */
.p-1 { padding: 4rpx; }
.p-2 { padding: 8rpx; }
.p-3 { padding: 12rpx; }
.p-4 { padding: 16rpx; }
.p-5 { padding: 20rpx; }
.p-6 { padding: 24rpx; }

/* 圆角系统 */
.rounded-sm { border-radius: 4rpx; }
.rounded { border-radius: 8rpx; }
.rounded-lg { border-radius: 16rpx; }
.rounded-xl { border-radius: 24rpx; }
.rounded-full { border-radius: 50%; }
\\\`\\\`\\\`

## 降级策略

### 背景模糊替代方案
\\\`\\\`\\\`css
/* 替代 backdrop-filter */
.blur-effect {
  position: relative;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.blur-effect::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}
\\\`\\\`\\\`

### 滤镜效果替代方案
\\\`\\\`\\\`css
/* 替代 filter: brightness() */
.bright-effect {
  background-color: rgba(255, 255, 255, 0.2);
  mix-blend-mode: overlay; /* 如果支持 */
}

/* 替代 filter: grayscale() */
.grayscale-effect {
  opacity: 0.6;
  background-color: #f0f0f0;
}
\\\`\\\`\\\`

### Grid 布局替代方案
\\\`\\\`\\\`css
/* 使用 Flexbox 模拟 Grid */
.grid-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.grid-item {
  flex: 0 0 calc(33.333% - 16rpx);
  margin-bottom: 16rpx;
}

.grid-item:nth-child(3n) {
  margin-right: 0;
}
\\\`\\\`\\\`

## Font Awesome 图标系统

### 强制配置
\\\`\\\`\\\`css
/* 必须在 TTSS 文件顶部声明 */
@font-face {
  font-family: 'font-awesome-icon';
  src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/webfonts/fa-solid-900.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

.fa-icon {
  font-family: 'font-awesome-icon';
  font-size: 32rpx;
  color: #333333;
}
\\\`\\\`\\\`

### 图标使用规范
\\\`\\\`\\\`css
.icon-home { font-family: 'font-awesome-icon'; }
.icon-user { font-family: 'font-awesome-icon'; }
.icon-settings { font-family: 'font-awesome-icon'; }

/* 不同尺寸 */
.icon-xs { font-size: 24rpx; }
.icon-sm { font-size: 28rpx; }
.icon-md { font-size: 32rpx; }
.icon-lg { font-size: 36rpx; }
.icon-xl { font-size: 40rpx; }
\\\`\\\`\\\`

## 性能优化建议

### 选择器优化
\\\`\\\`\\\`css
/* 高效选择器 */
.item { } /* 类选择器 - 最高效 */
#header { } /* ID选择器 - 高效 */
view { } /* 元素选择器 - 中等 */

/* 避免的选择器 */
* { } /* 通配符 - 性能差 */
.item view text { } /* 深层嵌套 - 性能较差 */
\\\`\\\`\\\`

### 重排重绘优化
\\\`\\\`\\\`css
/* 使用 transform 替代 position 变化 */
.animated {
  transform: translateX(100rpx);
  transition: transform 0.3s ease;
}

/* 使用 opacity 替代 visibility */
.fade {
  opacity: 0;
  transition: opacity 0.3s ease;
}
\\\`\\\`\\\`

### 内存优化
\\\`\\\`\\\`css
/* 避免复杂的动画 */
.simple-animation {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
\\\`\\\`\\\`

这些规范确保了 Lynx 应用的样式系统既强大又高效，能够在各种设备上提供一致的用户体验。
`;
  }
}

export const lynxStyleSystemBuilder = new LynxStyleSystemBuilder();