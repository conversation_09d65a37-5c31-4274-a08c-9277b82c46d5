import { QueryContext, BuilderModule } from './types';

export class TTSSStyleSystemBuilder implements BuilderModule {
  build(context: QueryContext): string {
    return `
# 🎨 TTSS 样式系统详细规范

## TTSS 样式系统基础

### 基础选择器
支持标准CSS选择器语法：

\\\`\\\`\\\`css
/* 元素选择器 */
view { display: flex; }
text { font-size: 28rpx; }
image { width: 100rpx; height: 100rpx; }

/* 类选择器 */
.container { padding: 20rpx; }
.title { font-weight: bold; }
.content { line-height: 1.6; }

/* ID选择器 */
#header { background-color: #ffffff; }
#footer { border-top: 1px solid #e0e0e0; }
\\\`\\\`\\\`

### 高级选择器限制

#### 🚨 不支持的选择器
- **多类选择器**: \`.btn.primary\` ❌
- **属性选择器**: \`[type="text"]\` ❌
- **通配符选择器**: \`*\` ❌ (性能问题)
- **伪元素选择器**: \`::before\`, \`::after\` ❌

#### ✅ 支持的选择器
- **后代选择器**: \`.container .item\` ✅
- **子选择器**: \`.list > .item\` ✅
- **相邻兄弟选择器**: \`.item + .item\` ✅
- **通用兄弟选择器**: \`.item ~ .item\` ✅

### CSS 属性使用规范

#### 🚨 禁止使用的属性
\\\`\\\`\\\`css
/* Webkit前缀属性 */
-webkit-transform: scale(1.2); /* ❌ 不支持 */
-webkit-transition: all 0.3s; /* ❌ 不支持 */
-webkit-appearance: none; /* ❌ 不支持 */

/* 现代CSS属性 */
backdrop-filter: blur(10px); /* ❌ 不支持 */
filter: brightness(1.2); /* ❌ 不支持 */
clip-path: polygon(50% 0%, 0% 100%, 100% 100%); /* ❌ 不支持 */
mask: url(#mask); /* ❌ 不支持 */

/* CSS Grid 布局 */
display: grid; /* ❌ 不支持 */
grid-template-columns: 1fr 1fr; /* ❌ 不支持 */
grid-gap: 20px; /* ❌ 不支持 */
\\\`\\\`\\\`

#### ✅ 推荐使用的属性
\\\`\\\`\\\`css
/* 布局属性 */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
position: relative;
top: 0;
left: 0;
width: 100%;
height: 100vh;

/* 尺寸属性 */
width: 750rpx;
height: 200rpx;
min-width: 100rpx;
max-width: 100%;
box-sizing: border-box;

/* 间距属性 */
margin: 20rpx;
padding: 16rpx;
border: 1px solid #e0e0e0;
border-radius: 8rpx;

/* 文本属性 */
font-size: 28rpx;
font-weight: 500;
line-height: 1.4;
color: #333333;
text-align: center;

/* 背景属性 */
background-color: #ffffff;
background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
background-size: cover;
background-position: center;

/* 变换动画 */
transform: translateX(100rpx) scale(1.1);
transition: transform 0.3s ease;
opacity: 0.8;
\\\`\\\`\\\`

## 单位与命名规范

### 单位系统
\\\`\\\`\\\`css
/* RPX单位 - 推荐使用 */
.element {
  width: 750rpx; /* 屏幕宽度 */
  height: 200rpx; /* 相对高度 */
  font-size: 28rpx; /* 字体大小 */
  margin: 20rpx; /* 外边距 */
  border-radius: 8rpx; /* 圆角 */
}

/* 像素单位 - 特殊情况 */
.border {
  border: 1px solid #e0e0e0; /* 1px 边框 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 阴影 */
}

/* 百分比单位 */
.percentage {
  width: 100%; /* 相对宽度 */
  height: 50%; /* 相对高度 */
}

/* 视口单位 */
.viewport {
  width: 100vw; /* 视口宽度 */
  height: 100vh; /* 视口高度 */
}
\\\`\\\`\\\`

### 命名规范
\\\`\\\`\\\`css
/* 小写字母和连字符 */
.user-profile { } /* ✅ 正确 */
.btn-primary { } /* ✅ 正确 */
.modal-content { } /* ✅ 正确 */

/* 避免的命名方式 */
.userProfile { } /* ❌ 驼峰命名 */
.btn_primary { } /* ❌ 下划线 */
.MODAL-CONTENT { } /* ❌ 大写字母 */
\\\`\\\`\\\`

## 图标系统 - Font Awesome 唯一方案

### 🚨 严格约束
- **唯一图标方案**: 只能使用 Font Awesome
- **禁止 Emoji**: 严禁使用任何 Emoji 表情符号
- **禁止替代方案**: 不允许使用其他图标库

### 字体配置
\\\`\\\`\\\`css
/* 必须在 TTSS 文件顶部声明 */
@font-face {
  font-family: 'font-awesome-icon';
  src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/webfonts/fa-solid-900.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

/* 图标基础样式 */
.fa-icon {
  font-family: 'font-awesome-icon';
  font-size: 32rpx;
  line-height: 1;
  font-weight: normal;
  font-style: normal;
  color: #333333;
}
\\\`\\\`\\\`

### 图标尺寸系统
\\\`\\\`\\\`css
/* 标准尺寸 */
.icon-xs { font-size: 24rpx; }
.icon-sm { font-size: 28rpx; }
.icon-md { font-size: 32rpx; }
.icon-lg { font-size: 36rpx; }
.icon-xl { font-size: 40rpx; }
.icon-2x { font-size: 48rpx; }
.icon-3x { font-size: 64rpx; }

/* 响应式图标 */
.icon-responsive {
  font-size: 4.27vw; /* 32rpx 在 750rpx 屏幕上 */
}
\\\`\\\`\\\`

### 图标状态样式
\\\`\\\`\\\`css
/* 不同状态的图标 */
.icon-active {
  color: #1890ff;
  transform: scale(1.1);
}

.icon-disabled {
  color: #d9d9d9;
  opacity: 0.6;
}

.icon-hover:hover {
  color: #40a9ff;
  transition: color 0.3s ease;
}

.icon-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
\\\`\\\`\\\`

## 响应式设计

### 媒体查询
\\\`\\\`\\\`css
/* 小屏幕适配 */
@media (max-width: 600rpx) {
  .container {
    padding: 10rpx;
  }
  
  .text {
    font-size: 24rpx;
  }
}

/* 大屏幕适配 */
@media (min-width: 1000rpx) {
  .container {
    padding: 30rpx;
  }
  
  .text {
    font-size: 32rpx;
  }
}
\\\`\\\`\\\`

### 弹性布局
\\\`\\\`\\\`css
/* 弹性网格系统 */
.flex-grid {
  display: flex;
  flex-wrap: wrap;
  margin: -10rpx;
}

.flex-col {
  flex: 1;
  min-width: 0;
  padding: 10rpx;
}

.flex-col-half {
  flex: 0 0 50%;
  max-width: 50%;
}

.flex-col-third {
  flex: 0 0 33.333%;
  max-width: 33.333%;
}

.flex-col-quarter {
  flex: 0 0 25%;
  max-width: 25%;
}
\\\`\\\`\\\`

## 性能优化

### 选择器优化
\\\`\\\`\\\`css
/* 高效选择器 */
.item { } /* 类选择器 - 最高效 */
#header { } /* ID选择器 - 高效 */
view { } /* 元素选择器 - 中等 */

/* 避免的选择器 */
.container .item .text { } /* 过深嵌套 - 低效 */
.item + .item + .item { } /* 复杂组合 - 低效 */
\\\`\\\`\\\`

### 重排重绘优化
\\\`\\\`\\\`css
/* 使用 transform 和 opacity 实现动画 */
.optimized-animation {
  transform: translateX(0);
  opacity: 1;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.optimized-animation.hidden {
  transform: translateX(100rpx);
  opacity: 0;
}
\\\`\\\`\\\`

### 内存使用优化
\\\`\\\`\\\`css
/* 避免复杂的阴影和渐变 */
.simple-shadow {
  box-shadow: 0 2px 8rpx rgba(0, 0, 0, 0.1);
}

.simple-gradient {
  background: linear-gradient(to bottom, #ffffff, #f5f5f5);
}

/* 使用 will-change 提示浏览器优化 */
.will-animate {
  will-change: transform, opacity;
}
\\\`\\\`\\\`

## 常见问题和解决方案

### 布局问题
\\\`\\\`\\\`css
/* 解决 flex 布局中的文本溢出 */
.flex-text {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 解决固定高度容器中的垂直居中 */
.center-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200rpx;
}
\\\`\\\`\\\`

### 兼容性问题
\\\`\\\`\\\`css
/* 使用 flexbox 替代 grid */
.grid-replacement {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.grid-item {
  flex: 0 0 calc(33.333% - 20rpx);
  margin-bottom: 20rpx;
}

/* 使用 transform 替代 position 动画 */
.position-animation {
  transform: translateX(0);
  transition: transform 0.3s ease;
}

.position-animation.moved {
  transform: translateX(100rpx);
}
\\\`\\\`\\\`

这些规范确保了 TTSS 样式系统的高效性和一致性，为 Lynx 应用提供了强大的样式解决方案。
`;
  }
}

export const ttssStyleSystemBuilder = new TTSSStyleSystemBuilder();