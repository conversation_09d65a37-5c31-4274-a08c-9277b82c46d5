/**
 * 认知优化Prompt系统类型定义
 */

export interface QueryContext {
  type: 'educational' | 'search_information' | 'mixed';
  complexity: 'simple' | 'medium' | 'complex';
  creativityWeight: number; // 0-1之间，动态调整创意权重
  constraintWeight: number; // 0-1之间，动态调整约束权重
  primaryGoal: 'learning' | 'information_seeking' | 'decision_making';
  isLightChartNeeded: boolean;
  isFontAwesomeNeeded: boolean;
  requiresCanvas: boolean;
  requiresAnimation: boolean;
  requiresScroll: boolean;
}

export interface BuilderModule {
  build(context: QueryContext, query?: string): string;
}

export interface CognitiveStats {
  totalTokens: number;
  creativityTokens: number;
  constraintTokens: number;
  optimizationRatio: number;
  moduleBreakdown: {
    [key: string]: number;
  };
}