<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Awesome 精确测试</title>
    <style>
        @font-face {
            font-family: font-awesome-icon;
            src: url('https://lf3-static.bytednsdoc.com/obj/eden-cn/lrbseh7nuptps/search/fa-solid-900.ttf');
        }
        
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        
        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .range-section {
            background: white;
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .range-header {
            background: #007bff;
            color: white;
            padding: 15px;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
            padding: 15px;
        }
        
        .icon-test {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
            min-height: 90px;
            justify-content: center;
        }
        
        .icon {
            font-family: font-awesome-icon;
            font-size: 24px;
            margin-bottom: 6px;
            color: #333;
            line-height: 1;
        }
        
        .icon-code {
            font-size: 11px;
            color: #666;
            font-family: monospace;
            margin-bottom: 4px;
        }
        
        .icon-status {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .working {
            background-color: #d4edda !important;
            border-color: #28a745 !important;
        }
        
        .working .icon-status {
            background: #28a745;
            color: white;
        }
        
        .broken {
            background-color: #f8d7da !important;
            border-color: #dc3545 !important;
        }
        
        .broken .icon-status {
            background: #dc3545;
            color: white;
        }
        
        .hidden {
            display: none !important;
        }
        
        .collapsed .icon-grid {
            display: none;
        }
        
        .reference-icon {
            font-family: font-awesome-icon;
            font-size: 24px;
            color: #333;
            display: inline-block;
            margin: 0 5px;
        }
        
        .test-reference {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Font Awesome 6.7.2 精确测试</h1>
        <p>使用更准确的检测方法，避免误判</p>
        
        <div class="test-reference">
            <strong>参考图标（已知可用）:</strong>
            <span class="reference-icon">&#xf015;</span> f015 (home)
            <span class="reference-icon">&#xf007;</span> f007 (user)
            <span class="reference-icon">&#xf013;</span> f013 (cog)
            <span class="reference-icon">&#xf005;</span> f005 (star)
            <br>
            <strong>参考图标（已知不可用）:</strong>
            <span class="reference-icon">&#xf0f6;</span> f0f6 (file-text-o)
            <span class="reference-icon">&#xf500;</span> f500 (高编码)
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="startAccurateTest()">开始精确测试</button>
            <button class="btn btn-success" onclick="showWorking()">只显示可用</button>
            <button class="btn btn-danger" onclick="showBroken()">只显示不可用</button>
            <button class="btn btn-primary" onclick="exportResults()">导出结果</button>
        </div>
        
        <div class="stats" id="stats">
            点击"开始精确测试"开始检测...
        </div>
    </div>
    
    <div id="results"></div>
    
    <script>
        let testResults = {
            total: 0,
            working: 0,
            broken: 0,
            ranges: {},
            workingIcons: [],
            brokenIcons: []
        };
        
        // 已知的参考图标
        const knownWorking = ['f015', 'f007', 'f013', 'f005', 'f080', 'f017', 'f0ad'];
        const knownBroken = ['f0f6', 'f500', 'f700', 'f099', 'f09a'];
        
        function generateTestGrid() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';
            
            // 重点测试范围：f000-f2ff
            const ranges = ['f0xx', 'f1xx', 'f2xx'];
            
            ranges.forEach(range => {
                const rangeDiv = document.createElement('div');
                rangeDiv.className = 'range-section';
                rangeDiv.innerHTML = `
                    <div class="range-header" onclick="toggleRange('${range}')">
                        <span>${range} 范围</span>
                        <span class="range-stats" id="stats-${range}">0/0</span>
                    </div>
                    <div class="icon-grid" id="grid-${range}"></div>
                `;
                resultsDiv.appendChild(rangeDiv);
                
                const grid = document.getElementById(`grid-${range}`);
                const baseCode = parseInt(range.charAt(1), 16) * 256 + 0xf000;
                
                // 每16个图标为一组，便于观察
                for (let j = 0; j < 256; j += 16) {
                    for (let k = 0; k < 16 && (j + k) < 256; k++) {
                        const code = baseCode + j + k;
                        const hexCode = code.toString(16);
                        
                        const iconDiv = document.createElement('div');
                        iconDiv.className = 'icon-test';
                        iconDiv.innerHTML = `
                            <span class="icon">&#x${hexCode};</span>
                            <div class="icon-code">${hexCode}</div>
                            <div class="icon-status">检测中</div>
                        `;
                        iconDiv.dataset.code = hexCode;
                        iconDiv.dataset.range = range;
                        grid.appendChild(iconDiv);
                    }
                }
                
                testResults.ranges[range] = { working: 0, broken: 0 };
            });
        }
        
        function startAccurateTest() {
            generateTestGrid();
            
            setTimeout(() => {
                runAccurateTest();
            }, 500); // 等待字体加载
        }
        
        function runAccurateTest() {
            const icons = document.querySelectorAll('.icon-test');
            testResults.total = icons.length;
            testResults.working = 0;
            testResults.broken = 0;
            testResults.workingIcons = [];
            testResults.brokenIcons = [];
            
            // 重置范围统计
            Object.keys(testResults.ranges).forEach(range => {
                testResults.ranges[range] = { working: 0, broken: 0 };
            });
            
            // 创建测试canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 32;
            canvas.height = 32;
            
            // 获取空白字符的像素数据作为基准
            ctx.font = '24px font-awesome-icon';
            ctx.fillStyle = '#000';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.clearRect(0, 0, 32, 32);
            ctx.fillText(String.fromCharCode(0xf999), 16, 16); // 不存在的字符
            const emptyData = ctx.getImageData(0, 0, 32, 32);
            const emptyPixels = Array.from(emptyData.data);
            
            icons.forEach((iconDiv, index) => {
                const code = iconDiv.dataset.code;
                const range = iconDiv.dataset.range;
                const statusEl = iconDiv.querySelector('.icon-status');
                
                // 使用已知结果快速判断
                if (knownWorking.includes(code)) {
                    markAsWorking(iconDiv, code, range, statusEl);
                    return;
                }
                
                if (knownBroken.includes(code)) {
                    markAsBroken(iconDiv, code, range, statusEl);
                    return;
                }
                
                // Canvas像素检测
                ctx.clearRect(0, 0, 32, 32);
                const char = String.fromCharCode(parseInt(code, 16));
                ctx.fillText(char, 16, 16);
                
                const imageData = ctx.getImageData(0, 0, 32, 32);
                const pixels = Array.from(imageData.data);
                
                // 比较像素差异
                let pixelDiff = 0;
                for (let i = 0; i < pixels.length; i++) {
                    if (pixels[i] !== emptyPixels[i]) {
                        pixelDiff++;
                    }
                }
                
                // 如果有足够的像素差异，认为图标存在
                if (pixelDiff > 50) {
                    markAsWorking(iconDiv, code, range, statusEl);
                } else {
                    markAsBroken(iconDiv, code, range, statusEl);
                }
            });
            
            updateFinalStats();
        }
        
        function markAsWorking(iconDiv, code, range, statusEl) {
            iconDiv.classList.add('working');
            statusEl.textContent = '可用';
            testResults.working++;
            testResults.ranges[range].working++;
            testResults.workingIcons.push(code);
        }
        
        function markAsBroken(iconDiv, code, range, statusEl) {
            iconDiv.classList.add('broken');
            statusEl.textContent = '不可用';
            testResults.broken++;
            testResults.ranges[range].broken++;
            testResults.brokenIcons.push(code);
        }
        
        function updateFinalStats() {
            const stats = document.getElementById('stats');
            const workingPercent = ((testResults.working / testResults.total) * 100).toFixed(1);
            
            stats.innerHTML = `
                <strong>测试完成!</strong><br>
                <strong>✅ 可用图标:</strong> ${testResults.working} (${workingPercent}%)<br>
                <strong>❌ 不可用图标:</strong> ${testResults.broken}<br>
                <strong>各范围统计:</strong><br>
                ${Object.entries(testResults.ranges).map(([range, data]) => 
                    `${range}: ✅${data.working} ❌${data.broken}`
                ).join(' | ')}
            `;
            
            // 更新范围统计
            Object.entries(testResults.ranges).forEach(([range, data]) => {
                document.getElementById(`stats-${range}`).textContent = 
                    `✅${data.working} ❌${data.broken}`;
            });
            
            console.log('精确测试结果:', testResults);
        }
        
        function toggleRange(range) {
            const rangeDiv = document.querySelector(`#grid-${range}`).parentElement;
            rangeDiv.classList.toggle('collapsed');
        }
        
        function showWorking() {
            document.querySelectorAll('.icon-test').forEach(icon => {
                if (icon.classList.contains('working')) {
                    icon.classList.remove('hidden');
                } else {
                    icon.classList.add('hidden');
                }
            });
        }
        
        function showBroken() {
            document.querySelectorAll('.icon-test').forEach(icon => {
                if (icon.classList.contains('broken')) {
                    icon.classList.remove('hidden');
                } else {
                    icon.classList.add('hidden');
                }
            });
        }
        
        function exportResults() {
            const results = {
                summary: {
                    total: testResults.total,
                    working: testResults.working,
                    broken: testResults.broken,
                    workingPercent: ((testResults.working / testResults.total) * 100).toFixed(1)
                },
                ranges: testResults.ranges,
                workingIcons: testResults.workingIcons.sort(),
                brokenIcons: testResults.brokenIcons.sort()
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'fontawesome-accurate-results.json';
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
