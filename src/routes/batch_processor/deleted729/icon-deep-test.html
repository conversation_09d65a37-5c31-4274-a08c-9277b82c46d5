<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Awesome 深度测试 - 易混淆图标</title>
    <style>
        @font-face {
            font-family: font-awesome-icon;
            src: url('https://lf3-static.bytednsdoc.com/obj/eden-cn/lrbseh7nuptps/search/fa-solid-900.ttf');
        }
        
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        .icon-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .icon-test {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-width: 100px;
            background: #fafafa;
        }
        
        .icon {
            font-family: font-awesome-icon;
            font-size: 24px;
            margin-bottom: 5px;
            color: #333;
        }
        
        .icon-code {
            font-size: 10px;
            color: #666;
            font-family: monospace;
            margin-bottom: 3px;
        }
        
        .icon-name {
            font-size: 11px;
            color: #333;
            text-align: center;
        }
        
        .comparison {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .vs {
            font-weight: bold;
            color: #dc3545;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>Font Awesome 深度测试 - Claude4易混淆图标</h1>
    
    <!-- FA5过时图标测试 -->
    <div class="test-section">
        <div class="section-title">FA5过时图标测试（带-o后缀）</div>
        <div class="warning">这些图标在FA6中已被移除或重命名，Claude4经常误用</div>
        
        <div class="comparison">
            <div class="icon-test">
                <span class="icon">&#xf0f6;</span>
                <div class="icon-code">&#xf0f6;</div>
                <div class="icon-name">file-text-o (FA5)</div>
            </div>
            <span class="vs">VS</span>
            <div class="icon-test">
                <span class="icon">&#xf15c;</span>
                <div class="icon-code">&#xf15c;</div>
                <div class="icon-name">file-text (FA6)</div>
            </div>
        </div>
        
        <div class="comparison">
            <div class="icon-test">
                <span class="icon">&#xf0f7;</span>
                <div class="icon-code">&#xf0f7;</div>
                <div class="icon-name">building-o (FA5)</div>
            </div>
            <span class="vs">VS</span>
            <div class="icon-test">
                <span class="icon">&#xf1ad;</span>
                <div class="icon-code">&#xf1ad;</div>
                <div class="icon-name">building (FA6)</div>
            </div>
        </div>
        
        <div class="comparison">
            <div class="icon-test">
                <span class="icon">&#xf133;</span>
                <div class="icon-code">&#xf133;</div>
                <div class="icon-name">calendar-o (FA5)</div>
            </div>
            <span class="vs">VS</span>
            <div class="icon-test">
                <span class="icon">&#xf073;</span>
                <div class="icon-code">&#xf073;</div>
                <div class="icon-name">calendar (FA6)</div>
            </div>
        </div>
        
        <div class="comparison">
            <div class="icon-test">
                <span class="icon">&#xf147;</span>
                <div class="icon-code">&#xf147;</div>
                <div class="icon-name">folder-o (FA5)</div>
            </div>
            <span class="vs">VS</span>
            <div class="icon-test">
                <span class="icon">&#xf07b;</span>
                <div class="icon-code">&#xf07b;</div>
                <div class="icon-name">folder (FA6)</div>
            </div>
        </div>
        
        <div class="comparison">
            <div class="icon-test">
                <span class="icon">&#xf10c;</span>
                <div class="icon-code">&#xf10c;</div>
                <div class="icon-name">circle-o (FA5)</div>
            </div>
            <span class="vs">VS</span>
            <div class="icon-test">
                <span class="icon">&#xf111;</span>
                <div class="icon-code">&#xf111;</div>
                <div class="icon-name">circle (FA6)</div>
            </div>
        </div>
    </div>
    
    <!-- 易混淆的相似图标 -->
    <div class="test-section">
        <div class="section-title">易混淆的相似图标</div>
        <div class="warning">Claude4经常在这些语义相似的图标间选择错误</div>
        
        <div class="comparison">
            <div class="icon-test">
                <span class="icon">&#xf067;</span>
                <div class="icon-code">&#xf067;</div>
                <div class="icon-name">plus</div>
            </div>
            <span class="vs">VS</span>
            <div class="icon-test">
                <span class="icon">&#xf055;</span>
                <div class="icon-code">&#xf055;</div>
                <div class="icon-name">plus-circle</div>
            </div>
        </div>
        
        <div class="comparison">
            <div class="icon-test">
                <span class="icon">&#xf068;</span>
                <div class="icon-code">&#xf068;</div>
                <div class="icon-name">minus</div>
            </div>
            <span class="vs">VS</span>
            <div class="icon-test">
                <span class="icon">&#xf056;</span>
                <div class="icon-code">&#xf056;</div>
                <div class="icon-name">minus-circle</div>
            </div>
        </div>
        
        <div class="comparison">
            <div class="icon-test">
                <span class="icon">&#xf00c;</span>
                <div class="icon-code">&#xf00c;</div>
                <div class="icon-name">check</div>
            </div>
            <span class="vs">VS</span>
            <div class="icon-test">
                <span class="icon">&#xf058;</span>
                <div class="icon-code">&#xf058;</div>
                <div class="icon-name">check-circle</div>
            </div>
        </div>
        
        <div class="comparison">
            <div class="icon-test">
                <span class="icon">&#xf00d;</span>
                <div class="icon-code">&#xf00d;</div>
                <div class="icon-name">times/xmark</div>
            </div>
            <span class="vs">VS</span>
            <div class="icon-test">
                <span class="icon">&#xf057;</span>
                <div class="icon-code">&#xf057;</div>
                <div class="icon-name">times-circle</div>
            </div>
        </div>
    </div>
    
    <!-- 品牌图标测试 -->
    <div class="test-section">
        <div class="section-title">品牌图标测试</div>
        <div class="error">这些品牌图标可能在Free版本中受限，Claude4应避免使用</div>
        
        <div class="icon-row">
            <div class="icon-test">
                <span class="icon">&#xf099;</span>
                <div class="icon-code">&#xf099;</div>
                <div class="icon-name">twitter</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf09a;</span>
                <div class="icon-code">&#xf09a;</div>
                <div class="icon-name">facebook</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf16d;</span>
                <div class="icon-code">&#xf16d;</div>
                <div class="icon-name">instagram</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf0e1;</span>
                <div class="icon-code">&#xf0e1;</div>
                <div class="icon-name">linkedin</div>
            </div>
        </div>
    </div>
    
    <!-- 高编码范围测试 -->
    <div class="test-section">
        <div class="section-title">高编码范围测试（可能为Pro版本）</div>
        <div class="error">f200以上的编码需要特别验证</div>
        
        <div class="icon-row">
            <div class="icon-test">
                <span class="icon">&#xf200;</span>
                <div class="icon-code">&#xf200;</div>
                <div class="icon-name">chart-pie</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf201;</span>
                <div class="icon-code">&#xf201;</div>
                <div class="icon-name">chart-line</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf500;</span>
                <div class="icon-code">&#xf500;</div>
                <div class="icon-name">f500范围</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf700;</span>
                <div class="icon-code">&#xf700;</div>
                <div class="icon-name">f700范围</div>
            </div>
        </div>
    </div>
    
    <script>
        // 检测图标渲染效果并标记
        window.addEventListener('load', function() {
            const icons = document.querySelectorAll('.icon');
            icons.forEach(icon => {
                const rect = icon.getBoundingClientRect();
                const container = icon.parentElement;
                
                // 如果图标宽度太小或显示为方块，标记为失败
                if (rect.width < 15) {
                    container.style.backgroundColor = '#f8d7da';
                    container.style.borderColor = '#dc3545';
                    
                    const nameEl = container.querySelector('.icon-name');
                    nameEl.style.color = '#721c24';
                    nameEl.textContent += ' ❌';
                } else {
                    container.style.backgroundColor = '#d4edda';
                    container.style.borderColor = '#28a745';
                    
                    const nameEl = container.querySelector('.icon-name');
                    nameEl.style.color = '#155724';
                    nameEl.textContent += ' ✅';
                }
            });
        });
    </script>
</body>
</html>
