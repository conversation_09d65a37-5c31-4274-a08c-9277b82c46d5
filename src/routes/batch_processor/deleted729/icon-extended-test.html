<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Awesome 扩展测试 - 边缘案例</title>
    <style>
        @font-face {
            font-family: font-awesome-icon;
            src: url('https://lf3-static.bytednsdoc.com/obj/eden-cn/lrbseh7nuptps/search/fa-solid-900.ttf');
        }
        
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .icon-test {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
            transition: all 0.3s ease;
        }
        
        .icon {
            font-family: font-awesome-icon;
            font-size: 28px;
            margin-bottom: 8px;
            color: #333;
            line-height: 1;
        }
        
        .icon-code {
            font-size: 11px;
            color: #666;
            font-family: monospace;
            margin-bottom: 4px;
        }
        
        .icon-name {
            font-size: 12px;
            color: #333;
            text-align: center;
            font-weight: 500;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .success {
            background-color: #d4edda !important;
            border-color: #28a745 !important;
        }
        
        .danger {
            background-color: #f8d7da !important;
            border-color: #dc3545 !important;
        }
        
        .range-test {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .range-label {
            font-weight: bold;
            min-width: 100px;
        }
    </style>
</head>
<body>
    <h1>Font Awesome 扩展测试 - Claude4边缘案例</h1>
    
    <!-- 编码边界测试 -->
    <div class="test-section">
        <div class="section-title">编码边界范围测试</div>
        <div class="info">测试不同编码范围的可用性，找出Free版本的边界</div>
        
        <div class="range-test">
            <span class="range-label">f1xx范围:</span>
            <div class="icon-test">
                <span class="icon">&#xf1c0;</span>
                <div class="icon-code">f1c0</div>
                <div class="icon-name">database</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf1c8;</span>
                <div class="icon-code">f1c8</div>
                <div class="icon-name">f1c8</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf1f0;</span>
                <div class="icon-code">f1f0</div>
                <div class="icon-name">f1f0</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf1ff;</span>
                <div class="icon-code">f1ff</div>
                <div class="icon-name">f1ff</div>
            </div>
        </div>
        
        <div class="range-test">
            <span class="range-label">f2xx范围:</span>
            <div class="icon-test">
                <span class="icon">&#xf200;</span>
                <div class="icon-code">f200</div>
                <div class="icon-name">chart-pie</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf240;</span>
                <div class="icon-code">f240</div>
                <div class="icon-name">f240</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf280;</span>
                <div class="icon-code">f280</div>
                <div class="icon-name">f280</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf2ff;</span>
                <div class="icon-code">f2ff</div>
                <div class="icon-name">f2ff</div>
            </div>
        </div>
        
        <div class="range-test">
            <span class="range-label">f3xx范围:</span>
            <div class="icon-test">
                <span class="icon">&#xf300;</span>
                <div class="icon-code">f300</div>
                <div class="icon-name">f300</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf350;</span>
                <div class="icon-code">f350</div>
                <div class="icon-name">f350</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf3ff;</span>
                <div class="icon-code">f3ff</div>
                <div class="icon-name">f3ff</div>
            </div>
        </div>
    </div>
    
    <!-- 常见误用图标测试 -->
    <div class="test-section">
        <div class="section-title">Claude4常见误用图标测试</div>
        <div class="warning">这些是Claude4经常错误选择的图标编码</div>
        
        <div class="icon-grid">
            <!-- 箭头类图标 -->
            <div class="icon-test">
                <span class="icon">&#xf060;</span>
                <div class="icon-code">f060</div>
                <div class="icon-name">arrow-left</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf061;</span>
                <div class="icon-code">f061</div>
                <div class="icon-name">arrow-right</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf062;</span>
                <div class="icon-code">f062</div>
                <div class="icon-name">arrow-up</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf063;</span>
                <div class="icon-code">f063</div>
                <div class="icon-name">arrow-down</div>
            </div>
            
            <!-- 媒体控制图标 -->
            <div class="icon-test">
                <span class="icon">&#xf04b;</span>
                <div class="icon-code">f04b</div>
                <div class="icon-name">play</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf04c;</span>
                <div class="icon-code">f04c</div>
                <div class="icon-name">pause</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf04d;</span>
                <div class="icon-code">f04d</div>
                <div class="icon-name">stop</div>
            </div>
            
            <!-- 编辑类图标 -->
            <div class="icon-test">
                <span class="icon">&#xf044;</span>
                <div class="icon-code">f044</div>
                <div class="icon-name">edit</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf1f8;</span>
                <div class="icon-code">f1f8</div>
                <div class="icon-name">trash</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf0c7;</span>
                <div class="icon-code">f0c7</div>
                <div class="icon-name">save</div>
            </div>
        </div>
    </div>
    
    <!-- 特殊字符和符号测试 -->
    <div class="test-section">
        <div class="section-title">特殊字符和符号测试</div>
        <div class="info">测试一些特殊的符号和字符图标</div>
        
        <div class="icon-grid">
            <div class="icon-test">
                <span class="icon">&#xf12a;</span>
                <div class="icon-code">f12a</div>
                <div class="icon-name">exclamation</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf128;</span>
                <div class="icon-code">f128</div>
                <div class="icon-name">question</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf06a;</span>
                <div class="icon-code">f06a</div>
                <div class="icon-name">exclamation-circle</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf059;</span>
                <div class="icon-code">f059</div>
                <div class="icon-name">question-circle</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf071;</span>
                <div class="icon-code">f071</div>
                <div class="icon-name">warning</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf05a;</span>
                <div class="icon-code">f05a</div>
                <div class="icon-name">info-circle</div>
            </div>
        </div>
    </div>
    
    <!-- 网络和通信图标测试 -->
    <div class="test-section">
        <div class="section-title">网络和通信图标测试</div>
        <div class="warning">这些图标Claude4经常需要但可能选错</div>
        
        <div class="icon-grid">
            <div class="icon-test">
                <span class="icon">&#xf0e0;</span>
                <div class="icon-code">f0e0</div>
                <div class="icon-name">envelope</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf095;</span>
                <div class="icon-code">f095</div>
                <div class="icon-name">phone</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf0c0;</span>
                <div class="icon-code">f0c0</div>
                <div class="icon-name">users</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf0ac;</span>
                <div class="icon-code">f0ac</div>
                <div class="icon-name">globe</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf0c1;</span>
                <div class="icon-code">f0c1</div>
                <div class="icon-name">link</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf127;</span>
                <div class="icon-code">f127</div>
                <div class="icon-name">unlink</div>
            </div>
        </div>
    </div>
    
    <!-- 高风险编码测试 -->
    <div class="test-section">
        <div class="section-title">高风险编码范围测试</div>
        <div class="warning">测试可能为Pro版本的高编码图标</div>
        
        <div class="icon-grid">
            <div class="icon-test">
                <span class="icon">&#xf400;</span>
                <div class="icon-code">f400</div>
                <div class="icon-name">f400</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf450;</span>
                <div class="icon-code">f450</div>
                <div class="icon-name">f450</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf4ff;</span>
                <div class="icon-code">f4ff</div>
                <div class="icon-name">f4ff</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf600;</span>
                <div class="icon-code">f600</div>
                <div class="icon-name">f600</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf650;</span>
                <div class="icon-code">f650</div>
                <div class="icon-name">f650</div>
            </div>
            <div class="icon-test">
                <span class="icon">&#xf6ff;</span>
                <div class="icon-code">f6ff</div>
                <div class="icon-name">f6ff</div>
            </div>
        </div>
    </div>
    
    <script>
        // 检测图标渲染效果并分类标记
        window.addEventListener('load', function() {
            const icons = document.querySelectorAll('.icon');
            let results = {
                working: 0,
                broken: 0,
                ranges: {}
            };
            
            icons.forEach(icon => {
                const rect = icon.getBoundingClientRect();
                const container = icon.parentElement;
                const codeEl = container.querySelector('.icon-code');
                const code = codeEl.textContent;
                
                // 获取编码范围
                const range = code.substring(0, 2);
                if (!results.ranges[range]) {
                    results.ranges[range] = { working: 0, broken: 0 };
                }
                
                // 检测渲染状态
                if (rect.width < 15) {
                    container.classList.add('danger');
                    const nameEl = container.querySelector('.icon-name');
                    nameEl.textContent += ' ❌';
                    nameEl.style.color = '#721c24';
                    results.broken++;
                    results.ranges[range].broken++;
                } else {
                    container.classList.add('success');
                    const nameEl = container.querySelector('.icon-name');
                    nameEl.textContent += ' ✅';
                    nameEl.style.color = '#155724';
                    results.working++;
                    results.ranges[range].working++;
                }
            });
            
            // 显示统计结果
            console.log('图标测试结果:', results);
            
            // 在页面顶部添加统计信息
            const summary = document.createElement('div');
            summary.style.cssText = 'background: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px; font-family: monospace;';
            summary.innerHTML = `
                <strong>测试统计:</strong><br>
                ✅ 可用: ${results.working} 个<br>
                ❌ 不可用: ${results.broken} 个<br>
                <strong>各范围统计:</strong><br>
                ${Object.entries(results.ranges).map(([range, data]) => 
                    `${range}xx: ✅${data.working} ❌${data.broken}`
                ).join('<br>')}
            `;
            document.body.insertBefore(summary, document.body.firstChild.nextSibling);
        });
    </script>
</body>
</html>
