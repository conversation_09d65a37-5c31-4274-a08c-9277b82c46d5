<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Awesome 全量图标测试</title>
    <style>
        @font-face {
            font-family: font-awesome-icon;
            src: url('https://lf3-static.bytednsdoc.com/obj/eden-cn/lrbseh7nuptps/search/fa-solid-900.ttf');
        }
        
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
            margin: 0;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-info { background: #17a2b8; color: white; }
        
        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .range-section {
            background: white;
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .range-header {
            background: #007bff;
            color: white;
            padding: 15px;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .range-stats {
            font-size: 12px;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 8px;
            padding: 15px;
        }
        
        .icon-test {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fafafa;
            transition: all 0.2s ease;
            min-height: 80px;
            justify-content: center;
        }
        
        .icon {
            font-family: font-awesome-icon;
            font-size: 20px;
            margin-bottom: 4px;
            color: #333;
            line-height: 1;
        }
        
        .icon-code {
            font-size: 9px;
            color: #666;
            font-family: monospace;
        }
        
        .working {
            background-color: #d4edda !important;
            border-color: #28a745 !important;
        }
        
        .broken {
            background-color: #f8d7da !important;
            border-color: #dc3545 !important;
        }
        
        .hidden {
            display: none !important;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            font-size: 18px;
            color: #666;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Font Awesome 6.7.2 全量图标测试 - 精确特征匹配版</h1>
        <p>测试范围：f000-f8ff (共2304个图标编码) | 基于精确数据特征的错误图标检测算法</p>
        <div style="background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; font-size: 14px;">
            <strong>🎯 精确匹配策略：</strong> 基于您提供的失效图标精确数据（总像素344，边框0，X标记36），使用严格的特征匹配。
        </div>

        <div style="background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; font-size: 14px;">
            <strong>🔍 错误图标判断条件（精确匹配）：</strong><br>
            • ✅ 特定尺寸：像素总数 300-400个<br>
            • ✅ 无边框：边框像素 < 5%<br>
            • ✅ 特定X标记：X标记像素 30-50个且占比8%-15%<br>
            • ✅ 同时满足以上三个条件 = 错误图标
        </div>

        <div style="background: #e7f3ff; padding: 10px; border-radius: 4px; margin: 10px 0; font-size: 14px;">
            <strong>📊 检测原理：</strong> 根据您提供的错误图标样式（矩形框内有X标记），算法会分析像素分布模式，
            识别出具有边框+对角线特征的占位符图标，与真实的Font Awesome图标区分开来。
        </div>
        
        <div class="controls">
            <button type="button" class="btn btn-primary" onclick="startTest()">开始全量测试</button>
            <button type="button" class="btn btn-success" onclick="showWorking()">只显示可用图标</button>
            <button type="button" class="btn btn-danger" onclick="showBroken()">只显示不可用图标</button>
            <button type="button" class="btn btn-warning" onclick="showAll()">显示全部</button>
            <button type="button" class="btn btn-primary" onclick="exportResults()">导出结果</button>
        </div>

        <div class="controls" style="margin-top: 10px;">
            <strong>快速验证范围：</strong>
            <button type="button" class="btn btn-info" onclick="testRange('f000', 'f07f')">测试 f000-f07f</button>
            <button type="button" class="btn btn-info" onclick="testRange('f080', 'f0ff')">测试 f080-f0ff</button>
            <button type="button" class="btn btn-info" onclick="testRange('f100', 'f1ff')">测试 f100-f1ff</button>
            <button type="button" class="btn btn-info" onclick="testRange('f200', 'f2ff')">测试 f200-f2ff</button>
        </div>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div class="stats" id="stats">
            点击"开始全量测试"开始检测...
        </div>
    </div>
    
    <div id="loading" class="loading hidden">
        正在生成图标测试网格...
    </div>
    
    <div id="results"></div>
    
    <script>
        let testResults = {
            total: 0,
            working: 0,
            broken: 0,
            ranges: {},
            workingIcons: [],
            brokenIcons: []
        };

        // 基于你的截图分析的精确禁用范围
        const DISABLED_RANGES = [
            // f0xx 范围：从截图看，f080+ 开始出现矩形框
            { start: 0xf080, end: 0xf0ff },

            // f1xx 范围：从截图看，几乎全部是矩形框，只有极少数绿色
            { start: 0xf100, end: 0xf1ff },

            // f2xx 范围：从截图看，几乎全部是矩形框
            { start: 0xf200, end: 0xf2ff },

            // f3xx 范围：从截图看，全部是矩形框
            { start: 0xf300, end: 0xf3ff },

            // f4xx 范围：从截图看，全部是矩形框
            { start: 0xf400, end: 0xf4ff },

            // f5xx 范围：从截图看，全部是矩形框
            { start: 0xf500, end: 0xf5ff },

            // f6xx 范围：从截图看，全部是矩形框
            { start: 0xf600, end: 0xf6ff },

            // f7xx 范围：从截图看，全部是矩形框
            { start: 0xf700, end: 0xf7ff },

            // f8xx 范围：从截图看，全部是矩形框
            { start: 0xf800, end: 0xf8ff },
        ];

        // 特殊的可用图标例外（即使在禁用范围内，但从截图看是绿色可用的）
        const AVAILABLE_EXCEPTIONS = [
            // 根据截图中的绿色图标，这里可以添加例外
            // 例如：'f1c0', 'f1f0' 等在f1xx范围内但显示为绿色的图标
        ];

        // 检查图标是否在禁用范围内
        function isIconInDisabledRange(codeNum) {
            const code = codeNum.toString(16).padStart(4, '0');

            // 检查是否在例外列表中（即使在禁用范围内也可用）
            if (AVAILABLE_EXCEPTIONS.includes(code)) {
                return false;
            }

            // 检查是否在禁用范围内
            return DISABLED_RANGES.some(range =>
                codeNum >= range.start && codeNum <= range.end
            );
        }

        // 简化且精确的错误图标检测
        function analyzeIconPattern(pixels, width, height) {
            // 计算总像素数
            let totalPixels = 0;
            const pixelMap = [];

            // 创建像素地图
            for (let y = 0; y < height; y++) {
                pixelMap[y] = [];
                for (let x = 0; x < width; x++) {
                    const alpha = pixels[(y * width + x) * 4 + 3];
                    const hasPixel = alpha > 128;
                    pixelMap[y][x] = hasPixel;
                    if (hasPixel) totalPixels++;
                }
            }

            // 如果像素太少，直接判断为无效
            if (totalPixels < 20) {
                return {
                    hasPixels: false,
                    totalPixels,
                    isErrorIcon: false,
                    isRealIcon: false,
                    debugInfo: '像素太少'
                };
            }

            // 检测矩形边框 - 简化版本
            let borderPixels = 0;

            // 上边框和下边框
            for (let x = 0; x < width; x++) {
                if (pixelMap[0] && pixelMap[0][x]) borderPixels++;
                if (pixelMap[height-1] && pixelMap[height-1][x]) borderPixels++;
            }

            // 左边框和右边框
            for (let y = 0; y < height; y++) {
                if (pixelMap[y] && pixelMap[y][0]) borderPixels++;
                if (pixelMap[y] && pixelMap[y][width-1]) borderPixels++;
            }

            // 检测X标记 - 简化版本
            let xMarkPixels = 0;
            const minDim = Math.min(width, height);

            for (let i = 0; i < minDim; i++) {
                // 主对角线
                if (pixelMap[i] && pixelMap[i][i]) xMarkPixels++;
                // 反对角线
                if (pixelMap[i] && pixelMap[i][minDim-1-i]) xMarkPixels++;
            }

            // 计算比例
            const borderRatio = borderPixels / totalPixels;
            const xMarkRatio = xMarkPixels / totalPixels;

            // 添加调试日志
            console.log(`图标分析 - 总像素:${totalPixels}, 边框:${borderPixels}(${(borderRatio*100).toFixed(1)}%), X标记:${xMarkPixels}(${(xMarkRatio*100).toFixed(1)}%)`);

            // 更精确的错误图标判断 - 基于您提供的具体数据
            // 失效图标的精确特征：总像素344，边框0(0.0%)，X标记36(10.5%)
            // 需要更严格的条件来避免误判

            const isSpecificSize = totalPixels >= 300 && totalPixels <= 400; // 特定像素范围
            const isNoBorder = borderPixels === 0 || borderRatio < 0.05; // 几乎没有边框
            const isSpecificXMark = xMarkPixels >= 30 && xMarkPixels <= 50 &&
                                   xMarkRatio >= 0.08 && xMarkRatio <= 0.15; // 特定X标记范围

            // 错误图标：非常具体的特征匹配
            const isErrorIcon = isSpecificSize && isNoBorder && isSpecificXMark;

            // 真实图标：有像素但不是错误图标
            const isRealIcon = totalPixels > 10 && !isErrorIcon;

            return {
                hasPixels: totalPixels > 10,
                totalPixels,
                borderPixels,
                borderRatio,
                xMarkPixels,
                xMarkRatio,
                isSpecificSize,
                isNoBorder,
                isSpecificXMark,
                isErrorIcon,
                isRealIcon,
                debugInfo: `总像素:${totalPixels} 边框:${borderPixels}(${(borderRatio*100).toFixed(1)}%) X标记:${xMarkPixels}(${(xMarkRatio*100).toFixed(1)}%)`
            };
        }
        
        function generateIconGrid() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';
            
            // 按范围分组测试 f000-f8ff
            const ranges = [];
            for (let i = 0; i <= 8; i++) {
                ranges.push(`f${i}xx`);
            }
            
            ranges.forEach(range => {
                const rangeDiv = document.createElement('div');
                rangeDiv.className = 'range-section';
                rangeDiv.innerHTML = `
                    <div class="range-header">
                        <span>${range} 范围</span>
                        <span class="range-stats" id="stats-${range}">0/0</span>
                    </div>
                    <div class="icon-grid" id="grid-${range}"></div>
                `;
                resultsDiv.appendChild(rangeDiv);
                
                const grid = document.getElementById(`grid-${range}`);
                const baseCode = parseInt(range.charAt(1), 16) * 256 + 0xf000;
                
                // 每个范围生成256个图标
                for (let j = 0; j < 256; j++) {
                    const code = baseCode + j;
                    const hexCode = code.toString(16);
                    
                    const iconDiv = document.createElement('div');
                    iconDiv.className = 'icon-test';
                    iconDiv.innerHTML = `
                        <span class="icon">&#x${hexCode};</span>
                        <div class="icon-code">${hexCode}</div>
                    `;
                    iconDiv.dataset.code = hexCode;
                    iconDiv.dataset.range = range;
                    grid.appendChild(iconDiv);
                }
                
                testResults.ranges[range] = { working: 0, broken: 0 };
            });
            
            testResults.total = ranges.length * 256;
        }
        
        function startTest() {
            document.getElementById('loading').classList.remove('hidden');
            
            setTimeout(() => {
                generateIconGrid();
                document.getElementById('loading').classList.add('hidden');
                runTest();
            }, 100);
        }
        
        function runTest() {
            const icons = document.querySelectorAll('.icon-test');
            let processed = 0;
            
            testResults.working = 0;
            testResults.broken = 0;
            testResults.workingIcons = [];
            testResults.brokenIcons = [];
            
            // 重置范围统计
            Object.keys(testResults.ranges).forEach(range => {
                testResults.ranges[range] = { working: 0, broken: 0 };
            });
            
            function processIcon(index) {
                if (index >= icons.length) {
                    updateFinalStats();
                    return;
                }
                
                const iconDiv = icons[index];
                const iconEl = iconDiv.querySelector('.icon');
                const code = iconDiv.dataset.code;
                const range = iconDiv.dataset.range;
                
                // 使用改进的矩形框检测方法
                requestAnimationFrame(() => {
                    const rect = iconEl.getBoundingClientRect();

                    // 创建canvas进行像素分析
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.width = 40;
                    canvas.height = 40;

                    ctx.font = '24px font-awesome-icon';
                    ctx.fillStyle = '#000';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';

                    // 绘制图标
                    const char = String.fromCharCode(parseInt(code, 16));
                    ctx.fillText(char, 20, 20);

                    // 获取像素数据进行分析
                    const imageData = ctx.getImageData(0, 0, 40, 40);
                    const pixels = imageData.data;

                    // 分析像素模式
                    const analysis = analyzeIconPattern(pixels, 40, 40);

                    // 优化的判断逻辑：基于新的错误图标检测
                    let isWorking = false;

                    if (analysis.hasPixels) {
                        // 如果检测到错误图标特征（矩形框+X标记），标记为不可用
                        isWorking = analysis.isRealIcon && !analysis.isErrorIcon;
                    }
                    
                    if (isWorking) {
                        iconDiv.classList.add('working');
                        testResults.working++;
                        testResults.ranges[range].working++;
                        testResults.workingIcons.push(code);

                        // 添加详细的调试信息
                        iconDiv.title = `✅ 真实图标
${analysis.debugInfo}
特定尺寸: ${analysis.isSpecificSize ? '是' : '否'} (300-400像素)
无边框: ${analysis.isNoBorder ? '是' : '否'} (边框<5%)
特定X标记: ${analysis.isSpecificXMark ? '是' : '否'} (30-50个且8%-15%)`;
                    } else {
                        iconDiv.classList.add('broken');
                        testResults.broken++;
                        testResults.ranges[range].broken++;
                        testResults.brokenIcons.push(code);

                        // 添加详细的调试信息
                        if (!analysis.hasPixels) {
                            iconDiv.title = '❌ 无像素内容';
                        } else if (analysis.isErrorIcon) {
                            iconDiv.title = `❌ 错误图标 (精确匹配)
${analysis.debugInfo}
✓ 特定尺寸: ${analysis.isSpecificSize ? '通过' : '未通过'} (300-400像素)
✓ 无边框: ${analysis.isNoBorder ? '通过' : '未通过'} (边框<5%)
✓ 特定X标记: ${analysis.isSpecificXMark ? '通过' : '未通过'} (30-50个且8%-15%)`;
                        } else {
                            iconDiv.title = `❌ 其他问题
像素总数: ${analysis.totalPixels}
边缘比例: ${(analysis.edgeRatio*100).toFixed(1)}%
中心比例: ${(analysis.centerRatio*100).toFixed(1)}%`;
                        }
                    }
                    
                    processed++;
                    
                    // 更新进度
                    const progress = (processed / icons.length) * 100;
                    document.getElementById('progressBar').style.width = progress + '%';
                    
                    // 更新范围统计
                    const rangeStats = testResults.ranges[range];
                    document.getElementById(`stats-${range}`).textContent = 
                        `✅${rangeStats.working} ❌${rangeStats.broken}`;
                    
                    // 更新总体统计
                    updateStats();
                    
                    // 批量处理，每10个图标暂停一下
                    if (processed % 10 === 0) {
                        setTimeout(() => processIcon(index + 1), 1);
                    } else {
                        processIcon(index + 1);
                    }
                });
            }
            
            processIcon(0);
        }
        
        function updateStats() {
            const stats = document.getElementById('stats');
            const workingPercent = ((testResults.working / testResults.total) * 100).toFixed(1);
            
            stats.innerHTML = `
                <strong>测试进度:</strong> ${testResults.working + testResults.broken}/${testResults.total}<br>
                <strong>✅ 可用图标:</strong> ${testResults.working} (${workingPercent}%)<br>
                <strong>❌ 不可用图标:</strong> ${testResults.broken}<br>
                <strong>各范围统计:</strong><br>
                ${Object.entries(testResults.ranges).map(([range, data]) => 
                    `${range}: ✅${data.working} ❌${data.broken}`
                ).join(' | ')}
            `;
        }
        
        function updateFinalStats() {
            updateStats();
            console.log('完整测试结果:', testResults);
            
            // 输出可用图标列表
            console.log('可用图标编码:', testResults.workingIcons);
            console.log('不可用图标编码:', testResults.brokenIcons);
        }
        
        function showWorking() {
            document.querySelectorAll('.icon-test').forEach(icon => {
                if (icon.classList.contains('working')) {
                    icon.classList.remove('hidden');
                } else {
                    icon.classList.add('hidden');
                }
            });
        }
        
        function showBroken() {
            document.querySelectorAll('.icon-test').forEach(icon => {
                if (icon.classList.contains('broken')) {
                    icon.classList.remove('hidden');
                } else {
                    icon.classList.add('hidden');
                }
            });
        }
        
        function showAll() {
            document.querySelectorAll('.icon-test').forEach(icon => {
                icon.classList.remove('hidden');
            });
        }

        // 测试特定范围
        function testRange(startCode, endCode) {
            const start = parseInt(startCode, 16);
            const end = parseInt(endCode, 16);

            // 清空现有结果
            document.getElementById('icon-grid').innerHTML = '';

            // 重置统计
            testResults = {
                total: 0,
                working: 0,
                broken: 0,
                ranges: {},
                workingIcons: [],
                brokenIcons: []
            };

            console.log(`开始测试范围 ${startCode}-${endCode}`);

            // 测试指定范围
            for (let i = start; i <= end; i++) {
                const code = i.toString(16).padStart(4, '0');
                setTimeout(() => testIcon(code), (i - start) * 10);
            }

            // 更新进度显示
            const total = end - start + 1;
            document.querySelector('.progress-bar').style.width = '0%';
            document.querySelector('.progress-text').textContent = `测试范围 ${startCode}-${endCode} (${total} 个图标)`;
        }
        
        function exportResults() {
            const results = {
                summary: {
                    total: testResults.total,
                    working: testResults.working,
                    broken: testResults.broken,
                    workingPercent: ((testResults.working / testResults.total) * 100).toFixed(1)
                },
                ranges: testResults.ranges,
                workingIcons: testResults.workingIcons,
                brokenIcons: testResults.brokenIcons
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'fontawesome-test-results.json';
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
