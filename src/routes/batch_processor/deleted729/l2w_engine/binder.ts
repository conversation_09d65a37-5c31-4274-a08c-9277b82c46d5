/**
 * L2W (Lynx-to-Web) Binder
 * 
 * This file is responsible for handling data binding expressions `{{...}}`.
 * It replaces these placeholders with actual data from the provided data context.
 */

/**
 * Binds data to a string containing `{{...}}` expressions.
 * 
 * @param text The string containing data binding expressions.
 * @param data The data context object.
 * @returns The string with expressions replaced by data.
 */
export function bindData(text: string, data: Record<string, any>): string {
  if (!text || typeof text !== 'string') {
    return text;
  }

  // Regex to find all `{{...}}` expressions.
  return text.replace(/\{\{([^\}]+)\}\}/g, (match, key) => {
    const trimmedKey = key.trim();
    // Basic key access. A more advanced version would handle nested paths like 'user.name'.
    const value = data[trimmedKey];

    // If the value is found, return it. Otherwise, return the original expression.
    return value !== undefined ? String(value) : match;
  });
}