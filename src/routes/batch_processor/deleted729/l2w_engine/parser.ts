/**
 * L2W (Lynx-to-Web) Parser
 * 
 * This file is responsible for parsing TTML strings into a structured AST (Abstract Syntax Tree)
 * using the 'parse5' library. The AST will then be used by the transformer.
 */

import * as parse5 from 'parse5';

/**
 * Represents a node in our custom L2W AST.
 */
export interface L2WNode {
  type: 'element' | 'text' | 'comment';
  tagName?: string;
  attributes: Record<string, string>;
  children: L2WNode[];
  content?: string;
  parent?: L2WNode;
}

/**
 * Parses a TTML string and returns the root L2WNode of the AST.
 * 
 * @param ttml The TTML string to parse.
 * @returns The root L2WNode.
 */
export function parse(ttml: string): L2WNode {
  const documentFragment = parse5.parseFragment(ttml, {
    sourceCodeLocationInfo: true,
  });

  // The root of a fragment is a 'template' tag, we need to process its children.
  const rootNode: L2WNode = {
    type: 'element',
    tagName: 'root',
    attributes: {},
    children: [],
  };

  // We need a function to convert parse5 nodes to our L2WNode format.
  // This will be implemented next.
  rootNode.children = (documentFragment.childNodes as any[]).map(child => convertNode(child, rootNode));

  return rootNode;
}

/**
 * Converts a parse5 node to an L2WNode.
 * 
 * @param p5Node The parse5 node.
 * @param parent The parent L2WNode.
 * @returns The converted L2WNode.
 */
function convertNode(p5Node: any, parent: L2WNode): L2WNode {
  if (p5Node.nodeName === '#text') {
    return {
      type: 'text',
      content: p5Node.value || '',
      attributes: {},
      children: [],
      parent,
    };
  }

  if (p5Node.nodeName === '#comment') {
    return {
      type: 'comment',
      content: p5Node.data || '',
      attributes: {},
      children: [],
      parent,
    };
  }

  const attributes: Record<string, string> = {};
  if (p5Node.attrs) {
    for (const attr of p5Node.attrs) {
      attributes[attr.name] = attr.value;
    }
  }

  const l2wNode: L2WNode = {
    type: 'element',
    tagName: p5Node.tagName,
    attributes,
    children: [],
    parent,
  };

  if (p5Node.childNodes && p5Node.childNodes.length > 0) {
    l2wNode.children = p5Node.childNodes.map((child: any) => convertNode(child, l2wNode));
  }

  return l2wNode;
}