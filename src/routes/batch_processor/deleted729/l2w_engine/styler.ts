/**
 * L2W (Lynx-to-Web) Styler
 * 
 * This file is responsible for parsing TTSS (Lynx Stylesheets) and applying them to the DOM tree.
 * It will handle selector mapping, property conversion (e.g., rpx to px), and style injection.
 */

const RPX_RATIO = 0.5; // Assuming a baseline screen width where 1rpx = 0.5px, as per the plan.

/**
 * Parses a TTSS string and applies the styles to the given DOM element.
 * This is a simplified implementation for now.
 * 
 * @param ttss The TTSS stylesheet string.
 * @param element The root HTMLElement to apply styles to.
 */
export function applyStyles(ttss: string, element: HTMLElement) {
  console.log('Applying styles...');

  // A real implementation would require a proper CSS parser.
  // For this temporary version, we'll use a simple regex-based approach
  // to find style rules and apply them. This is NOT robust.

  const rules = ttss.match(/([^{]+){([^}]+)}/g) || [];

  rules.forEach(rule => {
    const parts = rule.match(/([^{]+){([^}]+)}/);
    if (!parts) return;

    const selector = parts[1].trim();
    const styles = parts[2].trim();

    // Find elements matching the selector
    // Note: This doesn't support the full range of TTSS selectors yet.
    const elements = element.querySelectorAll(selector);

    elements.forEach(el => {
      if (el instanceof HTMLElement) {
        // Apply inline styles. A more advanced version would create a stylesheet.
        el.style.cssText += convertStyleProperties(styles);
      }
    });
  });

  console.log('Styles applied.');
}

/**
 * Converts a string of TTSS style properties to a CSS-compatible string.
 * Handles `rpx` unit conversion.
 * @param styleString The string of style properties (e.g., "width: 100rpx; height: 50rpx;").
 * @returns A CSS-compatible style string.
 */
function convertStyleProperties(styleString: string): string {
  return styleString
    .split(';')
    .map(prop => {
      if (!prop.trim()) return '';
      let [key, value] = prop.split(':');
      key = key.trim();
      value = value.trim();

      // Convert rpx to px
      if (value.endsWith('rpx')) {
        const rpxValue = parseFloat(value);
        value = `${rpxValue * RPX_RATIO}px`;
      }

      return `${key}: ${value};`;
    })
    .join(' ');
}