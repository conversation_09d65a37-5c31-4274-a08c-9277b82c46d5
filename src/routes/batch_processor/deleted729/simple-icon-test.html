<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超简单图标检测 - 基于视觉相似性</title>
    <style>
        @font-face {
            font-family: 'font-awesome-icon';
            src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/webfonts/fa-solid-900.woff2') format('woff2');
        }
        
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
            gap: 5px;
            margin: 20px 0;
        }
        
        .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 5px;
            border: 2px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
            min-height: 60px;
            justify-content: center;
        }
        
        .icon-item.working {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .icon-item.broken {
            border-color: #dc3545;
            background: #f8d7da;
        }
        
        .icon {
            font-family: 'font-awesome-icon';
            font-size: 20px;
            color: #333;
        }
        
        .icon-code {
            font-size: 10px;
            color: #666;
            font-family: monospace;
            margin-top: 2px;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        
        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .reference-box {
            background: #fff3cd;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .reference-icon {
            font-family: 'font-awesome-icon';
            font-size: 20px;
            display: inline-block;
            margin: 0 10px;
            padding: 5px;
            border: 1px solid #ccc;
        }
        
        #hidden-canvas {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>超简单图标检测 - 基于视觉相似性</h1>
        
        <div class="reference-box">
            <h3>参考矩形框样本：</h3>
            <span class="reference-icon" id="ref1">&#xf500;</span>
            <span class="reference-icon" id="ref2">&#xf600;</span>
            <span class="reference-icon" id="ref3">&#xf700;</span>
            <p>算法会将渲染结果与这些已知的矩形框进行比较</p>
        </div>
        
        <div class="controls">
            <button type="button" class="btn btn-primary" onclick="startSimpleTest()">开始简单测试</button>
            <button type="button" class="btn btn-success" onclick="testRange('f000', 'f0ff')">测试 f000-f0ff</button>
            <button type="button" class="btn btn-danger" onclick="testRange('f100', 'f1ff')">测试 f100-f1ff</button>
        </div>
        
        <div class="stats" id="stats">
            准备开始测试...
        </div>
        
        <div class="icon-grid" id="icon-grid"></div>
        
        <canvas id="hidden-canvas" width="32" height="32"></canvas>
    </div>

    <script>
        let referenceBoxData = null;
        let testResults = { working: 0, broken: 0, total: 0 };
        
        // 获取参考矩形框的像素数据
        function getReferenceBoxData() {
            const canvas = document.getElementById('hidden-canvas');
            const ctx = canvas.getContext('2d');
            
            // 渲染一个已知的矩形框图标
            ctx.clearRect(0, 0, 32, 32);
            ctx.font = '20px font-awesome-icon';
            ctx.fillStyle = '#000';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('\uf500', 16, 16); // 已知矩形框
            
            return ctx.getImageData(0, 0, 32, 32).data;
        }
        
        // 计算两个图像的相似度
        function calculateSimilarity(data1, data2) {
            let differences = 0;
            let totalPixels = 0;
            
            for (let i = 0; i < data1.length; i += 4) {
                const alpha1 = data1[i + 3];
                const alpha2 = data2[i + 3];
                
                totalPixels++;
                
                // 比较alpha通道（透明度）
                if (Math.abs(alpha1 - alpha2) > 50) {
                    differences++;
                }
            }
            
            return 1 - (differences / totalPixels);
        }
        
        // 检测图标是否为矩形框
        function isRectangleBox(iconCode) {
            const canvas = document.getElementById('hidden-canvas');
            const ctx = canvas.getContext('2d');
            
            // 渲染测试图标
            ctx.clearRect(0, 0, 32, 32);
            ctx.font = '20px font-awesome-icon';
            ctx.fillStyle = '#000';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(`\\u${iconCode}`, 16, 16);
            
            const testData = ctx.getImageData(0, 0, 32, 32).data;
            
            // 与参考矩形框比较
            const similarity = calculateSimilarity(referenceBoxData, testData);
            
            // 如果相似度超过80%，认为是矩形框
            return similarity > 0.8;
        }
        
        // 测试单个图标
        function testIcon(code) {
            const iconDiv = document.createElement('div');
            iconDiv.className = 'icon-item';
            iconDiv.innerHTML = `
                <div class="icon">&#x${code};</div>
                <div class="icon-code">${code}</div>
            `;
            
            // 检测是否为矩形框
            const isBox = isRectangleBox(code);
            
            if (isBox) {
                iconDiv.classList.add('broken');
                testResults.broken++;
            } else {
                iconDiv.classList.add('working');
                testResults.working++;
            }
            
            testResults.total++;
            document.getElementById('icon-grid').appendChild(iconDiv);
            
            // 更新统计
            updateStats();
        }
        
        // 更新统计信息
        function updateStats() {
            const workingPercent = testResults.total > 0 ? 
                ((testResults.working / testResults.total) * 100).toFixed(1) : 0;
            
            document.getElementById('stats').innerHTML = `
                <strong>测试进度:</strong> ${testResults.total}<br>
                <strong>✅ 可用图标:</strong> ${testResults.working} (${workingPercent}%)<br>
                <strong>❌ 矩形框:</strong> ${testResults.broken}
            `;
        }
        
        // 测试指定范围
        function testRange(startCode, endCode) {
            const start = parseInt(startCode, 16);
            const end = parseInt(endCode, 16);
            
            // 清空结果
            document.getElementById('icon-grid').innerHTML = '';
            testResults = { working: 0, broken: 0, total: 0 };
            
            // 获取参考数据
            if (!referenceBoxData) {
                referenceBoxData = getReferenceBoxData();
            }
            
            // 测试范围内的图标
            for (let i = start; i <= end; i++) {
                const code = i.toString(16).padStart(4, '0');
                setTimeout(() => testIcon(code), (i - start) * 20);
            }
        }
        
        // 开始简单测试
        function startSimpleTest() {
            testRange('f000', 'f0ff');
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 预加载参考数据
            setTimeout(() => {
                referenceBoxData = getReferenceBoxData();
                console.log('参考矩形框数据已加载');
            }, 1000);
        });
    </script>
</body>
</html>
