<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮一致性测试</title>
    <link rel="stylesheet" href="styles/index.css">
    <link rel="stylesheet" href="styles/components/buttons.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            padding: 20px;
            background: #f8fafc;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .button-group {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .button-group h3 {
            margin: 0 0 15px 0;
            color: #374151;
            font-size: 16px;
            font-weight: 600;
        }
        .buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        .comparison-table {
            margin: 20px 0;
            border-collapse: collapse;
            width: 100%;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #e5e7eb;
            padding: 8px 12px;
            text-align: left;
        }
        .comparison-table th {
            background: #f9fafb;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>按钮字体和图标一致性测试</h1>
        <p>测试黄色按钮与蓝色按钮的字体大小、图标大小是否一致</p>

        <div class="button-group">
            <h3>蓝色按钮 (参考标准)</h3>
            <div class="buttons">
                <button class="btn btn--secondary-glass">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    蓝色按钮
                </button>
                <button class="btn btn--secondary-glass btn--sm">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 11l3 3l8-8"/>
                    </svg>
                    复制日志
                </button>
            </div>
        </div>

        <div class="button-group">
            <h3>黄色按钮 (需要保持一致)</h3>
            <div class="buttons">
                <button class="btn btn--primary-gold">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    重试失败项
                    <span class="retry-failed-badge rounded-full px-2 py-0.5 text-xs font-mono font-semibold">3</span>
                </button>
                <button class="btn btn--primary-gold btn--sm">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    黄色按钮
                </button>
            </div>
        </div>

        <div class="button-group">
            <h3>特殊样式的黄色按钮</h3>
            <div class="buttons">
                <button class="retry-failed-btn btn--primary-gold">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    重试失败项 (特殊样式)
                    <span class="retry-failed-badge rounded-full px-2 py-0.5 text-xs font-mono font-semibold">5</span>
                </button>
            </div>
        </div>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>属性</th>
                    <th>蓝色按钮 (btn--secondary-glass)</th>
                    <th>黄色按钮 (btn--primary-gold)</th>
                    <th>是否一致</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>字体大小</td>
                    <td>13px</td>
                    <td>13px</td>
                    <td>✅ 一致</td>
                </tr>
                <tr>
                    <td>字体粗细</td>
                    <td>600</td>
                    <td>600</td>
                    <td>✅ 一致</td>
                </tr>
                <tr>
                    <td>图标大小</td>
                    <td>16px × 16px</td>
                    <td>16px × 16px</td>
                    <td>✅ 一致</td>
                </tr>
                <tr>
                    <td>布局方式</td>
                    <td>inline-flex</td>
                    <td>inline-flex</td>
                    <td>✅ 一致</td>
                </tr>
                <tr>
                    <td>对齐方式</td>
                    <td>center</td>
                    <td>center</td>
                    <td>✅ 一致</td>
                </tr>
                <tr>
                    <td>间距</td>
                    <td>8px gap</td>
                    <td>8px gap</td>
                    <td>✅ 一致</td>
                </tr>
            </tbody>
        </table>

        <div style="margin-top: 30px; padding: 15px; background: #f0f9ff; border-radius: 8px; border-left: 4px solid #0ea5e9;">
            <h4 style="margin: 0 0 10px 0; color: #0c4a6e;">修改说明</h4>
            <ul style="margin: 0; color: #0c4a6e;">
                <li>统一了黄色按钮的字体大小为 13px（与蓝色按钮一致）</li>
                <li>统一了黄色按钮的图标大小为 16px × 16px（与蓝色按钮一致）</li>
                <li>添加了一致的布局属性：display: inline-flex, align-items: center, gap: 8px</li>
                <li>保持了各自的颜色主题，只统一了尺寸和布局属性</li>
            </ul>
        </div>
    </div>

    <script>
        // 检查实际的计算样式
        document.addEventListener('DOMContentLoaded', function() {
            const blueBtn = document.querySelector('.btn--secondary-glass');
            const goldBtn = document.querySelector('.btn--primary-gold');
            
            if (blueBtn && goldBtn) {
                const blueStyles = window.getComputedStyle(blueBtn);
                const goldStyles = window.getComputedStyle(goldBtn);
                
                console.log('蓝色按钮字体大小:', blueStyles.fontSize);
                console.log('黄色按钮字体大小:', goldStyles.fontSize);
                console.log('蓝色按钮字体粗细:', blueStyles.fontWeight);
                console.log('黄色按钮字体粗细:', goldStyles.fontWeight);
                
                const blueSvg = blueBtn.querySelector('svg');
                const goldSvg = goldBtn.querySelector('svg');
                
                if (blueSvg && goldSvg) {
                    const blueSvgStyles = window.getComputedStyle(blueSvg);
                    const goldSvgStyles = window.getComputedStyle(goldSvg);
                    
                    console.log('蓝色按钮图标大小:', blueSvgStyles.width, 'x', blueSvgStyles.height);
                    console.log('黄色按钮图标大小:', goldSvgStyles.width, 'x', goldSvgStyles.height);
                }
            }
        });
    </script>
</body>
</html>
