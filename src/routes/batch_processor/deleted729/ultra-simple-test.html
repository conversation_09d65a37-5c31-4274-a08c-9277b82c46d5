<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>终极简单检测 - 直接文本比较</title>
    <style>
        @font-face {
            font-family: 'font-awesome-icon';
            src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/webfonts/fa-solid-900.woff2') format('woff2');
        }
        
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 5px;
            margin: 20px 0;
        }
        
        .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
            min-height: 70px;
            justify-content: center;
        }
        
        .icon-item.working {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .icon-item.broken {
            border-color: #dc3545;
            background: #f8d7da;
        }
        
        .icon {
            font-family: 'font-awesome-icon';
            font-size: 24px;
            color: #333;
            margin-bottom: 4px;
        }
        
        .icon-code {
            font-size: 11px;
            color: #666;
            font-family: monospace;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        
        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .method-info {
            background: #d1ecf1;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        #test-area {
            position: absolute;
            left: -9999px;
            font-family: 'font-awesome-icon';
            font-size: 24px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>终极简单检测 - 直接文本比较法</h1>
        
        <div class="method-info">
            <h3>检测原理：</h3>
            <p>1. 渲染图标到隐藏元素</p>
            <p>2. 获取元素的实际宽度</p>
            <p>3. 矩形框图标通常有固定的宽度特征</p>
            <p>4. 真实图标的宽度会根据图标形状变化</p>
        </div>
        
        <div class="controls">
            <button type="button" class="btn btn-primary" onclick="autoDetectPattern()">自动检测模式</button>
            <button type="button" class="btn btn-success" onclick="testRange('f000', 'f07f')">测试 f000-f07f</button>
            <button type="button" class="btn btn-warning" onclick="testRange('f080', 'f0ff')">测试 f080-f0ff</button>
            <button type="button" class="btn btn-danger" onclick="testRange('f100', 'f1ff')">测试 f100-f1ff</button>
        </div>
        
        <div class="stats" id="stats">
            准备开始测试...
        </div>
        
        <div class="icon-grid" id="icon-grid"></div>
        
        <div id="test-area"></div>
    </div>

    <script>
        let testResults = { working: 0, broken: 0, total: 0 };
        let rectangleBoxWidth = null;
        
        // 获取图标的渲染宽度
        function getIconWidth(iconCode) {
            const testArea = document.getElementById('test-area');
            testArea.innerHTML = `&#x${iconCode};`;
            return testArea.offsetWidth;
        }
        
        // 检测矩形框的标准宽度
        function detectRectangleBoxWidth() {
            // 使用已知的矩形框图标来确定标准宽度
            const knownBoxCodes = ['f500', 'f600', 'f700', 'f800'];
            const widths = knownBoxCodes.map(code => getIconWidth(code));
            
            // 取最常见的宽度作为矩形框标准
            const widthCounts = {};
            widths.forEach(w => {
                widthCounts[w] = (widthCounts[w] || 0) + 1;
            });
            
            let maxCount = 0;
            let standardWidth = 0;
            for (const [width, count] of Object.entries(widthCounts)) {
                if (count > maxCount) {
                    maxCount = count;
                    standardWidth = parseInt(width);
                }
            }
            
            console.log('检测到矩形框标准宽度:', standardWidth);
            return standardWidth;
        }
        
        // 检测图标是否为矩形框
        function isRectangleBox(iconCode) {
            if (!rectangleBoxWidth) {
                rectangleBoxWidth = detectRectangleBoxWidth();
            }
            
            const width = getIconWidth(iconCode);
            
            // 如果宽度与矩形框标准宽度相同，认为是矩形框
            return Math.abs(width - rectangleBoxWidth) <= 2; // 允许2px误差
        }
        
        // 测试单个图标
        function testIcon(code) {
            const iconDiv = document.createElement('div');
            iconDiv.className = 'icon-item';
            
            const width = getIconWidth(code);
            const isBox = isRectangleBox(code);
            
            iconDiv.innerHTML = `
                <div class="icon">&#x${code};</div>
                <div class="icon-code">${code}</div>
                <div style="font-size: 9px; color: #999;">w:${width}</div>
            `;
            
            if (isBox) {
                iconDiv.classList.add('broken');
                testResults.broken++;
            } else {
                iconDiv.classList.add('working');
                testResults.working++;
            }
            
            testResults.total++;
            document.getElementById('icon-grid').appendChild(iconDiv);
            
            updateStats();
        }
        
        // 更新统计信息
        function updateStats() {
            const workingPercent = testResults.total > 0 ? 
                ((testResults.working / testResults.total) * 100).toFixed(1) : 0;
            
            document.getElementById('stats').innerHTML = `
                <strong>测试进度:</strong> ${testResults.total}<br>
                <strong>✅ 可用图标:</strong> ${testResults.working} (${workingPercent}%)<br>
                <strong>❌ 矩形框:</strong> ${testResults.broken}<br>
                <strong>矩形框标准宽度:</strong> ${rectangleBoxWidth || '未检测'}px
            `;
        }
        
        // 测试指定范围
        function testRange(startCode, endCode) {
            const start = parseInt(startCode, 16);
            const end = parseInt(endCode, 16);
            
            // 清空结果
            document.getElementById('icon-grid').innerHTML = '';
            testResults = { working: 0, broken: 0, total: 0 };
            
            // 重新检测矩形框宽度
            rectangleBoxWidth = detectRectangleBoxWidth();
            
            console.log(`开始测试范围 ${startCode}-${endCode}`);
            
            // 测试范围内的图标
            for (let i = start; i <= end; i++) {
                const code = i.toString(16).padStart(4, '0');
                setTimeout(() => testIcon(code), (i - start) * 30);
            }
        }
        
        // 自动检测模式 - 测试多个范围找出规律
        function autoDetectPattern() {
            console.log('开始自动检测模式...');
            
            // 先测试小范围找出规律
            testRange('f000', 'f0ff');
            
            setTimeout(() => {
                console.log('第一轮测试完成，开始分析规律...');
                analyzePattern();
            }, 5000);
        }
        
        // 分析测试结果找出规律
        function analyzePattern() {
            const items = document.querySelectorAll('.icon-item');
            const workingCodes = [];
            const brokenCodes = [];
            
            items.forEach(item => {
                const code = item.querySelector('.icon-code').textContent;
                if (item.classList.contains('working')) {
                    workingCodes.push(code);
                } else {
                    brokenCodes.push(code);
                }
            });
            
            console.log('可用图标:', workingCodes);
            console.log('不可用图标:', brokenCodes);
            
            // 分析范围规律
            const workingRanges = findRanges(workingCodes);
            const brokenRanges = findRanges(brokenCodes);
            
            console.log('可用范围:', workingRanges);
            console.log('不可用范围:', brokenRanges);
        }
        
        // 找出连续的编码范围
        function findRanges(codes) {
            if (codes.length === 0) return [];
            
            const sorted = codes.map(c => parseInt(c, 16)).sort((a, b) => a - b);
            const ranges = [];
            let start = sorted[0];
            let end = sorted[0];
            
            for (let i = 1; i < sorted.length; i++) {
                if (sorted[i] === end + 1) {
                    end = sorted[i];
                } else {
                    ranges.push(`${start.toString(16)}-${end.toString(16)}`);
                    start = end = sorted[i];
                }
            }
            ranges.push(`${start.toString(16)}-${end.toString(16)}`);
            
            return ranges;
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，准备开始检测');
        });
    </script>
</body>
</html>
