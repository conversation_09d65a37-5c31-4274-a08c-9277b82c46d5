# 抖音内部底稿数据集成使用指南

## 概述

本文档介绍了新增的"使用抖音内部的底稿数据"功能的使用方法、技术实现和注意事项。

## 功能特性

### 🎯 核心功能
- **模式切换**: 在输入区域上方提供"是/否"开关，控制是否使用底稿数据
- **数据源集成**: 查询时先调用底稿数据接口，再将结果传递给AI进行UI排版
- **严格数据使用**: AI被要求严格使用底稿数据，只允许轻微调整结构和排版
- **无缝回退**: 底稿数据获取失败时自动回退到直接AI生成模式

### 🚀 技术特性
- **缓存机制**: 5分钟缓存，提升重复查询性能
- **错误处理**: 完善的错误分类和用户友好的错误提示
- **进度指示**: 实时显示底稿数据获取和处理进度
- **性能监控**: 提供缓存统计和性能指标

## 使用方法

### 1. 基本使用

```typescript
// 在QueryInputPanel组件中使用
<QueryInputPanel
  inputText={inputText}
  onInputChange={setInputText}
  queries={queries}
  isProcessing={isProcessing}
  onQueryProcessed={(result) => {
    console.log('查询处理结果:', result);
  }}
/>
```

### 2. 模式切换

用户可以通过界面上的开关控制数据源模式：
- **开启**: 使用抖音内部底稿数据作为数据源
- **关闭**: 直接使用AI生成内容（原有模式）

### 3. 批处理服务集成

```typescript
// 设置底稿数据模式
const batchService = new EnhancedBatchProcessorService();
batchService.setInternalDataMode(true);

// 检查当前模式
const isEnabled = batchService.getInternalDataMode();
```

## 技术架构

### 数据流程

```
用户输入 → 模式判断 → [底稿数据接口] → AI处理 → UI渲染
                ↓
            直接AI处理 → UI渲染
```

### 核心组件

1. **InternalDataService**: 底稿数据接口服务
2. **DataProcessingService**: 数据处理和prompt构建
3. **ModeToggle**: 模式切换UI组件
4. **DataSourceErrorHandler**: 错误处理UI组件

### 接口规范

#### 底稿数据接口
- **URL**: `https://9gzj7t9k.fn.bytedance.net/api/search/stream`
- **参数**:
  - `query`: 查询关键词（URL编码）
  - `summary_only`: 是否只返回摘要（1/0）
- **方法**: GET
- **超时**: 30秒
- **返回数据结构**:
  ```json
  {
    "answer": "核心答案内容，包含HTML标记和特殊标记🔶n🔷",
    "pv": 4351,
    "reasoning": "推理过程（可选）",
    "reference": "参考资料，包含来源链接和日期信息"
  }
  ```

#### 数据处理流程
1. 验证查询参数
2. 检查缓存
3. 调用底稿数据接口
4. 解析返回的数据结构：
   - 提取 `answer` 字段作为核心内容
   - 提取 `pv` 字段作为数据热度
   - 提取 `reference` 字段作为参考资料
   - 提取 `reasoning` 字段作为推理过程（可选）
5. 构建严格使用底稿数据的prompt
6. 传递给AI进行UI排版

#### 数据结构处理特点
- **HTML标记保持**: 保留answer中的`<mark>`等HTML标记
- **特殊标记处理**: 正确处理🔶n🔷格式的引用标记
- **参考资料解析**: 从reference字段中提取来源链接和日期
- **数据热度展示**: 显示pv值作为内容热度指标

## 配置选项

### BatchConfig扩展
```typescript
interface BatchConfig {
  processing: {
    // ... 其他配置
    useInternalData: boolean; // 是否使用底稿数据
  };
}
```

### DataProcessingConfig
```typescript
interface DataProcessingConfig {
  strictMode: boolean; // 严格模式
  allowDataModification: boolean; // 允许轻微修改
  maxDataSize: number; // 最大数据大小
  timeoutMs: number; // 处理超时
}
```

## 错误处理

### 错误类型
- `NETWORK_ERROR`: 网络连接失败
- `TIMEOUT_ERROR`: 请求超时
- `PARSE_ERROR`: 数据解析失败
- `API_ERROR`: 接口调用失败
- `INVALID_QUERY`: 查询参数无效

### 错误处理策略
1. **自动重试**: 网络错误和超时错误支持重试
2. **优雅降级**: 底稿数据获取失败时自动回退到直接模式
3. **用户提示**: 显示用户友好的错误信息和操作建议

## 性能优化

### 缓存策略
- **缓存时长**: 5分钟
- **缓存键**: `${query}_${summaryOnly}`
- **内存管理**: 自动清理过期缓存
- **缓存统计**: 提供缓存命中率和内存使用情况

### 请求优化
- **超时控制**: 30秒请求超时
- **并发限制**: 避免重复请求
- **数据压缩**: 大数据自动截断

## 监控和调试

### 日志记录
```typescript
// 模式切换日志
console.log('[QueryInputPanel] 数据源模式切换: 内部数据');

// 数据处理日志
console.log('[DataProcessingService] 底稿数据处理完成:', result);

// 错误日志
console.error('[InternalDataService] 底稿数据获取失败:', error);
```

### 性能监控
- 底稿数据获取耗时
- 数据处理耗时
- 缓存命中率
- 错误发生率

## 最佳实践

### 1. 查询优化
- 使用具体、明确的查询词
- 避免过长的查询内容
- 合理使用缓存机制

### 2. 错误处理
- 始终提供回退方案
- 显示用户友好的错误信息
- 记录详细的错误日志

### 3. 性能考虑
- 监控底稿数据接口响应时间
- 定期清理缓存
- 避免频繁的模式切换

## 故障排除

### 常见问题

1. **底稿数据获取失败**
   - 检查网络连接
   - 验证接口地址和参数
   - 查看错误日志

2. **数据解析错误**
   - 检查返回数据格式
   - 验证JSON结构
   - 查看解析日志

3. **性能问题**
   - 检查缓存命中率
   - 监控接口响应时间
   - 优化查询内容

### 调试工具
- 浏览器开发者工具
- 网络请求监控
- 缓存统计面板
- 错误日志查看器

## 更新日志

### v1.0.0 (2025-07-28)
- ✅ 新增底稿数据模式切换功能
- ✅ 实现InternalDataService底稿数据接口服务
- ✅ 添加DataProcessingService数据处理服务
- ✅ 创建ModeToggle模式切换组件
- ✅ 集成到QueryInputPanel和批处理服务
- ✅ 完善错误处理和用户体验
- ✅ 添加缓存机制和性能优化

## 联系支持

如有问题或建议，请联系开发团队或查看相关技术文档。
