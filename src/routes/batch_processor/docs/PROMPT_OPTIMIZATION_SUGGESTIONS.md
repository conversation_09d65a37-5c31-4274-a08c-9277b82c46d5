# Prompts 架构优化建议

## 1. 总体评价

当前的 `prompts` 文件夹和 `prompt.md` 的整合模式体现了高度的工程化和模块化思想，通过将不同的 prompt 规则拆分成独立的文件，实现了良好的可维护性和可扩展性。`prompt.md` 的内容详尽，规则明确，为 AI 生成高质量的 Lynx 代码提供了坚实的基础。

然而，在结构、内容和 AI 理解方面，仍然存在一些可以优化的空间，以进一步提升代码生成质量和布局的精美程度。

## 2. `prompts` 文件夹架构优化

当前的 `prompts` 文件夹内文件较多，虽然命名具有一定描述性，但扁平化的结构使得理解和维护变得困难。建议引入层级结构，对 prompt 进行分类组织。

### 2.1. 建议的目录结构

```
prompts/
├── 00_identity/
│   ├── 01_MasterDeveloper.ts
│   └── 02_Professionalism.ts
├── 01_core_rules/
│   ├── 01_OutputFormat.ts
│   ├── 02_CriticalProhibitions.ts
│   └── 03_FileStandards.ts
├── 02_technical_constraints/
│   ├── 01_TTML.ts
│   ├── 02_TTSS.ts
│   ├── 03_JavaScript.ts
│   └── 04_DataFlow.ts
├── 03_lynx_specifics/
│   ├── 01_ComponentLibrary.ts
│   ├── 02_APISystem.ts
│   └── 03_EventSystem.ts
├── 04_specialized_guides/
│   ├── 01_LightChart.ts
│   ├── 02_Canvas.ts
│   └── 03_UIGuidance.ts
├── 05_quality_and_examples/
│   ├── 01_CodeQuality.ts
│   └── 02_Examples.ts
├── assemblers/
│   └── HybridPromptAssembler.ts
└── README.md
```

### 2.2. 优化说明

- **按优先级和功能分类**：
  - `00_identity`: 定义 AI 的角色和专业水平。
  - `01_core_rules`: 定义最核心、最通用的规则，如输出格式、禁止项等。
  - `02_technical_constraints`: 按技术栈（TTML, TTSS, JS）组织技术约束。
  - `03_lynx_specifics`: 针对 Lynx 框架的特定内容，如组件、API、事件。
  - `04_specialized_guides`: 针对特定技术（如 LightChart, Canvas）的详细指南。
  - `05_quality_and_examples`: 包含代码质量要求和示例。
- **数字前缀**：使用数字前缀明确加载顺序和优先级，确保 AI 首先理解核心规则。
- **`assemblers` 目录**：将用于组合 prompt 的脚本（如 `HybridPromptAssembler.ts`）放在独立的目录中，与 prompt 内容本身分离。

## 3. `prompt.md` 内容优化

`prompt.md` 的内容已经非常出色，但可以通过以下方式进一步提升 AI 的理解能力。

### 3.1. 结构化与层次化

当前的 `prompt.md` 内容是线性的，虽然有标题，但缺乏更强的结构感。可以引入更深的层次结构，并使用 Markdown 的引用、列表等功能来增强可读性。

**优化建议**：

- **使用层级标题**：使用 `##`, `###`, `####` 来组织内容，形成清晰的树状结构。
- **使用引用块**：将关键规则、禁止项、强制要求等放在引用块中，使其在视觉上更突出。
- **使用列表**：将并列的规则、检查清单等使用有序或无序列表进行组织。

**示例 (优化后的 `CRITICAL TTML 组件约束`)**：

```markdown
## 2.1. TTML 技术约束

### 2.1.1. CRITICAL: 组件使用规范

> **核心原则**: 必须严格使用 Lynx TTML 组件，严禁混用任何 Web HTML 标签。

#### 绝对禁止项

- **严禁使用 Web 标签**:
  - `div`, `span`, `p`, `h1`-`h6`
  - `ul`, `li`, `table`, `form`
  - `button`, `input[type="button"]`, `a`
- **严禁混用 Web CSS 选择器**

#### 强制要求

- **基础容器**: 必须使用 `<view>`，并用 `</view>` 闭合。
- **文本内容**: 必须使用 `<text>`。
- **图片**: 必须使用 `<image>` (自闭合)。
- **可滚动区域**: 必须使用 `<scroll-view>`。
```

### 3.2. 规则的原子化与唯一性

当前 `prompt.md` 中存在一些重复的规则，例如 `TTML语法转义绝对禁止项` 出现了两次。这可能会让 AI 感到困惑。

**优化建议**：

- **合并重复规则**：将所有关于同一主题的规则合并到一个地方，并确保其完整性。
- **规则原子化**：每条规则应尽可能只描述一件事情，避免将多个不相关的约束放在一起。

### 3.3. 增加 “正面” 和 “反面” 示例

当前的 prompt 中已经包含了一些 “正确” 和 “错误” 的示例，这是一个非常好的实践。可以进一步扩展这种模式，为更多的规则提供正反面对比。

**优化建议**：

- **为每条核心规则提供 `Good` vs `Bad` 示例**：
  - **Good (✅)**: 清晰展示符合规则的代码片段。
  - **Bad (❌)**: 清晰展示违反规则的代码片段，并简要说明错误原因。

**示例 (scroll-view 高度设置规则)**：

```markdown
#### `scroll-view` 高度设置

> **核心要求**: `scroll-view` 必须明确设置高度，以确保在所有设备上表现一致。

**✅ 正确示例**:
```xml
<scroll-view style="height: 100vh; max-height: 100vh;" scroll-y="true">
  <!-- content -->
</scroll-view>
```

**❌ 错误示例**:
```xml
<!-- 错误：仅使用 min-height，可能导致在某些设备上高度计算不准确 -->
<scroll-view style="min-height: 100vh;" scroll-y="true">
  <!-- content -->
</scroll-view>
```

## 4. 提升 AI 对 Prompt 的理解能力

为了让 AI 更好地理解整个 prompt 规则并生成更精美的代码，可以引入一些 “元指令” (meta-instructions)。

### 4.1. 引入 “Prompt 理解指南”

在 `prompt.md` 的最开始，可以增加一小段文字，告诉 AI 如何去“学习”和“应用”这些规则。

**示例**:

```markdown
# AI Prompt 理解与执行指南

你好！作为世界顶级的 Lynx 框架专家，请在开始工作前，仔细阅读并理解以下所有规则。

1.  **结构化学习**: 本文档是结构化的，请按照从上到下的顺序学习。核心规则在前，具体实现细节在后。
2.  **规则的优先级**: 标题中带有 `CRITICAL` 或 `MANDATORY` 的规则是最高优先级的，必须严格遵守。
3.  **示例驱动**: 通过 “正确示例” 和 “错误示例” 来理解规则，并在生成代码时模仿 “正确示例” 的风格。
4.  **自我检查**: 在输出最终代码前，请根据本文档中的检查清单（Checklist）进行自我审查，确保所有规则都得到满足。

现在，请开始学习具体的规则。
```

### 4.2. 强化 “角色扮演”

在 `CRITICAL IDENTITY` 部分，可以进一步强化 AI 的角色，并赋予其对“美学”的追求。

**优化建议**:

在 `CRITICAL IDENTITY` 中增加：

- **大师级 UI/UX 设计能力**:
  - “你不仅是技术专家，更是一位对视觉美学有极致追求的设计大师。”
  - “你生成的代码不仅功能完善，更在布局、色彩、间距、动效等方面达到业界顶尖水评。”
  - “你会主动运用设计原则（如对齐、对比、重复、亲密性）来组织 UI 元素，创造出富有呼吸感和韵律感的界面。”

### 4.3. 引入 “动态反馈” 机制 (高级)

如果条件允许，可以设计一种机制，让 AI 在多次生成后，能够根据之前的输出质量进行自我调整。这需要更复杂的工程支持，但可以作为一个长远目标。

**思路**:

1.  **引入 `prompt-result.md` 的概念**: 将 AI 的每次生成结果保存下来。
2.  **在下一次请求中引用**：在新的请求中，可以摘要性地告诉 AI 上一次生成结果的优缺点，并让其在新生成中进行改进。

## 5. 总结

通过对 `prompts` 文件夹进行结构化重组，对 `prompt.md` 的内容进行层次化、原子化和示例化优化，并引入元指令来引导 AI 学习，我们可以显著提升 AI 对 prompt 规则的理解深度和执行准确性，从而生成更优秀、更精美的 Lynx 代码和布局。