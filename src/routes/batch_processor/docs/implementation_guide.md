# LightChart 提示词优化实施指南

## 🎯 核心问题与解决方案

### 当前问题诊断
1. **信息过载**：6055行单一文件，超出AI有效处理范围
2. **重复错误**：PIE图表size属性错误出现4次，说明现有规则无效
3. **结构混乱**：缺乏清晰的信息层次和执行优先级
4. **维护困难**：内容重复冗余，难以更新和扩展

### 解决方案概览
**模块化 + 优先级 + 记忆强化 = 高效提示词系统**

## 📋 立即执行方案

### Phase 1: 紧急修复 (今天完成)

#### 1.1 创建核心规则文件
**文件位置**：`src/routes/batch_processor/prompts/core/CriticalRules.ts`

```typescript
export const LIGHTCHART_CRITICAL_RULES = `
=== 🚨 P0级别 - 绝对不能违反的20条核心规则 ===

🔥 RULE 01: PIE图表radius强制规则
TRIGGER: 看到 type: 'pie'
REACTION: 立即配置 radius: ['0%', '80%']
FORBIDDEN: 绝对禁止使用 size 属性
MEMORY: PIE = radius，永远记住

🔥 RULE 02: marker样式层级强制规则  
TRIGGER: 看到 marker:
REACTION: 立即使用 marker: { shapeStyle: { fill, stroke } }
FORBIDDEN: 绝对禁止在marker层级直接配置样式
MEMORY: marker = shapeStyle子对象，永远记住

🔥 RULE 03: BAR图表颜色配置强制规则
TRIGGER: 看到 type: 'bar'
REACTION: 立即在option层级配置 colors: [...]
FORBIDDEN: 绝对禁止在series中配置shapeStyle
MEMORY: BAR = colors数组，永远记住

🔥 RULE 04: 环境检查强制规则
TRIGGER: 图表初始化
REACTION: 立即检查 if (typeof lynx === 'undefined' || !lynx.krypton) return;
FORBIDDEN: 绝对禁止跳过环境检查
MEMORY: 初始化 = 环境检查，永远记住

🔥 RULE 05: 异步延迟强制规则
TRIGGER: 图表创建后
REACTION: 立即使用 setTimeout(() => this.updateChart(), 100);
FORBIDDEN: 绝对禁止同步调用setOption
MEMORY: 创建后 = 延迟调用，永远记住

... (其他15条核心规则)
`;
```

#### 1.2 创建图表专家模块
**文件位置**：`src/routes/batch_processor/prompts/charts/PieChartExpert.ts`

```typescript
export const PIE_CHART_EXPERT = `
=== 🥧 PIE图表专家模块 ===

🎯 专用配置规则:
- 必须使用 radius: ['0%', '80%'] 
- 必须包含 encode: {name: 'name', value: 'value'}
- 必须使用 series.data 而不是 option.data
- 中心位置推荐 center: ['50%', '45%']

🚨 高频错误预防:
- 绝对禁止 size 属性 (导致apply错误)
- 绝对禁止 option.data (PIE图表专用series.data)
- 绝对禁止缺少 encode 配置

📋 标准模板:
series: [{
  type: 'pie',
  radius: ['0%', '80%'],
  center: ['50%', '45%'],
  data: [...],
  encode: {name: 'name', value: 'value'},
  label: {show: true, position: 'outside'}
}]

✅ 检查清单:
□ 使用radius而不是size？
□ 包含encode配置？
□ 使用series.data？
□ 数据数组非空？
`;
```

#### 1.3 重构主加载器
**文件位置**：`src/routes/batch_processor/prompts/LightChartPromptLoader.ts`

```typescript
import { LIGHTCHART_CRITICAL_RULES } from './core/CriticalRules';
import { PIE_CHART_EXPERT } from './charts/PieChartExpert';
import { LINE_CHART_EXPERT } from './charts/LineChartExpert';
import { BAR_CHART_EXPERT } from './charts/BarChartExpert';

export const LIGHTCHART_PROMPT_CONTENT = `
${LIGHTCHART_CRITICAL_RULES}

=== 📊 图表类型专家模块 ===
${PIE_CHART_EXPERT}
${LINE_CHART_EXPERT}
${BAR_CHART_EXPERT}

=== 🎯 执行流程控制 ===
STEP 1: 加载核心规则 (P0级别，必须100%执行)
STEP 2: 识别图表类型，加载对应专家模块
STEP 3: 应用标准模板，生成完整代码
STEP 4: 执行质量检查清单

=== 🔥 记忆强化训练 ===
PIE图表 → radius (永远不用size)
marker配置 → shapeStyle (永远不直接配)
BAR图表 → colors (永远不用shapeStyle)
图表初始化 → 环境检查 (永远不跳过)
`;

export default {
  LIGHTCHART_PROMPT_CONTENT,
};
```

### Phase 2: 结构优化 (明天完成)

#### 2.1 完善专家模块
创建完整的图表类型专家模块：
- `LineChartExpert.ts` - LINE图表专用规则和模板
- `BarChartExpert.ts` - BAR图表专用规则和模板  
- `MixedChartExpert.ts` - 混合图表专用规则和模板

#### 2.2 建立执行引擎
**文件位置**：`src/routes/batch_processor/prompts/execution/ExecutionEngine.ts`

```typescript
export const EXECUTION_ENGINE = `
=== ⚡ AI执行引擎 ===

🎯 三阶段执行流程:
PHASE 1 - 分析阶段:
1. 识别用户需求中的图表类型
2. 加载对应的专家模块
3. 检查数据结构和配置要求

PHASE 2 - 验证阶段:  
1. 验证P0级别核心规则
2. 检查高频错误预防清单
3. 确认配置完整性和正确性

PHASE 3 - 生成阶段:
1. 应用标准代码模板
2. 生成完整的四文件结构
3. 执行最终质量检查

🚨 强制执行规则:
- 每个阶段必须完成才能进入下一阶段
- P0级别规则违反时必须停止并修复
- 生成代码前必须通过所有检查清单
`;
```

#### 2.3 质量保证机制
**文件位置**：`src/routes/batch_processor/prompts/quality/QualityAssurance.ts`

```typescript
export const QUALITY_ASSURANCE = `
=== 🛡️ 质量保证机制 ===

🔍 自动检查清单:
PIE图表检查:
□ ✓ 使用radius而不是size？
□ ✓ 包含encode配置？
□ ✓ 使用series.data？

LINE图表检查:
□ ✓ marker样式在shapeStyle内？
□ ✓ lineStyle配置正确？
□ ✓ 避免直接样式配置？

混合图表检查:
□ ✓ 配置双Y轴？
□ ✓ 指定yAxisIndex？
□ ✓ 颜色在option层级？

🚨 运行时错误预防:
- apply错误 → 检查PIE图表size属性
- Canvas错误 → 检查环境和延迟调用
- 样式错误 → 检查配置层级结构
`;
```

### Phase 3: 智能化升级 (后天完成)

#### 3.1 动态加载机制
根据用户需求动态加载相关模块，避免信息过载

#### 3.2 效果监控系统
跟踪常见错误的出现频率，持续优化规则

#### 3.3 自适应优化
基于使用数据自动调整规则优先级

## 📊 预期效果

### 量化指标
- **文件大小**：从6055行减少到核心部分<500行
- **错误率**：PIE图表size错误从100%降到0%
- **成功率**：整体代码生成成功率从85%提升到98%+
- **维护效率**：模块化结构提升90%维护效率

### 质量提升
- **一致性**：消除重复和矛盾内容
- **可读性**：清晰的模块结构和优先级
- **执行效果**：AI能准确执行关键规则
- **用户体验**：一次性生成高质量代码

## 🚀 验证标准

使用之前失败的用户代码案例进行测试：
1. **PIE图表size属性案例** → 必须100%使用radius
2. **marker样式层级案例** → 必须100%使用shapeStyle
3. **混合图表配置案例** → 必须100%配置双Y轴
4. **运行时apply错误案例** → 必须100%预防

## 📋 实施检查清单

### 今天必须完成:
- [ ] 创建CriticalRules.ts (20条核心规则)
- [ ] 创建PieChartExpert.ts (PIE图表专用模块)
- [ ] 重构LightChartPromptLoader.ts (模块化加载)
- [ ] 测试PIE图表size错误修复效果

### 明天必须完成:
- [ ] 完善所有图表专家模块
- [ ] 创建ExecutionEngine.ts (执行流程控制)
- [ ] 建立QualityAssurance.ts (质量保证)
- [ ] 测试所有高频错误修复效果

### 后天必须完成:
- [ ] 实现动态加载机制
- [ ] 建立效果监控系统
- [ ] 完成全面测试验证
- [ ] 部署优化后的提示词系统

通过这个系统性的优化方案，我们将彻底解决当前提示词的所有问题，建立一个高效、可维护、高质量的LightChart代码生成系统。
