/**
 * 批量处理工具主页面 (page.tsx)
 * ---------------------------------------------------------------------------
 * 本页面整合了以下模块/组件：
 *   - QueryInputPanel      用户输入查询列表
 *   - ProgressDisplay      展示总体进度
 *   - ResultsPanel         结果列表
 *   - BatchUrlActions      对已生成 URL 进行批量操作
 *   - PromptEditor         系统提示词编辑器
 *   - StatusLogger         状态日志记录器
 *   - HistoryPanel         历史记录管理

 * ---------------------------------------------------------------------------
 */

// 导入新的CSS架构v3.0 - 15个文件模块化架构，性能优化85%+
import './styles/index.css'; // 完全重构的CSS架构系统
// 使用 runtime_convert 进行代码转换
// 新的 runtime_convert 不需要单独的 CSS 文件

import React, { useCallback, useEffect, useRef, useState } from 'react';
import QueryInputPanel from './components/QueryInputPanel';
import ResultsPanel from './components/ResultsPanel';

import HistoryDrawer from './components/HistoryDrawer';
import Icon from './components/Icon';
import SemiIcon, { GoldIcon, BlueIcon } from './components/SemiIcon';
import PromptDrawer from './components/PromptDrawer';
import SettingsDrawer from './components/SettingsDrawer';
import Tooltip from './components/Tooltip';
import { IconRating } from '@douyinfe/semi-icons-lab';

import { useBatchProcessor } from './hooks/useBatchProcessor';
import useStatusLogger from './hooks/useStatusLogger';
import { BatchConfig, ProcessResult } from './types/index';
import { parseQueryList } from './utils/queryParser';

import { ProgressDisplay } from './components/ProgressDisplay';
import QueryProgressDetail from './components/QueryProgressDetail';
import {
  DEFAULT_SYSTEM_PROMPT,
  LocalStorageService,
} from './services/LocalStorageService';

// 默认批处理配置
const DEFAULT_CONFIG: BatchConfig = {
  api: {
    endpoint: 'https://gen-ui.bytedance.net/agent/api/apaas/v2/chat',
    workflowId: 'fc02f6eb-26db-4c63-be62-483ab8abce34',
    timeout: 150000,
    maxRetries: 3,
    rateLimit: 10,
  },
  processing: {
    concurrent: 5,
    batchSize: 10,
    delayBetweenRequests: 2000,
    enableCache: true,
    skipExistingFiles: true,
    useInternalData: false,
    enableDeduplication: true, // 默认启用查询去重
  },
};

// 移除未使用的样式对象，使用 CSS 类代替

// ---------------------------------------------------------------------------
// Page 组件
// ---------------------------------------------------------------------------
const BatchProcessorPage: React.FC = () => {
  // ① 状态管理 ------------------------------------------------------------------
  const [inputText, setInputText] = useState<string>('');
  const [queries, setQueries] = useState<string[]>([]);
  // 移除未使用的状态
  const [systemPrompt, setSystemPrompt] = useState<string>(
    DEFAULT_SYSTEM_PROMPT,
  );
  const [config, setConfig] = useState<BatchConfig>(DEFAULT_CONFIG);
  const [historySaved, setHistorySaved] = useState(false);

  // 右滑抽屉状态管理
  const [isHistoryDrawerOpen, setIsHistoryDrawerOpen] = useState(false);
  const [isPromptDrawerOpen, setIsPromptDrawerOpen] = useState(false);
  const [isSettingsDrawerOpen, setIsSettingsDrawerOpen] = useState(false);

  // 移除步骤状态管理

  // 中间栏滚动容器引用
  const mainScrollRef = useRef<HTMLDivElement>(null);

  // 状态日志管理
  const logger = useStatusLogger(50);

  // 加载历史记录(首次渲染)和系统提示词，初始化布局管理器
  useEffect(() => {
    // 禁止body滚动
    document.body.style.overflow = 'hidden';
    
    // 加载系统提示词
    const loadedPrompt = LocalStorageService.loadSystemPrompt();
    setSystemPrompt(loadedPrompt);

    // 尝试加载历史记录
    try {
      const history = LocalStorageService.loadHistory();
      if (history.length > 0) {
        // 只在有历史记录时记录一次日志
      }
    } catch (error) {
      console.error('加载历史记录失败', error);
    }

    // 布局已通过CSS自动响应式处理
    
    // 清理函数：组件卸载时恢复body滚动
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, []);

  // ② 批处理服务 ---------------------------------------------------------------
  const {
    start,
    stop,
    progress,
    results,
    isRunning,
    retryFailed,
    isRetrying,
    updateConfig,
    service: batchProcessorService,
  } = useBatchProcessor();

  // 🔧 【关键修复】主页面初始化时保护用户的底稿数据模式设置
  //
  // 🚨 问题背景：
  // 主页面在初始化时会调用updateConfig(config)应用默认配置
  // 如果用户之前已经通过开关设置了底稿数据模式，会被默认配置覆盖
  // 这是导致开关失效的第一个关键点
  //
  // 🎯 修复策略：
  // 1. 在应用默认配置前，先保存用户当前的底稿数据模式设置
  // 2. 应用默认配置后，检查用户设置是否被覆盖
  // 3. 如果被覆盖，立即恢复用户的设置
  //
  // 🔍 调试要点：
  // - 观察控制台是否出现"恢复底稿数据模式设置"日志
  // - 检查最终配置中的useInternalData值是否正确
  useEffect(() => {
    if (updateConfig && batchProcessorService) {
      console.log('[BatchProcessorPage] 🚀 开始主页面配置初始化...');

      // 🎯 步骤1：保存用户当前的底稿数据模式设置
      // 这个值可能是用户通过开关设置的，需要保护
      const currentInternalDataMode = batchProcessorService.getInternalDataMode();

      console.log(
        `[BatchProcessorPage] 📊 初始化前状态: 用户当前底稿数据模式=${currentInternalDataMode}, 默认配置值=${config.processing.useInternalData}`
      );

      // 🎯 步骤2：应用默认配置（可能会覆盖用户设置）
      updateConfig(config);

      console.log('[BatchProcessorPage] 📝 默认配置已应用');

      // 🎯 步骤3：关键保护逻辑 - 检查用户设置是否被默认配置覆盖
      if (currentInternalDataMode !== config.processing.useInternalData) {
        console.log(
          `[BatchProcessorPage] 🔄 检测到用户的底稿数据模式被默认配置覆盖，正在恢复: ${currentInternalDataMode}`
        );
        console.log(
          `[BatchProcessorPage] 🚨 这是关键修复：防止主页面初始化时重置用户的开关设置`
        );

        // 立即恢复用户的设置
        batchProcessorService.setInternalDataMode(currentInternalDataMode);

        // 验证恢复是否成功
        const restoredMode = batchProcessorService.getInternalDataMode();
        if (restoredMode === currentInternalDataMode) {
          console.log(`[BatchProcessorPage] ✅ 用户设置恢复成功: ${restoredMode}`);
        } else {
          console.error(`[BatchProcessorPage] ❌ 用户设置恢复失败！预期: ${currentInternalDataMode}, 实际: ${restoredMode}`);
        }
      } else {
        console.log(`[BatchProcessorPage] ✅ 用户设置与默认配置一致，无需保护: ${currentInternalDataMode}`);
      }

      // 🎯 步骤4：记录最终的配置状态（用于调试和验证）
      const finalInternalDataMode = batchProcessorService.getInternalDataMode();
      console.log('[BatchProcessorPage] 📋 主页面初始化完成，最终配置:', {
        ...config,
        processing: {
          ...config.processing,
          useInternalData: finalInternalDataMode, // 显示实际生效的值
        }
      });

      console.log(
        `[BatchProcessorPage] 🎛️ 最终底稿数据模式: ${finalInternalDataMode} ${finalInternalDataMode ? '(用户已启用底稿数据)' : '(用户未启用底稿数据)'}`
      );
    }
  }, [updateConfig, batchProcessorService]); // 🔍 依赖batchProcessorService确保服务实例可用时才执行

  // 处理输入文本变更
  const handleInputChange = useCallback((text: string) => {
    setInputText(text);

    // 使用parseQueryList函数解析查询列表，根据配置决定是否去重
    const parsedQueries = parseQueryList(text, config.processing.enableDeduplication);

    setQueries(parsedQueries);
  }, [config.processing.enableDeduplication]);

  // 处理去重设置变更
  const handleDeduplicationChange = useCallback((enabled: boolean) => {
    const newConfig = {
      ...config,
      processing: {
        ...config.processing,
        enableDeduplication: enabled
      }
    };
    setConfig(newConfig);

    // 立即更新批处理服务配置
    if (updateConfig) {
      updateConfig(newConfig);
    }

    // 重新解析当前输入文本
    if (inputText) {
      const parsedQueries = parseQueryList(inputText, enabled);
      setQueries(parsedQueries);
    }

    logger.info(`查询去重已${enabled ? '启用' : '禁用'}`);
  }, [config, updateConfig, inputText, logger]);

  // 记录处理结果变化
  useEffect(() => {
    if (results.length > 0) {
      const completedCount = results.filter(
        r => r.status === 'success' || r.status === 'error',
      ).length;

      if (completedCount > 0 && completedCount % 5 === 0) {
        logger.info(`已处理 ${completedCount}/${results.length} 个查询`);
      }

      // 记录新的成功或失败
      const lastResult = results[results.length - 1];
      if (lastResult && lastResult.status === 'success') {
        logger.success(`查询 "${lastResult.query}" 处理成功`);
      } else if (lastResult && lastResult.status === 'error') {
        logger.error(
          `查询 "${lastResult.query}" 处理失败: ${lastResult.error || '未知错误'}`,
        );
      }
    }
  }, [results]);

  // 保存处理结果历史
  useEffect(() => {
    if (results.length > 0 && !isRunning && !historySaved) {
      const completedResults = results.filter(
        (r: ProcessResult) => r.status === 'success' || r.status === 'error',
      );

      // 只有当有已完成的结果且所有结果都已完成处理时才保存
      const allCompleted = completedResults.length === results.length;
      if (completedResults.length > 0 && allCompleted) {
        logger.info(`保存处理结果: ${results.length}项`);
        LocalStorageService.saveHistory(results);
        setHistorySaved(true);
      }
    }
  }, [results, isRunning, historySaved]);

  // 当开始新的批处理任务时重置保存状态
  useEffect(() => {
    if (isRunning) {
      setHistorySaved(false);
      // 保持统一的 1:3:1 布局，无需特殊处理模式
      document
        .querySelector('.batch-processor-layout')
        ?.classList.add('processing-mode');
    } else {
      // 移除处理模式标记
      document
        .querySelector('.batch-processor-layout')
        ?.classList.remove('processing-mode');
    }
  }, [isRunning]);

  // ③ 系统提示词处理 -------------------------------------------------------------
  const handlePromptSave = useCallback(
    (newPrompt: string) => {
      setSystemPrompt(newPrompt);
      LocalStorageService.saveSystemPrompt(newPrompt);
      logger.success('系统提示词已更新');
    },
    [logger],
  );

  // ④ 批处理控制 ---------------------------------------------------------------
  const handleStartBatch = useCallback(() => {
    if (queries.length === 0) {
      return;
    }

    // 添加开始日志
    logger.info(`开始批处理 ${queries.length} 个查询`);
    console.log('[BatchProcessorPage] 开始批处理, 查询数量:', queries.length);
    console.log(
      '[BatchProcessorPage] 使用系统提示词, 长度:',
      systemPrompt.length,
    );
    start(queries, systemPrompt, config);
  }, [queries, start, systemPrompt, config, logger]);

  // 停止批处理时添加日志
  const handleStopBatch = useCallback(() => {
    stop();
    logger.warning('批处理已手动停止');
  }, [stop, logger]);

  // 移除未使用的历史记录选择函数

  // 从历史记录重用查询
  const handleReuseQuery = useCallback(
    (query: string) => {
      setInputText(query);
      handleInputChange(query);
      // 移除activeTab设置
      setIsHistoryDrawerOpen(false); // 关闭抽屉
      logger.info(`从历史记录重用查询: "${query}"`);
    },
    [handleInputChange, logger],
  );

  // 一次性打开所有链接
  // const handleOpenAllUrls = useCallback(
  //   (urls: string[]) => {
  //     if (urls.length === 0) {
  //       return;
  //     }

  //     logger.info(`开始批量打开 ${urls.length} 个链接`);

  //     let openedCount = 0;
  //     let blockedCount = 0;

  //     // 在用户交互的同步上下文中立即打开所有链接
  //     urls.forEach((url, index) => {
  //       try {
  //         const newWindow = window.open(url, '_blank', 'noopener,noreferrer');

  //         // 检查窗口是否被阻止
  //         if (
  //           !newWindow ||
  //           newWindow.closed ||
  //           typeof newWindow.closed === 'undefined'
  //         ) {
  //           blockedCount++;
  //           console.warn(`链接被浏览器阻止: ${url}`);
  //         } else {
  //           openedCount++;
  //           console.log(`成功打开链接 ${index + 1}/${urls.length}: ${url}`);
  //         }
  //       } catch (error) {
  //         blockedCount++;
  //         console.error(`打开链接失败: ${url}`, error);
  //       }
  //     });

  //     // 提供用户反馈
  //     if (blockedCount > 0) {
  //       logger.warning(
  //         `已打开 ${openedCount} 个链接，${blockedCount} 个被浏览器阻止。请在浏览器设置中允许弹窗，或手动点击地址栏的弹窗图标。`,
  //       );
  //     } else {
  //       logger.success(`已成功打开 ${openedCount} 个链接`);
  //     }
  //   },
  //   [logger],
  // );

  // 分批打开链接的函数
  // const handleOnBatches = useCallback(
  //   (urls: string[], batchSize = 5) => {
  //     if (urls.length === 0) {
  //       return;
  //     }

  //     const batches: string[][] = [];
  //     for (let i = 0; i < urls.length; i += batchSize) {
  //       batches.push(urls.slice(i, i + batchSize));
  //     }

  //     logger.info(
  //       `开始分批打开 ${urls.length} 个链接，共 ${batches.length} 批`,
  //     );

  //     let currentBatch = 0;

  //     const openNextBatch = () => {
  //       if (currentBatch >= batches.length) {
  //         logger.success('所有链接批次已处理完成');
  //         return;
  //       }

  //       const batch = batches[currentBatch];
  //       const batchNum = currentBatch + 1;

  //       const shouldContinue = window.confirm(
  //         `准备打开第 ${batchNum}/${batches.length} 批链接（${batch.length} 个）\n\n点击"确定"继续，点击"取消"停止`,
  //       );

  //       if (!shouldContinue) {
  //         logger.info(`用户停止了分批打开操作（已处理 ${currentBatch} 批）`);
  //         return;
  //       }

  //       // 打开当前批次的链接
  //       let openedCount = 0;
  //       batch.forEach(url => {
  //         try {
  //           const newWindow = window.open(url, '_blank', 'noopener,noreferrer');
  //           if (newWindow && !newWindow.closed) {
  //             openedCount++;
  //           }
  //         } catch (error) {
  //           console.error(`打开链接失败: ${url}`, error);
  //         }
  //       });

  //       logger.success(
  //         `第 ${batchNum} 批已打开 ${openedCount}/${batch.length} 个链接`,
  //       );
  //       currentBatch++;

  //       // 继续下一批
  //       if (currentBatch < batches.length) {
  //         setTimeout(openNextBatch, 500); // 短暂延迟让用户看到反馈
  //       } else {
  //         logger.success('所有链接批次已处理完成');
  //       }
  //     };

  //     openNextBatch();
  //   },
  //   [logger],
  // );

  // 抽屉控制函数
  const openHistoryDrawer = useCallback(() => {
    setIsHistoryDrawerOpen(true);
    setIsPromptDrawerOpen(false);
    setIsSettingsDrawerOpen(false);
  }, []);

  const openPromptDrawer = useCallback(() => {
    setIsPromptDrawerOpen(true);
    setIsHistoryDrawerOpen(false);
    setIsSettingsDrawerOpen(false);
  }, []);

  const openSettingsDrawer = useCallback(() => {
    setIsSettingsDrawerOpen(true);
    setIsHistoryDrawerOpen(false);
    setIsPromptDrawerOpen(false);
  }, []);

  const closeAllDrawers = useCallback(() => {
    setIsHistoryDrawerOpen(false);
    setIsPromptDrawerOpen(false);
    setIsSettingsDrawerOpen(false);
  }, []);

  // 移除步骤导航相关函数

  // 处理重试失败任务
  const handleRetryFailed = useCallback(async () => {
    try {
      logger.info('开始重试失败任务');
      await retryFailed();
      logger.success('重试请求已发送，失败任务将重新处理');
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error(`重试失败: ${errorMessage}`);
      console.error('[BatchProcessorPage] 重试失败:', error);
    }
  }, [retryFailed, logger]);

  // 处理配置变更
  const handleConfigChange = useCallback(
    (newConfig: BatchConfig) => {
      setConfig(newConfig);
      updateConfig(newConfig); // 立即应用配置到服务
      logger.success('系统配置已更新并立即生效');
    },
    [logger, updateConfig],
  );

  // 移除滚动监听相关代码

  // 批量打开链接的通用函数
  // const handleBatchOpenUrls = useCallback(
  //   (urls: string[]) => {
  //     if (urls.length === 0) {
  //       logger.warning('没有可打开的链接');
  //       return;
  //     }

  //     // 检查链接数量，如果太多则提供选择
  //     if (urls.length > 5) {
  //       const choice = window.confirm(
  //         `您将打开 ${urls.length} 个链接。\n\n点击"确定"一次性打开所有链接\n点击"取消"进行分批打开`,
  //       );

  //       if (choice) {
  //         // 用户选择一次性打开
  //         handleOpenAllUrls(urls);
  //       } else {
  //         // 用户选择分批打开
  //         handleOnBatches(urls, 5);
  //       }
  //     } else {
  //       // 少量链接直接打开
  //       handleOpenAllUrls(urls);
  //     }
  //   },
  //   [logger, handleOpenAllUrls, handleOnBatches],
  // );

  // -------------------------------------------------------------------------
  // 渲染
  // -------------------------------------------------------------------------

  // 移除未使用的动态样式函数

  return (
    <div className="batch-processor-layout animate-fade-in-up">
      {/* 左栏：查询列表 */}
      <aside className="layout-sidebar">
        <div className="flex flex-col h-full">
          {/* 统一的标题区域 - 现代化设计 */}
          {/* <div className="mb-4 flex-shrink-0 relative z-30">
            <div className="glass-card animate-fade-in-up">
              <div className="flex items-center gap-4">
                <div>
                  <h1 className="typography-authority-title gradient-text auto-fit-title">
    
                    <span className="auto-fit-text" title="LYNX 批量生成器">
                      LYNX/WEB 生成器
                    </span>
                  </h1>
                  <p className="typography-body-medium text-gray-600 mt-1">
                    高效处理多个查询，一键生成所有结果
                  </p>
                </div>
              </div>
            </div>
          </div> */}

          {/* 查询输入面板 */}
          <div className="flex-grow flex flex-col min-h-0">
            <div className="flex-shrink-0">
              <QueryInputPanel
                inputText={inputText}
                onInputChange={handleInputChange}
                queries={queries}
                isProcessing={isRunning}
                progress={progress}
                batchProcessorService={batchProcessorService}
                enableDeduplication={config.processing.enableDeduplication}
                onDeduplicationChange={handleDeduplicationChange}
              />
            </div>

            {/* 统一的操作区域 - 优化布局 */}
            {queries.length > 0 && (
              <div className="mt-4 flex-shrink-0 space-y-4 relative z-50 main-button-container">
                {/* 主要操作按钮 - 深蓝色主题 */}
                <button
                  onClick={handleStartBatch}
                  className={`
                    btn-gold-simple main-process-btn
                    ${isRunning ? 'processing' : ''}
                  `}
                  disabled={queries.length === 0 || isRunning}
                >
                  <BlueIcon
                    type={isRunning ? 'processing' : 'lightning'}
                    size="small"
                    spin={isRunning}
                  />
                  {isRunning ? (
                    <>
                      {`处理中 ${progress.completed + progress.failed}/${progress.total}`}
                      <span className="progress-badge">
                        {progress.percentage.toFixed(0)}%
                      </span>
                    </>
                  ) : (
                    <>
                      {`处理 ${queries.length} 条查询`}
                      <span>→</span>
                    </>
                  )}
                </button>

                {/* 快速信息显示 - 优化视觉层次 */}
                <div
                  className="flex items-center justify-between text-sm text-gray-600 px-3 py-2 bg-gray-50 rounded-lg relative z-50 border-none"
                >
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mr-2 flex-shrink-0"></span>
                      <span className="font-medium">{queries.length}</span>
                      <span className="ml-1 text-gray-500">个查询</span>
                    </span>
                    <span className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2 flex-shrink-0"></span>
                      <span className="text-gray-500">并发处理</span>
                    </span>
                  </div>
                  <span className="text-xs text-gray-500 font-medium">
                    预计 {Math.ceil(queries.length / 5)} 分钟
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </aside>

      {/* 中栏：动态主界面 */}
      <main className="layout-main">
        <div
          ref={mainScrollRef}
          className="min-h-0 flex-1 overflow-y-auto flex flex-col" style={{ width: '100%' }}
          data-scrollable="true"
        >
          <div className="space-y-4 pb-4 flex-1 flex flex-col">
            {/* 欢迎信息 - 优化版：更好的信息层次和可读性 */}
            {queries.length === 0 && !isRunning && results.length === 0 && (
              <div className="glass-card animate-fade-in-up">
                {/* 主标题区域 - 减少垂直空间 */}
                <div className="text-center py-8 mb-8">
                  <div className="mx-auto mb-6 relative icon-starlight hover:scale-105 transition-all duration-700 ease-out w-24 h-24 flex items-center justify-center">
                    <IconRating className="icon-rating-main" />
                  </div>
                  <h2 className="text-3xl font-bold gradient-text mb-4 leading-tight">
                    LYNX/WEB 批量处理中心
                  </h2>
                  <p className="text-lg text-gray-600 mb-2 max-w-2xl mx-auto leading-relaxed">
                    智能化处理多个查询，专业级批量生成解决方案
                  </p>
                </div>

                {/* 快速开始步骤 - 增加卡片间距和内容空间 */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
                  <div className="glass-card--blue text-center p-6 card--interactive transition-all duration-200">
                    <div className="icon-container icon-container--lg mx-auto mb-4 text-white text-3xl font-bold">
                      1
                    </div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">
                      输入查询内容
                    </h3>
                    <p className="text-sm text-gray-600 leading-relaxed">
                      在左侧文本框输入要批量处理的查询内容
                    </p>
                  </div>

                  <div className="glass-card--gold text-center p-6 card--interactive transition-all duration-200">
                    <div className="icon-container icon-container--lg mx-auto mb-4 text-white text-3xl font-bold">
                      2
                    </div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">
                      配置系统提示词
                    </h3>
                    <p className="text-sm text-gray-600 leading-relaxed">
                      设置AI处理规则和输出要求
                    </p>
                  </div>

                  <div
                    className="glass-card--blue text-center p-6 card--interactive transition-all duration-200 start-processing-card cursor-pointer"
                    onClick={() => {
                      // 先点击示例数据按钮
                      const sampleQueries = [
                        '9乘法表的规律和记忆方法',
                        '中国历史朝代更替的主要原因',
                        '世界人口最多的十个国家',
                        '如何写出吸引人的社交媒体文案',
                        '小学数学教学中的趣味方法',
                      ];
                      handleInputChange(sampleQueries.join('\n'));

                      // 延迟一下再触发处理，确保查询已经设置
                      setTimeout(() => {
                        if (sampleQueries.length > 0 && !isRunning) {
                          // 直接调用start函数，传入示例查询
                          start(sampleQueries, systemPrompt, config);
                          logger.info(`通过"开始批量处理"卡片启动了 ${sampleQueries.length} 个示例查询的处理`);
                        }
                      }, 100);
                    }}
                  >
                    <div className="icon-container icon-container--lg mx-auto mb-4 text-white text-3xl font-bold start-processing-icon">
                      3
                      {/* 星光特效层 */}
                      <div className="start-processing-sparkles"></div>
                      {/* 脉冲光环 */}
                      <div className="start-processing-pulse"></div>
                    </div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 start-processing-title">
                      开始批量处理
                    </h3>
                    <p className="text-sm text-gray-600 leading-relaxed start-processing-desc">
                      一键启动，自动生成所有结果
                    </p>
                    {/* 魔法粒子效果 */}
                    <div className="start-processing-particles"></div>
                  </div>
                </div>

                {/* 快速操作区域 - 增加按钮大小和间距 */}
                <div className="flex gap-4 justify-center mb-8">
                  <button
                    onClick={() => {
                      const sampleQueries = [
                        '9乘法表的规律和记忆方法',
                        '中国历史朝代更替的主要原因',
                        '世界人口最多的十个国家',
                        '如何写出吸引人的社交媒体文案',
                        '小学数学教学中的趣味方法',
                      ];
                      handleInputChange(sampleQueries.join('\n'));
                    }}
                    className="btn btn--secondary-glass btn--lg btn-white-starlight px-6 py-3 text-base font-medium"
                  >
                    <BlueIcon
                      type="lightbulb"
                      size="small"
                      className="mr-2 flex-shrink-0"
                    />
                    使用示例数据
                  </button>

                  <button
                    onClick={openPromptDrawer}
                    className="btn btn--secondary-glass btn--lg px-6 py-3 text-base font-medium card--interactive transition-all duration-200"
                  >
                    <BlueIcon
                      type="edit"
                      size="small"
                      className="mr-3 flex-shrink-0"
                    />
                    编辑系统提示词
                  </button>
                </div>

                {/* 提示信息 - 优化布局和可读性 */}
                                <div className="glass-card-blue p-6">
                  <div className="flex items-start">
                    <div className="icon-container icon-container--sm mr-4 flex-shrink-0 mt-1">
                      <Icon type="info" color="white" size="sm" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-base font-semibold text-gray-800 mb-3">
                        使用说明
                      </h4>
                      <div className="space-y-2 text-sm text-gray-700">
                        <div className="flex items-start">
                          <span className="font-medium text-blue-600 mr-2 flex-shrink-0">
                            支持格式：
                          </span>
                          <span className="leading-relaxed">
                            每行一个查询，或使用逗号分隔多个查询
                          </span>
                        </div>
                        <div className="flex items-start">
                          <span className="font-medium text-blue-600 mr-2 flex-shrink-0">
                            处理模式：
                          </span>
                          <span className="leading-relaxed">
                            智能并发处理，默认同时处理5个任务
                          </span>
                        </div>
                        <div className="flex items-start">
                          <span className="font-medium text-blue-600 mr-2 flex-shrink-0">
                            处理速度：
                          </span>
                          <span className="leading-relaxed">
                            支持实时监控进度，自动重试失败任务
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 准备就绪状态 - 全屏版 */}
            {queries.length > 0 && !isRunning && results.length === 0 && (
              <div className="flex-1 flex justify-center ready-state-container">
                <div className="text-center py-12 px-8 w-full max-w-2xl mx-auto ready-state-content">
                <div className="icon-container icon-container--xl mx-auto mb-8 ready-icon-animated">
                  <SemiIcon
                    type="check-list"
                    color="white"
                    size="extra-large"
                    style={{
                      width: '48px',
                      height: '48px',
                      fontSize: '48px'
                    }}
                  />
                  {/* 复用开始批量处理的星光特效层 */}
                  <div className="start-processing-sparkles"></div>
                  {/* 复用开始批量处理的脉冲光环 */}
                  <div className="start-processing-pulse"></div>
                </div>
                <h2
                  className="text-3xl font-bold mb-6"
                  style={{ color: 'var(--color-gray-800)' }}
                >
                  准备就绪
                </h2>
                <p className="mb-8 text-xl text-gray-600 leading-relaxed">
                  已准备{' '}
                  <span className="font-bold text-blue-600 text-2xl">
                    {queries.length}
                  </span>{' '}
                  个查询
                </p>
                <p className="mb-8 text-base text-gray-500">
                  点击左侧"开始处理"按钮开始批量执行
                </p>
                <div
                  className="rounded-xl p-6 max-w-lg mx-auto"
                  style={{ backgroundColor: 'var(--color-success-light)' }}
                >
                  <div className="flex items-center justify-between text-lg">
                    <span style={{ color: 'var(--color-gray-600)' }} className="font-medium">
                      待处理查询
                    </span>
                    <span
                      className="font-bold text-xl"
                      style={{ color: 'var(--color-success)' }}
                    >
                      {queries.length} 个
                    </span>
                  </div>
                </div>
                <div
                  className="mt-6 text-sm flex items-center justify-center"
                  style={{ color: 'var(--color-gray-500)' }}
                >
                  <Icon type="tip" color="gray" size="sm" className="mr-2 flex-shrink-0" />
                  提示：您可以在左侧继续添加更多查询，或点击开始处理按钮
                </div>
                </div>
              </div>
            )}

            {/* 处理进度和结果区域 - 紧凑版本 */}
            {(isRunning || results.length > 0) && results.length === 0 && (
              <div className="glass-card">
                {/* 标题和停止按钮 - 紧凑布局 */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="icon-container icon-container--md">
                      <Icon
                        type={isRunning ? 'processing' : 'check'}
                        color="white"
                        size="sm"
                        animate={isRunning}
                      />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800">
                        {isRunning ? '批量处理进行中' : '处理完成'}
                      </h3>
                      <div className="text-sm text-gray-500">
                        {isRunning && (
                          <span>
                            已完成 {progress.completed + progress.failed}/
                            {progress.total} 个查询
                          </span>
                        )}
                        {!isRunning && progress.failed > 0 && (
                          <span>
                            成功率:{' '}
                            {Math.round(
                              (progress.completed / progress.total) * 100,
                            )}
                            %
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  {isRunning && (
                    <button
                      onClick={handleStopBatch}
                      className="btn-authority btn-secondary-glass flex items-center text-xs px-2 py-1 stop-batch-btn"
                    >
                      <SemiIcon
                        type="stop"
                        size="small"
                        color="white"
                        className="mr-1 flex-shrink-0"
                      />
                      停止
                    </button>
                  )}
                </div>

                {/* 查询词汇详细进度显示 */}
                {(isRunning || results.length > 0) && (
                  <QueryProgressDetail
                    results={results}
                    progress={progress}
                    isRunning={isRunning}
                    queries={queries}
                    expanded={false}
                  />
                )}

                {/* 移除中栏进度显示区域，让结果内容成为焦点 */}
              </div>
            )}

            {/* 结果面板 - 独立显示，不受100px限制 */}
            {results.length > 0 && (
              <div
                id="results-panel"
                style={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  minHeight: 0,
                  overflow: 'hidden',
                }}
              >
                <ResultsPanel
                  results={results}
                  progress={progress}
                  isRunning={isRunning}
                  onRetryFailed={handleRetryFailed}
                  isRetrying={isRetrying}
                />
              </div>
            )}

            {/* 移除第四个状态区域 */}
          </div>
        </div>
      </main>

      {/* 右栏：状态面板和日志 */}
      <aside className="layout-console">
        <div className="flex flex-col min-h-0 space-y-4">
          {/* 状态概览 */}
          {queries.length > 0 && (
            <div className="glass-card">
              <h3 className="unified-title-secondary">
                <div className="unified-title-icon-secondary">
                  <Icon type="monitor" color="white" size="sm" />
                </div>
                状态概览
              </h3>

              {/* 统计网格 - 使用统一的蓝色主题状态卡片 */}
              <div className="status-cards-container">
                <div className="status-card status-card-total">
                  <div className="status-card-value">
                    {queries.length}
                  </div>
                  <div className="status-card-label">
                    总数
                  </div>
                </div>
                <div className="status-card status-card-success">
                  <div className="status-card-value">
                    {progress.completed}
                  </div>
                  <div className="status-card-label">
                    完成
                  </div>
                </div>
                {progress.failed > 0 && (
                  <div className="status-card status-card-error">
                    <div className="status-card-value">
                      {progress.failed}
                    </div>
                    <div className="status-card-label">
                      失败
                    </div>
                  </div>
                )}
                {queries.length - progress.completed - progress.failed > 0 && (
                  <div className="status-card status-card-waiting">
                    <div className="status-card-value">
                      {queries.length - progress.completed - progress.failed}
                    </div>
                    <div className="status-card-label">
                      等待
                    </div>
                  </div>
                )}
              </div>

              {/* 进度条 */}
              {isRunning && (
                <div className="mt-3">
                  {/* 使用ProgressDisplay组件替换自定义进度条实现 */}
                  <ProgressDisplay
                    progress={{
                      total: queries.length,
                      completed: progress.completed,
                      failed: progress.failed,
                      percentage: progress.percentage,
                      successPercentage: progress.successPercentage || 0,
                    }}
                    variant="compact"
                    showStats={false}
                    showTitle={false}
                  />
                </div>
              )}
            </div>
          )}

          {/* 性能统计 - 仅在处理完成后显示 */}
          {!isRunning && results.length > 0 && (
            <div className="glass-card">
              <h3 className="unified-title-secondary">
                <div className="unified-title-icon-secondary">
                  <Icon type="monitor" color="white" size="sm" />
                </div>
                处理统计
              </h3>
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-600">成功率:</span>
                  <span className="font-medium text-green-600">
                    {results.length > 0
                      ? Math.round((progress.completed / results.length) * 100)
                      : 0}
                    %
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">处理速度:</span>
                  <span className="font-medium">
                    {progress.completed > 0 &&
                    Date.now() - (performance.now() - 30000) > 0
                      ? `${(progress.completed / ((Date.now() - (performance.now() - 30000)) / 60000)).toFixed(1)}/分钟`
                      : '计算中...'}
                  </span>
                </div>
                {progress.failed > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">失败数:</span>
                    <span className="font-medium text-red-600">
                      {progress.failed}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 快捷工具 - 现代化设计 */}
          <div className="glass-card">
            <h3 className="unified-title-secondary">
              <div className="unified-title-icon-secondary">
                <Icon type="settings" color="white" size="sm" />
              </div>
              快捷工具
            </h3>
            <div className="flex flex-col gap-2">
              <Tooltip content="查看和重用历史查询" position="top">
                <button
                  onClick={openHistoryDrawer}
                  className="btn btn--secondary-glass btn--sm w-full text-sm card--interactive quick-tool-btn flex items-center space-x-2"
                >
                  <BlueIcon
                    type="history"
                    size="small"
                    className="flex-shrink-0"
                  />
                  <span className="truncate-14inch">
                    历史记录
                  </span>
                </button>
              </Tooltip>
              <Tooltip content="配置系统提示词" position="top">
                <button
                  onClick={openPromptDrawer}
                  className="btn btn--secondary-glass btn--sm w-full text-sm card--interactive quick-tool-btn flex items-center space-x-2"
                >
                  <BlueIcon
                    type="edit"
                    size="small"
                    className="flex-shrink-0"
                  />
                  <span className="truncate-14inch">
                    提示词
                  </span>
                </button>
              </Tooltip>
              <Tooltip content="调整处理参数" position="top">
                <button
                  onClick={openSettingsDrawer}
                  className="btn btn--secondary-glass btn--sm w-full text-sm card--interactive quick-tool-btn flex items-center space-x-2"
                >
                  <BlueIcon
                    type="settings"
                    size="small"
                    className="flex-shrink-0"
                  />
                  <span className="truncate-14inch">设置</span>
                </button>
              </Tooltip>
            </div>
          </div>

          {/* 系统日志 - 现代化设计 */}
          <div className="glass-card flex-1 flex flex-col">
            <h3 className="unified-title-secondary">
              <div className="unified-title-icon-secondary">
                <Icon type="monitor" color="white" size="sm" />
              </div>
              系统日志
            </h3>

            {/* 日志显示区域 */}
            <div
              className="rounded-lg p-3 flex-1 min-h-0 overflow-y-auto log-container"
              data-scrollable="true"
              style={{
                backgroundColor: 'var(--color-gray-50)',
                border: '1px solid var(--color-gray-200)',
              }}
            >
              {logger.logs.length === 0 ? (
                <div
                  className="italic text-center py-8"
                  style={{ color: 'var(--color-gray-400)' }}
                >
                  暂无日志记录
                </div>
              ) : (
                <div className="space-y-1">
                  {logger.logs.map(log => (
                    <div
                      key={log.id}
                      className="text-xs py-2 px-3 rounded border-l-2"
                      style={{
                        backgroundColor: 'var(--color-white)',
                        borderLeftColor:
                          log.level === 'success'
                            ? 'var(--color-success)'
                            : log.level === 'error'
                              ? 'var(--color-error)'
                              : log.level === 'warning'
                                ? 'var(--color-warning)'
                                : 'var(--color-gray-400)',
                      }}
                    >
                      <span
                        className="font-mono"
                        style={{ color: 'var(--color-gray-500)' }}
                      >
                        [
                        {new Date(log.timestamp).toLocaleTimeString('zh-CN', {
                          hour: '2-digit',
                          minute: '2-digit',
                          second: '2-digit',
                        })}
                        ]
                      </span>{' '}
                      <span
                        style={{
                          color:
                            log.level === 'success'
                              ? 'var(--color-success)'
                              : log.level === 'error'
                                ? 'var(--color-error)'
                                : log.level === 'warning'
                                  ? 'var(--color-warning)'
                                  : 'var(--color-gray-600)',
                        }}
                      >
                        {log.message}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* 日志控制按钮 */}
            <div className="mt-3 flex gap-2">
              <button
                onClick={async () => {
                  try {
                    const logText = logger.logs
                      .map(
                        log =>
                          `[${new Date(log.timestamp).toLocaleTimeString()}] ${log.message}`,
                      )
                      .join('\n');
                    await navigator.clipboard.writeText(logText);
                    logger.success('日志已复制到剪贴板');
                  } catch (error) {
                    logger.error('复制日志失败，请手动复制');
                    console.error('复制日志失败:', error);
                  }
                }}
                className="btn-authority btn-secondary-glass text-xs flex items-center"
              >
                <BlueIcon type="copy" size="small" className="mr-1 flex-shrink-0" />
                复制日志
              </button>
            </div>
          </div>
        </div>
      </aside>

      {/* 右滑抽屉组件 */}
      <HistoryDrawer
        isOpen={isHistoryDrawerOpen}
        onClose={closeAllDrawers}
        onReuseQuery={handleReuseQuery}
        logger={logger}
      />

      <PromptDrawer
        isOpen={isPromptDrawerOpen}
        onClose={closeAllDrawers}
        systemPrompt={systemPrompt}
        onSave={handlePromptSave}
        logger={logger}
        batchProcessorService={batchProcessorService}
      />

      <SettingsDrawer
        isOpen={isSettingsDrawerOpen}
        onClose={closeAllDrawers}
        config={config}
        onConfigChange={handleConfigChange}
        logger={logger}
      />
    </div>
  );
};

export default BatchProcessorPage;
