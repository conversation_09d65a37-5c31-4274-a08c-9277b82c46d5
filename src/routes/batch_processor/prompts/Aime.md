1. 导言
在我们的上一篇文章（Aime：DevInfra 面向复杂现实任务的智能体探索）中，我们介绍了 Aime 1.0 系统，一个基于 PlanAct 方式的多智能体系统。该系统通过精确编排一系列专业、预定义的 Actor 来完成复杂任务，这种 “专家团队” 模式使得 Aime 在处理涉及代码生成、数据分析和写文档等专业领域的任务时，能够展现出较高的效率和质量。
然而，在实践中我们发现，这种依赖于预定义的固定模式，在面对开放、多变的真实世界任务时，暴露出了根本性的脆弱。问题的核心并非某个 Actor 的性能不足，而是整套思维范式的局限，即对于超出其预设能力范围的任务缺乏根据当前任务的具体需求动态调整策略的能力。
这一认知促使我们重新思考智能体的核心能力：一个真正强大的智能体，不仅要善于“执行计划”，更要具备在不确定性中“适应与演进”的韧性。为此，我们将目标从“构建完美的执行机器”转向“培育自适应的智能系统”，致力于让智能体在复杂环境中自主寻路，在持续交互中学习迭代，最终涌现出超越初始设计的智能。

本文将记录我们完成这一核心理念转型的全过程，分享我们关于构建一个能够动态演进的下一代 AI 智能体的思考与实践。我们的探索仍在继续，希望这些经验能为同样致力于将 AI 应用于复杂现实场景的团队提供有价值的参考。

2. PlanAct 框架的结构性缺陷
PlanAct 这种中心化规划在可预测环境中运行良好，却在开放、动态、充满不确定性的现实世界中显露出三个关键缺陷，我们以一个由三个专业化 Agent 组成的系统为例：
1. 信息检索 Agent：精于使用各类工具，从网络、数据库等信源高效搜集信息；
2. 代码生成 Agent：通晓多种编程语言、算法与数据结构，擅长编写功能性代码；
3. 数据可视化 Agent：擅长将数据转化为直观、精美的 HTML 可视化报告。

这种配置分工明确、优势互补。然而，当面对真实任务时，三个核心问题逐渐显现：
[图片]
[图片]
[图片]
静态规划在动态世界中的 “三大困境”
2.1 僵化的规划：无法适应执行过程中的变化
静态规划的第一个核心缺陷是规划的僵化性，它无法根据执行过程中的新情况进行动态调整。
具体案例：在"用户增长分析报告"任务中，Planner 设计了"取数 -> 清洗 -> 关联 -> 可视化"的线性流程。然而，信息检索 Agent 在第一步就发现了一个已预处理好的数据视图，直接输出了最终所需的数据。
问题后果：尽管第一步就获得了干净完整的数据，系统仍然机械地执行后续步骤：
- 代码生成 Agent 对已经干净的数据进行了不必要的"清洗"处理
- 数据关联步骤在已关联好的数据上重复劳动
- 系统资源被浪费在无意义的操作上，整体效率反而下降
根本问题：静态规划无法适应执行过程中的变化，无法识别和利用出现的"捷径"，也无法跳过变得多余的步骤。它用固定的流程惩罚了局部优化，抑制了系统提升整体效率的可能。

2.2 固化的角色：无法应对跨领域复合任务
第二个核心缺陷是角色的固化，即预设的专业化 Agent 难以处理需要跨领域能力的复合型任务。
具体案例：在"构建一个用于监控用户活跃度的实时交互式 Web 仪表盘"任务中，Planner 将后端开发指派给代码生成 Agent，将前端指派给数据可视化 Agent。
问题后果：
- 代码生成 Agent 构建了一个功能完备的 RESTful API
- 数据可视化 Agent 生成了一个精美但完全独立的静态 HTML 页面
- 两者之间缺乏必要的集成，无法实现"实时交互"功能
- 前端 API 调用、数据状态管理等关键工作恰好落在两个 Agent 的能力边界之外
根本问题：当任务需要跨领域能力时，预设的角色分工变得不合理。"各司其职"沦为"各自为政"，产出只是能力的碎片化拼凑，无法形成有机整体。

2.3 沟通的黑洞：信息在传递中不断失真
第三个核心缺陷是消息失真，在缺乏一个共享、动态更新的 “单一事实来源” 时，每个 Agent 都在信息不完整的情况下独立工作。它们的协作并非基于精确的实时上下文，而是依赖于规划之初的静态指令和一次性的结果交接。这使得错误如滚雪球般累积，微小的瑕疵也可能被无限放大。
其中，最危险的并非显而易见的错误（这很容易发现并加以避免），而是隐蔽的信息缺失。
具体案例：在"对比我方产品与竞品，生成优劣势分析报告"任务中：
- 信息检索 Agent 获取了我方完整数据，但对竞品只能获取部分公开信息，关键数据（如安全审计、用户满意度）字段为空
- 它将这份不完整的数据传递给下一个 Agent，但没有说明信息缺失的背景
- 代码生成 Agent 按"空值即 0 分"的逻辑处理，严重低估了竞品
- 数据可视化 Agent 基于这份带严重偏差的数据，生成了误导性的对比图表
根本问题：信息在多个 Agent 之间的传递过程中不断失真，缺乏有效的上下文共享机制。这种"传话游戏"式的协作模式，最终可能导致与事实相悖的结论。

3. 应对更复杂场景的挑战 - Aime 1.5 的架构设计
技术细节可参考论文 Aime: Towards a Fully-Autonomous Multi-Agent Framework
3.1 一个真实的 Case 
[图片]
Aime 的研发同学提供给 Aime 一份产品和技术文档，要求完成 10 个接口的开发并提交 MR。
- 技术文档：Aime 模板管理后端技术方案
- 产品文档：Aime - 模版管理
他晚上 10 点提交任务后下班了，第二天早上起来 Review 结果，Aime 已经完成了所有接口近 2000 行代码的开发。
这个任务的耗时接近2个小时，调用了500多轮LLM。
这个任务效果不错的前提是产品和技术文档写的都比较清楚，后续经过我们和一些用户朋友的实践，在 context 相对全面的情况下 Aime 是可以异步写一些严肃的需求的，我们后续也会持续增强体验和效果

3.2 为什么常规的 Agent 架构很难解决这个超长任务
最开始的 Plan：
[图片]
- 如果是常规的 Multi-Agent 架构，即一个 Planner + 多个固定专家 Actor（子 Agent）：
  - 计划僵硬：例如做一半发现之前 plan 有误，漏掉关键环节，图中要写的代码在阅读完技术和产品文档后发现要写的代码不仅是 service.thrift 的实现，并且还不是直接写实现需要参考之前 service 和 handler、dal 的模式
  - 能力固化：例如实现「根据飞书需求文档和仓库开发手册内容，在代码仓库中编写对应实现并提交 MR」的任务，需要通过 Coder 和飞书 MCP 两个子 Agent 反复调用来完成，上下文切换效率极低
  - 长时间执行任务偏离：上下文过长要压缩，模型可能忘记最初的任务目标，反复回顾或陷入某个特定子任务无法跳出的问题
- 如果是 Single-Agent 架构：500 轮上下文就爆了，而且这个任务每一个子任务都很复杂需要多步，很容易执行偏离 lost in the middle

3.3 我们是怎么做的
3.3.1 整体架构
暂时无法在飞书文档外展示此内容
3.3.2 自适应的 Planner - 解决规划僵硬
Planner 不是一开始设计好就一成不变的，而是基于每个 Actor 执行结果动态调整的，因为：
- 有些 Actor 可能一次执行了多个 Action 或者少执行了某个 Action
- 有些 Actor 可能完成失败
- 有些 Actor 执行完以后，会发现之前的 Plan 可能多规划或少规划了一些 Action
例如，在让 Aime 实现研发任务的场景下，研发同学可能只提供给 Aime 几个设计文档和一个代码仓库地址，Aime 的渐进式规划会基于这些已知信息迭代式地进行探索：如先查看相关文档、再克隆代码仓库、随后实现需求，最终提交 MR。在此过程中，Aime 执行完每个阶段均会基于当前状态动态地决定下一步的计划。例如，若发现文档只查看了一半，则会再规划一个步骤以继续查看文档；同样，若发现在克隆代码时已经初步地实现了需求的大体框架，那么在后续的需求实现中则仅实现具体的细节部分，避免重复工作。整个工作流是自适应动态调整的。在某些步骤由于意外或用户打断中止时，可以基于当前已完成的工作决定后续执行方向，具备更好的鲁棒性。
1. 最开始的 Plan
[图片]

2. Review 完 MR 以后已经知道了接口定义，并不是 step by step 做的
[图片]

...

n. 最终完成的Plan，多了红框的内容，这是在最早计划之外的

[图片]

3.3.3 动态组装的 Actor（动态专家）- 解决角色固化
针对 Agent 设计中的泛化性不足问题——即对于超出其预设能力范围的任务缺乏根据当前任务的具体需求动态调整策略的能力。动态执行器可以根据当前任务的具体需求动态调整自身的角色设定、行为风格，并能灵活选择、加载所需工具集和场景相关知识片段，更精准、高效地应对多样化的任务场景。
在传统多 Agent 架构下，如果要实现「根据飞书需求文档和仓库开发手册内容，在代码仓库中编写对应实现并提交 MR」的任务，需要通过 Coder 和 MCP 两个子 Agent 反复调用来完成。在动态执行器中，只需要一个动态组装的「需求实现 Agent」并携带文件、飞书文档和 Codebase MCP 工具即可在同一个 Agent 内部完成，避免 Agent 之间上下文传递的信息损耗或丢失。
以前面这个 case 举例（实际执行的 Actor 比这个略多）：
动态组装的Actor
定义
召回的工具
召回的知识
Actor拿到的任务
执行结果
读取飞书文档、设计软件架构的 Actor


[图片]

[图片]


[图片]
（图中的不全，其实很多）
[图片]

[图片]

写代码的  Actor
[图片]

[图片]

[图片]

[图片]

[图片]

...... 执行了一段时间，发现要写的不止是这两个文件，以及完成这个rpc接口的代码，并不是直接写到对应的接口实现函数，还有 service、handler、dal 等架构模式，于是：





分析程序结构的 Actor
[图片]

[图片]

[图片]

[图片]

[图片]

Codebase Actor，提交代码

[图片]

[图片]

[图片]

[图片]

[图片]

3.3.4 Planner 与 Actor 的双层进展跟踪 - 解决迷失问题
针对 Agent 执行长任务发生遗忘和迷失的问题——即 Agent 会因为上下文过长而忘记最初的任务目标，反复回顾或陷入某个特定子任务无法跳出的问题。规划器和执行器都追踪了当前执行任务的进展，使 Agent 在长程、多跳任务中能够稳定完成任务，不会因为中间过程过长发生迷失。
- [x] 分析 MR 和文档以理解需求
    - [x] 审查位于 xxx 的 MR
    - [x] 阅读技术方案文档
- [x] 理解代码结构和现有模式
    - [x] 分析 api/idl/nextagent/service.thrift 文件以理解新的接口
    - [x] 分析 template_file.go 和 template_version.go 文件以了解实体结构
- [ ] 实现所需功能
    - [x] 实现模板功能的服务层
    - [x] 实现模板功能的处理层（Handler层）
    - [ ] 在 service.thrift 中实现新的接口逻辑
- [ ] 创建新的 MR 以合并已实现的代码
- [x] 检查 service.thrift 文件中已有的模板相关接口
  - [x] 确认大部分接口已被定义
  - [x] 识别出命名差异："DownloadTemplateFile" 与 "DownloadTemplateFileStream"
- [ ] 如果需要，更新 service.thrift 文件
  - [ ] 决定是否需要将 "DownloadTemplateFile" 重命名为 "DownloadTemplateFileStream"
  - [ ] 确保所有必需的接口都已正确定义
在任务执行过程中，每当 Agent 完成列表中的某一项（可能需要若干轮工具调用），其会主动地更新该进度列表，并据此决定下一步的动作。在决定下一步动作之后，还会从知识库中召回相关知识以进行有效的指导。通过这种方法，避免了在超多轮执行过程中 Agent 的迷失。
4. 实战检验：跨领域的效果验证
为了客观评估 Aime 1.5 框架在实际应用中的效果，我们维护了一套自建评测集并构建了一套多维度的评测体系，用于对日常迭代效果进行追踪和观测。随聊Aime评测
此外，我们也选取了三个公认的、领域差异显著的基准测试进行验证。这三个测试分别从不同维度对智能体的核心能力进行考察：
- GAIA：长链逻辑推理。评估智能体在处理多步骤、跨领域问题时的规划与推理能力。它要求智能体能构建并执行复杂的逻辑链，对规划的准确性和纠错能力提出了很高要求。
- SWE-bench Verified：精准的代码修复。 基于真实的开源项目代码库，要求智能体在复杂的工程环境中精确定位并修复程序 Bug。其结果通过严格的单元测试进行验证，直接衡量智能体对代码的理解与修复能力。
- WebVoyager：动态的网络交互。将智能体置于真实、不可预测的互联网环境中，要求其自主完成多步交互任务。它主要考察智能体在面对环境变化时的感知、适应与恢复能力。
实验数据表明，在采用相同基座模型的前提下，Aime 1.5 的通用框架在上述三个专业领域中，均取得了有竞争力的结果，其表现甚至超过了某些为特定任务设计的专用智能体系统。
[图片]

这些数字为 Aime 1.5 的核心设计理念提供了有力的实践支持。
- GAIA 的优异表现，可归因于 Dynamic Planner 的动态调整能力。GAIA 任务的难点在于其复杂的推理链，静态规划一旦出错，后续步骤便难以为继。Aime 1.5 的 Dynamic Planner 机制允许系统在执行过程中持续评估当前路径。当一条推理路径被证明无效时，系统能够终止错误尝试并重新规划，从而提升了最终完成任务的概率。聊聊 Aime 是如何在 GAIA上取得 SOTA（短暂的～）
- SWE-bench 的有效性，得益于 Actor Factory 的按需生成机制。修复复杂的软件 Bug，通常需要综合运用多种技能。Aime 1.5 的 Actor Factory 展现出了良好的灵活性。它能根据当前任务的具体需求，即时生成并配置最合适的智能体——例如，先实例化一个侧重于“代码阅读与分析”的智能体，再生成一个侧重于 “调试与修改” 的智能体。这种 “按需组装能力” 的模式，相比固定的单一角色智能体，能更好地匹配复杂任务的需求。
- WebVoyager 的鲁棒性，得益于其高效的 “感知-决策” 反馈闭环。在真实网络环境中，页面变化是常见情况，这使得静态计划非常容易失效。Aime 1.5 通过 Dynamic Actor 的主动状态汇报与 Dynamic Planner 的快速响应，构建了一个有效的反馈闭环。该机制使系统能够从执行错误中恢复，并根据新情况动态调整策略，从而在多变的环境中保持较高的任务成功率。Aime Browser如何在WebVoyager上取得SOTA
综合实验结果表明，Aime 1.5的动态演进架构在多个领域都表现出了强大的适应性和问题解决能力。这证明了我们的核心假设：一个设计良好的通用框架，凭借其内在的适应性与动态调整机制，能够在高度专业的领域中达到甚至超过专用系统的性能。

5. 结论与展望
Aime 1.5 的实践初步验证了"动态演进框架"的可行性，但我们也认识到，这只是探索的一步。系统在效率和稳定性方面仍有提升空间，我们近期的工作将围绕以下两个方向改进：
1. 提升执行效率：当前框架的灵活性带来了一定的计算开销。我们已经采取了一些优化措施，将任务的平均执行时间减少了一半以上。后续，我们将继续探索更高效的规划策略、成本感知机制以及任务并行化，以降低实际应用中的资源消耗和时间成本。
2. 增强结果稳定性与自我学习：和许多基于 LLM 的系统一样，Aime 的执行结果存在不确定性。为了提升结果的稳定性，我们已开始引入模板化的经验复用机制，将成功的执行经验固化，用于指导新任务。下一步，我们会系统性地沉淀和泛化这些经验，使得 Aime 在处理相似任务时表现得更加稳定，让 Aime 越用越聪明！

探索未完，邀你同行 -- 参与 Aime 内测，了解最新进展

目前 Aime 正在进行内测，欢迎感兴趣的同学通过链接加入我们的内测交流群！
- 内测群内可以第一时间获取 Aime 最新迭代进展
- 与产品研发同学直接交流，提出宝贵建议
- 后续每一版的重要升级，群内用户将优先体验

期待与你们一起，推动 Aime 变得更好！🌟
暂时无法在飞书文档外展示此内容
