import { UNIFIED_UI_VISUALIZATION_GUIDANCE } from './UnifiedUIVisualizationGuidance';
import { LYNX_FRAMEWORK_CORE } from './LynxFrameworkCore';
import { LYNX_STYLE_SYSTEM } from './LynxStyleSystem';
import { LYNX_COMPONENTS } from './LynxComponents';
import { THREAD_SYNCHRONIZATION } from './ThreadSynchronization';
import { BEST_PRACTICES } from './BestPractices';
import { LYNX_UTILS_SYSTEM } from './LynxUtilsSystem';
import { TTML_STRICT_CONSTRAINTS } from './TTMLStrictConstraints';
import { TTSS_STRICT_CONSTRAINTS } from './TTSSStrictConstraints';

/**
 * 定义核心提示词模块内容。
 * 这些模块包含框架的基础、核心约束和通用规则，是每次请求都必须加载的。
 */
const CORE_PROMPT_MODULES = [
  UNIFIED_UI_VISUALIZATION_GUIDANCE, // 🎨 统一UI设计与可视化专家角色 - 必须第一个加载
  LYNX_FRAMEWORK_CORE,
  LYNX_STYLE_SYSTEM,
  LYNX_COMPONENTS,
];

/**
 * 定义可选的扩展模块内容。
 * 这些模块内容更具体、更深入，将根据用户查询的关键词进行按需检索和加载。
 */
const OPTIONAL_MODULES: Record<string, string[]> = {
  '数据同步,setData,线程': [THREAD_SYNCHRONIZATION],
  '可视化,图表,设计': [UNIFIED_UI_VISUALIZATION_GUIDANCE],
  '高级组件,Canvas,绘图': [LYNX_COMPONENTS],
  '最佳实践,示例,模式': [BEST_PRACTICES],
  'LightChart,图表,工具': [LYNX_UTILS_SYSTEM],
  'TTML,严格约束': [TTML_STRICT_CONSTRAINTS],
  'TTSS,CSS约束': [TTSS_STRICT_CONSTRAINTS],
};

/**
 * Hybrid Prompt Assembler
 * 负责根据用户查询，动态组装核心提示词和相关的扩展模块。
 */
export class HybridPromptAssembler {
  private static instance: HybridPromptAssembler;

  private constructor() {}

  public static getInstance(): HybridPromptAssembler {
    if (!HybridPromptAssembler.instance) {
      HybridPromptAssembler.instance = new HybridPromptAssembler();
    }
    return HybridPromptAssembler.instance;
  }

  /**
   * 加载并拼接所有核心提示词模块的内容。
   * @returns {string} 拼接后的核心提示词字符串。
   */
  private getCorePromptContent(): string {
    return CORE_PROMPT_MODULES.join('\n\n---\n\n');
  }

  /**
   * 根据用户查询分析需要加载的扩展模块。
   * @param {string} query - 用户查询字符串。
   * @returns {string} 拼接后的扩展模块内容字符串。
   */
  private getOptionalContent(query: string): string {
    const relevantModules: string[] = [];
    const queryLower = query.toLowerCase();
    for (const keywords in OPTIONAL_MODULES) {
      if (
        keywords
          .split(',')
          .some(keyword => queryLower.includes(keyword.toLowerCase()))
      ) {
        relevantModules.push(...OPTIONAL_MODULES[keywords]);
      }
    }

    // 去重，避免重复加载
    const uniqueModules = Array.from(new Set(relevantModules));

    if (uniqueModules.length === 0) {
      return '';
    }

    const optionalContent = uniqueModules.join('\n\n---\n\n');

    return '\n 相关文档参考\n\n' + optionalContent + '\n';
  }

  /**
   * 组装最终的混合提示词。
   * @param {string} userQuery - 用户的原始查询。
   * @returns {string} 包含核心提示词和按需加载的扩展内容的最终提示词。
   */
  public assemblePrompt(userQuery: string): string {
    const coreContent = this.getCorePromptContent();
    const optionalContent = this.getOptionalContent(userQuery);

    // 在这里可以添加更复杂的组装逻辑，例如基于Token预算的动态截断等

    return `${coreContent}${optionalContent}

🎯 输出执行指令
现在开始执行任务：

1. 分析用户需求
2. 根据决策树选择技术方案（View+TTSS 或 Canvas）
3. 直接输出完整的Lynx代码文件，使用<FILES>和<FILE>标签格式
4. 确保所有代码可直接运行，符合移动端最佳实践

⚠️ 严格输出约束 - 必须遵守
CRITICAL: 你必须且只能输出完整的 Lynx 代码。禁止输出任何解释、思考、说明文字或自然语言。

禁止输出任何非代码内容！立即开始编码！！`;
  }
}
