# 未被 ModularPromptLoader 引用的文件分析

## 📋 分析结果

基于对 `ModularPromptLoader.ts`、`HybridPromptAssembler.ts` 和相关文件的分析，以下文件**没有被当前主要的 prompt 加载器引用**：

### 🚨 完全未被引用的文件

1. **CognitiveOptimizedPrompt.ts**
   - 状态：未被任何主要加载器引用
   - 建议：可以归档

2. **LynxOutputFormat.ts**
   - 状态：未被任何主要加载器引用
   - 建议：检查内容是否已整合到其他文件

3. **LynxSyntaxEnforcer.ts**
   - 状态：未被任何主要加载器引用
   - 建议：检查内容是否已整合到其他文件

4. **ThreeStageEnhancement.ts**
   - 状态：未被任何主要加载器引用
   - 建议：可能是历史版本，可以归档

5. **ThreeStageEnhancementOptimized.ts**
   - 状态：未被任何主要加载器引用
   - 建议：可能是历史版本，可以归档

6. **ThreeStageEnhancementUltra.ts**
   - 状态：未被任何主要加载器引用
   - 建议：可能是历史版本，可以归档

### 🔧 Utils 目录中未被引用的文件

7. **utils/light-chart-fix.ts**
   - 状态：未被主要加载器引用
   - 建议：检查是否为工具文件

8. **utils/light.ts**
   - 状态：未被主要加载器引用
   - 建议：检查是否为工具文件

### ✅ 被引用的文件（当前活跃）

#### ModularPromptLoader.ts 直接引用：
- `LynxFrameworkCore.ts`
- `LynxComponents.ts`
- `LynxStyleSystem.ts`
- `LynxUtilsSystem.ts`
- `FontAwesome.ts`
- `BestPractices.ts`
- `ThreadSynchronization.ts`
- `TTMLStrictConstraints.ts`
- `TTSSStrictConstraints.ts`
- `LightChartPromptLoader.ts`
- `UnifiedUIVisualizationGuidance.ts`
- `LynxCanvasAudio.ts`

#### 间接引用：
- `LightChartStructuredGuide.ts` (被 LightChartPromptLoader.ts 引用)
- `utils/MasterLevelUIPromptLoader.ts` (引用 ModularPromptLoader)

### 📁 已归档的文件
- `archived/TTSSStyleSystem.ts` (内容已整合到 LynxStyleSystem.ts)

## 🎯 建议操作

### 立即可归档的文件：
```bash
# 历史版本文件
mv ThreeStageEnhancement.ts archived/
mv ThreeStageEnhancementOptimized.ts archived/
mv ThreeStageEnhancementUltra.ts archived/

# 认知优化版本（可能已被新版本替代）
mv CognitiveOptimizedPrompt.ts archived/
```

### 需要检查内容的文件：
1. **LynxOutputFormat.ts** - 检查输出格式规则是否已整合到其他文件
2. **LynxSyntaxEnforcer.ts** - 检查语法强制规则是否已整合到约束文件中
3. **utils/light-chart-fix.ts** - 检查是否为修复工具
4. **utils/light.ts** - 检查是否为工具文件

### 保留的核心文件：
- 所有被 ModularPromptLoader 和 HybridPromptAssembler 引用的文件
- utils/MasterLevelUIPromptLoader.ts（提供兼容性接口）

## 📊 引用关系总结

```
当前活跃的引用链：
ModularPromptLoader.ts (主加载器)
├── 12个核心模块文件
└── LightChartPromptLoader.ts
    └── LightChartStructuredGuide.ts

HybridPromptAssembler.ts (混合加载器)
├── 9个核心模块文件（与主加载器重叠）

utils/MasterLevelUIPromptLoader.ts (兼容性接口)
├── ModularPromptLoader.ts
└── utils/LightChartPromptLoader.md (可能已废弃)
```

## ⚠️ 注意事项

1. **内容验证** - 归档前确保重要内容已整合到活跃文件中
2. **依赖检查** - 确认没有其他地方引用这些文件
3. **测试验证** - 归档后测试主要功能是否正常
