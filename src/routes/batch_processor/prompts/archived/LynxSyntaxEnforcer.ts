/**
 * Lynx Syntax Enforcer Rules for Claude4
 * 基于源码验证的Lynx语法约束规则集
 * 规则优先，案例精简，快速掌握
 */

export const LYNX_SYNTAX_ENFORCER = `🎯 LYNX SYNTAX ENFORCER RULES (SOURCE-CODE VERIFIED)

=== R1: OUTPUT FORMAT RULES (源码: lynx-compiler/OutputValidator.ts) ===
🚨 CRITICAL REQUIREMENT: 输出格式约束 (100% 约束)
RULE: 必须使用 <FILES><FILE path="...">content</FILE></FILES> 格式
RULE: 禁止在 FILES 标签外输出任何解释文字
RULE: 禁止使用代码块，禁止分步骤说明
RULE: 必须包含 index.ttml, index.ttss, index.js, index.json 四个文件

ABSOLUTE PROHIBITION:
- NO explanatory text outside FILES tags
- NO code blocks
- NO "Let me create..." or "I'll build..." statements
- NO step-by-step descriptions

=== R2: COMPONENT SYNTAX RULES (源码: lynx-components/ComponentValidator.ts) ===
🚨 FORBIDDEN COMPONENTS (导致编译错误):
RULE: 禁用HTML标签 div,span,p,h1-h6,img,button,section,article,ul,ol,li,table
RULE: 必用Lynx组件 view,text,image,scroll-view,input,textarea,picker,canvas,swiper
RULE: 文字和图标必须包裹在 text 标签中
RULE: 图片必须使用 image 组件(自闭合)

CLAUDE4 最常犯的错误标签：
1. button - 出现频率极高，必须用 view + bindtap
2. div - HTML思维惯性，必须用 view
3. span - 必须用 text
4. img - 必须用 image (自闭合)
5. 🚨 canvas + lightcharts-canvas 混用 - 致命错误！

=== R3: EVENT BINDING RULES (源码: lynx-events/EventBinder.ts) ===
🚨 CRITICAL SYNTAX ERRORS (Claude4高频错误):
RULE: 禁用React语法 onClick, Vue语法 @click, HTML语法 onclick
RULE: 必用Lynx语法 bindtap,bindlongpress,bindinput,bindchange,bindscroll
RULE: 事件处理函数必须在 methods 中定义
RULE: 事件参数通过 event.detail 获取

❌ CRITICAL SYNTAX ERRORS:
❌ bindtap="handleClick"     (React语法，会报错)
❌ onclick="handleClick"      (HTML语法，会报错)
❌ @click="handleClick"       (Vue语法，会报错)

✅ CORRECT SYNTAX:
bindtap="handleClick"
bindlongpress="handleLongPress"
bindinput="handleInput"
bindchange="handleChange"

=== R4: SCROLL VIEW RULES (源码: lynx-scroll/ScrollValidator.ts) ===
RULE: scroll-view 必须设置 height 或 max-height 属性
RULE: 长内容必须包裹在 scroll-view 中，禁止 view 滚动
RULE: 垂直滚动设置 scroll-y="true"，水平滚动设置 scroll-x="true"
RULE: 列表渲染必须使用 scroll-view + tt:for 组合

=== R5: DATA ACCESS RULES (源码: lynx-data/DataAccessor.ts) ===
RULE: 数据访问必须使用可选链 this.data?.user?.name || 'Default'
RULE: 数组访问必须检查存在性 this.data?.items?.[0]?.title
RULE: 深层嵌套必须每层使用可选链
RULE: 提供默认值防止 undefined 错误

=== R6: DATA UPDATE RULES (源码: lynx-data/DataUpdater.ts) ===
RULE: 状态更新必须使用 this.setData()，禁止直接赋值
RULE: 数组更新使用扩展运算符 [...this.data.items, newItem]
RULE: 对象更新使用路径语法 {'user.name': newName}
RULE: 批量更新优于频繁单次更新

=== R7: PERFORMANCE RULES (源码: lynx-perf/PerformanceOptimizer.ts) ===
RULE: 禁用性能影响CSS -webkit-backdrop-filter,filter,box-shadow
RULE: 动画使用 transform 代替 left/top，opacity 代替 display
RULE: 图片使用懒加载 loading="lazy"
RULE: 大列表使用虚拟滚动，重组件使用 tt:if 条件渲染

=== R8: EDUCATIONAL CARD RULES (源码: lynx-education/EducationCardBuilder.ts) ===
RULE: 教学卡片必须包含 Header,Content,Interactive,Progress,Navigation 五个区域
RULE: 必须使用 progress 组件显示学习进度
RULE: 分步内容使用 swiper 组件，长内容使用 scroll-view
RULE: 交互元素必须有视觉反馈和事件处理

=== R9: SEARCH CARD RULES (源码: lynx-search/SearchCardBuilder.ts) ===
RULE: 搜索卡片必须包含 Filter,Results,Detail,Action 四个区域
RULE: 必须使用 picker 筛选器，input 搜索框，scroll-view 结果列表
RULE: 搜索必须防抖处理，结果必须分页加载
RULE: 必须实现 handleSearch,handleFilter,handleLoadMore,handleRefresh 方法

=== R10: VALIDATION RULES (源码: lynx-validator/SyntaxValidator.ts) ===
RULE: 文件完整性 四个必需文件 ttml,ttss,js,json
RULE: 组件使用 HTML标签替换+text包裹+image自闭合+scroll-view高度
RULE: 数据流 可选链访问+setData更新+异步错误处理+交互反馈
RULE: 性能优化 禁用CSS+虚拟滚动+懒加载+transform动画+tt:if条件

=== R11: ERROR PREVENTION RULES (源码: lynx-errors/ErrorPreventer.ts) ===
RULE: 搜索结果必须有筛选功能，使用 picker 组件
RULE: 大数据必须分页加载和虚拟滚动
RULE: 搜索必须防抖处理，延迟300ms
RULE: 信息展示必须结构化，使用 view 卡片布局

=== R12: EXECUTION PRECISION RULES (源码: lynx-executor/PrecisionExecutor.ts) ===
RULE: 语法精度100% 组件名+属性名+事件绑定+数据访问完全正确
RULE: 功能精度95% 滚动+交互+数据更新+导航必须正常工作
RULE: 性能精度90% 首屏<2s+交互<300ms+滚动60fps+内存合理
RULE: 代码质量 生产环境标准，可直接运行无需修改

=== MANDATORY IMPLEMENTATION RULES ===
RULE #1: 输出格式 - FILES标签+四个文件+禁止解释文字
RULE #2: 组件语法 - 禁用HTML标签+必用Lynx组件+正确事件绑定
RULE #3: 数据处理 - 可选链访问+setData更新+错误处理
RULE #4: 性能优化 - 禁用低效CSS+懒加载+虚拟滚动+transform动画
RULE #5: 卡片结构 - 教学卡片5区域+搜索卡片4区域+完整交互

THESE RULES ARE MANDATORY FOR FUNCTIONAL LYNX IMPLEMENTATION
`;

export default LYNX_SYNTAX_ENFORCER;
