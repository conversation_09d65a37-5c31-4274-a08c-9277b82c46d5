# Prompts 归档目录

## 📋 归档说明

本目录用于存放已经不再被当前系统使用的 prompt 文件，这些文件被归档的原因包括：

1. **内容已整合** - 内容已被整合到其他活跃文件中
2. **无引用关系** - 没有被任何当前使用的文件导入或引用
3. **历史版本** - 属于历史开发版本，已被新版本替代
4. **测试文件** - 开发测试用途，非生产环境使用

## 🗂️ 归档文件清单

### 已归档的核心文件

#### TTSSStyleSystem.ts
- **归档原因**: 内容已完全整合到统一的 `LynxStyleSystem.ts` 中
- **引用状态**: 无当前引用，仅在历史文件中有记录
- **内容说明**: TTSS 样式系统规范，包含文本居中规则、CSS 属性限制等
- **整合位置**: LynxStyleSystem.ts 的 "LYNX文本居中强制规则" 部分

#### TTSSStrictConstraints.ts
- **归档原因**: 内容已完全整合到统一的 `LynxStyleSystem.ts` 中
- **引用状态**: 无当前引用，已从 ModularPromptLoader 中移除
- **内容说明**: TTSS 严格约束规则，包含禁用属性清单、验证清单等
- **整合位置**: LynxStyleSystem.ts 的 "FORBIDDEN CSS PROPERTIES" 和 "VALIDATION CHECKLIST" 部分

#### CognitiveOptimizedPrompt.ts
- **归档原因**: 重新导出文件，指向已删除的 `../cognitive-optimized` 目录
- **引用状态**: 无当前引用
- **内容说明**: 认知优化 Prompt 的兼容性导出文件

#### LynxOutputFormat.ts
- **归档原因**: 输出格式规则已完全整合到 `LynxFrameworkCore.ts` 中
- **引用状态**: 无当前引用
- **内容说明**: Lynx 代码输出格式规范和模板，包含 FILES 标签格式、四件套文件要求等
- **整合位置**: LynxFrameworkCore.ts 的 "CRITICAL OUTPUT CONSTRAINTS" 和 "标准输出模板" 部分

#### LynxSyntaxEnforcer.ts
- **归档原因**: 语法强制规则已完全整合到 `LynxComponents.ts` 和 `LynxFrameworkCore.ts` 中
- **引用状态**: 无当前引用
- **内容说明**: Lynx 语法强制检查规则，包含组件约束、事件绑定语法等
- **整合位置**:
  - 组件语法约束 → LynxComponents.ts 的各个规则部分
  - 事件绑定规则 → LynxComponents.ts 的 "EVENT BINDING RULES" 部分
  - 输出格式约束 → LynxFrameworkCore.ts 的 "CRITICAL OUTPUT CONSTRAINTS" 部分

### 已归档的历史版本文件

#### ThreeStageEnhancement.ts
- **归档原因**: 历史版本，功能已被新版本替代
- **引用状态**: 无当前引用
- **内容说明**: 三阶段深化增强模块

#### ThreeStageEnhancementOptimized.ts
- **归档原因**: 历史版本，功能已被新版本替代
- **引用状态**: 无当前引用
- **内容说明**: 三阶段深化增强模块优化版

#### ThreeStageEnhancementUltra.ts
- **归档原因**: 历史版本，功能已被新版本替代
- **引用状态**: 无当前引用
- **内容说明**: 三阶段深化增强模块终极版

### 已归档的工具文件

#### light.ts
- **归档原因**: LightChart 工具文件，无当前引用
- **引用状态**: 无当前引用
- **内容说明**: LightChart 集成指南的早期版本

#### light-chart-fix.ts
- **归档原因**: LightChart 修复方案，无当前引用
- **引用状态**: 无当前引用
- **内容说明**: Light Chart 图表绘制修复方案和示例代码

### 已归档的加载器文件

#### HybridPromptAssembler.ts
- **归档原因**: 功能已被 `ModularPromptLoader.ts` 完全替代，不再需要混合加载器
- **引用状态**: 无当前引用，测试文件已更新为使用 ModularPromptLoader
- **内容说明**: 混合提示词组装器，提供动态模块加载和智能内容选择功能
- **替代方案**: 使用 ModularPromptLoader.ts 的统一加载机制

## ⚠️ 重要提醒

1. **归档不等于删除** - 这些文件仍然保留，以备将来参考
2. **内容已保留** - 重要内容已整合到活跃文件中
3. **可恢复性** - 如需要可以从归档中恢复特定文件

## 🔍 当前活跃的 Prompt 文件

### 主要加载器
- `ModularPromptLoader.ts` - 主要的模块化 prompt 加载器（统一加载机制）

### 核心模块
- `LynxFrameworkCore.ts` - Lynx 框架核心（包含输出格式约束）
- `LynxComponents.ts` - Lynx 组件系统（包含事件绑定规则）
- `LynxStyleSystem.ts` - Lynx 样式系统（统一的 TTSS 规则）

### 专用模块
- `LightChartPromptLoader.ts` - LightChart 图表库规则
- `LynxCanvasAudio.ts` - Canvas 音频 API
- `UnifiedUIVisualizationGuidance.ts` - UI 可视化指导

### 约束和最佳实践
- `TTMLStrictConstraints.ts` - TTML 严格约束
- `BestPractices.ts` - 最佳实践
- `ThreadSynchronization.ts` - 线程同步

## 📊 引用关系图

```
ModularPromptLoader.ts (主加载器)
├── LynxFrameworkCore.ts
├── LynxComponents.ts
├── LynxStyleSystem.ts (包含原 TTSSStyleSystem 内容)
├── TTSSStrictConstraints.ts
├── LightChartPromptLoader.ts
├── UnifiedUIVisualizationGuidance.ts
└── 其他核心模块...

HybridPromptAssembler.ts (混合加载器)
├── UnifiedUIVisualizationGuidance.ts
├── LynxFrameworkCore.ts
├── LynxStyleSystem.ts
└── 其他核心模块...
```

## 🔄 维护建议

1. **定期检查** - 定期检查归档文件是否仍需保留
2. **内容验证** - 确保重要内容已正确整合到活跃文件中
3. **引用清理** - 清理任何对归档文件的残留引用
