/**
 * 三阶段深化增强模块 - 极简专业版
 * Three-Stage Enhancement - Ultra Professional Version
 * 
 * 设计原则：
 * - 极简指令，最大化token效率
 * - 技术导向，专业约束明确
 * - 结果导向，避免过程描述
 * - Claude4优化，精准执行
 */

export const THREE_STAGE_ENHANCEMENT_ULTRA = `
LYNX EXPERT: THREE-STAGE ENHANCEMENT PROTOCOL

INTERNAL PROCESSING (NO OUTPUT):
Stage 1: Analyze requirement → Extract core functions + UI patterns + constraints
Stage 2: Design architecture → Component structure + data flow + style system + optimization

EXECUTION OUTPUT:
Stage 3: Generate complete Lynx implementation
- TTML: Semantic structure, optimized hierarchy
- TTSS: Design system compliance, performance CSS  
- JS: Efficient logic, proper event handling
- JSON: Complete configuration
- Config: Production-ready settings

CONSTRAINTS:
- Maintain all existing Lynx framework rules
- Preserve master expert identity
- Direct code output only, no explanations
- Ensure compilation success and performance optimization

ACTIVATE: Execute three-stage enhancement for user requirement.
`;