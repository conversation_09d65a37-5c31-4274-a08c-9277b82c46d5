/**
 * LightChart Prompt Loader - AI-Optimized Integration Guide for @byted/lynx-lightcharts
 * IMPORTANT: This is the revised, authoritative guide. It corrects significant errors from previous versions.
 * Do NOT use any Markdown syntax or symbols in the following prompt content.
 */
export const LIGHTCHART_PROMPT_CONTENT =
  'AI GUIDE TO @BYTED/LYNX-LIGHTCHARTS: THE DEFINITIVE MANUAL\n\n' +
  'This guide provides the complete, authoritative rules for generating runnable code for @byted/lynx-lightcharts. Adhering to these principles is mandatory for success.\n\n' +
  'SECTION 1: SETUP AND INTEGRATION\n\n' +
  'Step 1: Component Registration (index.json)\n' +
  '// { "usingComponents": { "lightcharts-canvas": "@byted/lynx-lightcharts/lightcharts-canvas/lightcharts-canvas" } }\n\n' +
  'Step 2: TTML Template Syntax\n' +
  '// <lightcharts-canvas bindinitchart="initChart" canvasName="myChart" style="width: 100%; height: 400px;"/>\n\n' +
  'Component Attributes:\n' +
  '- bindinitchart: (Required) Initialization event handler name.\n' +
  '- canvasName: (Required) Unique string identifier for the canvas.\n' +
  '- useKrypton: (Optional, Default: true) Recommended to keep enabled.\n' +
  '- style: (Optional) Must have explicit width and height.\n\n' +
  'SECTION 2: THE GOLDEN RULE - PURE, SERIALIZABLE JSON OPTIONS\n\n' +
  'CRITICAL: This is the most common point of failure.\n' +
  "The entire 'option' object is converted to a JSON string before being passed to the native rendering engine. Any value that is not a standard JSON data type will be lost or cause errors.\n\n" +
  'FORBIDDEN VALUES:\n' +
  '- Functions (e.g., in formatters or itemStyle.color)\n' +
  '- undefined, NaN, Infinity\n' +
  '- Complex Objects (new Date(), new echarts.graphic.LinearGradient())\n\n' +
  'WHY IT FAILS: JSON.stringify() discards functions and undefined, and converts NaN/Infinity to null. This breaks your chart configuration.\n' +
  'DEBUGGING TECHNIQUE: Before calling setOption, log JSON.stringify(option) to verify its structure is correct.\n\n' +
  'SECTION 3: DATA MODEL - TWO DISTINCT PATTERNS\n\n' +
  'There are two data patterns. Using the wrong one for your chart type will result in a blank chart.\n\n' +
  'PATTERN A: THE STANDARD MODEL (FOR COORDINATE-SYSTEM CHARTS like Line, Bar, Scatter)\n' +
  "RULE: Use 'option.data' to store the dataset and 'series.encode' to map data fields to visual dimensions.\n" +
  'CORRECT EXAMPLE (Line Chart):\n' +
  '// const lineOption = {\n' +
  '//   data: [\n' +
  '//     { "day": "Mon", "revenue": 120 },\n' +
  '//     { "day": "Tue", "revenue": 200 }\n' +
  '//   ],\n' +
  "//   xAxis: { type: 'category' },\n" +
  "//   yAxis: { type: 'value' },\n" +
  '//   series: [{\n' +
  "//     name: 'Revenue',\n" +
  "//     type: 'line',\n" +
  "//     encode: { x: 'day', y: 'revenue' }\n" +
  '//   }]\n' +
  '// };\n\n' +
  'PATTERN B: THE EXCEPTION MODEL (FOR NON-COORDINATE-SYSTEM CHARTS like Pie, Funnel)\n' +
  "RULE: Data MUST be provided directly in 'series.data'. The 'option.data' and 'series.encode' properties are IGNORED for these chart types.\n" +
  "The data format for each item is typically { name: '...', value: ... }.\n" +
  'CORRECT EXAMPLE (Pie Chart):\n' +
  '// const pieOption = {\n' +
  '//   series: [{\n' +
  "//     type: 'pie',\n" +
  '//     data: [\n' +
  "//       { name: 'Marketing', value: 548 },\n" +
  "//       { name: 'Sales', value: 735 }\n" +
  '//     ]\n' +
  '//   }]\n' +
  '// };\n' +
  "ANALYSIS OF PREVIOUS FAILURE: The user's pie chart failed because it used Pattern A (option.data + encode) when Pattern B (series.data) was required.\n\n" +
  'SECTION 4: LIFECYCLE MANAGEMENT - THE ONLY CORRECT WAY\n\n' +
  'CRITICAL: Incorrect state management causes instability and memory leaks.\n' +
  "RULE 1: Store the chart instance directly on the component (e.g., this._myChart), NEVER in 'this.data'. The chart object is not serializable.\n" +
  "RULE 2: Initialize the chart ONLY inside the 'bindinitchart' callback.\n" +
  "RULE 3: Destroy the chart ONLY inside the 'onUnload' lifecycle hook.\n" +
  "ANTI-PATTERN TO AVOID: NEVER call 'destroyChartByCanvasName' or 'chart.destroy()' inside an initialization function like 'initChart'. This causes race conditions and is a common bug.\n" +
  'CORRECT LIFECYCLE EXAMPLE:\n' +
  '// Card({\n' +
  '//   _myChart: null,\n' +
  '//   onUnload() {\n' +
  '//     if (this._myChart) {\n' +
  '//       this._myChart.destroy();\n' +
  '//       this._myChart = null;\n' +
  '//     }\n' +
  '//   },\n' +
  '//   initChart(e) {\n' +
  '//     const { canvasName, width, height } = e.detail;\n' +
  '//     // DO NOT call destroy functions here.\n' +
  '//     const chart = new LynxChart({ canvasName, width, height });\n' +
  '//     chart.setOption(/*...*/);\n' +
  '//     this._myChart = chart;\n' +
  '//   }\n' +
  '// });\n\n' +
  'SECTION 5: STYLING\n\n' +
  'METHOD 1: PER-ITEM STYLING\n' +
  "To style individual items (like pie slices), nest styling properties inside an 'itemStyle' object within the data item itself.\n" +
  'CORRECT PIE CHART STYLING (using Pattern B data structure):\n' +
  '// series: [{\n' +
  "//   type: 'pie',\n" +
  '//   data: [\n' +
  "//     { name: 'Category A', value: 100, itemStyle: { color: '#ff0000' } },\n" +
  "//     { name: 'Category B', value: 200, itemStyle: { color: '#00ff00' } }\n" +
  '//   ]\n' +
  '// }]\n' +
  "ANALYSIS OF PREVIOUS FAILURE: The user's pie chart styling failed because the 'color' property was at the top level of the data object, not nested inside 'itemStyle'.\n\n" +
  'METHOD 2: DATA-DRIVEN STYLING (BEST PRACTICE)\n' +
  "Use the 'visualMap' component to separate data from presentation. This is cleaner and more powerful.\n" +
  '// visualMap: {\n' +
  "//   type: 'piecewise',\n" +
  '//   pieces: [\n' +
  "//     { gte: 250, color: '#FF5733' },\n" +
  "//     { lte: 150, color: '#DAF7A6' }\n" +
  '//   ]\n' +
  '// },\n' +
  "// series: [{ type: 'bar', encode: { ... } }]\n\n" +
  'SECTION 6: FORMATTERS\n\n' +
  'RULE: Function-based formatters are forbidden due to serialization.\n' +
  'OPTION 1: STRING TEMPLATES (Most Common)\n' +
  'Use ECharts-style placeholders.\n' +
  "// tooltip: { formatter: '{b}: {c} ({d}%)' }\n\n" +
  'OPTION 2: RICH TEXT (For Complex Styling)\n' +
  "Import the 'html' helper. This is the ONLY approved way for complex formatters.\n" +
  "// import { html } from '@byted/lightcharts/lib/rich';\n" +
  '// tooltip: { formatter: html\'<div style="color: white;">{b}: {c}</div>\' }\n\n' +
  'SPECIAL CASE: MULTI-SERIES TOOLTIPS\n' +
  "When tooltip.trigger is 'axis' for a multi-series chart, you must use indexed placeholders.\n" +
  "// let formatter = '{b}'; // Category name\n" +
  '// seriesNames.forEach((name, index) => {\n' +
  "//   formatter += '<br/>{a' + index + '}: {c' + index + '}';\n" +
  '// });\n' +
  "// tooltip: { trigger: 'axis', formatter: formatter }\n\n" +
  'SECTION 7: COMPLETE RUNNABLE EXAMPLE (REVISED AND CORRECTED)\n\n' +
  'This example follows all the corrected rules.\n' +
  'TTML:\n' +
  '// <view>\n' +
  '//   <lightcharts-canvas canvasName="lineChart" bindinitchart="initLineChart" />\n' +
  '//   <lightcharts-canvas canvasName="pieChart" bindinitchart="initPieChart" />\n' +
  '// </view>\n\n' +
  'JAVASCRIPT:\n' +
  "// import LynxChart from '@byted/lynx-lightcharts/src/chart';\n" +
  '// Card({\n' +
  '//   _lineChart: null,\n' +
  '//   _pieChart: null,\n' +
  '//   onUnload() {\n' +
  '//     if (this._lineChart) this._lineChart.destroy();\n' +
  '//     if (this._pieChart) this._pieChart.destroy();\n' +
  '//   },\n' +
  '//   initLineChart(e) {\n' +
  '//     const chart = new LynxChart(e.detail);\n' +
  '//     const option = {\n' +
  '//       // Uses Pattern A: option.data + encode\n' +
  '//       data: [\n' +
  '//         { day: "Mon", revenue: 120 },\n' +
  '//         { day: "Tue", revenue: 200 }\n' +
  '//       ],\n' +
  "//       xAxis: { type: 'category' },\n" +
  "//       yAxis: { type: 'value' },\n" +
  '//       series: [{\n' +
  "//         name: 'Revenue',\n" +
  "//         type: 'line',\n" +
  "//         encode: { x: 'day', y: 'revenue' }\n" +
  '//       }]\n' +
  '//     };\n' +
  '//     chart.setOption(option);\n' +
  '//     this._lineChart = chart;\n' +
  '//   },\n' +
  '//   initPieChart(e) {\n' +
  '//     const chart = new LynxChart(e.detail);\n' +
  '//     const option = {\n' +
  '//       // Uses Pattern B: series.data\n' +
  '//       series: [{\n' +
  "//         type: 'pie',\n" +
  '//         data: [\n' +
  "//           { name: 'Marketing', value: 548, itemStyle: { color: '#5470C6' } },\n" +
  "//           { name: 'Sales', value: 735, itemStyle: { color: '#91CC75' } }\n" +
  '//         ]\n' +
  '//       }]\n' +
  '//     };\n' +
  '//     chart.setOption(option);\n' +
  '//     this._pieChart = chart;\n' +
  '//   }\n' +
  '// });\n';
