# Lynx Canvas Audio API 使用示例

基于新创建的 `LynxCanvasAudio.ts` prompt 文件，以下是如何使用 Lynx Canvas Audio API 的完整示例。

## 📋 基本音频可视化应用

### 1. 项目结构
```
audio-visualizer/
├── index.json      # 组件配置
├── index.ttml      # 模板文件
├── index.js        # 逻辑文件
└── index.ttss      # 样式文件
```

### 2. index.json
```json
{
  "usingComponents": {}
}
```

### 3. index.ttml
```html
<scroll-view style="height: 100vh; max-height: 100vh;" scroll-y="true">
  <view class="audio-container">
    <view class="title">
      <text class="title-text">{{title || '音频可视化器'}}</text>
    </view>
    
    <canvas 
      style="width: 100%; height: 300rpx; background: #000;" 
      name="audioCanvas"
    ></canvas>
    
    <view class="controls">
      <view bindtap="onTapSin" class="btn {{testCase === 'sin' ? 'active' : ''}}">
        <text>正弦波</text>
      </view>
      <view bindtap="onTapMic" class="btn {{testCase === 'mic' ? 'active' : ''}}">
        <text>麦克风</text>
      </view>
      <view bindtap="onTapWav" class="btn {{testCase === 'wav' ? 'active' : ''}}">
        <text>播放WAV</text>
      </view>
      <view bindtap="onTapMp3" class="btn {{testCase === 'mp3' ? 'active' : ''}}">
        <text>播放MP3</text>
      </view>
    </view>
    
    <view class="info" tt:if="{{playbackRate}}">
      <text class="info-text">{{playbackRate}}</text>
    </view>
    
    <view class="engine-info">
      <text class="engine-text">音频引擎: {{engineType}}</text>
    </view>
  </view>
</scroll-view>
```

### 4. index.js
```javascript
Card({
  data: {
    testCase: "",
    title: "",
    engineType: "default",
    playbackRate: ""
  },
  
  // Audio components
  canvas: null,
  audioContext: null,
  audioNode: null,
  audioBufferNode: null,
  oldAudio: null,
  newAudio: null,
  analyser: null,
  frameFunction: null,
  
  onReady() {
    // Initialize canvas with correct flow
    this.setupCanvas();

    // Setup canvas interaction
    this.setupCanvasInteraction();
    
    // Initialize audio context
    this.audioContext = lynx.getAudioContext();
    const engineType = lynx.krypton.aurum().engineType || "default";
    this.setData({ engineType });
    
    // Create analyser
    this.analyser = this.audioContext.createAnalyser();
    this.analyser.fftSize = 512; // Optimized for performance
    
    // Start with sine wave
    this.onTapSin();
    
    // Start animation loop
    this.startAnimationLoop();
  },
  
  setupCanvasInteraction() {
    this.canvas.addEventListener("touchstart", (event) => {
      if (event.changedTouches) {
        const touch = event.changedTouches[0];
        const valX = touch.clientX * 2 * SystemInfo.pixelRatio > this.canvas.width;
        const valY = touch.clientY * 2 * SystemInfo.pixelRatio > this.canvas.height;
        this.onTouchCanvas(valX, valY);
      }
    });
  },
  
  startAnimationLoop() {
    const drawFrame = () => {
      lynx.requestAnimationFrame(drawFrame);
      if (this.frameFunction) {
        this.frameFunction();
      }
    };
    lynx.requestAnimationFrame(drawFrame);
  },
  
  // Audio source methods
  onTapSin() {
    this.setData({ testCase: "sin", title: "正弦波生成器" });
    const { node, bufferNode } = this.playSin();
    this.switchAudioNode(node, bufferNode);
  },
  
  onTapMic() {
    this.setData({ testCase: "mic", title: "麦克风输入" });
    lynx.getUserMedia({ audio: true }, (stream) => {
      const node = this.audioContext.createMediaStreamSource(stream);
      this.switchAudioNode(node);
    }, err => {
      console.error('麦克风访问失败:', err);
      this.switchAudioNode(null);
    });
  },
  
  onTapWav() {
    this.setData({ testCase: "wav", title: "WAV 文件播放" });
    const src = "https://example.com/audio.wav"; // Replace with actual URL
    const node = this.playAudioSrc(src);
    this.switchAudioNode(node);
  },
  
  onTapMp3() {
    this.setData({ testCase: "mp3", title: "MP3 文件播放" });
    const src = "https://example.com/audio.mp3"; // Replace with actual URL
    const node = this.playAudioSrc(src);
    this.switchAudioNode(node);
  },
  
  // Core audio methods
  playSin() {
    const ctx = this.audioContext;
    const buf = this.createSinBuffer(ctx, 440); // A4 note
    const source = ctx.createBufferSource();
    source.buffer = buf;
    source.loop = true;
    
    // Create gain node for volume control
    const gainNode = ctx.createGain();
    gainNode.gain.value = 0;
    source.connect(gainNode);
    
    source.start();
    
    // Fade in effect
    this.fadeIn(gainNode);
    
    return { node: gainNode, bufferNode: source };
  },
  
  createSinBuffer(ctx, freq) {
    const bufferLength = Math.ceil(ctx.sampleRate * 3 / freq) * freq;
    const buf = ctx.createBuffer(2, bufferLength, ctx.sampleRate);
    
    for (let channel = 0; channel < buf.numberOfChannels; channel++) {
      const arr = buf.getChannelData(channel);
      for (let i = 0; i < buf.length; i++) {
        arr[i] = Math.sin((i / ctx.sampleRate) * freq * Math.PI * 2) * 0.6;
      }
    }
    return buf;
  },
  
  fadeIn(gainNode) {
    let x = 0;
    const step = 1 / (2000 / 100); // 2s fade in
    const fadeInterval = lynx.setInterval(() => {
      if (x > 1) {
        gainNode.gain.value = 1;
        clearInterval(fadeInterval);
      } else {
        gainNode.gain.value = Math.pow(2, x * 10) / 1024;
        x += step;
      }
    }, 100);
  },
  
  playAudioSrc(src) {
    const audio = lynx.createAudio(src);
    this.newAudio = audio;
    const source = this.audioContext.createMediaElementSource(audio);
    
    audio.loop = true;
    audio.autoplay = true;
    
    // Audio event listeners
    audio.oncanplay = () => console.log('Audio can play');
    audio.onplaying = () => console.log('Audio playing');
    audio.onerror = (err) => console.error('Audio error:', err);
    
    return source;
  },
  
  switchAudioNode(node, bufferNode = null) {
    // Disconnect old node
    if (this.audioNode) {
      this.audioNode.disconnect(this.audioContext.destination);
      this.audioNode.disconnect(this.analyser);
    }
    
    // Stop old audio
    if (this.oldAudio) {
      this.oldAudio.stop();
      this.oldAudio = null;
    }
    this.oldAudio = this.newAudio;
    this.newAudio = null;
    
    // Set new nodes
    this.audioBufferNode = bufferNode;
    this.audioNode = node;
    
    // Connect new node
    if (node) {
      node.connect(this.audioContext.destination);
      node.connect(this.analyser);
    }
    
    // Start visualization
    this.frameFunction = this.startAnalyse();
  },
  
  startAnalyse() {
    const bufferLength = this.analyser.frequencyBinCount;
    const timeArray = new Uint8Array(bufferLength);
    const freqArray = new Uint8Array(bufferLength);
    
    return () => {
      this.analyser.getByteTimeDomainData(timeArray);
      this.analyser.getByteFrequencyData(freqArray);
      
      const context = this.canvas.getContext('2d');
      context.clearRect(0, 0, this.canvas.width, this.canvas.height);
      
      // Draw waveform (blue)
      this.drawWaveform(context, timeArray);
      
      // Draw frequency spectrum (green)
      this.drawSpectrum(context, freqArray);
      
      // Draw labels
      this.drawLabels(context);
    };
  },
  
  drawWaveform(context, timeArray) {
    context.strokeStyle = "blue";
    context.lineWidth = 2;
    context.beginPath();
    
    const sliceWidth = this.canvas.width / timeArray.length;
    let x = 0;
    
    for (let i = 0; i < timeArray.length; i++) {
      const v = timeArray[i] / 128.0;
      const y = v * this.canvas.height / 2;
      
      if (i === 0) {
        context.moveTo(x, y);
      } else {
        context.lineTo(x, y);
      }
      x += sliceWidth;
    }
    context.stroke();
  },
  
  drawSpectrum(context, freqArray) {
    context.strokeStyle = "green";
    context.lineWidth = 3;
    context.beginPath();
    
    const sliceWidth = this.canvas.width / freqArray.length;
    let x = 0;
    
    for (let i = 0; i < freqArray.length; i++) {
      const v = 2 - freqArray[i] / 128.0;
      const y = v * this.canvas.height / 2;
      
      if (i === 0) {
        context.moveTo(x, y);
      } else {
        context.lineTo(x, y);
      }
      x += sliceWidth;
    }
    context.stroke();
  },
  
  drawLabels(context) {
    context.font = '24rpx monospace';
    context.fillStyle = 'blue';
    context.fillText('波形', this.canvas.width - 100, this.canvas.height - 40);
    context.fillStyle = 'green';
    context.fillText('频谱', this.canvas.width - 100, this.canvas.height - 70);
  },
  
  onTouchCanvas(valX, valY) {
    if (this.data.testCase === 'sin') {
      this.updatePlaybackRate(valX ? 0.1 : -0.1);
    }
  },
  
  updatePlaybackRate(value) {
    const playbackRate = this.audioBufferNode?.playbackRate;
    if (playbackRate) {
      playbackRate.value += value;
      this.setData({
        playbackRate: `播放速度: ${playbackRate.value.toFixed(2)}x`
      });
    }
  },
  
  // Lifecycle management
  onViewDisappeared() {
    const pause = lynx.aurum().pause;
    if (pause) {
      pause();
      console.log('Audio paused');
    }
  },
  
  onViewAppeared() {
    const resume = lynx.aurum().resume;
    if (resume) {
      resume();
      console.log('Audio resumed');
    }
  },
  
  onUnload() {
    // Cleanup audio resources
    this.switchAudioNode(null);
    if (this.audioContext) {
      // Note: AudioContext.close() may not be available in all environments
      console.log('Audio context cleanup');
    }
  },

  // 正确的Canvas初始化方法
  setupCanvas() {
    console.log('Setting up canvas...');
    try {
      const canvas = lynx.createCanvasNG();

      // 重要：resize事件监听必须在绑定前设置
      canvas.addEventListener("resize", ({ width, height }) => {
        console.log('Canvas resize event:', width, height);
        canvas.width = width * SystemInfo.pixelRatio;
        canvas.height = height * SystemInfo.pixelRatio;
        const ctx = canvas.getContext('2d');
        ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
        this.canvas = canvas;
        this.ctx = ctx;
        this.canvasWidth = width;
        this.canvasHeight = height;
        console.log('Canvas setup complete, starting animation...');
        this.startAnimationLoop();
      });

      // 绑定到Canvas视图
      canvas.attachToCanvasView("audioCanvas");
    } catch (error) {
      console.error('Canvas setup failed:', error);
    }
  }
});
```

### 5. index.ttss
```css
.audio-container {
  padding: 24rpx;
  background: #f5f5f5;
}

.title {
  text-align: center;
  margin-bottom: 24rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin: 24rpx 0;
  justify-content: center;
}

.btn {
  padding: 16rpx 24rpx;
  background: #007aff;
  border-radius: 8rpx;
  min-width: 120rpx;
  text-align: center;
}

.btn text {
  color: white;
  font-size: 28rpx;
}

.btn.active {
  background: #ff3b30;
}

.info {
  text-align: center;
  margin: 16rpx 0;
}

.info-text {
  font-size: 24rpx;
  color: #666;
}

.engine-info {
  text-align: center;
  margin-top: 16rpx;
}

.engine-text {
  font-size: 20rpx;
  color: #999;
}
```

## 🎯 关键特性

1. **音频上下文管理**: 正确初始化和管理 `lynx.getAudioContext()`
2. **Canvas 集成**: 实时音频可视化
3. **多种音频源**: 支持正弦波、麦克风、音频文件
4. **生命周期管理**: 正确处理视图显示/隐藏
5. **交互控制**: 触摸控制播放速度
6. **内存管理**: 正确断开和清理音频节点

这个示例展示了如何使用新创建的 `LynxCanvasAudio.ts` prompt 中的所有关键概念和最佳实践。
