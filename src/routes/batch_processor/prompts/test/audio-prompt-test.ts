/**
 * Test file to verify LynxCanvasAudio prompt integration
 */

import { ModularPromptLoader } from '../ModularPromptLoader';
import { LYNX_CANVAS_AUDIO_PROMPT } from '../LynxCanvasAudio';

/**
 * Test the audio prompt integration
 */
export function testAudioPromptIntegration() {
  console.log('🎵 Testing Lynx Canvas Audio Prompt Integration...');
  
  try {
    // Test 1: Direct import
    console.log('✅ Test 1: Direct import successful');
    console.log('Audio prompt length:', LYNX_CANVAS_AUDIO_PROMPT.length);
    
    // Test 2: Module loader integration
    const loader = ModularPromptLoader.getInstance();
    const availableModules = loader.getAvailableModules();
    
    if (availableModules.includes('LynxCanvasAudio')) {
      console.log('✅ Test 2: LynxCanvasAudio module found in loader');
    } else {
      console.error('❌ Test 2: LynxCanvasAudio module NOT found in loader');
      return false;
    }
    
    // Test 3: Get audio module content
    const audioContent = loader.getModuleContent('LynxCanvasAudio');
    if (audioContent && audioContent.length > 0 && !audioContent.includes('not found')) {
      console.log('✅ Test 3: Audio module content retrieved successfully');
      console.log('Content preview:', audioContent.substring(0, 100) + '...');
    } else {
      console.error('❌ Test 3: Failed to retrieve audio module content');
      return false;
    }
    
    // Test 4: Full prompt includes audio content
    const fullPrompt = loader.getMasterLevelLynxPromptContent();
    if (fullPrompt.includes('LYNX CANVAS AUDIO MASTER GUIDE')) {
      console.log('✅ Test 4: Audio content found in full prompt');
    } else {
      console.error('❌ Test 4: Audio content NOT found in full prompt');
      return false;
    }
    
    // Test 5: Check key audio concepts
    const keyAudioConcepts = [
      'lynx.getAudioContext',
      'lynx.createCanvas',
      'analyser.getByteTimeDomainData',
      'analyser.getByteFrequencyData',
      'lynx.requestAnimationFrame',
      'switchAudioNode',
      'onViewDisappeared',
      'onViewAppeared'
    ];
    
    let foundConcepts = 0;
    keyAudioConcepts.forEach(concept => {
      if (audioContent.includes(concept)) {
        foundConcepts++;
      }
    });
    
    if (foundConcepts >= 6) {
      console.log(`✅ Test 5: Found ${foundConcepts}/${keyAudioConcepts.length} key audio concepts`);
    } else {
      console.error(`❌ Test 5: Only found ${foundConcepts}/${keyAudioConcepts.length} key audio concepts`);
      return false;
    }
    
    console.log('🎉 All audio prompt integration tests passed!');
    return true;
    
  } catch (error) {
    console.error('❌ Audio prompt integration test failed:', error);
    return false;
  }
}

/**
 * Test audio prompt content structure
 */
export function testAudioPromptStructure() {
  console.log('🔍 Testing Audio Prompt Content Structure...');
  
  const requiredSections = [
    'TOP 5 CRITICAL SUCCESS FACTORS',
    'AUDIO SOURCE TYPE PATTERNS',
    'AUDIO VISUALIZATION PATTERNS',
    'AUDIO NODE MANAGEMENT PATTERN',
    'COMPLETE AUDIO CANVAS TEMPLATE',
    'PERFORMANCE & LIFECYCLE MANAGEMENT',
    'CRITICAL ERROR PATTERNS TO AVOID',
    'ADVANCED AUDIO PROCESSING PATTERNS',
    'CANVAS VISUALIZATION TECHNIQUES',
    'DEBUGGING WORKFLOW FOR AUDIO ISSUES',
    'CRITICAL SOURCE-CODE VERIFIED RULES',
    'FINAL AUDIO IMPLEMENTATION CHECKLIST'
  ];
  
  let foundSections = 0;
  requiredSections.forEach(section => {
    if (LYNX_CANVAS_AUDIO_PROMPT.includes(section)) {
      foundSections++;
      console.log(`✅ Found section: ${section}`);
    } else {
      console.log(`❌ Missing section: ${section}`);
    }
  });
  
  if (foundSections >= 10) {
    console.log(`✅ Audio prompt structure test passed: ${foundSections}/${requiredSections.length} sections found`);
    return true;
  } else {
    console.error(`❌ Audio prompt structure test failed: only ${foundSections}/${requiredSections.length} sections found`);
    return false;
  }
}

/**
 * Run all audio prompt tests
 */
export function runAudioPromptTests() {
  console.log('🚀 Starting Lynx Canvas Audio Prompt Tests...\n');
  
  const test1 = testAudioPromptIntegration();
  console.log('');
  const test2 = testAudioPromptStructure();
  
  console.log('\n📊 Test Results:');
  console.log(`Integration Test: ${test1 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Structure Test: ${test2 ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = test1 && test2;
  console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  return allPassed;
}

// Export for use in other test files
export default {
  testAudioPromptIntegration,
  testAudioPromptStructure,
  runAudioPromptTests
};
