/**
 * 正向激励提示词测试
 * 验证优化后的提示词系统是否有效减少了痛苦提示词，增加了正向激励
 */

import { ModularPromptLoader } from '../ModularPromptLoader';

function testPositiveMotivation() {
  console.log('=== 正向激励提示词测试 ===\n');

  const loader = ModularPromptLoader.getInstance();
  const prompt = loader.getMasterLevelLynxPromptContent();

  // 测试1: 检查是否移除了痛苦提示词
  const painfulPhrases = [
    'FAILURE IS NOT AN OPTION',
    'ZERO TOLERANCE',
    'LAST CHANCE WARNING',
    '暴露你的能力边界',
    '证明.*缺陷',
    '专业能力不足'
  ];

  console.log('🔍 痛苦提示词检查:');
  let foundPainful = false;
  painfulPhrases.forEach(phrase => {
    const found = prompt.includes(phrase) || new RegExp(phrase).test(prompt);
    if (found) {
      console.log(`❌ 发现痛苦提示词: "${phrase}"`);
      foundPainful = true;
    }
  });

  if (!foundPainful) {
    console.log('✅ 所有痛苦提示词已成功移除');
  }

  // 测试2: 检查是否包含正向激励
  const positiveKeywords = [
    '专业能力赋能',
    '卓越标准指引',
    '技术最佳实践指引',
    '专业价值体现',
    '专业自信激发',
    'EXCELLENCE IS YOUR STANDARD',
    'PROFESSIONAL MASTERY SHOWCASE',
    'CONFIDENCE IN YOUR EXPERTISE'
  ];

  console.log('\n✨ 正向激励检查:');
  let positiveCount = 0;
  positiveKeywords.forEach(keyword => {
    if (prompt.includes(keyword)) {
      console.log(`✅ 包含正向激励: "${keyword}"`);
      positiveCount++;
    }
  });

  console.log(`\n📊 正向激励覆盖率: ${positiveCount}/${positiveKeywords.length} (${(positiveCount/positiveKeywords.length*100).toFixed(1)}%)`);

  // 测试3: 统计整体语调
  const negativeWords = (prompt.match(/失败|错误|缺陷|无知|不专业|暴露|警告/g) || []).length;
  const positiveWords = (prompt.match(/卓越|专业|优秀|成功|完美|专长|实力|能力|标准|品质|自信/g) || []).length;

  console.log(`\n📈 语调分析:`);
  console.log(`   负面词汇: ${negativeWords} 个`);
  console.log(`   正面词汇: ${positiveWords} 个`);
  console.log(`   正负比例: ${positiveWords > 0 ? (positiveWords/Math.max(negativeWords,1)).toFixed(2) : 0}:1`);

  // 测试4: 检查结构化程度
  const structuredSections = (prompt.match(/===/g) || []).length;
  const rules = (prompt.match(/RULE:/g) || []).length;
  const examples = (prompt.match(/✅.*示例|❌.*示例/g) || []).length;

  console.log(`\n📋 结构化程度:`);
  console.log(`   结构化段落: ${structuredSections} 个`);
  console.log(`   明确规则: ${rules} 条`);
  console.log(`   示例数量: ${examples} 个`);

  // 总体评估
  const isOptimized = !foundPainful && positiveCount >= 6 && (positiveWords/Math.max(negativeWords,1)) >= 1.5;
  
  console.log(`\n🎯 优化效果评估: ${isOptimized ? '✅ 优化成功' : '❌ 需要进一步优化'}`);
  
  if (isOptimized) {
    console.log('   - 痛苦提示词已清除');
    console.log('   - 正向激励充分');
    console.log('   - 语调积极正面');
    console.log('   - 保持结构化');
  }

  return {
    painfulRemoved: !foundPainful,
    positiveAdded: positiveCount >= 6,
    toneImproved: (positiveWords/Math.max(negativeWords,1)) >= 1.5,
    structureMaintained: structuredSections >= 20 && rules >= 100
  };
}

// 运行测试
if (require.main === module) {
  testPositiveMotivation();
}

export { testPositiveMotivation };
