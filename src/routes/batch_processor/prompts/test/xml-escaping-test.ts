/**
 * XML转义规则测试
 * 验证新的智能转义规则是否正确工作
 */

export const XML_ESCAPING_TEST_CASES = {
  // ✅ 应该转义的静态文本内容
  SHOULD_ESCAPE: [
    {
      input: '<text>• |a| > 1 更窄</text>',
      expected: '<text>• |a| &gt; 1 更窄</text>',
      description: '数学表达式中的大于号应该转义',
    },
    {
      input: '<text>• |a| < 1 更宽</text>',
      expected: '<text>• |a| &lt; 1 更宽</text>',
      description: '数学表达式中的小于号应该转义',
    },
    {
      input: '<text>• a < 0 开口向下</text>',
      expected: '<text>• a &lt; 0 开口向下</text>',
      description: '数学条件中的小于号应该转义',
    },
    {
      input: '<text>温度 > 30度时需要开空调</text>',
      expected: '<text>温度 &gt; 30度时需要开空调</text>',
      description: '静态文本中的大于号应该转义',
    },
    {
      input: '<text>价格 < 100元的商品</text>',
      expected: '<text>价格 &lt; 100元的商品</text>',
      description: '静态文本中的小于号应该转义',
    },
    {
      input: '<text>数学公式：a > b && b < c</text>',
      expected: '<text>数学公式：a &gt; b &amp;&amp; b &lt; c</text>',
      description: '静态文本中的比较运算符和逻辑运算符应该转义',
    },
  ],

  // ❌ 绝对不应该转义的动态模板表达式
  SHOULD_NOT_ESCAPE: [
    {
      input:
        "<text class=\"metric-change {{fedData.rateChange > 0 ? 'positive' : 'negative'}}\">",
      expected:
        "<text class=\"metric-change {{fedData.rateChange > 0 ? 'positive' : 'negative'}}\">",
      description: '动态类名中的比较运算符不应该转义',
    },
    {
      input: '<view tt:if="{{temperature > 30}}">',
      expected: '<view tt:if="{{temperature > 30}}">',
      description: '条件判断中的比较运算符不应该转义',
    },
    {
      input: "<text>{{price < 100 ? '便宜' : '昂贵'}}</text>",
      expected: "<text>{{price < 100 ? '便宜' : '昂贵'}}</text>",
      description: '模板表达式中的比较运算符不应该转义',
    },
    {
      input:
        '<view bindtap="handleClick" data-value="{{item.id > 0 ? item.id : 0}}">',
      expected:
        '<view bindtap="handleClick" data-value="{{item.id > 0 ? item.id : 0}}">',
      description: '属性值中的模板表达式不应该转义',
    },
    {
      input:
        "<text>{{fedData.rateChange > 0 ? '+' : ''}}{{fedData.rateChange}}%</text>",
      expected:
        "<text>{{fedData.rateChange > 0 ? '+' : ''}}{{fedData.rateChange}}%</text>",
      description: '复杂模板表达式中的比较运算符不应该转义',
    },
  ],

  // 🔥 问题案例：之前错误转义导致的语法错误
  PROBLEMATIC_CASES: [
    {
      wrong:
        "<text class=\"metric-change {{fedData.rateChange &gt; 0 ? 'positive' : 'negative'}}\">",
      correct:
        "<text class=\"metric-change {{fedData.rateChange > 0 ? 'positive' : 'negative'}}\">",
      issue: '错误转义导致模板表达式语法错误',
    },
    {
      wrong:
        "<text>{{fedData.rateChange &gt; 0 ? '+' : ''}}{{fedData.rateChange}}%</text>",
      correct:
        "<text>{{fedData.rateChange > 0 ? '+' : ''}}{{fedData.rateChange}}%</text>",
      issue: '模板表达式内部被错误转义',
    },
  ],
};

/**
 * 智能转义规则验证函数
 */
export function validateSmartEscaping(input: string): {
  isValid: boolean;
  issues: string[];
  suggestions: string[];
} {
  const issues: string[] = [];
  const suggestions: string[] = [];

  // 检查是否在模板表达式内错误转义
  const templateExpressions = input.match(/\{\{[^}]+\}\}/g) || [];
  templateExpressions.forEach(expr => {
    if (
      expr.includes('&gt;') ||
      expr.includes('&lt;') ||
      expr.includes('&amp;')
    ) {
      issues.push(`模板表达式 ${expr} 中包含转义字符，这会导致语法错误`);
      suggestions.push(`移除模板表达式内的转义字符，保持原始比较运算符`);
    }
  });

  // 检查属性值中的模板表达式是否被错误转义
  const attributeTemplates = input.match(/="[^"]*\{\{[^}]+\}\}[^"]*"/g) || [];
  attributeTemplates.forEach(attr => {
    if (
      attr.includes('&gt;') ||
      attr.includes('&lt;') ||
      attr.includes('&amp;')
    ) {
      issues.push(`属性值 ${attr} 中的模板表达式被错误转义`);
      suggestions.push(`属性值中的模板表达式不应该转义比较运算符`);
    }
  });

  // 检查静态文本是否需要转义
  const textContent =
    input.match(/<text[^>]*>([^<{]*(?:\{[^{][^}]*\}[^<{]*)*)</g) || [];
  textContent.forEach(text => {
    const content = text.replace(/<text[^>]*>/, '');
    if (
      (content.includes('>') ||
        content.includes('<') ||
        content.includes('&')) &&
      !content.includes('{{')
    ) {
      suggestions.push(`静态文本内容 "${content}" 中的特殊字符应该转义`);
    }
  });

  return {
    isValid: issues.length === 0,
    issues,
    suggestions,
  };
}

/**
 * 运行测试用例
 */
export function runXMLEscapingTests(): void {
  console.log('🧪 运行XML转义规则测试...\n');

  // 测试应该转义的情况
  console.log('✅ 测试静态文本转义：');
  XML_ESCAPING_TEST_CASES.SHOULD_ESCAPE.forEach((testCase, index) => {
    console.log(`  ${index + 1}. ${testCase.description}`);
    console.log(`     输入: ${testCase.input}`);
    console.log(`     期望: ${testCase.expected}\n`);
  });

  // 测试不应该转义的情况
  console.log('❌ 测试模板表达式保护：');
  XML_ESCAPING_TEST_CASES.SHOULD_NOT_ESCAPE.forEach((testCase, index) => {
    console.log(`  ${index + 1}. ${testCase.description}`);
    console.log(`     输入: ${testCase.input}`);
    console.log(`     期望: ${testCase.expected}\n`);
  });

  // 测试问题案例
  console.log('🔥 问题案例分析：');
  XML_ESCAPING_TEST_CASES.PROBLEMATIC_CASES.forEach((testCase, index) => {
    console.log(`  ${index + 1}. ${testCase.issue}`);
    console.log(`     错误: ${testCase.wrong}`);
    console.log(`     正确: ${testCase.correct}\n`);
  });
}

// 导出测试函数
export default {
  XML_ESCAPING_TEST_CASES,
  validateSmartEscaping,
  runXMLEscapingTests,
};
