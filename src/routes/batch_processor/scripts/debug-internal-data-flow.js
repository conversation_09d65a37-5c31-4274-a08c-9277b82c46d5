/**
 * 底稿数据流程调试脚本
 * 
 * 🎯 目标：验证底稿数据开关的完整流程
 * 🔍 检查点：
 * 1. QueryInputPanel 开关状态
 * 2. BatchProcessorService 配置同步
 * 3. DataProcessingService 调用
 * 4. InternalDataService 接口请求
 * 5. AI接口调用时机
 */

console.log('🔍 开始调试底稿数据流程...\n');

// 模拟配置对象
const mockConfig = {
  processing: {
    useInternalData: false, // 初始状态
  }
};

// 模拟批处理服务
class MockBatchProcessorService {
  constructor(config) {
    this.config = config;
  }

  setInternalDataMode(enabled) {
    console.log(`📝 [BatchProcessorService] setInternalDataMode(${enabled})`);
    this.config.processing.useInternalData = enabled;
    console.log(`   ✅ 配置已更新: useInternalData = ${this.config.processing.useInternalData}`);
  }

  getInternalDataMode() {
    return this.config.processing.useInternalData;
  }

  async processQuery(query) {
    console.log(`🚀 [BatchProcessorService] processQuery("${query}")`);
    console.log(`   📊 当前配置: useInternalData = ${this.config.processing.useInternalData}`);
    
    if (this.config.processing.useInternalData) {
      console.log('   🗂️ 启用底稿数据模式，开始处理查询');
      
      // 模拟调用 DataProcessingService.processQuery
      console.log('   📞 调用 DataProcessingService.processQuery(query, true)');
      
      // 模拟调用 InternalDataService.fetchInternalData
      console.log('   📞 调用 InternalDataService.fetchInternalData(query, true)');
      console.log('   🌐 发送请求到: http://9gzj7t9k.fn.bytedance.net/api/search/stream');
      
      // 模拟底稿数据响应
      const mockInternalData = {
        success: true,
        answer: '模拟底稿数据内容',
        pv: 1000,
        reference: '模拟参考资料'
      };
      
      console.log('   ✅ 底稿数据获取成功');
      console.log('   🎯 构建包含底稿数据的AI输入内容');
      console.log('   📞 调用 AI接口 (包含底稿数据)');
      
      return {
        success: true,
        dataSource: 'internal',
        content: '基于底稿数据生成的内容'
      };
    } else {
      console.log('   📝 使用直接AI生成模式');
      console.log('   📞 直接调用 AI接口 (原始查询)');
      
      return {
        success: true,
        dataSource: 'ai',
        content: 'AI直接生成的内容'
      };
    }
  }
}

// 模拟 QueryInputPanel 的 handleModeToggle
function simulateHandleModeToggle(enabled, batchProcessorService) {
  console.log(`🎛️ [QueryInputPanel] handleModeToggle(${enabled})`);
  console.log(`   📝 setUseInternalData(${enabled})`);
  
  // 🎯 关键修复：同时更新批处理服务的底稿数据模式
  if (batchProcessorService) {
    batchProcessorService.setInternalDataMode(enabled);
    console.log(`   ✅ 批处理服务底稿数据模式已${enabled ? '启用' : '关闭'}`);
  } else {
    console.warn('   ⚠️ 批处理服务未提供，无法设置底稿数据模式');
  }
  
  console.log(`   📊 数据源模式切换: ${enabled ? '内部数据' : '直接模式'}\n`);
}

// 执行调试流程
async function runDebugFlow() {
  console.log('═══════════════════════════════════════════════════════════════');
  console.log('🧪 底稿数据流程完整调试');
  console.log('═══════════════════════════════════════════════════════════════\n');

  // 1. 创建模拟服务
  const batchService = new MockBatchProcessorService(mockConfig);
  
  console.log('1️⃣ 初始状态检查:');
  console.log(`   useInternalData: ${batchService.getInternalDataMode()}`);
  console.log('   ✅ 预期: false (默认关闭)\n');

  // 2. 模拟用户开启开关
  console.log('2️⃣ 用户开启底稿数据开关:');
  simulateHandleModeToggle(true, batchService);

  // 3. 验证配置同步
  console.log('3️⃣ 配置同步验证:');
  console.log(`   batchService.getInternalDataMode(): ${batchService.getInternalDataMode()}`);
  console.log('   ✅ 预期: true (已启用)\n');

  // 4. 模拟查询处理
  console.log('4️⃣ 模拟查询处理:');
  const result = await batchService.processQuery('九九乘法表');
  console.log(`   处理结果: ${JSON.stringify(result, null, 2)}`);
  console.log('   ✅ 预期: 调用底稿数据接口\n');

  // 5. 模拟用户关闭开关
  console.log('5️⃣ 用户关闭底稿数据开关:');
  simulateHandleModeToggle(false, batchService);

  // 6. 验证关闭后的行为
  console.log('6️⃣ 关闭后查询处理:');
  const result2 = await batchService.processQuery('三角函数');
  console.log(`   处理结果: ${JSON.stringify(result2, null, 2)}`);
  console.log('   ✅ 预期: 直接调用AI接口\n');

  console.log('═══════════════════════════════════════════════════════════════');
  console.log('🎉 调试完成！');
  console.log('═══════════════════════════════════════════════════════════════\n');

  // 7. 问题诊断
  console.log('🔧 问题诊断清单:');
  console.log('   ✅ QueryInputPanel.handleModeToggle 正确调用 setInternalDataMode');
  console.log('   ✅ BatchProcessorService.setInternalDataMode 正确更新配置');
  console.log('   ✅ BatchProcessorService.processQuery 正确检查配置');
  console.log('   ✅ 底稿数据模式下调用 DataProcessingService.processQuery');
  console.log('   ✅ DataProcessingService 调用 InternalDataService.fetchInternalData');
  console.log('   ✅ InternalDataService 发送请求到底稿数据接口\n');

  console.log('🚨 如果仍然直接调用chat接口，请检查:');
  console.log('   1. 主页面是否正确传递 batchProcessorService 引用');
  console.log('   2. QueryInputPanel 是否正确接收并使用 batchProcessorService');
  console.log('   3. handleModeToggle 是否被正确调用');
  console.log('   4. 浏览器控制台是否有相关错误日志');
  console.log('   5. 网络面板是否显示底稿数据接口请求');
}

// 运行调试
runDebugFlow().catch(console.error);
