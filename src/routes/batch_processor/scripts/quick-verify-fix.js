/**
 * 底稿数据开关修复快速验证脚本
 * 
 * 🎯 使用方法：
 * 1. 在批处理页面打开开发者工具控制台
 * 2. 复制并粘贴此脚本运行
 * 3. 按照提示进行测试
 */

console.log('🔍 底稿数据开关修复快速验证');
console.log('═══════════════════════════════════════════════════════════════');

// 验证函数：检查修复是否生效
function verifyInternalDataFix() {
  console.log('\n🧪 开始验证底稿数据开关修复...');
  
  // 步骤1：检查页面状态
  console.log('\n1️⃣ 检查页面状态:');
  
  const toggleSwitch = document.querySelector('.semi-switch') || 
                      document.querySelector('input[type="checkbox"]') ||
                      document.querySelector('[role="switch"]');
  
  if (toggleSwitch) {
    console.log('   ✅ 找到底稿数据开关元素');
    console.log('   📊 当前开关状态:', toggleSwitch.checked ? '开启' : '关闭');
  } else {
    console.log('   ❌ 未找到底稿数据开关元素');
    console.log('   💡 请确保页面已完全加载');
  }
  
  // 步骤2：检查批处理服务
  console.log('\n2️⃣ 检查批处理服务:');
  
  // 尝试从React组件树获取服务实例
  let batchService = null;
  try {
    const root = document.querySelector('#root');
    if (root) {
      // 尝试不同的React内部属性
      const fiber = root._reactInternalFiber || root._reactInternals;
      if (fiber) {
        // 简化的服务查找逻辑
        console.log('   🔍 正在查找批处理服务实例...');
        // 这里需要根据实际的React组件结构来调整
        console.log('   ⚠️ 需要手动检查服务实例');
      }
    }
  } catch (e) {
    console.log('   ⚠️ 无法自动获取服务实例:', e.message);
  }
  
  // 步骤3：提供手动测试指导
  console.log('\n3️⃣ 手动测试指导:');
  console.log('\n   📋 请按以下步骤测试:');
  console.log('   1. 开启"使用抖音内部的底稿数据"开关');
  console.log('   2. 观察控制台，应该看到以下日志:');
  console.log('      "[QueryInputPanel] 🎛️ 底稿数据开关切换:"');
  console.log('      "✅ 批处理服务配置同步成功: true"');
  console.log('');
  console.log('   3. 输入查询内容，如"九九乘法表"');
  console.log('   4. 点击"开始处理"按钮');
  console.log('   5. 观察控制台，应该看到以下日志:');
  console.log('      "[useBatchProcessor] 🔄 检测到底稿数据模式被默认配置覆盖"');
  console.log('      "[useBatchProcessor] ✅ 用户设置恢复成功: true"');
  console.log('      "[EnhancedBatchProcessorService] 🗂️ ✅ 底稿数据模式已启用"');
  console.log('');
  console.log('   6. 检查网络面板，应该看到:');
  console.log('      "http://9gzj7t9k.fn.bytedance.net/api/search/stream" 请求');
  
  // 步骤4：提供调试命令
  console.log('\n4️⃣ 调试命令:');
  console.log('\n   🔧 运行以下命令检查详细状态:');
  console.log('');
  console.log('   // 检查开关状态');
  console.log('   const toggle = document.querySelector(".semi-switch");');
  console.log('   console.log("开关状态:", toggle?.checked);');
  console.log('');
  console.log('   // 模拟开关切换');
  console.log('   if (toggle) toggle.click();');
  
  // 步骤5：成功标志
  console.log('\n5️⃣ 修复成功的标志:');
  console.log('\n   ✅ 如果看到以下情况，说明修复成功:');
  console.log('   1. 开关切换时有详细的配置同步日志');
  console.log('   2. 开始处理时有配置保护日志');
  console.log('   3. 查询处理时显示"底稿数据模式已启用"');
  console.log('   4. 网络面板显示底稿数据接口请求');
  console.log('   5. 结果显示"内部数据"标签（绿色）');
  console.log('');
  console.log('   ❌ 如果仍有问题，会看到:');
  console.log('   1. 查询处理时显示"底稿数据模式已关闭"');
  console.log('   2. 网络面板只有AI接口请求');
  console.log('   3. 结果显示"AI生成"标签（蓝色）');
}

// 创建测试按钮（可选）
function createTestButton() {
  const existingButton = document.getElementById('verify-fix-button');
  if (existingButton) {
    existingButton.remove();
  }
  
  const button = document.createElement('button');
  button.id = 'verify-fix-button';
  button.innerHTML = '🧪 验证底稿数据修复';
  button.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 10000;
    padding: 10px 15px;
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  `;
  
  button.onclick = () => {
    console.clear();
    verifyInternalDataFix();
  };
  
  document.body.appendChild(button);
  console.log('\n💡 已在页面右上角添加"验证底稿数据修复"按钮');
}

// 执行验证
verifyInternalDataFix();
createTestButton();

console.log('\n═══════════════════════════════════════════════════════════════');
console.log('🎉 验证脚本加载完成！');
console.log('');
console.log('📋 下一步操作:');
console.log('1. 按照上述指导进行手动测试');
console.log('2. 观察控制台日志输出');
console.log('3. 检查网络面板的请求');
console.log('4. 点击右上角的验证按钮重新运行检查');
console.log('');
console.log('🚀 如果看到底稿数据接口请求，说明修复成功！');
