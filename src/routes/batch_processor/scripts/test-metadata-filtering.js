/**
 * 底稿数据元数据过滤测试脚本
 * 
 * 🎯 目标：验证底稿数据中的元数据是否被正确过滤
 * 🚫 过滤内容：阅读量、浏览量、日期、地点、统计数据等与主题无关的信息
 * ✅ 保留内容：与主题直接相关的核心知识内容
 */

console.log('🧪 开始测试底稿数据元数据过滤功能...');
console.log('═══════════════════════════════════════════════════════════════');

// 模拟底稿数据样本
const mockInternalData = {
  answer: `九九乘法表是数学基础工具，用于快速计算乘法运算。

基本结构：
1×1=1, 1×2=2, 1×3=3...
2×1=2, 2×2=4, 2×3=6...

阅读量：12345次
浏览数：98765次
pv：54321

发布于北京市朝阳区
更新时间：2024-01-15 10:30:00
作者：数学教研组
编辑：张老师

统计数据：本内容被查看了1万次
数据来源：教育部数学教学大纲

九九乘法表的记忆方法：
1. 顺序记忆法
2. 规律记忆法
3. 口诀记忆法`,

  reference: `参考资料：
1. 《小学数学教学大纲》- 教育部出版
   更新时间：2023-12-01 09:00:00
   热度：8888
   评分：4.8
   ID：123456

2. 数学教育网 - https://math.edu.cn/multiplication?utm_source=search&from=baidu
   发布时间：2023-11-20
   版本：v2.1

3. 《数学基础知识手册》
   阅读量：50000次
   浏览量：120000次`,

  pv: 12345,
  reasoning: '基于教学大纲和实践经验，九九乘法表是数学基础。统计显示90%的学生通过背诵掌握。'
};

// 模拟过滤函数
function filterCoreContent(content) {
  if (!content || typeof content !== 'string') {
    return '';
  }

  let filtered = content
    // 移除阅读量/浏览量信息
    .replace(/\d+\s*[万千百十]?\s*[次个人]?\s*[阅读浏览观看点击]/gi, '')
    .replace(/[阅读浏览观看点击]\s*[量数]\s*[:：]\s*\d+/gi, '')
    .replace(/pv\s*[:：]\s*\d+/gi, '')
    
    // 移除日期时间信息
    .replace(/\d{4}[-年]\d{1,2}[-月]\d{1,2}[日号]?/g, '')
    .replace(/\d{1,2}[-月]\d{1,2}[日号]/g, '')
    .replace(/\d{4}年/g, '')
    .replace(/[昨今明前后]天|[上下]周|[上下]月/g, '')
    
    // 移除地点信息
    .replace(/(?:发布|更新|编辑|创建)(?:于|在|时间)[:：]?\s*[^\n\r。，,；;！!？?]*[市区县镇村]/g, '')
    
    // 移除统计数据
    .replace(/统计数据[:：][^\n\r。，,；;！!？?]*/g, '')
    .replace(/数据来源[:：][^\n\r。，,；;！!？?]*/g, '')
    
    // 移除作者/编辑信息
    .replace(/(?:作者|编辑|记者|来源)[:：][^\n\r。，,；;！!？?]*/g, '')
    
    // 清理多余的空白字符
    .replace(/\n\s*\n/g, '\n')
    .replace(/\s+/g, ' ')
    .trim();

  return filtered;
}

function filterReferenceContent(reference) {
  if (!reference || typeof reference !== 'string') {
    return '';
  }

  let filtered = reference
    // 移除统计信息
    .replace(/\d+\s*[万千百十]?\s*[次个人]?\s*[阅读浏览观看点击]/gi, '')
    .replace(/热度[:：]\s*\d+/gi, '')
    .replace(/评分[:：]\s*[\d.]+/gi, '')
    
    // 移除时间戳和日期
    .replace(/\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}/g, '')
    .replace(/更新时间[:：][^\n\r。，,；;！!？?]*/g, '')
    .replace(/发布时间[:：][^\n\r。，,；;！!？?]*/g, '')
    
    // 移除技术元数据
    .replace(/ID[:：]\s*\d+/gi, '')
    .replace(/版本[:：][^\n\r。，,；;！!？?]*/g, '')
    
    // 保留有用的链接，但移除追踪参数
    .replace(/(\?|&)[utm_source|utm_medium|utm_campaign|from|spm][^&\s]*/g, '')
    
    // 清理格式
    .replace(/\n\s*\n/g, '\n')
    .replace(/\s+/g, ' ')
    .trim();

  return filtered;
}

// 执行过滤测试
console.log('\n1️⃣ 原始底稿数据:');
console.log('📋 Answer字段:');
console.log(mockInternalData.answer);
console.log('\n📋 Reference字段:');
console.log(mockInternalData.reference);
console.log('\n📋 元数据:');
console.log(`PV: ${mockInternalData.pv}`);
console.log(`Reasoning: ${mockInternalData.reasoning}`);

console.log('\n2️⃣ 过滤后的纯净内容:');

// 过滤核心内容
const filteredAnswer = filterCoreContent(mockInternalData.answer);
console.log('🧹 过滤后的Answer:');
console.log(filteredAnswer);

// 过滤参考资料
const filteredReference = filterReferenceContent(mockInternalData.reference);
console.log('\n🧹 过滤后的Reference:');
console.log(filteredReference);

console.log('\n3️⃣ 过滤效果统计:');
console.log(`📊 Answer: ${mockInternalData.answer.length} → ${filteredAnswer.length} 字符 (减少 ${mockInternalData.answer.length - filteredAnswer.length} 字符)`);
console.log(`📊 Reference: ${mockInternalData.reference.length} → ${filteredReference.length} 字符 (减少 ${mockInternalData.reference.length - filteredReference.length} 字符)`);

console.log('\n4️⃣ 构建最终AI输入内容:');
const cleanContent = `用户查询：九九乘法表

请基于以下经过过滤的核心内容生成UI界面：

【核心内容】
${filteredAnswer}

${filteredReference ? `【相关参考】\n${filteredReference}` : ''}

请严格基于以上核心内容生成相应的UI界面，专注于主题相关信息，不要添加任何统计数据或元数据。`;

console.log(cleanContent);

console.log('\n5️⃣ 验证结果:');

// 检查是否还有元数据残留
const hasMetadata = {
  阅读量: /\d+\s*[万千百十]?\s*[次个人]?\s*[阅读浏览观看点击]/gi.test(cleanContent),
  PV数据: /pv\s*[:：]\s*\d+/gi.test(cleanContent),
  日期信息: /\d{4}[-年]\d{1,2}[-月]\d{1,2}[日号]?/.test(cleanContent),
  地点信息: /[市区县镇村]/.test(cleanContent),
  作者信息: /(?:作者|编辑|记者)[:：]/.test(cleanContent),
  统计数据: /统计数据[:：]/.test(cleanContent),
  热度评分: /(?:热度|评分)[:：]/.test(cleanContent)
};

console.log('🔍 元数据残留检查:');
Object.entries(hasMetadata).forEach(([key, hasResidue]) => {
  console.log(`   ${key}: ${hasResidue ? '❌ 仍有残留' : '✅ 已清理'}`);
});

const allClean = !Object.values(hasMetadata).some(Boolean);
console.log(`\n🎯 总体过滤效果: ${allClean ? '✅ 完全清理' : '❌ 仍有残留'}`);

console.log('\n6️⃣ 保留内容验证:');
const hasCore = {
  核心概念: /九九乘法表/.test(cleanContent),
  基本结构: /基本结构/.test(cleanContent),
  记忆方法: /记忆方法/.test(cleanContent),
  参考资料: /教学大纲|数学教育/.test(cleanContent)
};

console.log('📋 核心内容保留检查:');
Object.entries(hasCore).forEach(([key, isPreserved]) => {
  console.log(`   ${key}: ${isPreserved ? '✅ 已保留' : '❌ 被误删'}`);
});

const corePreserved = Object.values(hasCore).every(Boolean);
console.log(`\n🎯 核心内容保留: ${corePreserved ? '✅ 完整保留' : '❌ 有缺失'}`);

console.log('\n═══════════════════════════════════════════════════════════════');
console.log('🎉 元数据过滤测试完成！');
console.log('');
console.log('📋 测试结果总结:');
console.log(`   🧹 元数据清理: ${allClean ? '✅ 成功' : '❌ 失败'}`);
console.log(`   📚 核心内容: ${corePreserved ? '✅ 完整' : '❌ 缺失'}`);
console.log(`   📊 内容压缩: ${((1 - cleanContent.length / (mockInternalData.answer + mockInternalData.reference).length) * 100).toFixed(1)}%`);
console.log('');

if (allClean && corePreserved) {
  console.log('🚀 过滤功能正常！底稿数据中的元数据已被完全过滤，只保留主题相关的核心内容。');
} else {
  console.log('⚠️ 过滤功能需要调整！请检查过滤规则。');
}

console.log('\n💡 使用建议:');
console.log('1. 在实际使用中，观察控制台日志确认过滤效果');
console.log('2. 检查AI生成的内容是否包含统计数据或时间信息');
console.log('3. 如发现元数据泄露，请及时反馈进行规则优化');
