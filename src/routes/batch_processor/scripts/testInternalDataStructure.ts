/**
 * 测试抖音底稿数据结构处理脚本
 * 
 * 用于验证新的数据结构是否能正确处理
 */

import { DataProcessingService } from '../services/DataProcessingService';
import { InternalDataService } from '../services/InternalDataService';

// 模拟的抖音底稿数据结构
const mockInternalData = {
  answer: "<mark>九九乘法表是数学乘法运算的基础工具，用于快速计算1到9之间的两个数相乘的结果🔶2🔷。</mark>以下是相关信息：\n\n- **起源**：最早可追溯至先秦时期，《管子》《荀子》等文献中均有记载，最初是从"九九八十一"开始，到"二二得四"结束，与现代的顺序相反🔶5🔷。\n- **口诀**：从"一一得一"开始，到"九九八十一"为止，涵盖了1到9所有数字的乘法组合，共有45句口诀🔶2🔷。\n- **记忆方法**：可按行或列记忆，也可通过儿歌等方式帮助记忆🔶2🔷。",
  pv: 4351,
  reasoning: "",
  reference: "今天日期：2025年7月28日19时5分，当前位置：北京海淀\n请结合以下内容给出一个全面、清晰、有帮助的回答。问题：九九乘法表\n[参考内容]\n🔶1🔷 九九乘法表(彩版)(可打印)\n本文标题：九九乘法表(彩版)(可打印)（知识君）\n本文链接：http://m.toutiao.com/group/7232968067922559488/\n发布日期：2023年5月14日17时30分\n\n🔶2🔷 九九乘法口诀表一一得一九九乘法口诀表一三得三九九乘法口诀表一西得四一五得五九九乘法口诀表一七得七九九乘法口诀表一八得八九九乘法口诀表一九得九二二得四二二四得八二五一十九九乘法口诀表二六十二九九乘法口诀表二八十六九九乘法口诀表二九十八九九乘法口诀表三四十二九九乘法口诀表三六十八三七二十一三九二十七四四十六九九乘法口诀表四七二十八九九乘法口诀表四八三十二九九乘法口诀表四九三十六九九乘法口诀表五七三十五五八四十九九乘法口诀表五九四十五九九乘法口诀表六七四十二九九乘法口诀表六六九五十四九九乘法口诀表七九六十三1×5=52x5=103x5=154x5=20八八六十四1x4=42x4-83x4=124x4=161×5=52x5=103x5=154x5=20九九乘法口诀表\n本文标题：无限循环这首乘法口诀儿歌，让孩子轻松背熟#九九乘法口诀表，收藏起来磨耳朵吧#家长收藏孩子受益#二年级#乘法口诀#二年级数学（抖音视频）\n本文链接：无\n发布日期：2025年6月10日17时36分\n\n[参考内容结束]"
};

/**
 * 测试数据处理功能
 */
async function testDataProcessing() {
  console.log('🧪 开始测试抖音底稿数据结构处理...\n');

  try {
    // 1. 测试摘要提取
    console.log('1️⃣ 测试摘要提取:');
    const summary = (InternalDataService as any).extractSummary(mockInternalData);
    console.log('摘要结果:', summary);
    console.log('摘要长度:', summary.length);
    console.log('');

    // 2. 测试数据格式化显示
    console.log('2️⃣ 测试数据格式化显示:');
    const formattedData = DataProcessingService.formatInternalDataForDisplay(mockInternalData);
    console.log('格式化结果:');
    console.log(formattedData);
    console.log('');

    // 3. 测试查询处理（模拟模式）
    console.log('3️⃣ 测试查询处理:');
    
    // 模拟InternalDataService.fetchInternalData方法
    const originalFetch = global.fetch;
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockInternalData),
    });

    const query = '九九乘法表';
    const result = await DataProcessingService.processQuery(query, true);
    
    console.log('查询处理结果:');
    console.log('- 原始查询:', result.originalQuery);
    console.log('- 数据源:', result.source);
    console.log('- 处理后查询长度:', result.processedQuery.length);
    console.log('- 是否有上下文:', !!result.context);
    console.log('- 摘要:', result.internalDataSummary);
    console.log('');

    // 4. 测试处理后的prompt结构
    console.log('4️⃣ 测试处理后的prompt结构:');
    if (result.source === 'internal') {
      console.log('Prompt包含的关键元素:');
      console.log('- 包含"严格使用":', result.processedQuery.includes('严格使用'));
      console.log('- 包含"底稿数据":', result.processedQuery.includes('底稿数据'));
      console.log('- 包含核心答案:', result.processedQuery.includes(mockInternalData.answer.substring(0, 50)));
      console.log('- 包含数据热度:', result.processedQuery.includes(mockInternalData.pv.toString()));
      console.log('- 包含参考资料:', result.processedQuery.includes('参考资料'));
    }
    console.log('');

    // 恢复原始fetch
    global.fetch = originalFetch;

    console.log('✅ 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

/**
 * 测试不同数据结构的兼容性
 */
function testDataStructureCompatibility() {
  console.log('🔧 测试数据结构兼容性...\n');

  // 测试用例
  const testCases = [
    {
      name: '完整的抖音底稿数据',
      data: mockInternalData,
    },
    {
      name: '缺少reasoning字段',
      data: { ...mockInternalData, reasoning: undefined },
    },
    {
      name: '缺少pv字段',
      data: { ...mockInternalData, pv: undefined },
    },
    {
      name: '只有answer字段',
      data: { answer: mockInternalData.answer },
    },
    {
      name: '空数据',
      data: {},
    },
    {
      name: 'null数据',
      data: null,
    },
  ];

  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}️⃣ 测试: ${testCase.name}`);
    
    try {
      // 测试摘要提取
      const summary = (InternalDataService as any).extractSummary(testCase.data);
      console.log('  摘要提取:', summary.substring(0, 50) + (summary.length > 50 ? '...' : ''));
      
      // 测试格式化显示
      const formatted = DataProcessingService.formatInternalDataForDisplay(testCase.data);
      console.log('  格式化长度:', formatted.length);
      
      // 测试数据验证
      const isValid = (DataProcessingService as any).validateInternalData(testCase.data);
      console.log('  数据有效性:', isValid ? '✅' : '❌');
      
    } catch (error) {
      console.log('  处理错误:', error.message);
    }
    
    console.log('');
  });

  console.log('✅ 兼容性测试完成！');
}

/**
 * 性能测试
 */
function testPerformance() {
  console.log('⚡ 性能测试...\n');

  const iterations = 1000;
  
  // 测试摘要提取性能
  console.log('1️⃣ 摘要提取性能测试:');
  const summaryStart = Date.now();
  for (let i = 0; i < iterations; i++) {
    (InternalDataService as any).extractSummary(mockInternalData);
  }
  const summaryDuration = Date.now() - summaryStart;
  console.log(`  ${iterations} 次调用耗时: ${summaryDuration}ms`);
  console.log(`  平均每次: ${(summaryDuration / iterations).toFixed(3)}ms`);
  console.log('');

  // 测试格式化性能
  console.log('2️⃣ 格式化显示性能测试:');
  const formatStart = Date.now();
  for (let i = 0; i < iterations; i++) {
    DataProcessingService.formatInternalDataForDisplay(mockInternalData);
  }
  const formatDuration = Date.now() - formatStart;
  console.log(`  ${iterations} 次调用耗时: ${formatDuration}ms`);
  console.log(`  平均每次: ${(formatDuration / iterations).toFixed(3)}ms`);
  console.log('');

  console.log('✅ 性能测试完成！');
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 抖音底稿数据结构处理测试\n');
  console.log('=' .repeat(50));
  console.log('');

  await testDataProcessing();
  console.log('=' .repeat(50));
  console.log('');

  testDataStructureCompatibility();
  console.log('=' .repeat(50));
  console.log('');

  testPerformance();
  console.log('=' .repeat(50));
  console.log('');

  console.log('🎉 所有测试完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(console.error);
}

export { runTests, testDataProcessing, testDataStructureCompatibility, testPerformance };
