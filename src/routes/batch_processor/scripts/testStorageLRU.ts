/**
 * localStorage LRU清理功能测试脚本
 * 
 * 用于测试和验证localStorage空间不足时的自动清理功能
 */

import { LocalStorageService } from '../services/LocalStorageService';
import { PromptHistoryManager } from '../utils/promptTemplateManager';

/**
 * 测试LocalStorageService的LRU清理功能
 */
export function testLocalStorageLRU() {
  console.log('🧪 开始测试LocalStorageService LRU清理功能');
  
  // 1. 获取当前存储使用情况
  const initialUsage = LocalStorageService.getStorageInfo();
  console.log('📊 初始存储使用情况:', {
    used: `${(initialUsage.used / 1024 / 1024).toFixed(2)}MB`,
    total: `${(initialUsage.total / 1024 / 1024).toFixed(2)}MB`,
    available: `${(initialUsage.available / 1024 / 1024).toFixed(2)}MB`,
    usagePercent: `${initialUsage.usagePercent.toFixed(2)}%`
  });
  
  // 2. 创建大量测试数据
  console.log('📝 创建测试数据...');
  
  // 创建大量历史记录
  const testHistory = [];
  for (let i = 0; i < 100; i++) {
    testHistory.push({
      id: `test_${i}`,
      query: `测试查询 ${i} - ${'x'.repeat(1000)}`, // 每个查询约1KB
      playgroundUrl: `https://example.com/test_${i}`,
      status: i % 2 === 0 ? 'success' : 'error',
      startTime: Date.now() - (i * 60000), // 每分钟一个
      endTime: Date.now() - (i * 60000) + 30000,
      duration: 30000,
      error: i % 2 === 1 ? `测试错误 ${i}` : undefined
    });
  }
  
  // 保存测试历史记录
  const saveSuccess = LocalStorageService.saveHistory(testHistory);
  console.log('💾 测试历史记录保存结果:', saveSuccess);
  
  // 3. 测试系统提示词保存（大数据）
  const largePrompt = 'x'.repeat(500000); // 500KB的提示词
  console.log('📝 尝试保存大型系统提示词 (500KB)...');
  const promptSaveSuccess = LocalStorageService.saveSystemPrompt(largePrompt);
  console.log('💾 大型提示词保存结果:', promptSaveSuccess);
  
  // 4. 获取清理后的存储使用情况
  const finalUsage = LocalStorageService.getStorageInfo();
  console.log('📊 最终存储使用情况:', {
    used: `${(finalUsage.used / 1024 / 1024).toFixed(2)}MB`,
    total: `${(finalUsage.total / 1024 / 1024).toFixed(2)}MB`,
    available: `${(finalUsage.available / 1024 / 1024).toFixed(2)}MB`,
    usagePercent: `${finalUsage.usagePercent.toFixed(2)}%`
  });
  
  // 5. 测试手动清理
  console.log('🧹 测试手动清理功能...');
  const manualCleanupSuccess = LocalStorageService.manualCleanup(1); // 清理1MB
  console.log('🧹 手动清理结果:', manualCleanupSuccess);
  
  // 6. 验证历史记录是否被正确保留
  const remainingHistory = LocalStorageService.loadHistory();
  console.log('📋 剩余历史记录数量:', remainingHistory.length);
  
  console.log('✅ LocalStorageService LRU测试完成');
}

/**
 * 测试PromptHistoryManager的LRU清理功能
 */
export function testPromptHistoryLRU() {
  console.log('🧪 开始测试PromptHistoryManager LRU清理功能');
  
  // 1. 清空现有历史记录
  PromptHistoryManager.clearHistory();
  
  // 2. 添加大量测试提示词
  console.log('📝 添加测试提示词...');
  for (let i = 0; i < 60; i++) {
    const prompt = `测试提示词 ${i}\n${'内容'.repeat(1000)}`; // 每个约3KB
    PromptHistoryManager.addToHistory(prompt);
  }
  
  // 3. 检查历史记录数量
  const history = PromptHistoryManager.getHistory();
  console.log('📋 历史记录数量:', history.length);
  console.log('📋 最大允许数量:', 50);
  
  // 4. 尝试添加一个超大的提示词
  const hugePrompt = '超大提示词\n' + 'x'.repeat(1000000); // 1MB的提示词
  console.log('📝 尝试添加超大提示词 (1MB)...');
  PromptHistoryManager.addToHistory(hugePrompt);
  
  // 5. 检查最终结果
  const finalHistory = PromptHistoryManager.getHistory();
  console.log('📋 最终历史记录数量:', finalHistory.length);
  
  console.log('✅ PromptHistoryManager LRU测试完成');
}

/**
 * 模拟存储空间不足的情况
 */
export function simulateQuotaExceeded() {
  console.log('🧪 模拟存储空间不足情况');
  
  try {
    // 尝试填满localStorage
    let counter = 0;
    while (true) {
      const key = `test_fill_${counter}`;
      const value = 'x'.repeat(100000); // 100KB数据块
      
      try {
        localStorage.setItem(key, value);
        counter++;
        
        if (counter % 10 === 0) {
          const usage = LocalStorageService.getStorageInfo();
          console.log(`📊 已添加${counter}个数据块，使用率: ${usage.usagePercent.toFixed(2)}%`);
        }
        
        // 安全退出条件
        if (counter > 100) {
          console.log('⚠️ 达到安全限制，停止填充');
          break;
        }
      } catch (error) {
        console.log('💥 触发存储空间不足错误:', error.name);
        break;
      }
    }
    
    // 现在测试LRU清理是否工作
    console.log('🧹 测试LRU清理是否能处理空间不足...');
    const testData = 'x'.repeat(50000); // 50KB测试数据
    const success = LocalStorageService.saveSystemPrompt(testData);
    console.log('💾 LRU清理后保存结果:', success);
    
  } finally {
    // 清理测试数据
    console.log('🧹 清理测试数据...');
    for (let i = 0; i < 200; i++) {
      localStorage.removeItem(`test_fill_${i}`);
    }
  }
  
  console.log('✅ 存储空间不足模拟测试完成');
}

/**
 * 运行所有测试
 */
export function runAllStorageTests() {
  console.log('🚀 开始运行所有localStorage LRU测试');
  console.log('='.repeat(50));
  
  try {
    testLocalStorageLRU();
    console.log('='.repeat(50));
    
    testPromptHistoryLRU();
    console.log('='.repeat(50));
    
    simulateQuotaExceeded();
    console.log('='.repeat(50));
    
    console.log('🎉 所有测试完成！');
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

// 如果直接运行此脚本
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数暴露到全局
  (window as any).storageTests = {
    testLocalStorageLRU,
    testPromptHistoryLRU,
    simulateQuotaExceeded,
    runAllStorageTests,
    getStorageInfo: () => LocalStorageService.getStorageInfo(),
    manualCleanup: (sizeMB: number) => LocalStorageService.manualCleanup(sizeMB)
  };
  
  console.log('🛠️ 存储测试工具已加载到 window.storageTests');
  console.log('📖 使用方法:');
  console.log('  - window.storageTests.runAllStorageTests() - 运行所有测试');
  console.log('  - window.storageTests.getStorageInfo() - 查看存储使用情况');
  console.log('  - window.storageTests.manualCleanup(1) - 手动清理1MB空间');
}

export default {
  testLocalStorageLRU,
  testPromptHistoryLRU,
  simulateQuotaExceeded,
  runAllStorageTests
};
