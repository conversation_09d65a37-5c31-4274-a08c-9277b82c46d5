/**
 * 迭代检查器 - 执行各种代码质量检查
 */

import { CheckResult, Issue } from './MultiRoundIterationService';
import { IterationPromptBuilder } from './IterationPromptBuilder';

export class IterationChecker {
  
  /**
   * 执行结构检查
   */
  static async performStructureCheck(code: string): Promise<CheckResult> {
    console.log('🏗️ [StructureChecker] 开始结构检查...');
    
    const issues: Issue[] = [];
    let score = 100;

    try {
      // 1. 检查文件完整性
      const fileChecks = this.checkFileCompleteness(code);
      issues.push(...fileChecks.issues);
      score -= fileChecks.penalty;

      // 2. 检查TTML结构
      const ttmlChecks = this.checkTTMLStructure(code);
      issues.push(...ttmlChecks.issues);
      score -= ttmlChecks.penalty;

      // 3. 检查组件使用
      const componentChecks = this.checkComponentUsage(code);
      issues.push(...componentChecks.issues);
      score -= componentChecks.penalty;

      // 4. 使用AI进行深度结构分析
      const aiCheckResult = await this.performAIStructureCheck(code);
      if (aiCheckResult) {
        issues.push(...aiCheckResult.issues);
        score = Math.min(score, aiCheckResult.score);
      }

      return {
        passed: score >= 70 && !issues.some(i => i.type === 'critical'),
        score: Math.max(0, score),
        issues,
        suggestions: this.generateStructureSuggestions(issues),
        confidence: 0.9
      };

    } catch (error) {
      console.error('❌ [StructureChecker] 检查失败:', error);
      return {
        passed: false,
        score: 0,
        issues: [{
          type: 'critical',
          category: 'structure',
          message: `结构检查失败: ${error instanceof Error ? error.message : String(error)}`,
          fixSuggestion: '请检查代码格式是否正确'
        }],
        suggestions: [],
        confidence: 0.1
      };
    }
  }

  /**
   * 执行语法检查
   */
  static async performSyntaxCheck(code: string): Promise<CheckResult> {
    console.log('📝 [SyntaxChecker] 开始语法检查...');
    
    const issues: Issue[] = [];
    let score = 100;

    try {
      // 1. TTML语法检查
      const ttmlSyntax = this.checkTTMLSyntax(code);
      issues.push(...ttmlSyntax.issues);
      score -= ttmlSyntax.penalty;

      // 2. TTSS语法检查
      const ttssSyntax = this.checkTTSSSyntax(code);
      issues.push(...ttssSyntax.issues);
      score -= ttssSyntax.penalty;

      // 3. JavaScript语法检查
      const jsSyntax = this.checkJavaScriptSyntax(code);
      issues.push(...jsSyntax.issues);
      score -= jsSyntax.penalty;

      // 4. JSON语法检查
      const jsonSyntax = this.checkJSONSyntax(code);
      issues.push(...jsonSyntax.issues);
      score -= jsonSyntax.penalty;

      // 5. 使用AI进行深度语法分析
      const aiCheckResult = await this.performAISyntaxCheck(code);
      if (aiCheckResult) {
        issues.push(...aiCheckResult.issues);
        score = Math.min(score, aiCheckResult.score);
      }

      return {
        passed: score >= 75 && !issues.some(i => i.type === 'critical'),
        score: Math.max(0, score),
        issues,
        suggestions: this.generateSyntaxSuggestions(issues),
        confidence: 0.95
      };

    } catch (error) {
      console.error('❌ [SyntaxChecker] 检查失败:', error);
      return {
        passed: false,
        score: 0,
        issues: [{
          type: 'critical',
          category: 'syntax',
          message: `语法检查失败: ${error instanceof Error ? error.message : String(error)}`,
          fixSuggestion: '请检查代码语法是否正确'
        }],
        suggestions: [],
        confidence: 0.1
      };
    }
  }

  /**
   * 执行逻辑检查
   */
  static async performLogicCheck(code: string, originalQuery: string): Promise<CheckResult> {
    console.log('🧠 [LogicChecker] 开始逻辑检查...');
    
    const issues: Issue[] = [];
    let score = 100;

    try {
      // 1. 需求实现检查
      const requirementCheck = this.checkRequirementImplementation(code, originalQuery);
      issues.push(...requirementCheck.issues);
      score -= requirementCheck.penalty;

      // 2. 数据流检查
      const dataFlowCheck = this.checkDataFlow(code);
      issues.push(...dataFlowCheck.issues);
      score -= dataFlowCheck.penalty;

      // 3. 交互逻辑检查
      const interactionCheck = this.checkInteractionLogic(code);
      issues.push(...interactionCheck.issues);
      score -= interactionCheck.penalty;

      // 4. 使用AI进行深度逻辑分析
      const aiCheckResult = await this.performAILogicCheck(code, originalQuery);
      if (aiCheckResult) {
        issues.push(...aiCheckResult.issues);
        score = Math.min(score, aiCheckResult.score);
      }

      return {
        passed: score >= 70 && !issues.some(i => i.type === 'critical'),
        score: Math.max(0, score),
        issues,
        suggestions: this.generateLogicSuggestions(issues),
        confidence: 0.85
      };

    } catch (error) {
      console.error('❌ [LogicChecker] 检查失败:', error);
      return {
        passed: false,
        score: 0,
        issues: [{
          type: 'critical',
          category: 'logic',
          message: `逻辑检查失败: ${error instanceof Error ? error.message : String(error)}`,
          fixSuggestion: '请检查业务逻辑是否正确'
        }],
        suggestions: [],
        confidence: 0.1
      };
    }
  }

  /**
   * 执行最终质量检查
   */
  static async performFinalQualityCheck(code: string, originalQuery: string): Promise<CheckResult> {
    console.log('🎯 [FinalQualityChecker] 开始最终质量检查...');
    
    try {
      // 综合执行所有检查
      const [structureResult, syntaxResult, logicResult] = await Promise.all([
        this.performStructureCheck(code),
        this.performSyntaxCheck(code),
        this.performLogicCheck(code, originalQuery)
      ]);

      // 计算综合分数
      const weights = { structure: 0.3, syntax: 0.4, logic: 0.3 };
      const finalScore = Math.round(
        structureResult.score * weights.structure +
        syntaxResult.score * weights.syntax +
        logicResult.score * weights.logic
      );

      // 合并所有问题
      const allIssues = [
        ...structureResult.issues,
        ...syntaxResult.issues,
        ...logicResult.issues
      ];

      // 去重问题
      const uniqueIssues = this.deduplicateIssues(allIssues);

      // 合并建议
      const allSuggestions = [
        ...structureResult.suggestions,
        ...syntaxResult.suggestions,
        ...logicResult.suggestions
      ];

      return {
        passed: finalScore >= 80 && !uniqueIssues.some(i => i.type === 'critical'),
        score: finalScore,
        issues: uniqueIssues,
        suggestions: [...new Set(allSuggestions)], // 去重建议
        confidence: Math.min(structureResult.confidence, syntaxResult.confidence, logicResult.confidence)
      };

    } catch (error) {
      console.error('❌ [FinalQualityChecker] 检查失败:', error);
      return {
        passed: false,
        score: 0,
        issues: [{
          type: 'critical',
          category: 'structure',
          message: `最终质量检查失败: ${error instanceof Error ? error.message : String(error)}`,
          fixSuggestion: '请检查代码整体质量'
        }],
        suggestions: [],
        confidence: 0.1
      };
    }
  }

  // ============================================================================
  // 私有辅助方法
  // ============================================================================

  /**
   * 检查文件完整性
   */
  private static checkFileCompleteness(code: string): { issues: Issue[], penalty: number } {
    const issues: Issue[] = [];
    let penalty = 0;

    const requiredFiles = ['index.ttml', 'index.ttss', 'index.js', 'index.json', 'lynx.config.json'];
    const missingFiles = requiredFiles.filter(file => !code.includes(`path="${file}"`));

    missingFiles.forEach(file => {
      issues.push({
        type: 'critical',
        category: 'structure',
        message: `缺少必需文件: ${file}`,
        fixSuggestion: `请添加 ${file} 文件`
      });
      penalty += 20;
    });

    return { issues, penalty };
  }

  /**
   * 检查TTML结构
   */
  private static checkTTMLStructure(code: string): { issues: Issue[], penalty: number } {
    const issues: Issue[] = [];
    let penalty = 0;

    // 检查是否有TTML内容
    if (!code.includes('<view') && !code.includes('<text')) {
      issues.push({
        type: 'critical',
        category: 'structure',
        message: 'TTML文件缺少基础组件',
        fixSuggestion: '请添加view或text等基础组件'
      });
      penalty += 30;
    }

    return { issues, penalty };
  }

  /**
   * 去重问题
   */
  private static deduplicateIssues(issues: Issue[]): Issue[] {
    const seen = new Set<string>();
    return issues.filter(issue => {
      const key = `${issue.type}-${issue.category}-${issue.message}`;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  // 其他检查方法的占位符...
  private static checkComponentUsage(code: string) { return { issues: [], penalty: 0 }; }
  private static checkTTMLSyntax(code: string) { return { issues: [], penalty: 0 }; }
  private static checkTTSSSyntax(code: string) { return { issues: [], penalty: 0 }; }
  private static checkJavaScriptSyntax(code: string) { return { issues: [], penalty: 0 }; }
  private static checkJSONSyntax(code: string) { return { issues: [], penalty: 0 }; }
  private static checkRequirementImplementation(code: string, query: string) { return { issues: [], penalty: 0 }; }
  private static checkDataFlow(code: string) { return { issues: [], penalty: 0 }; }
  private static checkInteractionLogic(code: string) { return { issues: [], penalty: 0 }; }
  
  private static generateStructureSuggestions(issues: Issue[]): string[] { return []; }
  private static generateSyntaxSuggestions(issues: Issue[]): string[] { return []; }
  private static generateLogicSuggestions(issues: Issue[]): string[] { return []; }

  // AI检查方法占位符
  private static async performAIStructureCheck(code: string): Promise<CheckResult | null> { return null; }
  private static async performAISyntaxCheck(code: string): Promise<CheckResult | null> { return null; }
  private static async performAILogicCheck(code: string, query: string): Promise<CheckResult | null> { return null; }
}
