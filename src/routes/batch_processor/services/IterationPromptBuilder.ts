/**
 * 迭代Prompt构建器 - 为多轮迭代的不同阶段构建专门的Prompt
 */

import { IterationRound, Issue } from './MultiRoundIterationService';

export class IterationPromptBuilder {
  /**
   * 构建规划阶段的Prompt
   */
  static buildPlanningPrompt(query: string): string {
    return `# 🎯 Lynx代码生成规划专家

你是世界顶级的Lynx框架规划专家，负责分析用户需求并制定最优的代码生成策略。

## 用户需求
${query}

## 你的任务
请深度分析这个需求，并输出一个详细的规划方案：

### 1. 需求复杂度评估
- 简单(simple): 单一功能，基础组件
- 中等(medium): 多个功能，需要交互
- 复杂(complex): 复杂逻辑，多种可视化

### 2. 所需组件识别
列出需要使用的Lynx组件：
- 基础组件: view, text, image, button等
- 高级组件: scroll-view, swiper, canvas等
- 图表组件: LightChart相关组件

### 3. 技术实现策略
- 布局策略: 网格/流式/卡片布局
- 可视化策略: Canvas绘图/LightChart图表/纯CSS
- 交互策略: 事件处理/数据绑定/状态管理

### 4. 预估实现步骤
按优先级列出实现步骤

请以JSON格式输出规划结果：
\`\`\`json
{
  "complexity": "medium",
  "requiredComponents": ["view", "text", "canvas", "lightchart"],
  "layoutStrategy": "card",
  "visualizationStrategy": "canvas+chart",
  "interactionStrategy": "event-driven",
  "estimatedSteps": [
    "创建基础布局结构",
    "实现核心可视化",
    "添加交互功能",
    "优化用户体验"
  ],
  "estimatedComplexity": 0.7,
  "recommendedApproach": "canvas-first"
}
\`\`\``;
  }

  /**
   * 构建代码生成阶段的Prompt
   */
  static buildGenerationPrompt(
    query: string,
    history: IterationRound[],
  ): string {
    const planningInfo =
      history.find(r => r.stage === 'planning')?.output || '';

    return `# 🔨 Lynx代码生成专家

你是世界顶级的Lynx框架开发专家，现在需要根据需求和规划生成高质量的Lynx代码。

## 用户需求
${query}

## 规划信息
${planningInfo}

## 严格输出约束
CRITICAL 绝对禁止项：
- 禁止输出任何解释、思考、说明文字
- 禁止"这是一个..."、"我将为您..."等开场白
- 禁止代码前后的任何解释性语言
- 必须且只能输出完整的 Lynx 代码

## 强制要求
- 直接输出完整的Lynx四件套代码
- 使用<FILES>和<FILE>标签包裹所有文件
- 每个文件都必须完整可运行
- 优先使用Canvas可视化效果

## 输出格式
<FILES>
<FILE path="index.ttml">
<!-- 完整的TTML结构 -->
</FILE>
<FILE path="index.ttss">
/* 完整的TTSS样式 */
</FILE>
<FILE path="index.js">
// 完整的JavaScript逻辑
</FILE>
<FILE path="index.json">
{
  "component": true
}
</FILE>

</FILE>
</FILES>

立即开始生成代码！`;
  }

  /**
   * 构建错误纠正阶段的Prompt
   */
  static buildCorrectionPrompt(
    code: string,
    issues: Issue[],
    originalQuery: string,
  ): string {
    const issuesList = issues
      .map(
        issue =>
          `- ${issue.type.toUpperCase()}: ${issue.message} (${issue.category})`,
      )
      .join('\n');

    return `# 🔧 Lynx代码错误纠正专家

你是世界顶级的Lynx框架调试专家，需要修复代码中发现的问题。

## 原始需求
${originalQuery}

## 当前代码
${code}

## 发现的问题
${issuesList}

## 你的任务
1. 仔细分析每个问题的根本原因
2. 修复所有发现的问题
3. 确保修复后的代码符合Lynx规范
4. 保持原有功能的完整性

## 严格输出约束
- 只输出修复后的完整代码
- 使用<FILES>和<FILE>标签格式
- 不要输出任何解释或说明

立即输出修复后的代码！`;
  }

  /**
   * 构建优化阶段的Prompt
   */
  static buildOptimizationPrompt(
    code: string,
    originalQuery: string,
    history: IterationRound[],
  ): string {
    const previousIssues = history
      .filter(r => r.checkResult?.issues.length)
      .map(r => r.checkResult?.issues.map(i => i.message).join(', '))
      .join('; ');

    return `# ⚡ Lynx代码优化专家

你是世界顶级的Lynx框架性能优化专家，需要将代码提升到生产级别的质量。

## 原始需求
${originalQuery}

## 当前代码
${code}

## 历史问题记录
${previousIssues}

## 优化目标
1. **性能优化**: 减少不必要的渲染，优化数据绑定
2. **用户体验**: 提升交互流畅度，增加视觉反馈
3. **代码质量**: 提高可读性和可维护性
4. **移动端适配**: 确保在各种设备上的最佳表现
5. **视觉效果**: 增强视觉吸引力和专业度

## 具体优化方向
- 添加加载状态和过渡动画
- 优化触摸交互体验
- 增强视觉层次和色彩搭配
- 添加错误处理和边界情况
- 优化Canvas绘图性能
- 完善LightChart图表配置

## 严格输出约束
- 只输出优化后的完整代码
- 使用<FILES>和<FILE>标签格式
- 确保所有优化都是实用且有效的

立即输出优化后的代码！`;
  }

  /**
   * 构建结构检查的Prompt
   */
  static buildStructureCheckPrompt(code: string): string {
    return `# 🏗️ Lynx代码结构检查专家

你是专业的Lynx代码结构分析师，需要检查代码的结构完整性。

## 待检查代码
${code}

## 检查项目
1. **文件完整性**: 是否包含所有必需的文件(ttml, ttss, js, json, config)
2. **标签结构**: TTML标签是否正确嵌套和闭合
3. **组件规范**: 是否使用了正确的Lynx组件
4. **数据绑定**: 数据绑定语法是否正确
5. **配置完整**: JSON配置是否完整有效

## 输出格式
请以JSON格式输出检查结果：
\`\`\`json
{
  "passed": true/false,
  "score": 85,
  "confidence": 0.9,
  "issues": [
    {
      "type": "critical/warning/suggestion",
      "category": "structure",
      "message": "具体问题描述",
      "location": "文件名:行号",
      "fixSuggestion": "修复建议"
    }
  ],
  "suggestions": [
    "改进建议1",
    "改进建议2"
  ]
}
\`\`\``;
  }

  /**
   * 构建语法检查的Prompt
   */
  static buildSyntaxCheckPrompt(code: string): string {
    return `# 📝 Lynx代码语法检查专家

你是专业的Lynx语法验证专家，需要检查代码的语法正确性。

## 待检查代码
${code}

## 检查项目
1. **TTML语法**: 标签属性、事件绑定语法
2. **TTSS语法**: CSS属性、选择器、单位使用
3. **JavaScript语法**: ES6语法、Lynx API调用
4. **JSON语法**: 配置文件格式正确性
5. **Lynx规范**: 是否符合Lynx框架约束

## 输出格式
请以JSON格式输出检查结果：
\`\`\`json
{
  "passed": true/false,
  "score": 90,
  "confidence": 0.95,
  "issues": [
    {
      "type": "critical/warning/suggestion",
      "category": "syntax",
      "message": "语法错误描述",
      "location": "具体位置",
      "fixSuggestion": "修复方法"
    }
  ],
  "suggestions": [
    "语法改进建议"
  ]
}
\`\`\``;
  }

  /**
   * 构建逻辑检查的Prompt
   */
  static buildLogicCheckPrompt(code: string, originalQuery: string): string {
    return `# 🧠 Lynx代码逻辑检查专家

你是专业的Lynx功能逻辑分析师，需要检查代码是否正确实现了用户需求。

## 用户需求
${originalQuery}

## 待检查代码
${code}

## 检查项目
1. **需求实现**: 是否完整实现了用户的功能需求
2. **逻辑正确**: 业务逻辑是否正确无误
3. **数据流**: 数据流转是否合理
4. **交互逻辑**: 用户交互是否符合预期
5. **边界处理**: 是否处理了异常情况

## 输出格式
请以JSON格式输出检查结果：
\`\`\`json
{
  "passed": true/false,
  "score": 88,
  "confidence": 0.85,
  "issues": [
    {
      "type": "critical/warning/suggestion",
      "category": "logic",
      "message": "逻辑问题描述",
      "location": "相关代码位置",
      "fixSuggestion": "逻辑修复建议"
    }
  ],
  "suggestions": [
    "功能改进建议"
  ]
}
\`\`\``;
  }
}
