// -----------------------------------------------------------------------------
// LocalStorageService.ts
// -----------------------------------------------------------------------------
// 该服务负责本地持久化存储和读取：
//  1. 系统提示词(System Prompt)
//  2. 处理结果历史记录
//  3. 用户配置(User Preferences)
// -----------------------------------------------------------------------------

import { ProcessResult, PromptTemplate, HistoryStats } from '../types';
import { formatTime } from '../utils/timeFormatter';
import { getPEPromptContent } from '../utils/pePromptLoader';

/**
 * 存储键常量
 */
const STORAGE_KEYS = {
  SYSTEM_PROMPT: 'batchProcessor.systemPrompt',
  HISTORY: 'batchProcessor.history',
  PREFERENCES: 'batchProcessor.preferences',
  PROMPT_TEMPLATES: 'batchProcessor.promptTemplates',
  LAST_SESSION: 'batchProcessor.lastSession',
};

/**
 * 默认系统提示词 - 使用 MasterLevelUIPromptLoader 的内容
 */
export const DEFAULT_SYSTEM_PROMPT = getPEPromptContent();

/**
 * 历史记录过滤类型
 */
export type HistoryFilterType = 'all' | 'success' | 'error';

/**
 * 历史记录排序类型
 */
export type HistorySortType =
  | 'date-desc'
  | 'date-asc'
  | 'time-desc'
  | 'time-asc';

/**
 * 历史记录查询参数
 */
export interface HistoryQueryParams {
  filter?: HistoryFilterType;
  sort?: HistorySortType;
  search?: string;
  page?: number;
  pageSize?: number;
}

/**
 * 分页历史记录结果
 */
export interface PagedHistory {
  items: ProcessResult[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  stats: HistoryStats;
}

/**
 * 本地存储服务
 */
export class LocalStorageService {
  /**
   * 检查当前环境是否支持LocalStorage
   */
  private static isAvailable(): boolean {
    try {
      const testKey = '__test__';
      localStorage.setItem(testKey, testKey);
      localStorage.removeItem(testKey);
      return true;
    } catch (e) {
      console.warn('[LocalStorageService] 本地存储不可用');
      return false;
    }
  }

  /**
   * 获取localStorage使用情况
   */
  private static getStorageUsage(): {
    used: number;
    total: number;
    available: number;
  } {
    let used = 0;
    let total = 5 * 1024 * 1024; // 默认5MB限制

    try {
      // 计算已使用空间
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }

      // 尝试检测总容量（不是所有浏览器都支持）
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        navigator.storage.estimate().then(estimate => {
          if (estimate.quota) {
            total = estimate.quota;
          }
        });
      }
    } catch (error) {
      console.warn('[LocalStorageService] 无法获取存储使用情况:', error);
    }

    return {
      used,
      total,
      available: total - used,
    };
  }

  /**
   * 清理localStorage空间 - 使用LRU策略
   */
  private static cleanupStorage(targetSize: number): boolean {
    try {
      console.log(
        '[LocalStorageService] 开始清理存储空间，目标释放:',
        targetSize,
        '字节',
      );

      // 获取所有存储项及其访问时间
      const storageItems: Array<{
        key: string;
        size: number;
        lastAccess: number;
        priority: number; // 优先级，数字越小越重要
      }> = [];

      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          const value = localStorage[key];
          const size = key.length + value.length;

          // 根据key类型设置优先级和最后访问时间
          let lastAccess = Date.now();
          let priority = 5; // 默认优先级

          try {
            // 尝试从数据中提取时间戳
            const parsed = JSON.parse(value);
            if (Array.isArray(parsed) && parsed.length > 0) {
              // 历史记录数组
              const timestamps = parsed
                .map(
                  item =>
                    item.startTime ||
                    item.createdAt ||
                    item.lastUsed ||
                    item.timestamp,
                )
                .filter(t => typeof t === 'number');
              if (timestamps.length > 0) {
                lastAccess = Math.max(...timestamps);
              }
            } else if (parsed && typeof parsed === 'object') {
              // 单个对象
              lastAccess =
                parsed.startTime ||
                parsed.createdAt ||
                parsed.lastUsed ||
                parsed.timestamp ||
                lastAccess;
            }
          } catch (e) {
            // 解析失败，使用默认值
          }

          // 设置优先级（数字越小越重要，越不容易被删除）
          if (key.includes('systemPrompt')) {
            priority = 1; // 系统提示词最重要
          } else if (key.includes('preferences')) {
            priority = 2; // 用户配置次重要
          } else if (key.includes('promptTemplates')) {
            priority = 3; // 提示词模板
          } else if (key.includes('history')) {
            priority = 4; // 历史记录
          } else if (key.includes('lastSession')) {
            priority = 6; // 会话信息可以删除
          } else {
            priority = 7; // 其他数据优先删除
          }

          storageItems.push({ key, size, lastAccess, priority });
        }
      }

      // 按优先级和访问时间排序（优先级高的在前，同优先级按访问时间排序，旧的在前）
      storageItems.sort((a, b) => {
        if (a.priority !== b.priority) {
          return b.priority - a.priority; // 优先级低的在前（优先删除）
        }
        return a.lastAccess - b.lastAccess; // 访问时间早的在前（优先删除）
      });

      let freedSpace = 0;
      let deletedCount = 0;

      // 删除项目直到释放足够空间
      for (const item of storageItems) {
        if (freedSpace >= targetSize) {
          break;
        }

        // 保护重要数据
        if (item.priority <= 2) {
          console.log(
            `[LocalStorageService] 跳过重要数据: ${item.key} (优先级: ${item.priority})`,
          );
          continue;
        }

        try {
          localStorage.removeItem(item.key);
          freedSpace += item.size;
          deletedCount++;
          console.log(
            `[LocalStorageService] 删除: ${item.key} (${item.size}字节, 优先级: ${item.priority})`,
          );
        } catch (error) {
          console.warn(`[LocalStorageService] 删除${item.key}失败:`, error);
        }
      }

      console.log(
        `[LocalStorageService] 清理完成: 删除${deletedCount}项, 释放${freedSpace}字节`,
      );
      return freedSpace >= targetSize * 0.8; // 释放了目标空间的80%就算成功
    } catch (error) {
      console.error('[LocalStorageService] 清理存储空间失败:', error);
      return false;
    }
  }

  /**
   * 保存数据到LocalStorage - 支持LRU自动清理
   */
  private static save<T>(key: string, data: T): boolean {
    try {
      if (!this.isAvailable()) {
        return false;
      }

      const serialized = JSON.stringify(data);
      const dataSize = serialized.length + key.length;

      // 尝试直接保存
      try {
        localStorage.setItem(key, serialized);
        return true;
      } catch (error) {
        // 如果是空间不足错误，尝试清理空间
        if (
          error.name === 'QuotaExceededError' ||
          error.message.includes('quota')
        ) {
          console.warn(
            `[LocalStorageService] 存储空间不足，尝试清理空间以保存'${key}'`,
          );

          // 计算需要清理的空间（数据大小 + 20%缓冲）
          const targetCleanupSize = Math.max(dataSize * 1.2, 1024 * 1024); // 至少清理1MB

          // 执行清理
          const cleanupSuccess = this.cleanupStorage(targetCleanupSize);

          if (cleanupSuccess) {
            try {
              // 清理后重试保存
              localStorage.setItem(key, serialized);
              console.log(`[LocalStorageService] 清理空间后成功保存'${key}'`);
              return true;
            } catch (retryError) {
              console.error(
                `[LocalStorageService] 清理空间后仍无法保存'${key}':`,
                retryError,
              );
              return false;
            }
          } else {
            console.error(
              `[LocalStorageService] 清理空间失败，无法保存'${key}'`,
            );
            return false;
          }
        } else {
          // 其他类型的错误
          throw error;
        }
      }
    } catch (error) {
      console.error(`[LocalStorageService] 保存到'${key}'失败:`, error);
      return false;
    }
  }

  /**
   * 从LocalStorage读取数据
   */
  private static load<T>(key: string, defaultValue: T): T {
    try {
      if (!this.isAvailable()) {
        return defaultValue;
      }

      const serialized = localStorage.getItem(key);
      if (serialized === null) {
        return defaultValue;
      }

      return JSON.parse(serialized) as T;
    } catch (error) {
      console.error(`[LocalStorageService] 从'${key}'读取失败:`, error);
      return defaultValue;
    }
  }

  /**
   * 保存系统提示词
   */
  static saveSystemPrompt(prompt: string): boolean {
    console.log('[LocalStorageService] 保存系统提示词');
    return this.save(STORAGE_KEYS.SYSTEM_PROMPT, prompt);
  }

  /**
   * 加载系统提示词
   */
  static loadSystemPrompt(): string {
    console.log('[LocalStorageService] 加载系统提示词');
    return this.load(STORAGE_KEYS.SYSTEM_PROMPT, DEFAULT_SYSTEM_PROMPT);
  }

  /**
   * 保存处理结果历史 - 使用LRU策略，最多保存50条记录
   * @param history 最近的处理结果数组
   * @param maxItems 最大保存条目数（默认50）
   */
  static saveHistory(history: ProcessResult[], maxItems = 50): boolean {
    console.log(
      `[LocalStorageService] 保存历史记录 (${history.length}项) - LRU策略，最大${maxItems}条`,
    );

    try {
      // 加载现有历史记录
      const existingHistory = this.loadHistory();

      // 使用Map来实现LRU逻辑
      const historyMap = new Map<string, ProcessResult>();

      // 先加载现有记录（按时间顺序，最新的在前）
      existingHistory.forEach(item => {
        historyMap.set(item.id, item);
      });

      // 添加新记录，如果已存在则更新（这会自动移到最新位置）
      history.forEach(item => {
        // 如果记录已存在，先删除再添加，确保在Map中的位置是最新的
        if (historyMap.has(item.id)) {
          historyMap.delete(item.id);
        }
        historyMap.set(item.id, item);
      });

      // 转换回数组并按时间排序（最新的在前）
      const allHistory = Array.from(historyMap.values()).sort(
        (a, b) => b.startTime - a.startTime,
      );

      // LRU策略：限制历史记录数量，保留最新的maxItems条
      const trimmed = allHistory.slice(0, maxItems);

      console.log(
        `[LocalStorageService] LRU处理完成：保留${trimmed.length}条记录，丢弃${Math.max(0, allHistory.length - maxItems)}条旧记录`,
      );

      // 注意：最近会话跟踪已被移除，不再需要单独保存会话标识

      return this.save(STORAGE_KEYS.HISTORY, trimmed);
    } catch (error) {
      console.error('[LocalStorageService] 保存历史记录失败:', error);
      return false;
    }
  }

  /**
   * 加载处理结果历史
   * @param params 查询参数
   */
  static loadHistory(params?: HistoryQueryParams): ProcessResult[] {
    console.log('[LocalStorageService] 加载历史记录');
    const allHistory = this.load<ProcessResult[]>(STORAGE_KEYS.HISTORY, []);

    if (!params) {
      return allHistory;
    }

    // 应用过滤
    let filtered = allHistory;

    // 状态过滤
    if (params.filter && params.filter !== 'all') {
      filtered = filtered.filter(item => item.status === params.filter);
    }

    // 搜索过滤
    if (params.search) {
      const searchLower = params.search.toLowerCase();
      filtered = filtered.filter(
        item =>
          item.query.toLowerCase().includes(searchLower) ||
          item.playgroundUrl?.toLowerCase().includes(searchLower) ||
          item.error?.toLowerCase().includes(searchLower),
      );
    }

    // 排序
    if (params.sort) {
      switch (params.sort) {
        case 'date-asc':
          filtered.sort((a, b) => a.startTime - b.startTime);
          break;
        case 'date-desc':
          filtered.sort((a, b) => b.startTime - a.startTime);
          break;
        case 'time-asc':
          filtered.sort((a, b) => (a.processTime || 0) - (b.processTime || 0));
          break;
        case 'time-desc':
          filtered.sort((a, b) => (b.processTime || 0) - (a.processTime || 0));
          break;
      }
    }

    return filtered;
  }

  /**
   * 获取分页历史记录
   * @param params 查询参数
   */
  static getPagedHistory(params: HistoryQueryParams = {}): PagedHistory {
    const page = params.page || 1;
    const pageSize = params.pageSize || 10;

    // 获取过滤后的所有记录
    const filtered = this.loadHistory(params);
    const total = filtered.length;
    const totalPages = Math.ceil(total / pageSize);

    // 计算分页
    const startIndex = (page - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, total);
    const items = filtered.slice(startIndex, endIndex);

    // 计算统计信息
    const stats = this.getHistoryStats(filtered);

    return {
      items,
      total,
      page,
      pageSize,
      totalPages,
      stats,
    };
  }

  /**
   * 获取历史记录统计
   * @param items 可选的历史记录项目，如不提供则加载全部
   */
  static getHistoryStats(items?: ProcessResult[]): HistoryStats {
    const history = items || this.loadHistory();

    const totalRecords = history.length;
    const successfulQueries = history.filter(
      item => item.status === 'success',
    ).length;
    const failedQueries = history.filter(
      item => item.status === 'error',
    ).length;
    const totalQueries = history.length;
    const successRate =
      totalQueries > 0 ? (successfulQueries / totalQueries) * 100 : 0;

    return {
      totalRecords,
      totalQueries,
      successfulQueries,
      failedQueries,
      successRate,
    };
  }

  // 已移除未使用的会话和ID查询方法：
  // - saveLastSession()
  // - getLastSessionResults()
  // - getResultById()

  /**
   * 清除历史记录
   */
  static clearHistory(): boolean {
    console.log('[LocalStorageService] 清除历史记录');
    return this.save(STORAGE_KEYS.HISTORY, []);
  }

  /**
   * 获取localStorage使用情况 - 公共方法
   */
  static getStorageInfo(): {
    used: number;
    total: number;
    available: number;
    usagePercent: number;
  } {
    const usage = this.getStorageUsage();
    return {
      ...usage,
      usagePercent: usage.total > 0 ? (usage.used / usage.total) * 100 : 0,
    };
  }

  /**
   * 手动清理存储空间 - 公共方法
   */
  static manualCleanup(targetSizeMB: number = 1): boolean {
    const targetSize = targetSizeMB * 1024 * 1024; // 转换为字节
    console.log(
      `[LocalStorageService] 手动清理存储空间，目标释放: ${targetSizeMB}MB`,
    );
    return this.cleanupStorage(targetSize);
  }
}

export default LocalStorageService;
