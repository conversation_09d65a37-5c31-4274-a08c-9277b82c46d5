/**
 * 多轮迭代优化服务 - Multi-Round Iteration Service
 *
 * 基于Aime思想，针对单个query进行多轮迭代优化，大幅降低错误率
 *
 * 核心流程：
 * 1. 需求分析与规划 (Planning)
 * 2. 初始代码生成 (Generation)
 * 3. 结构完整性检查 (Structure Check)
 * 4. 语法正确性验证 (Syntax Check)
 * 5. 功能逻辑检查 (Logic Check)
 * 6. 问题识别与纠偏 (Error Correction)
 * 7. 迭代优化 (Iterative Improvement)
 */

import { v4 as uuidv4 } from 'uuid';
import { IterationPromptBuilder } from './IterationPromptBuilder';
import { IterationChecker } from './IterationChecker';

// 迭代阶段枚举
export enum IterationStage {
  PLANNING = 'planning', // 需求分析与规划
  GENERATION = 'generation', // 代码生成
  STRUCTURE_CHECK = 'structure_check', // 结构检查
  SYNTAX_CHECK = 'syntax_check', // 语法检查
  LOGIC_CHECK = 'logic_check', // 逻辑检查
  ERROR_CORRECTION = 'error_correction', // 错误纠正
  OPTIMIZATION = 'optimization', // 优化改进
  COMPLETED = 'completed', // 完成
}

// 检查结果类型
export interface CheckResult {
  passed: boolean;
  score: number; // 0-100分
  issues: Issue[];
  suggestions: string[];
  confidence: number; // 0-1，AI对结果的信心度
}

// 问题类型
export interface Issue {
  type: 'critical' | 'warning' | 'suggestion';
  category: 'structure' | 'syntax' | 'logic' | 'performance' | 'ui';
  message: string;
  location?: string; // 问题位置
  fixSuggestion?: string; // 修复建议
}

// 迭代上下文
export interface IterationContext {
  sessionId: string;
  originalQuery: string;
  currentStage: IterationStage;
  roundNumber: number;
  maxRounds: number;

  // 历史记录
  history: IterationRound[];

  // 当前状态
  currentCode: string;
  currentIssues: Issue[];

  // 配置
  config: IterationConfig;
}

// 单轮迭代记录
export interface IterationRound {
  roundNumber: number;
  stage: IterationStage;
  input: string;
  output: string;
  checkResult?: CheckResult;
  timestamp: number;
  processingTime: number;
  aiPrompt: string;
  aiResponse: string;
}

// 迭代配置
export interface IterationConfig {
  maxRounds: number;
  qualityThreshold: number; // 质量阈值，达到后停止迭代
  enableStructureCheck: boolean;
  enableSyntaxCheck: boolean;
  enableLogicCheck: boolean;
  enableOptimization: boolean;
  timeoutPerRound: number; // 单轮超时时间
}

// 最终结果
export interface IterationResult {
  success: boolean;
  finalCode: string;
  totalRounds: number;
  finalScore: number;
  processingTime: number;
  context: IterationContext;
  error?: string;
}

/**
 * 多轮迭代优化服务
 */
export class MultiRoundIterationService {
  private defaultConfig: IterationConfig = {
    maxRounds: 5,
    qualityThreshold: 85,
    enableStructureCheck: true,
    enableSyntaxCheck: true,
    enableLogicCheck: true,
    enableOptimization: true,
    timeoutPerRound: 30000, // 30秒
  };

  /**
   * 执行多轮迭代优化
   */
  async executeIteration(
    query: string,
    config?: Partial<IterationConfig>,
  ): Promise<IterationResult> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const sessionId = uuidv4();
    const startTime = Date.now();

    console.log(
      `🚀 [MultiRoundIteration] 开始多轮迭代优化: ${query.substring(0, 50)}...`,
    );

    const context: IterationContext = {
      sessionId,
      originalQuery: query,
      currentStage: IterationStage.PLANNING,
      roundNumber: 0,
      maxRounds: finalConfig.maxRounds,
      history: [],
      currentCode: '',
      currentIssues: [],
      config: finalConfig,
    };

    try {
      // 执行迭代循环
      while (context.roundNumber < context.maxRounds) {
        const roundResult = await this.executeRound(context);

        // 检查是否达到质量阈值
        if (
          roundResult.checkResult &&
          roundResult.checkResult.score >= finalConfig.qualityThreshold
        ) {
          console.log(
            `✅ [MultiRoundIteration] 达到质量阈值 ${finalConfig.qualityThreshold}，提前结束`,
          );
          context.currentStage = IterationStage.COMPLETED;
          break;
        }

        // 检查是否有致命错误需要重新开始
        if (
          roundResult.checkResult &&
          roundResult.checkResult.issues.some(
            issue => issue.type === 'critical',
          )
        ) {
          console.log(`⚠️ [MultiRoundIteration] 发现致命错误，进入纠错模式`);
          context.currentStage = IterationStage.ERROR_CORRECTION;
        }
      }

      const totalTime = Date.now() - startTime;
      const finalScore = this.calculateFinalScore(context);

      return {
        success: true,
        finalCode: context.currentCode,
        totalRounds: context.roundNumber,
        finalScore,
        processingTime: totalTime,
        context,
      };
    } catch (error) {
      console.error('[MultiRoundIteration] 迭代过程出错:', error);

      return {
        success: false,
        finalCode: context.currentCode,
        totalRounds: context.roundNumber,
        finalScore: 0,
        processingTime: Date.now() - startTime,
        context,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 执行单轮迭代
   */
  private async executeRound(
    context: IterationContext,
  ): Promise<IterationRound> {
    const roundStart = Date.now();
    context.roundNumber++;

    console.log(
      `🔄 [MultiRoundIteration] 第${context.roundNumber}轮 - 阶段: ${context.currentStage}`,
    );

    const round: IterationRound = {
      roundNumber: context.roundNumber,
      stage: context.currentStage,
      input: context.currentCode,
      output: '',
      timestamp: roundStart,
      processingTime: 0,
      aiPrompt: '',
      aiResponse: '',
    };

    try {
      // 根据当前阶段执行相应操作
      switch (context.currentStage) {
        case IterationStage.PLANNING:
          await this.executePlanningStage(context, round);
          context.currentStage = IterationStage.GENERATION;
          break;

        case IterationStage.GENERATION:
          await this.executeGenerationStage(context, round);
          context.currentStage = IterationStage.STRUCTURE_CHECK;
          break;

        case IterationStage.STRUCTURE_CHECK:
          await this.executeStructureCheckStage(context, round);
          context.currentStage = IterationStage.SYNTAX_CHECK;
          break;

        case IterationStage.SYNTAX_CHECK:
          await this.executeSyntaxCheckStage(context, round);
          context.currentStage = IterationStage.LOGIC_CHECK;
          break;

        case IterationStage.LOGIC_CHECK:
          await this.executeLogicCheckStage(context, round);
          context.currentStage = IterationStage.OPTIMIZATION;
          break;

        case IterationStage.ERROR_CORRECTION:
          await this.executeErrorCorrectionStage(context, round);
          context.currentStage = IterationStage.STRUCTURE_CHECK;
          break;

        case IterationStage.OPTIMIZATION:
          await this.executeOptimizationStage(context, round);
          context.currentStage = IterationStage.COMPLETED;
          break;

        default:
          throw new Error(`未知的迭代阶段: ${context.currentStage}`);
      }

      round.processingTime = Date.now() - roundStart;
      context.history.push(round);

      return round;
    } catch (error) {
      round.processingTime = Date.now() - roundStart;
      console.error(
        `❌ [MultiRoundIteration] 第${context.roundNumber}轮执行失败:`,
        error,
      );
      throw error;
    }
  }

  /**
   * 计算最终分数
   */
  private calculateFinalScore(context: IterationContext): number {
    if (context.history.length === 0) return 0;

    const lastRound = context.history[context.history.length - 1];
    return lastRound.checkResult?.score || 0;
  }

  /**
   * 执行规划阶段 - 分析需求，制定代码生成策略
   */
  private async executePlanningStage(
    context: IterationContext,
    round: IterationRound,
  ): Promise<void> {
    console.log('📋 [Planning] 分析需求并制定生成策略...');

    const planningPrompt = this.buildPlanningPrompt(context.originalQuery);
    round.aiPrompt = planningPrompt;

    // 调用AI进行需求分析
    const planningResult = await this.callAI(planningPrompt, 'planning');
    round.aiResponse = planningResult;
    round.output = planningResult;

    // 解析规划结果，提取关键信息
    const planningInfo = this.parsePlanningResult(planningResult);

    console.log('📋 [Planning] 规划完成:', {
      complexity: planningInfo.complexity,
      requiredComponents: planningInfo.requiredComponents,
      estimatedSteps: planningInfo.estimatedSteps,
    });
  }

  /**
   * 执行代码生成阶段
   */
  private async executeGenerationStage(
    context: IterationContext,
    round: IterationRound,
  ): Promise<void> {
    console.log('🔨 [Generation] 生成初始代码...');

    const generationPrompt = this.buildGenerationPrompt(
      context.originalQuery,
      context.history,
    );
    round.aiPrompt = generationPrompt;

    const generatedCode = await this.callAI(generationPrompt, 'generation');
    round.aiResponse = generatedCode;
    round.output = generatedCode;

    // 更新上下文中的当前代码
    context.currentCode = generatedCode;

    console.log('🔨 [Generation] 代码生成完成，长度:', generatedCode.length);
  }

  /**
   * 执行结构检查阶段
   */
  private async executeStructureCheckStage(
    context: IterationContext,
    round: IterationRound,
  ): Promise<void> {
    console.log('🏗️ [StructureCheck] 检查代码结构...');

    const checkResult = await this.performStructureCheck(context.currentCode);
    round.checkResult = checkResult;
    round.output = JSON.stringify(checkResult, null, 2);

    // 更新上下文中的问题列表
    context.currentIssues = checkResult.issues;

    console.log('🏗️ [StructureCheck] 结构检查完成:', {
      score: checkResult.score,
      issueCount: checkResult.issues.length,
      passed: checkResult.passed,
    });
  }

  /**
   * 执行语法检查阶段
   */
  private async executeSyntaxCheckStage(
    context: IterationContext,
    round: IterationRound,
  ): Promise<void> {
    console.log('📝 [SyntaxCheck] 检查语法正确性...');

    const checkResult = await this.performSyntaxCheck(context.currentCode);
    round.checkResult = checkResult;
    round.output = JSON.stringify(checkResult, null, 2);

    // 合并问题到上下文
    context.currentIssues = [...context.currentIssues, ...checkResult.issues];

    console.log('📝 [SyntaxCheck] 语法检查完成:', {
      score: checkResult.score,
      issueCount: checkResult.issues.length,
      passed: checkResult.passed,
    });
  }

  /**
   * 执行逻辑检查阶段
   */
  private async executeLogicCheckStage(
    context: IterationContext,
    round: IterationRound,
  ): Promise<void> {
    console.log('🧠 [LogicCheck] 检查功能逻辑...');

    const checkResult = await this.performLogicCheck(
      context.currentCode,
      context.originalQuery,
    );
    round.checkResult = checkResult;
    round.output = JSON.stringify(checkResult, null, 2);

    // 合并问题到上下文
    context.currentIssues = [...context.currentIssues, ...checkResult.issues];

    console.log('🧠 [LogicCheck] 逻辑检查完成:', {
      score: checkResult.score,
      issueCount: checkResult.issues.length,
      passed: checkResult.passed,
    });
  }

  /**
   * 执行错误纠正阶段
   */
  private async executeErrorCorrectionStage(
    context: IterationContext,
    round: IterationRound,
  ): Promise<void> {
    console.log('🔧 [ErrorCorrection] 纠正发现的问题...');

    const correctionPrompt = this.buildCorrectionPrompt(
      context.currentCode,
      context.currentIssues,
      context.originalQuery,
    );
    round.aiPrompt = correctionPrompt;

    const correctedCode = await this.callAI(correctionPrompt, 'correction');
    round.aiResponse = correctedCode;
    round.output = correctedCode;

    // 更新代码并清空问题列表
    context.currentCode = correctedCode;
    context.currentIssues = [];

    console.log(
      '🔧 [ErrorCorrection] 错误纠正完成，代码长度:',
      correctedCode.length,
    );
  }

  /**
   * 执行优化阶段
   */
  private async executeOptimizationStage(
    context: IterationContext,
    round: IterationRound,
  ): Promise<void> {
    console.log('⚡ [Optimization] 优化代码质量...');

    const optimizationPrompt = this.buildOptimizationPrompt(
      context.currentCode,
      context.originalQuery,
      context.history,
    );
    round.aiPrompt = optimizationPrompt;

    const optimizedCode = await this.callAI(optimizationPrompt, 'optimization');
    round.aiResponse = optimizedCode;
    round.output = optimizedCode;

    // 更新代码
    context.currentCode = optimizedCode;

    // 执行最终质量检查
    const finalCheck = await this.performFinalQualityCheck(
      context.currentCode,
      context.originalQuery,
    );
    round.checkResult = finalCheck;

    console.log('⚡ [Optimization] 优化完成，最终分数:', finalCheck.score);
  }

  // ============================================================================
  // Prompt构建方法
  // ============================================================================

  private buildPlanningPrompt(query: string): string {
    return IterationPromptBuilder.buildPlanningPrompt(query);
  }

  private buildGenerationPrompt(
    query: string,
    history: IterationRound[],
  ): string {
    return IterationPromptBuilder.buildGenerationPrompt(query, history);
  }

  private buildCorrectionPrompt(
    code: string,
    issues: Issue[],
    query: string,
  ): string {
    return IterationPromptBuilder.buildCorrectionPrompt(code, issues, query);
  }

  private buildOptimizationPrompt(
    code: string,
    query: string,
    history: IterationRound[],
  ): string {
    return IterationPromptBuilder.buildOptimizationPrompt(code, query, history);
  }

  // ============================================================================
  // 检查方法
  // ============================================================================

  private async performStructureCheck(code: string): Promise<CheckResult> {
    return IterationChecker.performStructureCheck(code);
  }

  private async performSyntaxCheck(code: string): Promise<CheckResult> {
    return IterationChecker.performSyntaxCheck(code);
  }

  private async performLogicCheck(
    code: string,
    query: string,
  ): Promise<CheckResult> {
    return IterationChecker.performLogicCheck(code, query);
  }

  private async performFinalQualityCheck(
    code: string,
    query: string,
  ): Promise<CheckResult> {
    return IterationChecker.performFinalQualityCheck(code, query);
  }

  // ============================================================================
  // AI调用和辅助方法
  // ============================================================================

  /**
   * 调用AI API
   */
  private async callAI(prompt: string, stage: string): Promise<string> {
    console.log(`🤖 [AI] 调用AI进行${stage}处理...`);

    try {
      // 这里应该调用实际的AI API
      // 暂时返回模拟结果
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API延迟

      if (stage === 'planning') {
        return JSON.stringify(
          {
            complexity: 'medium',
            requiredComponents: ['view', 'text', 'canvas'],
            layoutStrategy: 'card',
            visualizationStrategy: 'canvas',
            estimatedSteps: ['创建布局', '添加内容', '优化样式'],
          },
          null,
          2,
        );
      }

      if (stage === 'generation') {
        return `<FILES>
<FILE path="index.ttml">
<view class="container">
  <text class="title">示例标题</text>
</view>
</FILE>
<FILE path="index.ttss">
.container { padding: 20rpx; }
.title { font-size: 32rpx; }
</FILE>
<FILE path="index.js">
Card({
  data: { title: '示例' }
});
</FILE>
<FILE path="index.json">
{ "component": true }
</FILE>
<FILE path="lynx.config.json">
{ "window": { "navigationBarTitleText": "应用" } }
</FILE>
</FILES>`;
      }

      return '处理完成';
    } catch (error) {
      console.error(`❌ [AI] ${stage}处理失败:`, error);
      throw new Error(
        `AI ${stage}处理失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * 解析规划结果
   */
  private parsePlanningResult(result: string): any {
    try {
      // 尝试从结果中提取JSON
      const jsonMatch = result.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[1]);
      }

      // 如果没有找到JSON块，尝试直接解析
      return JSON.parse(result);
    } catch (error) {
      console.warn('⚠️ [Planning] 无法解析规划结果，使用默认值');
      return {
        complexity: 'medium',
        requiredComponents: ['view', 'text'],
        estimatedSteps: ['基础实现'],
      };
    }
  }
}
