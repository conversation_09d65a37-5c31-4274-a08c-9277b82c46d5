/**
 * 提示词上传服务
 *
 * 这个服务负责将所有的prompts文件上传到ByteRAG系统
 * 用于RAG检索和智能提示词生成
 */

import { uploadDocument, waitForDocumentReady } from '../byterag-api';
import { ModularPromptLoader } from '../prompts/ModularPromptLoader';
import { CognitiveOptimizedPrompt } from '../cognitive-optimized/index';

// 常量
const BIZ_ID = 'batch-processor-prompts';
const UPLOAD_TIMEOUT = 120000; // 2分钟

// 上传结果接口
interface UploadResult {
  success: boolean;
  fileName: string;
  docId?: string;
  error?: string;
}

// 提示词文件信息
interface PromptFileInfo {
  fileName: string;
  content: string;
  description: string;
  category: string;
}

/**
 * 提示词上传服务类
 */
export class PromptUploader {
  private static instance: PromptUploader;
  private uploadStatus: Map<string, UploadResult> = new Map();

  private constructor() {}

  static getInstance(): PromptUploader {
    if (!this.instance) {
      this.instance = new PromptUploader();
    }
    return this.instance;
  }

  /**
   * 获取所有需要上传的提示词文件
   */
  private async getPromptFiles(): Promise<PromptFileInfo[]> {
    const files: PromptFileInfo[] = [];

    try {
      // 1. 获取传统模块化提示词
      const modularLoader = ModularPromptLoader.getInstance();
      const traditionalPrompt = modularLoader.getMasterLevelLynxPromptContent();

      files.push({
        fileName: 'traditional-modular-prompt.txt',
        content: traditionalPrompt,
        description: '传统模块化Lynx提示词 - 完整版本',
        category: 'traditional',
      });

      // 2. 获取认知优化提示词
      const cognitiveOptimizer = CognitiveOptimizedPrompt.getInstance();
      const cognitivePrompt =
        cognitiveOptimizer.generateCognitiveOptimizedPrompt('通用代码生成');

      files.push({
        fileName: 'cognitive-optimized-prompt.txt',
        content: cognitivePrompt,
        description: '认知优化Lynx提示词 - 智能版本',
        category: 'cognitive',
      });

      // 3. 获取各个模块的单独内容
      const moduleNames = [
        'LynxFrameworkCore',
        'LynxComponents',
        'LynxStyleSystem',
        'LynxUtilsSystem',
        'FontAwesome',
        'BestPractices',
        'ThreadSynchronization',
        'VisualizationGuidance',
        'TTMLStrictConstraints',
        'TTSSStrictConstraints',
        'LightChartPromptLoader',
        'UIStandardsPrompt',
      ];

      for (const moduleName of moduleNames) {
        try {
          const moduleContent = modularLoader.getModuleContent(moduleName);
          if (moduleContent) {
            files.push({
              fileName: `module-${moduleName.toLowerCase()}.txt`,
              content: moduleContent,
              description: `${moduleName} 模块 - 单独内容`,
              category: 'module',
            });
          }
        } catch (error) {
          console.warn(`获取模块 ${moduleName} 失败:`, error);
        }
      }

      // 4. 添加使用示例和最佳实践
      const exampleContent = this.generateExampleContent();
      files.push({
        fileName: 'usage-examples.txt',
        content: exampleContent,
        description: 'Lynx框架使用示例和最佳实践',
        category: 'examples',
      });

      return files;
    } catch (error) {
      console.error('获取提示词文件失败:', error);
      return [];
    }
  }

  /**
   * 生成使用示例内容
   */
  private generateExampleContent(): string {
    return `# Lynx框架使用示例和最佳实践

## 基础组件示例

### 1. 文本和视图组件
\`\`\`ttml
<view class="container">
  <text class="title">标题文本</text>
  <text class="content">内容文本</text>
</view>
\`\`\`

### 2. 列表组件
\`\`\`ttml
<view class="list-container">
  <view tt:for="{{items}}" tt:for-item="item" class="list-item">
    <text class="item-text">{{item.name}}</text>
  </view>
</view>
\`\`\`

### 3. 表单组件
\`\`\`ttml
<view class="form-container">
  <input class="form-input" placeholder="请输入内容" bindinput="handleInput" />
  <button class="form-button" bindtap="handleSubmit">提交</button>
</view>
\`\`\`

## 样式系统示例

### 1. 基础样式
\`\`\`ttss
.container {
  padding: 20rpx;
  background-color: #ffffff;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}
\`\`\`

### 2. 布局样式
\`\`\`ttss
.flex-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
\`\`\`

## JavaScript逻辑示例

### 1. 页面生命周期
\`\`\`javascript
Page({
  data: {
    items: [],
    loading: false
  },

  onLoad() {
    this.fetchData();
  },

  async fetchData() {
    this.setData({ loading: true });
    try {
      const result = await tt.request({
        url: 'https://api.example.com/data',
        method: 'GET'
      });
      this.setData({ items: result.data });
    } catch (error) {
      console.error('数据获取失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  }
});
\`\`\`

### 2. 用户交互处理
\`\`\`javascript
Page({
  handleInput(event) {
    this.setData({
      inputValue: event.detail.value
    });
  },

  handleSubmit() {
    const { inputValue } = this.data;
    if (!inputValue.trim()) {
      tt.showToast({
        title: '请输入内容',
        icon: 'none'
      });
      return;
    }

    // 处理提交逻辑
    this.submitData(inputValue);
  }
});
\`\`\`

## 最佳实践

### 1. 性能优化
- 使用数据绑定时避免复杂计算
- 合理使用tt:if和tt:show条件渲染
- 图片懒加载和缓存策略

### 2. 用户体验
- 加载状态提示
- 错误处理和重试机制
- 响应式设计适配不同屏幕

### 3. 代码规范
- 组件化开发
- 统一的命名规范
- 注释和文档完善
`;
  }

  /**
   * 上传所有提示词文件到ByteRAG
   */
  async uploadAllPrompts(
    onProgress?: (progress: number, current: string) => void,
  ): Promise<UploadResult[]> {
    const files = await this.getPromptFiles();
    const results: UploadResult[] = [];

    console.log(`开始上传 ${files.length} 个提示词文件到ByteRAG...`);

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const progress = ((i + 1) / files.length) * 100;

      // 通知进度
      onProgress?.(progress, file.fileName);

      try {
        console.log(`正在上传: ${file.fileName}`);

        // 上传文档
        const uploadResult = await uploadDocument(
          file.content,
          BIZ_ID,
          file.fileName,
        );

        console.log(
          `上传成功: ${file.fileName}, 文档ID: ${uploadResult.doc_id}`,
        );

        // 等待文档处理完成
        const isReady = await waitForDocumentReady(
          uploadResult.doc_id,
          UPLOAD_TIMEOUT,
        );

        if (isReady) {
          const result: UploadResult = {
            success: true,
            fileName: file.fileName,
            docId: uploadResult.doc_id,
          };
          results.push(result);
          this.uploadStatus.set(file.fileName, result);
          console.log(`文档处理完成: ${file.fileName}`);
        } else {
          throw new Error('文档处理超时');
        }
      } catch (error) {
        console.error(`上传失败: ${file.fileName}`, error);
        const result: UploadResult = {
          success: false,
          fileName: file.fileName,
          error: error.message,
        };
        results.push(result);
        this.uploadStatus.set(file.fileName, result);
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`上传完成: ${successCount}/${files.length} 个文件成功`);

    return results;
  }

  /**
   * 获取上传状态
   */
  getUploadStatus(): Map<string, UploadResult> {
    return this.uploadStatus;
  }

  /**
   * 清除上传状态
   */
  clearUploadStatus(): void {
    this.uploadStatus.clear();
  }

  /**
   * 检查是否已上传
   */
  isUploaded(): boolean {
    return (
      this.uploadStatus.size > 0 &&
      Array.from(this.uploadStatus.values()).some(result => result.success)
    );
  }
}

// 导出单例实例
export const promptUploader = PromptUploader.getInstance();
