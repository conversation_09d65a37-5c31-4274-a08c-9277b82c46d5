/**
 * 三阶段深化增强适配器 - Three-Stage Enhancement Adapter
 * 
 * 核心职责：
 * - 为现有的EnhancedBatchProcessorService提供三阶段深化功能
 * - 保持完全的向后兼容性
 * - 提供可选的增强模式
 * - 优化prompt结构和组织方式
 * 
 * 设计原则：
 * - 最小化修改现有代码
 * - 插件式集成，不破坏原有架构
 * - 高性能，支持缓存和优化
 * - 完整的错误处理和降级机制
 */

import { 
  buildEnhancedPrompt, 
  buildThreeStagePrompt, 
  getEnhancedPromptStats,
  getMasterLevelLynxPromptContent 
} from '../prompts/ModularPromptLoader';

import { BatchConfig, ProcessResult } from '../types';

/**
 * 三阶段增强配置接口
 */
export interface ThreeStageEnhancementConfig {
  // 是否启用三阶段深化模式
  enableThreeStage?: boolean;
  
  // 质量增强选项
  qualityEnhancement?: {
    enableDeepAnalysis?: boolean;      // 启用深度需求分析
    enableDesignOptimization?: boolean; // 启用设计优化
    enableCodeExcellence?: boolean;    // 启用代码卓越性检查
  };
  
  // 性能优化选项
  performanceOptions?: {
    enablePromptCaching?: boolean;     // 启用prompt缓存
    enableStatisticsLogging?: boolean; // 启用统计日志
    maxPromptLength?: number;          // 最大prompt长度限制
  };
  
  // 降级策略选项
  fallbackOptions?: {
    enableAutoFallback?: boolean;      // 启用自动降级
    fallbackOnError?: boolean;         // 错误时降级到标准模式
    timeoutMs?: number;                // 超时降级时间
  };
}

/**
 * 增强处理结果接口
 */
export interface EnhancedProcessResult extends ProcessResult {
  // 增强信息
  enhancementInfo?: {
    isThreeStageUsed: boolean;         // 是否使用了三阶段模式
    promptLength: number;              // prompt长度
    cacheHit: boolean;                 // 是否命中缓存
    processingTime: number;            // 处理时间
    qualityScore?: number;             // 质量评分
  };
  
  // 阶段性能信息（可选）
  stagePerformance?: {
    analysisTime: number;              // 分析阶段耗时
    designTime: number;                // 设计阶段耗时
    implementationTime: number;        // 实现阶段耗时
  };
}

/**
 * 三阶段深化增强适配器类
 */
export class ThreeStageEnhancementAdapter {
  private config: ThreeStageEnhancementConfig;
  private performanceStats: Map<string, any> = new Map();
  
  constructor(config: ThreeStageEnhancementConfig = {}) {
    this.config = {
      enableThreeStage: false,
      qualityEnhancement: {
        enableDeepAnalysis: true,
        enableDesignOptimization: true,
        enableCodeExcellence: true,
      },
      performanceOptions: {
        enablePromptCaching: true,
        enableStatisticsLogging: false,
        maxPromptLength: 50000,
      },
      fallbackOptions: {
        enableAutoFallback: true,
        fallbackOnError: true,
        timeoutMs: 30000,
      },
      ...config,
    };
  }

  /**
   * 构建增强版系统prompt
   * @param userQuery 用户查询
   * @param originalConfig 原始配置
   * @returns 增强版prompt
   */
  public buildEnhancedSystemPrompt(
    userQuery: string, 
    originalConfig?: BatchConfig
  ): string {
    const startTime = Date.now();
    
    try {
      // 如果未启用三阶段模式，返回原有prompt
      if (!this.config.enableThreeStage) {
        return getMasterLevelLynxPromptContent();
      }

      // 构建三阶段深化prompt
      const enhancedPrompt = this.buildOptimizedThreeStagePrompt(userQuery);
      
      // 记录性能统计
      if (this.config.performanceOptions?.enableStatisticsLogging) {
        this.recordPerformanceStats(userQuery, startTime);
      }
      
      return enhancedPrompt;
      
    } catch (error) {
      console.error('Enhanced prompt building failed:', error);
      
      // 错误降级策略
      if (this.config.fallbackOptions?.fallbackOnError) {
        console.warn('Falling back to standard prompt due to error');
        return getMasterLevelLynxPromptContent();
      }
      
      throw error;
    }
  }

  /**
   * 构建优化的三阶段prompt
   */
  private buildOptimizedThreeStagePrompt(userQuery: string): string {
    // 获取基础的三阶段prompt
    let enhancedPrompt = buildThreeStagePrompt(userQuery);
    
    // 应用质量增强选项
    if (this.config.qualityEnhancement?.enableDeepAnalysis) {
      enhancedPrompt = this.applyDeepAnalysisEnhancement(enhancedPrompt);
    }
    
    if (this.config.qualityEnhancement?.enableDesignOptimization) {
      enhancedPrompt = this.applyDesignOptimizationEnhancement(enhancedPrompt);
    }
    
    if (this.config.qualityEnhancement?.enableCodeExcellence) {
      enhancedPrompt = this.applyCodeExcellenceEnhancement(enhancedPrompt);
    }
    
    // 应用长度限制
    if (this.config.performanceOptions?.maxPromptLength) {
      enhancedPrompt = this.applyLengthOptimization(
        enhancedPrompt, 
        this.config.performanceOptions.maxPromptLength
      );
    }
    
    return enhancedPrompt;
  }

  /**
   * 应用深度分析增强
   */
  private applyDeepAnalysisEnhancement(prompt: string): string {
    const deepAnalysisInstruction = `

## 🧠 深度分析增强指令
在阶段1进行需求洞察时，请特别关注：
- 用户隐含需求的识别和挖掘
- 使用场景的多维度分析
- 情感体验和用户价值的深度思考
- 功能优先级的智能排序

`;
    
    return prompt.replace(
      '## 🚀 执行指令',
      `${deepAnalysisInstruction}## 🚀 执行指令`
    );
  }

  /**
   * 应用设计优化增强
   */
  private applyDesignOptimizationEnhancement(prompt: string): string {
    const designOptimizationInstruction = `

## 🎨 设计优化增强指令
在阶段2进行架构设计时，请特别关注：
- 视觉层次的精心构建和优化
- 色彩搭配的和谐性和功能性
- 布局比例的黄金分割和美学平衡
- 用户体验流程的顺畅性和直觉性

`;
    
    return prompt.replace(
      '## 🚀 执行指令',
      `${designOptimizationInstruction}## 🚀 执行指令`
    );
  }

  /**
   * 应用代码卓越性增强
   */
  private applyCodeExcellenceEnhancement(prompt: string): string {
    const codeExcellenceInstruction = `

## 💎 代码卓越性增强指令
在阶段3进行代码实现时，请特别关注：
- 代码结构的清晰性和可维护性
- 性能优化的细节处理
- 错误处理和边界情况的考虑
- 代码的可扩展性和未来兼容性

`;
    
    return prompt.replace(
      '## 🚀 执行指令',
      `${codeExcellenceInstruction}## 🚀 执行指令`
    );
  }

  /**
   * 应用长度优化
   */
  private applyLengthOptimization(prompt: string, maxLength: number): string {
    if (prompt.length <= maxLength) {
      return prompt;
    }
    
    console.warn(`Prompt length (${prompt.length}) exceeds limit (${maxLength}), applying optimization`);
    
    // 简单的长度优化策略：保留核心部分
    const corePromptMatch = prompt.match(/(.*?## 📚 专业知识体系与技术规范.*)/s);
    if (corePromptMatch) {
      return corePromptMatch[1].substring(0, maxLength);
    }
    
    return prompt.substring(0, maxLength);
  }

  /**
   * 记录性能统计
   */
  private recordPerformanceStats(userQuery: string, startTime: number): void {
    const endTime = Date.now();
    const processingTime = endTime - startTime;
    
    const stats = getEnhancedPromptStats(userQuery);
    
    this.performanceStats.set(`query_${Date.now()}`, {
      userQuery,
      processingTime,
      ...stats,
      timestamp: endTime,
    });
    
    // 保持统计数据在合理范围内
    if (this.performanceStats.size > 100) {
      const oldestKey = this.performanceStats.keys().next().value;
      this.performanceStats.delete(oldestKey);
    }
  }

  /**
   * 获取性能统计信息
   */
  public getPerformanceStats(): Array<any> {
    return Array.from(this.performanceStats.values());
  }

  /**
   * 清理性能统计
   */
  public clearPerformanceStats(): void {
    this.performanceStats.clear();
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<ThreeStageEnhancementConfig>): void {
    this.config = {
      ...this.config,
      ...newConfig,
    };
  }

  /**
   * 获取当前配置
   */
  public getConfig(): ThreeStageEnhancementConfig {
    return { ...this.config };
  }

  /**
   * 检查是否应该使用三阶段模式
   */
  public shouldUseThreeStage(userQuery: string): boolean {
    if (!this.config.enableThreeStage) {
      return false;
    }
    
    // 可以根据查询复杂度进行智能判断
    const complexityScore = this.calculateQueryComplexity(userQuery);
    
    // 复杂度高的查询更适合使用三阶段模式
    return complexityScore > 0.5;
  }

  /**
   * 计算查询复杂度
   */
  private calculateQueryComplexity(userQuery: string): number {
    let complexity = 0;
    
    // 长度因子
    complexity += Math.min(userQuery.length / 200, 0.3);
    
    // 关键词复杂度
    const complexKeywords = ['图表', '可视化', '交互', '动画', '数据', '分析', '设计'];
    const keywordMatches = complexKeywords.filter(keyword => 
      userQuery.includes(keyword)
    ).length;
    complexity += (keywordMatches / complexKeywords.length) * 0.4;
    
    // 特殊符号和结构
    const specialChars = userQuery.match(/[，。；：""''（）【】]/g);
    if (specialChars) {
      complexity += Math.min(specialChars.length / 10, 0.3);
    }
    
    return Math.min(complexity, 1.0);
  }
}

/**
 * 创建默认的三阶段增强适配器实例
 */
export function createThreeStageAdapter(config?: ThreeStageEnhancementConfig): ThreeStageEnhancementAdapter {
  return new ThreeStageEnhancementAdapter(config);
}

/**
 * 创建启用三阶段模式的适配器
 */
export function createEnabledThreeStageAdapter(): ThreeStageEnhancementAdapter {
  return new ThreeStageEnhancementAdapter({
    enableThreeStage: true,
    qualityEnhancement: {
      enableDeepAnalysis: true,
      enableDesignOptimization: true,
      enableCodeExcellence: true,
    },
    performanceOptions: {
      enablePromptCaching: true,
      enableStatisticsLogging: true,
    },
  });
}

export default ThreeStageEnhancementAdapter;