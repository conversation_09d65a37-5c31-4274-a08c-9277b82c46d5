/**
 * 数据源标签组件样式
 * 
 * 🎯 核心功能：
 * - 数据源标签的视觉样式和交互效果
 * - 支持紧凑模式和标准模式
 * - 不同数据源类型的颜色区分
 * - 悬停效果和过渡动画
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🏷️ DATA SOURCE LABEL - 数据源标签基础样式
 * ═══════════════════════════════════════════════════════════════════════════ */

.data-source-label {
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  border-radius: 6px;
  cursor: help;
  transition: all 0.2s ease;
  white-space: nowrap;
  line-height: 1;
  min-width: fit-content;
}

/* 标准模式 */
.data-source-label:not(.compact) {
  gap: 6px;
  padding: 6px 10px;
  font-size: 12px;
  height: 28px;
}

/* 紧凑模式 */
.data-source-label.compact {
  gap: 4px;
  padding: 4px 8px;
  font-size: 11px;
  height: 24px;
}

/* 悬停效果 */
.data-source-label:hover {
  transform: translateY(-1px);
  color: #fff !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 DATA SOURCE TYPES - 不同数据源类型样式
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 底稿数据 - 蓝色主题 */
.data-source-label.internal {
  color: #2392EF;
  background-color: #f0f9ff;
  border: 1px solid #91d5ff;
}

.data-source-label.internal:hover {
  background-color: #2392EF;
  box-shadow: 0 2px 8px rgba(35, 146, 239, 0.2);
}

/* AI生成 - 蓝色主题 */
.data-source-label.ai {
  color: #1890ff;
  background-color: #f0f9ff;
  border: 1px solid #91d5ff;
}

.data-source-label.ai:hover {
  background-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

/* 回退模式 - 橙色主题 */
.data-source-label.fallback {
  color: #fa8c16;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
}

.data-source-label.fallback:hover {
  background-color: #fa8c16;
  box-shadow: 0 2px 8px rgba(250, 140, 22, 0.2);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔖 SPECIAL INDICATORS - 特殊标识符样式
 * ═══════════════════════════════════════════════════════════════════════════ */

.data-source-label .special-indicator {
  opacity: 0.8;
  margin-left: 2px;
}

.data-source-label.compact .special-indicator {
  font-size: 10px;
}

.data-source-label:not(.compact) .special-indicator {
  font-size: 11px;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📊 DATA SOURCE STATS - 数据源统计样式
 * ═══════════════════════════════════════════════════════════════════════════ */

.data-source-stats {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.data-source-stats.compact {
  gap: 8px;
}

.data-source-stats:not(.compact) {
  gap: 12px;
}

.data-source-stats-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.data-source-stats-item.compact {
  font-size: 11px;
}

.data-source-stats-item:not(.compact) {
  font-size: 12px;
}

.data-source-stats-count {
  font-weight: bold;
  margin-left: 2px;
}

.data-source-stats-percentage {
  opacity: 0.7;
}

.data-source-stats-percentage.compact {
  font-size: 10px;
}

.data-source-stats-percentage:not(.compact) {
  font-size: 11px;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 TOOLTIP CONTENT - 工具提示内容样式
 * ═══════════════════════════════════════════════════════════════════════════ */

.data-source-tooltip-content {
  max-width: 300px;
  white-space: pre-line;
  line-height: 1.4;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE DESIGN - 响应式设计
 * ═══════════════════════════════════════════════════════════════════════════ */

@media (max-width: 768px) {
  .data-source-label {
    font-size: 10px;
    padding: 3px 6px;
    height: 20px;
    gap: 3px;
  }
  
  .data-source-stats {
    gap: 6px;
  }
  
  .data-source-stats-item {
    font-size: 10px;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎭 ANIMATION EFFECTS - 动画效果
 * ═══════════════════════════════════════════════════════════════════════════ */

@keyframes labelPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.data-source-label.loading {
  animation: labelPulse 1.5s ease-in-out infinite;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔧 UTILITY CLASSES - 工具类
 * ═══════════════════════════════════════════════════════════════════════════ */

.data-source-label.no-hover {
  cursor: default;
}

.data-source-label.no-hover:hover {
  transform: none;
  background-color: inherit;
  color: inherit;
  box-shadow: none;
}

.data-source-label.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
