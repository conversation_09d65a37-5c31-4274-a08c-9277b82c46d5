/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎨 SEMI ICON SYSTEM - Semi Design 图标系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 提供与Semi Design图标的集成样式
 * 确保图标与按钮文字颜色保持一致
 *
 * @version 1.0
 * <AUTHOR> Agent
 * @updated 2025-07-18
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 BASE SEMI ICON STYLES - 基础图标样式
 * ═══════════════════════════════════════════════════════════════════════════ */

.semi-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  flex-shrink: 0;
  
  /* 平滑过渡 */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* 字体渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  /* 确保图标清晰度 */
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* 旋转动画 */
.semi-icon-spin {
  animation: semi-icon-spin 1s linear infinite;
  transform-origin: center center;
}

@keyframes semi-icon-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔘 BUTTON CONTEXT INTEGRATION - 按钮上下文集成
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 金色按钮中的图标 */
.btn-primary-gold .semi-icon,
.btn--primary-gold .semi-icon,
.btn-authority.btn-primary-gold .semi-icon,
.btn-gold-simple .semi-icon {
  color: #92400e;
}

/* 蓝色按钮中的图标 */
.btn-secondary-glass .semi-icon,
.btn--secondary-glass .semi-icon,
.btn-authority.btn-secondary-glass .semi-icon {
  color: #ffffff !important;
}

/* 抽屉中的金色按钮图标 */
.enhanced-drawer-container .action-buttons .btn-primary-gold .semi-icon,
.action-buttons .btn-primary-gold .semi-icon {
  color: #92400e !important;
}

/* 抽屉中的蓝色按钮图标 */
.enhanced-drawer-container .action-buttons .btn-secondary-glass .semi-icon,
.action-buttons .btn-secondary-glass .semi-icon {
  color: #ffffff !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 COLOR VARIANTS - 颜色变体
 * ═══════════════════════════════════════════════════════════════════════════ */

.semi-icon-gold {
  color: #92400e;
}

.semi-icon-blue {
  color: #1e40af;
}

.semi-icon-success {
  color: #16a34a;
}

.semi-icon-error {
  color: #dc2626;
}

.semi-icon-warning {
  color: #d97706;
}

.semi-icon-neutral {
  color: #64748b;
}

.semi-icon-white {
  color: #ffffff;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📏 SIZE VARIANTS - 尺寸变体
 * ═══════════════════════════════════════════════════════════════════════════ */

.semi-icon-small {
  width: 14px;
  height: 14px;
  font-size: 14px;
}

.semi-icon-default {
  width: 16px;
  height: 16px;
  font-size: 16px;
}

.semi-icon-large {
  width: 20px;
  height: 20px;
  font-size: 20px;
}

.semi-icon-extra-large {
  width: 24px;
  height: 24px;
  font-size: 24px;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 INTERACTIVE STATES - 交互状态
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 按钮悬停时的图标效果 */
button:hover .semi-icon:not(.semi-icon-spin) {
  transform: scale(1.05);
}

/* 按钮激活时的图标效果 */
button:active .semi-icon {
  transform: scale(0.95);
}

/* 禁用状态的图标 */
button:disabled .semi-icon {
  opacity: 0.5;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔧 ACCESSIBILITY - 无障碍支持
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .semi-icon {
    filter: contrast(1.2);
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .semi-icon-spin {
    animation: none;
  }
  
  .semi-icon,
  button:hover .semi-icon,
  button:active .semi-icon {
    transition: none;
    transform: none;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE DESIGN - 响应式设计
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 小屏幕图标调整 */
@media (max-width: 768px) {
  .semi-icon {
    width: 14px;
    height: 14px;
    font-size: 14px;
  }
  
  .semi-icon-large {
    width: 16px;
    height: 16px;
    font-size: 16px;
  }
  
  .semi-icon-extra-large {
    width: 18px;
    height: 18px;
    font-size: 18px;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎪 SPECIAL EFFECTS - 特殊效果
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 图标闪烁效果 */
.semi-icon-flash {
  animation: semi-icon-flash 0.6s ease-out;
}

@keyframes semi-icon-flash {
  0% { 
    transform: scale(1);
    opacity: 1;
  }
  50% { 
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% { 
    transform: scale(1);
    opacity: 1;
  }
}

/* 图标脉冲效果 */
.semi-icon-pulse {
  animation: semi-icon-pulse 2s ease-in-out infinite;
}

@keyframes semi-icon-pulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 1;
  }
  50% { 
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* 图标悬浮效果 */
.semi-icon-float {
  animation: semi-icon-float 3s ease-in-out infinite;
}

@keyframes semi-icon-float {
  0%, 100% { 
    transform: translateY(0);
  }
  50% { 
    transform: translateY(-2px);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔗 LEGACY COMPATIBILITY - 向后兼容
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 与原有Icon组件的兼容性 */
.icon.semi-icon {
  /* 确保新旧图标组件可以共存 */
  position: relative;
  z-index: auto;
}

/* 确保在现有按钮系统中正常工作 */
.batch-processor-layout .semi-icon {
  /* 继承现有的图标布局规则 */
  margin: 0;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📊 PERFORMANCE OPTIMIZATION - 性能优化
 * ═══════════════════════════════════════════════════════════════════════════ */

/* GPU加速优化 */
.semi-icon-spin,
.semi-icon-pulse,
.semi-icon-float {
  will-change: transform;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

/* 避免布局抖动 */
.semi-icon {
  contain: layout style;
}