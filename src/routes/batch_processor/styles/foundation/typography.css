/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 📝 TYPOGRAPHY SYSTEM - 字体系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 合并了typography.css和unified-titles.css的功能
 * 提供完整的字体排版系统和标题样式
 *
 * @version 4.0 - 整合版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces typography.css + unified-titles.css
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 TYPOGRAPHY UTILITY CLASSES - 字体工具类
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 标题样式 */
.typography-heading-large {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
}

.typography-heading-medium {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
}

.typography-heading-small {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--text-secondary);
}

/* 正文样式 */
.typography-body-large {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-secondary);
}

.typography-body-medium {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-secondary);
}

.typography-body-small {
  font-size: 12px;
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-tertiary);
}

/* 辅助文字样式 */
.typography-caption {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-disabled);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🌟 SPECIAL TYPOGRAPHY - 特殊字体效果
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 权威标题样式 */
.typography-authority-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  background: linear-gradient(135deg,
    #2392ef 0%,     /* 使用更鲜艳的蓝色起点 */
    #3a7bd5 25%,    /* 加入中间过渡蓝色 */
    #00d2ff 50%,    /* 添加亮青色增强层次感 */
    #ffed7f 75%,    /* 增加金色占比 */
    #d4af37 100%    /* 使用更亮的金色终点 */
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0px 2px 10px rgba(35, 146, 239, 0.2);
  animation: gradientShift 8s ease infinite;
  background-size: 200% auto;
}

/* 渐变文字效果 */
.gradient-text {
  background: linear-gradient(135deg,
    #2392ef 0%,     /* 使用更鲜艳的蓝色起点 */
    #3a7bd5 25%,    /* 加入中间过渡蓝色 */
    #00d2ff 50%,    /* 添加亮青色增强层次感 */
    #ffed7f 75%,    /* 增加金色占比 */
    #d4af37 100%    /* 使用更亮的金色终点 */
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0px 2px 10px rgba(35, 146, 239, 0.2);
  animation: gradientShift 8s ease infinite;
  background-size: 200% auto;
}

/* 统一标题样式 (从unified-titles.css合并) */
.title-text,
h3.title-text,
.glass-card .title-text,
.layout-console .title-text,
.batch-processor-layout .title-text {
  color: #0369a1 !important; /* 深蓝色 */
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-2);
}

.title-text:hover,
h3.title-text:hover,
.glass-card .title-text:hover,
.layout-console .title-text:hover,
.batch-processor-layout .title-text:hover {
  color: #1e40af !important; /* 更深蓝色 */
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📐 TEXT UTILITIES - 文本工具类
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 文本截断 */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-truncate-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.text-truncate-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* 文本对齐 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* 语义化文本颜色 - 增强对比度的蓝色文字变量 */
.text-primary { color: var(--text-blue-950); }
.text-secondary { color: var(--text-blue-800); }
.text-tertiary { color: var(--text-blue-700); }
.text-disabled { color: var(--text-blue-500); }
.text-inverse { color: var(--text-white); }

/* 字体权重 */
.font-light { font-weight: 300; }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* 字体大小 */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

/* 行高 */
.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎭 ANIMATIONS - 动画效果
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 渐变动画关键帧 */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 打字机效果 */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.typewriter-effect {
  overflow: hidden;
  border-right: 2px solid var(--color-primary-500);
  white-space: nowrap;
  animation: typewriter 3s steps(40, end), blink 0.75s step-end infinite;
}

@keyframes blink {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: var(--color-primary-500);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📝 SEMANTIC TYPOGRAPHY - 语义化排版
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 标题层级 */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-3);
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

/* 段落样式 */
p {
  margin-bottom: var(--space-4);
  line-height: var(--line-height-relaxed);
  color: var(--text-secondary);
}

/* 列表样式 */
ul, ol {
  margin-bottom: var(--space-4);
  padding-left: var(--space-5);
  color: var(--text-secondary);
}

li {
  margin-bottom: var(--space-1);
  line-height: var(--line-height-normal);
}

/* 链接样式 */
a {
  color: var(--color-primary-600);
  text-decoration: none;
  transition: color var(--duration-fast);
}

a:hover {
  color: var(--color-primary-700);
}

a:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
  border-radius: 2px;
}

/* 代码样式 */
code {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
  background: var(--color-gray-100);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  color: var(--color-gray-800);
}

pre {
  background: var(--color-gray-900);
  color: var(--color-gray-100);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  overflow-x: auto;
  margin-bottom: var(--space-4);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

pre code {
  background: none;
  padding: 0;
  color: inherit;
}

/* 引用样式 */
blockquote {
  border-left: 4px solid var(--color-primary-500);
  padding-left: var(--space-4);
  margin-left: 0;
  margin-bottom: var(--space-4);
  font-style: italic;
  color: var(--text-secondary);
}

/* 强调样式 */
strong {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

em {
  font-style: italic;
  color: var(--text-secondary);
}

/* 小字体 */
small {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

/* 标记样式 */
mark {
  background: var(--color-yellow-200);
  padding: var(--space-1);
  border-radius: var(--radius-sm);
}