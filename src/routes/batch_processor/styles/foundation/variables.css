/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎨 UNIFIED VARIABLES SYSTEM - 统一变量系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 🌟 解决变量冲突问题 - 统一variables.css和unified-theme.css
 * ✨ 保持UI完全一致 - 使用当前生产环境的实际值
 * 🎯 消除重复定义 - 单一变量来源
 * 💫 优化性能 - 减少CSS变量解析时间
 *
 * @version 4.0 - 统一版本
 * <AUTHOR> Agent  
 * @updated 2025-07-17
 * @replaces variables.css + unified-theme.css
 */

:root {
  /* ═══════════════════════════════════════════════════════════════════════════
   * 🌈 COLOR PALETTE - 色彩调色板 (统一定义，消除冲突)
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 💼 主色调系统 - Professional Blue Gradient Palette */
  /* 使用unified-theme.css中的值 (当前生产环境) */
  --color-primary-50: #f0f9ff;    /* 极浅天蓝色 - 模块背景 */
  --color-primary-100: #e0f2fe;   /* 浅天蓝色 - 卡片悬停背景 */
  --color-primary-200: #bae6fd;   /* 中浅天蓝色 - 边框色 */
  --color-primary-300: #7dd3fc;   /* 中天蓝色 - 辅助按钮 */
  --color-primary-400: #38bdf8;   /* 深天蓝色 - 主要按钮起始 */
  --color-primary-500: #87ceeb;   /* 主天蓝色 - 品牌色核心 */
  --color-primary-600: #0284c7;   /* 深主天蓝色 - 按钮悬停 */
  --color-primary-700: #0369a1;   /* 更深天蓝色 - 按钮激活 */
  --color-primary-800: #075985;   /* 深色天蓝色 - 渐变终点 */
  --color-primary-900: #0c4a6e;   /* 最深天蓝色 - 强调色 */

  /* 💙 辅助蓝色系统 - Secondary Blue Palette */
  --color-blue-50: #eff6ff;       /* 极浅蓝色 - 信息背景 */
  --color-blue-100: #dbeafe;      /* 浅蓝色 - 信息边框 */
  --color-blue-200: #bfdbfe;      /* 中浅蓝色 - 链接色 */
  --color-blue-300: #93c5fd;      /* 中蓝色 - 图标色 */
  --color-blue-400: #60a5fa;      /* 深蓝色 - 按钮色 */
  --color-blue-500: #3b82f6;      /* 主蓝色 - 强调色 */
  --color-blue-600: #2563eb;      /* 深主蓝色 - 悬停色 */
  --color-blue-700: #2392ef;      /* 更深蓝色 - 激活色 (保持兼容) */
  --color-blue-800: #1e40af;      /* 深蓝色 */
  --color-blue-900: #1e3a8a;      /* 最深蓝色 */
  --color-blue-rgb: 59, 130, 246; /* RGB值用于rgba */

  /* 🟡 黄色系统 - Yellow Palette */
  --color-yellow-50: #fefce8;
  --color-yellow-100: #fef3c7;
  --color-yellow-200: #fde68a;
  --color-yellow-300: #fcd34d;
  --color-yellow-400: #fbbf24;
  --color-yellow-500: #f59e0b;
  --color-yellow-600: #d97706;
  --color-yellow-700: #b45309;
  --color-yellow-800: #92400e;
  --color-yellow-900: #78350f;

  /* ⚪ 中性色系统 - Business Gray Palette */
  --color-white: #ffffff;          /* 纯白色 - 卡片背景 */
  --color-gray-50: #f9fafb;        /* 极浅灰 - 页面背景 */
  --color-gray-100: #f3f4f6;       /* 浅灰色 - 分割线 */
  --color-gray-200: #e5e7eb;       /* 中浅灰 - 边框色 */
  --color-gray-300: #d1d5db;       /* 中灰色 - 禁用边框 */
  --color-gray-400: #9ca3af;       /* 深中灰 - 占位符 */
  --color-gray-500: #6b7280;       /* 深灰色 - 次要文字 */
  --color-gray-600: #4b5563;       /* 更深灰 - 辅助文字 */
  --color-gray-700: #374151;       /* 深色文字 - 主要文字 */
  --color-gray-800: #1f2937;       /* 极深灰 - 标题文字 */
  --color-gray-900: #111827;       /* 最深灰 - 重要文字 */

  /* 🥇 金色系统 - Gold Accent Palette */
  --color-gold-50: #fffbeb;
  --color-gold-100: #fef3c7;
  --color-gold-200: #fde68a;
  --color-gold-300: #fcd34d;
  --color-gold-400: #fbbf24;
  --color-gold-500: #f59e0b;
  --color-gold-600: #d97706;
  --color-gold-700: #b45309;
  --color-gold-800: #92400e;
  --color-gold-900: #78350f;

  /* 🎯 状态色系统 - Status Colors (保持当前生产环境值) */
  --color-success: #87ceeb;           /* 成功天空蓝色 */
  --color-success-light: #0ea4e981;   /* 成功背景 */
  --color-success-highlight: linear-gradient(135deg, #10b0dc 0%, #1c9ded 100%);
  --color-success-500: #87ceeb;       /* 成功色主色调 */
  --color-success-600: #0284c7;       /* 成功色深色调 */
  --color-success-700: #0369a1;       /* 成功色更深色调 */

  --color-warning: #f59e0b;           /* 商务黄色 */
  --color-warning-light: #fef3c7;     /* 警告背景 */

  --color-error: #e37a17;             /* 失败暗金黄色 */
  --color-error-light: #fee2e2;       /* 错误背景 */
  --color-error-highlight: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  --color-error-500: #e37a17;         /* 错误色主色调 */
  --color-error-600: #d97706;         /* 错误色深色调 */

  --color-info: #87ceeb;              /* 商务蓝色 */
  --color-info-light: #e0f2fe;        /* 信息背景 */

  --color-processing: #6b7280;        /* 商务灰色 */
  --color-processing-light: #f3f4f6;  /* 处理背景 */

  /* RGB颜色变量，用于rgba动态透明度 */
  --color-primary-rgb: 14, 165, 233;  /* 主色RGB (87ceeb) */
  --color-blue-rgb: 59, 130, 246;     /* 蓝色RGB (3b82f6) */
  --color-success-rgb: 14, 165, 233;  /* 成功色RGB (87ceeb) */
  --color-warning-rgb: 245, 158, 11;  /* 警告色RGB (f59e0b) */
  --color-error-rgb: 227, 122, 23;    /* 错误色RGB (e37a17) */

  /* ═══════════════════════════════════════════════════════════════════════════
   * 🌈 GRADIENT SYSTEM - 渐变系统
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 🎨 主背景渐变 - 商务蓝白渐变 */
  --gradient-main: linear-gradient(135deg,
    var(--color-primary-50) 0%,    /* 极浅天蓝色起点 */
    var(--color-white) 35%,        /* 纯白色中段 */
    var(--color-gray-50) 65%,      /* 浅灰色过渡 */
    var(--color-blue-50) 100%      /* 浅蓝色终点 */
  );

  /* 🔘 按钮渐变系统 */
  --gradient-button: linear-gradient(135deg, #7dd3fc, #38bdf8 50%, #87ceeb);
  --gradient-button-hover: linear-gradient(135deg, #38bdf8, #87ceeb 50%, #0284c7);

  /* 🃏 卡片渐变 - 半透明白色到浅天蓝色 */
  --gradient-card: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(240, 249, 255, 0.8) 100%
  );

  /* 💼💙 商务蓝色主题渐变 - 右栏专用 */
  --gradient-business-blue: linear-gradient(135deg,
    rgba(240, 249, 255, 0.9) 0%,   /* 浅天蓝色 */
    rgba(239, 246, 255, 0.8) 50%,  /* 浅蓝色 */
    rgba(255, 255, 255, 0.95) 100% /* 纯白色 */
  );

  /* 状态渐变 */
  --color-success-gradient: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 30%, #cce7ff 70%, #87ceeb 100%);
  --color-error-gradient: linear-gradient(135deg, #fffef9 0%, #fffcf0 30%, #f5f5f5 70%, #e8e8e8 100%);

  /* ═══════════════════════════════════════════════════════════════════════════
   * 🌟 SHADOW SYSTEM - 阴影系统
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 🎭 基础阴影 - 商务蓝灰阴影系统 */
  --shadow-xs: 0 1px 2px rgba(100, 116, 139, 0.08);
  --shadow-sm: 0 1px 3px rgba(100, 116, 139, 0.12), 0 1px 2px rgba(100, 116, 139, 0.18);
  --shadow-md: 0 4px 6px rgba(100, 116, 139, 0.12), 0 2px 4px rgba(100, 116, 139, 0.08);
  --shadow-lg: 0 10px 15px rgba(100, 116, 139, 0.15), 0 4px 6px rgba(100, 116, 139, 0.08);
  --shadow-xl: 0 20px 25px rgba(100, 116, 139, 0.15), 0 10px 10px rgba(100, 116, 139, 0.04);
  --shadow-2xl: 0 25px 50px rgba(100, 116, 139, 0.20);

  /* 🔘 按钮专用阴影 */
  --shadow-button: 0 4px 12px rgba(14, 165, 233, 0.20);
  --shadow-button-hover: 0 6px 16px rgba(14, 165, 233, 0.28);
  --shadow-green-sm: 0 2px 8px rgba(14, 165, 233, 0.20);
  --shadow-green-md: 0 4px 12px rgba(14, 165, 233, 0.25);
  --shadow-red-sm: 0 2px 8px rgba(227, 122, 23, 0.20);
  --shadow-red-md: 0 4px 12px rgba(227, 122, 23, 0.25);

  /* ✨ 特殊效果阴影 */
  --shadow-elevated: 0 2px 8px rgba(100, 116, 139, 0.10), 0 1px 4px rgba(100, 116, 139, 0.06);
  --shadow-floating: 0 12px 28px rgba(100, 116, 139, 0.15), 0 4px 12px rgba(100, 116, 139, 0.08);
  --shadow-focus: 0 0 0 3px rgba(14, 165, 233, 0.12);
  --shadow-inner: inset 0 2px 4px rgba(100, 116, 139, 0.06);

  /* ═══════════════════════════════════════════════════════════════════════════
   * 📝 TYPOGRAPHY SYSTEM - 字体系统
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 字体族 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* 字体大小 */
  --font-size-xs: 0.75rem;      /* 12px */
  --font-size-sm: 0.875rem;     /* 14px */
  --font-size-base: 1rem;       /* 16px */
  --font-size-lg: 1.125rem;     /* 18px */
  --font-size-xl: 1.25rem;      /* 20px */
  --font-size-2xl: 1.5rem;      /* 24px */
  --font-size-3xl: 1.875rem;    /* 30px */

  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* 字重 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 蓝色文字色阶 - 增强深度和对比度 */
  --text-blue-50: #f0f9ff;    /* 极浅蓝色文字 - 深色背景用 */
  --text-blue-100: #e0f2fe;   /* 浅蓝色文字 - 深色背景用 */
  --text-blue-200: #bae6fd;   /* 中浅蓝色文字 - 深色背景用 */
  --text-blue-300: #7dd3fc;   /* 中蓝色文字 - 中等背景用 */
  --text-blue-400: #38bdf8;   /* 深中蓝色文字 - 浅色背景用 */
  --text-blue-500: #87ceeb;   /* 主蓝色文字 - 浅色背景用 */
  --text-blue-600: #0284c7;   /* 深蓝色文字 - 浅色背景用 */
  --text-blue-700: #0369a1;   /* 更深蓝色文字 - 浅色背景用 */
  --text-blue-800: #075985;   /* 深色蓝色文字 - 浅色背景用 */
  --text-blue-900: #0c4a6e;   /* 最深蓝色文字 - 浅色背景用 */
  --text-blue-950: #082f49;   /* 超深蓝色文字 - 浅色背景用 */
  --text-blue-975: #051e34;   /* 极深蓝色文字 - 浅色背景用 */

  /* 白色文字系列 - 深色背景用 */
  --text-white: #ffffff;       /* 纯白色文字 - 深色背景用 */
  --text-white-90: rgba(255, 255, 255, 0.9);   /* 90%透明度白色 */
  --text-white-80: rgba(255, 255, 255, 0.8);   /* 80%透明度白色 */
  --text-white-70: rgba(255, 255, 255, 0.7);   /* 70%透明度白色 */
  --text-white-60: rgba(255, 255, 255, 0.6);   /* 60%透明度白色 */
  --text-white-50: rgba(255, 255, 255, 0.5);   /* 50%透明度白色 */

  /* 输入框相关文字颜色 */
  --input-placeholder: var(--color-gray-400);  /* 占位符文字 - 浅灰色 */

  /* ═══════════════════════════════════════════════════════════════════════════
   * 📐 SPACING SYSTEM - 间距系统
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 8px 网格系统 */
  --space-0: 0;
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */

  /* 语义化间距 */
  --container-padding: var(--space-4);
  --content-gap: var(--space-6);
  --section-gap: var(--space-8);
  --card-gap: var(--space-3);
  --button-gap: var(--space-2);

  /* ═══════════════════════════════════════════════════════════════════════════
   * 🎨 BORDER RADIUS SYSTEM - 圆角系统
   * ═══════════════════════════════════════════════════════════════════════════ */

  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-full: 9999px;

  /* ═══════════════════════════════════════════════════════════════════════════
   * ⏱️ ANIMATION SYSTEM - 动画系统
   * ═══════════════════════════════════════════════════════════════════════════ */

  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;

  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* ═══════════════════════════════════════════════════════════════════════════
   * 🎯 LAYOUT SYSTEM - 布局系统
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 布局尺寸 */
  --sidebar-width: 300px;      /* 左栏宽度 */
  --console-width: 320px;      /* 右栏宽度 */
  --layout-gap: 12px;          /* 栏间距 */
  --layout-padding: 16px;      /* 栏内边距 */

  /* 响应式断点 */
  --breakpoint-sm: 768px;      /* 小屏断点 */
  --breakpoint-md: 1024px;     /* 中屏断点 */
  --breakpoint-lg: 1440px;     /* 大屏断点 */
  --breakpoint-xl: 1920px;     /* 超大屏断点 */

  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE VARIABLES - 响应式变量
 * ═══════════════════════════════════════════════════════════════════════════ */

@media (max-width: 1200px) {
  :root {
    --sidebar-width: 280px;
    --console-width: 300px;
    --layout-gap: 8px;
  }
}

@media (max-width: 768px) {
  :root {
    --sidebar-width: 100%;
    --console-width: 100%;
    --layout-gap: 4px;
    --layout-padding: 12px;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🌗 DARK MODE VARIABLES - 暗色模式变量 (预留)
 * ═══════════════════════════════════════════════════════════════════════════ */

@media (prefers-color-scheme: dark) {
  :root {
    /* 暗色模式变量预留 */
    /* 未来可在此处定义暗色模式的变量覆盖 */
  }
}