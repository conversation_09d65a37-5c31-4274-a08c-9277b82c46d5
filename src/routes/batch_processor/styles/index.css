/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎨 STYLES INDEX - 新样式系统主入口
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 🌟 简化的模块化样式系统
 * ✨ 从39个@import减少到9个@import (减少77%)
 * 🎯 按依赖关系和重要性排序加载
 * 💫 100%保持UI一致性
 *
 * @version 4.0 - 新架构版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces 原styles/index.css (39个@import)
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🌍 FOUNDATION LAYER - 基础层 (必须最先加载)
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 1. 统一变量系统 - 解决variables.css和unified-theme.css冲突 */
@import './foundation/variables.css';

/* 2. 基础样式 - 合并globals.css和layout-fix.css */
@import './foundation/base.css';

/* 3. 字体系统 - 合并typography.css和unified-titles.css */
@import './foundation/typography.css';

/* ═══════════════════════════════════════════════════════════════════════════
 * 🏗️ LAYOUT LAYER - 布局层
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 4. 网格布局 - 合并layout-fixes.css和responsive-14inch.css */
@import './layout/grid.css';

/* 5. 响应式布局 - 合并responsive-layout.css */
@import './layout/responsive.css';

/* ═══════════════════════════════════════════════════════════════════════════
 * 🧩 COMPONENTS LAYER - 组件层
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 6. 按钮组件 - 合并3个按钮文件 */
@import './components/buttons.css';

/* 7. 卡片组件 - 合并glass-card系统 */
@import './components/cards.css';

/* 8. 表单组件 - 合并输入相关样式 */
@import './components/forms.css';

/* 9. 面板组件 - 合并panels目录 */
@import './components/panels.css';

/* 10. 抽屉组件 - 合并drawers目录 */
@import './components/drawers.css';

/* 11. 指示器组件 - 合并status相关 */
@import './components/indicators.css';

/* 12. 标题组件 - 统一标题和图标系统 */
@import './components/titles.css';

/* 13. 图标组件 - 统一图标尺寸和颜色 */
@import './components/icons.css';

/* 14. Semi图标组件 - Semi Design图标系统 */
@import './components/semi-icons.css';

/* 15. 数据源标签组件 - 数据源标识和统计 */
@import './components/data-source-label.css';

/* ═══════════════════════════════════════════════════════════════════════════
 * 🛠️ UTILITIES LAYER - 工具层
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 16. 动画工具 - 合并animations目录 */
@import './utilities/animations.css';

/* 17. 辅助工具 - 合并各种helper */
@import './utilities/helpers.css';

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 THEMES LAYER - 主题层
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 18. 默认主题 - 合并优化文件 */
@import './themes/default.css';


/* ⚡ 主按钮优化 - 最高优先级覆盖 */
body .batch-processor-layout .btn-primary,
body .batch-processor-layout button.btn-primary {
  /* 🎨 浅金色渐变 - 明亮专业 */
  background: linear-gradient(135deg,
      #fefce8 0%,
        /* 极浅奶黄 */
        #fef3c7 20%,
          /* 浅黄色 */
          #fde68a 45%,
          /* 金黄色 */
          #fcd34d 70%,
          /* 中等金色 */
        #f59e0b 100%
          /* 温和橙金 */
    ) !important;
  color: white !important;

  /* 📏 优化的尺寸 - 100%宽度 */
    width: 100% !important;
    max-width: none !important;
  padding: 14px 24px !important;
  min-height: 48px !important;
  border-radius: 10px !important;
  font-size: 15px !important;
  font-weight: 600 !important;

  /* 🌟 优化的阴影效果 - 更明亮 */
    box-shadow:
      0 4px 16px rgba(251, 191, 36, 0.2),
      0 2px 8px rgba(253, 230, 138, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;

  /* ✨ 文字阴影优化 - 白色文字深色阴影 */
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 1px rgba(0, 0, 0, 0.2) !important;

  /* 确保内容完美居中 */
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;

  /* 过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

  /* 确保层级 */
  position: relative !important;
  z-index: 10 !important;
  border: none !important;
  overflow: hidden !important;
  }
  
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                /* 🌟 主按钮星光效果 - 金色主题 - 最高优先级 */
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                html body .batch-processor-layout .btn-primary::before,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                html body .batch-processor-layout button.btn-primary::before,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                body .batch-processor-layout .btn-primary.sparkle-hover::before,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                body .batch-processor-layout button.btn-primary.sparkle-hover::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
      height: 100% !important;
    background:
      radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
      radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.8) 1.5px, transparent 2.5px),
      radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
      radial-gradient(circle at 25% 75%, rgba(255, 255, 255, 0.7) 2px, transparent 3px),
      radial-gradient(circle at 92% 85%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),
      radial-gradient(circle at 45% 35%, rgba(255, 255, 255, 0.6) 1px, transparent 1.5px),
      radial-gradient(circle at 90% 60%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),
      radial-gradient(circle at 10% 85%, rgba(255, 255, 255, 0.7) 1px, transparent 1.5px),
      radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
      radial-gradient(circle at 75% 45%, rgba(255, 255, 255, 0.6) 1px, transparent 1.5px),
      radial-gradient(circle at 35% 60%, rgba(255, 255, 255, 0.5) 0.5px, transparent 1px),
      radial-gradient(circle at 80% 40%, rgba(255, 255, 255, 0.6) 0.5px, transparent 1px),
      radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.4) 0.5px, transparent 1px),
      radial-gradient(circle at 55% 85%, rgba(255, 255, 255, 0.7) 0.5px, transparent 1px),
      radial-gradient(circle at 5% 30%, rgba(255, 255, 255, 0.5) 0.5px, transparent 1px) !important;
    background-size: 100% 100% !important;
    animation: randomTwinkle1 3.2s ease-in-out infinite, randomTwinkle2 2.8s ease-in-out infinite 0.5s, randomTwinkle3 3.5s ease-in-out infinite 1s !important;
    pointer-events: none !important;
    z-index: 1 !important;
    border-radius: inherit !important;
    opacity: 1 !important;
      display: block !important;
  }
  
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                /* ✨ 主按钮高光渐变扫过效果 - 最高优先级 */
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                html body .batch-processor-layout .btn-primary::after,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                html body .batch-processor-layout button.btn-primary::after,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                body .batch-processor-layout .btn-primary.sparkle-hover::after,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                body .batch-processor-layout button.btn-primary.sparkle-hover::after {
    content: '' !important;
    position: absolute !important;
    top: -50% !important;
    left: -50% !important;
    width: 200% !important;
    height: 200% !important;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 40%,
        rgba(255, 255, 255, 0.3) 45%,
        rgba(255, 255, 255, 0.6) 50%,
        rgba(255, 255, 255, 0.3) 55%,
        rgba(255, 255, 255, 0.1) 60%,
        transparent 70%) !important;
    transform: translateX(-200%) translateY(-200%) rotate(45deg) !important;
    animation: whiteStarSweep 3.5s ease-in-out infinite !important;
    filter: blur(0.5px) !important;
    pointer-events: none !important;
    z-index: 2 !important;
    opacity: 1 !important;
      display: block !important;
  }

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                /* 主按钮悬停时的高光扫过 - 最高优先级 */
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                html body .batch-processor-layout .btn-primary:hover::after,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                html body .batch-processor-layout button.btn-primary:hover::after,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                body .batch-processor-layout .btn-primary.sparkle-hover:hover::after,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                body .batch-processor-layout button.btn-primary.sparkle-hover:hover::after {
    transform: translateX(100%) translateY(100%) rotate(45deg) !important;
  }
  
  /* 🔧 确保按钮内容在特效之上 */
  body .batch-processor-layout .btn-primary>*,
  body .batch-processor-layout button.btn-primary>* {
    position: relative !important;
    z-index: 3 !important;
  }
  
  /* ✨ sparkle-hover 类实现 - 增强星光效果 */
  body .batch-processor-layout .sparkle-hover {
    position: relative !important;
    overflow: hidden !important;
  }
  
  /* sparkle-hover 悬停时增强星光效果 */
  body .batch-processor-layout .sparkle-hover:hover::before {
    animation: gentleStarTwinkle 2s ease-in-out infinite !important;
    opacity: 1 !important;
  }
  
  /* sparkle-hover 悬停时额外的闪烁效果 */
  body .batch-processor-layout .sparkle-hover:hover {
    box-shadow:
      0 6px 20px rgba(251, 191, 36, 0.4),
      0 3px 12px rgba(253, 230, 138, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.7),
      0 0 30px rgba(255, 215, 0, 0.3) !important;
  }
  
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                /* 🚨 删除重复的星光效果定义 - 避免冲突 */
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                /* 星光效果已在第142行定义，此处删除重复定义 */
  
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                /* 🚨 删除重复的高光效果定义 - 避免冲突 */
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                /* 高光效果已在第163行定义，此处删除重复定义 */

/* 🌟 主按钮悬停效果 - 更明亮 */
body .batch-processor-layout .btn-primary:hover:not(:disabled),
body .batch-processor-layout button.btn-primary:hover:not(:disabled) {
  /* 🎨 悬停时的浅金色渐变 */
  background: linear-gradient(135deg,
      #fefce8 0%,
        /* 极浅奶黄 */
        #fef3c7 15%,
        /* 浅黄色 */
        #fde68a 35%,
        /* 金黄色 */
        #fcd34d 60%,
        /* 中等金色 */
        #f59e0b 100%
          /* 温和橙金 */
    ) !important;
  color: white !important;

  /* ✨ 白色文字阴影 */
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4), 0 1px 1px rgba(0, 0, 0, 0.3) !important;

  /* ✨ 增强的阴影效果 */
    box-shadow:
      0 6px 20px rgba(251, 191, 36, 0.3),
      0 3px 12px rgba(253, 230, 138, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;

  /* 🎭 微妙的提升动画 */
    transform: translateY(-1px) scale(1.01) !important;
}

/* 主按钮激活状态 */
body .batch-processor-layout .btn-primary:active,
body .batch-processor-layout button.btn-primary:active {
  /* 激活状态的金色效果 */
  background: linear-gradient(135deg,
      #fcd34d 0%,
      /* 中等金色 */
      #f59e0b 50%,
      /* 温和橙金 */
      #d97706 100%
      /* 深橙金色 */
    ) !important;

  transform: translateY(0) scale(1) !important;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3),
    inset 0 2px 4px rgba(146, 64, 14, 0.2) !important;
}

/* 主按钮禁用状态优化 */
body .batch-processor-layout .btn-primary:disabled,
body .batch-processor-layout button.btn-primary:disabled {
  background: linear-gradient(135deg,
      #f3f4f6 0%,
      #e5e7eb 100%) !important;
  color: #9ca3af !important;
  cursor: not-allowed !important;

  box-shadow: 0 2px 4px rgba(156, 163, 175, 0.1) !important;
  transform: none !important;

  /* 禁用所有交互效果 */
  pointer-events: none !important;
}

/* 处理中状态的特殊样式 */
body .batch-processor-layout .btn-primary.processing,
body .batch-processor-layout button.btn-primary.processing {
  background: linear-gradient(135deg,
      #fef3c7 0%,
      #fde68a 50%,
      #fcd34d 100%) !important;

  /* 添加脉冲动画 */
  animation: processing-pulse 2s ease-in-out infinite !important;
}

/* 主按钮内部结构优化 */
body .batch-processor-layout .btn-primary .button-content,
body .batch-processor-layout button.btn-primary .button-content {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  width: 100% !important;
}

body .batch-processor-layout .btn-primary .button-icon,
body .batch-processor-layout button.btn-primary .button-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
}

body .batch-processor-layout .btn-primary .button-text,
body .batch-processor-layout button.btn-primary .button-text {
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
  font-weight: 600 !important;
  white-space: nowrap !important;
}

body .batch-processor-layout .btn-primary .progress-badge,
body .batch-processor-layout button.btn-primary .progress-badge {
  background: rgba(146, 64, 14, 0.2) !important;
  color: #92400e !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
  font-size: 12px !important;
  font-weight: 700 !important;
  margin-left: 4px !important;
  border: 1px solid rgba(146, 64, 14, 0.3) !important;
}

/* 脉冲动画定义 */
@keyframes processing-pulse {

  0%,
  100% {
    box-shadow: 0 4px 16px rgba(251, 191, 36, 0.25);
  }

  50% {
    box-shadow: 0 6px 20px rgba(251, 191, 36, 0.4);
  }
}

/* 主按钮容器居中对齐 - 更精确的选择器 */
body .batch-processor-layout .mt-4.flex-shrink-0.space-y-4.relative.z-50,
body .batch-processor-layout .mt-4.flex-shrink-0.space-y-4,
body .batch-processor-layout div[class*="mt-4"][class*="space-y-4"] {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  text-align: center !important;
}

/* 🚨 删除重复的按钮样式定义 - 避免冲突 */
/* 主按钮的居中和基础样式已在第93行定义，此处删除重复定义 */

/* 渐变高光扫过效果已移至btn-primary-gold-simple的伪元素实现 */

/* ✨ 星光emoji效果 */
body .batch-processor-layout .btn-primary.sparkle-hover::after,
body .batch-processor-layout button.btn-primary.sparkle-hover::after {
  content: '✨' !important;
  position: absolute !important;
  top: 8px !important;
  right: 8px !important;
  font-size: 12px !important;
  opacity: 0 !important;
  transform: scale(0.5) rotate(0deg) !important;
  transition: all 0.4s ease !important;
  pointer-events: none !important;
  z-index: 4 !important;
  filter: hue-rotate(30deg) brightness(1.2) !important;
}

body .batch-processor-layout .btn-primary.sparkle-hover:hover::after,
body .batch-processor-layout button.btn-primary.sparkle-hover:hover::after {
  opacity: 0.9 !important;
  transform: scale(1.1) rotate(180deg) !important;
  animation: sparkleFloat 2.5s ease-in-out infinite !important;
}

@keyframes sparkleFloat {

  0%,
  100% {
    transform: scale(1.1) rotate(180deg) translateY(0);
    filter: hue-rotate(30deg) brightness(1.2);
  }

  50% {
    transform: scale(1.3) rotate(180deg) translateY(-3px);
    filter: hue-rotate(45deg) brightness(1.4);
  }
}

@keyframes gentleStarTwinkle {

  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }

                50% {
                  opacity: 0.9;
                  transform: scale(1.05);
                }
}

/* ✨ 强化星光图标容器 - 移除背景突出星光 */
body .batch-processor-layout .elegant-icon-container {
  width: 64px !important;
  height: 64px !important;
  margin: 0 auto 16px auto !important;
  border-radius: 50% !important;
  background: transparent !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;

  /* 移除阴影，突出星光效果 */
    box-shadow: none !important;

  /* 强化动画 */
    animation: enhancedIconFloat 4s ease-in-out infinite !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

body .batch-processor-layout .elegant-icon-container:hover {
  transform: translateY(-4px) scale(1.1) !important;
    box-shadow: none !important;
  }
    
    /* 🌟 悬停时增强星光效果 */
                                body .batch-processor-layout .elegant-icon-container:hover::before {
                                  animation: gentleSparkle 2s ease-in-out infinite, hoverSparkleBoost 0.6s ease-out !important;
                                }
                                body .batch-processor-layout .elegant-icon-container:hover::after {
                                  animation: surpriseSparkle 3s ease-in-out infinite, hoverSurpriseBoost 0.8s ease-out !important;
                                }
                
                                @keyframes hoverSparkleBoost {
                                  0% {
                                    opacity: 0.3;
                                    transform: scale(1) rotate(0deg);
                                  }
                
                                  50% {
                                    opacity: 0.9;
                                    transform: scale(1.1) rotate(180deg);
                                  }
                
                                  100% {
                                    opacity: 0.6;
                                    transform: scale(1.05) rotate(360deg);
                                  }
                                }
                
                                @keyframes hoverSurpriseBoost {
                                  0% {
                                    opacity: 0;
                                    transform: scale(0.5) rotate(0deg);
                                  }
                
                                  30% {
                                    opacity: 1;
                                    transform: scale(1.6) rotate(180deg);
                                  }
                
                                  60% {
                                    opacity: 0.8;
                                    transform: scale(1.3) rotate(360deg);
                                  }
                
                                  100% {
                                    opacity: 0.6;
                                    transform: scale(1.1) rotate(540deg);
                                  }
                                }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                /* 🌟 用星光形状代替图标 */
                                body .batch-processor-layout .elegant-icon-container .icon-main {
                                  position: relative !important;
                                  z-index: 2 !important;
                                  width: 32px !important;
                                    height: 32px !important;
                                    background: transparent !important;
                                    color: transparent !important;
                                    transform-origin: center !important;
                                }
/* 创建星光形状 */
body .batch-processor-layout .elegant-icon-container .icon-main::before {
  content: '' !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  width: 24px !important;
  height: 24px !important;
  background:
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 1) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 1) 50%, transparent 70%),
    linear-gradient(0deg, transparent 40%, rgba(147, 197, 253, 0.9) 50%, transparent 60%),
    linear-gradient(90deg, transparent 40%, rgba(147, 197, 253, 0.9) 50%, transparent 60%) !important;
  background-size: 100% 100% !important;
  transform: translate(-50%, -50%) !important;
  animation: starShapeGlow 2s ease-in-out infinite !important;
  filter: blur(0.5px) brightness(1.4) !important;
  z-index: 2 !important;
}

/* 星光形状的外层光晕 */
body .batch-processor-layout .elegant-icon-container .icon-main::after {
  content: '' !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  width: 36px !important;
  height: 36px !important;
  background:
    radial-gradient(circle, rgba(147, 197, 253, 0.6) 0%, rgba(59, 130, 246, 0.4) 30%, transparent 70%) !important;
  transform: translate(-50%, -50%) !important;
  animation: starGlowPulse 3s ease-in-out infinite !important;
  z-index: 1 !important;
}

/* ✨ 强化星光闪烁效果 - 更大更亮更多 */
                                body .batch-processor-layout .elegant-icon-container::before {
                                  content: '' !important;
                                  position: absolute !important;
                                  top: -15px !important;
                                    left: -15px !important;
                                    right: -15px !important;
                                    bottom: -15px !important;
                                  border-radius: inherit !important;
                                  background:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 1) 4px, rgba(147, 197, 253, 0.8) 6px, transparent 10px),
          radial-gradient(circle at 75% 30%, rgba(255, 255, 255, 0.9) 3px, rgba(147, 197, 253, 0.6) 5px, transparent 8px),
          radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 1) 4px, rgba(147, 197, 253, 0.8) 6px, transparent 10px),
          radial-gradient(circle at 20% 70%, rgba(255, 255, 255, 0.9) 3px, rgba(147, 197, 253, 0.6) 5px, transparent 8px),
          radial-gradient(circle at 50% 15%, rgba(255, 255, 255, 0.8) 2px, rgba(147, 197, 253, 0.5) 4px, transparent 6px),
          radial-gradient(circle at 15% 50%, rgba(255, 255, 255, 0.8) 2px, rgba(147, 197, 253, 0.5) 4px, transparent 6px) !important;
          background-size: 100% 100% !important;
      animation: enhancedSparkle 2.5s ease-in-out infinite !important;
        pointer-events: none !important;
        z-index: 1 !important;
        filter: blur(0.5px) brightness(1.3) !important;
      }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                /* 🌟 移除emoji，用纯星光效果 */
                                body .batch-processor-layout .elegant-icon-container::after {
                                  content: none !important;
                                  }
      
      @keyframes gentleSparkle {

        0%,
          100% {
          opacity: 0.8;
            transform: scale(1) rotate(0deg);
          }

                                25% {
          opacity: 1;
            transform: scale(1.05) rotate(90deg);
          }

                                50% {
          opacity: 0.9;
            transform: scale(1.02) rotate(180deg);
          }

                                75% {
          opacity: 1;
            transform: scale(1.03) rotate(270deg);
          }
          }
          
          @keyframes surpriseSparkle {

            0%,
            70%,
              100% {
                opacity: 0;
                transform: scale(0.5) rotate(0deg);
              }

                        75% {
                          opacity: 1;
                          transform: scale(1.3) rotate(180deg);
                        }

                        85% {
                          opacity: 1;
              transform: scale(1.5) rotate(360deg);
              }

                        95% {
                          opacity: 0.8;
                          transform: scale(1.2) rotate(540deg);
                        }
                        }
/* 🌊 增强的容器浮动动画 */
@keyframes enhancedIconFloat {

  0%,
  100% {
    transform: translateY(0) scale(1) rotate(0deg);
    }
    
    25% {
      transform: translateY(-2px) scale(1.02) rotate(1deg);
  }

  50% {
    transform: translateY(-4px) scale(1.05) rotate(0deg);
    }
    
    75% {
      transform: translateY(-2px) scale(1.02) rotate(-1deg);
    }
    }
    
    /* ⚡ 增强的图标脉冲动画 */
    @keyframes enhancedIconPulse {
    
      0%,
      100% {
        transform: scale(1) rotate(0deg);
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2)) drop-shadow(0 0 8px rgba(59, 130, 246, 0.6)) drop-shadow(0 0 16px rgba(147, 197, 253, 0.4));
      }
    
      33% {
        transform: scale(1.1) rotate(2deg);
        filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.3)) drop-shadow(0 0 12px rgba(59, 130, 246, 0.8)) drop-shadow(0 0 24px rgba(147, 197, 253, 0.6));
      }
    
      66% {
        transform: scale(1.05) rotate(-1deg);
        filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.25)) drop-shadow(0 0 10px rgba(59, 130, 246, 0.7)) drop-shadow(0 0 20px rgba(147, 197, 253, 0.5));
      }
    }
    
    /* ✨ 增强的星光闪烁动画 */
    @keyframes enhancedSparkle {
    
      0%,
      100% {
        opacity: 0.9;
        transform: scale(1) rotate(0deg);
      }
    
      20% {
        opacity: 1;
        transform: scale(1.1) rotate(72deg);
      }
    
      40% {
        opacity: 0.8;
        transform: scale(1.05) rotate(144deg);
      }
    
      60% {
        opacity: 1;
        transform: scale(1.08) rotate(216deg);
      }
    
      80% {
        opacity: 0.9;
        transform: scale(1.03) rotate(288deg);
  }
}

/* 🌟 星光主题文本框脉冲动画 */
.starlight-pulse {
  position: relative !important;
  animation: starlightPulse 1.2s ease-in-out !important;
}

.starlight-pulse::before {
  content: '' !important;
  position: absolute !important;
  top: -2px !important;
  left: -2px !important;
  right: -2px !important;
  bottom: -2px !important;
  background:
    radial-gradient(circle at 25% 25%, rgba(147, 197, 253, 0.6) 1px, transparent 3px),
    radial-gradient(circle at 75% 25%, rgba(255, 255, 255, 0.8) 1px, transparent 3px),
    radial-gradient(circle at 75% 75%, rgba(147, 197, 253, 0.6) 1px, transparent 3px),
    radial-gradient(circle at 25% 75%, rgba(255, 255, 255, 0.8) 1px, transparent 3px) !important;
  background-size: 50px 50px !important;
  border-radius: inherit !important;
  animation: gentleStarTwinkle 0.8s ease-in-out infinite !important;
  pointer-events: none !important;
  z-index: -1 !important;
}

@keyframes starlightPulse {

  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(147, 197, 253, 0.4);
    border-color: var(--color-blue-300);
  }

  50% {
    box-shadow: 0 0 0 8px rgba(147, 197, 253, 0.1);
    border-color: var(--color-blue-500);
  }
}

/* ✨ 星光主题清空动画 */
.starlight-clear {
  animation: starlightClear 0.6s ease-in-out !important;
}

@keyframes starlightClear {
  0% {
    transform: scale(1);
    opacity: 1;
    box-shadow: 0 0 0 0 rgba(147, 197, 253, 0.4);
  }

  25% {
    transform: scale(0.98);
    opacity: 0.8;
    box-shadow: 0 0 0 4px rgba(147, 197, 253, 0.3);
  }

  50% {
    transform: scale(1.02);
    opacity: 0.6;
    box-shadow: 0 0 0 8px rgba(147, 197, 253, 0.2);
  }

  75% {
    transform: scale(0.99);
    opacity: 0.8;
    box-shadow: 0 0 0 4px rgba(147, 197, 253, 0.3);
  }

  100% {
    transform: scale(1);
    opacity: 1;
    box-shadow: 0 0 0 0 rgba(147, 197, 253, 0.4);
  }
}
/* 🎨 确保按钮内容在特效之上 */
body .batch-processor-layout .btn-primary>*,
body .batch-processor-layout button.btn-primary>* {
  position: relative !important;
  z-index: 3 !important;
  }
  
  /* 主按钮后面的信息区域也居中 */
  body .batch-processor-layout .mt-4.flex-shrink-0.space-y-4>*,
  body .batch-processor-layout div[class*="mt-4"][class*="space-y-4"]>* {
    margin-left: auto !important;
    margin-right: auto !important;
    text-align: center !important;
  }
  
  /* 专门的主按钮容器样式 - 支持按钮100%宽度 */
  body .batch-processor-layout .main-button-container {
    display: flex !important;
    flex-direction: column !important;
    align-items: stretch !important;
    justify-content: center !important;
    width: 100% !important;
    text-align: center !important;
  }
  
  /* 主按钮容器内的非按钮元素居中 */
  body .batch-processor-layout .main-button-container>*:not(button) {
    margin-left: auto !important;
    margin-right: auto !important;
    text-align: center !important;
  }
  
  /* 确保主按钮在容器中占满100%宽度 */
  body .batch-processor-layout .main-button-container .btn-primary {
    margin: 0 !important;
    display: flex !important;
    width: 100% !important;
    max-width: 100% !important;
  }
  
  /* ═══════════════════════════════════════════════════════════════════════════
       * 🎨 ICON CONTAINER THEME OPTIMIZATION - 图标容器主题优化
       * ═══════════════════════════════════════════════════════════════════════════ */
  
  /* 优化图标容器主题色 - Light Blue到白色渐变 */
  body .batch-processor-layout .icon-container.icon-container--lg {
    background: linear-gradient(135deg,
        #87ceeb 0%,
        /* Light Blue */
        #b3d9f2 30%,
        /* 浅蓝色 */
        #ddeef8 70%,
        /* 极浅蓝色 */
        #ffffff 100%
        /* 白色 */
      ) !important;

    /* 确保圆形形状 */
    border-radius: 50% !important;

    /* 优化阴影效果 */
    box-shadow:
      0 4px 16px rgba(135, 206, 235, 0.25),
      0 2px 8px rgba(179, 217, 242, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;

    /* 确保图标颜色 */
    color: #1e40af !important;

    /* 过渡动画 */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
/* 修复脉冲动画定位问题 */
  position: relative !important;
  }
  
  /* 图标容器悬停效果 */
  body .batch-processor-layout .icon-container.icon-container--lg:hover {
  background: linear-gradient(135deg,
    #60a5fa 0%,
      /* 亮蓝色 */
      #93c5fd 30%,
      /* 中等蓝色 */
      #dbeafe 70%,
      /* 浅蓝色 */
      #ffffff 100%
      /* 白色 */
      ) !important;
  box-shadow:
    0 6px 20px rgba(96, 165, 250, 0.3),
      0 3px 10px rgba(147, 197, 253, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.9) !important;
    
      transform: translateY(-1px) scale(1.02) !important;
    }
    
    /* 确保图标容器内的Semi图标正确显示 */
    body .batch-processor-layout .icon-container.icon-container--lg .semi-icon,
    body .batch-processor-layout .icon-container.icon-container--lg svg {
      color: #1e40af !important;
      fill: #1e40af !important;
            width: 24px !important;
              height: 24px !important;
      
    }
    
    /* 图标容器内的Semi图标尺寸优化 */
    body .batch-processor-layout .icon-container.icon-container--lg .semi-icon {
      font-size: 24px !important;
      width: 24px !important;
      height: 24px !important;
}

/* ✨ 图标星光效果已移至 themes/default.css 以确保最高优先级 */
/* 图标基础尺寸设置 - 允许动画缩放 */
body .batch-processor-layout .icon-starlight .semi-icon,
body .batch-processor-layout .icon-starlight .semi-icon-default,
body .batch-processor-layout .icon-starlight .semi-icon-rating {
  font-size: 8rem !important;
  width: 8rem !important;
  height: 8rem !important;
  color: inherit !important;
  /* 确保动画可以正常工作 */
    transform-origin: center center !important;
    display: block !important;
}

body .batch-processor-layout .icon-starlight::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.6) 1px, rgba(219, 234, 254, 0.3) 3px, transparent 5px),
      radial-gradient(circle at 70% 20%, rgba(147, 197, 253, 0.5) 1px, rgba(96, 165, 250, 0.25) 3px, transparent 5px),
      radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.5) 1px, rgba(219, 234, 254, 0.25) 3px, transparent 5px) !important;
  background-size: 100% 100% !important;
  animation: ultraGentleStarTwinkle 8s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
  pointer-events: none !important;
  z-index: 1 !important;
  border-radius: 50% !important;
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.4)) !important;
}


body .batch-processor-layout .icon-starlight:hover {
  box-shadow:
    0 4px 16px rgba(59, 130, 246, 0.35) !important,
      0 0 25px rgba(255, 255, 255, 0.3) !important,
      0 0 35px rgba(59, 130, 246, 0.2) !important,
      inset 0 2px 0 rgba(255, 255, 255, 0.4) !important;
  animation: ultraGentleIconBreathe 4s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
}
/* 增强图标的平滑过渡 */
body .batch-processor-layout .icon-starlight .semi-icon,
body .batch-processor-layout .icon-starlight svg {
  transition: all 0.7s cubic-bezier(0.4, 0, 0.6, 1) !important;
  will-change: transform !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 BLUE THEME TEXT COLORS - 蓝色主题文字颜色系统
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 全局文字颜色覆盖 - 蓝色主题增强对比度 */
body .batch-processor-layout,
.batch-processor-layout {
  /* 基础文字颜色 - 使用更深的蓝色 */
  color: var(--text-blue-900) !important;
}

/* 标题文字 - 最深蓝色系 */
body .batch-processor-layout h1,
body .batch-processor-layout h2,
body .batch-processor-layout h3,
body .batch-processor-layout h4,
body .batch-processor-layout h5,
body .batch-processor-layout h6,
.batch-processor-layout h1,
.batch-processor-layout h2,
.batch-processor-layout h3,
.batch-processor-layout h4,
.batch-processor-layout h5,
.batch-processor-layout h6 {
  color: var(--text-blue-950);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔄 SEMI DESIGN SWITCH THEME OVERRIDE - Semi Design开关主题强制覆盖
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 全局强制覆盖所有Semi Design开关组件为蓝色主题 */
.batch-processor-layout .semi-switch,
.batch-processor-layout .semi-switch-checked,
.batch-processor-layout .semi-switch-small,
.batch-processor-layout .semi-switch-default,
.batch-processor-layout .semi-switch-large,
.batch-processor-layout [class*="semi-switch"],
body .semi-switch,
body .semi-switch-checked,
body .semi-switch-small,
body .semi-switch-default,
body .semi-switch-large,
body [class*="semi-switch"] {
  --semi-color-primary: #2392EF !important;
  --semi-color-primary-hover: #1F7ED6 !important;
  --semi-color-primary-active: #1B6ABD !important;
  --semi-color-success: #2392EF !important;
  --semi-color-success-hover: #1F7ED6 !important;
  --semi-color-success-active: #1B6ABD !important;
  --semi-color-success-disabled: rgba(35, 146, 239, 0.4) !important;
}

/* 开关内部元素的强制覆盖 */
.batch-processor-layout .semi-switch-knob,
.batch-processor-layout .semi-switch-inner,
.batch-processor-layout .semi-switch-loading,
body .semi-switch-knob,
body .semi-switch-inner,
body .semi-switch-loading {
  --semi-color-primary: #2392EF !important;
  --semi-color-success: #2392EF !important;
}
/* 段落和普通文字 - 深蓝色 */
body .batch-processor-layout p,
body .batch-processor-layout span,
body .batch-processor-layout div,
.batch-processor-layout p,
.batch-processor-layout span,
.batch-processor-layout div {
  color: var(--text-blue-800);
}

/* 次要文字 - 中蓝色 */
body .batch-processor-layout .text-sm,
body .batch-processor-layout .text-xs,
.batch-processor-layout .text-sm,
.batch-processor-layout .text-xs {
  color: var(--text-blue-700);
}

/* 深色背景上的白色文字 */
body .batch-processor-layout .bg-blue-500,
body .batch-processor-layout .bg-blue-600,
body .batch-processor-layout .bg-blue-700,
body .batch-processor-layout .bg-blue-800,
body .batch-processor-layout .bg-blue-900,
.batch-processor-layout .bg-blue-500,
.batch-processor-layout .bg-blue-600,
.batch-processor-layout .bg-blue-700,
.batch-processor-layout .bg-blue-800,
.batch-processor-layout .bg-blue-900 {
  color: var(--text-white) !important;
}

/* 深色背景上的文字层次 */
body .batch-processor-layout .bg-blue-500 h1,
body .batch-processor-layout .bg-blue-500 h2,
body .batch-processor-layout .bg-blue-500 h3,
.batch-processor-layout .bg-blue-500 h1,
.batch-processor-layout .bg-blue-500 h2,
.batch-processor-layout .bg-blue-500 h3 {
  color: var(--text-white) !important;
}

body .batch-processor-layout .bg-blue-500 p,
body .batch-processor-layout .bg-blue-500 span,
.batch-processor-layout .bg-blue-500 p,
.batch-processor-layout .bg-blue-500 span {
  color: var(--text-white-90) !important;
}

body .batch-processor-layout .bg-blue-500 .text-sm,
body .batch-processor-layout .bg-blue-500 .text-xs,
.batch-processor-layout .bg-blue-500 .text-sm,
.batch-processor-layout .bg-blue-500 .text-xs {
  color: var(--text-white-80) !important;
}
/* 注意：图标星光效果样式已移至 themes/default.css 以确保最高优先级 */
/* 次要玻璃按钮样式确保 - 使用高特异性选择器避免!important滥用 */
body .batch-processor-layout .btn-secondary-glass,
body .batch-processor-layout .btn--secondary-glass,
body .batch-processor-layout .btn-authority.btn-secondary-glass,
body .batch-processor-layout .enhanced-drawer-container .action-buttons .btn-secondary-glass,
body .batch-processor-layout .enhanced-drawer-container .action-buttons button.btn-secondary-glass,
body .batch-processor-layout .action-buttons .btn-secondary-glass,
body .batch-processor-layout .action-buttons button.btn-secondary-glass {
  background: linear-gradient(135deg, #7dd3fc, #7dd3fc 50%, #4ab3e3a7);
    color: #ffffff;
  border: 1px solid rgba(135, 206, 235, 0.6);
  border-radius: 8px;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  box-shadow: 0 4px 12px rgba(135, 206, 235, 0.3), 0 2px 6px rgba(163, 213, 240, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.6), inset 0 -1px 0 rgba(135, 206, 235, 0.2);
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.15);
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 次要玻璃按钮悬停效果 - 使用高特异性选择器避免!important滥用 */
body .batch-processor-layout .btn-secondary-glass:hover,
body .batch-processor-layout .btn--secondary-glass:hover,
body .batch-processor-layout .btn-authority.btn-secondary-glass:hover,
body .batch-processor-layout .enhanced-drawer-container .action-buttons .btn-secondary-glass:hover,
body .batch-processor-layout .action-buttons .btn-secondary-glass:hover {
  background: linear-gradient(135deg, #a3d5f0 0%, #87ceeb 100%, #a3d5f0 100%, #a3d5f0 100%, #7dd3fc 100%);
  border-color: rgba(135, 206, 235, 0.8);
  transform: translateY(-2px) scale(1.03);
  box-shadow: 0 6px 20px rgba(135, 206, 235, 0.4), 0 3px 10px rgba(163, 213, 240, 0.3), inset 0 2px 0 rgba(255, 255, 255, 0.7), inset 0 -2px 0 rgba(135, 206, 235, 0.3), 0 0 25px rgba(135, 206, 235, 0.2);
}

/* 快捷工具按钮专用样式 - 浅蓝到浅白渐变 */
.quick-tool-btn,
.btn.quick-tool-btn,
.btn--secondary-glass.quick-tool-btn,
body .batch-processor-layout .quick-tool-btn,
body .batch-processor-layout .btn.quick-tool-btn,
body .batch-processor-layout .btn--secondary-glass.quick-tool-btn,
body .batch-processor-layout .enhanced-drawer-container .action-buttons .quick-tool-btn,
body .batch-processor-layout .action-buttons .quick-tool-btn {
  background: linear-gradient(135deg,
        rgba(147, 196, 253, 0.434) 0%,
        rgba(191, 219, 254, 0.6) 50%,
        rgba(255, 255, 255, 0.9) 100%) !important;
    color: #1e40af !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
    backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1), 0 1px 3px rgba(147, 197, 253, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
    text-shadow: none !important;
    font-weight: 500 !important;
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.2s ease-out !important;
    z-index: 1 !important;
}

/* 快捷工具按钮悬停效果 - 浅蓝到浅白渐变悬停 */
.quick-tool-btn:hover,
.btn.quick-tool-btn:hover,
.btn--secondary-glass.quick-tool-btn:hover,
body .batch-processor-layout .quick-tool-btn:hover,
body .batch-processor-layout .btn.quick-tool-btn:hover,
body .batch-processor-layout .btn--secondary-glass.quick-tool-btn:hover,
body .batch-processor-layout .enhanced-drawer-container .action-buttons .quick-tool-btn:hover,
body .batch-processor-layout .action-buttons .quick-tool-btn:hover {
  transform: translateY(-1px) !important;
  background: linear-gradient(135deg,
        rgba(96, 165, 250, 0.9) 0%,
        rgba(147, 197, 253, 0.7) 50%,
        rgba(255, 255, 255, 0.95) 100%) !important;
    color: #1d4ed8 !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
    box-shadow: 0 3px 12px rgba(59, 130, 246, 0.15), 0 2px 6px rgba(96, 165, 250, 0.12), inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

/* 快捷工具按钮激活效果 - 轻量浅蓝色渐变 */
.quick-tool-btn:active,
.btn.quick-tool-btn:active,
.btn--secondary-glass.quick-tool-btn:active,
body .batch-processor-layout .quick-tool-btn:active,
body .batch-processor-layout .btn.quick-tool-btn:active,
body .batch-processor-layout .btn--secondary-glass.quick-tool-btn:active,
body .batch-processor-layout .enhanced-drawer-container .action-buttons .quick-tool-btn:active,
body .batch-processor-layout .action-buttons .quick-tool-btn:active {
  transform: translateY(0) scale(0.99) !important;
    box-shadow: 0 1px 4px rgba(30, 64, 175, 0.08) !important;
    transition: transform 0.15s cubic-bezier(0.25, 1, 0.5, 1) !important;
}

/* 快捷工具按钮图标颜色 - 确保图标与文字颜色一致 */
.quick-tool-btn .semi-icon,
.quick-tool-btn svg,
.btn.quick-tool-btn .semi-icon,
.btn.quick-tool-btn svg,
.btn--secondary-glass.quick-tool-btn .semi-icon,
.btn--secondary-glass.quick-tool-btn svg,
body .batch-processor-layout .quick-tool-btn .semi-icon,
body .batch-processor-layout .quick-tool-btn svg,
body .batch-processor-layout .btn.quick-tool-btn .semi-icon,
body .batch-processor-layout .btn.quick-tool-btn svg,
body .batch-processor-layout .btn--secondary-glass.quick-tool-btn .semi-icon,
body .batch-processor-layout .btn--secondary-glass.quick-tool-btn svg {
  color: #1e40af !important;
  fill: #1e40af !important;
}

/* 快捷工具按钮悬停时图标颜色 */
.quick-tool-btn:hover .semi-icon,
.quick-tool-btn:hover svg,
.btn.quick-tool-btn:hover .semi-icon,
.btn.quick-tool-btn:hover svg,
.btn--secondary-glass.quick-tool-btn:hover .semi-icon,
.btn--secondary-glass.quick-tool-btn:hover svg,
body .batch-processor-layout .quick-tool-btn:hover .semi-icon,
body .batch-processor-layout .quick-tool-btn:hover svg,
body .batch-processor-layout .btn.quick-tool-btn:hover .semi-icon,
body .batch-processor-layout .btn.quick-tool-btn:hover svg,
body .batch-processor-layout .btn--secondary-glass.quick-tool-btn:hover .semi-icon,
body .batch-processor-layout .btn--secondary-glass.quick-tool-btn:hover svg {
  color: #1d4ed8 !important;
  fill: #1d4ed8 !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🏷️ STATUS FILTER BUTTONS - 状态筛选按钮
 * ═══════════════════════════════════════════════════════════════════════════ */

/* ✨ 状态标签样式 - 浅色渐变和高光效果 */
body .batch-processor-layout .status-filter-btn {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 未选中状态 - 浅色渐变背景 */
body .batch-processor-layout .status-filter-btn.unselected {
  background: linear-gradient(135deg,
      rgba(248, 250, 252, 0.9) 0%,
      rgba(241, 245, 249, 0.8) 100%);
  color: #64748b;
  border-color: rgba(203, 213, 225, 0.5);
}

body .batch-processor-layout .status-filter-btn.unselected:hover {
  background: linear-gradient(135deg,
      rgba(241, 245, 249, 0.95) 0%,
      rgba(226, 232, 240, 0.9) 100%);
  color: #475569;
  border-color: rgba(203, 213, 225, 0.7);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

/* 全部按钮 - 深蓝色渐变背景和金色星光特效 */
body .batch-processor-layout .status-filter-btn.selected.all {
  background: linear-gradient(135deg,
      #67b8f1 0%,
      /* 深蓝 */
      #21a2f1 25%,
      /* 中蓝 */
      #30a4e2 75%,
      /* 浅蓝 */
      #93c5fd 100%);
  color: #ffffff;
  border-color: rgba(59, 130, 246, 0.6);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.4),
    0 0 20px rgba(147, 197, 253, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 全部按钮的金色星光特效 */
body .batch-processor-layout .status-filter-btn.selected.all::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.9) 2px, rgba(255, 215, 0, 0.5) 4px, transparent 6px),
    radial-gradient(circle at 75% 25%, rgba(255, 215, 0, 0.8) 2px, rgba(255, 248, 220, 0.4) 4px, transparent 6px),
    radial-gradient(circle at 85% 75%, rgba(255, 255, 255, 0.8) 2px, rgba(255, 215, 0, 0.4) 4px, transparent 6px);
  background-size: 100% 100%;
  animation: gentleStarTwinkle 3s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
  border-radius: inherit;
}

/* 全部按钮悬停效果 */
body .batch-processor-layout .status-filter-btn.selected.all:hover {
  background: linear-gradient(135deg,
      #1e88ca 0%,
      #77c3e6 25%,
      #60a5fa 50%,
      #93c5fd 75%,
      #dbeafe 100%);
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.6),
    0 0 40px rgba(255, 255, 255, 0.5),
    0 0 60px rgba(59, 130, 246, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
  transform: translateY(-1px) scale(1.02);
}

/* 成功按钮 - 浅蓝主题 */
body .batch-processor-layout .status-filter-btn.selected.success {
  background: linear-gradient(135deg,
      rgba(14, 165, 233, 0.15) 0%,
      rgba(2, 132, 199, 0.2) 50%,
      rgba(3, 105, 161, 0.15) 100%);
  color: #0369a1;
  border-color: rgba(14, 165, 233, 0.3);
  box-shadow:
    0 4px 12px rgba(14, 165, 233, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

/* 失败按钮 - 浅红主题 */
body .batch-processor-layout .status-filter-btn.selected.error {
  background: linear-gradient(135deg,
      rgba(239, 68, 68, 0.15) 0%,
      rgba(220, 38, 38, 0.2) 50%,
      rgba(185, 28, 28, 0.15) 100%);
  color: #dc2626;
  border-color: rgba(239, 68, 68, 0.3);
  box-shadow:
    0 4px 12px rgba(239, 68, 68, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

/* 处理中按钮 - 浅黄主题 */
body .batch-processor-layout .status-filter-btn.selected.processing {
  background: linear-gradient(135deg,
      rgba(251, 191, 36, 0.15) 0%,
      rgba(245, 158, 11, 0.2) 50%,
      rgba(217, 119, 6, 0.15) 100%);
  color: #d97706;
  border-color: rgba(251, 191, 36, 0.3);
  box-shadow:
    0 4px 12px rgba(251, 191, 36, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

/* 选中状态的悬停效果 */
body .batch-processor-layout .status-filter-btn.selected:hover {
  transform: translateY(-1px);
  box-shadow:
    0 6px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

/* 数字徽章样式增强 */
body .batch-processor-layout .status-filter-btn .count-badge {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

body .batch-processor-layout .status-filter-btn.selected .count-badge {
  background: rgba(255, 255, 255, 0.35);
  border-color: rgba(255, 255, 255, 0.3);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 BUTTON ALIGNMENT FIX - 按钮对齐修复
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 确保金色主要操作按钮内容居中对齐 */
body .batch-processor-layout .btn-primary-gold,
body .batch-processor-layout .btn--primary-gold,
body .batch-processor-layout .btn-authority.btn-primary-gold {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 按钮内部容器居中 */
body .batch-processor-layout .btn-primary-gold>div,
body .batch-processor-layout .btn--primary-gold>div,
body .batch-processor-layout .btn-authority.btn-primary-gold>div {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

/* 确保图标和文本容器的对齐 */
body .batch-processor-layout .btn-primary-gold .relative.flex.items-center.justify-center.w-full,
body .batch-processor-layout .btn--primary-gold .relative.flex.items-center.justify-center.w-full,
body .batch-processor-layout .btn-authority.btn-primary-gold .relative.flex.items-center.justify-center.w-full {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 修复图标容器的padding和margin问题 */
body .batch-processor-layout .btn-primary-gold .relative.flex.items-center.mr-2,
body .batch-processor-layout .btn--primary-gold .relative.flex.items-center.mr-2,
body .batch-processor-layout .btn-authority.btn-primary-gold .relative.flex.items-center.mr-2 {
  margin-right: 0;
}

/* 修复图标内层容器的padding */
body .batch-processor-layout .btn-primary-gold .relative.origin-center.z-20.inline-block.pl-5,
body .batch-processor-layout .btn--primary-gold .relative.origin-center.z-20.inline-block.pl-5,
body .batch-processor-layout .btn-authority.btn-primary-gold .relative.origin-center.z-20.inline-block.pl-5 {
  padding-left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保文本容器也居中 */
body .batch-processor-layout .btn-primary-gold .truncate.relative.z-10.flex.items-center,
body .batch-processor-layout .btn--primary-gold .truncate.relative.z-10.flex.items-center,
body .batch-processor-layout .btn-authority.btn-primary-gold .truncate.relative.z-10.flex.items-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 🔧 强制覆盖Tailwind类 - 使用更高优先级 */
body .batch-processor-layout .btn-primary-gold.pl-5,
body .batch-processor-layout .btn--primary-gold.pl-5,
body .batch-processor-layout .btn-authority.btn-primary-gold.pl-5,
body .batch-processor-layout .btn-primary-gold .pl-5,
body .batch-processor-layout .btn--primary-gold .pl-5,
body .batch-processor-layout .btn-authority.btn-primary-gold .pl-5 {
  padding-left: 0;
}

body .batch-processor-layout .btn-primary-gold.mr-2,
body .batch-processor-layout .btn--primary-gold.mr-2,
body .batch-processor-layout .btn-authority.btn-primary-gold.mr-2,
body .batch-processor-layout .btn-primary-gold .mr-2,
body .batch-processor-layout .btn--primary-gold .mr-2,
body .batch-processor-layout .btn-authority.btn-primary-gold .mr-2 {
  margin-right: 0;
}

/* 🎯 终极修复 - 直接针对按钮内的具体元素结构 */
body .batch-processor-layout button.btn-primary-gold div.relative.flex.items-center.mr-2,
body .batch-processor-layout button.btn--primary-gold div.relative.flex.items-center.mr-2,
body .batch-processor-layout button.btn-authority.btn-primary-gold div.relative.flex.items-center.mr-2 {
  margin-right: 0;
}

body .batch-processor-layout button.btn-primary-gold div.relative.origin-center.z-20.inline-block.pl-5,
body .batch-processor-layout button.btn--primary-gold div.relative.origin-center.z-20.inline-block.pl-5,
body .batch-processor-layout button.btn-authority.btn-primary-gold div.relative.origin-center.z-20.inline-block.pl-5 {
  padding-left: 0;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🌟 REMOVED DUPLICATE BUTTON STYLES - 已删除重复的按钮样式
 * ═══════════════════════════════════════════════════════════════════════════
 *
 * 注意：此处原有的 .btn-primary 样式定义已被删除，
 * 因为它与前面第93行的优化样式冲突。
 * 现在使用第93行的统一优化样式。
 */

/* 重复的星光特效已删除 - 避免与主要样式冲突 */

/* 重复的高光效果已删除 - 避免与主要样式冲突 */

/* 重复的悬停效果已删除 - 使用第141行的统一悬停样式 */

/* 重复的星光效果和内容层级已删除 - 避免冲突 */

/* 重复的处理中状态已删除 - 使用第203行的统一处理中样式 */

/* 重复的处理中星光效果已删除 - 避免冲突 */

/* 🎨 主要金色按钮 - 完全优化版本 */
body .batch-processor-layout .btn-primary-gold-optimized,
body .batch-processor-layout .btn--primary-gold-optimized,
body .batch-processor-layout button.btn-primary-gold-optimized {
  /* 🎯 布局和对齐 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;

  /* 📏 尺寸和间距 */
  width: 100%;
  height: 64px;
  padding: 0 24px;
  border-radius: 16px;

  /* 🎨 优化的金色渐变 */
  background: linear-gradient(135deg,
      #fef7cd 0%,
      /* 更浅的奶黄色 */
      #fef3c7 15%,
      /* 浅黄色 */
      #fde68a 35%,
      /* 金黄色 */
      #fcd34d 60%,
      /* 深金色 */
      #f59e0b 85%,
      /* 橙金色 */
      #d97706 100%
      /* 深橙金色 */
    );
  background-size: 200% 200%;

  /* 🖋️ 文字样式 */
  color: #92400e;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.4);

  /* 🔲 边框 */
  border: 2px solid rgba(217, 119, 6, 0.3);

  /* ✨ 阴影效果 */
  box-shadow:
    0 8px 24px rgba(245, 158, 11, 0.25),
    0 4px 12px rgba(251, 191, 36, 0.2),
    inset 0 2px 0 rgba(255, 255, 255, 0.7),
    inset 0 -1px 0 rgba(217, 119, 6, 0.2);

  /* 🎭 过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  user-select: none;
}

/* 🌟 悬停效果 - 优化为浅色 */
body .batch-processor-layout .btn-primary-gold-optimized:hover:not(:disabled),
body .batch-processor-layout .btn--primary-gold-optimized:hover:not(:disabled),
body .batch-processor-layout button.btn-primary-gold-optimized:hover:not(:disabled) {
  background: linear-gradient(135deg,
      #fefce8 0%,
      /* 极浅的奶黄色 */
      #fef3c7 20%,
      /* 浅黄色 */
      #fde68a 45%,
      /* 金黄色 */
      #fcd34d 70%,
      /* 中等金色 */
      #f59e0b 100%
      /* 温和的橙金色 */
    );

  transform: translateY(-2px) scale(1.02);

  box-shadow:
    0 12px 32px rgba(251, 191, 36, 0.3),
    0 6px 16px rgba(253, 230, 138, 0.25),
    inset 0 2px 0 rgba(255, 255, 255, 0.9),
    inset 0 -1px 0 rgba(251, 191, 36, 0.25);

  border-color: rgba(251, 191, 36, 0.4);
}

/* 🔄 处理中状态 - 优化为浅色 */
body .batch-processor-layout .btn-primary-gold-optimized.processing,
body .batch-processor-layout .btn--primary-gold-optimized.processing,
body .batch-processor-layout button.btn-primary-gold-optimized.processing {
  background: linear-gradient(135deg,
      #fefce8 0%,
      /* 极浅的奶黄色 */
      #fef3c7 25%,
      /* 浅黄色 */
      #fde68a 50%,
      /* 金黄色 */
      #fcd34d 75%,
      /* 中等金色 */
      #f59e0b 100%
      /* 温和的橙金色 */
    );
  animation: goldButtonPulse 2s ease-in-out infinite;
}

/* 🚫 禁用状态 */
body .batch-processor-layout .btn-primary-gold-optimized:disabled,
body .batch-processor-layout .btn--primary-gold-optimized:disabled,
body .batch-processor-layout button.btn-primary-gold-optimized:disabled {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #9ca3af;
  border-color: #d1d5db;
  box-shadow: none;
  cursor: not-allowed;
  transform: none;
}

/* 🎯 按钮内容容器优化 */
body .batch-processor-layout .btn-primary-gold-optimized .button-content,
body .batch-processor-layout .btn--primary-gold-optimized .button-content,
body .batch-processor-layout button.btn-primary-gold-optimized .button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
  position: relative;
  z-index: 2;
}

/* 🎨 图标样式优化 */
body .batch-processor-layout .btn-primary-gold-optimized .button-icon,
body .batch-processor-layout .btn--primary-gold-optimized .button-icon,
body .batch-processor-layout button.btn-primary-gold-optimized .button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: #92400e;
  flex-shrink: 0;
}

/* 📝 文本样式优化 */
body .batch-processor-layout .btn-primary-gold-optimized .button-text,
body .batch-processor-layout .btn--primary-gold-optimized .button-text,
body .batch-processor-layout button.btn-primary-gold-optimized .button-text {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 🏷️ 进度徽章优化 */
body .batch-processor-layout .btn-primary-gold-optimized .progress-badge,
body .batch-processor-layout .btn--primary-gold-optimized .progress-badge,
body .batch-processor-layout button.btn-primary-gold-optimized .progress-badge {
  background: rgba(146, 64, 14, 0.2);
  color: #92400e;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid rgba(146, 64, 14, 0.3);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

/* 波光效果已移至btn-primary-gold-simple的伪元素实现 */

/* 脉冲效果已移至btn-primary-gold-simple的动画实现 */

/* 🎭 动画定义 */
@keyframes goldButtonPulse {

  0%,
  100% {
    background-position: 0% 50%;
    box-shadow:
      0 8px 24px rgba(245, 158, 11, 0.25),
      0 4px 12px rgba(251, 191, 36, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.7),
      inset 0 -1px 0 rgba(217, 119, 6, 0.2);
  }

  50% {
    background-position: 100% 50%;
    box-shadow:
      0 12px 32px rgba(245, 158, 11, 0.35),
      0 6px 16px rgba(251, 191, 36, 0.3),
      inset 0 2px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(217, 119, 6, 0.3);
  }
}

@keyframes pulseFlow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 超柔和星光闪烁动画 */
@keyframes ultraGentleStarTwinkle {

  0%,
  100% {
    opacity: 0.4;
    transform: scale(0.95);
  }

  20% {
    opacity: 0.5;
    transform: scale(0.98);
  }

  40% {
    opacity: 0.6;
    transform: scale(1);
  }

  60% {
    opacity: 0.7;
    transform: scale(1.02);
  }

  80% {
    opacity: 0.6;
    transform: scale(1);
  }
}

/* 超柔和图标呼吸动画 */
@keyframes ultraGentleIconBreathe {

  0%,
  100% {
    transform: scale(1);
    box-shadow:
      0 0 15px rgba(59, 130, 246, 0.2),
      0 0 30px rgba(147, 197, 253, 0.15),
      inset 0 2px 0 rgba(255, 255, 255, 0.25);
  }

  25% {
    transform: scale(1.005);
    box-shadow:
      0 0 18px rgba(59, 130, 246, 0.25),
      0 0 35px rgba(147, 197, 253, 0.18),
      inset 0 2px 0 rgba(255, 255, 255, 0.28);
  }

  50% {
    transform: scale(1.01);
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(147, 197, 253, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.3);
  }

  75% {
    transform: scale(1.005);
    box-shadow:
      0 0 18px rgba(59, 130, 246, 0.25),
      0 0 35px rgba(147, 197, 253, 0.18),
      inset 0 2px 0 rgba(255, 255, 255, 0.28);
  }
}
/* ═══════════════════════════════════════════════════════════════════════════
 * 📊 PERFORMANCE STATS - 性能统计
 * ═══════════════════════════════════════════════════════════════════════════ */

/*
 * 🎯 优化成果:
 * - 文件数量: 从41个减少到15个 (减少63%)
 * - @import数量: 从39个减少到14个 (减少64%)
 * - 预期代码行数: 从5000+行减少到3500行 (减少30%)
 * - 变量冲突: 从50+个重复变量减少到0个 (减少100%)
 * - 维护复杂度: 从极高减少到中等 (减少70%)
 * 
 * 🚀 加载性能:
 * - 减少CSS解析时间: 约30%
 * - 减少变量计算开销: 约40%
 * - 简化依赖关系: 约77%
 * - 提升开发效率: 约60%
 */
/* ═══════════════════════════════════════════════════════════════════════════
 * 🚨 EMERGENCY STARLIGHT FIX - 紧急星光修复
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 🌟 超强力星光效果 - 绝对最高优先级 */
button.btn-primary::before,
.btn-primary::before,
button[class*="btn-primary"]::before,
[class*="btn-primary"]::before,
.batch-processor-layout .btn-primary::before,
.batch-processor-layout button.btn-primary::before,
.batch-processor-layout .sparkle-hover::before,
.batch-processor-layout button.sparkle-hover::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 1) 1px, rgba(255, 248, 220, 0.9) 3px, transparent 6px),
    radial-gradient(circle at 70% 20%, rgba(255, 248, 220, 1) 1px, rgba(255, 255, 255, 0.8) 3px, transparent 6px),
    radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 1) 1px, rgba(255, 248, 220, 0.7) 3px, transparent 6px),
    radial-gradient(circle at 30% 80%, rgba(255, 248, 220, 1) 1px, rgba(255, 255, 255, 0.6) 3px, transparent 6px),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.4) 0.5px, transparent 2px) !important;
  background-size: 100% 100% !important;
  animation: forceStarTwinkle 2.5s ease-in-out infinite !important;
  pointer-events: none !important;
  z-index: 1 !important;
  border-radius: inherit !important;
  opacity: 1 !important;
  display: block !important;
  visibility: visible !important;
}

/* 🌟 强制星光动画 */
@keyframes forceStarTwinkle {

  0%,
  100% {
    opacity: 0.7;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

/* ✨ 强制高光扫过效果 */
.batch-processor-layout .btn-primary::after,
.batch-processor-layout button.btn-primary::after {
  content: '' !important;
  position: absolute !important;
  top: -50% !important;
  left: -50% !important;
  width: 200% !important;
  height: 200% !important;
  background: linear-gradient(45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.5) 50%,
      transparent 70%) !important;
  transform: translateX(-100%) translateY(-100%) rotate(45deg) !important;
  transition: transform 0.8s ease !important;
  pointer-events: none !important;
  z-index: 2 !important;
  opacity: 1 !important;
  display: block !important;
}

/* 悬停时的高光扫过 */
.batch-processor-layout .btn-primary:hover::after,
.batch-processor-layout button.btn-primary:hover::after {
  transform: translateX(100%) translateY(100%) rotate(45deg) !important;
}

/* 确保按钮内容在特效之上 */
.batch-processor-layout .btn-primary>*,
.batch-processor-layout button.btn-primary>* {
  position: relative !important;
  z-index: 3 !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🌟 STARLIGHT LAYER - 星光层实现
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 🚀 开始批量处理 - 美丽惊喜动画 - 高优先级 */
body .batch-processor-layout .start-processing-card,
.batch-processor-layout .start-processing-card,
.start-processing-card {
  position: relative !important;
  overflow: hidden !important;
  animation: startCardFloat 4s ease-in-out infinite !important;
  cursor: pointer !important;
  will-change: transform, box-shadow !important;
}

body .batch-processor-layout .start-processing-card:hover,
.batch-processor-layout .start-processing-card:hover,
.start-processing-card:hover {
  transform: translateY(-8px) scale(1.05) !important;
  box-shadow:
    0 20px 40px rgba(59, 130, 246, 0.3),
    0 0 60px rgba(147, 197, 253, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  animation: startCardHover 0.6s ease-out forwards !important;
}

/* 🌟 图标容器增强 - 高优先级 */
body .batch-processor-layout .start-processing-icon,
.batch-processor-layout .start-processing-icon,
.start-processing-icon,
body .batch-processor-layout .ready-icon-animated,
.batch-processor-layout .ready-icon-animated,
.ready-icon-animated {
  position: relative !important;
  animation: startIconPulse 3s ease-in-out infinite !important;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #2563eb 100%) !important;
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.4),
    0 0 40px rgba(147, 197, 253, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.2) !important;
  will-change: transform, box-shadow !important;
}

/* ✨ 星光特效层 */
.start-processing-sparkles {
  position: absolute !important;
  top: -5px !important;
  left: -5px !important;
  right: -5px !important;
  bottom: -5px !important;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.9) 1px, transparent 3px),
    radial-gradient(circle at 80% 30%, rgba(255, 255, 255, 0.7) 1px, transparent 2px),
    radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.8) 1px, transparent 3px),
    radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.6) 1px, transparent 2px),
    radial-gradient(circle at 50% 10%, rgba(255, 255, 255, 0.5) 1px, transparent 2px),
    radial-gradient(circle at 10% 50%, rgba(255, 255, 255, 0.5) 1px, transparent 2px) !important;
  background-size: 100% 100% !important;
  animation: startSparklesTwinkle 2s ease-in-out infinite !important;
  pointer-events: none !important;
  border-radius: inherit !important;
  z-index: 1 !important;
}

/* 💫 脉冲光环 */
.start-processing-pulse {
  position: absolute !important;
  top: -10px !important;
  left: -10px !important;
  right: -10px !important;
  bottom: -10px !important;
  border: 2px solid rgba(147, 197, 253, 0.6) !important;
  border-radius: inherit !important;
  animation: startPulseRing 2.5s ease-in-out infinite !important;
  pointer-events: none !important;
  z-index: 0 !important;
}

/* 📝 标题动画 */
.start-processing-title {
  animation: startTitleGlow 3.5s ease-in-out infinite !important;
  background: linear-gradient(45deg, #1e40af, #3b82f6, #60a5fa, #3b82f6, #1e40af) !important;
  background-size: 300% 100% !important;
  -webkit-background-clip: text !important;
  background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  text-shadow: 0 0 20px rgba(59, 130, 246, 0.3) !important;
}

/* 📄 描述文字动画 */
.start-processing-desc {
  animation: startDescFloat 4s ease-in-out infinite !important;
  transition: all 0.3s ease !important;
}

.start-processing-card:hover .start-processing-desc {
  color: #1e40af !important;
  text-shadow: 0 1px 3px rgba(59, 130, 246, 0.2) !important;
}

/* 🎆 魔法粒子效果 */
.start-processing-particles {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background:
    radial-gradient(circle at 15% 25%, rgba(147, 197, 253, 0.4) 1px, transparent 2px),
    radial-gradient(circle at 85% 35%, rgba(59, 130, 246, 0.3) 1px, transparent 2px),
    radial-gradient(circle at 25% 75%, rgba(147, 197, 253, 0.4) 1px, transparent 2px),
    radial-gradient(circle at 75% 85%, rgba(59, 130, 246, 0.3) 1px, transparent 2px),
    radial-gradient(circle at 50% 15%, rgba(255, 255, 255, 0.6) 0.5px, transparent 1px),
    radial-gradient(circle at 90% 60%, rgba(255, 255, 255, 0.5) 0.5px, transparent 1px) !important;
  background-size: 80px 80px, 60px 60px, 70px 70px, 90px 90px, 40px 40px, 50px 50px !important;
  animation: startParticlesDrift 8s linear infinite !important;
  pointer-events: none !important;
  opacity: 0 !important;
  z-index: -1 !important;
}

.start-processing-card:hover .start-processing-particles {
  opacity: 1 !important;
  animation: startParticlesDrift 4s linear infinite, startParticlesFadeIn 0.6s ease-out !important;
}

/* 🌟 星光形状发光动画 */
@keyframes starShapeGlow {

  0%,
  100% {
    opacity: 0.9;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
    filter: blur(0.5px) brightness(1.4);
  }

  33% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1) rotate(120deg);
    filter: blur(0.3px) brightness(1.6);
  }

  66% {
    opacity: 0.95;
    transform: translate(-50%, -50%) scale(1.05) rotate(240deg);
    filter: blur(0.4px) brightness(1.5);
  }
}

/* 💫 星光光晕脉冲动画 */
@keyframes starGlowPulse {

  0%,
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }

  50% {
    opacity: 0.9;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

/* 🌟 星光层基础样式 - 超高优先级 */
html body .batch-processor-layout .btn-primary .starlight-layer,
html body .batch-processor-layout button.btn-primary .starlight-layer,
.batch-processor-layout .btn-primary .starlight-layer,
.batch-processor-layout button.btn-primary .starlight-layer,
.btn-primary .starlight-layer,
button.btn-primary .starlight-layer {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 1) 1px, rgba(255, 248, 220, 0.9) 3px, transparent 6px),
    radial-gradient(circle at 70% 20%, rgba(255, 248, 220, 1) 1px, rgba(255, 255, 255, 0.8) 3px, transparent 6px),
    radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 1) 1px, rgba(255, 248, 220, 0.7) 3px, transparent 6px),
    radial-gradient(circle at 30% 80%, rgba(255, 248, 220, 1) 1px, rgba(255, 255, 255, 0.6) 3px, transparent 6px),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.4) 0.5px, transparent 2px) !important;
  background-size: 100% 100% !important;
  animation: starlightTwinkle 2.5s ease-in-out infinite !important;
  pointer-events: none !important;
  z-index: 1 !important;
  border-radius: inherit !important;
  opacity: 1 !important;
  display: block !important;
    visibility: visible !important;
}

/* 星光闪烁动画 */
@keyframes starlightTwinkle {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.01);
  }
}

/* ✨ 高光扫过层样式 - 超高优先级 */
html body .batch-processor-layout .btn-primary .highlight-sweep,
html body .batch-processor-layout button.btn-primary .highlight-sweep,
.batch-processor-layout .btn-primary .highlight-sweep,
.batch-processor-layout button.btn-primary .highlight-sweep,
.btn-primary .highlight-sweep,
button.btn-primary .highlight-sweep {
  position: absolute !important;
  top: -50% !important;
  left: -50% !important;
  width: 200% !important;
  height: 200% !important;
  background: linear-gradient(45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.5) 50%,
      transparent 70%) !important;
  transform: translateX(-100%) translateY(-100%) rotate(45deg) !important;
  transition: transform 0.8s ease !important;
  pointer-events: none !important;
  z-index: 2 !important;
  opacity: 1 !important;
  display: block !important;
    visibility: visible !important;
}

/* 🎯 悬停时的高光扫过 - 超高优先级 */
html body .batch-processor-layout .btn-primary:hover .highlight-sweep,
html body .batch-processor-layout button.btn-primary:hover .highlight-sweep,
.batch-processor-layout .btn-primary:hover .highlight-sweep,
.batch-processor-layout button.btn-primary:hover .highlight-sweep,
.btn-primary:hover .highlight-sweep,
button.btn-primary:hover .highlight-sweep {
  transform: translateX(100%) translateY(100%) rotate(45deg) !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🚀 开始批量处理 - 动画关键帧
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 🌊 卡片浮动动画 */
@keyframes startCardFloat {

  0%,
  100% {
    transform: translateY(0) scale(1);
    box-shadow:
      0 8px 25px rgba(59, 130, 246, 0.15),
      0 3px 10px rgba(0, 0, 0, 0.1);
  }

  50% {
    transform: translateY(-3px) scale(1.01);
    box-shadow:
      0 12px 35px rgba(59, 130, 246, 0.2),
      0 5px 15px rgba(0, 0, 0, 0.12);
  }
}

/* 🎭 卡片悬停动画 */
@keyframes startCardHover {
  0% {
    transform: translateY(0) scale(1);
  }

  50% {
    transform: translateY(-4px) scale(1.03);
  }

  100% {
    transform: translateY(-8px) scale(1.05);
  }
}

/* 💫 图标脉冲动画 */
@keyframes startIconPulse {

  0%,
  100% {
    transform: scale(1);
    box-shadow:
      0 8px 32px rgba(59, 130, 246, 0.4),
      0 0 40px rgba(147, 197, 253, 0.3),
      inset 0 2px 0 rgba(255, 255, 255, 0.2);
  }

  50% {
    transform: scale(1.05);
    box-shadow:
      0 12px 40px rgba(59, 130, 246, 0.5),
      0 0 50px rgba(147, 197, 253, 0.4),
      inset 0 2px 0 rgba(255, 255, 255, 0.3);
  }
}

/* ✨ 星光闪烁动画 */
@keyframes startSparklesTwinkle {

  0%,
  100% {
    opacity: 0.8;
    transform: scale(1) rotate(0deg);
  }

  33% {
    opacity: 1;
    transform: scale(1.1) rotate(120deg);
  }

  66% {
    opacity: 0.9;
    transform: scale(1.05) rotate(240deg);
  }
}

/* 💫 脉冲光环动画 */
@keyframes startPulseRing {
  0% {
    opacity: 0.6;
    transform: scale(1);
    border-color: rgba(147, 197, 253, 0.6);
  }

  50% {
    opacity: 1;
    transform: scale(1.2);
    border-color: rgba(59, 130, 246, 0.8);
  }

  100% {
    opacity: 0.6;
    transform: scale(1);
    border-color: rgba(147, 197, 253, 0.6);
  }
}

/* 📝 标题发光动画 */
@keyframes startTitleGlow {

  0%,
  100% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}

/* 📄 描述浮动动画 */
@keyframes startDescFloat {

  0%,
  100% {
    transform: translateY(0);
    opacity: 0.8;
  }

  50% {
    transform: translateY(-1px);
    opacity: 1;
  }
}

/* 🎆 粒子漂移动画 */
@keyframes startParticlesDrift {
  0% {
    background-position: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
  }

  100% {
    background-position: 100% 100%, -100% -100%, 100% -100%, -100% 100%, 50% -50%, -50% 50%;
  }
}

/* 🌟 粒子淡入动画 */
@keyframes startParticlesFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}
/* 确保按钮内容在星光层之上 */
.batch-processor-layout .btn-primary .button-content {
  position: relative !important;
  z-index: 3 !important;
}
/* ═══════════════════════════════════════════════════════════════════════════
 * 🌟 TAILWIND COMPATIBLE STARLIGHT - Tailwind兼容的星光效果
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 星光闪烁动画 - 全局定义 */
@keyframes starlightTwinkle {

  0%,
  100% {
    opacity: 0.7;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.01);
  }
}

/* 确保动画在Tailwind环境下正常工作 */
.starlight-animation {
  animation: starlightTwinkle 2.5s ease-in-out infinite;
}

/* ===== 新的金色按钮样式 - 迁移自内联样式，避免使用!important ===== */

/* 🔥 超强力白色文字 - 覆盖所有Tailwind样式 */
html body div.batch-processor-layout button.force-white-text,
html body div.batch-processor-layout button.force-white-text *,
html body div.batch-processor-layout button.force-white-text span,
html body div.batch-processor-layout button.force-white-text div,
html body .batch-processor-layout button.relative.w-full.overflow-hidden,
html body .batch-processor-layout button.relative.w-full.overflow-hidden *,
html body .batch-processor-layout button.relative.w-full.overflow-hidden span,
html body .batch-processor-layout button.relative.w-full.overflow-hidden div,
.force-white-text,
.force-white-text * {
  color: rgb(255, 255, 255) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* 确保悬停状态也是白色 - 超强力 */
html body div.batch-processor-layout button.force-white-text:hover,
html body div.batch-processor-layout button.force-white-text:hover *,
html body div.batch-processor-layout button.force-white-text:hover span,
html body div.batch-processor-layout button.force-white-text:hover div,
html body .batch-processor-layout button.relative.w-full.overflow-hidden:hover,
html body .batch-processor-layout button.relative.w-full.overflow-hidden:hover *,
html body .batch-processor-layout button.relative.w-full.overflow-hidden:hover span,
html body .batch-processor-layout button.relative.w-full.overflow-hidden:hover div,
.force-white-text:hover,
.force-white-text:hover * {
  color: rgb(255, 255, 255) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6), 0 1px 2px rgba(0, 0, 0, 0.4) !important;
}

/* 🎯 金色按钮完美居中布局 */
.batch-processor-layout button.relative.w-full.overflow-hidden.bg-gradient-to-br {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* 按钮内容容器 */
.batch-processor-layout button.relative.w-full .relative.z-10 {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
}

/* 按钮内容主体 */
.batch-processor-layout button.relative.w-full .button-icon,
.batch-processor-layout button.relative.w-full .button-text {
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
}

/* 进度徽章样式 */
.batch-processor-layout button.relative.w-full .progress-badge {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border-radius: 4px !important;
  padding: 2px 8px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
}

/* 星光效果层 - 增强版，匹配当前内联样式 */
.batch-processor-layout button.relative.w-full .absolute.inset-0.pointer-events-none {
  background:
    radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 1) 2px, rgba(255, 248, 220, 1) 4px, transparent 8px),
    radial-gradient(circle at 75% 15%, rgba(255, 248, 220, 1) 2px, rgba(255, 255, 255, 1) 4px, transparent 8px),
    radial-gradient(circle at 85% 75%, rgba(255, 255, 255, 1) 2px, rgba(255, 248, 220, 1) 4px, transparent 8px),
    radial-gradient(circle at 25% 85%, rgba(255, 248, 220, 1) 2px, rgba(255, 255, 255, 1) 4px, transparent 8px),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.8) 1px, transparent 3px),
    radial-gradient(circle at 35% 45%, rgba(255, 255, 255, 0.6) 1px, transparent 3px),
    radial-gradient(circle at 65% 55%, rgba(255, 248, 220, 0.8) 1px, transparent 3px),
    radial-gradient(circle at 45% 65%, rgba(255, 255, 255, 0.5) 1px, transparent 3px);
  background-size: 100% 100%;
  animation: enhancedStarlightTwinkle 2s ease-in-out infinite;
  z-index: 1;
  border-radius: inherit;
}

/* 高光扫过效果已移至btn-primary-gold-simple的伪元素实现 */

/* 额外的闪光效果层 - 第二个星光层 */
.batch-processor-layout button.relative.w-full .absolute.inset-0.pointer-events-none:nth-child(3) {
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  animation: sparkleGlow 3s ease-in-out infinite;
  z-index: 3;
  border-radius: inherit;
}

/* 箭头样式 */
.batch-processor-layout button.relative.w-full .button-text span:last-child {
  opacity: 0.75;
}

/* 悬停动画已移至btn-primary-gold-simple的伪元素实现 */

/* 闪光效果动画 */
@keyframes sparkleGlow {

  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 增强悬停效果 */
button.relative.w-full:hover {
  transform: translateY(-2px) scale(1.02) !important;
  box-shadow: 0 12px 25px rgba(251, 191, 36, 0.4), 0 6px 15px rgba(253, 230, 138, 0.3) !important;
}
/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 READY STATE STYLES - 准备就绪状态样式
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 准备就绪容器 - 稍微偏上显示 */
.ready-state-container {
  box-shadow: none;
  background: linear-gradient(135deg,
      rgba(248, 250, 252, 0.8) 0%,
      rgba(241, 245, 249, 0.9) 25%,
      rgba(226, 232, 240, 0.8) 50%,
      rgba(241, 245, 249, 0.9) 75%,
      rgba(248, 250, 252, 0.8) 100%);
  border: none;
  border-radius: 0;
  animation: readyStateFloat 6s ease-in-out infinite;
  width: 100%;
  height: 100%;
  min-height: 100%;
  align-items: flex-start;
  padding-top: 15vh;
  position: relative;
  z-index: 1;
}

.ready-state-content {
  box-shadow: none;
  background: transparent;
  animation: readyContentPulse 4s ease-in-out infinite;
  position: relative;
  z-index: 2;
}

/* 动画效果 */
@keyframes readyStateFloat {

  0%,
  100% {
    background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.8) 0%,
        rgba(241, 245, 249, 0.9) 25%,
        rgba(226, 232, 240, 0.8) 50%,
        rgba(241, 245, 249, 0.9) 75%,
        rgba(248, 250, 252, 0.8) 100%);
  }

  50% {
    background: linear-gradient(135deg,
        rgba(241, 245, 249, 0.9) 0%,
        rgba(226, 232, 240, 0.8) 25%,
        rgba(219, 234, 254, 0.7) 50%,
        rgba(226, 232, 240, 0.8) 75%,
        rgba(241, 245, 249, 0.9) 100%);
  }
}

@keyframes readyContentPulse {

  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }

  50% {
    transform: translateY(-2px) scale(1.005);
    opacity: 0.98;
  }
}

/* 图标增强 */
.ready-state-content .ready-icon-animated {
  animation: readyIconFloat 3s ease-in-out infinite;
  box-shadow: none;
  transform: scale(1.2);
}

@keyframes readyIconFloat {

  0%,
  100% {
    transform: scale(1.2) translateY(0) rotate(0deg);
  }

  33% {
    transform: scale(1.25) translateY(-3px) rotate(1deg);
  }

  66% {
    transform: scale(1.15) translateY(-1px) rotate(-1deg);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ready-state-container {
    padding-top: 10vh;
  }
}

@media (max-width: 480px) {
  .ready-state-container {
    padding-top: 8vh;
  }
}

/* 悬停效果 */
.ready-state-container:hover {
  background: linear-gradient(135deg,
      rgba(241, 245, 249, 0.9) 0%,
      rgba(226, 232, 240, 0.95) 25%,
      rgba(219, 234, 254, 0.8) 50%,
      rgba(226, 232, 240, 0.95) 75%,
      rgba(241, 245, 249, 0.9) 100%);
  box-shadow: none;
}

.ready-state-content:hover .ready-icon-animated {
  animation-duration: 2s;
  transform: scale(1.3);
}

/* 强制移除阴影 */
.ready-state-container,
.ready-state-container *,
.ready-state-content,
.ready-state-content * {
  box-shadow: none;
}