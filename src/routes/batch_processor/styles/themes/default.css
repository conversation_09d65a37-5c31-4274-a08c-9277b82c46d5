/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎨 UNIFIED DEFAULT THEME - 统一默认主题
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 合并了unified-theme.css + modules/drawers/themes.css + console-harmony-optimization.css
 * 提供完整的默认主题解决方案
 *
 * @version 4.0 - 整合版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces unified-theme.css + modules/drawers/themes.css + console-harmony-optimization.css
 */

:root {
  /* ═══════════════════════════════════════════════════════════════════════════
   * 🌈 COLOR PALETTE - 色彩调色板
   * ═══════════════════════════════════════════════════════════════════════════ */

  /* 💼 主色调系统 - Professional Blue Gradient Palette */
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #87ceeb;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;

  /* 💙 辅助蓝色系统 - Secondary Blue Palette */
  --color-blue-50: #eff6ff;
  --color-blue-100: #dbeafe;
  --color-blue-200: #bfdbfe;
  --color-blue-300: #93c5fd;
  --color-blue-400: #60a5fa;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: rgb(35, 146, 239);
  --color-blue-800: #1e40af;
  --color-blue-900: #1e3a8a;
  --color-blue-rgb: 59, 130, 246;

  /* 🟡 浅黄色系统 - Light Yellow Palette */
  --color-yellow-50: #fefce8;
  --color-yellow-100: #fef3c7;
  --color-yellow-200: #fde68a;
  --color-yellow-300: #fcd34d;
  --color-yellow-400: #fbbf24;
  --color-yellow-500: #f59e0b;
  --color-yellow-600: #d97706;
  --color-yellow-700: #b45309;
  --color-yellow-800: #92400e;
  --color-yellow-900: #78350f;

  /* ⚪ 中性色系统 - Business Gray Palette */
  --color-white: #ffffff;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* 🥇 金色系统 - Gold Accent Palette */
  --color-gold-50: #fffbeb;
  --color-gold-100: #fef3c7;
  --color-gold-200: #fde68a;
  --color-gold-300: #fcd34d;
  --color-gold-400: #fbbf24;
  --color-gold-500: #f59e0b;
  --color-gold-600: #d97706;
  --color-gold-700: #b45309;
  --color-gold-800: #92400e;
  --color-gold-900: #78350f;

  /* 🎯 状态色系统 - Status Colors */
  --color-success: #87ceeb;
  --color-success-light: #0ea4e981;
  --color-success-highlight: linear-gradient(135deg, #10b0dc 0%, #1c9ded 100%);
  
  --color-warning: #f59e0b;
  --color-warning-light: #fef3c7;
  
  --color-error: #e37a17;
  --color-error-light: #fee2e2;
  --color-error-highlight: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  
  --color-info: #87ceeb;
  --color-info-light: #e0f2fe;
  
  --color-processing: #6b7280;
  --color-processing-light: #f3f4f6;

  /* 🎨 主题背景变量 */
  --color-bg-primary: var(--color-gray-50);
  --color-bg-secondary: var(--color-white);
  --color-bg-tertiary: var(--color-gray-100);

  /* ═══════════════════════════════════════════════════════════════════════════
   * 📝 TYPOGRAPHY SYSTEM - 字体系统
   * ═══════════════════════════════════════════════════════════════════════════ */
  
  /* 语义化文本色彩 - 增强对比度的蓝色文字变量 */
  --text-primary: var(--text-blue-950);    /* 主要文字 - 最深蓝色 */
  --text-secondary: var(--text-blue-800);  /* 次要文字 - 深蓝色 */
  --text-tertiary: var(--text-blue-700);   /* 三级文字 - 中深蓝色 */
  --text-disabled: var(--text-blue-500);   /* 禁用文字 - 中蓝色 */
  --text-inverse: var(--text-white);       /* 反色文字 - 白色 */
  
  /* 字体规格 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* ═══════════════════════════════════════════════════════════════════════════
   * 📐 SPACING SYSTEM - 间距系统
   * ═══════════════════════════════════════════════════════════════════════════ */
  
  /* 8px 网格系统 */
  --space-0: 0;
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  
  /* 语义化间距 */
  --container-padding: var(--space-4);
  --content-gap: var(--space-6);
  --section-gap: var(--space-8);
  --card-gap: var(--space-4);
  --paragraph-margin: var(--space-4);

  /* ═══════════════════════════════════════════════════════════════════════════
   * 🎭 SHADOWS & EFFECTS - 阴影与特效
   * ═══════════════════════════════════════════════════════════════════════════ */
  
  /* 阴影系统 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* 特殊阴影 */
  --shadow-colored: 0 4px 6px -1px rgba(var(--color-blue-rgb), 0.1);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* 控制台和谐阴影 */
  --shadow-console-soft: 0 2px 8px rgba(59, 130, 246, 0.06), 0 1px 4px rgba(59, 130, 246, 0.04);
  --shadow-console-medium: 0 4px 12px rgba(59, 130, 246, 0.08), 0 2px 6px rgba(59, 130, 246, 0.06);
  --shadow-console-elevated: 0 8px 24px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(59, 130, 246, 0.08);

  /* ═══════════════════════════════════════════════════════════════════════════
   * 🔄 BORDER SYSTEM - 边框系统
   * ═══════════════════════════════════════════════════════════════════════════ */
  
  /* 边框宽度 */
  --border-width: 1px;
  --border-width-2: 2px;
  --border-width-4: 4px;
  
  /* 边框颜色 */
  --border-color: var(--color-gray-200);
  --border-color-light: var(--color-gray-100);
  --border-color-medium: var(--color-gray-300);
  --border-color-focus: var(--color-blue-400);
  
  /* 圆角 */
  --border-radius-sm: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --border-radius-2xl: 1.5rem;
  --border-radius-full: 50%;

  /* ═══════════════════════════════════════════════════════════════════════════
   * ⚡ ANIMATION SYSTEM - 动画系统
   * ═══════════════════════════════════════════════════════════════════════════ */
  
  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  
  /* 缓动函数 */
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  
  /* 常用过渡 */
  --transition-colors: color var(--duration-normal) var(--ease-in-out), 
                       background-color var(--duration-normal) var(--ease-in-out), 
                       border-color var(--duration-normal) var(--ease-in-out);
  --transition-shadow: box-shadow var(--duration-normal) var(--ease-in-out);
  --transition-transform: transform var(--duration-normal) var(--ease-in-out);

  /* ═══════════════════════════════════════════════════════════════════════════
   * 📏 LAYOUT DIMENSIONS - 布局尺寸
   * ═══════════════════════════════════════════════════════════════════════════ */
  
  /* 批处理器特定尺寸 */
  --batch-processor-gap: var(--space-6);
  --batch-processor-padding: var(--space-4);
  --batch-processor-header-height: 4rem;
  
  /* 组件尺寸 */
  --button-height-sm: 2rem;
  --button-height-md: 2.5rem;
  --button-height-lg: 3rem;
  
  --input-height-sm: 2rem;
  --input-height-md: 2.5rem;
  --input-height-lg: 3rem;
  
  /* 容器尺寸 */
  --container-xs: 20rem;
  --container-sm: 24rem;
  --container-md: 28rem;
  --container-lg: 32rem;
  --container-xl: 36rem;
  --container-2xl: 42rem;
  --container-3xl: 48rem;

  /* Z-index系统 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* ═══════════════════════════════════════════════════════════════════════════
   * 🎨 CONSOLE HARMONY GRADIENTS - 控制台和谐渐变
   * ═══════════════════════════════════════════════════════════════════════════ */
  
  /* 和谐渐变调色板 */
  --gradient-console-primary: linear-gradient(135deg, 
    rgba(248, 250, 252, 0.98) 0%, 
    rgba(241, 245, 249, 0.95) 50%,
    rgba(236, 242, 248, 0.92) 100%);
  
  --gradient-console-elevated: linear-gradient(135deg,
    rgba(252, 252, 253, 0.98) 0%,
    rgba(247, 250, 252, 0.96) 50%,
    rgba(243, 248, 252, 0.94) 100%);
  
  --gradient-console-active: linear-gradient(135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(250, 252, 254, 0.96) 50%,
    rgba(245, 249, 253, 0.94) 100%);
  
  /* 进度条和谐色彩 */
  --progress-success: linear-gradient(135deg, 
    rgba(16, 185, 129, 0.9) 0%, 
    rgba(5, 150, 105, 0.85) 50%,
    rgba(4, 120, 87, 0.8) 100%);
  
  --progress-processing: linear-gradient(135deg,
    rgba(245, 158, 11, 0.9) 0%,
    rgba(217, 119, 6, 0.85) 50%,
    rgba(180, 83, 9, 0.8) 100%);
  
  --progress-error: linear-gradient(135deg,
    rgba(239, 68, 68, 0.9) 0%,
    rgba(220, 38, 38, 0.85) 50%,
    rgba(185, 28, 28, 0.8) 100%);
  
  --progress-pending: linear-gradient(135deg,
    rgba(107, 114, 128, 0.9) 0%,
    rgba(75, 85, 99, 0.85) 50%,
    rgba(55, 65, 81, 0.8) 100%);
  
  /* 标签文字和谐色彩 - 蓝色主题 */
  --text-primary-harmony: rgba(7, 89, 133, 0.9);   /* 深蓝色 blue-800 */
  --text-secondary-harmony: rgba(2, 132, 199, 0.8); /* 中蓝色 blue-600 */
  --text-tertiary-harmony: rgba(14, 165, 233, 0.7); /* 浅蓝色 blue-500 */

  /* 版本标识 */
  --theme-version: '4.0-unified';
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🌐 GLOBAL STYLES - 全局样式
 * ═══════════════════════════════════════════════════════════════════════════ */

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: var(--font-family);
  line-height: var(--line-height-normal);
  color: var(--text-blue-950);
  background-color: var(--color-primary-50);
  font-size: var(--font-size-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  min-height: 100vh;
  background: var(--color-primary-50);
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-size: cover;
  color: var(--text-blue-950);
}

/* 确保所有容器都有适当的背景 */
#root,
[data-reactroot],
.batch-processor,
.batch-processor-main {
  background: var(--color-gray-50);
  min-height: 100vh;
}

/* 页面根容器强化 */
#root {
  background: var(--color-gray-50);
  width: 100%;
  min-height: 100vh;
  position: relative;
}

/* 页面级容器背景 */
.page-container,
.main-content,
.app-container {
  background: var(--color-gray-50);
}

/* 面板和卡片背景 */
.panel,
.card,
.drawer,
.modal {
  background: var(--color-white);
}

/* 输入区域背景 */
.input-panel,
.query-input-panel,
.settings-panel {
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
}

/* 结果区域背景 */
.results-panel,
.results-grid,
.result-card {
  background: var(--color-white);
}

/* 侧边栏背景 */
.sidebar,
.left-sidebar,
.right-sidebar {
  background: var(--color-gray-50);
}

/* 确保抽屉有正确的背景 */
.ant-drawer-content,
.ant-drawer-body {
  background: var(--color-white) !important;
  position: relative;
  z-index: var(--z-modal);
}

/* Semi UI 组件背景修复 */
.semi-card,
.semi-panel,
.semi-input,
.semi-textarea {
  background: var(--color-white);
  position: relative;
}

/* ArcoDesign 组件背景修复 */
.arco-card,
.arco-panel,
.arco-input,
.arco-textarea {
  background: var(--color-white);
  position: relative;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔧 LAYOUT SYSTEM - 布局系统
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 主布局容器层级管理 */
.batch-processor-layout {
  position: relative;
  z-index: 0;
  isolation: isolate;
  contain: layout style;
  display: grid !important;
  grid-template-columns: 380px 1fr 350px !important;
  gap: 16px !important;
  height: 100vh !important;
  overflow: hidden !important;
  padding: 16px !important;
  background: var(--color-bg-primary) !important;
}

/* 侧边栏层级 */
.layout-sidebar {
  position: relative;
  z-index: 1;
  isolation: isolate;
  display: flex !important;
  flex-direction: column !important;
  overflow-y: auto !important;
  height: 100% !important;
  max-height: calc(100vh - 32px) !important;
}

/* 主内容区域层级 */
.layout-main {
  position: relative;
  z-index: 1;
  isolation: isolate;
  display: flex !important;
  flex-direction: column !important;
  overflow-y: auto !important;
  height: 100% !important;
  max-height: calc(100vh - 32px) !important;
}

/* 右侧控制台层级 */
.layout-console {
  position: relative;
  z-index: 1;
  isolation: isolate;
  display: flex !important;
  flex-direction: column !important;
  overflow-y: auto !important;
  height: 100% !important;
  max-height: calc(100vh - 32px) !important;
  background: var(--gradient-console-primary) !important;
  border: 1px solid rgba(59, 130, 246, 0.06) !important;
  box-shadow: var(--shadow-console-soft) !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 控制台悬停状态 */
.layout-console:hover {
  background: var(--gradient-console-elevated) !important;
  box-shadow: var(--shadow-console-medium) !important;
  transform: translateY(-1px) !important;
  border-color: rgba(59, 130, 246, 0.08) !important;
}

/* 控制台激活状态 */
.layout-console:focus-within {
  background: var(--gradient-console-active) !important;
  box-shadow: var(--shadow-console-elevated) !important;
  border-color: rgba(59, 130, 246, 0.12) !important;
}

/* 确保侧边栏内容区域正确滚动 */
.layout-sidebar .flex.flex-col.h-full {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  min-height: 0 !important;
}

/* 确保查询输入面板和按钮区域正确布局 */
.layout-sidebar .flex-grow.flex.flex-col.min-h-0 {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  min-height: 0 !important;
}

/* 确保金色按钮区域可见且不被截断 */
.layout-sidebar .mt-4.flex-shrink-0.space-y-4 {
  flex-shrink: 0 !important;
  margin-top: 16px !important;
  margin-bottom: 16px !important;
  padding-bottom: 16px !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 DRAWER THEMES - 抽屉主题系统
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 设置抽屉 - 蓝色科技主题 */
.settings-drawer .enhanced-drawer-container {
  background: linear-gradient(135deg,
    rgba(30, 58, 138, 0.08) 0%,
    rgba(59, 130, 246, 0.05) 25%,
    rgba(255, 255, 255, 0.98) 45%,
    rgba(147, 197, 253, 0.04) 75%,
    rgba(37, 99, 235, 0.06) 100%
  ) !important;
  
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(147, 197, 253, 0.08) 0%, transparent 50%),
    linear-gradient(45deg, rgba(59, 130, 246, 0.08) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(59, 130, 246, 0.08) 25%, transparent 25%) !important;
  background-size: 
    200px 200px,
    150px 150px,
    20px 20px,
    20px 20px !important;
  background-position: 
    0 0,
    100px 100px,
    0 0,
    0 0 !important;
  
  backdrop-filter: blur(25px) !important;
  border: none !important;
  box-shadow:
    -18px 0 60px rgba(30, 58, 138, 0.1),
    -10px 0 30px rgba(59, 130, 246, 0.06),
    -4px 0 12px rgba(147, 197, 253, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.4) !important;
}

.settings-drawer .enhanced-drawer-content {
  background: linear-gradient(135deg,
    rgba(30, 58, 138, 0.03) 0%,
    rgba(59, 130, 246, 0.02) 25%,
    rgba(255, 255, 255, 0.96) 45%,
    rgba(147, 197, 253, 0.02) 75%,
    rgba(37, 99, 235, 0.04) 100%
  ) !important;
  padding: 32px 40px 28px 40px !important;
}

.settings-drawer .enhanced-drawer-header {
  background: linear-gradient(135deg,
    rgba(30, 58, 138, 0.06) 0%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(147, 197, 253, 0.05) 100%
  ) !important;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1) !important;
}

/* 历史抽屉 - 琥珀金色主题 */
.history-drawer .enhanced-drawer-container {
  background: linear-gradient(135deg,
    rgba(146, 64, 14, 0.08) 0%,
    rgba(217, 119, 6, 0.05) 25%,
    rgba(255, 255, 255, 0.98) 45%,
    rgba(252, 211, 77, 0.04) 75%,
    rgba(180, 83, 9, 0.06) 100%
  ) !important;
  
  background-image: 
    radial-gradient(circle at 30% 20%, rgba(245, 158, 11, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 70% 80%, rgba(251, 191, 36, 0.06) 0%, transparent 60%),
    linear-gradient(45deg, rgba(59, 130, 246, 0.08) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(59, 130, 246, 0.08) 25%, transparent 25%) !important;
  background-size: 
    180px 180px,
    120px 120px,
    40px 40px,
    40px 40px !important;
  background-position: 
    0 0,
    90px 90px,
    0 0,
    20px 20px !important;
  
  backdrop-filter: blur(25px) !important;
  border: none !important;
  box-shadow:
    -18px 0 60px rgba(146, 64, 14, 0.1),
    -10px 0 30px rgba(217, 119, 6, 0.06),
    -4px 0 12px rgba(252, 211, 77, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.4) !important;
}

.history-drawer .enhanced-drawer-content {
  background: linear-gradient(135deg,
    rgba(146, 64, 14, 0.03) 0%,
    rgba(217, 119, 6, 0.02) 25%,
    rgba(255, 255, 255, 0.96) 45%,
    rgba(252, 211, 77, 0.02) 75%,
    rgba(180, 83, 9, 0.04) 100%
  ) !important;
  padding: 32px 40px 28px 40px !important;
}

.history-drawer .enhanced-drawer-header {
  background: linear-gradient(135deg,
    rgba(146, 64, 14, 0.06) 0%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(252, 211, 77, 0.05) 100%
  ) !important;
  border-bottom: 1px solid rgba(217, 119, 6, 0.1) !important;
}

/* 提示词抽屉 - 紫色魔法主题 */
.prompt-drawer .enhanced-drawer-container {
  background: linear-gradient(135deg,
    rgba(88, 28, 135, 0.08) 0%,
    rgba(139, 92, 246, 0.05) 25%,
    rgba(255, 255, 255, 0.98) 45%,
    rgba(196, 181, 253, 0.04) 75%,
    rgba(67, 56, 202, 0.06) 100%
  ) !important;
  
  background-image: 
    radial-gradient(circle at 40% 30%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 60% 70%, rgba(167, 139, 250, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 20% 80%, rgba(196, 181, 253, 0.04) 0%, transparent 40%),
    linear-gradient(60deg, rgba(59, 130, 246, 0.08) 50%, transparent 50%),
    linear-gradient(-60deg, rgba(59, 130, 246, 0.08) 50%, transparent 50%) !important;
  background-size: 
    160px 160px,
    100px 100px,
    80px 80px,
    30px 30px,
    30px 30px !important;
  background-position: 
    0 0,
    80px 80px,
    40px 40px,
    0 0,
    15px 15px !important;
  
  backdrop-filter: blur(25px) !important;
  border: none !important;
  box-shadow:
    -18px 0 60px rgba(88, 28, 135, 0.1),
    -10px 0 30px rgba(139, 92, 246, 0.06),
    -4px 0 12px rgba(196, 181, 253, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.4) !important;
}

.prompt-drawer .enhanced-drawer-content {
  background: linear-gradient(135deg,
    rgba(88, 28, 135, 0.03) 0%,
    rgba(139, 92, 246, 0.02) 25%,
    rgba(255, 255, 255, 0.96) 45%,
    rgba(196, 181, 253, 0.02) 75%,
    rgba(67, 56, 202, 0.04) 100%
  ) !important;
}

.prompt-drawer .enhanced-drawer-header {
  background: linear-gradient(135deg,
    rgba(88, 28, 135, 0.06) 0%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(196, 181, 253, 0.05) 100%
  ) !important;
  border-bottom: 1px solid rgba(139, 92, 246, 0.1) !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎭 THEME FORM ELEMENTS - 主题化表单元素
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 设置抽屉表单元素 */
.settings-drawer .form-input-modern {
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
}

.settings-drawer .form-input-modern:focus {
  border-color: rgba(59, 130, 246, 0.4) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.settings-drawer .glass-card {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(59, 130, 246, 0.1) !important;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.05) !important;
  padding: 20px 24px !important;
  border-radius: 12px !important;
  margin-bottom: 16px !important;
}

/* 历史抽屉表单元素 */
.history-drawer .form-input-modern {
  border: 1px solid rgba(217, 119, 6, 0.2) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
}

.history-drawer .form-input-modern:focus {
  border-color: rgba(217, 119, 6, 0.4) !important;
  box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.1) !important;
}

.history-drawer .glass-card {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(217, 119, 6, 0.1) !important;
  box-shadow: 0 4px 20px rgba(217, 119, 6, 0.05) !important;
  padding: 20px 24px !important;
  border-radius: 12px !important;
  margin-bottom: 16px !important;
}

/* 提示词抽屉表单元素 */
.prompt-drawer .form-input-modern {
  border: 1px solid rgba(139, 92, 246, 0.2) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
}

.prompt-drawer .form-input-modern:focus {
  border-color: rgba(139, 92, 246, 0.4) !important;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1) !important;
}

.prompt-drawer .glass-card {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(139, 92, 246, 0.1) !important;
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.05) !important;
  padding: 20px 24px !important;
  border-radius: 12px !important;
  margin-bottom: 16px !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔧 LAYOUT FIXES - 布局修复
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 防止动画元素影响布局 */
.animate-rocket-launch,
.animate-button-launch-glow {
  transform-origin: center center;
  will-change: transform, box-shadow;
}

/* 火箭尾焰不影响布局 */
.animate-rocket-flame,
.animate-rocket-flame-2 {
  pointer-events: none;
  transform: translateZ(0);
  clip-path: inset(0 0 0 0);
}

/* 修复可能的层级问题 */
.batch-processor-main {
  isolation: isolate;
}

/* 防止动画元素脱离文档流 */
.relative {
  contain: layout style;
}

/* 结果面板层级 */
.results-panel,
.result-card {
  position: relative;
  z-index: 2;
  isolation: isolate;
}

/* 动画元素不影响布局 */
.animate-rocket-launch,
.animate-button-launch-glow,
.animate-rocket-flame,
.animate-rocket-flame-2 {
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* 绝对定位的动画元素 */
.absolute.animate-rocket-flame,
.absolute.animate-rocket-flame-2 {
  z-index: 5;
  pointer-events: none;
}

/* 抽屉和模态框层级 */
.ant-drawer,
.semi-modal,
.arco-modal {
  z-index: var(--z-modal) !important;
}

/* 开发工具浮动按钮 */
.capsule-draggable-Nn4PAI,
.fab-_shO90 {
  z-index: var(--z-tooltip) !important;
  pointer-events: auto;
}

/* 修复可能的flexbox重叠 */
.flex {
  min-width: 0;
  min-height: 0;
}

/* 修复可能的grid重叠 */
.grid {
  min-width: 0;
  min-height: 0;
}

/* 确保内容不溢出容器 */
.overflow-hidden {
  contain: layout;
}

/* 防止内容超出视口 */
.batch-processor {
  max-width: 100vw;
  overflow-x: hidden;
}

/* 确保输入框不被其他元素覆盖 */
input,
textarea,
.semi-input,
.arco-input {
  position: relative;
  z-index: 3;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE DESIGN - 响应式设计
 * ═══════════════════════════════════════════════════════════════════════════ */

@media (max-width: 1536px) {
  .batch-processor-layout {
    grid-template-columns: 360px 1fr 320px !important;
  }
}

@media (max-width: 1366px) {
  .batch-processor-layout {
    grid-template-columns: 340px 1fr 300px !important;
  }
}

@media (max-width: 1200px) {
  .batch-processor-layout {
    grid-template-columns: 320px 1fr 280px !important;
    gap: 12px !important;
    padding: 12px !important;
  }
  
  .layout-sidebar,
  .layout-main,
  .layout-console {
    max-height: calc(100vh - 24px) !important;
  }
}

@media (max-width: 1024px) {
  .batch-processor-layout {
    display: flex !important;
    flex-direction: column !important;
    gap: 12px !important;
    height: 100vh !important;
    overflow: hidden !important;
  }
  
  .layout-sidebar {
    order: 1;
    height: auto !important;
    max-height: 50vh !important;
    min-height: 300px !important;
  }
  
  .layout-main {
    order: 2;
    flex: 1 !important;
    min-height: 0 !important;
  }
  
  .layout-console {
    order: 3;
    height: auto !important;
    max-height: 25vh !important;
    min-height: 200px !important;
  }
}

/* 移动端优化 - 简化动画和纹理 */
@media (max-width: 768px) {
  .settings-drawer .enhanced-drawer-container,
  .history-drawer .enhanced-drawer-container,
  .prompt-drawer .enhanced-drawer-container {
    animation: none;
    background-size:
      100px 100px,
      75px 75px,
      40px 40px,
      15px 15px,
      15px 15px;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ♿ ACCESSIBILITY - 无障碍
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  .settings-drawer .enhanced-drawer-container,
  .history-drawer .enhanced-drawer-container,
  .prompt-drawer .enhanced-drawer-container {
    animation: none;
  }
  
  .animate-rocket-launch,
  .animate-button-launch-glow,
  .animate-rocket-flame,
  .animate-rocket-flame-2 {
    animation: none !important;
    transition: none !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .settings-drawer .glass-card,
  .history-drawer .glass-card,
  .prompt-drawer .glass-card {
    border-width: 2px !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .settings-drawer .form-input-modern,
  .history-drawer .form-input-modern,
  .prompt-drawer .form-input-modern {
    min-height: 44px !important;
    padding: 12px 16px !important;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ✨ ICON STARLIGHT EFFECT - 图标星光效果 (最高优先级)
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 图标星光效果 - 优化版本，确保最高优先级 */
body .batch-processor-layout .icon-starlight,
.batch-processor-layout .icon-starlight,
.icon-starlight {
  position: relative !important;
  overflow: visible !important;
  background: linear-gradient(135deg,
      #1e3a8a 0%,
      #3b82f6 25%,
      #60a5fa 75%,
      #93c5fd 100%) !important;
  border-radius: 50% !important;
  box-shadow:
    0 0 15px rgba(59, 130, 246, 0.2),
    0 0 30px rgba(147, 197, 253, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.25) !important;
  animation: ultraGentleIconBreathe 4s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
  transition: all 0.7s cubic-bezier(0.4, 0, 0.6, 1) !important;
  will-change: transform, box-shadow !important;
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
}

/* 星光点效果 */
body .batch-processor-layout .icon-starlight::before,
.batch-processor-layout .icon-starlight::before,
.icon-starlight::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.6) 1px, rgba(219, 234, 254, 0.3) 3px, transparent 5px),
    radial-gradient(circle at 70% 20%, rgba(147, 197, 253, 0.5) 1px, rgba(96, 165, 250, 0.25) 3px, transparent 5px),
    radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.5) 1px, rgba(219, 234, 254, 0.25) 3px, transparent 5px) !important;
  background-size: 100% 100% !important;
  animation: ultraGentleStarTwinkle 5s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
  pointer-events: none !important;
  z-index: 1 !important;
  border-radius: 50% !important;
  filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.4)) !important;
}

/* 悬停效果 */
body .batch-processor-layout .icon-starlight:hover,
.batch-processor-layout .icon-starlight:hover,
.icon-starlight:hover {
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.45) !important,
    0 0 35px rgba(255, 255, 255, 0.4) !important,
    0 0 50px rgba(59, 130, 246, 0.3) !important,
    inset 0 2px 0 rgba(255, 255, 255, 0.5) !important;
  animation: ultraGentleIconBreathe 3s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
}

/* Hover时的星光效果增强 */
body .batch-processor-layout .icon-starlight:hover::before,
.batch-processor-layout .icon-starlight:hover::before,
.icon-starlight:hover::before {
  animation: hoverStarTwinkle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.9) 2px, rgba(219, 234, 254, 0.5) 4px, transparent 6px),
    radial-gradient(circle at 70% 20%, rgba(147, 197, 253, 0.8) 2px, rgba(96, 165, 250, 0.4) 4px, transparent 6px),
    radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.8) 2px, rgba(219, 234, 254, 0.4) 4px, transparent 6px) !important;
}

/* 图标内部元素优化 - 添加呼吸动效 */
body .batch-processor-layout .icon-starlight .semi-icon,
body .batch-processor-layout .icon-starlight svg,
body .batch-processor-layout .icon-starlight .icon-rating-main,
.batch-processor-layout .icon-starlight .semi-icon,
.batch-processor-layout .icon-starlight svg,
.batch-processor-layout .icon-starlight .icon-rating-main,
.icon-starlight .semi-icon,
.icon-starlight svg,
.icon-starlight .icon-rating-main {
  transition: all 0.7s cubic-bezier(0.4, 0, 0.6, 1) !important;
  will-change: transform, filter !important;
  position: relative !important;
  z-index: 2 !important;
  animation: iconBreathe 4s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
  transform-origin: center center !important;
  /* 设置图标尺寸 */
  font-size: 8rem !important;
  width: 8rem !important;
  height: 8rem !important;
  display: block !important;
}

/* Hover时图标的增强动效 */
body .batch-processor-layout .icon-starlight:hover .semi-icon,
body .batch-processor-layout .icon-starlight:hover svg,
body .batch-processor-layout .icon-starlight:hover .icon-rating-main,
.batch-processor-layout .icon-starlight:hover .semi-icon,
.batch-processor-layout .icon-starlight:hover svg,
.batch-processor-layout .icon-starlight:hover .icon-rating-main,
.icon-starlight:hover .semi-icon,
.icon-starlight:hover svg,
.icon-starlight:hover .icon-rating-main {
  animation: iconHoverBreathe 2s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
}