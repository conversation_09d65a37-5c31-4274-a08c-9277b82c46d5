# 数据一致性修复测试

## 问题描述
左侧失败数量显示0，右侧失败数量显示20，数据不一致。

## 根本原因
- **左侧数据源**：`results.filter(r => r.status === 'error').length`
- **右侧数据源**：`progress.failed`
- 两个数据源可能在不同时机更新，导致不一致

## 修复方案
统一数据源，让左侧也使用 `progress` 数据：

### 修改前
```typescript
const stats = useMemo(() => {
  const success = results.filter(r => r.status === 'success').length;
  const error = results.filter(r => r.status === 'error').length;
  const processing = results.filter(r => r.status === 'processing').length;
  const pending = results.length - (success + error + processing);
  return { success, error, processing, pending };
}, [results]);
```

### 修改后
```typescript
const stats = useMemo(() => {
  // 优先使用progress数据，确保与右侧状态概览一致
  if (progress) {
    return {
      success: progress.completed,
      error: progress.failed,
      processing: progress.processing,
      pending: progress.pending,
    };
  }
  
  // 降级方案：如果没有progress数据，则从results计算
  const success = results.filter(r => r.status === 'success').length;
  const error = results.filter(r => r.status === 'error').length;
  const processing = results.filter(r => r.status === 'processing').length;
  const pending = results.length - (success + error + processing);
  return { success, error, processing, pending };
}, [results, progress]);
```

## 影响范围
1. ✅ 状态过滤按钮数量显示（成功、失败、处理中）
2. ✅ 复制成功结果按钮的显示条件和数量
3. ✅ 重试失败项按钮（已经使用progress.failed）
4. ⚠️ 打开所有成功结果按钮（保持使用results，因为需要检查playgroundUrl）

## 测试验证
1. 启动批处理任务
2. 观察左右两侧失败数量是否一致
3. 检查状态过滤按钮数量是否正确
4. 验证按钮显示逻辑是否正常

## 预期结果
- 左右两侧数据完全一致
- 所有基于状态统计的功能正常工作
- 数据更新实时同步
