<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动画复用效果测试</title>
    <link rel="stylesheet" href="../styles/foundation/variables.css">
    <link rel="stylesheet" href="../styles/components/buttons.css">
    <link rel="stylesheet" href="../styles/components/panels.css">
    <link rel="stylesheet" href="../styles/utilities/animations.css">
    <link rel="stylesheet" href="../styles/index.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(12px);
        }

        .demo-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 8px;
            color: #1f2937;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-description {
            color: #6b7280;
            margin-bottom: 20px;
            font-size: 0.95rem;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .comparison-item {
            padding: 20px;
            background: rgba(248, 250, 252, 0.8);
            border-radius: 12px;
            border: 1px solid rgba(226, 232, 240, 0.6);
            text-align: center;
        }

        .comparison-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 16px;
            font-size: 1rem;
        }

        /* 模拟icon-container样式 */
        .icon-container {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            font-weight: bold;
            font-size: 24px;
        }

        .icon-container--lg {
            width: 80px;
            height: 80px;
            font-size: 32px;
        }

        /* 开始批量处理的原始动画 */
        .start-processing-icon {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #2563eb 100%);
            position: relative;
            animation: iconPulse 3s ease-in-out infinite;
            box-shadow:
                0 8px 32px rgba(59, 130, 246, 0.4),
                0 0 40px rgba(147, 197, 253, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.2);
            will-change: transform, box-shadow;
        }

        /* 准备就绪的复用动画 */
        .ready-icon-animated {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            position: relative;
            animation: iconPulse 3s ease-in-out infinite;
            will-change: transform, box-shadow;
        }

        /* 星光特效层 */
        .start-processing-sparkles {
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background:
                radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.9) 1px, transparent 3px),
                radial-gradient(circle at 80% 30%, rgba(255, 255, 255, 0.7) 1px, transparent 2px),
                radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.8) 1px, transparent 3px),
                radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.6) 1px, transparent 2px),
                radial-gradient(circle at 50% 10%, rgba(255, 255, 255, 0.5) 1px, transparent 2px),
                radial-gradient(circle at 10% 50%, rgba(255, 255, 255, 0.5) 1px, transparent 2px);
            background-size: 100% 100%;
            animation: sparklesTwinkle 2s ease-in-out infinite;
            pointer-events: none;
            border-radius: inherit;
            z-index: 1;
        }

        /* 脉冲光环 */
        .start-processing-pulse {
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 2px solid rgba(147, 197, 253, 0.6);
            border-radius: inherit;
            animation: pulseRing 2.5s ease-in-out infinite;
            pointer-events: none;
            z-index: 0;
        }

        @keyframes sparklesTwinkle {
            0%, 100% {
                opacity: 0.8;
                transform: scale(1) rotate(0deg);
            }
            33% {
                opacity: 1;
                transform: scale(1.1) rotate(120deg);
            }
            66% {
                opacity: 0.9;
                transform: scale(1.05) rotate(240deg);
            }
        }

        @keyframes pulseRing {
            0%, 100% {
                transform: scale(1);
                opacity: 0.6;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.3;
            }
        }

        .highlight-box {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.15) 100%);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 12px;
            padding: 16px;
            margin-top: 20px;
        }

        .highlight-title {
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 8px;
        }

        .highlight-text {
            color: #3730a3;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-section">
            <h1 class="demo-title">🎯 动画复用效果演示</h1>
            <p class="demo-description">
                将"开始批量处理"卡片的icon动画成功复用到"准备就绪"的icon上，实现代码复用而非复制
            </p>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">动画复用对比</h2>
            <p class="demo-description">
                左侧是原始的"开始批量处理"动画，右侧是"准备就绪"复用相同动画的效果
            </p>
            
            <div class="comparison-grid">
                <div class="comparison-item">
                    <div class="comparison-title">🚀 开始批量处理 (原始)</div>
                    <div class="icon-container icon-container--lg start-processing-icon">
                        3
                        <!-- 星光特效层 -->
                        <div class="start-processing-sparkles"></div>
                        <!-- 脉冲光环 -->
                        <div class="start-processing-pulse"></div>
                    </div>
                    <p style="color: #6b7280; font-size: 0.875rem;">
                        原始的开始批量处理动画<br>
                        包含脉冲、星光、光环效果
                    </p>
                </div>
                
                <div class="comparison-item">
                    <div class="comparison-title">✅ 准备就绪 (复用)</div>
                    <div class="icon-container icon-container--lg ready-icon-animated">
                        ▶
                        <!-- 复用开始批量处理的星光特效层 -->
                        <div class="start-processing-sparkles"></div>
                        <!-- 复用开始批量处理的脉冲光环 -->
                        <div class="start-processing-pulse"></div>
                    </div>
                    <p style="color: #6b7280; font-size: 0.875rem;">
                        复用相同动画的准备就绪状态<br>
                        通过CSS类复用，无代码重复
                    </p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="highlight-box">
                <div class="highlight-title">🎯 复用实现方式</div>
                <div class="highlight-text">
                    ✅ <strong>CSS类复用</strong>：创建 `.ready-icon-animated` 类复用 `iconPulse` 动画<br>
                    ✅ <strong>特效层复用</strong>：直接使用 `.start-processing-sparkles` 和 `.start-processing-pulse` 类<br>
                    ✅ <strong>禁用!important</strong>：通过合理的CSS选择器优先级实现，无!important污染<br>
                    ✅ <strong>代码复用</strong>：避免重复定义动画，提高可维护性<br>
                    ✅ <strong>性能优化</strong>：减少CSS文件大小，提升加载性能
                </div>
            </div>
        </div>
    </div>
</body>
</html>
