/**
 * 数据源标签集成测试
 * 
 * 🎯 测试目标：
 * 1. 验证底稿数据接口出错时的退化机制
 * 2. 验证结果列表中的数据源标签显示
 * 3. 验证不同数据源的正确标识
 * 4. 验证数据源统计的准确性
 */

import { EnhancedBatchProcessorService } from '../services/EnhancedBatchProcessorService';
import { InternalDataService } from '../services/InternalDataService';
import { ProcessResult } from '../types';

// Mock fetch for testing
global.fetch = jest.fn();

describe('数据源标签集成测试', () => {
  let batchService: EnhancedBatchProcessorService;
  
  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks();
    
    // 创建批处理服务实例
    batchService = new EnhancedBatchProcessorService({
      api: {
        endpoint: 'https://test-api.example.com/chat',
        workflowId: 'test-workflow-id',
        timeout: 30000,
      },
      processing: {
        useInternalData: false, // 默认关闭
        concurrency: 1,
        retryAttempts: 1,
        retryDelay: 1000,
      },
      upload: {
        enabled: false,
      },
    });
  });

  afterEach(async () => {
    // 清理资源
    await batchService.dispose();
  });

  describe('数据源标识测试', () => {
    test('底稿数据模式成功时应标识为internal', async () => {
      // 开启底稿数据模式
      batchService.setInternalDataMode(true);

      // Mock底稿数据接口成功返回
      const mockInternalData = {
        answer: '九九乘法表内容...',
        pv: 4351,
        reference: '参考资料...',
      };

      jest.spyOn(InternalDataService, 'fetchInternalData').mockResolvedValue({
        success: true,
        data: mockInternalData,
        summary: '九九乘法表相关内容',
        source: 'api',
        timestamp: Date.now(),
      });

      // Mock AI API调用
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        body: {
          getReader: () => ({
            read: jest.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: ${JSON.stringify({ content: 'AI生成的UI代码...' })}\n\n`),
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined,
              }),
          }),
        },
      });

      // 执行查询处理
      const result = await batchService.processQuery('九九乘法表');

      // 验证数据源标识
      expect(result.dataSource).toBe('internal');
      expect(result.internalDataSummary).toBe('九九乘法表相关内容');
    });

    test('底稿数据模式关闭时应标识为ai', async () => {
      // 确保底稿数据模式关闭
      batchService.setInternalDataMode(false);

      // Mock AI API调用
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        body: {
          getReader: () => ({
            read: jest.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: ${JSON.stringify({ content: 'AI生成的UI代码...' })}\n\n`),
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined,
              }),
          }),
        },
      });

      // 执行查询处理
      const result = await batchService.processQuery('测试查询');

      // 验证数据源标识
      expect(result.dataSource).toBe('ai');
      expect(result.internalDataSummary).toBeUndefined();
    });

    test('底稿数据接口出错时应标识为fallback并退化到原有query链路', async () => {
      // 开启底稿数据模式
      batchService.setInternalDataMode(true);

      // Mock底稿数据接口失败
      jest.spyOn(InternalDataService, 'fetchInternalData').mockRejectedValue(
        new Error('网络连接失败')
      );

      // Mock AI API调用
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        body: {
          getReader: () => ({
            read: jest.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: ${JSON.stringify({ content: 'AI生成的UI代码...' })}\n\n`),
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined,
              }),
          }),
        },
      });

      // 执行查询处理
      const result = await batchService.processQuery('测试查询');

      // 验证数据源标识为回退模式
      expect(result.dataSource).toBe('fallback');
      expect(result.internalDataSummary).toBeUndefined();

      // 验证AI接收到的是原始query（退化到原有链路）
      const fetchCall = (global.fetch as jest.Mock).mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);
      const userMessage = requestBody.messages.find((msg: any) => msg.role === 'user');
      
      expect(userMessage.content).toBe('测试查询');
    });

    test('底稿数据返回但无有效内容时应标识为fallback', async () => {
      // 开启底稿数据模式
      batchService.setInternalDataMode(true);

      // Mock底稿数据接口返回但source为direct
      jest.spyOn(InternalDataService, 'fetchInternalData').mockResolvedValue({
        success: false,
        data: null,
        error: '未找到相关数据',
        source: 'api',
        timestamp: Date.now(),
      });

      // Mock AI API调用
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        body: {
          getReader: () => ({
            read: jest.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: ${JSON.stringify({ content: 'AI生成的UI代码...' })}\n\n`),
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined,
              }),
          }),
        },
      });

      // 执行查询处理
      const result = await batchService.processQuery('测试查询');

      // 验证数据源标识为回退模式
      expect(result.dataSource).toBe('fallback');
    });
  });

  describe('数据源统计测试', () => {
    test('应正确统计不同数据源的数量', () => {
      const results: ProcessResult[] = [
        {
          id: '1',
          query: 'query1',
          status: 'success',
          startTime: Date.now(),
          dataSource: 'internal',
          internalDataSummary: '底稿数据摘要1',
        },
        {
          id: '2',
          query: 'query2',
          status: 'success',
          startTime: Date.now(),
          dataSource: 'ai',
        },
        {
          id: '3',
          query: 'query3',
          status: 'success',
          startTime: Date.now(),
          dataSource: 'fallback',
        },
        {
          id: '4',
          query: 'query4',
          status: 'success',
          startTime: Date.now(),
          dataSource: 'internal',
          internalDataSummary: '底稿数据摘要2',
        },
        {
          id: '5',
          query: 'query5',
          status: 'success',
          startTime: Date.now(),
          // 没有dataSource字段，应该默认为ai
        },
      ];

      // 计算统计
      const stats = {
        internal: 0,
        ai: 0,
        fallback: 0,
      };
      
      results.forEach(result => {
        if (result.dataSource) {
          stats[result.dataSource]++;
        } else {
          stats.ai++;
        }
      });

      // 验证统计结果
      expect(stats.internal).toBe(2);
      expect(stats.ai).toBe(2); // 1个明确的ai + 1个默认的ai
      expect(stats.fallback).toBe(1);
    });
  });

  describe('错误处理和回退机制测试', () => {
    test('网络错误时应该正确回退', async () => {
      batchService.setInternalDataMode(true);

      // Mock网络错误
      jest.spyOn(InternalDataService, 'fetchInternalData').mockRejectedValue(
        new Error('NETWORK_ERROR')
      );

      // Mock AI API调用
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        body: {
          getReader: () => ({
            read: jest.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: ${JSON.stringify({ content: 'AI响应' })}\n\n`),
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined,
              }),
          }),
        },
      });

      const result = await batchService.processQuery('测试查询');

      expect(result.dataSource).toBe('fallback');
      expect(result.status).toBe('success'); // 应该成功完成，不受底稿数据错误影响
    });

    test('超时错误时应该正确回退', async () => {
      batchService.setInternalDataMode(true);

      // Mock超时错误
      jest.spyOn(InternalDataService, 'fetchInternalData').mockRejectedValue(
        new Error('TIMEOUT_ERROR')
      );

      // Mock AI API调用
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        body: {
          getReader: () => ({
            read: jest.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: ${JSON.stringify({ content: 'AI响应' })}\n\n`),
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined,
              }),
          }),
        },
      });

      const result = await batchService.processQuery('测试查询');

      expect(result.dataSource).toBe('fallback');
      expect(result.status).toBe('success');
    });
  });

  describe('UI组件集成测试', () => {
    test('DataSourceLabel组件应该正确显示不同数据源', () => {
      // 这里可以添加React组件的测试
      // 由于需要React测试环境，这里只做接口验证
      
      const internalResult: ProcessResult = {
        id: '1',
        query: 'test',
        status: 'success',
        startTime: Date.now(),
        dataSource: 'internal',
        internalDataSummary: '底稿数据摘要',
      };

      const aiResult: ProcessResult = {
        id: '2',
        query: 'test',
        status: 'success',
        startTime: Date.now(),
        dataSource: 'ai',
      };

      const fallbackResult: ProcessResult = {
        id: '3',
        query: 'test',
        status: 'success',
        startTime: Date.now(),
        dataSource: 'fallback',
      };

      // 验证数据结构正确
      expect(internalResult.dataSource).toBe('internal');
      expect(internalResult.internalDataSummary).toBeDefined();
      
      expect(aiResult.dataSource).toBe('ai');
      expect(aiResult.internalDataSummary).toBeUndefined();
      
      expect(fallbackResult.dataSource).toBe('fallback');
      expect(fallbackResult.internalDataSummary).toBeUndefined();
    });
  });
});
