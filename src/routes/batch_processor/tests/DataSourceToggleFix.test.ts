/**
 * 数据源开关修复验证测试
 * 
 * 🎯 测试目标：
 * 验证底稿数据开关能够正确传递到批处理服务
 */

import { EnhancedBatchProcessorService } from '../services/EnhancedBatchProcessorService';

// Mock fetch for testing
global.fetch = jest.fn();

describe('数据源开关修复验证', () => {
  let batchService: EnhancedBatchProcessorService;
  
  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks();
    
    // 创建批处理服务实例
    batchService = new EnhancedBatchProcessorService({
      api: {
        endpoint: 'https://test-api.example.com/chat',
        workflowId: 'test-workflow-id',
        timeout: 30000,
      },
      processing: {
        useInternalData: false, // 默认关闭
        concurrency: 1,
        retryAttempts: 1,
        retryDelay: 1000,
      },
      upload: {
        enabled: false,
      },
    });
  });

  afterEach(async () => {
    // 清理资源
    await batchService.dispose();
  });

  test('setInternalDataMode 方法应该正确设置配置', () => {
    // 验证初始状态
    expect(batchService.getInternalDataMode()).toBe(false);
    
    // 启用底稿数据模式
    batchService.setInternalDataMode(true);
    expect(batchService.getInternalDataMode()).toBe(true);
    
    // 关闭底稿数据模式
    batchService.setInternalDataMode(false);
    expect(batchService.getInternalDataMode()).toBe(false);
  });

  test('底稿数据模式开启时应该调用底稿数据接口', async () => {
    // 开启底稿数据模式
    batchService.setInternalDataMode(true);
    
    // Mock底稿数据接口成功返回
    const mockFetch = global.fetch as jest.Mock;
    
    // Mock底稿数据接口调用
    mockFetch.mockImplementationOnce(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          answer: '测试答案',
          pv: 1000,
          reference: '测试参考',
        }),
      })
    );
    
    // Mock AI API调用
    mockFetch.mockImplementationOnce(() =>
      Promise.resolve({
        ok: true,
        body: {
          getReader: () => ({
            read: jest.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: ${JSON.stringify({ content: 'AI响应' })}\n\n`),
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined,
              }),
          }),
        },
      })
    );

    // 执行查询处理
    const result = await batchService.processQuery('测试查询');

    // 验证底稿数据接口被调用
    const fetchCalls = mockFetch.mock.calls;
    expect(fetchCalls.length).toBeGreaterThanOrEqual(1);
    
    // 验证第一个调用是底稿数据接口
    const firstCall = fetchCalls[0];
    expect(firstCall[0]).toContain('9gzj7t9k.fn.bytedance.net');
    expect(firstCall[0]).toContain('api/search/stream');
    expect(firstCall[0]).toContain('query=');
    
    // 验证结果包含数据源信息
    expect(result.dataSource).toBeDefined();
  });

  test('底稿数据模式关闭时应该直接调用AI接口', async () => {
    // 确保底稿数据模式关闭
    batchService.setInternalDataMode(false);
    
    // Mock AI API调用
    const mockFetch = global.fetch as jest.Mock;
    mockFetch.mockResolvedValue({
      ok: true,
      body: {
        getReader: () => ({
          read: jest.fn()
            .mockResolvedValueOnce({
              done: false,
              value: new TextEncoder().encode(`data: ${JSON.stringify({ content: 'AI响应' })}\n\n`),
            })
            .mockResolvedValueOnce({
              done: true,
              value: undefined,
            }),
        }),
      },
    });

    // 执行查询处理
    const result = await batchService.processQuery('测试查询');

    // 验证只调用了AI接口，没有调用底稿数据接口
    const fetchCalls = mockFetch.mock.calls;
    expect(fetchCalls.length).toBe(1);
    
    // 验证调用的是AI接口
    const firstCall = fetchCalls[0];
    expect(firstCall[0]).toContain('gen-ui.bytedance.net');
    expect(firstCall[0]).toContain('chat');
    
    // 验证结果数据源为ai
    expect(result.dataSource).toBe('ai');
  });

  test('getInternalDataMode 方法应该返回正确的状态', () => {
    // 测试默认状态
    expect(batchService.getInternalDataMode()).toBe(false);
    
    // 测试启用状态
    batchService.setInternalDataMode(true);
    expect(batchService.getInternalDataMode()).toBe(true);
    
    // 测试关闭状态
    batchService.setInternalDataMode(false);
    expect(batchService.getInternalDataMode()).toBe(false);
  });
});
