/**
 * 底稿数据集成功能测试
 * 
 * 测试范围：
 * 1. InternalDataService 接口调用
 * 2. DataProcessingService 数据处理
 * 3. 缓存机制
 * 4. 错误处理
 * 5. 模式切换
 */

import { InternalDataService, InternalDataError } from '../services/InternalDataService';
import { DataProcessingService } from '../services/DataProcessingService';

describe('底稿数据集成功能测试', () => {
  
  beforeEach(() => {
    // 清空缓存
    InternalDataService.clearCache();
  });

  describe('InternalDataService', () => {
    
    test('应该能够获取底稿数据', async () => {
      const query = '九九乘法表';
      const result = await InternalDataService.fetchInternalData(query, true);
      
      expect(result).toBeDefined();
      expect(result.success).toBeDefined();
      expect(result.timestamp).toBeDefined();
      expect(result.source).toMatch(/^(api|cache)$/);
      
      if (result.success) {
        expect(result.data).toBeDefined();
        expect(result.summary).toBeDefined();
      } else {
        expect(result.error).toBeDefined();
      }
    });

    test('应该能够处理无效查询', async () => {
      const result = await InternalDataService.fetchInternalData('', true);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('查询参数不能为空');
    });

    test('缓存机制应该正常工作', async () => {
      const query = '测试查询';
      
      // 第一次请求
      const result1 = await InternalDataService.fetchInternalData(query, true);
      
      // 第二次请求应该使用缓存
      const result2 = await InternalDataService.fetchInternalData(query, true);
      
      if (result1.success && result2.success) {
        expect(result2.source).toBe('cache');
      }
    });

    test('缓存统计应该正确', () => {
      const stats = InternalDataService.getCacheStats();
      
      expect(stats).toBeDefined();
      expect(stats.size).toBeDefined();
      expect(stats.keys).toBeInstanceOf(Array);
      expect(stats.totalMemoryUsage).toBeDefined();
    });

  });

  describe('DataProcessingService', () => {
    
    test('直接模式应该返回原始查询', async () => {
      const query = '测试查询';
      const result = await DataProcessingService.processQuery(query, false);
      
      expect(result.processedQuery).toBe(query);
      expect(result.source).toBe('direct');
      expect(result.originalQuery).toBe(query);
    });

    test('底稿数据模式应该增强查询', async () => {
      const query = '九九乘法表';
      const result = await DataProcessingService.processQuery(query, true);
      
      expect(result).toBeDefined();
      expect(result.originalQuery).toBe(query);
      expect(result.source).toMatch(/^(internal|direct)$/);
      
      if (result.source === 'internal') {
        expect(result.processedQuery).toContain('底稿数据');
        expect(result.processedQuery).toContain('严格使用');
        expect(result.context).toBeDefined();
      }
    });

    test('进度回调应该被调用', async () => {
      const query = '测试查询';
      const progressValues: number[] = [];
      
      await DataProcessingService.processQuery(
        query, 
        true, 
        (progress) => {
          progressValues.push(progress);
        }
      );
      
      expect(progressValues.length).toBeGreaterThan(0);
      expect(progressValues[progressValues.length - 1]).toBe(1.0);
    });

  });

  describe('错误处理', () => {
    
    test('网络错误应该被正确处理', async () => {
      // 模拟网络错误（使用无效URL）
      const originalFetch = global.fetch;
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));
      
      const result = await InternalDataService.fetchInternalData('测试', true);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      
      // 恢复原始fetch
      global.fetch = originalFetch;
    });

    test('数据处理失败应该回退到直接模式', async () => {
      const query = '测试查询';
      
      // 模拟底稿数据获取失败
      const originalFetch = global.fetch;
      global.fetch = jest.fn().mockRejectedValue(new Error('API error'));
      
      const result = await DataProcessingService.processQuery(query, true);
      
      expect(result.source).toBe('direct');
      expect(result.processedQuery).toBe(query);
      
      // 恢复原始fetch
      global.fetch = originalFetch;
    });

  });

  describe('性能测试', () => {
    
    test('缓存应该提升性能', async () => {
      const query = '性能测试查询';
      
      // 第一次请求（API调用）
      const start1 = Date.now();
      const result1 = await InternalDataService.fetchInternalData(query, true);
      const duration1 = Date.now() - start1;
      
      // 第二次请求（缓存）
      const start2 = Date.now();
      const result2 = await InternalDataService.fetchInternalData(query, true);
      const duration2 = Date.now() - start2;
      
      if (result1.success && result2.success && result2.source === 'cache') {
        expect(duration2).toBeLessThan(duration1);
      }
    });

    test('大数据应该被正确处理', async () => {
      // 创建大查询
      const largeQuery = 'A'.repeat(1000);
      const result = await DataProcessingService.processQuery(largeQuery, true);
      
      expect(result).toBeDefined();
      expect(result.originalQuery).toBe(largeQuery);
    });

  });

});

/**
 * 集成测试：完整的数据流程
 */
describe('集成测试', () => {
  
  test('完整的底稿数据处理流程', async () => {
    const query = '九九乘法表';
    let progressCount = 0;
    
    const result = await DataProcessingService.processQuery(
      query,
      true,
      (progress) => {
        progressCount++;
        expect(progress).toBeGreaterThanOrEqual(0);
        expect(progress).toBeLessThanOrEqual(1);
      }
    );
    
    expect(result).toBeDefined();
    expect(result.originalQuery).toBe(query);
    expect(progressCount).toBeGreaterThan(0);
    
    if (result.source === 'internal') {
      expect(result.processedQuery).toContain(query);
      expect(result.processedQuery).toContain('底稿数据');
      expect(result.context).toBeDefined();
      expect(result.internalDataSummary).toBeDefined();
    }
  });

  test('模式切换应该影响处理结果', async () => {
    const query = '测试查询';
    
    // 直接模式
    const directResult = await DataProcessingService.processQuery(query, false);
    expect(directResult.source).toBe('direct');
    expect(directResult.processedQuery).toBe(query);
    
    // 底稿数据模式
    const internalResult = await DataProcessingService.processQuery(query, true);
    expect(internalResult.originalQuery).toBe(query);
    
    // 两种模式的结果应该不同（除非底稿数据获取失败回退）
    if (internalResult.source === 'internal') {
      expect(internalResult.processedQuery).not.toBe(directResult.processedQuery);
    }
  });

});

/**
 * 模拟工具函数
 */
const mockFetch = (response: any, success: boolean = true) => {
  global.fetch = jest.fn().mockImplementation(() => {
    if (success) {
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve(response),
        text: () => Promise.resolve(JSON.stringify(response)),
      });
    } else {
      return Promise.resolve({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        text: () => Promise.resolve('Server Error'),
      });
    }
  });
};

/**
 * 测试数据
 */
const mockInternalData = {
  title: '九九乘法表',
  content: '1×1=1, 1×2=2, 1×3=3...',
  summary: '九九乘法表是基础数学运算表',
  data: [
    { row: 1, values: [1, 2, 3, 4, 5, 6, 7, 8, 9] },
    { row: 2, values: [2, 4, 6, 8, 10, 12, 14, 16, 18] },
    // ... 更多数据
  ]
};
