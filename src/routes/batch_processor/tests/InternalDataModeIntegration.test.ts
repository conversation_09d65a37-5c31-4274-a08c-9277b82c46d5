/**
 * 底稿数据模式集成测试
 * 
 * 🎯 测试目标：
 * 1. 验证底稿数据模式的完整链路
 * 2. 确保AI接口接收到的是底稿数据内容而不是原始query
 * 3. 验证错误处理和回退机制
 * 4. 确保不影响原有的默认AI链路
 */

import { EnhancedBatchProcessorService } from '../services/EnhancedBatchProcessorService';
import { DataProcessingService } from '../services/DataProcessingService';
import { InternalDataService } from '../services/InternalDataService';

// Mock fetch for testing
global.fetch = jest.fn();

describe('底稿数据模式集成测试', () => {
  let batchService: EnhancedBatchProcessorService;
  
  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks();
    
    // 创建批处理服务实例
    batchService = new EnhancedBatchProcessorService({
      api: {
        endpoint: 'https://test-api.example.com/chat',
        workflowId: 'test-workflow-id',
        timeout: 30000,
      },
      processing: {
        useInternalData: false, // 默认关闭
        concurrency: 1,
        retryAttempts: 1,
        retryDelay: 1000,
      },
      upload: {
        enabled: false,
      },
    });
  });

  afterEach(async () => {
    // 清理资源
    await batchService.dispose();
  });

  describe('底稿数据模式开启时', () => {
    beforeEach(() => {
      // 开启底稿数据模式
      batchService.setInternalDataMode(true);
    });

    test('应该使用底稿数据内容作为AI输入', async () => {
      // Mock底稿数据接口返回
      const mockInternalData = {
        answer: '九九乘法表是数学乘法运算的基础工具，以下是完整的九九乘法表内容...',
        pv: 4351,
        reasoning: '这是一个基础数学概念的查询',
        reference: '参考资料：小学数学教材第三章...',
      };

      // Mock InternalDataService
      jest.spyOn(InternalDataService, 'fetchInternalData').mockResolvedValue({
        success: true,
        data: mockInternalData,
        summary: '九九乘法表相关内容',
        source: 'api',
        timestamp: Date.now(),
      });

      // Mock AI API调用
      const mockAIResponse = 'AI生成的UI代码...';
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        body: {
          getReader: () => ({
            read: jest.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: ${JSON.stringify({ content: mockAIResponse })}\n\n`),
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined,
              }),
          }),
        },
      });

      // 执行查询处理
      const result = await batchService.processQuery('九九乘法表');

      // 验证底稿数据接口被调用
      expect(InternalDataService.fetchInternalData).toHaveBeenCalledWith('九九乘法表', true);

      // 验证AI API被调用，且content包含底稿数据
      expect(global.fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining(mockInternalData.answer),
        })
      );

      // 验证传递给AI的content不是原始query
      const fetchCall = (global.fetch as jest.Mock).mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);
      const userMessage = requestBody.messages.find((msg: any) => msg.role === 'user');
      
      expect(userMessage.content).toContain('用户查询：九九乘法表');
      expect(userMessage.content).toContain('【底稿数据内容】');
      expect(userMessage.content).toContain(mockInternalData.answer);
      expect(userMessage.content).toContain(`【数据热度】\n${mockInternalData.pv} 次浏览`);
      expect(userMessage.content).toContain('【参考资料】');
      expect(userMessage.content).toContain(mockInternalData.reference);
    });

    test('底稿数据获取失败时应该回退到原始query', async () => {
      // Mock底稿数据接口失败
      jest.spyOn(InternalDataService, 'fetchInternalData').mockResolvedValue({
        success: false,
        data: null,
        error: '网络错误',
        source: 'api',
        timestamp: Date.now(),
      });

      // Mock AI API调用
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        body: {
          getReader: () => ({
            read: jest.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: ${JSON.stringify({ content: 'AI响应' })}\n\n`),
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined,
              }),
          }),
        },
      });

      // 执行查询处理
      await batchService.processQuery('测试查询');

      // 验证AI API被调用，且content是原始query
      const fetchCall = (global.fetch as jest.Mock).mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);
      const userMessage = requestBody.messages.find((msg: any) => msg.role === 'user');
      
      expect(userMessage.content).toBe('测试查询');
    });
  });

  describe('底稿数据模式关闭时', () => {
    beforeEach(() => {
      // 确保底稿数据模式关闭
      batchService.setInternalDataMode(false);
    });

    test('应该直接使用原始query作为AI输入', async () => {
      // Mock AI API调用
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        body: {
          getReader: () => ({
            read: jest.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: ${JSON.stringify({ content: 'AI响应' })}\n\n`),
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined,
              }),
          }),
        },
      });

      // 执行查询处理
      await batchService.processQuery('测试查询');

      // 验证底稿数据接口没有被调用
      expect(InternalDataService.fetchInternalData).not.toHaveBeenCalled();

      // 验证AI API被调用，且content是原始query
      const fetchCall = (global.fetch as jest.Mock).mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);
      const userMessage = requestBody.messages.find((msg: any) => msg.role === 'user');
      
      expect(userMessage.content).toBe('测试查询');
    });

    test('不应该影响原有的AI链路', async () => {
      // Mock AI API调用
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        body: {
          getReader: () => ({
            read: jest.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: ${JSON.stringify({ content: '正常AI响应' })}\n\n`),
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined,
              }),
          }),
        },
      });

      // 执行查询处理
      const result = await batchService.processQuery('普通查询');

      // 验证结果正常
      expect(result).toBeDefined();
      expect(global.fetch).toHaveBeenCalledTimes(1);
      
      // 验证请求格式正确
      const fetchCall = (global.fetch as jest.Mock).mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);
      
      expect(requestBody.messages).toHaveLength(2);
      expect(requestBody.messages[0].role).toBe('system');
      expect(requestBody.messages[1].role).toBe('user');
      expect(requestBody.messages[1].content).toBe('普通查询');
    });
  });

  describe('DataProcessingService集成测试', () => {
    test('buildInternalDataContent方法应该正确构建内容', () => {
      const originalQuery = '九九乘法表';
      const internalData = {
        answer: '九九乘法表是数学基础工具',
        pv: 1234,
        reference: '参考：数学教材',
        reasoning: '基础数学概念',
      };

      // 通过反射访问私有方法进行测试
      const service = batchService as any;
      const result = service.buildInternalDataContent(originalQuery, internalData);

      expect(result).toContain('用户查询：九九乘法表');
      expect(result).toContain('【底稿数据内容】');
      expect(result).toContain('九九乘法表是数学基础工具');
      expect(result).toContain('【数据热度】');
      expect(result).toContain('1234 次浏览');
      expect(result).toContain('【参考资料】');
      expect(result).toContain('参考：数学教材');
      expect(result).toContain('【推理过程】');
      expect(result).toContain('基础数学概念');
      expect(result).toContain('请严格基于以上底稿数据内容生成相应的UI界面');
    });
  });

  describe('错误处理测试', () => {
    test('底稿数据处理异常时应该优雅回退', async () => {
      // 开启底稿数据模式
      batchService.setInternalDataMode(true);

      // Mock底稿数据接口抛出异常
      jest.spyOn(InternalDataService, 'fetchInternalData').mockRejectedValue(
        new Error('网络连接失败')
      );

      // Mock AI API调用
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        body: {
          getReader: () => ({
            read: jest.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: ${JSON.stringify({ content: 'AI响应' })}\n\n`),
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined,
              }),
          }),
        },
      });

      // 执行查询处理，不应该抛出异常
      const result = await batchService.processQuery('测试查询');

      // 验证结果正常，使用了回退机制
      expect(result).toBeDefined();
      
      // 验证AI API被调用，且使用了原始query
      const fetchCall = (global.fetch as jest.Mock).mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);
      const userMessage = requestBody.messages.find((msg: any) => msg.role === 'user');
      
      expect(userMessage.content).toBe('测试查询');
    });
  });
});
