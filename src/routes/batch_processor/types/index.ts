/**
 * 批处理系统的类型定义
 * 集中定义所有在批处理系统中共享的接口和类型
 */

// -----------------------------------------------------------------------------
// 类型定义
// -----------------------------------------------------------------------------

/**
 * 批处理配置
 */
export interface BatchConfig {
  processing: {
    concurrent: number; // 并发处理数量
    batchSize: number; // 批次大小
    delayBetweenRequests: number; // 请求间隔时间(ms)
    enableCache: boolean; // 启用缓存
    skipExistingFiles: boolean; // 跳过已存在文件
    useInternalData: boolean; // 使用抖音内部底稿数据
    enableDeduplication: boolean; // 启用查询去重（默认true，设为false时允许重复查询）
  };
  api: {
    endpoint: string; // API端点
    workflowId: string; // 工作流ID
    timeout: number; // 超时时间(ms)
    maxRetries: number; // 最大重试次数
    rateLimit: number; // 速率限制
  };
}

/**
 * 处理结果项
 */
export interface ProcessResult {
  id: string; // 唯一标识符
  query: string; // 查询文本
  status: ProcessStatus; // 处理状态
  playgroundUrl?: string; // Playground URL
  error?: string; // 错误信息
  timestamp?: number; // 时间戳
  startTime: number; // 开始处理时间
  endTime?: number; // 结束处理时间
  processTime?: number; // 处理耗时（毫秒）
  fileCount?: number; // 生成的文件数量
  metadata?: Record<string, any>; // 元数据
  dataSource?: 'internal' | 'ai' | 'fallback'; // 数据源标识：internal=底稿数据，ai=AI生成，fallback=底稿数据失败回退
  internalDataSummary?: string; // 底稿数据摘要（仅当dataSource为internal时）
}

/**
 * 处理状态
 */
export type ProcessStatus =
  | 'success'
  | 'error'
  | 'processing'
  | 'pending'
  | 'waiting';

/**
 * 批处理进度
 */
export interface BatchProgress {
  total: number; // 总任务数
  completed: number; // 已完成数
  failed: number; // 失败数
  processing: number; // 正在处理数
  pending: number; // 待处理数
  percentage: number; // 总完成百分比 (已完成 + 失败) / 总数
  successPercentage: number; // 成功完成百分比 已完成 / 总数
  current?: string; // 当前处理的查询
  estimatedTimeRemaining?: number; // 预估剩余时间(ms)
  throughput?: number; // 吞吐量(任务/秒)
  startTime?: number; // 开始处理时间戳
}

// 流数据
export interface StreamData {
  /** 唯一标识符 */
  id: string;
  /** 查询内容 */
  query: string;
  /** 完整的流响应内容 */
  content: string;
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
  /** 大小(字节) */
  size: number;
}

// 存储统计信息
export interface StorageStats {
  /** 存储项目数量 */
  itemCount: number;
  /** 总存储大小(字节) */
  totalSize: number;
  /** 最早项目时间 */
  oldestItemTime?: number;
  /** 最新项目时间 */
  newestItemTime?: number;
}

// 文件结构
export interface FileStructure {
  /** 键为文件路径，值为文件内容 */
  [path: string]: string;
}

/**
 * 上传结果
 */
export interface UploadResult {
  /** 是否成功 */
  success: boolean;
  /** CDN URL */
  cdnUrl?: string;
  /** 下载URL */
  downloadUrl?: string;
  /** 错误信息 */
  error?: string;
  /** 上传时间 */
  timestamp?: number;
  /** 文件大小(字节) */
  size?: number;
  /** 文件ID */
  fileId?: string;
  /** CDN域名 */
  domain?: string;
  /** CDN路径前缀 */
  path?: string;
  /** TOS密钥 */
  tosKey?: string;
  /** 响应代码 */
  code?: number;
}

/**
 * 查询解析结果
 */
export interface QueryParseResult {
  original: string; // 原始文本
  queries: string[]; // 解析后的查询列表
  valid: number; // 有效查询数量
  invalid: number; // 无效查询数量
}

/**
 * 回调函数类型
 */
export type ProgressCallback = (progress: BatchProgress) => void;
export type ResultCallback = (result: ProcessResult) => void;

/**
 * 批处理作业
 */
export interface BatchJob {
  id: string; // 作业ID
  query: string; // 查询内容
  retries: number; // 已重试次数
  status: ProcessStatus; // 处理状态
  error?: string; // 错误信息
  result?: any; // 处理结果
  createdAt: number; // 创建时间戳
  startedAt?: number; // 开始处理时间戳
  completedAt?: number; // 完成时间戳
}

/**
 * 流处理结果
 */
export interface StreamProcessResult {
  success: boolean; // 是否成功
  extractedContent?: string; // 提取的内容
  error?: string; // 错误信息
  codeBlocks?: string[]; // 代码块列表
  metadata?: Record<string, any>; // 元数据
}

/**
 * 提示词模板
 */
export interface PromptTemplate {
  id: string; // 模板ID
  name: string; // 模板名称
  content: string; // 模板内容
  isDefault: boolean; // 是否为默认模板
  createdAt: number; // 创建时间戳
  updatedAt?: number; // 更新时间戳
  lastUsedAt?: number; // 最后使用时间戳
  useCount: number; // 使用次数
}

/**
 * 历史记录
 */
export interface HistoryRecord {
  id: string; // 记录ID
  queries: string[]; // 查询列表
  results: ProcessResult[]; // 处理结果列表
  systemPrompt: string; // 使用的系统提示词
  timestamp: number; // 记录时间戳
  config: Partial<BatchConfig>; // 使用的配置
  successful: number; // 成功数量
  failed: number; // 失败数量
}

/**
 * 历史统计
 */
export interface HistoryStats {
  totalRecords: number; // 总记录数
  totalQueries: number; // 总查询数
  successfulQueries: number; // 成功查询数
  failedQueries: number; // 失败查询数
  successRate: number; // 成功率
}

/**
 * 存储项目
 */
export interface StorageItem<T = any> {
  key: string; // 键
  value: T; // 值
  timestamp: number; // 创建时间戳
  expires?: number; // 过期时间戳
}
