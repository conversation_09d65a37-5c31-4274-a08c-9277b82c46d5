/**
 * LynxChart Compatibility Layer
 * 解决 "lynx.createCanvasContext is not a function" 错误的兼容性层
 */

// Types
interface LynxAPI {
  krypton: {
    CanvasElement: new (name: string, legacy: boolean) => HTMLCanvasElement;
    createImage: (src?: string) => HTMLImageElement;
    createCanvas: (canvasName: string) => HTMLCanvasElement;
  };
  createOffscreenCanvas: (width: number, height: number) => HTMLCanvasElement;
  requestAnimationFrame: typeof requestAnimationFrame;
  cancelAnimationFrame: typeof cancelAnimationFrame;
}

interface SystemInfo {
  pixelRatio: number;
}

interface LynxChartConfig {
  canvasName: string;
  width: number;
  height: number;
}

// Global declarations
declare global {
  var lynx: LynxAPI;
  var SystemInfo: SystemInfo;
}

/**
 * 检查Lynx环境是否可用
 */
export function checkLynxEnvironment(): boolean {
  try {
    return !!(
      typeof lynx !== 'undefined' &&
      lynx.krypton &&
      typeof lynx.krypton.createCanvas === 'function' &&
      typeof lynx.createOffscreenCanvas === 'function' &&
      typeof SystemInfo !== 'undefined'
    );
  } catch (error) {
    return false;
  }
}

/**
 * 获取环境诊断信息
 */
export function diagnoseLynxEnvironment() {
  const diagnosis = {
    lynxDefined: typeof lynx !== 'undefined',
    kryptonDefined: typeof lynx?.krypton !== 'undefined',
    systemInfoDefined: typeof SystemInfo !== 'undefined',
    createCanvasAvailable: typeof lynx?.krypton?.createCanvas === 'function',
    createOffscreenCanvasAvailable: typeof lynx?.createOffscreenCanvas === 'function',
    requestAnimationFrameAvailable: typeof lynx?.requestAnimationFrame === 'function',
    errors: [] as string[]
  };

  if (!diagnosis.lynxDefined) {
    diagnosis.errors.push('全局对象 lynx 未定义');
  }
  
  if (!diagnosis.kryptonDefined) {
    diagnosis.errors.push('lynx.krypton 渲染引擎未定义');
  }
  
  if (!diagnosis.systemInfoDefined) {
    diagnosis.errors.push('全局对象 SystemInfo 未定义');
  }
  
  if (!diagnosis.createCanvasAvailable) {
    diagnosis.errors.push('lynx.krypton.createCanvas 方法不可用');
  }

  return diagnosis;
}

/**
 * 创建Lynx环境Mock（仅用于开发调试）
 * 警告：仅在开发环境使用，生产环境应使用真实Lynx环境
 */
export function createLynxEnvironmentMock(): void {
  if (typeof window === 'undefined') {
    console.warn('LynxChart Mock只能在浏览器环境中使用');
    return;
  }

  console.warn('🚧 使用Lynx环境Mock - 仅限开发调试使用');

  // Mock lynx object
  if (typeof (globalThis as any).lynx === 'undefined') {
    (globalThis as any).lynx = {
      krypton: {
        CanvasElement: function(name: string, legacy: boolean = false) {
          const canvas = document.createElement('canvas');
          canvas.id = name;
          canvas.setAttribute('data-legacy', String(legacy));
          return canvas;
        },
        
        createImage: function(src?: string): HTMLImageElement {
          const img = new Image();
          if (src) img.src = src;
          return img;
        },
        
        createCanvas: function(canvasName: string): HTMLCanvasElement {
          console.log(`Mock: 创建Canvas - ${canvasName}`);
          const canvas = document.createElement('canvas');
          canvas.id = canvasName;
          canvas.setAttribute('data-mock', 'true');
          // 设置默认尺寸
          canvas.width = 300;
          canvas.height = 200;
          canvas.style.border = '1px dashed #ccc';
          canvas.style.background = '#f9f9f9';
          return canvas;
        }
      },
      
      createOffscreenCanvas: function(width: number, height: number): HTMLCanvasElement {
        console.log(`Mock: 创建离屏Canvas - ${width}x${height}`);
        
        if (typeof OffscreenCanvas !== 'undefined') {
          return new OffscreenCanvas(width, height) as any;
        }
        
        // Fallback to regular canvas
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        canvas.setAttribute('data-offscreen-mock', 'true');
        return canvas;
      },
      
      requestAnimationFrame: function(callback: FrameRequestCallback): number {
        return requestAnimationFrame(callback);
      },
      
      cancelAnimationFrame: function(handle: number): void {
        return cancelAnimationFrame(handle);
      }
    };
  }

  // Mock SystemInfo
  if (typeof (globalThis as any).SystemInfo === 'undefined') {
    (globalThis as any).SystemInfo = {
      pixelRatio: window.devicePixelRatio || 1
    };
  }

  console.log('✅ Lynx环境Mock已创建');
}

/**
 * 安全的LynxChart创建器
 */
export async function createLynxChartSafely(config: LynxChartConfig, options: {
  enableMock?: boolean;
  fallback?: (config: LynxChartConfig) => any;
} = {}) {
  const { enableMock = false, fallback } = options;

  // 1. 环境检查
  const diagnosis = diagnoseLynxEnvironment();
  
  if (!checkLynxEnvironment()) {
    console.warn('Lynx环境不可用:', diagnosis.errors);
    
    // 2. 尝试启用Mock（如果允许）
    if (enableMock && typeof window !== 'undefined') {
      console.log('🔧 启用Lynx环境Mock');
      createLynxEnvironmentMock();
      
      // 重新检查
      if (!checkLynxEnvironment()) {
        throw new Error('Mock环境创建失败');
      }
    } else if (fallback) {
      console.log('🔄 使用fallback方案');
      return fallback(config);
    } else {
      throw new Error(`Lynx环境不可用且未提供fallback方案: ${diagnosis.errors.join(', ')}`);
    }
  }

  // 3. 动态导入LynxChart
  try {
    const { default: LynxChart } = await import('@byted/lynx-lightcharts/src/chart');
    
    console.log(`✅ 创建LynxChart: ${config.canvasName} (${config.width}x${config.height})`);
    const chart = new LynxChart(config);
    
    // 添加销毁方法的安全包装
    const originalDestroy = chart.destroy.bind(chart);
    chart.destroy = function() {
      try {
        originalDestroy();
        console.log(`🗑️  LynxChart已销毁: ${config.canvasName}`);
      } catch (error) {
        console.warn(`销毁LynxChart时出错: ${error.message}`);
      }
    };
    
    return chart;
    
  } catch (error) {
    console.error('LynxChart创建失败:', error);
    
    if (fallback) {
      console.log('🔄 使用fallback方案');
      return fallback(config);
    }
    
    throw new Error(`LynxChart创建失败: ${error.message}`);
  }
}

/**
 * LynxChart兼容性管理器
 */
export class LynxChartManager {
  private charts = new Map<string, any>();
  private mockEnabled = false;

  constructor(private enableMock: boolean = false) {
    this.mockEnabled = enableMock;
  }

  async createChart(config: LynxChartConfig) {
    const chart = await createLynxChartSafely(config, {
      enableMock: this.mockEnabled,
      fallback: this.createFallbackChart.bind(this)
    });

    this.charts.set(config.canvasName, chart);
    return chart;
  }

  destroyChart(canvasName: string): boolean {
    const chart = this.charts.get(canvasName);
    if (chart) {
      try {
        if (typeof chart.destroy === 'function') {
          chart.destroy();
        }
        this.charts.delete(canvasName);
        return true;
      } catch (error) {
        console.error(`销毁图表失败 ${canvasName}:`, error);
        return false;
      }
    }
    return false;
  }

  destroyAllCharts(): void {
    for (const [canvasName] of this.charts) {
      this.destroyChart(canvasName);
    }
  }

  getChart(canvasName: string) {
    return this.charts.get(canvasName);
  }

  getAllCharts() {
    return Array.from(this.charts.values());
  }

  private createFallbackChart(config: LynxChartConfig) {
    console.warn('🔄 使用fallback图表实现');
    
    // 简单的Canvas fallback
    const canvas = document.createElement('canvas');
    canvas.id = config.canvasName;
    canvas.width = config.width;
    canvas.height = config.height;
    canvas.style.border = '2px solid #ff6b6b';
    canvas.style.background = '#ffe3e3';
    
    // 绘制错误提示
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#ff6b6b';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Chart Error', config.width / 2, config.height / 2 - 10);
      ctx.fillText('Lynx环境不可用', config.width / 2, config.height / 2 + 10);
    }

    return {
      canvas,
      setOption: (option: any) => {
        console.warn('Fallback chart - setOption调用被忽略:', option);
      },
      destroy: () => {
        canvas.remove();
      },
      getDom: () => canvas,
      getRenderInstance: () => null
    };
  }
}

/**
 * 预检工具 - 在应用启动时运行
 */
export function performLynxChartPreCheck() {
  console.log('🔍 LynxChart环境预检查');
  
  const diagnosis = diagnoseLynxEnvironment();
  
  console.log('📊 环境诊断结果:');
  console.table(diagnosis);
  
  if (diagnosis.errors.length > 0) {
    console.warn('⚠️  检测到以下问题:', diagnosis.errors);
    console.warn('💡 建议使用 createLynxChartSafely 或启用Mock模式');
  } else {
    console.log('✅ Lynx环境完全可用');
  }
  
  return diagnosis;
}

// 默认导出管理器实例
export const defaultLynxChartManager = new LynxChartManager(false);