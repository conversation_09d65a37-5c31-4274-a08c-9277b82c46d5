/**
 * Prompt Template Manager
 * 提示词模板管理器
 *
 * Responsible for managing LYNX and Web default templates plus user custom templates
 * 负责管理 LYNX 和 Web 的默认模板以及用户自定义模板
 *
 * Features:
 * - Template categorization and organization (模板分类和组织)
 * - Template renaming and customization (模板重命名和自定义)
 * - History tracking and versioning (历史跟踪和版本管理)
 * - Built-in template protection (内置模板保护)
 * - Local storage persistence (本地存储持久化)
 */

import { getPEHTMLPromptContent } from './peHTMLPromptLoader';
import { getMasterLevelLynxPromptContent } from '../prompts/ModularPromptLoader';
import {
  getMasterLevelLynxPromptFromMd,
  checkMdFilesAvailability,
} from './MdPromptLoader';

/**
 * Interface for prompt template objects
 * 提示词模板对象的接口定义
 */
export interface PromptTemplate {
  /** Unique identifier for the template */
  id: string;
  /** Display name of the template */
  name: string;
  /** Template content/prompt text */
  content: string;
  /** Template type classification */
  type: 'lynx' | 'web' | 'custom';
  /** Whether this is the default template for its type */
  isDefault: boolean;
  /** Whether this is a built-in system template */
  isBuiltin: boolean;
  /** Timestamp when template was created */
  createdAt: number;
  /** Timestamp when template was last updated */
  updatedAt: number;
  /** Optional description of template purpose */
  description?: string;
  /** Optional tags for categorization */
  tags?: string[];
}

/**
 * Interface for template category organization
 * 模板分类组织的接口定义
 */
export interface PromptTemplateCategory {
  /** Category display name */
  name: string;
  /** Category type matching template types */
  type: 'lynx' | 'web' | 'custom';
  /** Array of templates in this category */
  templates: PromptTemplate[];
  /** Icon identifier for UI display */
  icon: string;
  /** Category description */
  description: string;
}

/**
 * Static class for managing prompt templates across the application
 * 用于在整个应用程序中管理提示词模板的静态类
 */
export class PromptTemplateManager {
  /** Local storage key for saving custom templates */
  private static readonly STORAGE_KEY = 'batch_processor_prompt_templates';
  /** Local storage key for saving prompt history */
  private static readonly HISTORY_KEY = 'batch_processor_prompt_history';

  /**
   * Get all built-in default templates
   * 获取所有内置默认模板
   *
   * 支持 TypeScript 和 Markdown 两种格式的 prompt 加载
   * 自动检测 MD 文件可用性，优先使用 Markdown 版本
   *
   * @returns Array of built-in template objects
   */
  static getBuiltinTemplates(): PromptTemplate[] {
    return [
      {
        id: 'builtin_lynx_default',
        name: 'LYNX 大师级UI生成 (Template-Assembler v3.0)',
        content: getMasterLevelLynxPromptContent(), // TypeScript 版本
        type: 'lynx',
        isDefault: true,
        isBuiltin: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        description:
          '世界大师级Lynx移动端UI设计专家，集成Template-Assembler v3.0规则和@byted-lynx/web-speedy-plugin完整映射，生成企业级TTML、TTSS和JavaScript代码 (TypeScript版本)',
        tags: [
          'LYNX',
          '大师级',
          'Template-Assembler',
          'Web-Speedy-Plugin',
          'v3.0',
          'TypeScript',
          '内置',
        ],
      },
      {
        id: 'builtin_lynx_md',
        name: 'LYNX 大师级UI生成 (Markdown优化版)',
        content: '', // 动态加载
        type: 'lynx',
        isDefault: false,
        isBuiltin: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        description:
          '世界大师级Lynx移动端UI设计专家，基于Markdown格式优化，解决反引号转义问题，提供更好的维护性和可读性 (推荐版本)',
        tags: [
          'LYNX',
          '大师级',
          'Template-Assembler',
          'Web-Speedy-Plugin',
          'v3.0',
          'Markdown',
          '优化版',
          '内置',
        ],
      },
      {
        id: 'builtin_web_default',
        name: 'HTML 网页生成',
        content: getPEHTMLPromptContent(),
        type: 'web',
        isDefault: true,
        isBuiltin: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        description:
          '生成精美的 HTML 移动端可视化页面，包含完整的 HTML、CSS 和 JavaScript',
        tags: ['HTML', '网页', '可视化', '内置'],
      },
    ];
  }

  /**
   * 获取用户自定义模板
   */
  static getUserTemplates(): PromptTemplate[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) {
        return [];
      }

      const templates = JSON.parse(stored) as PromptTemplate[];
      return templates.filter(t => !t.isBuiltin);
    } catch (error) {
      console.error('[PromptTemplateManager] 加载用户模板失败:', error);
      return [];
    }
  }

  /**
   * 获取所有模板（内置 + 用户自定义）
   */
  static getAllTemplates(): PromptTemplate[] {
    const builtin = this.getBuiltinTemplates();
    const user = this.getUserTemplates();

    return [...builtin, ...user].sort((a, b) => {
      // 内置模板优先，然后按更新时间排序
      if (a.isBuiltin && !b.isBuiltin) {
        return -1;
      }
      if (!a.isBuiltin && b.isBuiltin) {
        return 1;
      }
      return b.updatedAt - a.updatedAt;
    });
  }

  /**
   * 根据类型获取模板分类
   */
  static getTemplateCategories(): PromptTemplateCategory[] {
    const allTemplates = this.getAllTemplates();

    return [
      {
        name: 'LYNX 模板',
        type: 'lynx',
        icon: 'mobile',
        description: '生成 LYNX 小程序代码的专用模板',
        templates: allTemplates.filter(t => t.type === 'lynx'),
      },
      {
        name: 'Web 模板',
        type: 'web',
        icon: 'web',
        description: '生成 HTML 网页代码的专用模板',
        templates: allTemplates.filter(t => t.type === 'web'),
      },
      {
        name: '自定义模板',
        type: 'custom',
        icon: 'settings',
        description: '用户创建的自定义提示词模板',
        templates: allTemplates.filter(t => t.type === 'custom'),
      },
    ];
  }

  /**
   * 根据类型获取默认模板
   * 直接从对应的 promptloader 读取内容
   */
  static getDefaultTemplateByType(type: 'lynx' | 'web'): PromptTemplate | null {
    const builtinTemplates = this.getBuiltinTemplates();
    return builtinTemplates.find(t => t.type === type && t.isDefault) || null;
  }

  /**
   * 直接获取 promptloader 内容
   * 支持 TypeScript 和 Markdown 两种格式
   */
  static getPromptLoaderContent(type: 'lynx' | 'web'): string {
    if (type === 'lynx') {
      return getMasterLevelLynxPromptContent(); // TypeScript 版本
    } else {
      return getPEHTMLPromptContent();
    }
  }

  /**
   * 异步获取 Markdown 版本的 promptloader 内容 - 严格版本
   * 任何错误都直接抛出，禁止回退机制
   */
  static async getPromptLoaderContentFromMd(
    type: 'lynx' | 'web',
  ): Promise<string> {
    if (type === 'lynx') {
      return await getMasterLevelLynxPromptFromMd(); // 直接调用，不捕获错误
    } else {
      return getPEHTMLPromptContent(); // Web 版本暂时保持 TypeScript
    }
  }

  /**
   * 获取模板内容 (支持动态加载) - 严格版本
   * 任何错误都直接抛出，禁止回退机制
   */
  static async getTemplateContent(template: PromptTemplate): Promise<string> {
    // 如果是 Markdown 版本的 Lynx 模板，动态加载内容
    if (template.id === 'builtin_lynx_md') {
      return await getMasterLevelLynxPromptFromMd(); // 直接调用，不捕获错误
    }

    // 其他模板直接返回已有内容
    return template.content;
  }

  /**
   * 检查 Markdown 文件可用性并更新默认模板
   */
  static async updateDefaultTemplate(): Promise<void> {
    const { available, unavailable } = await checkMdFilesAvailability();

    if (available.length > 0) {
      console.log('[PromptTemplateManager] Markdown文件可用，推荐使用优化版本');

      // 如果 Markdown 文件可用，将 Markdown 版本设为默认
      // 这里可以通过本地存储或配置来记录用户偏好
      localStorage.setItem('preferred_lynx_template', 'builtin_lynx_md');
    } else {
      console.log(
        '[PromptTemplateManager] Markdown文件不可用，使用TypeScript版本',
      );
      localStorage.setItem('preferred_lynx_template', 'builtin_lynx_default');
    }
  }

  /**
   * 安全保存到localStorage - 支持自动清理
   */
  private static safeSetItem(key: string, value: string): boolean {
    try {
      localStorage.setItem(key, value);
      return true;
    } catch (error) {
      if (
        error.name === 'QuotaExceededError' ||
        error.message.includes('quota')
      ) {
        console.warn('[PromptTemplateManager] 存储空间不足，尝试清理用户模板');

        // 尝试清理旧的用户模板
        try {
          const userTemplates = this.getUserTemplates();
          if (userTemplates.length > 10) {
            // 按创建时间排序，保留最新的10个
            const sortedTemplates = userTemplates.sort(
              (a, b) => b.createdAt - a.createdAt,
            );
            const trimmedTemplates = sortedTemplates.slice(0, 10);

            localStorage.setItem(
              this.STORAGE_KEY,
              JSON.stringify(trimmedTemplates),
            );
            console.log(
              `[PromptTemplateManager] 清理完成: 保留${trimmedTemplates.length}个模板`,
            );

            // 清理后重试保存
            try {
              localStorage.setItem(key, value);
              console.log('[PromptTemplateManager] 清理空间后成功保存');
              return true;
            } catch (retryError) {
              console.error(
                '[PromptTemplateManager] 清理空间后仍无法保存:',
                retryError,
              );
              return false;
            }
          } else {
            console.error(
              '[PromptTemplateManager] 模板数量不多，无法进一步清理',
            );
            return false;
          }
        } catch (cleanupError) {
          console.error('[PromptTemplateManager] 清理模板失败:', cleanupError);
          return false;
        }
      } else {
        console.error('[PromptTemplateManager] 保存失败:', error);
        return false;
      }
    }
  }

  /**
   * 保存用户模板 - 支持LRU自动清理
   */
  static saveUserTemplate(
    template: Omit<PromptTemplate, 'id' | 'createdAt' | 'updatedAt'>,
  ): PromptTemplate {
    const userTemplates = this.getUserTemplates();

    const newTemplate: PromptTemplate = {
      ...template,
      id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      isBuiltin: false,
    };

    userTemplates.push(newTemplate);

    const success = this.safeSetItem(
      this.STORAGE_KEY,
      JSON.stringify(userTemplates),
    );

    if (success) {
      console.log(
        '[PromptTemplateManager] 保存用户模板成功:',
        newTemplate.name,
      );
    } else {
      console.warn(
        '[PromptTemplateManager] 保存用户模板失败，但模板已创建:',
        newTemplate.name,
      );
      // 不抛出错误，返回模板对象，让用户可以继续使用
    }

    return newTemplate;
  }

  /**
   * 更新用户模板
   */
  static updateUserTemplate(
    id: string,
    updates: Partial<PromptTemplate>,
  ): boolean {
    const userTemplates = this.getUserTemplates();
    const index = userTemplates.findIndex(t => t.id === id);

    if (index === -1) {
      console.warn('[PromptTemplateManager] 模板不存在:', id);
      return false;
    }

    userTemplates[index] = {
      ...userTemplates[index],
      ...updates,
      updatedAt: Date.now(),
    };

    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(userTemplates));
      console.log(
        '[PromptTemplateManager] 更新用户模板成功:',
        userTemplates[index].name,
      );
      return true;
    } catch (error) {
      console.error('[PromptTemplateManager] 更新用户模板失败:', error);
      return false;
    }
  }

  /**
   * 删除用户模板
   */
  static deleteUserTemplate(id: string): boolean {
    const userTemplates = this.getUserTemplates();
    const index = userTemplates.findIndex(t => t.id === id);

    if (index === -1) {
      console.warn('[PromptTemplateManager] 模板不存在:', id);
      return false;
    }

    const deletedTemplate = userTemplates.splice(index, 1)[0];

    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(userTemplates));
      console.log(
        '[PromptTemplateManager] 删除用户模板成功:',
        deletedTemplate.name,
      );
      return true;
    } catch (error) {
      console.error('[PromptTemplateManager] 删除用户模板失败:', error);
      return false;
    }
  }

  /**
   * 重命名模板
   */
  static renameTemplate(id: string, newName: string): boolean {
    return this.updateUserTemplate(id, { name: newName.trim() });
  }

  /**
   * 复制模板（将内置模板复制为用户模板，或复制用户模板）
   */
  static duplicateTemplate(
    id: string,
    newName?: string,
  ): PromptTemplate | null {
    const allTemplates = this.getAllTemplates();
    const sourceTemplate = allTemplates.find(t => t.id === id);

    if (!sourceTemplate) {
      console.warn('[PromptTemplateManager] 源模板不存在:', id);
      return null;
    }

    const duplicatedTemplate = this.saveUserTemplate({
      name: newName || `${sourceTemplate.name} (副本)`,
      content: sourceTemplate.content,
      type: sourceTemplate.type,
      isDefault: false,
      isBuiltin: false,
      description: sourceTemplate.description,
      tags: [...(sourceTemplate.tags || []), '副本'],
    });

    return duplicatedTemplate;
  }

  /**
   * 搜索模板
   */
  static searchTemplates(
    query: string,
    type?: 'lynx' | 'web' | 'custom',
  ): PromptTemplate[] {
    const allTemplates = this.getAllTemplates();
    const lowerQuery = query.toLowerCase();

    return allTemplates.filter(template => {
      // 类型过滤
      if (type && template.type !== type) {
        return false;
      }

      // 文本搜索
      return (
        template.name.toLowerCase().includes(lowerQuery) ||
        template.description?.toLowerCase().includes(lowerQuery) ||
        template.tags?.some(tag => tag.toLowerCase().includes(lowerQuery)) ||
        template.content.toLowerCase().includes(lowerQuery)
      );
    });
  }

  /**
   * 获取模板统计信息
   */
  static getTemplateStats() {
    const allTemplates = this.getAllTemplates();
    const userTemplates = this.getUserTemplates();

    return {
      total: allTemplates.length,
      builtin: allTemplates.filter(t => t.isBuiltin).length,
      user: userTemplates.length,
      lynx: allTemplates.filter(t => t.type === 'lynx').length,
      web: allTemplates.filter(t => t.type === 'web').length,
      custom: allTemplates.filter(t => t.type === 'custom').length,
    };
  }

  /**
   * 导出用户模板
   */
  static exportUserTemplates(): string {
    const userTemplates = this.getUserTemplates();
    return JSON.stringify(
      {
        version: '1.0.0',
        exportDate: new Date().toISOString(),
        templates: userTemplates,
      },
      null,
      2,
    );
  }

  /**
   * 导入用户模板
   */
  static importUserTemplates(jsonData: string): {
    success: boolean;
    imported: number;
    errors: string[];
  } {
    try {
      const data = JSON.parse(jsonData);
      const errors: string[] = [];
      let imported = 0;

      if (!data.templates || !Array.isArray(data.templates)) {
        throw new Error('无效的模板数据格式');
      }

      for (const template of data.templates) {
        try {
          // 验证必需字段
          if (!template.name || !template.content) {
            errors.push(`模板 "${template.name || '未命名'}" 缺少必需字段`);
            continue;
          }

          // 避免重复导入
          const existing = this.getUserTemplates().find(
            t => t.name === template.name,
          );
          if (existing) {
            errors.push(`模板 "${template.name}" 已存在，跳过导入`);
            continue;
          }

          this.saveUserTemplate({
            name: template.name,
            content: template.content,
            type: template.type || 'custom',
            isDefault: false,
            isBuiltin: false,
            description: template.description,
            tags: template.tags,
          });

          imported++;
        } catch (templateError) {
          errors.push(
            `导入模板 "${template.name || '未命名'}" 失败: ${templateError}`,
          );
        }
      }

      return { success: true, imported, errors };
    } catch (error) {
      return {
        success: false,
        imported: 0,
        errors: [
          `导入失败: ${error instanceof Error ? error.message : String(error)}`,
        ],
      };
    }
  }

  /**
   * 清理过期或无效的模板
   */
  static cleanupTemplates(): { cleaned: number; errors: string[] } {
    const userTemplates = this.getUserTemplates();
    const errors: string[] = [];
    let cleaned = 0;

    const validTemplates = userTemplates.filter(template => {
      // 检查必需字段
      if (!template.name || !template.content || !template.id) {
        errors.push(`清理无效模板: ${template.name || '未命名'}`);
        cleaned++;
        return false;
      }

      // 检查内容长度
      if (template.content.length < 10) {
        errors.push(`清理内容过短的模板: ${template.name}`);
        cleaned++;
        return false;
      }

      return true;
    });

    if (cleaned > 0) {
      try {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(validTemplates));
      } catch (error) {
        errors.push(`保存清理结果失败: ${error}`);
      }
    }

    return { cleaned, errors };
  }
}

// 历史记录管理
export class PromptHistoryManager {
  private static readonly HISTORY_KEY = 'batch_processor_prompt_history';
  private static readonly MAX_HISTORY = 50;

  /**
   * 清理localStorage空间 - 针对历史记录的LRU策略
   */
  private static cleanupHistoryStorage(): boolean {
    try {
      console.log('[PromptHistoryManager] 开始清理历史记录存储空间');

      // 获取当前历史记录
      const currentHistory = this.getHistory();

      if (currentHistory.length === 0) {
        return false;
      }

      // 按最后使用时间排序，保留最近使用的一半
      const sortedHistory = currentHistory.sort(
        (a, b) => b.lastUsed - a.lastUsed,
      );
      const keepCount = Math.max(Math.floor(this.MAX_HISTORY / 2), 10); // 至少保留10条
      const trimmedHistory = sortedHistory.slice(0, keepCount);

      // 尝试保存精简后的历史记录
      try {
        localStorage.setItem(this.HISTORY_KEY, JSON.stringify(trimmedHistory));
        console.log(
          `[PromptHistoryManager] 清理完成: 保留${keepCount}条记录，删除${currentHistory.length - keepCount}条`,
        );
        return true;
      } catch (error) {
        // 如果还是失败，进一步减少
        const minimalHistory = sortedHistory.slice(0, 5); // 只保留最近5条
        try {
          localStorage.setItem(
            this.HISTORY_KEY,
            JSON.stringify(minimalHistory),
          );
          console.log('[PromptHistoryManager] 极简清理完成: 只保留5条最新记录');
          return true;
        } catch (finalError) {
          // 最后手段：清空历史记录
          localStorage.removeItem(this.HISTORY_KEY);
          console.log('[PromptHistoryManager] 清空所有历史记录以释放空间');
          return true;
        }
      }
    } catch (error) {
      console.error('[PromptHistoryManager] 清理存储空间失败:', error);
      return false;
    }
  }

  /**
   * 安全保存到localStorage - 支持自动清理
   */
  private static safeSetItem(key: string, value: string): boolean {
    try {
      localStorage.setItem(key, value);
      return true;
    } catch (error) {
      if (
        error.name === 'QuotaExceededError' ||
        error.message.includes('quota')
      ) {
        console.warn('[PromptHistoryManager] 存储空间不足，尝试清理空间');

        // 尝试清理空间
        const cleanupSuccess = this.cleanupHistoryStorage();

        if (cleanupSuccess) {
          try {
            // 清理后重试
            localStorage.setItem(key, value);
            console.log('[PromptHistoryManager] 清理空间后成功保存');
            return true;
          } catch (retryError) {
            console.error(
              '[PromptHistoryManager] 清理空间后仍无法保存:',
              retryError,
            );
            return false;
          }
        } else {
          console.error('[PromptHistoryManager] 清理空间失败');
          return false;
        }
      } else {
        console.error('[PromptHistoryManager] 保存失败:', error);
        return false;
      }
    }
  }

  /**
   * 添加到历史记录 - 支持LRU自动清理
   */
  static addToHistory(prompt: string): void {
    if (!prompt.trim()) {
      return;
    }

    try {
      const history = this.getHistory();

      // 去重：如果已存在相同内容，先移除
      const filtered = history.filter(item => item.content !== prompt);

      // 添加到顶部
      const newItem = {
        id: `history_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        content: prompt,
        name: this.generateHistoryName(prompt),
        createdAt: Date.now(),
        lastUsed: Date.now(),
      };

      filtered.unshift(newItem);

      // 限制历史记录数量
      const trimmed = filtered.slice(0, this.MAX_HISTORY);

      // 使用安全保存方法
      const success = this.safeSetItem(
        this.HISTORY_KEY,
        JSON.stringify(trimmed),
      );

      if (!success) {
        console.warn(
          '[PromptHistoryManager] 历史记录保存失败，但不影响功能使用',
        );
      }
    } catch (error) {
      console.error('[PromptHistoryManager] 添加历史记录失败:', error);
      // 不抛出错误，避免影响用户操作
    }
  }

  /**
   * 获取历史记录
   */
  static getHistory(): Array<{
    id: string;
    content: string;
    name: string;
    createdAt: number;
    lastUsed: number;
  }> {
    try {
      const stored = localStorage.getItem(this.HISTORY_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('[PromptHistoryManager] 加载历史记录失败:', error);
      return [];
    }
  }

  /**
   * 重命名历史记录
   */
  static renameHistoryItem(id: string, newName: string): boolean {
    try {
      const history = this.getHistory();
      const index = history.findIndex(item => item.id === id);

      if (index === -1) {
        return false;
      }

      history[index].name = newName.trim();
      return this.safeSetItem(this.HISTORY_KEY, JSON.stringify(history));
    } catch (error) {
      console.error('[PromptHistoryManager] 重命名历史记录失败:', error);
      return false;
    }
  }

  /**
   * 删除历史记录项
   */
  static deleteHistoryItem(id: string): boolean {
    try {
      const history = this.getHistory();
      const filtered = history.filter(item => item.id !== id);

      return this.safeSetItem(this.HISTORY_KEY, JSON.stringify(filtered));
    } catch (error) {
      console.error('[PromptHistoryManager] 删除历史记录失败:', error);
      return false;
    }
  }

  /**
   * 清空历史记录
   */
  static clearHistory(): boolean {
    try {
      localStorage.removeItem(this.HISTORY_KEY);
      return true;
    } catch (error) {
      console.error('[PromptHistoryManager] 清空历史记录失败:', error);
      return false;
    }
  }

  /**
   * 更新使用时间
   */
  static updateLastUsed(id: string): void {
    try {
      const history = this.getHistory();
      const index = history.findIndex(item => item.id === id);

      if (index !== -1) {
        history[index].lastUsed = Date.now();
        const success = this.safeSetItem(
          this.HISTORY_KEY,
          JSON.stringify(history),
        );
        if (!success) {
          console.warn(
            '[PromptHistoryManager] 更新使用时间失败，但不影响功能使用',
          );
        }
      }
    } catch (error) {
      console.error('[PromptHistoryManager] 更新使用时间失败:', error);
      // 不抛出错误，避免影响用户操作
    }
  }

  /**
   * 生成历史记录名称
   */
  private static generateHistoryName(prompt: string): string {
    const lines = prompt.split('\n').filter(line => line.trim());

    // 尝试提取第一行作为标题
    const firstLine = lines[0]?.trim();
    if (firstLine && firstLine.length > 0) {
      // 移除常见的标记符号
      let title = firstLine.replace(/^[#*\-\s]+/, '').trim();

      // 限制长度
      if (title.length > 30) {
        title = `${title.substring(0, 30)}...`;
      }

      return title || '未命名提示词';
    }

    // 如果没有明显的标题，使用时间戳
    return `提示词 ${new Date().toLocaleString()}`;
  }
}
