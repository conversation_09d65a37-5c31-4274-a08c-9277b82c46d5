/**
 * 基于 Prompts 的智能转换系统
 * 
 * 核心特性：
 * - 完全整合 prompts 中的设计规范
 * - 智能化的 UI 转换逻辑
 * - 基于内容类型的可视化选型
 * - 符合杂志化设计理念
 * - 严格遵循 P0 级治理标准
 */

import { parse, serialize } from 'parse5';
import type { Element, Document } from 'parse5/dist/tree-adapters/default';

// 导入 prompts 中的设计规范
import { LYNX_FRAMEWORK_CORE } from '../prompts/LynxFrameworkCore';
import { LYNX_COMPONENTS } from '../prompts/LynxComponents';
import { UNIFIED_UI_VISUALIZATION_GUIDANCE } from '../prompts/UnifiedUIVisualizationGuidance';
import { TTML_STRICT_CONSTRAINTS } from '../prompts/TTMLStrictConstraints';
import { TTSS_STRICT_CONSTRAINTS } from '../prompts/TTSSStrictConstraints';
import { LYNX_STYLE_SYSTEM } from '../prompts/LynxStyleSystem';

/**
 * 智能转换配置
 */
interface SmartConversionConfig {
  // 内容分析配置
  enableContentAnalysis: boolean;
  enableIntelligentVisualization: boolean;

  // 设计规范配置
  enforceUnifiedUIVisualization: boolean;
  enforceP0Standards: boolean;
  enableMagazineDesign: boolean;

  // 转换优化配置
  enableSmartTemplating: boolean;
  enableContextualSamples: boolean;
  enableResponsiveDesign: boolean;

  // 调试配置
  enableDebugMode: boolean;
  enablePromptValidation: boolean;
}

/**
 * 默认配置
 */
const DEFAULT_SMART_CONFIG: SmartConversionConfig = {
  enableContentAnalysis: true,
  enableIntelligentVisualization: true,
  enforceUnifiedUIVisualization: true,
  enforceP0Standards: true,
  enableMagazineDesign: true,
  enableSmartTemplating: true,
  enableContextualSamples: true,
  enableResponsiveDesign: true,
  enableDebugMode: true,
  enablePromptValidation: true,
};

/**
 * 内容类型枚举
 */
enum ContentType {
  HIERARCHY = 'hierarchy', // 层级关系
  PROCESS = 'process', // 流程顺序
  COMPARISON = 'comparison', // 对比分析
  DATA = 'data', // 数据呈现
  CONCEPT = 'concept', // 概念解释
  TIMELINE = 'timeline', // 时间轴
  MIXED = 'mixed', // 混合内容
}

/**
 * 可视化组件类型
 */
enum VisualizationType {
  MIND_MAP = 'mind_map', // 思维导图
  TREE_CHART = 'tree_chart', // 树状图
  FLOW_CHART = 'flow_chart', // 流程图
  TIMELINE = 'timeline', // 时间轴
  COMPARISON_TABLE = 'comparison_table', // 对比表格
  DATA_CHART = 'data_chart', // 数据图表
  CONCEPT_CARD = 'concept_card', // 概念卡片
  LIST_VIEW = 'list_view', // 列表视图
}

/**
 * 基于 Prompts 的智能转换器
 */
export class PromptsBasedConverter {
  private config: SmartConversionConfig;
  private contentAnalyzer: ContentAnalyzer;
  private uiGuidelineEnforcer: UIGuidelineEnforcer;
  private smartTemplateProcessor: SmartTemplateProcessor;
  private responsiveDesignGenerator: ResponsiveDesignGenerator;

  constructor(config: Partial<SmartConversionConfig> = {}) {
    this.config = { ...DEFAULT_SMART_CONFIG, ...config };
    this.contentAnalyzer = new ContentAnalyzer();
    this.uiGuidelineEnforcer = new UIGuidelineEnforcer();
    this.smartTemplateProcessor = new SmartTemplateProcessor();
    this.responsiveDesignGenerator = new ResponsiveDesignGenerator();
  }

  /**
   * 智能转换主方法
   */
  async convertWithPrompts(
    ttml: string,
    ttss: string,
    js: string,
    query?: string,
  ): Promise<{
    success: boolean;
    html: string;
    css: string;
    javascript: string;
    errors: string[];
    insights: ConversionInsights;
  }> {
    const startTime = performance.now();
    const errors: string[] = [];

    try {
      console.log('🚀 [PromptsBasedConverter] 开始基于 Prompts 的智能转换');

      // 1. 内容分析和类型识别
      const contentAnalysis = await this.contentAnalyzer.analyze(
        ttml,
        ttss,
        query,
      );
      console.log('📊 [ContentAnalyzer] 内容类型:', contentAnalysis.type);
      console.log(
        '🎯 [ContentAnalyzer] 推荐可视化:',
        contentAnalysis.recommendedVisualization,
      );

      // 2. 智能模板处理
      const processedTtml = await this.smartTemplateProcessor.process(
        ttml,
        contentAnalysis,
      );

      // 3. 基于 UI 指导原则的样式增强
      const enhancedTtss = await this.uiGuidelineEnforcer.enhance(
        ttss,
        contentAnalysis,
      );

      // 4. 解析和转换
      const document = parse(processedTtml);
      const transformedHtml = await this.transformWithUnifiedUIVisualization(
        document,
        contentAnalysis,
      );

      // 5. 生成响应式设计
      const responsiveCss = await this.responsiveDesignGenerator.generate(
        enhancedTtss,
        contentAnalysis,
      );

      // 6. 生成完整的 HTML 文档
      const completeHtml = this.generatePromptsCompliantHTML(
        transformedHtml,
        responsiveCss,
        js,
        contentAnalysis,
      );

      const processingTime = performance.now() - startTime;

      console.log('✅ [PromptsBasedConverter] 转换完成');
      console.log('⏱️ [性能] 处理时间:', processingTime.toFixed(2) + 'ms');

      return {
        success: true,
        html: completeHtml,
        css: responsiveCss,
        javascript: js,
        errors,
        insights: {
          contentType: contentAnalysis.type,
          visualizationType: contentAnalysis.recommendedVisualization,
          processingTime,
          optimizations: contentAnalysis.optimizations,
        },
      };
    } catch (error) {
      console.error('❌ [PromptsBasedConverter] 转换失败:', error);
      errors.push(error instanceof Error ? error.message : String(error));

      return {
        success: false,
        html: '',
        css: '',
        javascript: '',
        errors,
        insights: {
          contentType: ContentType.MIXED,
          visualizationType: VisualizationType.LIST_VIEW,
          processingTime: performance.now() - startTime,
          optimizations: [],
        },
      };
    }
  }

  /**
   * 基于统一UI可视化指导原则的 HTML 转换
   */
  private async transformWithUnifiedUIVisualization(
    document: Document,
    contentAnalysis: ContentAnalysis,
  ): Promise<string> {
    console.log('🎨 [UnifiedUIVisualization] 应用统一UI设计与可视化规范');

    // 递归处理所有节点
    this.processNodeWithUnifiedUIVisualization(document, contentAnalysis);

    // 🚨 CRITICAL FIX: 使用自定义序列化器来正确输出HTML
    const serializedHtml = this.serializeWithComponentMapping(document);

    return serializedHtml;
  }

  /**
   * 递归处理节点，应用统一UI可视化指导原则
   */
  private processNodeWithUnifiedUIVisualization(
    node: any,
    contentAnalysis: ContentAnalysis,
  ): void {
    if (
      node.nodeName &&
      node.nodeName !== '#text' &&
      node.nodeName !== '#comment'
    ) {
      // 应用组件映射
      this.applyComponentMapping(node);

      // 应用 UI 指导原则
      this.applyUIGuidelines(node, contentAnalysis);

      // 添加可视化增强
      this.addVisualizationEnhancements(node, contentAnalysis);
    }

    // 递归处理子节点
    if (node.childNodes) {
      for (const child of node.childNodes) {
        this.processNodeWithUnifiedUIVisualization(child, contentAnalysis);
      }
    }
  }

  /**
   * 应用组件映射
   */
  private applyComponentMapping(node: any): void {
    // 🚨 CRITICAL: 完整的组件映射表（遵循 prompts 中的严格组件规则）
    const componentMappings = {
      view: 'div',
      text: 'span',
      'scroll-view': 'div',
      image: 'img',
      button: 'button',
      input: 'input',
      textarea: 'textarea',
      swiper: 'div',
      'swiper-item': 'div',
      canvas: 'canvas',
      video: 'video',
      audio: 'audio',
      navigator: 'a',
      'rich-text': 'div',
      progress: 'div',
      slider: 'input',
      switch: 'input',
      checkbox: 'input',
      radio: 'input',
      picker: 'select',
      'picker-view': 'div',
      form: 'form',
      'web-view': 'iframe',
      map: 'div',
      'cover-view': 'div',
      'cover-image': 'img',
      'movable-view': 'div',
      'movable-area': 'div',
    };

    if (componentMappings[node.nodeName]) {
      const originalTag = node.nodeName;
      node.nodeName = componentMappings[node.nodeName];

      // 添加 Lynx 类名和数据属性
      this.addOrUpdateAttribute(node, 'class', `lynx-${originalTag}`);
      this.addOrUpdateAttribute(node, 'data-lynx-component', originalTag);

      // 🚨 MANDATORY: 为 scroll-view 添加必要的滚动属性
      if (originalTag === 'scroll-view') {
        this.addOrUpdateAttribute(
          node,
          'style',
          'overflow: auto; -webkit-overflow-scrolling: touch;',
        );
        this.addOrUpdateAttribute(node, 'data-scroll-y', 'true');
      }

      // 🚨 MANDATORY: 处理自闭合标签
      if (
        [
          'image',
          'input',
          'progress',
          'checkbox',
          'radio',
          'slider',
          'switch',
        ].includes(originalTag)
      ) {
        node.selfClosing = true;
      }

      // 🚨 MANDATORY: 处理事件绑定转换
      this.processEventBindings(node);
    }
  }

  /**
   * 🚨 MANDATORY: 处理事件绑定转换（bindtap -> onclick）
   */
  private processEventBindings(node: any): void {
    if (!node.attrs) return;

    node.attrs.forEach((attr: any) => {
      // 转换 bindtap -> onclick
      if (attr.name === 'bindtap' || attr.name === 'bindtap') {
        attr.name = 'onclick';
        attr.value = `${attr.value}(event)`;
      }

      // 转换 catch:tap -> onclick（阻止冒泡）
      if (attr.name === 'catch:tap' || attr.name === 'catchtap') {
        attr.name = 'onclick';
        attr.value = `${attr.value}(event); event.stopPropagation();`;
      }

      // 转换其他事件绑定
      if (attr.name.startsWith('bind')) {
        const eventName = attr.name.replace('bind', '');
        attr.name = `on${eventName}`;
      }

      if (attr.name.startsWith('catch:')) {
        const eventName = attr.name.replace('catch:', '');
        attr.name = `on${eventName}`;
        attr.value = `${attr.value}(event); event.stopPropagation();`;
      }
    });
  }

  /**
   * 🚨 CRITICAL FIX: 自定义序列化器，正确输出 HTML 而不是 Lynx 标签
   */
  private serializeWithComponentMapping(document: Document): string {
    const body =
      document.childNodes
        .find((node: any) => node.nodeName === 'html')
        ?.childNodes?.find((node: any) => node.nodeName === 'body') || document;

    return this.serializeNode(body);
  }

  /**
   * 递归序列化节点，应用组件映射
   */
  private serializeNode(node: any): string {
    if (node.nodeName === '#text') {
      return this.escapeXMLEntities(node.value || '');
    }

    if (node.nodeName === '#comment') {
      return `<!-- ${node.data} -->`;
    }

    if (node.nodeName === 'html' || node.nodeName === 'body') {
      // 跳过 html 和 body 标签，直接处理子节点
      return node.childNodes
        ? node.childNodes
            .map((child: any) => this.serializeNode(child))
            .join('')
        : '';
    }

    // 🚨 CRITICAL: 应用组件映射到序列化
    const componentMappings = {
      view: 'div',
      text: 'span',
      'scroll-view': 'div',
      image: 'img',
      button: 'button',
      input: 'input',
      textarea: 'textarea',
      swiper: 'div',
      'swiper-item': 'div',
      canvas: 'canvas',
      video: 'video',
      audio: 'audio',
      navigator: 'a',
      'rich-text': 'div',
      progress: 'div',
      slider: 'input',
      switch: 'input',
      checkbox: 'input',
      radio: 'input',
      picker: 'select',
      'picker-view': 'div',
      form: 'form',
      'web-view': 'iframe',
      map: 'div',
      'cover-view': 'div',
      'cover-image': 'img',
      'movable-view': 'div',
      'movable-area': 'div',
    };

    const originalTag = node.nodeName;
    const mappedTag = componentMappings[originalTag] || originalTag;

    // 构建属性字符串
    let attributes = '';
    if (node.attrs) {
      const attrPairs = node.attrs.map((attr: any) => {
        // 🚨 MANDATORY: 处理事件绑定转换
        let attrName = attr.name;
        let attrValue = attr.value || '';

        if (attrName === 'bindtap' || attrName === 'bindtap') {
          attrName = 'onclick';
          attrValue = `${attrValue}(event)`;
        } else if (attrName === 'catch:tap' || attrName === 'catchtap') {
          attrName = 'onclick';
          attrValue = `${attrValue}(event); event.stopPropagation();`;
        } else if (attrName.startsWith('bind')) {
          const eventName = attrName.replace('bind', '');
          attrName = `on${eventName}`;
        } else if (attrName.startsWith('catch:')) {
          const eventName = attrName.replace('catch:', '');
          attrName = `on${eventName}`;
          attrValue = `${attrValue}(event); event.stopPropagation();`;
        }

        return `${attrName}="${this.escapeXMLEntities(attrValue)}"`;
      });

      // 添加 Lynx 类名和数据属性
      if (componentMappings[originalTag]) {
        attrPairs.push(`class="lynx-${originalTag}"`);
        attrPairs.push(`data-lynx-component="${originalTag}"`);

        // 🚨 MANDATORY: 为 scroll-view 添加必要的滚动属性
        if (originalTag === 'scroll-view') {
          attrPairs.push(
            'style="overflow: auto; -webkit-overflow-scrolling: touch;"',
          );
          attrPairs.push('data-scroll-y="true"');
        }
      }

      attributes = attrPairs.length > 0 ? ' ' + attrPairs.join(' ') : '';
    }

    // 🚨 MANDATORY: 处理自闭合标签
    const selfClosingTags = [
      'img',
      'input',
      'br',
      'hr',
      'meta',
      'link',
      'source',
      'track',
      'embed',
      'area',
      'base',
      'col',
      'param',
      'wbr',
    ];
    const isSelfClosing =
      selfClosingTags.includes(mappedTag) ||
      [
        'image',
        'input',
        'progress',
        'checkbox',
        'radio',
        'slider',
        'switch',
      ].includes(originalTag);

    if (isSelfClosing) {
      return `<${mappedTag}${attributes} />`;
    }

    // 处理子节点
    const childrenHtml = node.childNodes
      ? node.childNodes.map((child: any) => this.serializeNode(child)).join('')
      : '';

    return `<${mappedTag}${attributes}>${childrenHtml}</${mappedTag}>`;
  }

  /**
   * 添加或更新节点属性
   */
  private addOrUpdateAttribute(node: any, name: string, value: string): void {
    if (!node.attrs) {
      node.attrs = [];
    }

    const existingAttr = node.attrs.find((attr: any) => attr.name === name);
    if (existingAttr) {
      // 如果是 class 属性，进行合并
      if (name === 'class') {
        const existingClasses = existingAttr.value.split(' ').filter(Boolean);
        const newClasses = value.split(' ').filter(Boolean);
        const mergedClasses = [...new Set([...existingClasses, ...newClasses])];
        existingAttr.value = mergedClasses.join(' ');
      } else {
        existingAttr.value = value;
      }
    } else {
      node.attrs.push({ name, value });
    }
  }

  /**
   * 🚨 MANDATORY: 处理 TTML 文字转义（遵循 prompts 中的 XML 实体转义规则）
   */
  private escapeXMLEntities(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
  }

  /**
   * 应用 UI 指导原则
   */
  private applyUIGuidelines(node: any, contentAnalysis: ContentAnalysis): void {
    // 根据内容类型应用特定的 UI 指导原则
    switch (contentAnalysis.type) {
      case ContentType.TIMELINE:
        this.applyTimelineGuidelines(node);
        break;
      case ContentType.COMPARISON:
        this.applyComparisonGuidelines(node);
        break;
      case ContentType.DATA:
        this.applyDataVisualizationGuidelines(node);
        break;
      default:
        this.applyGeneralGuidelines(node);
    }
  }

  /**
   * 添加可视化增强
   */
  private addVisualizationEnhancements(
    node: any,
    contentAnalysis: ContentAnalysis,
  ): void {
    const visualizationType = contentAnalysis.recommendedVisualization;

    switch (visualizationType) {
      case VisualizationType.TIMELINE:
        this.addTimelineEnhancements(node);
        break;
      case VisualizationType.COMPARISON_TABLE:
        this.addComparisonTableEnhancements(node);
        break;
      case VisualizationType.DATA_CHART:
        this.addDataChartEnhancements(node);
        break;
      default:
        this.addGeneralEnhancements(node);
    }
  }

  /**
   * 生成符合 Prompts 的完整 HTML 文档
   */
  private generatePromptsCompliantHTML(
    html: string,
    css: string,
    js: string,
    contentAnalysis: ContentAnalysis,
  ): string {
    const bodyContent = html
      .replace(/<\/?(?:html|head|body)[^>]*>/g, '')
      .trim();

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>智能 Lynx 预览</title>
  <style>
    /* 🎨 基于 Prompts 的设计规范 */
    /* P0 级治理标准：白色背景、无阴影、轻量调性 */
    
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
      background: #ffffff; /* P0: 只允许白色背景 */
      color: #333333;
      line-height: 1.6;
      font-size: 16px;
      overflow-x: hidden;
    }
    
    /* 🎨 杂志化设计与主题性 */
    .lynx-magazine-container {
      max-width: 375px;
      margin: 0 auto;
      padding: 20px;
      background: #ffffff;
      /* P0: 禁止阴影 */
      border-radius: 0;
    }
    
    /* 🎨 适应性原则：动态适配 */
    @media (max-width: 375px) {
      .lynx-magazine-container {
        max-width: 100%;
        padding: 16px;
      }
    }
    
    /* 🎨 调性原则：轻量、整洁、干净 */
    .lynx-card {
      background: #ffffff;
      border-radius: 8px;
      padding: 16px;
      margin: 12px 0;
      border: 1px solid #e5e7eb;
      /* P0: 禁止阴影 */
    }
    
    .lynx-text {
      color: #333333;
      font-size: 14px;
      line-height: 1.6;
      margin: 4px 0;
    }
    
    /* 🚨 CRITICAL: scroll-view 强制样式规则 */
    .lynx-scroll-view {
      display: block;
      overflow: auto;
      box-sizing: border-box;
      -webkit-overflow-scrolling: touch;
      height: 100%; /* 🚨 MANDATORY: 必须设置具体高度 */
    }
    
    /* 🚨 MANDATORY: scroll-view 容器强制样式 */
    .lynx-scroll-container {
      display: block;
      overflow-y: auto;
      overflow-x: hidden;
      max-height: 400px; /* 默认最大高度 */
      -webkit-overflow-scrolling: touch;
      box-sizing: border-box;
      padding: 8px;
    }
    
    .lynx-title {
      font-size: 17px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 8px;
      line-height: 1.4;
    }
    
    .lynx-subtitle {
      font-size: 15px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 6px;
      line-height: 1.5;
    }
    
    /* 🎨 时间轴组件（基于 Prompts 规范） */
    .lynx-timeline {
      position: relative;
      padding-left: 32px;
    }
    
    .lynx-timeline::before {
      content: '';
      position: absolute;
      left: 16px;
      top: 0;
      bottom: 0;
      width: 2px;
      background: #d1d5db;
      border-style: dashed;
    }
    
    .lynx-timeline-item {
      position: relative;
      padding: 16px 0;
    }
    
    .lynx-timeline-item::before {
      content: '';
      position: absolute;
      left: -24px;
      top: 20px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: #3b82f6;
      border: 4px solid #ffffff;
      box-shadow: 0 0 0 2px #3b82f6;
    }
    
    .lynx-timeline-date {
      font-size: 13px;
      color: #6b7280;
      margin-bottom: 4px;
    }
    
    .lynx-timeline-title {
      font-size: 15px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 4px;
    }
    
    .lynx-timeline-content {
      font-size: 14px;
      color: #4b5563;
      line-height: 1.6;
    }
    
    /* 🎨 对比表格（基于 Prompts 规范） */
    .lynx-comparison-table {
      width: 100%;
      border-collapse: collapse;
      margin: 16px 0;
    }
    
    .lynx-comparison-table th {
      background: #f3f4f6;
      color: #1f2937;
      font-size: 14px;
      font-weight: 600;
      padding: 12px;
      text-align: left;
      border-bottom: 2px solid #e5e7eb;
    }
    
    .lynx-comparison-table td {
      padding: 12px;
      font-size: 13px;
      color: #374151;
      border-bottom: 1px solid #f3f4f6;
    }
    
    .lynx-comparison-table tr:nth-child(even) {
      background: #f9fafb;
    }
    
    /* 🎨 列表组件（基于 Prompts 规范） */
    .lynx-list {
      list-style: none;
      padding: 0;
      margin: 16px 0;
    }
    
    .lynx-list-item {
      padding: 12px 0;
      border-bottom: 1px solid #f3f4f6;
      display: flex;
      align-items: flex-start;
    }
    
    .lynx-list-item:last-child {
      border-bottom: none;
    }
    
    .lynx-list-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      margin-top: 2px;
      flex-shrink: 0;
    }
    
    .lynx-list-content {
      flex: 1;
    }
    
    .lynx-list-title {
      font-size: 14px;
      font-weight: 500;
      color: #1f2937;
      margin-bottom: 2px;
    }
    
    .lynx-list-description {
      font-size: 13px;
      color: #6b7280;
      line-height: 1.5;
    }
    
    /* 🎨 自定义样式（来自 TTSS） */
    ${css}
    
    /* 🎨 响应式设计增强 */
    @media (max-width: 375px) {
      .lynx-magazine-container {
        padding: 12px;
      }
      
      .lynx-card {
        padding: 12px;
        margin: 8px 0;
      }
      
      .lynx-title {
        font-size: 16px;
      }
      
      .lynx-subtitle {
        font-size: 14px;
      }
    }
  </style>
</head>
<body>
  <div class="lynx-magazine-container">
    <div class="lynx-content-wrapper" data-content-type="${contentAnalysis.type}" data-visualization="${contentAnalysis.recommendedVisualization}">
      ${bodyContent}
    </div>
  </div>
  
  ${js ? '<script>' + js + '</script>' : ''}
  
  <script>
    // 🚀 基于 Prompts 的运行时支持
    console.log('✅ 智能 Lynx 预览加载完成');
    console.log('📊 内容类型:', '${contentAnalysis.type}');
    console.log('🎯 可视化类型:', '${contentAnalysis.recommendedVisualization}');
    
    // 模拟 Lynx API
    window.lynx = {
      setData: function(data, callback) {
        console.log('📝 setData:', data);
        if (callback) callback();
      },
      
      navigateTo: function(options) {
        console.log('🔗 navigateTo:', options);
      },
      
      showToast: function(options) {
        console.log('💬 showToast:', options);
      }
    };
    
    // 智能交互增强
    document.addEventListener('DOMContentLoaded', function() {
      // 时间轴交互
      document.querySelectorAll('.lynx-timeline-item').forEach(item => {
        item.addEventListener('click', function() {
          console.log('🕐 时间轴项点击:', this.textContent);
        });
      });
      
      // 表格交互
      document.querySelectorAll('.lynx-comparison-table tr').forEach(row => {
        row.addEventListener('click', function() {
          console.log('📊 表格行点击:', this.textContent);
        });
      });
      
      // 列表交互
      document.querySelectorAll('.lynx-list-item').forEach(item => {
        item.addEventListener('click', function() {
          console.log('📝 列表项点击:', this.textContent);
        });
      });
    });
  </script>
</body>
</html>`;
  }

  // 辅助方法
  private addOrUpdateAttribute(node: any, name: string, value: string): void {
    if (!node.attrs) {
      node.attrs = [];
    }

    const existingAttr = node.attrs.find((attr: any) => attr.name === name);
    if (existingAttr) {
      if (name === 'class') {
        const existingClasses = existingAttr.value.split(' ');
        const newClasses = value.split(' ');
        const combinedClasses = [
          ...new Set([...existingClasses, ...newClasses]),
        ];
        existingAttr.value = combinedClasses.join(' ');
      } else {
        existingAttr.value = value;
      }
    } else {
      node.attrs.push({ name, value });
    }
  }

  // 时间轴指导原则
  private applyTimelineGuidelines(node: any): void {
    if (node.nodeName === 'view' && this.hasTimelineContent(node)) {
      this.addOrUpdateAttribute(node, 'class', 'lynx-timeline');
    }
  }

  // 对比指导原则
  private applyComparisonGuidelines(node: any): void {
    if (node.nodeName === 'view' && this.hasComparisonContent(node)) {
      this.addOrUpdateAttribute(node, 'class', 'lynx-comparison-container');
    }
  }

  // 数据可视化指导原则
  private applyDataVisualizationGuidelines(node: any): void {
    if (node.nodeName === 'view' && this.hasDataContent(node)) {
      this.addOrUpdateAttribute(node, 'class', 'lynx-data-container');
    }
  }

  // 通用指导原则
  private applyGeneralGuidelines(node: any): void {
    if (node.nodeName === 'view') {
      this.addOrUpdateAttribute(node, 'class', 'lynx-card');
    }
  }

  // 内容检测辅助方法
  private hasTimelineContent(node: any): boolean {
    // 检测是否包含时间轴内容
    return false; // 简化实现
  }

  private hasComparisonContent(node: any): boolean {
    // 检测是否包含对比内容
    return false; // 简化实现
  }

  private hasDataContent(node: any): boolean {
    // 检测是否包含数据内容
    return false; // 简化实现
  }

  // 可视化增强方法
  private addTimelineEnhancements(node: any): void {
    // 添加时间轴增强
  }

  private addComparisonTableEnhancements(node: any): void {
    // 添加对比表格增强
  }

  private addDataChartEnhancements(node: any): void {
    // 添加数据图表增强
  }

  private addGeneralEnhancements(node: any): void {
    // 添加通用增强
  }
}

/**
 * 内容分析器
 */
class ContentAnalyzer {
  async analyze(ttml: string, ttss: string, query?: string): Promise<ContentAnalysis> {
    console.log('🔍 [ContentAnalyzer] 开始内容分析');
    
    // 分析内容类型
    const contentType = this.detectContentType(ttml, query);
    
    // 推荐可视化类型
    const recommendedVisualization = this.recommendVisualization(contentType, ttml);
    
    // 分析优化建议
    const optimizations = this.analyzeOptimizations(ttml, ttss);
    
    return {
      type: contentType,
      recommendedVisualization,
      optimizations,
      complexity: this.calculateComplexity(ttml),
      keywords: this.extractKeywords(ttml, query),
    };
  }

  private detectContentType(ttml: string, query?: string): ContentType {
    // 关键词检测
    const timelineKeywords = ['发展历程', '历史', '沿革', '计划', '时间', '年份'];
    const comparisonKeywords = ['对比', '比较', '优缺点', 'vs', '区别'];
    const dataKeywords = ['数据', '统计', '图表', '数字', '比例'];
    const hierarchyKeywords = ['层级', '结构', '架构', '组织', '分类'];
    const processKeywords = ['步骤', '流程', '过程', '方法', '操作'];
    
    const content = (ttml + ' ' + (query || '')).toLowerCase();
    
    if (timelineKeywords.some(keyword => content.includes(keyword))) {
      return ContentType.TIMELINE;
    }
    
    if (comparisonKeywords.some(keyword => content.includes(keyword))) {
      return ContentType.COMPARISON;
    }
    
    if (dataKeywords.some(keyword => content.includes(keyword))) {
      return ContentType.DATA;
    }
    
    if (hierarchyKeywords.some(keyword => content.includes(keyword))) {
      return ContentType.HIERARCHY;
    }
    
    if (processKeywords.some(keyword => content.includes(keyword))) {
      return ContentType.PROCESS;
    }
    
    return ContentType.MIXED;
  }

  private recommendVisualization(contentType: ContentType, ttml: string): VisualizationType {
    const visualizationMap = {
      [ContentType.TIMELINE]: VisualizationType.TIMELINE,
      [ContentType.COMPARISON]: VisualizationType.COMPARISON_TABLE,
      [ContentType.DATA]: VisualizationType.DATA_CHART,
      [ContentType.HIERARCHY]: VisualizationType.TREE_CHART,
      [ContentType.PROCESS]: VisualizationType.FLOW_CHART,
      [ContentType.CONCEPT]: VisualizationType.CONCEPT_CARD,
      [ContentType.MIXED]: VisualizationType.LIST_VIEW,
    };
    
    return visualizationMap[contentType] || VisualizationType.LIST_VIEW;
  }

  private analyzeOptimizations(ttml: string, ttss: string): string[] {
    const optimizations: string[] = [];
    
    // 分析 TTML 优化建议
    if (ttml.includes('scroll-view')) {
      optimizations.push('检测到滚动视图，建议优化长列表性能');
    }
    
    // 分析 TTSS 优化建议
    if (ttss.includes('rpx')) {
      optimizations.push('检测到 RPX 单位，将进行智能转换');
    }
    
    return optimizations;
  }

  private calculateComplexity(ttml: string): number {
    const elementCount = (ttml.match(/<[^>]+>/g) || []).length;
    const nestingDepth = this.calculateNestingDepth(ttml);
    
    return Math.min(10, Math.floor((elementCount + nestingDepth * 2) / 10));
  }

  private calculateNestingDepth(ttml: string): number {
    let maxDepth = 0;
    let currentDepth = 0;
    
    const matches = ttml.match(/<[^>]+>/g) || [];
    for (const match of matches) {
      if (match.startsWith('</')) {
        currentDepth--;
      } else if (!match.endsWith('/>')) {
        currentDepth++;
        maxDepth = Math.max(maxDepth, currentDepth);
      }
    }
    
    return maxDepth;
  }

  private extractKeywords(ttml: string, query?: string): string[] {
    const content = (ttml + ' ' + (query || '')).toLowerCase();
    const keywords: string[] = [];
    
    // 提取关键词逻辑
    const commonKeywords = [
      '系统', '架构', '设计', '开发', '功能', '特性', '优势', '使用', '方法',
      '步骤', '流程', '过程', '结果', '数据', '信息', '内容', '展示'
    ];
    
    commonKeywords.forEach(keyword => {
      if (content.includes(keyword)) {
        keywords.push(keyword);
      }
    });
    
    return keywords.slice(0, 10); // 限制关键词数量
  }
}

/**
 * UI 指导原则执行器
 */
class UIGuidelineEnforcer {
  async enhance(ttss: string, contentAnalysis: ContentAnalysis): Promise<string> {
    console.log('🎨 [UIGuidelineEnforcer] 应用 UI 指导原则');
    
    let enhancedTtss = ttss;
    
    // 1. RPX 智能转换
    enhancedTtss = this.convertRpxIntelligently(enhancedTtss);
    
    // 2. 应用 P0 级治理标准
    enhancedTtss = this.applyP0Standards(enhancedTtss);
    
    // 3. 应用杂志化设计
    enhancedTtss = this.applyMagazineDesign(enhancedTtss, contentAnalysis);
    
    // 4. 应用调性原则
    enhancedTtss = this.applyTonePrinciples(enhancedTtss);
    
    return enhancedTtss;
  }

  private convertRpxIntelligently(ttss: string): string {
    // 智能 RPX 转换，考虑上下文
    return ttss.replace(/(\d+(?:\.\d+)?)rpx/g, (match, value) => {
      const numValue = parseFloat(value);
      
      // 基于 375px iPhone X 宽度的智能转换
      const pxValue = (numValue / 750) * 375;
      
      return `${pxValue}px`;
    });
  }

  private applyP0Standards(ttss: string): string {
    // 应用 P0 级治理标准：移除阴影、确保白色背景
    let enhanced = ttss;
    
    // 移除阴影
    enhanced = enhanced.replace(/box-shadow[^;]+;/g, '');
    enhanced = enhanced.replace(/text-shadow[^;]+;/g, '');
    
    // 确保背景色符合标准
    enhanced = enhanced.replace(/background-color\s*:\s*#(?![fF]{6})[^;]+;/g, 'background-color: #ffffff;');
    
    return enhanced;
  }

  private applyMagazineDesign(ttss: string, contentAnalysis: ContentAnalysis): string {
    // 应用杂志化设计原则
    let enhanced = ttss;
    
    // 根据内容类型应用特定设计
    switch (contentAnalysis.type) {
      case ContentType.TIMELINE:
        enhanced += this.getTimelineDesign();
        break;
      case ContentType.COMPARISON:
        enhanced += this.getComparisonDesign();
        break;
      case ContentType.DATA:
        enhanced += this.getDataVisualizationDesign();
        break;
      default:
        enhanced += this.getGeneralDesign();
    }
    
    return enhanced;
  }

  private applyTonePrinciples(ttss: string): string {
    // 应用调性原则：轻量、整洁、干净
    let enhanced = ttss;
    
    // 确保充足的留白
    enhanced = enhanced.replace(/margin\s*:\s*[^;]+;/g, 'margin: 12px 0;');
    enhanced = enhanced.replace(/padding\s*:\s*[^;]+;/g, 'padding: 16px;');
    
    return enhanced;
  }

  private getTimelineDesign(): string {
    return `
      /* 时间轴设计 */
      .timeline-container {
        position: relative;
        padding-left: 32px;
      }
      
      .timeline-item {
        position: relative;
        padding: 16px 0;
        border-left: 2px dashed #d1d5db;
        padding-left: 24px;
      }
      
      .timeline-item::before {
        content: '';
        position: absolute;
        left: -9px;
        top: 20px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #3b82f6;
        border: 4px solid #ffffff;
      }
    `;
  }

  private getComparisonDesign(): string {
    return `
      /* 对比表格设计 */
      .comparison-table {
        width: 100%;
        border-collapse: collapse;
        margin: 16px 0;
      }
      
      .comparison-table th {
        background: #f3f4f6;
        color: #1f2937;
        padding: 12px;
        text-align: left;
        font-weight: 600;
      }
      
      .comparison-table td {
        padding: 12px;
        border-bottom: 1px solid #e5e7eb;
      }
    `;
  }

  private getDataVisualizationDesign(): string {
    return `
      /* 数据可视化设计 */
      .data-container {
        padding: 20px;
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      
      .data-chart {
        width: 100%;
        height: 200px;
        margin: 16px 0;
      }
    `;
  }

  private getGeneralDesign(): string {
    return `
      /* 通用设计 */
      .content-card {
        background: #ffffff;
        border-radius: 8px;
        padding: 16px;
        margin: 12px 0;
        border: 1px solid #e5e7eb;
      }
    `;
  }
}

/**
 * 智能模板处理器
 */
class SmartTemplateProcessor {
  async process(ttml: string, contentAnalysis: ContentAnalysis): Promise<string> {
    console.log('🧠 [SmartTemplateProcessor] 智能模板处理');
    
    let processed = ttml;
    
    // 1. 智能数据绑定
    processed = this.processDataBindings(processed, contentAnalysis);
    
    // 2. 智能循环处理
    processed = this.processLoops(processed, contentAnalysis);
    
    // 3. 条件渲染优化
    processed = this.processConditions(processed);
    
    // 4. 添加语义化标记
    processed = this.addSemanticMarkup(processed, contentAnalysis);
    
    return processed;
  }

  private processDataBindings(ttml: string, contentAnalysis: ContentAnalysis): string {
    // 🚨 CRITICAL: 智能处理数据绑定，强制使用可选链操作符（遵循 prompts 规则）
    const contextualData = this.generateContextualData(contentAnalysis);
    
    return ttml.replace(/\{\{([^}]+)\}\}/g, (match, expression) => {
      const varName = expression.trim();
      
      // 🚨 MANDATORY: 强制转换为可选链语法
      let safeExpression = this.convertToOptionalChain(varName);
      
      if (contextualData[varName]) {
        return contextualData[varName];
      }
      
      // 回退到通用数据
      const fallbackData = {
        title: '示例标题',
        content: '这是示例内容，展示了智能转换的效果',
        count: '5',
        name: '示例名称',
        value: '示例值',
        description: '详细描述信息'
      };
      
      return fallbackData[varName] || `[${safeExpression}]`;
    });
  }

  /**
   * 🚨 MANDATORY: 将数据绑定转换为可选链语法
   */
  private convertToOptionalChain(expression: string): string {
    // 处理简单的属性访问：user.name -> user?.name
    if (expression.includes('.') && !expression.includes('?.')) {
      return expression.replace(/\./g, '?.');
    }
    
    // 处理数组访问：list[0].title -> list?.[0]?.title
    if (expression.includes('[') && expression.includes(']')) {
      return expression.replace(/\[/g, '?.[').replace(/\]\./g, ']?.');
    }
    
    // 处理方法调用：user.getName() -> user?.getName?.()
    if (expression.includes('(') && expression.includes(')')) {
      return expression.replace(/\.([^.]+)\(/g, '?.$1?.(');
    }
    
    return expression;
  }

  private processLoops(ttml: string, contentAnalysis: ContentAnalysis): string {
    // 🚨 CRITICAL: 智能处理循环，强制使用 scroll-view 包裹（遵循 prompts 规则）
    return ttml.replace(
      /<([^>]+)\s+tt:for="([^"]+)"\s*([^>]*)>([\s\S]*?)<\/\1>/g,
      (match, tagName, forExpression, attributes, content) => {
        const sampleData = this.generateSampleData(contentAnalysis);
        let repeatedContent = '';
        
        sampleData.forEach((item, index) => {
          let itemContent = content;
          // 🚨 MANDATORY: 强制使用可选链操作符
          itemContent = itemContent.replace(/\{\{[^}]*item[^}]*\}\}/g, item.title || item.name || item);
          itemContent = itemContent.replace(/\{\{[^}]*index[^}]*\}\}/g, index.toString());
          itemContent = itemContent.replace(/\{\{[^}]*description[^}]*\}\}/g, item.description || `描述 ${index + 1}`);
          repeatedContent += itemContent;
        });
        
        // 🚨 MANDATORY: tt:for 循环必须包裹在 scroll-view 中
        return `<scroll-view class="lynx-scroll-container" scroll-y="true" style="height: 100%;" data-lynx-for="true">
          <${tagName} ${attributes} data-processed="true">${repeatedContent}</${tagName}>
        </scroll-view>`;
      }
    );
  }

  private processConditions(ttml: string): string {
    // 处理条件渲染
    return ttml.replace(/tt:if="([^"]+)"/g, 'data-condition="$1"');
  }

  private addSemanticMarkup(ttml: string, contentAnalysis: ContentAnalysis): string {
    // 添加语义化标记
    let processed = ttml;
    
    // 根据内容类型添加特定标记
    switch (contentAnalysis.type) {
      case ContentType.TIMELINE:
        processed = this.addTimelineMarkup(processed);
        break;
      case ContentType.COMPARISON:
        processed = this.addComparisonMarkup(processed);
        break;
      default:
        processed = this.addGeneralMarkup(processed);
    }
    
    return processed;
  }

  private generateContextualData(contentAnalysis: ContentAnalysis): Record<string, string> {
    const dataMap = {
      [ContentType.TIMELINE]: {
        title: '发展历程',
        year: '2024',
        event: '重要事件',
        description: '详细描述这个阶段的重要发展'
      },
      [ContentType.COMPARISON]: {
        title: '对比分析',
        feature: '核心特性',
        advantage: '主要优势',
        description: '详细的对比说明'
      },
      [ContentType.DATA]: {
        title: '数据统计',
        value: '85%',
        metric: '关键指标',
        description: '数据分析结果说明'
      }
    };
    
    return dataMap[contentAnalysis.type] || {
      title: '智能内容',
      content: '基于内容分析生成的智能示例',
      description: '这是根据内容类型智能生成的示例数据'
    };
  }

  private generateSampleData(contentAnalysis: ContentAnalysis): any[] {
    const sampleDataMap = {
      [ContentType.TIMELINE]: [
        { title: '2020年', description: '项目启动阶段' },
        { title: '2021年', description: '快速发展期' },
        { title: '2022年', description: '稳定成长期' },
        { title: '2023年', description: '创新突破期' },
        { title: '2024年', description: '全面升级期' }
      ],
      [ContentType.COMPARISON]: [
        { title: '方案A', description: '传统解决方案' },
        { title: '方案B', description: '创新解决方案' },
        { title: '方案C', description: '混合解决方案' }
      ],
      [ContentType.DATA]: [
        { title: '用户满意度', description: '95%' },
        { title: '性能提升', description: '40%' },
        { title: '成本节约', description: '25%' }
      ]
    };
    
    return sampleDataMap[contentAnalysis.type] || [
      { title: '示例项目1', description: '这是第一个示例项目的描述' },
      { title: '示例项目2', description: '这是第二个示例项目的描述' },
      { title: '示例项目3', description: '这是第三个示例项目的描述' }
    ];
  }

  private addTimelineMarkup(ttml: string): string {
    return ttml.replace(/<view([^>]*)>/g, '<view$1 data-semantic="timeline">');
  }

  private addComparisonMarkup(ttml: string): string {
    return ttml.replace(/<view([^>]*)>/g, '<view$1 data-semantic="comparison">');
  }

  private addGeneralMarkup(ttml: string): string {
    return ttml.replace(/<view([^>]*)>/g, '<view$1 data-semantic="content">');
  }
}

/**
 * 响应式设计生成器
 */
class ResponsiveDesignGenerator {
  async generate(ttss: string, contentAnalysis: ContentAnalysis): Promise<string> {
    console.log('📱 [ResponsiveDesignGenerator] 生成响应式设计');
    
    let responsiveCss = ttss;
    
    // 1. 基础响应式框架
    responsiveCss = this.addResponsiveFramework(responsiveCss);
    
    // 2. 内容特定的响应式规则
    responsiveCss = this.addContentSpecificRules(responsiveCss, contentAnalysis);
    
    // 3. 设备适配优化
    responsiveCss = this.addDeviceOptimizations(responsiveCss);
    
    return responsiveCss;
  }

  private addResponsiveFramework(css: string): string {
    return css + `
      /* 响应式框架 */
      @media (max-width: 480px) {
        .lynx-container {
          padding: 12px;
        }
        
        .lynx-card {
          margin: 8px 0;
          padding: 12px;
        }
      }
      
      @media (min-width: 481px) and (max-width: 768px) {
        .lynx-container {
          padding: 16px;
        }
        
        .lynx-card {
          margin: 10px 0;
          padding: 16px;
        }
      }
      
      @media (min-width: 769px) {
        .lynx-container {
          padding: 20px;
        }
        
        .lynx-card {
          margin: 12px 0;
          padding: 20px;
        }
      }
    `;
  }

  private addContentSpecificRules(css: string, contentAnalysis: ContentAnalysis): string {
    const typeSpecificRules = {
      [ContentType.TIMELINE]: `
        @media (max-width: 480px) {
          .lynx-timeline {
            padding-left: 24px;
          }
          
          .lynx-timeline-item::before {
            left: -20px;
            width: 12px;
            height: 12px;
          }
        }
      `,
      [ContentType.COMPARISON]: `
        @media (max-width: 480px) {
          .lynx-comparison-table {
            font-size: 12px;
          }
          
          .lynx-comparison-table th,
          .lynx-comparison-table td {
            padding: 8px;
          }
        }
      `,
      [ContentType.DATA]: `
        @media (max-width: 480px) {
          .lynx-data-container {
            padding: 12px;
          }
          
          .lynx-data-chart {
            height: 150px;
          }
        }
      `
    };
    
    return css + (typeSpecificRules[contentAnalysis.type] || '');
  }

  private addDeviceOptimizations(css: string): string {
    return css + `
      /* 设备优化 */
      @media (-webkit-min-device-pixel-ratio: 2) {
        .lynx-text {
          -webkit-font-smoothing: antialiased;
        }
      }
      
      @media (hover: hover) and (pointer: fine) {
        .lynx-card:hover {
          transform: translateY(-2px);
          transition: transform 0.2s ease;
        }
      }
      
      @media (prefers-reduced-motion: reduce) {
        * {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }
    `;
  }
}

/**
 * 内容分析结果接口
 */
interface ContentAnalysis {
  type: ContentType;
  recommendedVisualization: VisualizationType;
  optimizations: string[];
  complexity: number;
  keywords: string[];
}

/**
 * 转换洞察接口
 */
interface ConversionInsights {
  contentType: ContentType;
  visualizationType: VisualizationType;
  processingTime: number;
  optimizations: string[];
}