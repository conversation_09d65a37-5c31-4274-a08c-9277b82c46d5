/**
 * FILES和FILE标签保护工具
 * 
 * 🚨 CRITICAL FIX: 解决AI输出的<FILES><FILE path="index.ttml">格式
 * 被误删变成"FILES FILE index.ttml"的问题
 * 
 * 问题根源：
 * - htmlModifier.ts:408 使用 content.replace(/<[^>]*>/g, '') 移除所有HTML标签
 * - fastHtmlEditor.ts:113 使用相同的正则表达式
 * - 这导致<FILES>和<FILE>标签被当作普通HTML标签移除
 * 
 * 解决方案：
 * - 在移除HTML标签前先保护FILES和FILE标签
 * - 使用特殊占位符替换这些标签
 * - 移除其他HTML标签后再恢复保护的标签
 */

/**
 * 安全地提取文本内容，保护FILES和FILE标签
 * 
 * @param content 包含HTML标签的内容
 * @returns 移除了HTML标签但保护了FILES和FILE标签的内容
 */
export function extractTextContentSafely(content: string): string {
  if (!content) {
    return '';
  }

  // 🔧 关键修复：先保护FILES和FILE标签
  let protectedContent = content
    .replace(/<FILES>/g, '___PROTECTED_FILES_START___')
    .replace(/<\/FILES>/g, '___PROTECTED_FILES_END___')
    .replace(/<FILE([^>]*)>/g, '___PROTECTED_FILE_START___$1___PROTECTED_FILE_ATTR_END___')
    .replace(/<\/FILE>/g, '___PROTECTED_FILE_END___');

  // 移除其他HTML标签
  protectedContent = protectedContent.replace(/<[^>]*>/g, '');

  // 恢复保护的标签
  const restoredContent = protectedContent
    .replace(/___PROTECTED_FILES_START___/g, '<FILES>')
    .replace(/___PROTECTED_FILES_END___/g, '</FILES>')
    .replace(/___PROTECTED_FILE_START___([^_]*)___PROTECTED_FILE_ATTR_END___/g, '<FILE$1>')
    .replace(/___PROTECTED_FILE_END___/g, '</FILE>');

  return restoredContent.trim();
}

/**
 * 保护FILES和FILE标签的内容处理器
 * 
 * @param content 原始内容
 * @param processor 内容处理函数
 * @returns 处理后的内容，FILES和FILE标签被保护
 */
export function processContentWithProtection(
  content: string,
  processor: (content: string) => string
): string {
  if (!content) {
    return '';
  }

  // 保护FILES和FILE标签
  const protectedContent = content
    .replace(/<FILES>/g, '___PROTECTED_FILES_START___')
    .replace(/<\/FILES>/g, '___PROTECTED_FILES_END___')
    .replace(/<FILE([^>]*)>/g, '___PROTECTED_FILE_START___$1___PROTECTED_FILE_ATTR_END___')
    .replace(/<\/FILE>/g, '___PROTECTED_FILE_END___');

  // 应用处理函数
  const processedContent = processor(protectedContent);

  // 恢复保护的标签
  const restoredContent = processedContent
    .replace(/___PROTECTED_FILES_START___/g, '<FILES>')
    .replace(/___PROTECTED_FILES_END___/g, '</FILES>')
    .replace(/___PROTECTED_FILE_START___([^_]*)___PROTECTED_FILE_ATTR_END___/g, '<FILE$1>')
    .replace(/___PROTECTED_FILE_END___/g, '</FILE>');

  return restoredContent;
}

/**
 * 检查内容是否包含FILES或FILE标签
 * 
 * @param content 要检查的内容
 * @returns 是否包含FILES或FILE标签
 */
export function containsFilesTags(content: string): boolean {
  if (!content) {
    return false;
  }

  return content.includes('<FILES>') || 
         content.includes('</FILES>') || 
         content.includes('<FILE') || 
         content.includes('</FILE>');
}

/**
 * 验证FILES和FILE标签格式是否正确
 * 
 * @param content 要验证的内容
 * @returns 验证结果
 */
export function validateFilesTagFormat(content: string): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!content) {
    return { isValid: true, errors, warnings };
  }

  // 检查是否有FILES标签但没有尖括号（问题格式）
  if (content.includes('FILES') && !content.includes('<FILES>')) {
    errors.push('检测到可能的FILES标签格式错误：缺少尖括号');
  }

  if (content.includes('FILE ') && !content.includes('<FILE')) {
    errors.push('检测到可能的FILE标签格式错误：缺少尖括号');
  }

  // 检查FILES标签配对
  const filesStartCount = (content.match(/<FILES>/g) || []).length;
  const filesEndCount = (content.match(/<\/FILES>/g) || []).length;
  
  if (filesStartCount !== filesEndCount) {
    errors.push(`FILES标签不配对：开始标签${filesStartCount}个，结束标签${filesEndCount}个`);
  }

  // 检查FILE标签配对
  const fileStartCount = (content.match(/<FILE[^>]*>/g) || []).length;
  const fileEndCount = (content.match(/<\/FILE>/g) || []).length;
  
  if (fileStartCount !== fileEndCount) {
    errors.push(`FILE标签不配对：开始标签${fileStartCount}个，结束标签${fileEndCount}个`);
  }

  // 检查FILE标签是否有path属性
  const fileTagsWithoutPath = content.match(/<FILE(?![^>]*path=)[^>]*>/g);
  if (fileTagsWithoutPath && fileTagsWithoutPath.length > 0) {
    warnings.push(`发现${fileTagsWithoutPath.length}个FILE标签缺少path属性`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 修复损坏的FILES和FILE标签格式
 * 
 * @param content 可能包含损坏格式的内容
 * @returns 修复后的内容
 */
export function repairFilesTagFormat(content: string): string {
  if (!content) {
    return '';
  }

  let repairedContent = content;

  // 修复缺少尖括号的FILES标签
  repairedContent = repairedContent
    .replace(/^FILES$/gm, '<FILES>')
    .replace(/^\/FILES$/gm, '</FILES>')
    .replace(/\bFILES\b(?![^<]*>)/g, '<FILES>')
    .replace(/\b\/FILES\b(?![^<]*>)/g, '</FILES>');

  // 修复缺少尖括号的FILE标签
  repairedContent = repairedContent
    .replace(/^FILE\s+path="([^"]+)"$/gm, '<FILE path="$1">')
    .replace(/^\/FILE$/gm, '</FILE>')
    .replace(/\bFILE\s+path="([^"]+)"(?![^<]*>)/g, '<FILE path="$1">')
    .replace(/\b\/FILE\b(?![^<]*>)/g, '</FILE>');

  return repairedContent;
}

export default {
  extractTextContentSafely,
  processContentWithProtection,
  containsFilesTags,
  validateFilesTagFormat,
  repairFilesTagFormat
};
